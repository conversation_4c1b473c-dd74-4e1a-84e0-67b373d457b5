'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect } from '@/components/ui/multi-select';
import { PhoneInput } from '@/components/ui/phone-input';
import { Save, User, Phone, Mail, MapPin, Shield, Plus, X, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface DoctorProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  specialties: string[];
  consultory: {
    id: string;
    name: string;
    address?: string;
  };
  licenseNumber?: string;
  roles: string[];
}

interface PersonalInfoFormProps {
  profile: DoctorProfile | null;
  onUpdate: () => void;
}

export function PersonalInfoForm({ profile, onUpdate }: PersonalInfoFormProps) {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    phone: '',
    licenseNumber: '',
    specialties: [] as string[],
    bio: '',
  });
  const [saving, setSaving] = useState(false);
  const [availableSpecialties, setAvailableSpecialties] = useState<string[]>([]);

  useEffect(() => {
    if (profile) {
      setFormData({
        firstName: profile.firstName || '',
        lastName: profile.lastName || '',
        phone: profile.phone || '',
        licenseNumber: profile.licenseNumber || '',
        specialties: profile.specialties || [],
        bio: '',
      });
    }
  }, [profile]);

  useEffect(() => {
    fetchSpecialties();
  }, []);

  const fetchSpecialties = async () => {
    try {
      const response = await fetch('/api/catalogs/medical-specialties');
      const data = await response.json();
      
      if (data.success) {
        const specialtyNames = data.data.map((specialty: any) => specialty.name);
        setAvailableSpecialties(specialtyNames);
      }
    } catch (error) {
      console.error('Error fetching specialties:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      setSaving(true);
      
      const response = await fetch('/api/doctor/profile', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Perfil actualizado correctamente');
        onUpdate();
      } else {
        toast.error(data.error || 'Error al actualizar el perfil');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setSaving(false);
    }
  };

  const handleSpecialtyChange = (selectedValues: string[]) => {
    setFormData(prev => ({
      ...prev,
      specialties: selectedValues,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Información básica */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <User className="h-5 w-5" />
            Información Básica
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="firstName">Nombre</Label>
              <Input
                id="firstName"
                value={formData.firstName}
                onChange={(e) => setFormData(prev => ({ ...prev, firstName: e.target.value }))}
                placeholder="Tu nombre"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="lastName">Apellido</Label>
              <Input
                id="lastName"
                value={formData.lastName}
                onChange={(e) => setFormData(prev => ({ ...prev, lastName: e.target.value }))}
                placeholder="Tu apellido"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="email" className="flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Email
            </Label>
            <Input
              id="email"
              type="email"
              value={profile?.email || ''}
              disabled
              className="bg-gray-50"
            />
            <p className="text-xs text-gray-600">
              El email no se puede modificar desde aquí. Contacta al administrador si necesitas cambiarlo.
            </p>
          </div>

          <PhoneInput
            id="phone"
            label={
              <span className="flex items-center gap-2">
                <Phone className="h-4 w-4" />
                Teléfono
              </span>
            }
            value={formData.phone}
            onChange={(value) => setFormData(prev => ({ ...prev, phone: value }))}
          />
        </CardContent>
      </Card>

      {/* Información profesional */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Información Profesional
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="licenseNumber">Número de Licencia Médica</Label>
            <Input
              id="licenseNumber"
              value={formData.licenseNumber}
              onChange={(e) => setFormData(prev => ({ ...prev, licenseNumber: e.target.value }))}
              placeholder="Ej: 12345"
            />
          </div>

          <div className="space-y-2">
            <Label>Especialidades Médicas</Label>
            <MultiSelect
              options={availableSpecialties.map(specialty => ({
                label: specialty,
                value: specialty,
              }))}
              onValueChange={handleSpecialtyChange}
              defaultValue={formData.specialties}
              placeholder="Selecciona tus especialidades"
              variant="inverted"
              animation={2}
              maxCount={3}
            />
            <p className="text-xs text-gray-600">
              Selecciona hasta 3 especialidades principales
            </p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio">Biografía Profesional</Label>
            <Textarea
              id="bio"
              value={formData.bio}
              onChange={(e) => setFormData(prev => ({ ...prev, bio: e.target.value }))}
              placeholder="Describe brevemente tu experiencia y especialización..."
              rows={4}
            />
          </div>
        </CardContent>
      </Card>

      {/* Información del consultorio */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Consultorio Asignado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div>
              <Label className="text-sm text-gray-600">Consultorio</Label>
              <p className="font-medium">{profile?.consultory?.name || 'No asignado'}</p>
            </div>
            
            {profile?.consultory?.address && (
              <div>
                <Label className="text-sm text-gray-600">Dirección</Label>
                <p className="text-sm">{profile.consultory.address}</p>
              </div>
            )}
            
            <p className="text-xs text-gray-600">
              Para cambiar de consultorio, contacta al administrador del sistema.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Roles actuales */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Roles del Sistema</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2">
            {profile?.roles?.map((role, index) => (
              <Badge key={index} variant="outline">
                {role}
              </Badge>
            ))}
            {(!profile?.roles || profile.roles.length === 0) && (
              <p className="text-sm text-gray-600">No hay roles asignados</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Botón de guardar */}
      <div className="flex justify-end pt-4">
        <Button 
          type="submit" 
          disabled={saving}
          size="lg"
          className="bg-emerald-600 hover:bg-emerald-700"
        >
          {saving ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Guardando...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Guardar Cambios
            </>
          )}
        </Button>
      </div>
    </form>
  );
}