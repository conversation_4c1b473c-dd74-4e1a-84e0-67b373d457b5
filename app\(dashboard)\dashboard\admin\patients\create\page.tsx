'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { toast } from 'sonner';
import { UnifiedPatientForm, PatientFormData } from '@/components/forms/unified-patient-form';

export default function CreatePatientPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [returnUrl, setReturnUrl] = useState<string | null>(null);

  useEffect(() => {
    const returnUrlParam = searchParams.get('returnUrl');
    setReturnUrl(returnUrlParam);
  }, [searchParams]);

  const handleSubmit = async (data: PatientFormData) => {
    setLoading(true);
    
    try {
      const response = await fetch('/api/patients/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Paciente creado exitosamente');
        
        // Si hay un returnUrl, redirigir allí; sino, ir a la lista de usuarios
        if (returnUrl) {
          router.push(returnUrl);
        } else {
          router.push('/dashboard/admin/users');
        }
      } else {
        toast.error(result.error || 'Error al crear paciente');
      }
    } catch (error) {
      console.error('Error creating patient:', error);
      toast.error('Error al crear paciente');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    if (returnUrl) {
      router.push(returnUrl);
    } else {
      router.back();
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          onClick={handleCancel}
          className="hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Crear Paciente</h1>
          <p className="text-gray-600">Registrar un nuevo paciente en el sistema</p>
        </div>
      </div>

      {/* Formulario usando UnifiedPatientForm */}
      <UnifiedPatientForm
        mode="complete"
        context={{
          showEmailInvitation: true,
        }}
        onSubmit={handleSubmit}
        loading={loading}
        onCancel={handleCancel}
      />
    </div>
  );
}