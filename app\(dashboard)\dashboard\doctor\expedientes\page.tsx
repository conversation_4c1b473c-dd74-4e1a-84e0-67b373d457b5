'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  FileText, 
  ArrowLeft,
  RefreshCw,
  Download,
  Search,
  Eye,
  Filter,
  Calendar,
  User,
  Heart,
  Clock,
  Activity
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { cn, formatDate } from '@/lib/utils';

import { es } from 'date-fns/locale';

interface ExpedienteListItem {
  id: string;
  recordNumber: string;
  patientName: string;
  patientAge: number;
  status: 'active' | 'inactive' | 'transferred' | 'archived';
  openDate: string;
  totalConsultations: number;
  lastConsultationDate?: string;
  primaryDoctorName: string;
  consultoryName: string;
}

interface Statistics {
  total: number;
  active: number;
  inactive: number;
  archived: number;
}

export default function ExpedientesPage() {
  const router = useRouter();
  const { user } = useUser();
  
  // Estados principales
  const [expedientes, setExpedientes] = useState<ExpedienteListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [page, setPage] = useState(1);
  const [total, setTotal] = useState(0);
  const [totalPages, setTotalPages] = useState(1);
  const [stats, setStats] = useState<Statistics>({
    total: 0,
    active: 0,
    inactive: 0,
    archived: 0,
  });

  // Cargar expedientes
  const fetchExpedientes = async () => {
    try {
      setLoading(true);
      
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        search: searchTerm,
        status: statusFilter !== 'all' ? statusFilter : ''
      });

      const response = await fetch(`/api/medical-records?${queryParams}`);
      const data = await response.json();

      if (response.ok && data.data) {
        setExpedientes(data.data || []);
        setTotal(data.pagination?.total || 0);
        setTotalPages(data.pagination?.totalPages || 1);
        
        // Calcular estadísticas
        const expedientesList = data.data || [];
        setStats({
          total: expedientesList.length,
          active: expedientesList.filter((e: ExpedienteListItem) => e.status === 'active').length,
          inactive: expedientesList.filter((e: ExpedienteListItem) => e.status === 'inactive').length,
          archived: expedientesList.filter((e: ExpedienteListItem) => e.status === 'archived').length,
        });
      } else {
        toast.error('Error al cargar expedientes');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  // Efectos
  useEffect(() => {
    fetchExpedientes();
  }, [page, statusFilter]);

  // Búsqueda con debounce
  useEffect(() => {
    const timer = setTimeout(() => {
      if (page === 1) {
        fetchExpedientes();
      } else {
        setPage(1);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [searchTerm]);

  const handleRefresh = () => {
    fetchExpedientes();
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800 border-green-200">Activo</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactivo</Badge>;
      case 'transferred':
        return <Badge className="bg-blue-100 text-blue-800 border-blue-200">Transferido</Badge>;
      case 'archived':
        return <Badge variant="outline">Archivado</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Activo';
      case 'inactive':
        return 'Inactivo';
      case 'transferred':
        return 'Transferido';
      case 'archived':
        return 'Archivado';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Expedientes Clínicos
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Gestión integral de historias clínicas pediátricas
              </p>
            </div>
          </div>
        </div>
        
        {/* Botones de acción */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Expedientes
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Activos
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <Activity className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.active}</div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Inactivos
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
              <Clock className="h-4 w-4 text-gray-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.inactive}</div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Archivados
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-gray-100 flex items-center justify-center">
              <Heart className="h-4 w-4 text-gray-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-600">{stats.archived}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros de búsqueda */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-600" />
              Buscar Expedientes
            </CardTitle>
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Filtros
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Búsqueda principal */}
            <div>
              <Label className="text-sm font-medium text-gray-700">Buscar expediente</Label>
              <div className="relative mt-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por número de expediente, nombre del paciente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filtros adicionales */}
            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Estado</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger>
                      <SelectValue placeholder="Todos los estados" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos los estados</SelectItem>
                      <SelectItem value="active">Activos</SelectItem>
                      <SelectItem value="inactive">Inactivos</SelectItem>
                      <SelectItem value="transferred">Transferidos</SelectItem>
                      <SelectItem value="archived">Archivados</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Lista de expedientes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg">
              Expedientes ({total})
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="text-center py-8">
              <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-gray-400" />
              <p className="text-gray-500">Cargando expedientes...</p>
            </div>
          ) : expedientes.length === 0 ? (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron expedientes</h3>
              <p className="text-gray-500 mb-4">
                {searchTerm || statusFilter !== 'all' 
                  ? 'Intenta ajustar los filtros de búsqueda' 
                  : 'Para crear expedientes ve a Agenda → Crear Cita → Al completar cita → Crear expediente automáticamente'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {expedientes.map((expediente) => (
                <div
                  key={expediente.id}
                  className="relative p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer group"
                  onClick={() => router.push(`/dashboard/doctor/expedientes/${expediente.id}`)}
                >
                  {/* Layout Mobile-First */}
                  <div className="flex flex-col space-y-3 sm:flex-row sm:items-start sm:space-y-0 sm:space-x-4">
                    {/* Header: Avatar + Título + Botón de acción */}
                    <div className="flex items-start justify-between sm:flex-col sm:items-start sm:space-y-2">
                      <div className="flex items-center space-x-3 sm:flex-col sm:space-x-0 sm:space-y-2">
                        <div className="flex-shrink-0">
                          <div className="w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-blue-100 flex items-center justify-center">
                            <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600" />
                          </div>
                        </div>
                        
                        {/* Título en mobile separado del badge */}
                        <div className="sm:text-center">
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 truncate">
                            {expediente.patientName}
                          </h3>
                        </div>
                      </div>
                      
                      {/* Botón de acción solo visible en mobile aquí */}
                      <div className="sm:hidden">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="opacity-70 hover:opacity-100 hover:bg-green-100 transition-all"
                          onClick={(e) => {
                            e.stopPropagation();
                            router.push(`/dashboard/doctor/expedientes/${expediente.id}`);
                          }}
                        >
                          <Eye className="h-4 w-4 text-green-600 hover:text-green-800 transition-colors" />
                        </Button>
                      </div>
                    </div>
                    
                    {/* Contenido principal */}
                    <div className="flex-1 min-w-0 space-y-3">
                      {/* Badge del estado en su propia línea en mobile */}
                      <div className="sm:hidden">
                        {getStatusBadge(expediente.status)}
                      </div>
                      
                      {/* Información principal en grid responsivo */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4 text-sm text-gray-600">
                        <div>
                          <span className="font-medium text-gray-800">Exp:</span> 
                          <span className="ml-1">{expediente.recordNumber}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-800">Edad:</span> 
                          <span className="ml-1">{expediente.patientAge} años</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-800">Consultas:</span> 
                          <span className="ml-1">{expediente.totalConsultations}</span>
                        </div>
                        <div>
                          <span className="font-medium text-gray-800">Abierto:</span> 
                          <span className="ml-1">{formatDate(expediente.openDate)}</span>
                        </div>
                      </div>
                      
                      {/* Información del doctor y consultorio */}
                      <div className="text-xs sm:text-sm text-gray-500 space-y-1 sm:space-y-0">
                        <div className="sm:inline">
                          <span className="font-medium">Dr:</span> {expediente.primaryDoctorName}
                        </div>
                        <div className="sm:inline sm:ml-2">
                          <span className="hidden sm:inline">•</span>
                          <span className="font-medium"> Consultorio:</span> {expediente.consultoryName}
                        </div>
                        {expediente.lastConsultationDate && (
                          <div className="sm:inline sm:ml-2">
                            <span className="hidden sm:inline">•</span>
                            <span className="font-medium"> Última consulta:</span> {formatDate(expediente.lastConsultationDate)}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  {/* Botón de acción en desktop - posición absoluta */}
                  <div className="hidden sm:block absolute top-4 right-4 lg:top-6 lg:right-6">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="opacity-70 group-hover:opacity-100 hover:bg-green-100 transition-all"
                      onClick={(e) => {
                        e.stopPropagation();
                        router.push(`/dashboard/doctor/expedientes/${expediente.id}`);
                      }}
                    >
                      <Eye className="h-4 w-4 text-green-600 hover:text-green-800 transition-colors" />
                    </Button>
                  </div>
                  
                  {/* Badge del estado en desktop - posición absoluta */}
                  <div className="hidden sm:block absolute top-4 left-4 lg:top-6 lg:left-6">
                    {getStatusBadge(expediente.status)}
                  </div>
                </div>
              ))}
            </div>
          )}
          
          {/* Paginación */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6">
              <Button
                variant="outline"
                onClick={() => setPage(Math.max(1, page - 1))}
                disabled={page <= 1}
              >
                Anterior
              </Button>
              
              <span className="text-sm text-gray-600">
                Página {page} de {totalPages}
              </span>
              
              <Button
                variant="outline"
                onClick={() => setPage(Math.min(totalPages, page + 1))}
                disabled={page >= totalPages}
              >
                Siguiente
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Para crear pacientes ir a Agenda → Crear Cita → Si no existe paciente → Crearlo ahí */}
    </div>
  );
}