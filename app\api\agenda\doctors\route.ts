import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, appointments } from '@/db/schema';
import { eq, and, sql, gte, lte } from 'drizzle-orm';

export async function GET(request: Request) {
  try {
    const authData = await auth();
    if (!authData?.userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario sea doctor o asistente
    const currentUser = await db.query.userRoles.findFirst({
      where: and(
        eq(userRoles.userId, authData.userId),
        eq(userRoles.status, 'active')
      ),
      with: {
        user: true
      }
    });

    if (!currentUser || !['doctor', 'assistant'].includes(currentUser.role)) {
      return NextResponse.json({ 
        error: 'No tienes permisos para ver la lista de doctores' 
      }, { status: 403 });
    }

    // Obtener fecha de hoy para contar citas
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Obtener todos los doctores activos con sus citas de hoy
    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: userRoles.role,
        specialtyId: userRoles.specialtyId,
        status: userRoles.status,
        appointmentsToday: sql<number>`
          COALESCE(
            (SELECT COUNT(*) 
             FROM ${appointments} 
             WHERE ${appointments.doctorId} = ${user.id}
             AND ${appointments.scheduledDate} >= ${today.toISOString()}
             AND ${appointments.scheduledDate} < ${tomorrow.toISOString()}
             AND ${appointments.status} != 'cancelled'
            ), 0
          )`
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .orderBy(user.firstName, user.lastName);

    // Si es un doctor, solo devolver su propia información
    if (currentUser.role === 'doctor') {
      const selfDoctor = doctors.find(d => d.id === authData.userId);
      return NextResponse.json({
        success: true,
        data: selfDoctor ? [selfDoctor] : []
      });
    }

    // Si es asistente, devolver todos los doctores
    return NextResponse.json({
      success: true,
      data: doctors.map(doctor => ({
        ...doctor,
        isAvailable: doctor.status === 'active',
        appointmentsToday: Number(doctor.appointmentsToday)
      }))
    });

  } catch (error) {
    console.error('Error al obtener doctores:', error);
    return NextResponse.json(
      { error: 'Error al cargar la lista de doctores' },
      { status: 500 }
    );
  }
}