// Script para agregar configuración de teléfono a system_config
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function addPhoneConfiguration() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🚀 Agregando configuración de teléfono al sistema...\n');
  
  try {
    // Configuraciones de teléfono para agregar
    const phoneConfigs = [
      {
        key: 'regional.phoneFormat',
        value: '"####-####"',
        description: 'Formato de número telefónico predeterminado',
        category: 'regional'
      },
      {
        key: 'regional.phoneDigits',
        value: '8',
        description: 'Cantidad de dígitos para números telefónicos',
        category: 'regional'
      },
      {
        key: 'regional.phoneExample',
        value: '"5555-5555"',
        description: 'Ejemplo de número telefónico para placeholders',
        category: 'regional'
      }
    ];

    let inserted = 0;
    for (const config of phoneConfigs) {
      try {
        await sql`
          INSERT INTO system_config (key, value, description, category, active, created_at, updated_at) 
          VALUES (${config.key}, ${config.value}::jsonb, ${config.description}, ${config.category}, true, now(), now())
          ON CONFLICT (key) DO UPDATE SET 
            value = ${config.value}::jsonb,
            description = ${config.description},
            updated_at = now()
        `;
        console.log(`   ✅ ${config.key}`);
        inserted++;
      } catch (error) {
        console.error(`   ❌ Error con ${config.key}: ${error.message}`);
      }
    }
    
    console.log(`\n📊 Resultado: ${inserted}/${phoneConfigs.length} configuraciones insertadas\n`);

    // Actualizar consultories con valores predeterminados
    console.log('🏥 Actualizando consultories con configuración de teléfono...');
    
    const updateResult = await sql`
      UPDATE consultories 
      SET 
        regional_settings = 
          COALESCE(regional_settings, '{}'::jsonb) || 
          '{"phoneFormat": "####-####", "phoneDigits": 8, "phoneExample": "5555-5555"}'::jsonb,
        "updatedAt" = now()
      WHERE 
        regional_settings IS NULL 
        OR NOT (regional_settings ? 'phoneFormat')
    `;
    
    console.log(`   ✅ ${updateResult.count || 0} consultories actualizados`);
    
    console.log('\n🎉 ¡Configuración de teléfono agregada exitosamente!');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

addPhoneConfiguration();