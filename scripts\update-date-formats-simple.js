// Script simplificado para actualizar formatos de fecha sin dependencias externas
const fs = require('fs');
const path = require('path');

// Archivos principales a actualizar
const filesToUpdate = [
  'app/(dashboard)/dashboard/doctor/agenda/appointment/[[...params]]/page.tsx',
  'app/(dashboard)/dashboard/doctor/expedientes/page.tsx', 
  'app/(dashboard)/dashboard/doctor/expedientes/[id]/page.tsx',
  'app/(dashboard)/dashboard/doctor/pacientes/page.tsx',
  'components/agenda/calendar-month-view.tsx',
  'components/agenda/calendar-week-view.tsx',
  'components/agenda/calendar-day-view.tsx',
  'components/agenda/appointments-list.tsx',
  'app/(dashboard)/dashboard/doctor/agenda/page.tsx',
  'app/(dashboard)/dashboard/assistant/agenda/page.tsx',
  'app/(dashboard)/dashboard/admin/agenda/page.tsx',
  'components/agenda/appointment-tooltip.tsx',
  'components/agenda/appointment-modal.tsx',
  'components/ui/date-picker.tsx',
  'app/(dashboard)/dashboard/admin/users/[id]/page.tsx',
  'app/(dashboard)/dashboard/admin/users/page.tsx',
  'app/(dashboard)/dashboard/admin/requests/[id]/page.tsx',
  'app/(dashboard)/dashboard/admin/requests/page.tsx'
];

// Patrones de reemplazo
const replacements = [
  {
    // format(new Date(date), 'dd/MM/yyyy', { locale: es }) -> formatDate(date)
    pattern: /format\s*\(\s*new\s+Date\s*\(\s*([^)]+)\s*\)\s*,\s*['"`]dd\/MM\/yyyy['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDate($1)'
  },
  {
    // format(date, 'dd/MM/yyyy', { locale: es }) -> formatDate(date)
    pattern: /format\s*\(\s*([^,\)]+)\s*,\s*['"`]dd\/MM\/yyyy['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDate($1)'
  },
  {
    // format(new Date(date), 'dd/MM/yyyy HH:mm', { locale: es }) -> formatDateTime(date)
    pattern: /format\s*\(\s*new\s+Date\s*\(\s*([^)]+)\s*\)\s*,\s*['"`]dd\/MM\/yyyy\s+HH:mm['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDateTime($1)'
  },
  {
    // format(date, 'dd/MM/yyyy HH:mm', { locale: es }) -> formatDateTime(date)
    pattern: /format\s*\(\s*([^,\)]+)\s*,\s*['"`]dd\/MM\/yyyy\s+HH:mm['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDateTime($1)'
  },
  {
    // format(date, 'HH:mm', { locale: es }) -> formatTime(date)
    pattern: /format\s*\(\s*([^,\)]+)\s*,\s*['"`]HH:mm['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatTime($1)'
  }
];

async function updateDateFormats() {
  console.log('🔄 Actualizando formatos de fecha en archivos...\n');

  let updatedFiles = 0;
  let totalReplacements = 0;

  for (const filePath of filesToUpdate) {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      console.log(`⏭️  Saltando ${filePath} (no existe)`);
      continue;
    }

    try {
      let content = fs.readFileSync(fullPath, 'utf8');
      let modified = false;
      let fileReplacements = 0;

      // Aplicar cada patrón de reemplazo
      for (const { pattern, replacement } of replacements) {
        const matches = content.match(pattern);
        if (matches) {
          content = content.replace(pattern, replacement);
          modified = true;
          fileReplacements += matches.length;
        }
      }

      if (modified) {
        // Verificar y actualizar imports
        const hasFormatImport = content.includes('import { format }') || content.includes('import { format,');
        const hasUtilsImport = content.includes('from "@/lib/utils"') || content.includes('from \'@/lib/utils\'');
        const needsFormatDate = content.includes('formatDate(');
        const needsFormatDateTime = content.includes('formatDateTime(');
        const needsFormatTime = content.includes('formatTime(');

        if ((needsFormatDate || needsFormatDateTime || needsFormatTime) && !hasUtilsImport) {
          // Agregar import de utils
          const imports = [];
          if (needsFormatDate) imports.push('formatDate');
          if (needsFormatDateTime) imports.push('formatDateTime');
          if (needsFormatTime) imports.push('formatTime');
          
          const importStatement = `import { ${imports.join(', ')} } from '@/lib/utils';\n`;
          
          // Encontrar donde insertar el import
          const importLines = content.split('\n');
          let insertIndex = 0;
          
          // Buscar la última línea de import
          for (let i = 0; i < importLines.length; i++) {
            if (importLines[i].trim().startsWith('import ') && !importLines[i].includes('//')) {
              insertIndex = i + 1;
            }
          }
          
          importLines.splice(insertIndex, 0, importStatement.trim());
          content = importLines.join('\n');
        } else if (hasUtilsImport && (needsFormatDate || needsFormatDateTime || needsFormatTime)) {
          // Actualizar import existente de utils
          content = content.replace(
            /import\s*\{([^}]+)\}\s*from\s*['"]@\/lib\/utils['"];?/,
            (match, imports) => {
              const importList = imports.split(',').map(i => i.trim());
              if (needsFormatDate && !importList.some(i => i.includes('formatDate'))) {
                importList.push('formatDate');
              }
              if (needsFormatDateTime && !importList.some(i => i.includes('formatDateTime'))) {
                importList.push('formatDateTime');
              }
              if (needsFormatTime && !importList.some(i => i.includes('formatTime'))) {
                importList.push('formatTime');
              }
              return `import { ${importList.join(', ')} } from '@/lib/utils';`;
            }
          );
        }

        // Limpiar imports de date-fns si ya no se usan
        if (hasFormatImport && !content.match(/format\s*\(/)) {
          // Remover import de format de date-fns
          content = content.replace(/import\s*\{\s*format\s*\}\s*from\s*['"]date-fns['"];?\n?/g, '');
          content = content.replace(/import\s*\{\s*format\s*,\s*/g, 'import { ');
          content = content.replace(/,\s*format\s*\}/g, ' }');
          content = content.replace(/,\s*format\s*,/g, ', ');
        }

        // Guardar archivo actualizado
        fs.writeFileSync(fullPath, content);
        updatedFiles++;
        totalReplacements += fileReplacements;
        console.log(`✅ ${filePath} (${fileReplacements} cambios)`);
      } else {
        console.log(`ℹ️  ${filePath} (sin cambios)`);
      }
    } catch (error) {
      console.error(`❌ Error en ${filePath}:`, error.message);
    }
  }

  console.log(`\n🎉 ¡Actualización completada!`);
  console.log(`📊 Resumen:`);
  console.log(`   - ${updatedFiles} archivos modificados`);
  console.log(`   - ${totalReplacements} reemplazos realizados`);
  console.log(`   - Los formatos ahora usan configuración regional automáticamente`);
  
  console.log(`\n✅ Próximos pasos:`);
  console.log(`   1. Reiniciar el servidor de desarrollo: npm run dev`);
  console.log(`   2. Los formularios ahora usarán dd/MM/yyyy automáticamente`);
}

updateDateFormats().catch(console.error);