import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, userRoles, medicalSpecialties } from '@/db/schema';
import { eq, and, ilike, or } from 'drizzle-orm';

// GET /api/catalogs/doctor-service-prices/doctors - Obtener lista de médicos disponibles
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const onlyActive = searchParams.get('onlyActive') === 'true';

    // Construir condiciones WHERE
    const conditions = [
      eq(userRoles.role, 'doctor'), // Solo usuarios con rol de doctor
    ];

    if (onlyActive) {
      conditions.push(eq(userRoles.status, 'active')); // Solo médicos activos
    }

    if (search) {
      conditions.push(
        or(
          ilike(user.firstName, `%${search}%`),
          ilike(user.lastName, `%${search}%`),
          ilike(user.email, `%${search}%`),
          ilike(userRoles.medicalLicense, `%${search}%`)
        )
      );
    }

    // Obtener médicos con sus especialidades
    let query = db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        imageUrl: user.image,
        medicalLicense: userRoles.medicalLicense,
        phone: user.phone,
        status: userRoles.status,
        specialtyId: userRoles.specialtyId,
        specialtyName: medicalSpecialties.name,
        consultoryId: userRoles.consultoryId,
      })
      .from(userRoles)
      .innerJoin(user, eq(userRoles.userId, user.id))
      .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id));

    if (conditions.length === 1) {
      query = query.where(conditions[0]);
    } else if (conditions.length > 1) {
      query = query.where(and(...conditions));
    }

    const doctors = await query.orderBy(user.firstName, user.lastName);

    // Nota: En agenda del asistente se usan iniciales por seguridad

    // Formatear datos para la respuesta
    const formattedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      name: `${doctor.firstName || ''} ${doctor.lastName || ''}`.trim(),
      firstName: doctor.firstName,
      lastName: doctor.lastName,
      email: doctor.email,
      imageUrl: doctor.imageUrl,
      clerkImageUrl: doctor.imageUrl, // Agregar clerkImageUrl como copia de imageUrl
      medicalLicense: doctor.medicalLicense,
      phone: doctor.phone,
      isActive: doctor.status === 'active',
      specialty: doctor.specialtyName || null,
      specialtyId: doctor.specialtyId,
      consultoryId: doctor.consultoryId,
    }));

    return NextResponse.json({
      success: true,
      data: formattedDoctors,
      total: formattedDoctors.length,
    });

  } catch (error: any) {
    console.error('Error fetching doctors:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}