-- Add missing audit fields to tables that don't have them

-- Add audit fields to occupations table (if they don't exist)
DO $$ 
BEGIN
  -- Check if createdAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'occupations' AND column_name = 'createdAt') THEN
    ALTER TABLE "occupations" ADD COLUMN "createdAt" timestamp DEFAULT now() NOT NULL;
  END IF;
  
  -- Check if updatedAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'occupations' AND column_name = 'updatedAt') THEN
    ALTER TABLE "occupations" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;
  END IF;
END $$;

-- Add audit fields to departments table (if they don't exist)
DO $$ 
BEGIN
  -- Check if createdAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'departments' AND column_name = 'createdAt') THEN
    ALTER TABLE "departments" ADD COLUMN "createdAt" timestamp DEFAULT now() NOT NULL;
  END IF;
  
  -- Check if updatedAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'departments' AND column_name = 'updatedAt') THEN
    ALTER TABLE "departments" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;
  END IF;
END $$;

-- Add audit fields to municipalities table (if they don't exist)
DO $$ 
BEGIN
  -- Check if createdAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'municipalities' AND column_name = 'createdAt') THEN
    ALTER TABLE "municipalities" ADD COLUMN "createdAt" timestamp DEFAULT now() NOT NULL;
  END IF;
  
  -- Check if updatedAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'municipalities' AND column_name = 'updatedAt') THEN
    ALTER TABLE "municipalities" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;
  END IF;
END $$;