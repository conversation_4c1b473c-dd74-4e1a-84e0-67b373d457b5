"use client";

import { useState } from "react";
import { useSignUp } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { <PERSON>, <PERSON>rk<PERSON>, Eye, EyeOff, Mail, Lock, Chrome, Heart, X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

interface SignUpModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToSignIn: () => void;
}

export default function SignUpModal({ isOpen, onClose, onSwitchToSignIn }: SignUpModalProps) {
  const { isLoaded, signUp, setActive } = useSignUp();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [pendingVerification, setPendingVerification] = useState(false);
  const [code, setCode] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);
    setError("");

    try {
      await signUp.create({
        emailAddress: email,
        password,
      });

      // Send email verification code
      await signUp.prepareEmailAddressVerification({ strategy: "email_code" });
      
      // Change to verification step
      setPendingVerification(true);
      setError("");
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Error al crear cuenta");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);
    setError("");

    try {
      const completeSignUp = await signUp.attemptEmailAddressVerification({
        code,
      });

      if (completeSignUp.status === "complete") {
        await setActive({ session: completeSignUp.createdSessionId });
        onClose();
        router.push("/onboarding");
      } else {
        setError("Error al verificar el código");
      }
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Código incorrecto");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    if (!isLoaded) return;
    
    setIsLoading(true);
    try {
      await signUp.authenticateWithRedirect({
        strategy: "oauth_google",
        redirectUrl: "/onboarding",
        redirectUrlComplete: "/onboarding",
      });
    } catch (err) {
      setError("Error al conectar con Google");
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail("");
    setPassword("");
    setError("");
    setShowPassword(false);
    setPendingVerification(false);
    setCode("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-0 border-0 bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute top-0 right-0 w-32 h-32 bg-emerald-200/20 rounded-full blur-2xl -mr-16 -mt-16 animate-pulse delay-300"></div>
        <div className="absolute bottom-0 left-0 w-28 h-28 bg-green-200/20 rounded-full blur-2xl -ml-14 -mb-14 animate-pulse"></div>
        
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-20 w-8 h-8 rounded-full bg-white/80 hover:bg-white shadow-md flex items-center justify-center transition-all duration-200"
        >
          <X className="h-4 w-4 text-gray-500" />
        </button>
        
        <div className="relative z-10 p-6">
          {/* Header */}
          <DialogHeader className="text-center mb-6">
            <div className="inline-flex items-center gap-3 mb-4 justify-center">
              <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-500 rounded-3xl flex items-center justify-center shadow-lg">
                <Baby className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                  Mundo Pediatra
                </h1>
                <div className="flex items-center gap-1 mt-0.5 justify-center">
                  <Heart className="h-1.5 w-1.5 text-pink-400 fill-current" />
                  <span className="text-xs text-gray-600">Para pequeños héroes</span>
                  <Heart className="h-1.5 w-1.5 text-pink-400 fill-current" />
                </div>
              </div>
            </div>
            
            <div className="space-y-1">
              <DialogTitle className="text-lg font-bold text-gray-800">
                {pendingVerification ? "Verifica tu email" : "¡Únete a la familia!"}
              </DialogTitle>
              <p className="text-gray-600 text-sm">
                {pendingVerification 
                  ? `Enviamos un código a ${email}` 
                  : "Crea tu cuenta para empezar"}
              </p>
            </div>
          </DialogHeader>

          {/* Form Card */}
          <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
            <CardContent className="p-5 space-y-4">
              
              {/* Google Sign Up */}
              <Button
                onClick={handleGoogleSignUp}
                disabled={isLoading}
                variant="outline"
                className="w-full h-10 border-2 border-gray-200 hover:border-green-300 hover:bg-green-50 transition-all duration-200"
              >
                <Chrome className="h-4 w-4 mr-2 text-red-500" />
                <span className="font-medium text-gray-700">Google</span>
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">O crea tu cuenta</span>
                  </div>
                </div>

              {/* Error Message */}
              {error && (
                <div className="p-2.5 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              {/* Sign Up Form */}
              <form onSubmit={handleSubmit} className="space-y-3">
                {/* Email */}
                <div className="space-y-1.5">
                  <Label htmlFor="email" className="text-gray-700 font-medium text-sm">
                    Email
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                      className="pl-10 h-10 border-2 border-gray-200 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-100 transition-all"
                    />
                  </div>
                </div>

                {/* Password */}
                <div className="space-y-1.5">
                  <Label htmlFor="password" className="text-gray-700 font-medium text-sm">
                    Contraseña
                  </Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Mínimo 8 caracteres"
                      required
                      className="pl-10 pr-10 h-10 border-2 border-gray-200 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-100 transition-all"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                  <p className="text-xs text-gray-500 mt-0.5">
                    Al menos 8 caracteres
                  </p>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-10 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Creando...
                    </div>
                  ) : (
                    "Crear Cuenta"
                  )}
                </Button>
              </form>
              
              {/* CAPTCHA element for Clerk */}
              <div id="clerk-captcha" className="mt-2"></div>

              {/* Footer */}
              <div className="text-center space-y-2">
                <p className="text-sm text-gray-600">
                  ¿Ya tienes cuenta?{" "}
                  <button 
                    onClick={onSwitchToSignIn}
                    className="text-green-600 hover:text-green-700 font-medium"
                  >
                    Inicia sesión
                  </button>
                </p>
                <p className="text-xs text-gray-500 leading-tight">
                  Al registrarte aceptas nuestros términos y política de privacidad
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 