import { db } from '@/db/drizzle';
import { consultories, activityTypes } from '@/db/schema';
import { eq } from 'drizzle-orm';

async function getDefaultIds() {
  try {
    console.log('🔍 Obteniendo IDs por defecto...\n');

    // Obtener consultorio activo
    const consultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);

    // Obtener tipo de actividad activo
    const activityType = await db
      .select()
      .from(activityTypes)
      .where(eq(activityTypes.isActive, true))
      .limit(1);

    console.log('📋 Consultorio:');
    if (consultory.length > 0) {
      console.log(`   ID: ${consultory[0].id}`);
      console.log(`   Nombre: ${consultory[0].name}`);
    } else {
      console.log('   ❌ No hay consultorios activos');
    }

    console.log('\n🏷️ Tipo de Actividad:');
    if (activityType.length > 0) {
      console.log(`   ID: ${activityType[0].id}`);
      console.log(`   Nombre: ${activityType[0].name}`);
    } else {
      console.log('   ❌ No hay tipos de actividad activos');
    }

    console.log('\n📝 Para usar en el código:');
    console.log(`const defaultConsultoryId = "${consultory[0]?.id || 'NO_CONSULTORY'}";`);
    console.log(`const defaultActivityId = "${activityType[0]?.id || 'NO_ACTIVITY'}";`);

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

getDefaultIds();