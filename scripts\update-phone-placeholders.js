// Script para actualizar todos los placeholders de teléfono hardcodeados
const fs = require('fs');
const path = require('path');

// Archivos identificados con placeholders de teléfono hardcodeados
const filesToUpdate = [
  {
    path: 'components/onboarding/general-info-form.tsx',
    pattern: /placeholder={selectedCountryPhoneCode \? `\${selectedCountryPhoneCode} 5555-5555` : "Teléfono[^"]*"}/g,
    replacement: 'placeholder={getPhonePlaceholder()}'
  },
  {
    path: 'app/(dashboard)/dashboard/doctor/expedientes/new/page.tsx',
    pattern: /placeholder="Teléfono de contacto"/g,
    replacement: 'placeholder={getPhonePlaceholder()}'
  },
  {
    path: 'app/(dashboard)/dashboard/admin/users/[id]/page.tsx',
    pattern: /placeholder="Teléfono[^"]*"/g,
    replacement: 'placeholder={getPhonePlaceholder()}'
  },
  {
    path: 'app/(dashboard)/dashboard/admin/users/[id]/edit/page.tsx',
    pattern: /placeholder="Número de teléfono"/g,
    replacement: 'placeholder={getPhonePlaceholder()}'
  },
  {
    path: 'app/(dashboard)/dashboard/admin/users/create/page.tsx',
    pattern: /placeholder="Número de teléfono"/g,
    replacement: 'placeholder={getPhonePlaceholder()}'
  }
];

function updateFile(file) {
  const filePath = path.join(process.cwd(), file.path);
  
  if (!fs.existsSync(filePath)) {
    console.log(`⏭️  Saltando ${file.path} (no existe)`);
    return false;
  }

  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;

    // Verificar si el archivo ya importa useRegionalConfig
    const hasRegionalConfigImport = content.includes('useRegionalConfig');
    
    // Aplicar los reemplazos
    const newContent = content.replace(file.pattern, file.replacement);
    
    if (newContent !== content) {
      modified = true;
      content = newContent;

      // Si modificamos y no tiene el import, agregarlo
      if (!hasRegionalConfigImport) {
        // Buscar dónde insertar el import
        const hookImportRegex = /import\s+{[^}]*}\s+from\s+['"]@\/hooks\/[^'"]+['"]/;
        const lastImportRegex = /import[^;]+from[^;]+;(?=\s*\n(?!import))/;
        
        const importStatement = "import { useRegionalConfig } from '@/hooks/use-regional-config';";
        
        if (hookImportRegex.test(content)) {
          // Si ya hay imports de hooks, agregar después del último
          content = content.replace(hookImportRegex, (match) => `${match}\n${importStatement}`);
        } else if (lastImportRegex.test(content)) {
          // Si no, agregar después del último import
          const matches = content.match(/import[^;]+from[^;]+;/g);
          if (matches) {
            const lastImport = matches[matches.length - 1];
            content = content.replace(lastImport, `${lastImport}\n${importStatement}`);
          }
        }

        // También necesitamos agregar la llamada al hook dentro del componente
        // Buscar el patrón del componente funcional
        const componentPattern = /export\s+(?:default\s+)?function\s+\w+[^{]*{\s*\n/;
        const componentMatch = content.match(componentPattern);
        
        if (componentMatch) {
          const hookCall = '  const { getPhonePlaceholder } = useRegionalConfig();\n';
          content = content.replace(componentMatch[0], `${componentMatch[0]}${hookCall}`);
        }
      }

      fs.writeFileSync(filePath, content);
      console.log(`✅ ${file.path} actualizado`);
      return true;
    } else {
      console.log(`ℹ️  ${file.path} (sin cambios)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error en ${file.path}:`, error.message);
    return false;
  }
}

console.log('🔄 Actualizando placeholders de teléfono...\n');

let updatedCount = 0;
for (const file of filesToUpdate) {
  if (updateFile(file)) {
    updatedCount++;
  }
}

console.log(`\n✅ Actualización completada: ${updatedCount}/${filesToUpdate.length} archivos modificados`);
console.log('\n📝 Nota: Revisa manualmente los archivos actualizados para asegurar que:');
console.log('   1. El hook useRegionalConfig se agregó correctamente');
console.log('   2. La función getPhonePlaceholder() se llama en el lugar correcto');
console.log('   3. No hay conflictos con otros hooks o estados');