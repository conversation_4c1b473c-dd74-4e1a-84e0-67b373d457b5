import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { mediaSources } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener medio de comunicación por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;

    const mediaSource = await db.select()
      .from(mediaSources)
      .where(eq(mediaSources.id, id))
      .limit(1);

    if (mediaSource.length === 0) {
      return NextResponse.json({ error: 'Medio de comunicación no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: mediaSource[0] 
    });

  } catch (error) {
    console.error('Error obteniendo medio de comunicación:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar medio de comunicación
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para eliminar medios de comunicación' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical';

    // Verificar que el medio de comunicación existe
    const existingSource = await db.select()
      .from(mediaSources)
      .where(eq(mediaSources.id, id))
      .limit(1);

    if (existingSource.length === 0) {
      return NextResponse.json({ error: 'Medio de comunicación no encontrado' }, { status: 404 });
    }

    const mediaSource = existingSource[0];

    // Validación: No permitir eliminar medio activo SOLO para eliminación lógica
    if (mediaSource.isActive && deleteType !== 'physical') {
      return NextResponse.json({ 
        error: 'No se puede eliminar un medio de comunicación activo', 
        suggestion: 'Desactive el medio de comunicación antes de eliminarlo',
        code: 'ACTIVE_MEDIA_SOURCE'
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física
      await db.delete(mediaSources)
        .where(eq(mediaSources.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Medio de comunicación eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedSource] = await db.update(mediaSources)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(mediaSources.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedSource,
        message: 'Medio de comunicación desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error eliminando medio de comunicación:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}