require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function cleanAndRestoreRejectedUser() {
  try {
    console.log('🧹 Limpiando datos existentes...');
    
    const userId = 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg';
    
    // Limpiar datos existentes
    await pool.query('DELETE FROM user_roles WHERE "userId" = $1', [userId]);
    await pool.query('DELETE FROM "registrationRequests" WHERE "userId" = $1', [userId]);
    
    console.log('✅ Datos existentes limpiados');
    
    console.log('🔄 Restaurando usuario rechazado...');
    
    // 1. Insertar rol rechazado
    await pool.query(`
      INSERT INTO user_roles (id, "userId", role, status, "rejectionReason", "rejectedAt", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), $1, 'assistant', 'rejected', 'Documentación incompleta - Se requiere completar certificaciones', NOW(), NOW(), NOW())
    `, [userId]);
    console.log('✅ Rol rechazado insertado');
    
    // 2. Insertar solicitud rechazada
    await pool.query(`
      INSERT INTO "registrationRequests" (id, "userId", role, status, "rejectionReason", "reviewedAt", "submittedAt", "updatedAt")
      VALUES (gen_random_uuid(), $1, 'assistant', 'rejected', 'Documentación incompleta - Se requiere completar certificaciones', NOW(), NOW(), NOW())
    `, [userId]);
    console.log('✅ Solicitud rechazada insertada');

    // 3. Actualizar status del usuario
    await pool.query(`
      UPDATE "user" 
      SET "overallStatus" = 'rejected', "updatedAt" = NOW() 
      WHERE id = $1
    `, [userId]);
    console.log('✅ Status del usuario actualizado a rejected');
    
    console.log('🎉 Usuario rechazado restaurado exitosamente');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

cleanAndRestoreRejectedUser();