'use client';

import { useState, useMemo, useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, User, Calendar, X, UserPlus, Plus, RefreshCw, Check, Clock, AlertCircle, CalendarDays, UserCheck, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';
import { DateInput } from '@/components/ui/date-input';
import { useRegionalConfig } from '@/hooks/use-regional-config';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  documentType?: string;
  documentNumber?: string;
  dateOfBirth?: string;
  gender?: string;
}

interface PatientSelectorProps {
  patients: Patient[];
  value: string;
  onChange: (patientId: string) => void;
  onPatientCreated?: (patient: Patient) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  autoFocus?: boolean;
  returnUrl?: string;
}

interface CreatePatientFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;
  gender: string;
  documentType: string;
  documentNumber: string;
  isMinor: boolean;
  sendInvitation: boolean;
}

interface CatalogItem {
  id: string | number;
  name: string;
}

export function PatientSelector({ 
  patients, 
  value, 
  onChange, 
  onPatientCreated,
  disabled = false, 
  placeholder = "Buscar y seleccionar paciente...",
  className,
  autoFocus = false,
  returnUrl
}: PatientSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [creating, setCreating] = useState(false);
  const { getPhonePlaceholder } = useRegionalConfig();
  const router = useRouter();
  
  // Ref para el input de búsqueda
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Estado para modal de citas
  const [appointmentModalOpen, setAppointmentModalOpen] = useState(false);
  const [selectedPatientAppointments, setSelectedPatientAppointments] = useState<{
    patient: Patient | null;
    appointments: Array<{
      id: string;
      date: Date;
      status: string;
      title: string;
    }>;
  }>({ patient: null, appointments: [] });
  
  // Estado para información de citas de pacientes
  const [patientAppointmentStatus, setPatientAppointmentStatus] = useState<Record<string, {
    totalUpcoming: number;
    nextAppointment: Date | null;
    hasScheduled: boolean;
    hasPendingConfirmation: boolean;
    hasConfirmed: boolean;
    statuses: string[];
    recentAppointments: Array<{
      id: string;
      date: Date;
      status: string;
      title: string;
    }>;
  }>>({});
  
  // Estado del formulario simplificado
  const [formData, setFormData] = useState<CreatePatientFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    documentType: '',
    documentNumber: '',
    isMinor: false,
    sendInvitation: false
  });
  
  // Estados para catálogos básicos
  const [documentTypes, setDocumentTypes] = useState<CatalogItem[]>([]);
  const [catalogsLoading, setCatalogsLoading] = useState(false);
  const [showFamilyRelationDialog, setShowFamilyRelationDialog] = useState(false);
  const [existingUser, setExistingUser] = useState<any>(null);
  const [creatingRelation, setCreatingRelation] = useState(false);
  
  // Estados para validación de email
  const [emailValidation, setEmailValidation] = useState<{
    isValidating: boolean;
    isSystemUser: boolean;
    systemUser: any;
    showGuardianModal: boolean;
  }>({
    isValidating: false,
    isSystemUser: false,
    systemUser: null,
    showGuardianModal: false,
  });

  // Filtrar pacientes basado en el término de búsqueda
  const filteredPatients = useMemo(() => {
    if (!searchTerm) return patients;
    
    const term = searchTerm.toLowerCase();
    return patients.filter(patient =>
      patient.firstName.toLowerCase().includes(term) ||
      patient.lastName.toLowerCase().includes(term) ||
      patient.email?.toLowerCase().includes(term) ||
      patient.phone?.includes(term) ||
      patient.documentNumber?.toLowerCase().includes(term)
    );
  }, [patients, searchTerm]);

  const selectedPatient = value ? patients.find(p => p.id === value) : null;

  // Funciones para crear paciente
  const loadDocumentTypes = async () => {
    try {
      setCatalogsLoading(true);
      const response = await fetch('/api/catalogs/document-types?active=true&limit=100');
      const data = await response.json();
      setDocumentTypes(data.data || []);
    } catch (error) {
      console.error('Error loading document types:', error);
      toast.error('Error cargando tipos de documento');
    } finally {
      setCatalogsLoading(false);
    }
  };

  const updateFormData = (field: keyof CreatePatientFormData, value: any) => {
    setFormData(prev => {
      const newData = { ...prev, [field]: value };
      return newData;
    });
    
    // Si es el campo email, validar en tiempo real
    if (field === 'email' && value && value.includes('@')) {
      validateEmail(value);
    } else if (field === 'email' && !value) {
      // Limpiar validación si se borra el email
      setEmailValidation({
        isValidating: false,
        isSystemUser: false,
        systemUser: null,
        showGuardianModal: false,
      });
    }
  };

  const validateEmail = async (email: string) => {
    setEmailValidation(prev => ({ ...prev, isValidating: true }));
    
    try {
      const response = await fetch('/api/patients/validate-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      
      if (result.success && result.isSystemUser) {
        setEmailValidation({
          isValidating: false,
          isSystemUser: true,
          systemUser: result.user,
          showGuardianModal: true,
        });
      } else {
        setEmailValidation({
          isValidating: false,
          isSystemUser: false,
          systemUser: null,
          showGuardianModal: false,
        });
      }
    } catch (error) {
      console.error('Error validating email:', error);
      setEmailValidation({
        isValidating: false,
        isSystemUser: false,
        systemUser: null,
        showGuardianModal: false,
      });
    }
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      dateOfBirth: '',
      gender: '',
      documentType: '',
      documentNumber: '',
      isMinor: false,
      sendInvitation: false
    });
    setEmailValidation({
      isValidating: false,
      isSystemUser: false,
      systemUser: null,
      showGuardianModal: false,
    });
  };

  const handleCreatePatientClick = () => {
    // **ÚNICO CAMBIO**: Navegar a página en lugar de modal
    const params = new URLSearchParams();
    if (returnUrl) {
      params.set('returnUrl', returnUrl);
    }
    
    router.push(`/dashboard/admin/patients/create?${params.toString()}`);
    setIsOpen(false);
  };

  // Auto-abrir dropdown cuando autoFocus es true
  useEffect(() => {
    if (autoFocus && !disabled && !value) {
      setIsOpen(true);
      // Poner foco en el input después de que se abra
      setTimeout(() => {
        inputRef.current?.focus();
      }, 100);
    }
  }, [autoFocus, disabled, value]);

  // Cargar estado de citas cuando se abra el dropdown
  useEffect(() => {
    if (isOpen && patients.length > 0) {
      loadPatientAppointmentStatus();
    }
  }, [isOpen, patients]);

  const loadPatientAppointmentStatus = async () => {
    try {
      const patientIds = filteredPatients.slice(0, 20).map(p => p.id);
      if (patientIds.length === 0) return;

      // Usar GET con query params como espera la API
      const queryParams = new URLSearchParams({
        patientIds: patientIds.join(',')
      });

      const response = await fetch(`/api/patients/appointment-status?${queryParams.toString()}`, {
        method: 'GET',
      });

      if (response.ok) {
        const data = await response.json();
        setPatientAppointmentStatus(data.data || {}); // Corregido: data.data en lugar de data.status
      }
    } catch (error) {
      console.error('Error loading patient appointment status:', error);
    }
  };

  const translateGender = (gender: string) => {
    const genderMap: { [key: string]: string } = {
      'male': 'Masculino',
      'female': 'Femenino',
      'masculino': 'Masculino',
      'femenino': 'Femenino',
      'otros': 'Otros',
      'other': 'Otros'
    };
    return genderMap[gender.toLowerCase()] || 'Otros';
  };

  const getGenderIcon = (gender: string) => {
    const translatedGender = translateGender(gender);
    switch (translatedGender) {
      case 'Masculino': return UserCheck;
      case 'Femenino': return UserCheck;
      case 'Otros': return Users;
      default: return Users;
    }
  };

  const getGenderColor = (gender: string) => {
    const translatedGender = translateGender(gender);
    switch (translatedGender) {
      case 'Masculino': return 'bg-blue-100 text-blue-700';
      case 'Femenino': return 'bg-pink-100 text-pink-700';
      case 'Otros': return 'bg-gray-100 text-gray-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const formatDateSubtle = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dateStr = date.toDateString();
    const todayStr = today.toDateString();
    const tomorrowStr = tomorrow.toDateString();
    
    if (dateStr === todayStr) {
      return 'hoy';
    } else if (dateStr === tomorrowStr) {
      return 'mañana';
    } else {
      // Formato corto: "15 Ene"
      return date.toLocaleDateString('es-ES', { 
        day: 'numeric', 
        month: 'short'
      });
    }
  };

  const formatDateFull = (date: Date) => {
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const dateStr = date.toDateString();
    const todayStr = today.toDateString();
    const tomorrowStr = tomorrow.toDateString();
    
    if (dateStr === todayStr) {
      return 'Hoy';
    } else if (dateStr === tomorrowStr) {
      return 'Mañana';
    } else {
      // Formato completo: "Lunes, 15 de enero"
      return date.toLocaleDateString('es-ES', { 
        weekday: 'long',
        day: 'numeric', 
        month: 'long'
      });
    }
  };

  const openAppointmentModal = (patient: Patient) => {
    const status = patientAppointmentStatus[patient.id];
    if (status && status.recentAppointments.length > 0) {
      setSelectedPatientAppointments({
        patient,
        appointments: status.recentAppointments
      });
      setAppointmentModalOpen(true);
    }
  };

  const getStatusText = (status: string) => {
    const statusMap: { [key: string]: string } = {
      'scheduled': 'Programada',
      'pending_confirmation': 'Pendiente de Confirmación',
      'confirmed': 'Confirmada',
      'checked_in': 'Registrado',
      'in_progress': 'En Progreso',
      'completed': 'Completada',
      'cancelled': 'Cancelada',
      'no_show': 'No Asistió'
    };
    return statusMap[status?.toLowerCase()] || 'Desconocido';
  };

  const getStatusColor = (status: string) => {
    const colorMap: { [key: string]: string } = {
      'scheduled': 'text-amber-600',
      'pending_confirmation': 'text-orange-600',
      'confirmed': 'text-emerald-600',
      'checked_in': 'text-teal-600',
      'in_progress': 'text-blue-600',
      'completed': 'text-green-600',
      'cancelled': 'text-red-600',
      'no_show': 'text-gray-600'
    };
    return colorMap[status?.toLowerCase()] || 'text-gray-600';
  };

  // Generar badges dinámicos para cada paciente
  const getPatientBadges = (patient: Patient) => {
    const badges = [];
    const status = patientAppointmentStatus[patient.id];

    // Badge de género con icono y color específico
    if (patient.gender) {
      const GenderIcon = getGenderIcon(patient.gender);
      badges.push({
        icon: GenderIcon,
        text: translateGender(patient.gender),
        variant: 'secondary' as const,
        color: getGenderColor(patient.gender)
      });
    }

    // Badges de citas con fecha sutil
    if (status && status.totalUpcoming > 0) {
      // Badge principal de citas con fecha sutil
      const nextDateText = status.nextAppointment 
        ? ` · ${formatDateSubtle(new Date(status.nextAppointment))}`
        : '';
      
      badges.push({
        icon: Calendar,
        text: `${status.totalUpcoming} cita${status.totalUpcoming > 1 ? 's' : ''}${nextDateText}`,
        variant: 'default' as const,
        color: 'bg-blue-100 text-blue-700',
        clickable: true,
        patientId: patient.id
      });

      // Badge de estado con icono (basado en los statuses de las citas)
      if (status.hasConfirmed) {
        badges.push({
          icon: Check,
          text: 'Confirmada',
          variant: 'default' as const,
          color: 'bg-emerald-100 text-emerald-700'
        });
      } else if (status.hasScheduled) {
        badges.push({
          icon: Clock,
          text: 'Programada',
          variant: 'secondary' as const,
          color: 'bg-amber-100 text-amber-700'
        });
      }
      
      // Verificar si hay citas pendientes de confirmación
      if (status.hasPendingConfirmation && !status.hasConfirmed) {
        badges.push({
          icon: AlertCircle,
          text: 'Pendiente Confirmación',
          variant: 'secondary' as const,
          color: 'bg-orange-100 text-orange-700'
        });
      }
    }

    return badges;
  };



  if (disabled && selectedPatient) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md', className)}>
        <div className="flex items-center gap-2">
          <User className="h-4 w-4 text-gray-500" />
          <span className="text-sm text-gray-700">
            {selectedPatient.firstName} {selectedPatient.lastName}
          </span>
          {selectedPatient.email && (
            <span className="text-xs text-gray-500">({selectedPatient.email})</span>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      {/* Campo de entrada */}
      <div className="relative">
        <Input
          ref={inputRef}
          value={selectedPatient ? `${selectedPatient.firstName} ${selectedPatient.lastName}` : searchTerm}
          onChange={(e) => {
            if (!selectedPatient) {
              setSearchTerm(e.target.value);
            }
          }}
          onFocus={() => {
            setIsOpen(true);
          }}
          placeholder={placeholder}
          disabled={disabled}
          className="pr-10"
        />
        
        <div className="absolute inset-y-0 right-0 flex items-center pr-3">
          {selectedPatient ? (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => {
                onChange('');
                setSearchTerm('');
                setIsOpen(true);
                setTimeout(() => {
                  inputRef.current?.focus();
                }, 50);
              }}
              className="h-6 w-6 p-0 hover:bg-gray-100"
            >
              <X className="h-4 w-4" />
            </Button>
          ) : (
            <Search className="h-4 w-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Dropdown de pacientes */}
      {isOpen && !disabled && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          
          <Card className="absolute top-full left-0 right-0 z-20 mt-1 shadow-lg border-2 border-gray-200 max-h-80 overflow-hidden">
            <CardContent className="p-0">
              <div className="max-h-80 overflow-y-auto">
                <Button
                  type="button"
                  variant="outline"
                  onClick={handleCreatePatientClick}
                  className="w-full border-emerald-200 bg-emerald-50 hover:bg-emerald-100 text-emerald-700 border-dashed"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Crear Nuevo Paciente
                </Button>
                
                {filteredPatients.length > 0 ? (
                  filteredPatients.map((patient) => {
                    const badges = getPatientBadges(patient);
                    
                    return (
                      <Button
                        key={patient.id}
                        type="button"
                        variant="ghost"
                        onClick={() => {
                          onChange(patient.id);
                          setIsOpen(false);
                          setSearchTerm('');
                          inputRef.current?.blur();
                        }}
                        className="w-full justify-start p-4 h-auto text-left hover:bg-gray-50 border-b border-gray-100 last:border-b-0"
                      >
                        <div className="flex items-center gap-3 w-full">
                          <User className="h-4 w-4 text-gray-500 flex-shrink-0" />
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-gray-900 truncate">
                                {patient.firstName} {patient.lastName}
                              </span>
                            </div>
                            
                            <div className="flex flex-col gap-1">
                              {patient.email && (
                                <span className="text-xs text-gray-600 truncate">
                                  {patient.email}
                                </span>
                              )}
                              
                              {patient.phone && (
                                <span className="text-xs text-gray-600">
                                  {patient.phone}
                                </span>
                              )}
                            </div>
                            
                            {badges.length > 0 && (
                              <div className="flex flex-wrap gap-1 mt-2">
                                {badges.map((badge, index) => {
                                  const IconComponent = badge.icon;
                                  
                                  return (
                                    <Badge 
                                      key={index} 
                                      variant={badge.variant}
                                      className={cn(
                                        'text-xs px-2 py-0.5 flex items-center gap-1', 
                                        badge.color,
                                        badge.clickable && 'cursor-pointer hover:opacity-80 hover:scale-105 transition-all duration-200'
                                      )}
                                      onClick={badge.clickable ? (e) => {
                                        e.stopPropagation();
                                        if (badge.patientId) {
                                          openAppointmentModal(patient);
                                        }
                                      } : undefined}
                                    >
                                      {IconComponent && <IconComponent className="h-3 w-3 flex-shrink-0" />}
                                      <span className={badge.clickable ? 'underline-offset-2 hover:underline' : ''}>
                                        {badge.text}
                                      </span>
                                    </Badge>
                                  );
                                })}
                              </div>
                            )}
                          </div>
                        </div>
                      </Button>
                    );
                  })
                ) : searchTerm ? (
                  <div className="p-4 text-center text-gray-500">
                    <User className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">No se encontraron pacientes</p>
                    <div className="mt-3">
                      <Button
                        type="button"
                        onClick={handleCreatePatientClick}
                        className="bg-emerald-600 hover:bg-emerald-700 text-white"
                        size="sm"
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        Crear Nuevo Paciente
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="p-4 text-center text-gray-500">
                    <Search className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                    <p className="text-sm">Escribe para buscar pacientes</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Modal de citas del paciente */}
      <Dialog open={appointmentModalOpen} onOpenChange={setAppointmentModalOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              Citas de {selectedPatientAppointments.patient?.firstName} {selectedPatientAppointments.patient?.lastName}
            </DialogTitle>
            <DialogDescription>
              {(() => {
                const patient = selectedPatientAppointments.patient;
                const status = patient ? patientAppointmentStatus[patient.id] : null;
                const totalCitas = status?.totalUpcoming || 0;
                const showingCitas = selectedPatientAppointments.appointments.length;
                
                if (totalCitas > showingCitas) {
                  return `Mostrando ${showingCitas} de ${totalCitas} citas programadas`;
                } else {
                  return `${totalCitas} cita${totalCitas !== 1 ? 's' : ''} programada${totalCitas !== 1 ? 's' : ''}`;
                }
              })()}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-3 max-h-80 overflow-y-auto">
            {selectedPatientAppointments.appointments.map((appointment, index) => (
              <div key={appointment.id} className="flex items-start justify-between p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-100 hover:shadow-sm transition-shadow">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <div className="p-1 bg-blue-100 rounded-full">
                      <CalendarDays className="h-3 w-3 text-blue-600" />
                    </div>
                    <span className="font-semibold text-sm text-gray-900">
                      {formatDateFull(new Date(appointment.date))}
                    </span>
                  </div>
                  <p className="text-sm text-gray-700 font-medium mb-1">
                    {appointment.title || 'Cita médica'}
                  </p>
                  <div className="flex items-center gap-2">
                    <Clock className="h-3 w-3 text-gray-400" />
                    <span className="text-xs text-gray-500">
                      {new Date(appointment.date).toLocaleDateString('es-ES', {
                        day: '2-digit',
                        month: '2-digit',
                        year: '2-digit'
                      })}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col items-end gap-2">
                  <Badge 
                    variant="secondary" 
                    className={cn('text-xs font-medium flex items-center gap-1', 
                      appointment.status === 'scheduled' ? 'bg-amber-100 text-amber-700' :
                      appointment.status === 'pending_confirmation' ? 'bg-orange-100 text-orange-700' :
                      appointment.status === 'confirmed' ? 'bg-emerald-100 text-emerald-700' :
                      appointment.status === 'checked_in' ? 'bg-teal-100 text-teal-700' :
                      appointment.status === 'in_progress' ? 'bg-blue-100 text-blue-700' :
                      appointment.status === 'completed' ? 'bg-green-100 text-green-700' :
                      appointment.status === 'cancelled' ? 'bg-red-100 text-red-700' :
                      appointment.status === 'no_show' ? 'bg-gray-100 text-gray-700' :
                      'bg-gray-100 text-gray-600'
                    )}
                  >
                    {appointment.status === 'scheduled' && <Clock className="h-3 w-3" />}
                    {appointment.status === 'pending_confirmation' && <AlertCircle className="h-3 w-3" />}
                    {appointment.status === 'confirmed' && <Check className="h-3 w-3" />}
                    {appointment.status === 'checked_in' && <UserCheck className="h-3 w-3" />}
                    {appointment.status === 'in_progress' && <RefreshCw className="h-3 w-3" />}
                    {appointment.status === 'completed' && <Check className="h-3 w-3" />}
                    {appointment.status === 'cancelled' && <X className="h-3 w-3" />}
                    {appointment.status === 'no_show' && <X className="h-3 w-3" />}
                    <span>{getStatusText(appointment.status)}</span>
                  </Badge>
                </div>
              </div>
            ))}
          </div>

          {selectedPatientAppointments.appointments.length === 0 && (
            <div className="text-center py-6 text-gray-500">
              <Calendar className="h-12 w-12 mx-auto mb-2 text-gray-300" />
              <p className="text-sm">No hay citas disponibles</p>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}