'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  AlertTriangle, 
  Database, 
  Trash2, 
  Shield, 
  RefreshCw,
  Info,
  CheckCircle,
  XCircle,
  Users,
  FileText,
  Calendar,
  Heart,
  History,
  Eye,
  ChevronDown,
  ChevronUp
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface DatabaseStats {
  totalRecords: number;
  totalUsers: number;
  usersToDelete: number;
  recordsToDelete: number;
  willRemain: {
    admins: number;
    catalogs: string;
  };
  usersByRole: Record<string, number>;
  currentRecords: Record<string, number>;
}

interface ResetResult {
  success: boolean;
  message: string;
  summary: {
    recordsDeleted: number;
    usersDeleted: number;
    clerkUsersDeleted: number;
    clerkDeletionErrors: number;
    remainingUsers: number;
    executedBy: string;
    executedAt: string;
  };
  details: {
    database: {
      before: Record<string, number>;
      after: Record<string, number>;
      usersByRole: Record<string, number>;
    };
    clerk: {
      stats: {
        totalProcessed: number;
        successful: number;
        failed: number;
        withoutClerkId: number;
      };
      results: Array<{
        success: boolean;
        user: string;
        clerkUserId: string | null;
        error?: string;
        note?: string;
      }>;
    };
    remainingUsers: Array<{
      id: string;
      firstName: string;
      lastName: string;
      role: string;
    }>;
  };
}

interface AuditLog {
  id: string;
  action: string;
  executedBy: string;
  executedByName: string;
  timestamp: string;
  details: any;
  success: boolean;
  errorMessage?: string;
  ipAddress?: string;
  userAgent?: string;
  createdAt: string;
}

interface AuditLogsResponse {
  success: boolean;
  data: AuditLog[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  stats: {
    totalLogs: number;
    actionBreakdown: Record<string, {
      total: number;
      successful: number;
      failed: number;
    }>;
  };
}

interface AppointmentsPreview {
  totalRecords: number;
  breakdown: {
    appointments: number;
    medicalConsultations: number;
    medicalRecords: number;
    patientInvitations: number;
    guardianPatientRelations: number;
  };
  willKeep: string[];
}

interface AppointmentsCleanResult {
  success: boolean;
  message: string;
  summary: {
    cleanType: string;
    totalDeleted: number;
    remainingRecords: number;
    executedBy: string;
    executedAt: string;
  };
  details: {
    before: Record<string, number>;
    deleted: Record<string, number>;
    after: Record<string, number>;
  };
}

export default function AdminUtilitiesPage() {
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [stats, setStats] = useState<DatabaseStats | null>(null);
  const [resetResult, setResetResult] = useState<ResetResult | null>(null);
  
  // Estado del formulario de confirmación
  const [confirmationPhrase, setConfirmationPhrase] = useState('');
  const [confirmDelete, setConfirmDelete] = useState(false);
  
  // Estado para limpieza de citas
  const [appointmentsLoading, setAppointmentsLoading] = useState(false);
  const [appointmentsPreviewLoading, setAppointmentsPreviewLoading] = useState(false);
  const [appointmentsPreview, setAppointmentsPreview] = useState<AppointmentsPreview | null>(null);
  const [appointmentsResult, setAppointmentsResult] = useState<AppointmentsCleanResult | null>(null);
  const [appointmentsCleanType, setAppointmentsCleanType] = useState<'appointments-only' | 'appointments-and-relations'>('appointments-only');
  const [appointmentsConfirmationPhrase, setAppointmentsConfirmationPhrase] = useState('');
  const [appointmentsConfirmDelete, setAppointmentsConfirmDelete] = useState(false);
  
  // Estado para logs de auditoría
  const [auditLogs, setAuditLogs] = useState<AuditLogsResponse | null>(null);
  const [logsLoading, setLogsLoading] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  const [expandedLog, setExpandedLog] = useState<string | null>(null);
  
  const REQUIRED_PHRASE = 'BORRAR TODA LA INFORMACION MEDICA';
  const APPOINTMENTS_PHRASES = {
    'appointments-only': 'LIMPIAR SOLO CITAS MEDICAS',
    'appointments-and-relations': 'LIMPIAR CITAS Y RELACIONES'
  };

  const getPreviewStats = async () => {
    setPreviewLoading(true);
    try {
      const response = await fetch('/api/admin/utilities/reset-database', {
        method: 'GET'
      });

      const data = await response.json();

      if (response.ok) {
        setStats({
          ...data.preview,
          currentRecords: data.details.currentRecords,
          usersByRole: data.details.usersByRole
        });
        setResetResult(null); // Limpiar resultados anteriores
      } else {
        toast.error(data.error || 'Error obteniendo estadísticas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
    } finally {
      setPreviewLoading(false);
    }
  };

  const executeReset = async () => {
    if (confirmationPhrase !== REQUIRED_PHRASE) {
      toast.error('Debe escribir la frase de confirmación exacta');
      return;
    }

    if (!confirmDelete) {
      toast.error('Debe confirmar que desea proceder con la eliminación');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/admin/utilities/reset-database', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          confirmationPhrase,
          confirmDelete
        })
      });

      const data = await response.json();

      if (response.ok) {
        setResetResult(data);
        toast.success('Base de datos limpiada exitosamente');
        
        // Limpiar formulario
        setConfirmationPhrase('');
        setConfirmDelete(false);
        setStats(null);
        
        // Recargar logs de auditoría si están visibles
        if (showLogs) {
          loadAuditLogs();
        }
      } else {
        toast.error(data.error || 'Error ejecutando limpieza');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  const loadAuditLogs = async () => {
    setLogsLoading(true);
    try {
      const response = await fetch('/api/admin/utilities/audit-logs?limit=10');
      const data = await response.json();

      if (response.ok) {
        setAuditLogs(data);
      } else {
        toast.error(data.error || 'Error cargando logs de auditoría');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
    } finally {
      setLogsLoading(false);
    }
  };

  const toggleLogs = async () => {
    setShowLogs(!showLogs);
    if (!showLogs && !auditLogs) {
      await loadAuditLogs();
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy HH:mm:ss', { locale: es });
    } catch (error) {
      return dateString;
    }
  };

  const getAppointmentsPreview = async () => {
    setAppointmentsPreviewLoading(true);
    try {
      const response = await fetch('/api/admin/utilities/clean-appointments', {
        method: 'GET'
      });

      const data = await response.json();

      if (response.ok) {
        setAppointmentsPreview(data.preview);
        setAppointmentsResult(null); // Limpiar resultados anteriores
      } else {
        toast.error(data.error || 'Error obteniendo vista previa de citas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
    } finally {
      setAppointmentsPreviewLoading(false);
    }
  };

  const executeAppointmentsClean = async () => {
    const requiredPhrase = APPOINTMENTS_PHRASES[appointmentsCleanType];
    
    if (appointmentsConfirmationPhrase !== requiredPhrase) {
      toast.error('Debe escribir la frase de confirmación exacta');
      return;
    }

    if (!appointmentsConfirmDelete) {
      toast.error('Debe confirmar que desea proceder con la eliminación');
      return;
    }

    setAppointmentsLoading(true);
    try {
      const response = await fetch('/api/admin/utilities/clean-appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          confirmationPhrase: appointmentsConfirmationPhrase,
          confirmDelete: appointmentsConfirmDelete,
          cleanType: appointmentsCleanType
        })
      });

      const data = await response.json();

      if (response.ok) {
        setAppointmentsResult(data);
        toast.success('Limpieza de citas completada exitosamente');
        
        // Limpiar formulario
        setAppointmentsConfirmationPhrase('');
        setAppointmentsConfirmDelete(false);
        setAppointmentsPreview(null);
        
        // Recargar logs de auditoría si están visibles
        if (showLogs) {
          loadAuditLogs();
        }
      } else {
        toast.error(data.error || 'Error ejecutando limpieza de citas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error de conexión');
    } finally {
      setAppointmentsLoading(false);
    }
  };

  const getActionBadgeColor = (action: string) => {
    switch (action) {
      case 'DATABASE_RESET':
        return 'destructive';
      case 'APPOINTMENTS_CLEAN':
        return 'destructive';
      default:
        return 'default';
    }
  };

  const isFormValid = () => {
    return confirmationPhrase === REQUIRED_PHRASE && confirmDelete;
  };

  const isAppointmentsFormValid = () => {
    const requiredPhrase = APPOINTMENTS_PHRASES[appointmentsCleanType];
    return appointmentsConfirmationPhrase === requiredPhrase && appointmentsConfirmDelete;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-3">
        <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-red-100">
          <Database className="h-5 w-5 text-red-600" />
        </div>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Utilidades del Sistema</h1>
          <p className="text-gray-600">Herramientas administrativas avanzadas</p>
        </div>
      </div>

      {/* Warning Card */}
      <Card className="border-red-200 bg-red-50">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-800">
            <AlertTriangle className="h-5 w-5" />
            ⚠️ ZONA DE PELIGRO
          </CardTitle>
          <CardDescription className="text-red-700">
            Las siguientes herramientas pueden eliminar datos permanentemente. 
            Úselas con extrema precaución.
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Reset Database Tool */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-600" />
            Reiniciar Base de Datos
          </CardTitle>
          <CardDescription>
            Elimina TODA la información médica, usuarios (excepto administradores) y datos del sistema.
            Los catálogos se mantendrán intactos.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Preview Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Vista Previa de Eliminación</span>
            </div>
            
            <Button 
              onClick={getPreviewStats}
              disabled={previewLoading}
              variant="outline"
              className="w-full"
            >
              {previewLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Obteniendo estadísticas...
                </>
              ) : (
                <>
                  <Database className="h-4 w-4 mr-2" />
                  Ver qué se eliminará
                </>
              )}
            </Button>

            {stats && (
              <Card className="bg-gray-50">
                <CardContent className="pt-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Registros */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">📊 Registros Médicos</h4>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Solicitudes de registro:</span>
                          <Badge variant="destructive">{stats.currentRecords.registrationRequests || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Citas médicas:</span>
                          <Badge variant="destructive">{stats.currentRecords.appointments || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Slots de citas:</span>
                          <Badge variant="destructive">{stats.currentRecords.appointmentSlots || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Expedientes:</span>
                          <Badge variant="destructive">{stats.currentRecords.medicalRecords || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Consultas:</span>
                          <Badge variant="destructive">{stats.currentRecords.medicalConsultations || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Historia médica:</span>
                          <Badge variant="destructive">{stats.currentRecords.patientMedicalHistory || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Documentos médicos:</span>
                          <Badge variant="destructive">{stats.currentRecords.medicalDocuments || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Horarios médicos:</span>
                          <Badge variant="destructive">{stats.currentRecords.doctorSchedules || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Excepciones horario:</span>
                          <Badge variant="destructive">{stats.currentRecords.doctorScheduleExceptions || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Precios de servicios:</span>
                          <Badge variant="destructive">{stats.currentRecords.doctorServicePrices || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Relaciones guardian-paciente:</span>
                          <Badge variant="destructive">{stats.currentRecords.guardianRelations || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Invitaciones pacientes:</span>
                          <Badge variant="destructive">{stats.currentRecords.patientInvitations || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Medicamentos favoritos:</span>
                          <Badge variant="destructive">{stats.currentRecords.doctorFavoriteMedications || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Plantillas prescripción:</span>
                          <Badge variant="destructive">{stats.currentRecords.prescriptionTemplates || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Relaciones asistente-doctor:</span>
                          <Badge variant="destructive">{stats.currentRecords.assistantDoctorRelations || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Códigos de asociación:</span>
                          <Badge variant="destructive">{stats.currentRecords.associationCodes || 0}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Notificaciones:</span>
                          <Badge variant="destructive">{stats.currentRecords.notifications || 0}</Badge>
                        </div>
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Total registros:</span>
                          <Badge variant="destructive">{stats.totalRecords}</Badge>
                        </div>
                      </div>
                    </div>

                    {/* Usuarios */}
                    <div className="space-y-3">
                      <h4 className="font-semibold text-gray-900">👥 Usuarios</h4>
                      <div className="space-y-2">
                        {Object.entries(stats.usersByRole).map(([role, count]) => (
                          <div key={role} className="flex justify-between">
                            <span className="text-sm capitalize">{role}:</span>
                            <Badge variant={role === 'admin' ? 'default' : 'destructive'}>
                              {count}
                            </Badge>
                          </div>
                        ))}
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Se eliminarán:</span>
                          <Badge variant="destructive">{stats.usersToDelete}</Badge>
                        </div>
                        <div className="flex justify-between font-medium text-green-700">
                          <span>Permanecerán:</span>
                          <Badge variant="default">{stats.willRemain.admins} admin(s)</Badge>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="mt-6 p-4 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2 text-green-800">
                      <Shield className="h-4 w-4" />
                      <span className="font-medium">Se mantendrán:</span>
                    </div>
                    <ul className="mt-2 text-sm text-green-700 space-y-1">
                      <li>• {stats.willRemain.admins} usuario(s) administrador(es)</li>
                      <li>• Todos los catálogos (medicamentos, especialidades, etc.)</li>
                      <li>• Configuración del sistema</li>
                    </ul>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <Separator />

          {/* Confirmation Form */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <span className="font-medium text-red-800">Confirmación de Seguridad</span>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="confirmation">
                  Escriba exactamente: <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{REQUIRED_PHRASE}</code>
                </Label>
                <Input
                  id="confirmation"
                  value={confirmationPhrase}
                  onChange={(e) => setConfirmationPhrase(e.target.value)}
                  placeholder="Escriba la frase de confirmación..."
                  className={`mt-2 ${confirmationPhrase === REQUIRED_PHRASE ? 'border-green-500' : 'border-red-300'}`}
                />
                {confirmationPhrase && confirmationPhrase !== REQUIRED_PHRASE && (
                  <p className="text-sm text-red-600 mt-1">
                    Frase incorrecta. Debe escribir exactamente como se muestra arriba.
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="confirm-delete"
                  checked={confirmDelete}
                  onCheckedChange={setConfirmDelete}
                />
                <Label htmlFor="confirm-delete" className="text-sm">
                  Confirmo que entiendo que esta acción eliminará PERMANENTEMENTE toda la información médica y usuarios (excepto administradores) del sistema
                </Label>
              </div>
            </div>

            <Button
              onClick={executeReset}
              disabled={loading || !isFormValid()}
              variant="destructive"
              className="w-full"
            >
              {loading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Ejecutando limpieza...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  EJECUTAR LIMPIEZA COMPLETA
                </>
              )}
            </Button>
          </div>

          {/* Results */}
          {resetResult && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <CheckCircle className="h-5 w-5" />
                  Limpieza Completada Exitosamente
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Resumen */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-green-900">📊 Resumen</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Registros eliminados:</span>
                        <Badge>{resetResult.summary.recordsDeleted}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Usuarios eliminados (BD):</span>
                        <Badge>{resetResult.summary.usersDeleted}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Usuarios eliminados (Clerk):</span>
                        <Badge>{resetResult.summary.clerkUsersDeleted}</Badge>
                      </div>
                      {resetResult.summary.clerkDeletionErrors > 0 && (
                        <div className="flex justify-between">
                          <span>Errores en Clerk:</span>
                          <Badge variant="destructive">{resetResult.summary.clerkDeletionErrors}</Badge>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span>Usuarios restantes:</span>
                        <Badge variant="default">{resetResult.summary.remainingUsers}</Badge>
                      </div>
                    </div>
                  </div>

                  {/* Detalles */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-green-900">ℹ️ Información</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium">Ejecutado por:</span>
                        <p className="text-green-700">{resetResult.summary.executedBy}</p>
                      </div>
                      <div>
                        <span className="font-medium">Fecha:</span>
                        <p className="text-green-700">
                          {new Date(resetResult.summary.executedAt).toLocaleString()}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Usuarios restantes */}
                {resetResult.details.remainingUsers.length > 0 && (
                  <div className="mt-6 p-4 bg-white rounded-lg border">
                    <h4 className="font-semibold text-gray-900 mb-3">👤 Usuarios que permanecen en el sistema:</h4>
                    <div className="space-y-2">
                      {resetResult.details.remainingUsers.map((user) => (
                        <div key={user.id} className="flex items-center gap-3">
                          <Badge variant="default">{user.role}</Badge>
                          <span className="text-sm">{user.firstName} {user.lastName}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Errores de Clerk si los hay */}
                {resetResult.details.clerk.stats.failed > 0 && (
                  <div className="mt-6 p-4 bg-red-50 rounded-lg border border-red-200">
                    <h4 className="font-semibold text-red-900 mb-3 flex items-center gap-2">
                      <XCircle className="h-4 w-4" />
                      Errores en eliminación de Clerk:
                    </h4>
                    <div className="space-y-2">
                      {resetResult.details.clerk.results.filter(r => !r.success).map((result, index) => (
                        <div key={index} className="text-sm text-red-700">
                          <span className="font-medium">{result.user}:</span> {result.error}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Clean Appointments Tool */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5 text-orange-600" />
            Limpiar Citas Médicas
          </CardTitle>
          <CardDescription>
            Elimina todas las citas médicas, consultas y expedientes relacionados. 
            Los usuarios, consultorios y catálogos se mantienen intactos.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Clean Type Selection */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Tipo de Limpieza</span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card className={`cursor-pointer border-2 ${appointmentsCleanType === 'appointments-only' ? 'border-blue-500 bg-blue-50' : 'border-gray-200'}`}>
                <CardContent className="pt-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="appointments-only"
                      name="cleanType"
                      value="appointments-only"
                      checked={appointmentsCleanType === 'appointments-only'}
                      onChange={(e) => setAppointmentsCleanType(e.target.value as any)}
                      className="h-4 w-4"
                    />
                    <Label htmlFor="appointments-only" className="cursor-pointer">
                      <div>
                        <div className="font-medium">Solo Citas Médicas</div>
                        <div className="text-sm text-gray-600">Borra citas, consultas y expedientes. Mantiene usuarios y relaciones familiares.</div>
                      </div>
                    </Label>
                  </div>
                </CardContent>
              </Card>

              <Card className={`cursor-pointer border-2 ${appointmentsCleanType === 'appointments-and-relations' ? 'border-red-500 bg-red-50' : 'border-gray-200'}`}>
                <CardContent className="pt-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="radio"
                      id="appointments-and-relations"
                      name="cleanType"
                      value="appointments-and-relations"
                      checked={appointmentsCleanType === 'appointments-and-relations'}
                      onChange={(e) => setAppointmentsCleanType(e.target.value as any)}
                      className="h-4 w-4"
                    />
                    <Label htmlFor="appointments-and-relations" className="cursor-pointer">
                      <div>
                        <div className="font-medium">Citas y Relaciones</div>
                        <div className="text-sm text-gray-600">Borra citas, consultas, expedientes Y relaciones familiares. Más agresivo.</div>
                      </div>
                    </Label>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Preview Section */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Eye className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-800">Vista Previa de Eliminación</span>
            </div>
            
            <Button 
              onClick={getAppointmentsPreview}
              disabled={appointmentsPreviewLoading}
              variant="outline"
              className="w-full"
            >
              {appointmentsPreviewLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Obteniendo vista previa...
                </>
              ) : (
                <>
                  <Calendar className="h-4 w-4 mr-2" />
                  Ver qué se eliminará
                </>
              )}
            </Button>

            {appointmentsPreview && (
              <Card className="bg-orange-50 border-orange-200">
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <h4 className="font-semibold text-orange-900">📊 Datos que se eliminarán</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Citas médicas:</span>
                          <Badge variant="destructive">{appointmentsPreview.breakdown.appointments}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Consultas médicas:</span>
                          <Badge variant="destructive">{appointmentsPreview.breakdown.medicalConsultations}</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Expedientes médicos:</span>
                          <Badge variant="destructive">{appointmentsPreview.breakdown.medicalRecords}</Badge>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm">Invitaciones pacientes:</span>
                          <Badge variant="destructive">{appointmentsPreview.breakdown.patientInvitations}</Badge>
                        </div>
                        {appointmentsCleanType === 'appointments-and-relations' && (
                          <div className="flex justify-between">
                            <span className="text-sm">Relaciones familiares:</span>
                            <Badge variant="destructive">{appointmentsPreview.breakdown.guardianPatientRelations}</Badge>
                          </div>
                        )}
                        <Separator />
                        <div className="flex justify-between font-medium">
                          <span>Total a eliminar:</span>
                          <Badge variant="destructive">{appointmentsPreview.totalRecords}</Badge>
                        </div>
                      </div>
                    </div>

                    <div className="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex items-center gap-2 text-green-800">
                        <Shield className="h-4 w-4" />
                        <span className="font-medium">Se mantendrán:</span>
                      </div>
                      <ul className="mt-2 text-sm text-green-700 space-y-1">
                        {appointmentsPreview.willKeep.map((item, index) => (
                          <li key={index}>• {item}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          <Separator />

          {/* Confirmation Form */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <span className="font-medium text-orange-800">Confirmación de Seguridad</span>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="appointments-confirmation">
                  Escriba exactamente: <code className="bg-gray-100 px-2 py-1 rounded text-sm font-mono">{APPOINTMENTS_PHRASES[appointmentsCleanType]}</code>
                </Label>
                <Input
                  id="appointments-confirmation"
                  value={appointmentsConfirmationPhrase}
                  onChange={(e) => setAppointmentsConfirmationPhrase(e.target.value)}
                  placeholder="Escriba la frase de confirmación..."
                  className={`mt-2 ${appointmentsConfirmationPhrase === APPOINTMENTS_PHRASES[appointmentsCleanType] ? 'border-green-500' : 'border-orange-300'}`}
                />
                {appointmentsConfirmationPhrase && appointmentsConfirmationPhrase !== APPOINTMENTS_PHRASES[appointmentsCleanType] && (
                  <p className="text-sm text-orange-600 mt-1">
                    Frase incorrecta. Debe escribir exactamente como se muestra arriba.
                  </p>
                )}
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="confirm-appointments-delete"
                  checked={appointmentsConfirmDelete}
                  onCheckedChange={setAppointmentsConfirmDelete}
                />
                <Label htmlFor="confirm-appointments-delete" className="text-sm">
                  {appointmentsCleanType === 'appointments-only' 
                    ? 'Confirmo que entiendo que esta acción eliminará PERMANENTEMENTE todas las citas médicas del sistema'
                    : 'Confirmo que entiendo que esta acción eliminará PERMANENTEMENTE todas las citas médicas Y relaciones familiares del sistema'
                  }
                </Label>
              </div>
            </div>

            <Button
              onClick={executeAppointmentsClean}
              disabled={appointmentsLoading || !isAppointmentsFormValid()}
              variant="destructive"
              className="w-full"
            >
              {appointmentsLoading ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Ejecutando limpieza...
                </>
              ) : (
                <>
                  <Trash2 className="h-4 w-4 mr-2" />
                  {appointmentsCleanType === 'appointments-only' 
                    ? 'LIMPIAR SOLO CITAS MÉDICAS' 
                    : 'LIMPIAR CITAS Y RELACIONES'
                  }
                </>
              )}
            </Button>
          </div>

          {/* Results */}
          {appointmentsResult && (
            <Card className="bg-green-50 border-green-200">
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-green-800">
                  <CheckCircle className="h-5 w-5" />
                  Limpieza de Citas Completada
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Resumen */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-green-900">📊 Resumen</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Tipo de limpieza:</span>
                        <Badge>{appointmentsResult.summary.cleanType === 'appointments-only' ? 'Solo Citas' : 'Citas y Relaciones'}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Registros eliminados:</span>
                        <Badge>{appointmentsResult.summary.totalDeleted}</Badge>
                      </div>
                      <div className="flex justify-between">
                        <span>Registros restantes:</span>
                        <Badge variant="default">{appointmentsResult.summary.remainingRecords}</Badge>
                      </div>
                    </div>
                  </div>

                  {/* Detalles */}
                  <div className="space-y-3">
                    <h4 className="font-semibold text-green-900">ℹ️ Información</h4>
                    <div className="space-y-2 text-sm">
                      <div>
                        <span className="font-medium">Ejecutado por:</span>
                        <p className="text-green-700">{appointmentsResult.summary.executedBy}</p>
                      </div>
                      <div>
                        <span className="font-medium">Fecha:</span>
                        <p className="text-green-700">
                          {formatDate(appointmentsResult.summary.executedAt)}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Detalles de eliminación */}
                <div className="mt-6 p-4 bg-white rounded-lg border">
                  <h4 className="font-semibold text-gray-900 mb-3">📋 Detalle de registros eliminados:</h4>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    {Object.entries(appointmentsResult.details.deleted).map(([key, count]) => (
                      <div key={key} className="text-center">
                        <div className="text-lg font-bold text-gray-900">{count}</div>
                        <div className="text-gray-600 capitalize">{key.replace(/([A-Z])/g, ' $1').toLowerCase()}</div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Audit Logs Section */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <History className="h-5 w-5 text-blue-600" />
              <CardTitle>Logs de Auditoría</CardTitle>
            </div>
            <Button
              onClick={toggleLogs}
              variant="outline"
              size="sm"
            >
              {showLogs ? (
                <>
                  <ChevronUp className="h-4 w-4 mr-2" />
                  Ocultar Logs
                </>
              ) : (
                <>
                  <ChevronDown className="h-4 w-4 mr-2" />
                  Ver Logs
                </>
              )}
            </Button>
          </div>
          <CardDescription>
            Historial de operaciones administrativas críticas ejecutadas en el sistema
          </CardDescription>
        </CardHeader>
        
        {showLogs && (
          <CardContent>
            {logsLoading ? (
              <div className="flex items-center justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mr-2" />
                <span>Cargando logs de auditoría...</span>
              </div>
            ) : auditLogs ? (
              <div className="space-y-4">
                {/* Stats */}
                {auditLogs.stats && (
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-gray-900">{auditLogs.stats.totalLogs}</div>
                      <div className="text-sm text-gray-600">Total de Logs</div>
                    </div>
                    {Object.entries(auditLogs.stats.actionBreakdown).map(([action, stats]) => (
                      <div key={action} className="text-center">
                        <div className="text-lg font-semibold text-gray-900">
                          {stats.successful}/{stats.total}
                        </div>
                        <div className="text-sm text-gray-600">{action}</div>
                        <div className="text-xs text-gray-500">
                          {stats.failed > 0 && `${stats.failed} errores`}
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Logs Table */}
                <div className="space-y-2">
                  {auditLogs.data.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      No hay logs de auditoría registrados
                    </div>
                  ) : (
                    auditLogs.data.map((log) => (
                      <Card key={log.id} className="border-l-4 border-l-blue-500">
                        <CardContent className="pt-4">
                          <div className="flex items-start justify-between">
                            <div className="space-y-2">
                              <div className="flex items-center gap-3">
                                <Badge variant={getActionBadgeColor(log.action)}>
                                  {log.action}
                                </Badge>
                                <Badge variant={log.success ? 'default' : 'destructive'}>
                                  {log.success ? (
                                    <>
                                      <CheckCircle className="h-3 w-3 mr-1" />
                                      Exitoso
                                    </>
                                  ) : (
                                    <>
                                      <XCircle className="h-3 w-3 mr-1" />
                                      Error
                                    </>
                                  )}
                                </Badge>
                              </div>
                              
                              <div className="text-sm space-y-1">
                                <div>
                                  <span className="font-medium">Ejecutado por:</span> {log.executedByName}
                                </div>
                                <div>
                                  <span className="font-medium">Fecha:</span> {formatDate(log.timestamp)}
                                </div>
                                {log.ipAddress && (
                                  <div>
                                    <span className="font-medium">IP:</span> {log.ipAddress}
                                  </div>
                                )}
                                {log.errorMessage && (
                                  <div className="text-red-600">
                                    <span className="font-medium">Error:</span> {log.errorMessage}
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => setExpandedLog(expandedLog === log.id ? null : log.id)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </div>

                          {expandedLog === log.id && (
                            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
                              <h4 className="font-medium mb-2">Detalles de la operación:</h4>
                              <pre className="text-xs bg-white p-3 rounded border overflow-auto max-h-96">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>
                              {log.userAgent && (
                                <div className="mt-2 text-xs text-gray-600">
                                  <span className="font-medium">User Agent:</span> {log.userAgent}
                                </div>
                              )}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    ))
                  )}
                </div>

                {/* Pagination info */}
                {auditLogs.pagination.total > 0 && (
                  <div className="text-center text-sm text-gray-500">
                    Mostrando {auditLogs.data.length} de {auditLogs.pagination.total} logs
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                Haga clic en "Ver Logs" para cargar el historial de auditoría
              </div>
            )}
          </CardContent>
        )}
      </Card>
    </div>
  );
}