import { db } from '../db/drizzle';
import { appointments } from '../db/schema';
import { eq } from 'drizzle-orm';

async function fixAppointmentDate() {
  console.log('🔧 Actualizando fecha de la cita...\n');
  
  try {
    const appointmentId = 'p8hUkwKJhCbio1ePEdSFe'; // ID de la cita del 23 julio
    
    // Actualizar la fecha a hoy (24 de julio)
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Resetear hora a medianoche
    
    console.log(`Actualizando cita ${appointmentId} a fecha: ${today}`);
    
    await db
      .update(appointments)
      .set({ 
        scheduledDate: today,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));
    
    console.log('✅ Fecha de cita actualizada correctamente');
    
    // Verificar el cambio
    const updatedAppointment = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);
    
    if (updatedAppointment.length > 0) {
      const appt = updatedAppointment[0];
      console.log(`\n📅 Cita actualizada:`);
      console.log(`ID: ${appt.id}`);
      console.log(`Título: ${appt.title}`);
      console.log(`Nueva fecha: ${appt.scheduledDate}`);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixAppointmentDate();