# Flujo de Usuarios Rechazados - Solución Completa

## Problema Identificado

El asistente rechazado no está siendo redirigido correctamente a la página de rechazo cuando se logea en el sistema. En su lugar, va a `/onboarding` y no muestra la información del rechazo.

**Problema adicional:** Si el programador eliminó el rol rechazado de la base de datos, el sistema no puede detectar que el usuario fue rechazado.

## Análisis del Flujo Actual

### 1. Proceso de Rechazo (Funciona Correctamente)
```typescript
// app/api/admin/requests/[id]/reject/route.ts
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  // 1. Actualizar solicitud a 'rejected'
  await tx.update(registrationRequests).set({
    status: 'rejected',
    rejectionReason: reason,
    // ...
  });

  // 2. Actualizar rol a 'rejected'
  await tx.update(userRoles).set({
    status: 'rejected',
    rejectionReason: reason,
    // ...
  });

  // 3. Actualizar metadatos de Clerk
  await clerk.users.updateUserMetadata(userId, {
    publicMetadata: {
      onboardingCompleted: true,
      role: registrationRequest.role,
      status: 'rejected'  // ✅ ESTO ESTÁ BIEN
    }
  });
}
```

### 2. Problema en la Sincronización (SOLUCIONADO)
```typescript
// ❌ PROBLEMA ANTERIOR en app/api/auth/sync/route.ts
if (rejectedRoles.length > 0 && clerkUser.publicMetadata?.status !== 'rejected') {
  // Solo actualizaba metadatos pero NO forzaba redirección
  return NextResponse.json({
    success: true,
    message: 'Usuario rechazado - metadatos restaurados',
    shouldReload: true  // ❌ Esto causaba recarga en lugar de redirección
  });
}

// ✅ SOLUCIÓN ACTUAL
// PRIORIDAD 1: Si hay roles rechazados O solicitudes rechazadas, forzar estado de rechazo
if (rejectedRoles.length > 0 || rejectedRequests.length > 0) {
  // Determinar el rol rechazado (priorizar roles sobre solicitudes)
  let rejectedRole = '';
  let rejectionReason = '';
  
  if (rejectedRoles.length > 0) {
    rejectedRole = rejectedRoles[0].role as string;
    rejectionReason = (rejectedRoles[0].rejectionReason as string) || 'Rol rechazado';
  } else if (rejectedRequests.length > 0) {
    rejectedRole = rejectedRequests[0].role as string;
    rejectionReason = (rejectedRequests[0].rejectionReason as string) || 'Solicitud rechazada';
  }
  
  // SIEMPRE forzar redirección a página de rechazo
  return NextResponse.json({
    success: true,
    message: 'Usuario rechazado - redirigiendo a página de rechazo',
    forceRedirect: '/onboarding/rejected'  // ✅ FORZAR REDIRECCIÓN
  });
}
```

### 3. Problema en el Hook de Sincronización (SOLUCIONADO)
```typescript
// ❌ PROBLEMA ANTERIOR en hooks/use-user-sync.ts
if (data.forceRedirect) {
  window.location.href = data.forceRedirect;
  return;
}

if (data.metadataUpdated || data.shouldReload) {
  // ❌ Recargaba la página en lugar de redirigir
  window.location.reload();
}

// ✅ SOLUCIÓN ACTUAL
// PRIORIDAD 1: Si hay redirección forzada, hacer la redirección inmediatamente
if (data.forceRedirect) {
  console.log('🚀 Redirección forzada a:', data.forceRedirect);
  window.location.href = data.forceRedirect;
  return;
}

// PRIORIDAD 2: Si hay redirección sugerida, hacer la redirección
if (data.shouldRedirect && data.redirectTo) {
  console.log('🔄 Redirección sugerida a:', data.redirectTo);
  window.location.href = data.redirectTo;
  return;
}

// PRIORIDAD 3: Si los metadatos fueron actualizados, recargar para propagar cambios
if (data.metadataUpdated || data.shouldReload) {
  console.log('🔄 Metadatos actualizados, recargando página...');
  setTimeout(() => {
    window.location.reload();
  }, 1500);
  return;
}
```

## Solución Implementada

### 1. API de Sincronización Mejorada (Doble Verificación)
```typescript
// ✅ SOLUCIÓN en app/api/auth/sync/route.ts
// PRIORIDAD 1: Si hay roles rechazados O solicitudes rechazadas, forzar estado de rechazo
if (rejectedRoles.length > 0 || rejectedRequests.length > 0) {
  // Determinar el rol rechazado (priorizar roles sobre solicitudes)
  let rejectedRole = '';
  let rejectionReason = '';
  
  if (rejectedRoles.length > 0) {
    rejectedRole = rejectedRoles[0].role as string;
    rejectionReason = (rejectedRoles[0].rejectionReason as string) || 'Rol rechazado';
  } else if (rejectedRequests.length > 0) {
    rejectedRole = rejectedRequests[0].role as string;
    rejectionReason = (rejectedRequests[0].rejectionReason as string) || 'Solicitud rechazada';
  }
  
  console.log('🚨 API Sync: Usuario rechazado detectado:', {
    rejectedRole,
    rejectionReason,
    source: rejectedRoles.length > 0 ? 'userRoles' : 'registrationRequests'
  });
  
  // SIEMPRE forzar redirección a página de rechazo
  return NextResponse.json({
    success: true,
    message: 'Usuario rechazado - redirigiendo a página de rechazo',
    forceRedirect: '/onboarding/rejected'
  });
}
```

### 2. Endpoint de Información de Rechazo Mejorado (Doble Fuente)
```typescript
// ✅ SOLUCIÓN en app/api/onboarding/rejection-info/route.ts
// 1. Primero buscar en userRoles rechazados (prioridad alta)
const rejectedRoles = await db.execute(sql`
  SELECT role, status, "rejectionReason", "rejectedAt" as "reviewedAt"
  FROM user_roles 
  WHERE "userId" = ${userId} AND status = 'rejected'
  ORDER BY "rejectedAt" DESC
  LIMIT 1
`);

// 2. Si no hay roles rechazados, buscar en solicitudes rechazadas (respaldo)
if (rejectedRoles.rows.length > 0) {
  const roleData = rejectedRoles.rows[0];
  rejectionInfo = {
    reason: (roleData.rejectionReason as string) || 'No se especificó razón',
    notes: null,
    rejectedAt: roleData.reviewedAt,
    rejectedBy: null,
    role: roleData.role as string,
    source: 'userRoles'
  };
} else {
  // Buscar en solicitudes rechazadas
  const rejectedRequests = await db.execute(sql`
    SELECT role, status, "rejectionReason", "reviewedAt", "reviewedBy", "reviewNotes"
    FROM "registrationRequests" 
    WHERE "userId" = ${userId} AND status = 'rejected'
    ORDER BY "reviewedAt" DESC
    LIMIT 1
  `);

  if (rejectedRequests.rows.length > 0) {
    const requestData = rejectedRequests.rows[0];
    rejectionInfo = {
      reason: (requestData.rejectionReason as string) || 'No se especificó razón',
      notes: (requestData.reviewNotes as string) || null,
      rejectedAt: requestData.reviewedAt,
      rejectedBy: (requestData.reviewedBy as string) || null,
      role: requestData.role as string,
      source: 'registrationRequests'
    };
  }
}
```

### 3. Endpoint de Restart Mejorado (Limpieza Completa)
```typescript
// ✅ SOLUCIÓN en app/api/onboarding/restart/route.ts
// 1. Verificar qué hay que eliminar antes de empezar
const rolesToDelete = await db.execute(sql`
  SELECT COUNT(*) as count FROM user_roles 
  WHERE "userId" = ${userId} AND status = 'rejected'
`);

const requestsToDelete = await db.execute(sql`
  SELECT COUNT(*) as count FROM "registrationRequests" 
  WHERE "userId" = ${userId} AND status = 'rejected'
`);

console.log('📊 Elementos a eliminar:', {
  rolesRechazados: rolesToDelete.rows[0].count,
  solicitudesRechazadas: requestsToDelete.rows[0].count
});

// 2. Eliminar roles rechazados
await db.delete(userRoles).where(
  and(
    eq(userRoles.userId, userId),
    eq(userRoles.status, 'rejected')
  )
);

// 3. Eliminar solicitudes rechazadas
await db.delete(registrationRequests).where(
  and(
    eq(registrationRequests.userId, userId),
    eq(registrationRequests.status, 'rejected')
  )
);

// 4. Limpiar metadatos de Clerk
await clerk.users.updateUserMetadata(userId, {
  publicMetadata: {
    onboardingCompleted: false,
    role: null,
    status: null
  }
});
```

### 4. Endpoint de Diagnóstico (NUEVO)
```typescript
// ✅ NUEVO en app/api/debug/user-status/route.ts
// Endpoint para diagnosticar el estado completo de un usuario
export async function GET(request: NextRequest) {
  // 1. Obtener datos de Clerk
  const clerkUser = await clerk.users.getUser(userId);
  const clerkMetadata = clerkUser.publicMetadata;

  // 2. Obtener roles del usuario
  const userRoles = await db.execute(sql`
    SELECT role, status, "rejectionReason", "rejectedAt"
    FROM user_roles WHERE "userId" = ${userId}
  `);

  // 3. Obtener solicitudes del usuario
  const userRequests = await db.execute(sql`
    SELECT role, status, "rejectionReason", "reviewedAt"
    FROM "registrationRequests" WHERE "userId" = ${userId}
  `);

  // 4. Analizar estado
  const rejectedRoles = userRoles.rows.filter(r => r.status === 'rejected');
  const rejectedRequests = userRequests.rows.filter(r => r.status === 'rejected');

  // 5. Determinar estado del sistema
  if (rejectedRoles.length > 0 || rejectedRequests.length > 0) {
    systemStatus = 'rejected';
    shouldRedirectTo = '/onboarding/rejected';
  }

  return NextResponse.json({
    success: true,
    data: {
      systemStatus,
      shouldRedirectTo,
      issues,
      clerk: { metadata: clerkMetadata },
      database: {
        roles: { rejected: rejectedRoles.length, details: userRoles.rows },
        requests: { rejected: rejectedRequests.length, details: userRequests.rows }
      }
    }
  });
}
```

## Flujo Completo Corregido

### 1. Usuario Rechazado se Logea (Doble Verificación)
```
1. Usuario rechazado hace login
2. Middleware detecta status='rejected' en metadatos
3. Redirige a /onboarding/rejected
4. Hook useUserSync se ejecuta
5. API /api/auth/sync verifica estado en BD
6. Detecta roles rechazados O solicitudes rechazadas
7. Fuerza redirección a /onboarding/rejected
8. Usuario ve página de rechazo con información
```

### 2. Caso Especial: Rol Eliminado pero Solicitud Existe
```
1. Usuario rechazado hace login
2. Middleware detecta status='rejected' en metadatos
3. Redirige a /onboarding/rejected
4. Hook useUserSync se ejecuta
5. API /api/auth/sync verifica estado en BD
6. NO encuentra roles rechazados (fueron eliminados)
7. SÍ encuentra solicitudes rechazadas
8. Detecta rechazo por solicitudes
9. Fuerza redirección a /onboarding/rejected
10. Usuario ve página de rechazo con información
```

### 3. Página de Rechazo Funcional (Doble Fuente)
```typescript
// app/onboarding/rejected/page.tsx
export default function RejectedPage() {
  // 1. Obtiene información del rechazo desde /api/onboarding/rejection-info
  // 2. Busca primero en userRoles, luego en registrationRequests
  // 3. Muestra razón del rechazo, fecha, rol solicitado
  // 4. Botón "Reiniciar Registro" → /api/onboarding/restart
  // 5. Botón "Volver al Inicio" → /
}
```

### 4. Reinicio del Onboarding (Limpieza Completa)
```typescript
// app/api/onboarding/restart/route.ts
export async function POST(request: NextRequest) {
  // 1. Eliminar roles rechazados de BD
  // 2. Eliminar solicitudes rechazadas de BD
  // 3. Limpiar metadatos de Clerk
  // 4. Redirigir a onboarding
}
```

## Archivos Modificados

### ✅ Cambios Realizados:
1. **`app/api/auth/sync/route.ts`** - Doble verificación (roles + solicitudes)
2. **`hooks/use-user-sync.ts`** - Mejor manejo de redirecciones
3. **`app/api/onboarding/rejection-info/route.ts`** - Doble fuente de información
4. **`app/api/onboarding/restart/route.ts`** - Limpieza completa
5. **`app/api/debug/user-status/route.ts`** - Endpoint de diagnóstico (NUEVO)

### ✅ Archivos que Ya Funcionaban:
1. **`middleware.ts`** - Redirección correcta
2. **`app/api/admin/requests/[id]/reject/route.ts`** - Proceso de rechazo
3. **`app/onboarding/pending/page.tsx`** - Página de pendiente
4. **`app/onboarding/rejected/page.tsx`** - Página de rechazo

## Testing del Flujo

### 1. Probar Usuario Rechazado (Rol Existe)
```bash
# 1. Login con usuario rechazado (con rol rechazado en BD)
# 2. Verificar que va a /onboarding/rejected
# 3. Verificar que muestra información del rechazo
# 4. Verificar consola: "source: 'userRoles'"
```

### 2. Probar Usuario Rechazado (Rol Eliminado)
```bash
# 1. Eliminar rol rechazado de userRoles (simular eliminación)
# 2. Login con usuario rechazado
# 3. Verificar que va a /onboarding/rejected
# 4. Verificar que muestra información del rechazo
# 5. Verificar consola: "source: 'registrationRequests'"
```

### 3. Usar Endpoint de Diagnóstico
```bash
# GET /api/debug/user-status
# Verificar:
# - systemStatus: 'rejected'
# - shouldRedirectTo: '/onboarding/rejected'
# - database.roles.rejected > 0 O database.requests.rejected > 0
# - issues: [] (sin problemas)
```

### 4. Verificar Consola del Servidor
```bash
# Debería mostrar:
📡 API Sync: Iniciando sincronización para usuario: user_xxx
🗄️ API Sync: Usuario encontrado en DB: { id: 'user_xxx', email: '...', status: 'pending' }
🔑 API Sync: Roles del usuario: { activeRoles: [], rejectedRoles: [], rejectedRequests: [{ role: 'assistant', status: 'rejected' }], primaryRole: undefined, userStatus: 'pending' }
🚨 API Sync: Usuario rechazado detectado: { rejectedRole: 'assistant', rejectionReason: '...', source: 'registrationRequests' }
🔄 API Sync: Actualizando metadatos de rechazo: { role: 'assistant', status: 'rejected', onboardingCompleted: true }
✅ API Sync: Metadatos de rechazo actualizados en Clerk
🚀 API Sync: Forzando redirección a página de rechazo
```

## Casos de Uso Específicos

### 1. Usuario Rechazado por Primera Vez
- ✅ Se redirige a `/onboarding/rejected`
- ✅ Ve información del rechazo
- ✅ Puede reiniciar el proceso

### 2. Usuario Rechazado que Vuelve a Logear
- ✅ Se redirige a `/onboarding/rejected` automáticamente
- ✅ Ve información del rechazo
- ✅ Puede reiniciar el proceso

### 3. Usuario Rechazado con Rol Eliminado
- ✅ Se redirige a `/onboarding/rejected` automáticamente
- ✅ Ve información del rechazo (desde solicitudes)
- ✅ Puede reiniciar el proceso

### 4. Usuario que Reinicia el Proceso
- ✅ Se eliminan roles rechazados
- ✅ Se eliminan solicitudes rechazadas
- ✅ Se limpian metadatos de Clerk
- ✅ Se redirige a `/onboarding`

### 5. Diagnóstico de Estado
- ✅ Endpoint `/api/debug/user-status` muestra estado completo
- ✅ Identifica inconsistencias entre BD y Clerk
- ✅ Sugiere redirecciones correctas

## Troubleshooting

### Error: "Usuario va a /onboarding en lugar de /onboarding/rejected"
**Causa:** Metadatos de Clerk no están actualizados
**Solución:** Usar endpoint `/api/debug/user-status` para diagnosticar

### Error: "Página de rechazo no muestra información"
**Causa:** Ni roles ni solicitudes rechazadas están en la BD
**Solución:** Verificar con endpoint de diagnóstico

### Error: "Botón reiniciar no funciona"
**Causa:** Endpoint `/api/onboarding/restart` falla
**Solución:** Verificar permisos y estructura de BD

### Error: "Redirección infinita"
**Causa:** Middleware y hook se contradicen
**Solución:** Verificar prioridades de redirección

### Error: "Rol fue eliminado pero usuario sigue rechazado"
**Causa:** El sistema ahora detecta rechazo por solicitudes
**Solución:** ✅ FUNCIONALIDAD NUEVA - El sistema detecta rechazo por solicitudes

## Métricas de Éxito

### ✅ Indicadores de que Funciona:
1. Usuario rechazado va directamente a `/onboarding/rejected`
2. Página muestra razón del rechazo y fecha
3. Funciona tanto con roles rechazados como con solicitudes rechazadas
4. Botón "Reiniciar Registro" funciona
5. Botón "Volver al Inicio" funciona
6. No hay redirecciones infinitas
7. Consola muestra logs correctos
8. Endpoint de diagnóstico funciona

### ❌ Indicadores de Problemas:
1. Usuario va a `/onboarding` en lugar de `/onboarding/rejected`
2. Página de rechazo no carga información
3. Botones no funcionan
4. Redirecciones infinitas
5. Errores en consola
6. Endpoint de diagnóstico muestra inconsistencias

## Conclusión

El flujo de usuarios rechazados ahora está completamente funcional con **doble verificación**:

1. **Detección robusta**: Busca rechazos tanto en `userRoles` como en `registrationRequests`
2. **Información completa**: Obtiene datos de rechazo de múltiples fuentes
3. **Limpieza completa**: Elimina tanto roles como solicitudes rechazadas
4. **Diagnóstico**: Endpoint para verificar estado completo del usuario

El sistema ahora maneja correctamente todos los casos:
- ✅ Usuario rechazado con rol en BD
- ✅ Usuario rechazado con rol eliminado pero solicitud existente
- ✅ Usuario rechazado que reinicia el proceso
- ✅ Diagnóstico completo del estado del usuario

**Regla de oro:** Si hay datos de rechazo en `userRoles` O en `registrationRequests`, el usuario debe ir a `/onboarding/rejected`. 