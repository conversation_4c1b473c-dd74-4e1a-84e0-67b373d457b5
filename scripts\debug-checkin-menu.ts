import { db } from "@/db";
import { appointments } from "@/db/schema";
import { eq, and, gte, lte } from "drizzle-orm";
import { format } from "date-fns";

async function debugCheckInMenu() {
  console.log("🔍 Depurando por qué no aparece la opción de check-in...\n");
  
  try {
    // Obtener citas de hoy
    const today = new Date();
    const startOfDay = new Date(today.setHours(0, 0, 0, 0));
    const endOfDay = new Date(today.setHours(23, 59, 59, 999));
    
    const todayAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          gte(appointments.scheduledDate, startOfDay),
          lte(appointments.scheduledDate, endOfDay)
        )
      );
    
    console.log(`📊 Citas de hoy: ${todayAppointments.length}\n`);
    
    // Analizar cada cita
    for (const appointment of todayAppointments) {
      console.log(`📅 Cita: ${appointment.title}`);
      console.log(`   ID: ${appointment.id}`);
      console.log(`   Estado: ${appointment.status}`);
      console.log(`   Hora: ${format(new Date(appointment.startTime), 'HH:mm')}`);
      console.log(`   Check-in: ${appointment.checkedInAt ? '✅ Sí' : '❌ No'}`);
      
      // Verificar condiciones para mostrar check-in
      console.log("\n   Condiciones para mostrar check-in:");
      console.log(`   1. Estado = 'confirmed': ${appointment.status === 'confirmed' ? '✅' : '❌'} (actual: ${appointment.status})`);
      console.log(`   2. Sin check-in previo: ${!appointment.checkedInAt ? '✅' : '❌'}`);
      console.log(`   3. UserRole = 'assistant': Se pasa desde el componente`);
      
      if (appointment.status === 'confirmed' && !appointment.checkedInAt) {
        console.log(`   ✅ DEBERÍA mostrar opción de check-in`);
      } else {
        console.log(`   ❌ NO debe mostrar opción de check-in`);
        if (appointment.status !== 'confirmed') {
          console.log(`      Razón: Estado debe ser 'confirmed', pero es '${appointment.status}'`);
        }
        if (appointment.checkedInAt) {
          console.log(`      Razón: Ya tiene check-in registrado`);
        }
      }
      console.log("\n" + "-".repeat(50) + "\n");
    }
    
    // Buscar específicamente citas confirmadas
    const confirmedAppointments = todayAppointments.filter(a => a.status === 'confirmed');
    console.log(`\n📌 Resumen:`);
    console.log(`   - Citas confirmadas: ${confirmedAppointments.length}`);
    console.log(`   - Citas sin check-in: ${todayAppointments.filter(a => !a.checkedInAt).length}`);
    console.log(`   - Citas que deberían mostrar check-in: ${confirmedAppointments.filter(a => !a.checkedInAt).length}`);
    
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

// Ejecutar
debugCheckInMenu();