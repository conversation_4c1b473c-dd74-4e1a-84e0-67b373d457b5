/**
 * <PERSON>ript definitivo para estandarizar TODOS los menús contextuales
 * Patrón único: Ver → Editar → [SEPARADOR] → Toggle → Eliminar
 */

import fs from 'fs';

const catalogFiles = [
  'app/(dashboard)/dashboard/admin/catalogs/countries/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/currencies/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/departments/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/doctor-service-prices/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/media-sources/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/medical-specialties/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/medications/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/municipalities/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/non-pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/occupations/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/relationships/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/symptoms/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/religions/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/companies/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/consultories/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/medical-services/page.tsx',
  'app/(dashboard)/dashboard/admin/users/page.tsx'
];

function standardizeMenu(filePath: string): boolean {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let updated = false;
    
    console.log(`\n📄 Procesando: ${filePath.split('/').pop()}`);
    
    // 1. Primero limpiar todos los separadores existentes DENTRO de menús
    const cleanSeparators = content.replace(
      /<DropdownMenuContent[^>]*>([\s\S]*?)<\/DropdownMenuContent>/g,
      (match, menuContent) => {
        // Eliminar TODOS los DropdownMenuSeparator existentes dentro del menú
        const cleanedContent = menuContent.replace(/\s*<DropdownMenuSeparator\s*\/>\s*/g, '');
        return match.replace(menuContent, cleanedContent);
      }
    );
    
    if (cleanSeparators !== content) {
      content = cleanSeparators;
      updated = true;
      console.log('  ✓ Limpiados separadores existentes');
    }
    
    // 2. Ahora agregar UN SOLO separador en la posición correcta
    // Buscar el patrón: Editar</DropdownMenuItem> seguido por cualquier DropdownMenuItem con toggle/Desactivar/Activar
    const addSeparatorPattern = /(Editar\s*<\/DropdownMenuItem>\s*)(<DropdownMenuItem[^>]*onClick=\{[^}]*(?:toggle|Toggle|Desactivar|Activar)[^}]*\})/g;
    
    content = content.replace(addSeparatorPattern, (match, beforeSeparator, afterSeparator) => {
      updated = true;
      console.log('  ✓ Agregado separador único');
      
      // Determinar la indentación correcta basándose en el contenido
      const indentation = beforeSeparator.includes('                              ') 
        ? '                              ' // Desktop (más indentación)
        : '                          ';    // Mobile (menos indentación)
      
      return beforeSeparator + 
             `\n${indentation}<DropdownMenuSeparator />\n${indentation}` + 
             afterSeparator;
    });
    
    // 3. Asegurar que todas las clases de eliminar sean consistentes
    content = content.replace(
      /className="text-red-600"(?!\s+focus:text-red-600)/g,
      'className="text-red-600 focus:text-red-600"'
    );
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf-8');
      console.log('  ✅ Archivo actualizado');
      return true;
    } else {
      console.log('  ⏭️  Sin cambios necesarios');
      return false;
    }
    
  } catch (error) {
    console.error(`  ❌ Error: ${error}`);
    return false;
  }
}

async function main() {
  console.log('🎯 ESTANDARIZACIÓN FINAL DE MENÚS CONTEXTUALES');
  console.log('Patrón objetivo: Ver → Editar → [SEPARADOR ÚNICO] → Toggle → Eliminar\n');
  
  let totalUpdated = 0;
  let totalProcessed = 0;
  
  for (const file of catalogFiles) {
    if (fs.existsSync(file)) {
      totalProcessed++;
      if (standardizeMenu(file)) {
        totalUpdated++;
      }
    } else {
      console.log(`⚠️  Archivo no encontrado: ${file}`);
    }
  }
  
  console.log('\n📊 RESUMEN FINAL:');
  console.log(`✅ Archivos actualizados: ${totalUpdated}`);
  console.log(`📁 Archivos procesados: ${totalProcessed}`);
  console.log(`🎯 Patrón aplicado: EXACTAMENTE UN separador entre Editar y Toggle`);
  
  if (totalUpdated > 0) {
    console.log('\n🚀 TODOS los menús ahora tienen el MISMO patrón estandarizado!');
  }
}

main().catch(console.error);