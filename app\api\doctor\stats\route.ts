import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  appointments,
  doctorServicePrices,
  medicalServices,
  medicalRecords,
  user
} from '@/db/schema';
import { eq, and, sql, gte, lte, count, desc } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener fechas del mes actual y anterior
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);
    const previousMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const previousMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0, 23, 59, 59);

    // Consultas del mes actual
    const currentMonthConsultations = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, currentMonthStart),
        lte(medicalConsultations.consultationDate, currentMonthEnd)
      ));

    // Consultas del mes anterior
    const previousMonthConsultations = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, previousMonthStart),
        lte(medicalConsultations.consultationDate, previousMonthEnd)
      ));

    const currentCount = currentMonthConsultations[0]?.count || 0;
    const previousCount = previousMonthConsultations[0]?.count || 0;

    // Calcular porcentaje de cambio en consultas
    let consultationsChangePercent = 0;
    if (previousCount > 0) {
      consultationsChangePercent = ((currentCount - previousCount) / previousCount) * 100;
    } else if (currentCount > 0) {
      consultationsChangePercent = 100;
    }

    // Calcular ingresos del mes actual desde las consultas
    const currentMonthConsultationsData = await db
      .select({
        services: medicalConsultations.services
      })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, currentMonthStart),
        lte(medicalConsultations.consultationDate, currentMonthEnd)
      ));

    // Calcular ingresos del mes anterior
    const previousMonthConsultationsData = await db
      .select({
        services: medicalConsultations.services
      })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, previousMonthStart),
        lte(medicalConsultations.consultationDate, previousMonthEnd)
      ));

    // Sumar ingresos del mes actual
    const currentMonthRevenue = currentMonthConsultationsData.reduce((total, consultation) => {
      const services = consultation.services as any[] || [];
      const consultationTotal = services.reduce((sum, service) => {
        return sum + (parseFloat(service.price?.toString() || '0') || 0);
      }, 0);
      return total + consultationTotal;
    }, 0);

    // Sumar ingresos del mes anterior
    const previousMonthRevenue = previousMonthConsultationsData.reduce((total, consultation) => {
      const services = consultation.services as any[] || [];
      const consultationTotal = services.reduce((sum, service) => {
        return sum + (parseFloat(service.price?.toString() || '0') || 0);
      }, 0);
      return total + consultationTotal;
    }, 0);

    // Calcular porcentaje de cambio en ingresos
    let revenueChangePercent = 0;
    if (previousMonthRevenue > 0) {
      revenueChangePercent = ((currentMonthRevenue - previousMonthRevenue) / previousMonthRevenue) * 100;
    } else if (currentMonthRevenue > 0) {
      revenueChangePercent = 100;
    }

    // Obtener fechas para la semana actual (Lunes a Domingo)
    const weekStart = new Date(now);
    const dayOfWeek = now.getDay(); // 0 = Domingo, 1 = Lunes, etc.
    const daysFromMonday = dayOfWeek === 0 ? 6 : dayOfWeek - 1; // Si es domingo, retroceder 6 días
    weekStart.setDate(now.getDate() - daysFromMonday); // Ir al lunes de esta semana
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6); // Domingo de esta semana
    weekEnd.setHours(23, 59, 59, 999);

    // Resumen semanal - consultas de esta semana
    const weeklyConsultations = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, weekStart),
        lte(medicalConsultations.consultationDate, weekEnd)
      ));

    // Pacientes nuevos esta semana
    const newPatientsThisWeek = await db
      .select({ count: count() })
      .from(medicalRecords)
      .where(and(
        eq(medicalRecords.primaryDoctorId, userId),
        eq(medicalRecords.status, 'active'),
        gte(medicalRecords.openDate, weekStart),
        lte(medicalRecords.openDate, weekEnd)
      ));

    // Servicios por categoría esta semana
    const weeklyConsultationsWithServices = await db
      .select({
        services: medicalConsultations.services
      })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, weekStart),
        lte(medicalConsultations.consultationDate, weekEnd)
      ));

    // Contar servicios por categoría
    const servicesByCategory: Record<string, number> = {};
    weeklyConsultationsWithServices.forEach(consultation => {
      const services = consultation.services as any[] || [];
      services.forEach(service => {
        const category = service.category || 'Otros';
        servicesByCategory[category] = (servicesByCategory[category] || 0) + 1;
      });
    });

    // Convertir a array ordenado por cantidad (descendente)
    const sortedServicesByCategory = Object.entries(servicesByCategory)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3); // Top 3 categorías

    // Pacientes recientes (últimos 5 con consultas completadas)
    const recentPatients = await db
      .select({
        patientId: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        dateOfBirth: user.dateOfBirth,
        lastConsultation: medicalConsultations.consultationDate,
      })
      .from(medicalConsultations)
      .leftJoin(medicalRecords, eq(medicalConsultations.medicalRecordId, medicalRecords.id))
      .leftJoin(user, eq(medicalRecords.patientId, user.id))
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed')
      ))
      .orderBy(desc(medicalConsultations.consultationDate))
      .limit(5);

    // Procesar pacientes recientes para calcular edad y tiempo desde última visita
    const processedRecentPatients = recentPatients.map(patient => {
      let age = null;
      if (patient.dateOfBirth) {
        const today = new Date();
        const birthDate = new Date(patient.dateOfBirth);
        age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
      }

      // Calcular tiempo desde última visita
      let lastVisitText = 'Hoy';
      if (patient.lastConsultation) {
        const daysDiff = Math.floor((now.getTime() - new Date(patient.lastConsultation).getTime()) / (1000 * 60 * 60 * 24));
        if (daysDiff === 0) lastVisitText = 'Hoy';
        else if (daysDiff === 1) lastVisitText = '1 día';
        else if (daysDiff < 7) lastVisitText = `${daysDiff} días`;
        else if (daysDiff < 14) lastVisitText = '1 semana';
        else if (daysDiff < 30) lastVisitText = `${Math.floor(daysDiff / 7)} semanas`;
        else if (daysDiff < 60) lastVisitText = '1 mes';
        else lastVisitText = `${Math.floor(daysDiff / 30)} meses`;
      }

      return {
        name: `${patient.firstName || ''} ${patient.lastName || ''}`.trim(),
        age: age ? `${age} años` : 'N/A',
        lastVisit: lastVisitText,
        avatar: `${patient.firstName?.charAt(0) || 'P'}${patient.lastName?.charAt(0) || 'A'}`
      };
    });

    return NextResponse.json({
      data: {
        consultations: {
          current: currentCount,
          previous: previousCount,
          changePercent: Math.round(consultationsChangePercent * 100) / 100,
          trend: consultationsChangePercent >= 0 ? 'up' : 'down'
        },
        revenue: {
          current: currentMonthRevenue,
          previous: previousMonthRevenue,
          changePercent: Math.round(revenueChangePercent * 100) / 100,
          trend: revenueChangePercent >= 0 ? 'up' : 'down',
          currency: 'GTQ'
        },
        weeklyStats: {
          consultationsCompleted: weeklyConsultations[0]?.count || 0,
          newPatients: newPatientsThisWeek[0]?.count || 0,
          servicesByCategory: sortedServicesByCategory
        },
        recentPatients: processedRecentPatients
      }
    });

  } catch (error) {
    console.error('Error fetching doctor stats:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}