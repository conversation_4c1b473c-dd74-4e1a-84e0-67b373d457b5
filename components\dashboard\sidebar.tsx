'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  LayoutDashboard, 
  Users, 
  Calendar, 
  FileText, 
  Settings, 
  Heart,
  Stethoscope,
  Baby,
  ClipboardList,
  BarChart3,
  MessageSquare,
  Shield,
  Package,
  Clock,
  ChevronLeft,
  ChevronRight,
  MenuIcon,
  DollarSign,
  Settings as SettingsIcon,
  User,
  Database,
  BookOpen,
  Wrench
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSidebar } from './sidebar-context';
import { Button } from '@/components/ui/button';
import { useConsultoryInfo } from '@/hooks/use-consultory-info';
import Image from 'next/image';

interface SidebarProps {
  role: string;
}

const roleConfigs = {
  admin: {
    title: 'Administrador',
    icon: User,
    navigation: [
      { name: 'Dashboard', href: '/dashboard/admin', icon: LayoutDashboard },
      { name: 'Solicitudes', href: '/dashboard/admin/requests', icon: Clock },
      { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/admin/users', icon: Users },
      { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/dashboard/admin/doctors', icon: Stethoscope },
      { name: 'Pacientes', href: '/dashboard/admin/patients', icon: Baby },
      { name: 'Agenda Médica', href: '/dashboard/admin/agenda', icon: Calendar },
      { name: 'Reportes', href: '/dashboard/admin/reports', icon: BarChart3 },
      { name: 'Configuración', href: '/dashboard/admin/settings', icon: SettingsIcon },
      { name: 'Catálogos', href: '/dashboard/admin/catalogs', icon: Database },
      { name: 'Utilidades', href: '/dashboard/admin/utilities', icon: Wrench },
      { name: 'Facturación', href: '/dashboard/admin/billing', icon: ClipboardList },
    ]
  },
  doctor: {
    title: 'Dr. Panel',
    icon: Stethoscope,
    navigation: [
      { name: 'Dashboard Principal', href: '/dashboard/doctor', icon: LayoutDashboard },
      { name: 'Perfil', href: '/dashboard/doctor/profile', icon: User },
      { name: 'Configuración', href: '/dashboard/doctor/settings', icon: SettingsIcon },
      { name: 'Agenda Médica', href: '/dashboard/doctor/agenda', icon: Calendar },
      { name: 'Pacientes', href: '/dashboard/doctor/pacientes', icon: Baby },
      { name: 'Expedientes Clínicos', href: '/dashboard/doctor/expedientes', icon: FileText },
      { name: 'Ventas', href: '/dashboard/doctor/sales', icon: DollarSign },
      { name: 'Gastos', href: '/dashboard/doctor/expenses', icon: ClipboardList },
      { name: 'Gestor Contenidos', href: '/dashboard/doctor/content', icon: FileText },
      { name: 'Consultas', href: '/dashboard/doctor/consultations', icon: Stethoscope },
      { name: 'Catálogos', href: '/dashboard/doctor/catalogs', icon: BookOpen },
      { name: 'IA Agent', href: '/dashboard/doctor/ai-agent', icon: MessageSquare },
    ]
  },
  assistant: {
    title: 'Asistente',
    icon: Users,
    navigation: [
      { name: 'Dashboard', href: '/dashboard/assistant', icon: LayoutDashboard },
      { name: 'Agenda Médica', href: '/dashboard/assistant/agenda', icon: Calendar },
      { name: 'Pacientes', href: '/dashboard/assistant/pacientes', icon: Baby },
      { name: 'Sala de Espera', href: '/dashboard/assistant/waiting-room', icon: Clock },
      { name: 'Facturación', href: '/dashboard/assistant/billing', icon: FileText },
      { name: 'Inventario', href: '/dashboard/assistant/inventory', icon: Package },
      { name: 'Catálogos', href: '/dashboard/assistant/catalogs', icon: BookOpen },
      { name: 'Reportes', href: '/dashboard/assistant/reports', icon: BarChart3 },
    ]
  },
  patient: {
    title: 'Mi Portal',
    icon: Baby,
    navigation: [
      { name: 'Dashboard', href: '/dashboard/patient', icon: LayoutDashboard },
      { name: 'Mis Citas', href: '/dashboard/patient/appointments', icon: Calendar },
      { name: 'Mi Expediente', href: '/dashboard/patient/medical-record', icon: FileText },
      { name: 'Mis Recetas', href: '/dashboard/patient/prescriptions', icon: ClipboardList },
      { name: 'Vacunas', href: '/dashboard/patient/vaccines', icon: Shield },
      { name: 'Documentos', href: '/dashboard/patient/documents', icon: FileText },
      { name: 'Mensajes', href: '/dashboard/patient/messages', icon: MessageSquare },
    ]
  },
  guardian: {
    title: 'Portal Familiar',
    icon: Shield,
    navigation: [
      { name: 'Dashboard', href: '/dashboard/guardian', icon: LayoutDashboard },
      { name: 'Mis Dependientes', href: '/dashboard/guardian/dependents', icon: Baby },
      { name: 'Citas', href: '/dashboard/guardian/appointments', icon: Calendar },
      { name: 'Expedientes', href: '/dashboard/guardian/medical-records', icon: FileText },
      { name: 'Vacunas', href: '/dashboard/guardian/vaccines', icon: Shield },
      { name: 'Facturas', href: '/dashboard/guardian/billing', icon: ClipboardList },
      { name: 'Mensajes', href: '/dashboard/guardian/messages', icon: MessageSquare },
    ]
  },
  provider: {
    title: 'Proveedor',
    icon: Package,
    navigation: [
      { name: 'Dashboard', href: '/dashboard/provider', icon: LayoutDashboard },
      { name: 'Catálogo', href: '/dashboard/provider/catalog', icon: Package },
      { name: 'Órdenes', href: '/dashboard/provider/orders', icon: ClipboardList },
      { name: 'Inventario', href: '/dashboard/provider/inventory', icon: Package },
      { name: 'Facturación', href: '/dashboard/provider/billing', icon: FileText },
      { name: 'Reportes', href: '/dashboard/provider/reports', icon: BarChart3 },
    ]
  }
};

export function Sidebar({ role }: SidebarProps) {
  const pathname = usePathname();
  const { isCollapsed, toggleSidebar, isMobileOpen, toggleMobileSidebar } = useSidebar();
  const { consultory, isLoading } = useConsultoryInfo();
  const [mounted, setMounted] = useState(false);
  const config = roleConfigs[role as keyof typeof roleConfigs] || roleConfigs.doctor;
  const TitleIcon = config.icon;

  useEffect(() => {
    setMounted(true);
  }, []);

  return (
    <>
      {/* Mobile Overlay */}
      {isMobileOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black/50 md:hidden"
          onClick={toggleMobileSidebar}
        />
      )}
      
      {/* Sidebar */}
      <div className={cn(
        "fixed md:relative h-full bg-white border-r border-gray-200 shadow-sm transition-all duration-300 ease-in-out flex flex-col z-50",
        "left-0 top-0",
        isCollapsed ? "w-20" : "w-72",
        isMobileOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
      )}>
      {/* Logo and Title */}
      <div className="flex h-28 items-center justify-between border-b border-gray-200 px-4">
        <div className={cn(
          "flex items-center transition-all duration-300",
          isCollapsed ? "justify-center" : "space-x-3"
        )}>
          {mounted && consultory?.logoUrl && !isLoading ? (
            <div className="w-16 h-16 rounded-full overflow-hidden shadow-lg bg-white p-1">
              <Image
                src={consultory.logoUrl}
                alt={`${consultory.name} logo`}
                width={64}
                height={64}
                className="w-full h-full object-contain"
              />
            </div>
          ) : (
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-br from-[#ea6cb0] to-[#ea6cb0]/80 shadow-lg">
              <Heart className="h-10 w-10 text-white" />
            </div>
          )}
          {!isCollapsed && (
            <div>
              <h1 className="text-xl font-bold text-[#ea6cb0]">
                {!mounted || isLoading ? 'Sistema Médico' : consultory?.name || 'Sistema Médico'}
              </h1>
              <p className="text-xs text-gray-500 uppercase tracking-wide">Sistema Médico</p>
            </div>
          )}
        </div>
        
        {/* Collapse button - Desktop only */}
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleSidebar}
          className={cn(
            "hidden md:flex text-gray-400 hover:text-gray-600 hover:bg-gray-100 h-8 w-8 p-0 transition-all duration-200",
            isCollapsed && "hidden"
          )}
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        {/* Close button - Mobile only */}
        <Button
          variant="ghost"
          size="sm"
          onClick={toggleMobileSidebar}
          className="md:hidden text-gray-400 hover:text-gray-600 hover:bg-gray-100 h-8 w-8 p-0 transition-all duration-200"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
      </div>

      {/* Role Title */}
      {!isCollapsed && (
        <div className="flex items-center space-x-3 px-6 py-5 border-b border-gray-100 bg-gray-50">
          <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-white shadow-sm border">
            <TitleIcon className="h-5 w-5 text-emerald-600" />
          </div>
          <div>
            <p className="text-base font-semibold text-gray-900">{config.title}</p>
            <p className="text-sm text-gray-500">Panel de Control</p>
          </div>
        </div>
      )}

      {/* Collapsed state - Expand button at top - Desktop only */}
      {isCollapsed && (
        <div className="hidden md:block px-4 py-4 border-b border-gray-100">
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="w-full h-12 justify-center text-gray-400 hover:text-gray-600 hover:bg-emerald-50 hover:text-emerald-600 transition-all duration-200 rounded-xl group"
            title="Expandir menú"
          >
            <MenuIcon className="h-6 w-6 group-hover:scale-110 transition-transform" />
          </Button>
        </div>
      )}

      {/* Navigation */}
      <nav className={cn(
        "flex-1 px-4 py-6 space-y-2",
        isCollapsed ? "overflow-hidden" : "overflow-y-auto"
      )}>
        {config.navigation.map((item) => {
          const Icon = item.icon;
          const isActive = pathname === item.href;
          
          return (
            <Link
              key={item.name}
              href={item.href}
              className={cn(
                "group flex items-center px-3 py-3 text-sm font-medium rounded-xl transition-all duration-200 relative",
                isActive
                  ? "bg-emerald-50 text-emerald-700 shadow-sm"
                  : "text-gray-600 hover:bg-gray-50 hover:text-gray-900",
                isCollapsed ? "justify-center" : ""
              )}
              title={isCollapsed ? item.name : undefined}
            >
              {/* Active indicator */}
              {isActive && !isCollapsed && (
                <div className="absolute left-0 top-1/2 -translate-y-1/2 w-1 h-6 bg-emerald-500 rounded-r-full" />
              )}
              
              <Icon
                className={cn(
                  "h-5 w-5 flex-shrink-0 transition-all duration-200",
                  isActive
                    ? "text-emerald-600"
                    : "text-gray-400 group-hover:text-gray-600",
                  isCollapsed ? "mr-0" : "mr-3"
                )}
              />
              {!isCollapsed && (
                <span className="transition-all duration-200">
                  {item.name}
                </span>
              )}
              
              {/* Tooltip for collapsed state */}
              {isCollapsed && (
                <div className="absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50 shadow-lg">
                  {item.name}
                  <div className="absolute top-1/2 -left-1 -translate-y-1/2 border-4 border-transparent border-r-gray-900" />
                </div>
              )}
            </Link>
          );
        })}
      </nav>

      {/* Settings and Expand Button */}
      <div className="border-t border-gray-200 p-4 space-y-2">
        {/* Settings Link */}
        <Link
          href="/dashboard/settings"
          className={cn(
            "group flex items-center px-3 py-3 text-sm font-medium text-gray-600 rounded-xl hover:bg-gray-50 hover:text-gray-900 transition-all duration-200 relative",
            isCollapsed ? "justify-center" : ""
          )}
          title={isCollapsed ? "Configuración" : undefined}
        >
          <Settings className={cn(
            "h-5 w-5 flex-shrink-0 text-gray-400 group-hover:text-gray-600 transition-all duration-200",
            isCollapsed ? "mr-0" : "mr-3"
          )} />
          {!isCollapsed && (
            <span className="transition-all duration-200">
              Configuración
            </span>
          )}
          
          {/* Tooltip for collapsed state */}
          {isCollapsed && (
            <div className="absolute left-full ml-3 px-3 py-2 bg-gray-900 text-white text-sm rounded-lg opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 whitespace-nowrap z-50 shadow-lg">
              Configuración
              <div className="absolute top-1/2 -left-1 -translate-y-1/2 border-4 border-transparent border-r-gray-900" />
            </div>
          )}
        </Link>
      </div>
    </div>
    </>
  );
}