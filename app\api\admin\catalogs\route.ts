import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Metadatos de catálogos por fases
const catalogsMetadata = {
  phase1: {
    title: "Fase 1: Catálogos Críticos",
    description: "Funcionalidad inmediata para onboarding + citas + expediente básico",
    total: 15,
    catalogs: [
      // ✅ Implementados
      { 
        id: 'countries', 
        name: 'Países', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/countries',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'departments', 
        name: 'Departamentos', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/departments',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'municipalities', 
        name: 'Municipios', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/municipalities',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'occupations', 
        name: 'Ocupaciones', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/occupations',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'relationships', 
        name: 'Parentescos', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/relationships',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'specialties', 
        name: 'Especialidades Médicas', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/medical-specialties',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      
      // ❌ Pendientes de implementar
      { 
        id: 'currencies', 
        name: 'Monedas', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/currencies',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'consultories', 
        name: 'Consultorios', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/consultories',
        adminOnly: true,
        doctorAccess: 'none',
        assistantAccess: 'read'
      },
      { 
        id: 'activities', 
        name: 'Tipos de Actividad', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/activity-types',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'pathological', 
        name: 'Antecedentes Patológicos', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/pathological-history',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'none'
      },
      { 
        id: 'nonPathological', 
        name: 'Antecedentes No Patológicos', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/non-pathological-history',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'none'
      },
      { 
        id: 'media', 
        name: 'Medios/Origen de Pacientes', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/media-sources',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'education', 
        name: 'Niveles de Escolaridad', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/education-levels',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'documents', 
        name: 'Tipos de Documento', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/document-types',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'marital', 
        name: 'Estados Civiles', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/marital-status',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      }
    ]
  },
  phase2: {
    title: "Fase 2: Servicios Médicos y Seguros",
    description: "Facturación Guatemala + seguros médicos + Integración CIE-11",
    total: 8,
    catalogs: [
      { 
        id: 'companies', 
        name: 'Empresas/NITs', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/companies',
        adminOnly: true,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'services', 
        name: 'Servicios Médicos', 
        status: 'implemented', 
        apiEndpoint: '/api/catalogs/medical-services',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'pricing', 
        name: 'Precios por Médico', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/service-pricing',
        adminOnly: true,
        doctorAccess: 'read',
        assistantAccess: 'none'
      },
      { 
        id: 'insurance', 
        name: 'Aseguradoras', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/insurance-providers',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'coverage', 
        name: 'Planes de Cobertura', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/coverage-plans',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'medications', 
        name: 'Medicamentos', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/medications',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'cie11', 
        name: 'Integración CIE-11', 
        status: 'pending', 
        apiEndpoint: '/api/medical/cie11-integration',
        adminOnly: true,
        doctorAccess: 'read',
        assistantAccess: 'none'
      },
      { 
        id: 'payments', 
        name: 'Formas de Pago', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/payment-methods',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      }
    ]
  },
  phase3: {
    title: "Fase 3: Catálogos Avanzados",
    description: "Funcionalidades opcionales y empresariales",
    total: 7,
    catalogs: [
      { 
        id: 'memberships', 
        name: 'Membresías', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/memberships',
        adminOnly: true,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'banks', 
        name: 'Bancos', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/banks',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'conditions', 
        name: 'Padecimientos/Síntomas', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/conditions',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'none'
      },
      { 
        id: 'zones', 
        name: 'Zonas', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/zones',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'colonies', 
        name: 'Colonias', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/colonies',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      },
      { 
        id: 'religions', 
        name: 'Religiones', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/religions',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'edit'
      },
      { 
        id: 'docTypes', 
        name: 'Tipos de Documento Avanzados', 
        status: 'pending', 
        apiEndpoint: '/api/catalogs/document-types-advanced',
        adminOnly: false,
        doctorAccess: 'read',
        assistantAccess: 'read'
      }
    ]
  }
};

// GET - Obtener metadatos de catálogos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    const searchParams = request.nextUrl.searchParams;
    const phase = searchParams.get('phase');
    const includeStats = searchParams.get('stats') === 'true';

    // Filtrar catálogos según el rol del usuario
    const filterCatalogsByRole = (catalogs: any[]) => {
      return catalogs.map(catalog => {
        let hasAccess = true;
        let accessLevel = 'none';

        switch (userRole) {
          case 'admin':
            accessLevel = 'edit';
            break;
          case 'doctor':
            if (catalog.doctorAccess === 'none') {
              hasAccess = false;
            } else {
              accessLevel = catalog.doctorAccess;
            }
            break;
          case 'assistant':
            if (catalog.assistantAccess === 'none') {
              hasAccess = false;
            } else {
              accessLevel = catalog.assistantAccess;
            }
            break;
          default:
            hasAccess = false;
        }

        return {
          ...catalog,
          hasAccess,
          accessLevel,
          // Solo mostrar API endpoint si tiene acceso
          apiEndpoint: hasAccess ? catalog.apiEndpoint : undefined
        };
      }).filter(catalog => catalog.hasAccess);
    };

    let result: any = {};

    if (phase && catalogsMetadata[phase as keyof typeof catalogsMetadata]) {
      // Retornar catálogos de una fase específica
      const phaseData = catalogsMetadata[phase as keyof typeof catalogsMetadata];
      result = {
        ...phaseData,
        catalogs: filterCatalogsByRole(phaseData.catalogs)
      };
    } else {
      // Retornar todas las fases
      result = Object.entries(catalogsMetadata).reduce((acc, [key, value]) => {
        acc[key] = {
          ...value,
          catalogs: filterCatalogsByRole(value.catalogs)
        };
        return acc;
      }, {} as any);
    }

    // Agregar estadísticas si se solicita
    if (includeStats) {
      const allCatalogs = Object.values(catalogsMetadata).flatMap(phase => phase.catalogs);
      const filteredCatalogs = filterCatalogsByRole(allCatalogs);
      
      result.stats = {
        total: filteredCatalogs.length,
        implemented: filteredCatalogs.filter(c => c.status === 'implemented').length,
        pending: filteredCatalogs.filter(c => c.status === 'pending').length,
        editable: filteredCatalogs.filter(c => c.accessLevel === 'edit').length,
        readonly: filteredCatalogs.filter(c => c.accessLevel === 'read').length
      };
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('Error obteniendo metadatos de catálogos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Verificar disponibilidad de un catálogo específico
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { catalogId } = await request.json();

    if (!catalogId) {
      return NextResponse.json(
        { error: 'ID de catálogo requerido' },
        { status: 400 }
      );
    }

    // Buscar el catálogo en todas las fases
    let catalogInfo = null;
    let phaseInfo = null;

    for (const [phaseKey, phaseData] of Object.entries(catalogsMetadata)) {
      const catalog = phaseData.catalogs.find(c => c.id === catalogId);
      if (catalog) {
        catalogInfo = catalog;
        phaseInfo = { key: phaseKey, title: phaseData.title };
        break;
      }
    }

    if (!catalogInfo) {
      return NextResponse.json(
        { error: 'Catálogo no encontrado' },
        { status: 404 }
      );
    }

    const userRole = sessionClaims?.metadata?.role;
    let hasAccess = true;
    let accessLevel = 'none';

    switch (userRole) {
      case 'admin':
        accessLevel = 'edit';
        break;
      case 'doctor':
        if (catalogInfo.doctorAccess === 'none') {
          hasAccess = false;
        } else {
          accessLevel = catalogInfo.doctorAccess;
        }
        break;
      case 'assistant':
        if (catalogInfo.assistantAccess === 'none') {
          hasAccess = false;
        } else {
          accessLevel = catalogInfo.assistantAccess;
        }
        break;
      default:
        hasAccess = false;
    }

    return NextResponse.json({
      catalog: {
        ...catalogInfo,
        hasAccess,
        accessLevel,
        apiEndpoint: hasAccess ? catalogInfo.apiEndpoint : undefined
      },
      phase: phaseInfo
    });

  } catch (error) {
    console.error('Error verificando catálogo:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}