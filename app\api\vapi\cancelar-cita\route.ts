import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export async function DELETE(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { shortCode, motivo } = body;

    // Validaciones básicas
    if (!shortCode) {
      return NextResponse.json(
        { success: false, error: 'Campo requerido: shortCode' },
        { status: 400 }
      );
    }

    console.log(`❌ Cancelando cita VAPI - ShortCode: ${shortCode}`);

    // 1. Buscar la cita por shortCode
    const appointmentResult = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        status: appointments.status,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        shortCode: appointments.shortCode,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        consultoryId: appointments.consultoryId,
        tempPatientData: appointments.tempPatientData,
        createdAt: appointments.createdAt,
      })
      .from(appointments)
      .where(eq(appointments.shortCode, shortCode.toUpperCase()))
      .limit(1);

    if (appointmentResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cita no encontrada con ese código' },
        { status: 404 }
      );
    }

    const appointment = appointmentResult[0];

    // 2. Verificar que la cita se puede cancelar
    const estadosNoCancelables = ['cancelled', 'completed', 'no_show'];
    if (estadosNoCancelables.includes(appointment.status)) {
      return NextResponse.json(
        { 
          success: false, 
          error: `No se puede cancelar una cita con estado: ${appointment.status}`,
          code: 'CANNOT_CANCEL_STATUS'
        },
        { status: 400 }
      );
    }

    // 3. Verificar límite de tiempo (24 horas para citas VAPI)
    const horasDesdeCreacion = (Date.now() - appointment.createdAt.getTime()) / (1000 * 60 * 60);
    const horasHastaCita = (appointment.startTime.getTime() - Date.now()) / (1000 * 60 * 60);

    // Si la cita es en menos de 2 horas, no permitir cancelación vía VAPI
    if (horasHastaCita < 2) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'No se pueden cancelar citas con menos de 2 horas de anticipación via teléfono. Llame directamente al consultorio.',
          code: 'TOO_LATE_TO_CANCEL'
        },
        { status: 400 }
      );
    }

    // 4. Obtener información adicional para la respuesta
    let patientName = 'Paciente';
    let doctorName = 'Doctor';
    let consultoryName = 'Consultorio';

    // Obtener nombre del paciente
    if (appointment.patientId) {
      const patientResult = await db
        .select({
          firstName: user.firstName,
          lastName: user.lastName,
        })
        .from(user)
        .where(eq(user.id, appointment.patientId))
        .limit(1);
      
      if (patientResult.length > 0) {
        const patient = patientResult[0];
        patientName = `${patient.firstName} ${patient.lastName}`;
      }
    } else if (appointment.tempPatientData) {
      // Si es paciente temporal (creado por VAPI pero no confirmado)
      try {
        const tempData = JSON.parse(appointment.tempPatientData);
        patientName = tempData.name || 'Paciente';
      } catch (e) {
        console.warn('Error parsing tempPatientData:', e);
      }
    }

    // Obtener nombre del doctor
    const doctorResult = await db
      .select({
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(eq(user.id, appointment.doctorId))
      .limit(1);
    
    if (doctorResult.length > 0) {
      const doctor = doctorResult[0];
      doctorName = `${doctor.firstName} ${doctor.lastName}`;
    }

    // Obtener nombre del consultorio
    if (appointment.consultoryId) {
      const consultoryResult = await db
        .select({ name: consultories.name })
        .from(consultories)
        .where(eq(consultories.id, appointment.consultoryId))
        .limit(1);
      
      if (consultoryResult.length > 0) {
        consultoryName = consultoryResult[0].name;
      }
    }

    // 5. Cancelar la cita
    await db.update(appointments)
      .set({
        status: 'cancelled',
        cancelledAt: new Date(),
        cancellationReason: motivo || 'Cancelada por reagendamiento via VAPI',
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointment.id));

    // 6. Log del evento
    console.log(`✅ Cita cancelada exitosamente - ID: ${appointment.id}, Paciente: ${patientName}`);

    // 7. Formatear fecha y hora para respuesta
    const fechaOriginal = format(appointment.scheduledDate, "EEEE, dd 'de' MMMM", { locale: es });
    const horaOriginal = format(appointment.startTime, 'h:mm a');

    return NextResponse.json({
      success: true,
      data: {
        citaCancelada: shortCode,
        citaId: appointment.id,
        pacienteNombre: patientName,
        doctorNombre: doctorName,
        consultorioNombre: consultoryName,
        fechaOriginal,
        horaOriginal,
        motivoCancelacion: motivo || 'Reagendamiento solicitado',
        horarioLiberado: true,
        mensaje: `Cita cancelada exitosamente para ${patientName} el ${fechaOriginal} a las ${horaOriginal}. El horario está disponible nuevamente.`
      }
    });

  } catch (error) {
    console.error('Error en cancelar-cita VAPI:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}