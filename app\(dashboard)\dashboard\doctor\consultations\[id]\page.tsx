'use client';

import { useState, useEffect, use, useMemo, useCallback, useRef } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ServiceSelector } from '@/components/ui/service-selector';
import { MedicationSelector } from '@/components/ui/medication-selector';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { MultiSelect } from '@/components/ui/multi-select';
import { 
  ArrowLeft, 
  User, 
  Calendar,
  Clock,
  Stethoscope,
  Heart,
  Activity,
  FileText,
  Save,
  CheckCircle,
  AlertTriangle,
  Loader2,
  Edit,
  Pill,
  FileSignature,
  Plus,
  Trash2,
  ClipboardList,
  AlertCircle,
  X,
  MessageSquare,
  Download
} from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { generatePrescriptionPDF, validatePrescriptionData, calculateAge } from '@/lib/pdf-utils';
import { PreCheckinConsultationSummary } from '@/components/pre-checkin/pre-checkin-consultation-summary';

interface ConsultationPageProps {
  params: Promise<{
    id: string;
  }>;
}

export default function ConsultationPage({ params }: ConsultationPageProps) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [consultation, setConsultation] = useState<any>(null);
  const [patientHistory, setPatientHistory] = useState<any[]>([]);
  
  // Unwrap the params Promise
  const { id } = use(params);
  
  // Estados para modo de visualización
  const [activeTab, setActiveTab] = useState('patient');
  
  // Estados para edición por secciones
  const [editingSections, setEditingSections] = useState<{[key: string]: boolean}>({});
  
  // Estados para auto-guardado
  const [autoSaving, setAutoSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const autoSaveTimeoutRef = useRef<NodeJS.Timeout>();

  // Función para calcular la fecha de la próxima cita
  const calculateNextAppointmentDate = useCallback((value: number, unit: string, baseDate = new Date()) => {
    const date = new Date(baseDate);
    
    switch(unit) {
      case 'days':
        date.setDate(date.getDate() + value);
        break;
      case 'weeks':
        date.setDate(date.getDate() + (value * 7));
        break;
      case 'months':
        date.setMonth(date.getMonth() + value);
        break;
      case 'years':
        date.setFullYear(date.getFullYear() + value);
        break;
    }
    
    return date;
  }, []);
  const formDataRef = useRef<any>(null);
  
  // Estados para catálogos - simplificados
  const [medications, setMedications] = useState<any[]>([]);
  const [symptoms, setSymptoms] = useState<any[]>([]);
  const [pathologicalHistory, setPathologicalHistory] = useState<any[]>([]);
  const [nonPathologicalHistory, setNonPathologicalHistory] = useState<any[]>([]);
  const [medicalServices, setMedicalServices] = useState<any[]>([]);
  const [religions, setReligions] = useState<any[]>([]);
  const [occupations, setOccupations] = useState<any[]>([]);
  const [maritalStatuses, setMaritalStatuses] = useState<any[]>([]);
  const [educationLevels, setEducationLevels] = useState<any[]>([]);
  const [mediaSources, setMediaSources] = useState<any[]>([]);
  const [companies, setCompanies] = useState<any[]>([]);
  const [consultories, setConsultories] = useState<any[]>([]);
  const [availableDoctors, setAvailableDoctors] = useState<any[]>([]);
  const [currentDoctorName, setCurrentDoctorName] = useState('');
  
  // Refs para los campos de notas de servicios
  const serviceNotesRefs = useRef<{ [key: number]: HTMLTextAreaElement | null }>({});
  
  // Función para enfocar el campo de notas de un servicio
  const focusServiceNotes = useCallback((serviceIndex: number) => {
    setTimeout(() => {
      const notesField = serviceNotesRefs.current[serviceIndex];
      if (notesField) {
        notesField.focus();
      }
    }, 200); // Pequeño delay para asegurar que el DOM se actualice
  }, []);
  
  const [formData, setFormData] = useState<any>({
    currentIllness: '',
    selectedSymptoms: [],
    pathologicalHistory: [],
    nonPathologicalHistory: [],
    allergies: [],
    services: [],
    demographics: {
      religion: '',
      occupation: '',
      maritalStatus: '',
      educationLevel: ''
    },
    administrative: {
      mediaSource: '',
      company: '',
      preferredConsultory: ''
    },
    vitalSigns: {
      weight: '',
      height: '',
      temperature: '',
      heartRate: '',
      respiratoryRate: '',
      bloodPressure: '',
      oxygenSaturation: ''
    },
    physicalExam: {
      general: '',
      head: '',
      neck: '',
      chest: '',
      abdomen: '',
      extremities: '',
      neurological: ''
    },
    diagnoses: [{ code: '', description: '', type: 'primary' }],
    treatment: {
      plan: '',
      diet: '',
      activity: '',
      monitoring: ''
    },
    prescriptions: [],
    recommendations: '',
    followUpInstructions: '',
    nextAppointment: {
      value: 1,
      unit: 'weeks',
      type: 'control',
      notes: '',
      calculatedDate: null
    }
  });

  // Tabs reorganizados según nueva estructura
  const tabs = [
    { id: 'patient', label: 'Información del Paciente', icon: User },
    { id: 'medical', label: 'Información Médica', icon: Stethoscope },
    { id: 'diagnosis', label: 'Diagnóstico y Tratamiento', icon: ClipboardList }
  ];


  const appointmentId = searchParams.get('appointment');

  const fetchMedicalRecord = useCallback(async (medicalRecordId: string) => {
    try {
      const response = await fetch(`/api/medical-records/${medicalRecordId}`);
      const result = await response.json();

      if (response.ok && result.data) {
        const recordData = result.data;
        
        // Cargar datos del expediente médico en el formulario
        setFormData(prev => ({
          ...prev,
          demographics: {
            ...prev.demographics,
            ...(recordData.record?.demographics || {})
          },
          administrative: {
            ...prev.administrative,
            ...(recordData.record?.administrative || {})
          },
          pathologicalHistory: recordData.record?.pathologicalHistory || prev.pathologicalHistory,
          nonPathologicalHistory: recordData.record?.nonPathologicalHistory || prev.nonPathologicalHistory,
          allergies: recordData.medicalHistory?.allergies || recordData.record?.patientSummary?.allergies || prev.allergies,
          selectedSymptoms: recordData.record?.selectedSymptoms || prev.selectedSymptoms,
        }));
      }
    } catch (error) {
      console.error('Error loading medical record:', error);
    }
  }, []);

  const fetchPatientHistory = useCallback(async (patientId: string) => {
    try {
      const response = await fetch(`/api/medical-consultations/patient/${patientId}?exclude=${id}&limit=3`);
      const result = await response.json();

      if (response.ok && result.success) {
        console.log('Patient history loaded:', result.data);
        setPatientHistory(result.data || []);
      } else {
        console.error('Error loading patient history:', result.error);
        setPatientHistory([]);
      }
    } catch (error) {
      console.error('Error fetching patient history:', error);
      setPatientHistory([]);
    }
  }, [id]);

  const fetchConsultationData = useCallback(async () => {
    try {
      const response = await fetch(`/api/medical-consultations/${id}`);
      const result = await response.json();

      if (response.ok && result.success) {
        const data = result.data;
        console.log('Consultation data received:', data);
        setConsultation(data);
        
        // Cargar datos del expediente médico si existe
        if (data?.medicalRecord?.id) {
          await fetchMedicalRecord(data.medicalRecord.id);
        }
        
        // Cargar historial del paciente
        if (data?.patient?.id) {
          await fetchPatientHistory(data.patient.id);
        }
        
        // Pre-cargar formulario con datos existentes de la consulta
        if (data?.consultation) {
          console.log('🔍 DEBUG FRONTEND: Servicios recibidos desde API:', data.consultation.services);
          setFormData(prev => {
            console.log('🔍 DEBUG FRONTEND: FormData anterior services:', prev.services);
            return {
              ...prev,
            currentIllness: data.consultation.currentIllness || (data.preCheckin?.symptoms || ''),
            services: data.consultation.services || [],
            vitalSigns: {
              ...prev.vitalSigns,
              ...(data.consultation.vitalSigns || {})
            },
            physicalExam: {
              ...prev.physicalExam,
              ...(data.consultation.physicalExam || {})
            },
            diagnoses: data.consultation.diagnoses?.length > 0 ? data.consultation.diagnoses : prev.diagnoses,
            treatment: {
              ...prev.treatment,
              ...(data.consultation.treatment || {})
            },
            prescriptions: data.consultation.prescriptions || [],
            recommendations: data.consultation.recommendations || '',
            followUpInstructions: data.consultation.followUpInstructions || '',
            nextAppointment: data.consultation.nextAppointment || {
              value: 1,
              unit: 'weeks',
              type: 'control',
              notes: '',
              calculatedDate: null
            },
            };
          });
        }
      } else {
        console.error('Error response:', result);
        toast.error(result.error || 'Error al cargar la consulta');
        router.push('/dashboard/doctor/agenda');
      }
    } catch (error) {
      console.error('Error fetching consultation:', error);
      toast.error('Error al conectar con el servidor');
      router.push('/dashboard/doctor/agenda');
    } finally {
      setLoading(false);
    }
  }, [id, router, fetchMedicalRecord, fetchPatientHistory]);

  const fetchCatalogs = useCallback(async () => {
    try {
      // Cargar medicamentos
      const medicationsResponse = await fetch('/api/catalogs/medications?status=active&limit=100');
      if (medicationsResponse.ok) {
        const medicationsData = await medicationsResponse.json();
        setMedications(medicationsData.data || []);
      }

      // Cargar síntomas
      const symptomsResponse = await fetch('/api/catalogs/symptoms?status=active&limit=100');
      if (symptomsResponse.ok) {
        const symptomsData = await symptomsResponse.json();
        setSymptoms(symptomsData.data || []);
      }

      // Cargar antecedentes patológicos
      const pathologicalResponse = await fetch('/api/catalogs/pathological-history?status=active&limit=100');
      if (pathologicalResponse.ok) {
        const pathologicalData = await pathologicalResponse.json();
        setPathologicalHistory(pathologicalData.data || []);
      }

      // Cargar antecedentes no patológicos
      const nonPathologicalResponse = await fetch('/api/catalogs/non-pathological-history?status=active&limit=100');
      if (nonPathologicalResponse.ok) {
        const nonPathologicalData = await nonPathologicalResponse.json();
        setNonPathologicalHistory(nonPathologicalData.data || []);
      }

      // Cargar servicios médicos
      const medicalServicesResponse = await fetch('/api/catalogs/medical-services?status=active&limit=100');
      if (medicalServicesResponse.ok) {
        const medicalServicesData = await medicalServicesResponse.json();
        setMedicalServices(medicalServicesData.data || []);
      }

      // Cargar religiones
      const religionsResponse = await fetch('/api/catalogs/religions?status=active&limit=100');
      if (religionsResponse.ok) {
        const religionsData = await religionsResponse.json();
        setReligions(religionsData.data || []);
      }

      // Cargar ocupaciones
      const occupationsResponse = await fetch('/api/catalogs/occupations?status=active&limit=100');
      if (occupationsResponse.ok) {
        const occupationsData = await occupationsResponse.json();
        setOccupations(occupationsData.data || []);
      }

      // Cargar estados civiles
      const maritalResponse = await fetch('/api/catalogs/marital-status?status=active&limit=100');
      if (maritalResponse.ok) {
        const maritalData = await maritalResponse.json();
        setMaritalStatuses(maritalData.data || []);
      }

      // Cargar niveles educativos
      const educationResponse = await fetch('/api/catalogs/education-levels?status=active&limit=100');
      if (educationResponse.ok) {
        const educationData = await educationResponse.json();
        setEducationLevels(educationData.data || []);
      }

      // Cargar fuentes de medios (administrativo)
      const mediaResponse = await fetch('/api/catalogs/media-sources?status=active&limit=100');
      if (mediaResponse.ok) {
        const mediaData = await mediaResponse.json();
        setMediaSources(mediaData.data || []);
      }

      // Cargar empresas (administrativo)
      const companiesResponse = await fetch('/api/catalogs/companies?status=active&limit=100');
      if (companiesResponse.ok) {
        const companiesData = await companiesResponse.json();
        setCompanies(companiesData.data || []);
      }

      // Cargar consultorios (administrativo)
      const consultoriesResponse = await fetch('/api/catalogs/consultories?status=active&limit=100');
      if (consultoriesResponse.ok) {
        const consultoriesData = await consultoriesResponse.json();
        setConsultories(consultoriesData.data || []);
      }

      // Cargar doctores del consultorio
      const doctorsResponse = await fetch('/api/agenda/doctors');
      if (doctorsResponse.ok) {
        const doctorsData = await doctorsResponse.json();
        setAvailableDoctors(doctorsData.data || []);
      }

      // Obtener información del doctor actual
      if (user?.id) {
        const currentUserResponse = await fetch('/api/auth/current-user');
        if (currentUserResponse.ok) {
          const currentUserData = await currentUserResponse.json();
          if (currentUserData.success && currentUserData.data) {
            const fullName = `${currentUserData.data.firstName || ''} ${currentUserData.data.lastName || ''}`.trim();
            const doctorName = fullName || currentUserData.data.name || 'Doctor';
            setCurrentDoctorName(doctorName);
          }
        }
      }
    } catch (error) {
      console.error('Error loading catalogs:', error);
    }
  }, [user?.id]);

  const saveMedicalRecordData = useCallback(async () => {
    if (!consultation?.medicalRecord?.id) return;

    try {
      const response = await fetch(`/api/medical-records/${consultation.medicalRecord.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          demographics: formData.demographics,
          administrative: formData.administrative,
          pathologicalHistory: formData.pathologicalHistory,
          nonPathologicalHistory: formData.nonPathologicalHistory,
          allergies: formData.allergies,
          selectedSymptoms: formData.selectedSymptoms
        })
      });

      if (!response.ok) {
        console.error('Error saving medical record data');
      }
    } catch (error) {
      console.error('Error saving medical record:', error);
    }
  }, [consultation?.medicalRecord?.id, formData.demographics, formData.administrative, formData.pathologicalHistory, formData.nonPathologicalHistory, formData.allergies, formData.selectedSymptoms]);

  const handleSave = useCallback(async (status = 'draft') => {
    // Validaciones básicas
    if (status === 'completed') {
      if (!formData.currentIllness) {
        toast.error('Debe registrar la enfermedad actual');
        setActiveTab('general');
        return;
      }
      if (!formData.diagnoses[0]?.description) {
        toast.error('Debe registrar al menos un diagnóstico');
        setActiveTab('diagnosis');
        return;
      }
    }

    setSaving(true);
    try {
      // Guardar datos del expediente médico primero
      await saveMedicalRecordData();
      
      // Preparar datos de la consulta (sin datos demográficos ni del expediente médico)
      const consultationData = {
        currentIllness: formData.currentIllness,
        services: formData.services,
        vitalSigns: formData.vitalSigns,
        physicalExam: formData.physicalExam,
        diagnoses: formData.diagnoses,
        treatment: formData.treatment,
        prescriptions: formData.prescriptions,
        recommendations: formData.recommendations,
        followUpInstructions: formData.followUpInstructions,
        nextAppointment: formData.nextAppointment,
        status
      };
      
      const response = await fetch(`/api/medical-consultations/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(consultationData)
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Actualizar estados de auto-guardado
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
        
        toast.success(status === 'completed' ? 'Consulta completada exitosamente' : 'Consulta guardada exitosamente');
        
        if (status === 'completed') {
          router.push('/dashboard/doctor/agenda');
        } else {
          // Permanecer en la página
          router.push(`/dashboard/doctor/consultations/${id}`);
        }
      } else {
        toast.error(result.error || 'Error al guardar la consulta');
      }
    } catch (error) {
      console.error('Error saving consultation:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setSaving(false);
    }
  }, [formData, consultation, id, appointmentId, router, saveMedicalRecordData]);

  const updateFormData = useCallback((section: string, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  }, []);

  const updateArrayField = useCallback((section: string, index: number, field: string, value: any) => {
    setFormData((prev: any) => ({
      ...prev,
      [section]: prev[section].map((item: any, i: number) => 
        i === index ? { ...item, [field]: value } : item
      )
    }));
  }, []);

  // Función para convertir valores de frecuencia a texto legible
  const getFrequencyText = useCallback((frequency: string) => {
    const frequencyMap: Record<string, string> = {
      'cada_4h': 'Cada 4 horas',
      'cada_6h': 'Cada 6 horas', 
      'cada_8h': 'Cada 8 horas',
      'cada_12h': 'Cada 12 horas',
      'cada_24h': 'Cada 24 horas',
      'sos': 'SOS (según necesidad)'
    };
    return frequencyMap[frequency] || frequency || 'No especificado';
  }, []);

  const addPrescription = useCallback(() => {
    setFormData((prev: any) => ({
      ...prev,
      prescriptions: [...prev.prescriptions, {
        medication: '',
        dose: '',
        frequency: '',
        duration: '',
        route: 'oral',
        instructions: '',
        addedAt: new Date().toISOString() // Marcador temporal para detectar nuevas prescripciones
      }]
    }));
  }, []);

  const removePrescription = useCallback((index: number) => {
    setFormData((prev: any) => ({
      ...prev,
      prescriptions: prev.prescriptions.filter((_: any, i: number) => i !== index)
    }));
  }, []);

  // Función para generar PDF de prescripción
  const handleGeneratePrescriptionPDF = useCallback(async () => {
    try {
      // Obtener datos del paciente desde consultation
      const patient = {
        name: consultation?.patient?.name || 'Paciente',
        documentNumber: consultation?.patient?.documentNumber,
        age: consultation?.patient?.age,
        phone: consultation?.patient?.phone
      };

      // Obtener datos del doctor
      const doctor = {
        name: consultation?.doctor?.name || currentDoctorName || user?.fullName || user?.firstName + ' ' + user?.lastName || 'Doctor',
        medicalLicense: consultation?.doctor?.medicalLicense,
        specialty: consultation?.doctor?.specialty,
        phone: consultation?.doctor?.phone,
        email: consultation?.doctor?.email
      };

      // Obtener datos del consultorio
      const consultory = {
        name: consultation?.consultory?.name || 'Consultorio Médico',
        address: consultation?.consultory?.address,
        phone: consultation?.consultory?.phone,
        email: consultation?.consultory?.email,
        logoUrl: consultation?.consultory?.logoUrl
      };

      // Validar datos antes de generar
      const validationErrors = validatePrescriptionData({
        prescriptions: formData.prescriptions,
        patient,
        doctor,
        consultory
      });

      if (validationErrors.length > 0) {
        toast.error(`Error en los datos: ${validationErrors.join(', ')}`);
        return;
      }

      // Procesar prescripciones para incluir nombres de medicamentos
      const processedPrescriptions = formData.prescriptions.map((prescription: any) => {
        const selectedMed = medications.find(m => m.id === prescription.medication);
        return {
          ...prescription,
          medicationName: selectedMed ? selectedMed.name : prescription.medication,
          medicationDetails: selectedMed ? `${selectedMed.dosageForm} - ${selectedMed.strength}` : ''
        };
      });

      // Generar y descargar PDF
      await generatePrescriptionPDF({
        prescriptions: processedPrescriptions,
        patient,
        doctor,
        consultory,
        consultationDate: new Date(consultation?.consultationDate || Date.now()),
        diagnoses: formData.diagnoses || []
      });

      toast.success('PDF de prescripción generado exitosamente');
      
    } catch (error) {
      console.error('Error generando PDF:', error);
      toast.error('Error al generar el PDF de la prescripción');
    }
  }, [formData.prescriptions, formData.diagnoses, consultation, currentDoctorName, user, medications]);

  useEffect(() => {
    if (id) {
      fetchConsultationData();
      fetchCatalogs();
    }
  }, [id, fetchConsultationData, fetchCatalogs]);


  // Calcular total de servicios - DEBE estar antes de cualquier return condicional
  const totalServices = useMemo(() => {
    if (!Array.isArray(formData.services)) return 0;
    return formData.services.reduce((total, service) => {
      const price = parseFloat(service.price) || 0;
      return total + price;
    }, 0);
  }, [formData.services]);

  // Obtener moneda del primer servicio o usar Q. por defecto
  const currency = formData.services?.[0]?.currency || 'Q.';

  // Funciones para manejo de edición por secciones
  const toggleSectionEdit = useCallback((sectionKey: string) => {
    const wasEditing = editingSections[sectionKey];
    setEditingSections(prev => ({
      ...prev,
      [sectionKey]: !prev[sectionKey]
    }));

    // Auto-focus cuando se entra en modo edición
    if (!wasEditing) {
      setTimeout(() => {
        focusSectionField(sectionKey);
      }, 100);
    }
  }, [editingSections]);

  // Función para auto-focus por sección
  const focusSectionField = useCallback((sectionKey: string) => {
    let selector = '';
    
    switch (sectionKey) {
      case 'currentIllness':
        selector = 'textarea[placeholder*="Describe la enfermedad actual"]';
        break;
      case 'demographics':
        selector = 'button[aria-haspopup="listbox"]'; // Primer select (religión)
        break;
      case 'administrative':
        selector = 'button[aria-haspopup="listbox"]'; // Primer select (fuente)
        break;
      case 'symptoms':
        selector = 'input[placeholder*="Seleccionar síntomas"]';
        break;
      case 'pathologicalHistory':
        selector = 'input[placeholder*="Seleccionar antecedentes"]';
        break;
      case 'nonPathologicalHistory':
        selector = 'input[placeholder*="Seleccionar antecedentes"]';
        break;
      case 'allergies':
        selector = 'input[placeholder*="Penicilina"]:not([data-allergy-index])'; // Primera alergia existente
        break;
      case 'diagnoses':
        selector = 'input[placeholder*="Z00.0"]';
        break;
      case 'treatment':
        selector = 'textarea[placeholder*="plan general de tratamiento"]';
        break;
      case 'prescriptions':
        selector = 'input[placeholder*="Buscar medicamento"]';
        break;
      case 'recommendations':
        selector = 'textarea[placeholder*="Recomendaciones adicionales"]';
        break;
      case 'services':
        selector = 'input[placeholder*="Buscar y seleccionar servicio"]';
        break;
      default:
        // Buscar el primer input o textarea en la sección
        selector = `[data-section="${sectionKey}"] input:not([disabled]), [data-section="${sectionKey}"] textarea:not([disabled])`;
    }

    const element = document.querySelector(selector) as HTMLElement;
    if (element) {
      element.focus();
      // Si es un textarea, colocar cursor al final
      if (element.tagName === 'TEXTAREA' || element.tagName === 'INPUT') {
        const inputElement = element as HTMLInputElement | HTMLTextAreaElement;
        inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length);
      }
    }
  }, []);

  const isSectionEditing = useCallback((sectionKey: string) => {
    return editingSections[sectionKey];
  }, [editingSections]);

  const saveSectionChanges = useCallback(async (sectionKey: string, status: 'draft' | 'completed' = 'draft') => {
    try {
      const result = await handleSave(status);
      if (result?.success) {
        setEditingSections(prev => ({
          ...prev,
          [sectionKey]: false
        }));
        const statusText = status === 'completed' ? 'finalizada' : 'guardada';
        toast.success(`Sección "${sectionKey}" ${statusText} exitosamente`);
      }
    } catch (error) {
      toast.error('Error al guardar la sección');
    }
  }, [handleSave]);

  const cancelSectionEdit = useCallback((sectionKey: string) => {
    setEditingSections(prev => ({
      ...prev,
      [sectionKey]: false
    }));
    // Recargar datos originales
    fetchConsultationData();
  }, [fetchConsultationData]);

  // Auto-guardado silencioso
  const autoSave = useCallback(async () => {
    if (!consultation || autoSaving) return;
    
    try {
      setAutoSaving(true);
      console.log('🔄 AUTO-SAVE: Guardando servicios:', formData.services);
      
      const response = await fetch(`/api/medical-consultations/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          ...formData, 
          status: 'draft' // Siempre auto-guardar como borrador
        })
      });

      const result = await response.json();
      
      if (result.success) {
        setHasUnsavedChanges(false);
        setLastSaved(new Date());
      }
    } catch (error) {
      console.error('Error en auto-guardado:', error);
    } finally {
      setAutoSaving(false);
    }
  }, [consultation, formData, id, autoSaving]);

  // Debounce del auto-guardado
  const debouncedAutoSave = useCallback(() => {
    if (autoSaveTimeoutRef.current) {
      clearTimeout(autoSaveTimeoutRef.current);
    }
    
    setHasUnsavedChanges(true);
    
    autoSaveTimeoutRef.current = setTimeout(() => {
      autoSave();
    }, 3000); // 3 segundos de debounce
  }, [autoSave]);

  // Trigger manual para auto-guardado en campos específicos
  const triggerAutoSave = useCallback(() => {
    console.log('🔄 TRIGGER MANUAL: Auto-guardado activado por cambio de campo');
    debouncedAutoSave();
  }, [debouncedAutoSave]);

  // Auto-guardado cuando cambia formData (evitar loop infinito)
  useEffect(() => {
    // Solo auto-guardar si realmente hay cambios y no es la carga inicial
    if (consultation && formData && formDataRef.current) {
      const hasRealChanges = JSON.stringify(formDataRef.current) !== JSON.stringify(formData);
      if (hasRealChanges) {
        console.log('🔄 CAMBIO DETECTADO en formData, activando auto-save');
        debouncedAutoSave();
      }
    }
    formDataRef.current = formData;
  }, [formData, consultation, debouncedAutoSave]);

  // Protección antes de cerrar la ventana
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '¿Estás seguro de que quieres salir? Tienes cambios sin guardar.';
        return e.returnValue;
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    
    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges]);

  // Limpiar timeout al desmontar
  useEffect(() => {
    return () => {
      if (autoSaveTimeoutRef.current) {
        clearTimeout(autoSaveTimeoutRef.current);
      }
    };
  }, []);

  // Componente para headers de sección con edición individual
  const SectionHeader = ({ title, icon: Icon, sectionKey, description }: { 
    title: string; 
    icon: any; 
    sectionKey: string; 
    description?: string;
  }) => {
    const isEditing = isSectionEditing(sectionKey);
    
    return (
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Icon className="h-5 w-5 text-[#3D4E80]" />
            <div>
              <CardTitle className="text-lg">{title}</CardTitle>
              {description && (
                <CardDescription className="text-[#3D4E80]/70">{description}</CardDescription>
              )}
            </div>
          </div>
          
          {consultation.consultation.status !== 'completed' && (
            <div className="flex items-center gap-2">
              {isEditing ? (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => cancelSectionEdit(sectionKey)}
                    className="text-gray-600"
                  >
                    <X className="h-4 w-4 mr-1" />
                    Cancelar
                  </Button>
                  <Button
                    size="sm"
                    onClick={() => saveSectionChanges(sectionKey)}
                    className="bg-[#3D4E80] hover:bg-[#3D4E80]/80 text-white"
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Guardar
                  </Button>
                </>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => toggleSectionEdit(sectionKey)}
                  className="text-[#3D4E80] border-[#3D4E80]/30 hover:bg-[#3D4E80]/10"
                >
                  <Edit className="h-4 w-4 mr-1" />
                  Editar
                </Button>
              )}
            </div>
          )}
        </div>
      </CardHeader>
    );
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
          <span className="text-gray-500">Cargando consulta...</span>
        </div>
      </div>
    );
  }

  if (!consultation) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center text-gray-500">
          No se pudo cargar la información de la consulta
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
        {/* Header Responsivo según estándar */}
        <div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0 mb-6">
          <div className="flex items-center space-x-3">
            <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/doctor/agenda')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <div className="flex items-center gap-3 mb-1">
                <h1 className="text-2xl font-bold text-gray-900">
                  Consulta Médica #{consultation.consultation.id.slice(0, 8)}
                </h1>
                <Badge variant="outline" className="text-sm font-medium bg-blue-50 text-blue-700 border-blue-200">
                  Expediente: {consultation.medicalRecord.recordNumber}
                </Badge>
                
                {/* Indicador de estado de guardado */}
                <div className="flex items-center gap-1.5">
                  {autoSaving ? (
                    <>
                      <Loader2 className="h-3 w-3 animate-spin text-blue-500" />
                      <span className="text-xs text-blue-600">Guardando...</span>
                    </>
                  ) : hasUnsavedChanges ? (
                    <>
                      <div className="h-2 w-2 bg-orange-500 rounded-full animate-pulse"></div>
                      <span className="text-xs text-orange-600">Sin guardar</span>
                    </>
                  ) : lastSaved ? (
                    <>
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-xs text-green-600">
                        Guardado {format(lastSaved, 'HH:mm')}
                      </span>
                    </>
                  ) : null}
                </div>
              </div>
              <p className="text-gray-600 text-sm">
                {consultation.patient.firstName} {consultation.patient.lastName}
              </p>
              
              {/* Badges en móvil */}
              <div className="flex items-center justify-between gap-2 mt-2 lg:hidden">
                <div className="flex items-center gap-2">
                  <Badge className={
                    consultation.consultation.status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : consultation.consultation.status === 'in_progress'
                      ? 'bg-purple-100 text-purple-800'
                      : 'bg-yellow-100 text-yellow-800'
                  }>
                    {consultation.consultation.status === 'completed' ? 'Completada' : 
                     consultation.consultation.status === 'in_progress' ? 'En Progreso' : 'Borrador'}
                  </Badge>
                  {consultation.appointment && (
                    <Badge variant="secondary">
                      {format(new Date(consultation.appointment.date), 'dd/MM/yyyy')}
                    </Badge>
                  )}
                </div>
                
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {/* Badges en desktop */}
            <div className="hidden lg:flex items-center gap-2">
              <Badge className={
                consultation.consultation.status === 'completed' 
                  ? 'bg-green-100 text-green-800' 
                  : consultation.consultation.status === 'in_progress'
                  ? 'bg-purple-100 text-purple-800'
                  : 'bg-yellow-100 text-yellow-800'
              }>
                {consultation.consultation.status === 'completed' ? 'Completada' : 
                 consultation.consultation.status === 'in_progress' ? 'En Progreso' : 'Borrador'}
              </Badge>
              {consultation.appointment && (
                <Badge variant="secondary">
                  {format(new Date(consultation.appointment.date), 'dd/MM/yyyy')}
                </Badge>
              )}
            </div>
            
          </div>
        </div>

        {/* Layout con Sidebar - Responsivo: móvil/tablet primero los cards, desktop al lado */}
        <div className="flex flex-col-reverse xl:grid xl:grid-cols-[1fr_320px] gap-6">
          {/* Contenido Principal - En móvil aparece segundo, en desktop a la izquierda */}
          <div className="space-y-4 order-2 xl:order-1">
          {/* Tab Navigation con el mismo estilo que usuarios */}
          <div className="bg-white rounded-lg shadow-sm border">
            <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-300 ${
                      activeTab === tab.id
                        ? 'bg-[#3D4E80] text-white shadow-md'
                        : 'text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2] border border-[#ADB6CA]'
                    }`}
                  >
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-2 mb-1">
                        <Icon className="h-4 w-4" />
                        <span className="hidden sm:inline font-semibold">{tab.label}</span>
                      </div>
                      <div className="sm:hidden text-xs font-semibold">{tab.label}</div>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>

          {/* Contenido de los tabs - Organización Vertical según estándar */}
          <div className="space-y-4">
            {/* Tab 1: Información del Paciente */}
            {activeTab === 'patient' && (
              <div className="space-y-4">
                {/* Información del Paciente */}
                <Card className="border-[#3D4E80]/20 bg-[#3D4E80]/5 hover:shadow-lg transition-all duration-300">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-5 w-5 text-[#3D4E80]" />
                      Información del Paciente
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Nombre Completo</Label>
                        <p className="text-sm text-gray-900">
                          {consultation.patient.firstName} {consultation.patient.lastName}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Edad</Label>
                        <p className="text-sm text-gray-900">
                          {consultation.patient.age} años
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                        <p className="text-sm text-gray-900">
                          {consultation.patient.dateOfBirth && format(new Date(consultation.patient.dateOfBirth), 'dd/MM/yyyy')}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                        <p className="text-sm text-gray-900">
                          {consultation.patient.phone || 'No registrado'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Email</Label>
                        <p className="text-sm text-gray-900">
                          {consultation.patient.email}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Pre-checkin Summary */}
                <PreCheckinConsultationSummary 
                  preCheckinData={consultation.preCheckin} 
                  appointmentInfo={consultation.appointment}
                />

                {/* Información Demográfica y Social */}
                <Card className="border-[#ADB6CA]/40 bg-[#ADB6CA]/15 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Información Demográfica y Social"
                    icon={User}
                    sectionKey="demographics"
                    description="Datos sociales relevantes para el contexto médico del paciente"
                  />
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Religión */}
                      <div className="space-y-2">
                        <Label htmlFor="religion">Religión</Label>
                        {isSectionEditing('demographics') ? (
                          <Select
                            value={formData.demographics.religion}
                            onValueChange={(value) => {
                              updateFormData('demographics', 'religion', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione religión" />
                            </SelectTrigger>
                            <SelectContent>
                              {religions.map((religion) => (
                                <SelectItem key={religion.id} value={religion.id}>
                                  {religion.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-gray-900">
                            {(() => {
                              const selectedReligion = religions.find(r => r.id === formData.demographics.religion);
                              return selectedReligion?.name || 'No especificado';
                            })()}
                          </p>
                        )}
                      </div>

                      {/* Ocupación */}
                      <div className="space-y-2">
                        <Label htmlFor="occupation">Ocupación</Label>
                        {isSectionEditing('demographics') ? (
                          <Select
                            value={formData.demographics.occupation}
                            onValueChange={(value) => {
                              updateFormData('demographics', 'occupation', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione ocupación" />
                            </SelectTrigger>
                            <SelectContent>
                              {occupations.map((occupation) => (
                                <SelectItem key={occupation.id} value={occupation.id.toString()}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{occupation.name}</span>
                                    {occupation.category && (
                                      <span className="text-xs text-gray-500">{occupation.category}</span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div>
                            {(() => {
                              const selectedOccupation = occupations.find(o => o.id.toString() === formData.demographics.occupation);
                              if (!selectedOccupation) return <p className="text-sm text-gray-900">No especificado</p>;
                              
                              return (
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{selectedOccupation.name}</p>
                                  {selectedOccupation.category && (
                                    <p className="text-xs text-gray-500">{selectedOccupation.category}</p>
                                  )}
                                </div>
                              );
                            })()}
                          </div>
                        )}
                      </div>

                      {/* Estado Civil */}
                      <div className="space-y-2">
                        <Label htmlFor="maritalStatus">Estado Civil</Label>
                        {isSectionEditing('demographics') ? (
                          <Select
                            value={formData.demographics.maritalStatus}
                            onValueChange={(value) => {
                              updateFormData('demographics', 'maritalStatus', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione estado civil" />
                            </SelectTrigger>
                            <SelectContent>
                              {maritalStatuses.map((status) => (
                                <SelectItem key={status.id} value={status.id}>
                                  {status.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-gray-900">
                            {(() => {
                              const selectedStatus = maritalStatuses.find(s => s.id === formData.demographics.maritalStatus);
                              return selectedStatus?.name || 'No especificado';
                            })()}
                          </p>
                        )}
                      </div>

                      {/* Nivel Educativo */}
                      <div className="space-y-2">
                        <Label htmlFor="educationLevel">Nivel Educativo</Label>
                        {isSectionEditing('demographics') ? (
                          <Select
                            value={formData.demographics.educationLevel}
                            onValueChange={(value) => {
                              updateFormData('demographics', 'educationLevel', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione nivel educativo" />
                            </SelectTrigger>
                            <SelectContent>
                              {educationLevels.map((level) => (
                                <SelectItem key={level.id} value={level.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{level.name}</span>
                                    {level.description && (
                                      <span className="text-xs text-gray-500">{level.description}</span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div>
                            {(() => {
                              const selectedLevel = educationLevels.find(l => l.id === formData.demographics.educationLevel);
                              if (!selectedLevel) return <p className="text-sm text-gray-900">No especificado</p>;
                              
                              return (
                                <div>
                                  <p className="text-sm font-medium text-gray-900">{selectedLevel.name}</p>
                                  {selectedLevel.description && (
                                    <p className="text-xs text-gray-500">{selectedLevel.description}</p>
                                  )}
                                </div>
                              );
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Información Administrativa */}
                <Card className="border-[#FCEEA8]/50 bg-[#FCEEA8]/20 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Información Administrativa"
                    icon={FileText}
                    sectionKey="administrative"
                    description="Datos administrativos y de referencia del paciente"
                  />
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                      {/* Fuente de Referencia */}
                      <div className="space-y-2">
                        <Label htmlFor="mediaSource">¿Cómo nos conoció?</Label>
                        {isSectionEditing('administrative') ? (
                          <Select
                            value={formData.administrative.mediaSource}
                            onValueChange={(value) => {
                              updateFormData('administrative', 'mediaSource', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione fuente" />
                            </SelectTrigger>
                            <SelectContent>
                              {mediaSources.map((source) => (
                                <SelectItem key={source.id} value={source.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{source.name}</span>
                                    <span className="text-xs text-gray-500">{source.category}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <p className="text-sm text-gray-900">
                            {(() => {
                              const selectedSource = mediaSources.find(s => s.id === formData.administrative.mediaSource);
                              return selectedSource?.name || 'No especificado';
                            })()}
                          </p>
                        )}
                      </div>

                      {/* Empresa/Seguro */}
                      <div className="space-y-2">
                        <Label htmlFor="company">Empresa/Seguro</Label>
                        {isSectionEditing('administrative') ? (
                          <Select
                            value={formData.administrative.company}
                            onValueChange={(value) => {
                              updateFormData('administrative', 'company', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione empresa" />
                            </SelectTrigger>
                            <SelectContent>
                              {companies.map((company) => (
                                <SelectItem key={company.id} value={company.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{company.commercialName}</span>
                                    <span className="text-xs text-gray-500">NIT: {company.nit}</span>
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div>
                            {(() => {
                              const selectedCompany = companies.find(c => c.id === formData.administrative.company);
                              if (!selectedCompany) return <p className="text-sm text-gray-700">No especificado</p>;
                              
                              return (
                                <div>
                                  <p className="text-sm font-medium text-gray-700">{selectedCompany.commercialName}</p>
                                  <p className="text-xs text-gray-500">NIT: {selectedCompany.nit}</p>
                                </div>
                              );
                            })()}
                          </div>
                        )}
                      </div>

                      {/* Consultorio Preferido */}
                      <div className="space-y-2">
                        <Label htmlFor="preferredConsultory">Consultorio Preferido</Label>
                        {isSectionEditing('administrative') ? (
                          <Select
                            value={formData.administrative.preferredConsultory}
                            onValueChange={(value) => {
                              updateFormData('administrative', 'preferredConsultory', value);
                              triggerAutoSave();
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Seleccione consultorio" />
                            </SelectTrigger>
                            <SelectContent>
                              {consultories.map((consultory) => (
                                <SelectItem key={consultory.id} value={consultory.id}>
                                  <div className="flex flex-col">
                                    <span className="font-medium">{consultory.name}</span>
                                    {consultory.specialty && (
                                      <span className="text-xs text-gray-500">{consultory.specialty}</span>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div>
                            {(() => {
                              const selectedConsultory = consultories.find(c => c.id === formData.administrative.preferredConsultory);
                              if (!selectedConsultory) return <p className="text-sm text-gray-700">No especificado</p>;
                              
                              return (
                                <div>
                                  <p className="text-sm font-medium text-gray-700">{selectedConsultory.name}</p>
                                  {selectedConsultory.specialty && (
                                    <p className="text-xs text-gray-500">{selectedConsultory.specialty}</p>
                                  )}
                                </div>
                              );
                            })()}
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            {activeTab === 'medical' && (
              <div className="space-y-4">
                {/* Servicios de la Consulta */}
                <Card className="border-[#50bed2]/30 bg-[#50bed2]/10 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Servicios Médicos"
                    icon={Stethoscope}
                    sectionKey="services"
                    description="Servicios realizados en esta consulta"
                  />
                  <CardContent>
                    <div className="space-y-4">
                      {/* Lista de servicios actuales - Solo en modo visualización */}
                      {!isSectionEditing('services') && (
                        <div className="space-y-2">
                          {Array.isArray(formData.services) && formData.services.length > 0 ? (
                            formData.services.map((service: any, index: number) => {
                              const serviceDetails = medicalServices.find((s: any) => s.id === service.serviceId);
                              return (
                                <div key={index} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                                  <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center gap-2">
                                      <p className="font-medium text-blue-900">
                                        {service.serviceName || serviceDetails?.name || 'Servicio'}
                                      </p>
                                      {index === 0 && consultation.appointment && (
                                        <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700">
                                          Heredado de la cita
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  {serviceDetails?.description && (
                                    <p className="text-sm text-blue-700 mb-2">{serviceDetails.description}</p>
                                  )}
                                  <div className="flex items-center gap-4 text-xs text-blue-600">
                                    <span>Categoría: {service.category || serviceDetails?.category}</span>
                                    {service.duration && <span>Duración: {service.duration} min</span>}
                                    {service.price && <span>Precio: Q. {service.price}</span>}
                                  </div>
                                  {service.notes && (
                                    <p className="text-xs text-blue-600 mt-1">Notas: {service.notes}</p>
                                  )}
                                </div>
                              );
                            })
                          ) : consultation.appointment ? (
                            <div className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                              <p className="text-sm text-orange-700">
                                Los servicios se determinarán automáticamente desde la información de la cita.
                              </p>
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500">No hay servicios registrados</p>
                          )}
                        </div>
                      )}

                      {/* Selector para agregar servicios adicionales */}
                      {isSectionEditing('services') && (
                        <div className="space-y-3">
                          <div className="flex items-center gap-2">
                            <h4 className="text-sm font-medium text-gray-700">Agregar servicio adicional</h4>
                            <Badge variant="outline" className="text-xs">
                              Opcional
                            </Badge>
                          </div>
                          {/* Lista de servicios individuales */}
                          {Array.isArray(formData.services) && formData.services.length > 0 ? (
                            <div className="space-y-4">
                              {formData.services.map((service, index) => (
                                <div key={index} className="p-4 border rounded-lg space-y-4">
                                  <div className="flex items-center justify-between">
                                    <h4 className="font-medium">Servicio {index + 1}</h4>
                                    {isSectionEditing('services') && (
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => setFormData(prev => ({
                                          ...prev,
                                          services: prev.services?.filter((_, i) => i !== index) || []
                                        }))}
                                      >
                                        <Trash2 className="h-4 w-4 text-red-500" />
                                      </Button>
                                    )}
                                  </div>
                                  
                                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2 md:col-span-2">
                                      <Label>Servicio</Label>
                                      {isSectionEditing('services') ? (
                                        <ServiceSelector
                                          services={medicalServices.map(s => ({
                                            id: s.id,
                                            name: s.name,
                                            description: s.description,
                                            basePrice: s.basePrice,
                                            currency: s.currency || 'Q.',
                                            category: s.category
                                          }))}
                                          value={service.serviceId}
                                          autoOpen={!service.serviceId && service.addedAt && 
                                            new Date().getTime() - new Date(service.addedAt).getTime() < 1000}
                                          onChange={(value) => {
                                            const selectedService = medicalServices.find(s => s.id === value);
                                            if (selectedService) {
                                              const updatedServices = [...(formData.services || [])];
                                              updatedServices[index] = {
                                                ...service,
                                                serviceId: selectedService.id,
                                                serviceName: selectedService.name,
                                                category: selectedService.category,
                                                price: selectedService.basePrice || 0,
                                                duration: selectedService.duration || 30,
                                                performedBy: currentDoctorName || 'Doctor'
                                              };
                                              setFormData(prev => ({ ...prev, services: updatedServices }));
                                              
                                              // Enfocar el campo de notas después de seleccionar el servicio
                                              focusServiceNotes(index);
                                            }
                                          }}
                                          placeholder="Buscar y seleccionar servicio médico..."
                                          className="w-full"
                                        />
                                      ) : (
                                        <div>
                                          <p className="text-sm font-medium text-gray-900">{service.serviceName}</p>
                                          <p className="text-xs text-gray-500">{service.category}</p>
                                        </div>
                                      )}
                                    </div>
                                    
                                    {/* Fila compacta: Precio, Duración y Realizado por */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 md:col-span-2">
                                      <div className="space-y-2">
                                        <Label>Precio</Label>
                                        {isSectionEditing('services') ? (
                                          <Input
                                            type="number"
                                            value={service.price || ''}
                                            onChange={(e) => {
                                              const updatedServices = [...(formData.services || [])];
                                              updatedServices[index] = {
                                                ...service,
                                                price: parseFloat(e.target.value) || 0
                                              };
                                              setFormData(prev => ({ ...prev, services: updatedServices }));
                                            }}
                                            placeholder="0.00"
                                            step="0.01"
                                          />
                                        ) : (
                                          <p className="text-sm text-gray-900">
                                            Q. {service.price?.toFixed(2) || '0.00'}
                                          </p>
                                        )}
                                      </div>
                                      
                                      <div className="space-y-2">
                                        <Label>Duración (min)</Label>
                                        {isSectionEditing('services') ? (
                                          <Input
                                            type="number"
                                            value={service.duration || ''}
                                            onChange={(e) => {
                                              const updatedServices = [...(formData.services || [])];
                                              updatedServices[index] = {
                                                ...service,
                                                duration: parseInt(e.target.value) || 0
                                              };
                                              setFormData(prev => ({ ...prev, services: updatedServices }));
                                            }}
                                            placeholder="30"
                                          />
                                        ) : (
                                          <p className="text-sm text-gray-900">
                                            {service.duration || 0} min
                                          </p>
                                        )}
                                      </div>
                                      
                                      <div className="space-y-2">
                                        <Label>Realizado por</Label>
                                        {isSectionEditing('services') ? (
                                          <Select
                                            value={service.performedBy || ''}
                                            onValueChange={(value) => {
                                              const updatedServices = [...(formData.services || [])];
                                              updatedServices[index] = {
                                                ...service,
                                                performedBy: value
                                              };
                                              setFormData(prev => ({ ...prev, services: updatedServices }));
                                            }}
                                          >
                                            <SelectTrigger>
                                              <SelectValue placeholder="Seleccionar doctor" />
                                            </SelectTrigger>
                                            <SelectContent>
                                              {availableDoctors.map((doctor) => (
                                                <SelectItem key={doctor.id} value={doctor.name}>
                                                  <div className="flex flex-col">
                                                    <span className="font-medium">{doctor.name}</span>
                                                    {doctor.specialty && (
                                                      <span className="text-xs text-gray-500">{doctor.specialty}</span>
                                                    )}
                                                  </div>
                                                </SelectItem>
                                              ))}
                                              {/* Opción para doctor actual por defecto */}
                                              {currentDoctorName && !availableDoctors.find(d => d.name === currentDoctorName) && (
                                                <SelectItem value={currentDoctorName}>
                                                  <div className="flex flex-col">
                                                    <span className="font-medium">{currentDoctorName}</span>
                                                    <span className="text-xs text-gray-500">Doctor actual</span>
                                                  </div>
                                                </SelectItem>
                                              )}
                                            </SelectContent>
                                          </Select>
                                        ) : (
                                          <p className="text-sm text-gray-900">
                                            {service.performedBy || 'No especificado'}
                                          </p>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <div className="space-y-2">
                                    <Label>Notas del servicio</Label>
                                    {isSectionEditing('services') ? (
                                      <Textarea
                                        ref={(el) => {
                                          serviceNotesRefs.current[index] = el;
                                        }}
                                        value={service.notes || ''}
                                        onChange={(e) => {
                                          const updatedServices = [...(formData.services || [])];
                                          updatedServices[index] = {
                                            ...service,
                                            notes: e.target.value
                                          };
                                          setFormData(prev => ({ ...prev, services: updatedServices }));
                                        }}
                                        placeholder="Observaciones adicionales del servicio..."
                                        className="min-h-16"
                                      />
                                    ) : (
                                      <p className="text-sm text-gray-900 whitespace-pre-wrap">
                                        {service.notes || 'Sin notas adicionales'}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          ) : (
                            <p className="text-sm text-gray-500 text-center py-8">
                              No se han agregado servicios adicionales a esta consulta
                            </p>
                          )}
                          
                          {/* Botón para agregar nuevo servicio */}
                          {isSectionEditing('services') && (
                            <Button
                              variant="outline"
                              onClick={() => {
                                const newService = {
                                  serviceId: '',
                                  serviceName: '',
                                  category: '',
                                  price: 0,
                                  duration: 30,
                                  performedBy: currentDoctorName || 'Doctor',
                                  addedAt: new Date().toISOString(),
                                  notes: ''
                                };
                                setFormData(prev => ({
                                  ...prev,
                                  services: [...(prev.services || []), newService]
                                }));
                              }}
                              className="w-full"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Agregar Servicio Adicional
                            </Button>
                          )}
                          
                          {/* Total de servicios - Solo en modo visualización */}
                          {!isSectionEditing('services') && Array.isArray(formData.services) && formData.services.length > 0 && (
                            <div className="p-3 bg-gray-50 rounded-lg border">
                              <div className="flex justify-between items-center">
                                <span className="text-sm font-medium">Total de servicios:</span>
                                <span className="text-sm">
                                  {formData.services.length} servicio{formData.services.length !== 1 ? 's' : ''}
                                </span>
                              </div>
                              {Array.isArray(formData.services) && formData.services.some(s => s.price) && (
                                <div className="flex justify-between items-center mt-1">
                                  <span className="text-sm font-medium">Total estimado:</span>
                                  <span className="text-sm font-semibold">
                                    Q. {(Array.isArray(formData.services) 
                                      ? formData.services.reduce((total, service) => total + (parseFloat(service.price) || 0), 0) 
                                      : 0
                                    ).toFixed(2)}
                                  </span>
                                </div>
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Enfermedad Actual */}
                <Card className="border-[#ea6cb0]/20 bg-[#ea6cb0]/5 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Enfermedad Actual"
                    icon={FileText}
                    sectionKey="currentIllness"
                    description="Describe la enfermedad actual y su evolución"
                  />
                  <CardContent>
                    {isSectionEditing('currentIllness') ? (
                      <Textarea
                        value={formData.currentIllness}
                        onChange={(e) => {
                          setFormData(prev => ({ ...prev, currentIllness: e.target.value }));
                          triggerAutoSave();
                        }}
                        placeholder="Describe la enfermedad actual, síntomas, evolución, factores agravantes o atenuantes..."
                        className="min-h-32"
                      />
                    ) : (
                      <p className="text-sm text-gray-900 whitespace-pre-wrap">
                        {formData.currentIllness || 'No registrado'}
                      </p>
                    )}
                  </CardContent>
                </Card>

                {/* Síntomas */}
                <Card className="border-[#FAC9D1]/60 bg-[#FAC9D1]/20 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Síntomas Presentes"
                    icon={AlertTriangle}
                    sectionKey="symptoms"
                    description="Seleccione los síntomas que presenta el paciente"
                  />
                  <CardContent>
                    {isSectionEditing('symptoms') && (
                      <MultiSelect
                        options={symptoms.map(symptom => ({
                          value: symptom.id,
                          label: symptom.name,
                          category: symptom.category,
                          color: symptom.severity === 'high' ? 'red' : 
                                 symptom.severity === 'medium' ? 'yellow' : 'green'
                        }))}
                        value={formData.selectedSymptoms}
                        onChange={(value) => {
                          setFormData(prev => ({ ...prev, selectedSymptoms: value }));
                          triggerAutoSave();
                        }}
                        placeholder="Seleccionar síntomas..."
                        emptyText="No se encontraron síntomas."
                      />
                    )}
                    
                    {/* Vista de solo lectura - Solo cuando NO está editando */}
                    {(
                      <div className="space-y-2">
                        {formData.selectedSymptoms.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {formData.selectedSymptoms.map((symptomId) => {
                              const symptom = symptoms.find(s => s.id === symptomId);
                              if (!symptom) return null;
                              return (
                                <Badge
                                  key={symptomId}
                                  variant="outline"
                                  className={`text-xs ${
                                    symptom.severity === 'high' ? 'bg-red-100 text-red-800 border-red-200' :
                                    symptom.severity === 'medium' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                                    'bg-green-100 text-green-800 border-green-200'
                                  }`}
                                >
                                  {symptom.name}
                                </Badge>
                              );
                            })}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">No se han registrado síntomas</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Antecedentes Patológicos */}
                <Card className="border-[#3D4E80]/20 bg-[#3D4E80]/5 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Antecedentes Patológicos"
                    icon={Heart}
                    sectionKey="pathologicalHistory"
                    description="Historial médico patológico del paciente"
                  />
                  <CardContent>
                    {isSectionEditing('pathologicalHistory') && (
                      <MultiSelect
                        options={pathologicalHistory.map(item => ({
                          value: item.id,
                          label: item.name,
                          category: item.category,
                          color: item.severity === 'high' ? 'red' : 
                                 item.severity === 'moderate' ? 'yellow' : 'green'
                        }))}
                        value={formData.pathologicalHistory}
                        onChange={(value) => {
                          setFormData(prev => ({ ...prev, pathologicalHistory: value }));
                          triggerAutoSave();
                        }}
                        placeholder="Seleccionar antecedentes patológicos..."
                        emptyText="No se encontraron antecedentes patológicos."
                      />
                    )}
                    
                    {/* Vista de solo lectura - Solo cuando NO está editando */}
                    {(
                      <div className="space-y-2">
                        {formData.pathologicalHistory.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {formData.pathologicalHistory.map((historyId) => {
                              const historyItem = pathologicalHistory.find(h => h.id === historyId);
                              if (!historyItem) return null;
                              return (
                                <Badge
                                  key={historyId}
                                  variant="outline"
                                  className={`text-xs ${
                                    historyItem.severity === 'high' ? 'bg-red-100 text-red-800 border-red-200' :
                                    historyItem.severity === 'moderate' ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                                    'bg-green-100 text-green-800 border-green-200'
                                  }`}
                                >
                                  {historyItem.name}
                                  {historyItem.isHereditary && ' (Hereditario)'}
                                </Badge>
                              );
                            })}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">Sin antecedentes patológicos registrados</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Antecedentes No Patológicos */}
                <Card className="border-[#ADB6CA]/40 bg-[#ADB6CA]/15 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Antecedentes No Patológicos"
                    icon={Activity}
                    sectionKey="nonPathologicalHistory"
                    description="Estilo de vida y hábitos del paciente"
                  />
                  <CardContent>
                    {isSectionEditing('nonPathologicalHistory') && (
                      <MultiSelect
                        options={nonPathologicalHistory.map(item => ({
                          value: item.id,
                          label: item.name,
                          category: item.category,
                          color: item.isPositive ? 'green' : 'orange'
                        }))}
                        value={formData.nonPathologicalHistory}
                        onChange={(value) => {
                          setFormData(prev => ({ ...prev, nonPathologicalHistory: value }));
                          triggerAutoSave();
                        }}
                        placeholder="Seleccionar antecedentes no patológicos..."
                        emptyText="No se encontraron antecedentes no patológicos."
                      />
                    )}
                    
                    {/* Vista de solo lectura - Solo cuando NO está editando */}
                    {(
                      <div className="space-y-2">
                        {formData.nonPathologicalHistory.length > 0 ? (
                          <div className="flex flex-wrap gap-2">
                            {formData.nonPathologicalHistory.map((historyId) => {
                              const historyItem = nonPathologicalHistory.find(h => h.id === historyId);
                              if (!historyItem) return null;
                              return (
                                <Badge
                                  key={historyId}
                                  variant="outline"
                                  className={`text-xs ${
                                    historyItem.isPositive ? 'bg-green-100 text-green-800 border-green-200' :
                                    'bg-orange-100 text-orange-800 border-orange-200'
                                  }`}
                                >
                                  {historyItem.name}
                                </Badge>
                              );
                            })}
                          </div>
                        ) : (
                          <p className="text-sm text-gray-500">Sin antecedentes no patológicos registrados</p>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Alergias */}
                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                  <SectionHeader 
                    title="Alergias"
                    icon={AlertCircle}
                    sectionKey="allergies"
                    description="Alergias conocidas del paciente (crítico para seguridad)"
                  />
                  <CardContent>
                    {isSectionEditing('allergies') ? (
                      <div className="space-y-4">
                        {/* Lista de alergias actuales */}
                        {(() => {
                          console.log('🔍 RENDER: Current allergies count:', formData.allergies?.length || 0);
                          return Array.isArray(formData.allergies) && formData.allergies.length > 0;
                        })() ? (
                          <div className="space-y-2">
                            <Label className="text-sm font-medium text-gray-700">Alergias registradas</Label>
                            <div className="grid grid-cols-1 gap-2">
                              {formData.allergies.map((allergy: any, index: number) => (
                                <div key={`allergy-${index}-${allergy.id || `new-${index}`}`} className="p-4 bg-red-50 border border-red-200 rounded-lg" data-allergy-index={index}>
                                  {/* Formulario de edición de alergia */}
                                  <div className="space-y-3">
                                    <div className="flex items-center justify-between mb-2">
                                      <Label className="text-sm font-medium text-red-800">
                                        {allergy.allergen ? `Alergia: ${allergy.allergen}` : 'Nueva Alergia'}
                                      </Label>
                                      <Button
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          const updatedAllergies = formData.allergies.filter((_: any, i: number) => i !== index);
                                          setFormData({ ...formData, allergies: updatedAllergies });
                                        }}
                                        className="text-red-600 hover:text-red-700 hover:bg-red-100"
                                      >
                                        <X className="h-4 w-4" />
                                      </Button>
                                    </div>
                                      
                                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                        {/* Alérgeno */}
                                        <div>
                                          <Label className="text-xs text-red-700">Alérgeno *</Label>
                                          <Input
                                            placeholder="Ej: Penicilina, Maní, Polen..."
                                            value={allergy.allergen || ''}
                                            onChange={(e) => {
                                              console.log('🔍 Updating allergy at index:', index, 'with value:', e.target.value);
                                              const currentAllergies = Array.isArray(formData.allergies) ? formData.allergies : [];
                                              const updatedAllergies = [...currentAllergies];
                                              
                                              // Asegurar que el índice existe
                                              if (updatedAllergies[index]) {
                                                updatedAllergies[index] = { 
                                                  ...updatedAllergies[index], 
                                                  allergen: e.target.value 
                                                };
                                              }
                                              
                                              console.log('🔍 Updated allergies count:', updatedAllergies.length);
                                              console.log('🔍 Updated allergies details:', updatedAllergies.map(a => ({ id: a.id, allergen: a.allergen })));
                                              setFormData(prev => ({ 
                                                ...prev, 
                                                allergies: updatedAllergies 
                                              }));
                                              triggerAutoSave();
                                            }}
                                            className="text-sm"
                                          />
                                        </div>
                                        
                                        {/* Tipo */}
                                        <div>
                                          <Label className="text-xs text-red-700">Tipo</Label>
                                          <Select 
                                            value={allergy.type || 'medication'} 
                                            onValueChange={(value) => {
                                              const updatedAllergies = [...formData.allergies];
                                              updatedAllergies[index] = { ...allergy, type: value };
                                              setFormData({ ...formData, allergies: updatedAllergies });
                                              triggerAutoSave();
                                            }}
                                          >
                                            <SelectTrigger className="text-sm">
                                              <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="medication">Medicamento</SelectItem>
                                              <SelectItem value="food">Alimento</SelectItem>
                                              <SelectItem value="environmental">Ambiental</SelectItem>
                                              <SelectItem value="other">Otro</SelectItem>
                                            </SelectContent>
                                          </Select>
                                        </div>
                                        
                                        {/* Severidad */}
                                        <div>
                                          <Label className="text-xs text-red-700">Severidad</Label>
                                          <Select 
                                            value={allergy.severity || 'mild'} 
                                            onValueChange={(value) => {
                                              const updatedAllergies = [...formData.allergies];
                                              updatedAllergies[index] = { ...allergy, severity: value };
                                              setFormData({ ...formData, allergies: updatedAllergies });
                                              triggerAutoSave();
                                            }}
                                          >
                                            <SelectTrigger className="text-sm">
                                              <SelectValue />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem value="mild">Leve</SelectItem>
                                              <SelectItem value="moderate">Moderada</SelectItem>
                                              <SelectItem value="severe">Severa</SelectItem>
                                              <SelectItem value="anaphylaxis">Anafilaxia</SelectItem>
                                            </SelectContent>
                                          </Select>
                                        </div>
                                        
                                        {/* Reacción */}
                                        <div>
                                          <Label className="text-xs text-red-700">Reacción</Label>
                                          <Input
                                            placeholder="Ej: Ronchas, dificultad respiratoria..."
                                            value={allergy.reaction || ''}
                                            onChange={(e) => {
                                              const updatedAllergies = [...formData.allergies];
                                              updatedAllergies[index] = { ...allergy, reaction: e.target.value };
                                              setFormData({ ...formData, allergies: updatedAllergies });
                                              triggerAutoSave();
                                            }}
                                            className="text-sm"
                                          />
                                        </div>
                                      </div>
                                      
                                      {/* Notas adicionales */}
                                      <div>
                                        <Label className="text-xs text-red-700">Notas adicionales</Label>
                                        <Textarea
                                          placeholder="Información adicional sobre la alergia..."
                                          value={allergy.notes || ''}
                                          onChange={(e) => {
                                            const updatedAllergies = [...formData.allergies];
                                            updatedAllergies[index] = { ...allergy, notes: e.target.value };
                                            setFormData({ ...formData, allergies: updatedAllergies });
                                            triggerAutoSave();
                                          }}
                                          className="text-sm min-h-16"
                                        />
                                      </div>
                                    </div>
                                  </div>
                              ))}
                            </div>
                          </div>
                        ) : (
                          <Alert className="border-yellow-200 bg-yellow-50">
                            <AlertTriangle className="h-4 w-4 text-yellow-600" />
                            <AlertDescription className="text-yellow-800">
                              No se han registrado alergias. Es importante confirmar si el paciente no tiene alergias conocidas.
                            </AlertDescription>
                          </Alert>
                        )}
                        
                        {/* Agregar nueva alergia */}
                        <div className="border-t pt-4">
                          <Button
                            variant="outline"
                            onClick={() => {
                              console.log('🔍 ADDING NEW ALLERGY - Current allergies count:', formData.allergies?.length || 0);
                              const newAllergy = {
                                id: `allergy-${Date.now()}-${Math.random()}`,
                                allergen: '',
                                type: 'medication',
                                severity: 'mild',
                                reaction: '',
                                notes: ''
                              };
                              const updatedFormData = {
                                ...formData,
                                allergies: [...(formData.allergies || []), newAllergy]
                              };
                              console.log('🔍 NEW ALLERGY ADDED - New allergies count:', updatedFormData.allergies.length);
                              setFormData(updatedFormData);
                              
                              // Auto-focus en el campo de alérgeno de la nueva alergia
                              setTimeout(() => {
                                const allergyIndex = updatedFormData.allergies.length - 1;
                                const allergenInput = document.querySelector(`[data-allergy-index="${allergyIndex}"] input[placeholder*="Penicilina"]`);
                                if (allergenInput) {
                                  (allergenInput as HTMLInputElement).focus();
                                }
                              }, 100);
                            }}
                            className="w-full border-dashed border-red-300 text-red-600 hover:bg-red-50"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Agregar Nueva Alergia
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <div>
                        {Array.isArray(formData.allergies) && formData.allergies.length > 0 ? (
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                            {formData.allergies.map((allergy: any, index: number) => (
                              <div key={index} className="p-3 bg-red-50 border border-red-200 rounded-lg">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="font-medium text-red-900">{allergy.allergen}</span>
                                  <Badge variant="destructive" className="text-xs">
                                    {allergy.severity === 'anaphylaxis' ? 'Anafilaxia' :
                                     allergy.severity === 'severe' ? 'Severa' :
                                     allergy.severity === 'moderate' ? 'Moderada' : 'Leve'}
                                  </Badge>
                                </div>
                                <div className="text-sm text-red-700">
                                  Tipo: {allergy.type === 'medication' ? 'Medicamento' :
                                        allergy.type === 'food' ? 'Alimento' :
                                        allergy.type === 'environmental' ? 'Ambiental' : 'Otro'}
                                </div>
                                {allergy.reaction && (
                                  <div className="text-sm text-red-600">Reacción: {allergy.reaction}</div>
                                )}
                              </div>
                            ))}
                          </div>
                        ) : (
                          <Alert className="border-green-200 bg-green-50">
                            <AlertCircle className="h-4 w-4 text-green-600" />
                            <AlertDescription className="text-green-800">
                              Sin alergias conocidas registradas
                            </AlertDescription>
                          </Alert>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>
            )}

            {/* Tab 3: Diagnóstico y Tratamiento */}
            {activeTab === 'diagnosis' && (
              <div className="space-y-4">
                {/* Diagnósticos */}
                <Card className="border-[#ea6cb0]/30 bg-[#ea6cb0]/10 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Diagnósticos"
                    icon={ClipboardList}
                    sectionKey="diagnoses"
                    description="Diagnósticos establecidos para el paciente"
                  />
                  <CardContent>
                    <div className="space-y-4">
                      {formData.diagnoses.map((diagnosis: any, index: number) => (
                        <div key={index} className="p-4 border border-[#ea6cb0]/30 rounded-lg bg-white">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Código</Label>
                              <Input
                                value={diagnosis.code}
                                onChange={(e) => {
                                  const newDiagnoses = [...formData.diagnoses];
                                  newDiagnoses[index].code = e.target.value;
                                  setFormData({ ...formData, diagnoses: newDiagnoses });
                                  triggerAutoSave();
                                }}
                                placeholder="Ej: Z00.0"
                                disabled={!isSectionEditing('diagnoses')}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Tipo</Label>
                              <Select 
                                value={diagnosis.type} 
                                onValueChange={(value) => {
                                  const newDiagnoses = [...formData.diagnoses];
                                  newDiagnoses[index].type = value;
                                  setFormData({ ...formData, diagnoses: newDiagnoses });
                                  triggerAutoSave();
                                }}
                                disabled={!isSectionEditing('diagnoses')}
                              >
                                <SelectTrigger className="mt-1">
                                  <SelectValue placeholder="Seleccionar tipo" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="primary">Primario</SelectItem>
                                  <SelectItem value="secondary">Secundario</SelectItem>
                                  <SelectItem value="differential">Diferencial</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                          <div className="mt-4">
                            <Label className="text-sm font-medium text-gray-700">Descripción</Label>
                            <Textarea
                              value={diagnosis.description}
                              onChange={(e) => {
                                const newDiagnoses = [...formData.diagnoses];
                                newDiagnoses[index].description = e.target.value;
                                setFormData({ ...formData, diagnoses: newDiagnoses });
                                triggerAutoSave();
                              }}
                              placeholder="Descripción detallada del diagnóstico"
                              disabled={!isSectionEditing('diagnoses')}
                              className="mt-1"
                              rows={3}
                            />
                          </div>
                          {isSectionEditing('diagnoses') && formData.diagnoses.length > 1 && (
                            <div className="mt-4 flex justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  const newDiagnoses = formData.diagnoses.filter((_, i) => i !== index);
                                  setFormData({ ...formData, diagnoses: newDiagnoses });
                                }}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <X className="h-4 w-4 mr-1" />
                                Eliminar
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                      {isSectionEditing('diagnoses') && (
                        <Button
                          variant="outline"
                          onClick={() => {
                            setFormData({
                              ...formData,
                              diagnoses: [...formData.diagnoses, { code: '', description: '', type: 'secondary' }]
                            });
                          }}
                          className="w-full border-dashed border-[#ea6cb0] text-[#ea6cb0] hover:bg-[#ea6cb0]/10"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Agregar Diagnóstico
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Plan de Tratamiento */}
                <Card className="border-[#50bed2]/30 bg-[#50bed2]/10 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Plan de Tratamiento"
                    icon={FileText}
                    sectionKey="treatment"
                    description="Plan terapéutico para el paciente"
                  />
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Plan General</Label>
                        <Textarea
                          value={formData.treatment.plan}
                          onChange={(e) => {
                            setFormData({
                              ...formData,
                              treatment: { ...formData.treatment, plan: e.target.value }
                            });
                            triggerAutoSave();
                          }}
                          placeholder="Describe el plan general de tratamiento"
                          disabled={!isSectionEditing('treatment')}
                          className="mt-1"
                          rows={4}
                        />
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Dieta</Label>
                          <Textarea
                            value={formData.treatment.diet}
                            onChange={(e) => {
                              setFormData({
                                ...formData,
                                treatment: { ...formData.treatment, diet: e.target.value }
                              });
                              triggerAutoSave();
                            }}
                            placeholder="Recomendaciones dietéticas"
                            disabled={!isSectionEditing('treatment')}
                            className="mt-1"
                            rows={3}
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Actividad</Label>
                          <Textarea
                            value={formData.treatment.activity}
                            onChange={(e) => {
                              setFormData({
                                ...formData,
                                treatment: { ...formData.treatment, activity: e.target.value }
                              });
                              triggerAutoSave();
                            }}
                            placeholder="Recomendaciones de actividad física"
                            disabled={!isSectionEditing('treatment')}
                            className="mt-1"
                            rows={3}
                          />
                        </div>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Monitoreo</Label>
                        <Textarea
                          value={formData.treatment.monitoring}
                          onChange={(e) => {
                            setFormData({
                              ...formData,
                              treatment: { ...formData.treatment, monitoring: e.target.value }
                            });
                            triggerAutoSave();
                          }}
                          placeholder="Instrucciones de seguimiento y monitoreo"
                          disabled={!isSectionEditing('treatment')}
                          className="mt-1"
                          rows={3}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Prescripciones */}
                <Card className="border-[#FCEEA8]/50 bg-[#FCEEA8]/20 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Prescripciones"
                    icon={Pill}
                    sectionKey="prescriptions"
                    description="Medicamentos prescritos al paciente"
                  />
                  <CardContent>
                    <div className="space-y-4">
                      {/* Lista de prescripciones en modo visualización */}
                      {!isSectionEditing('prescriptions') && (
                        <div className="space-y-2">
                          {Array.isArray(formData.prescriptions) && formData.prescriptions.length > 0 ? (
                            formData.prescriptions.map((prescription: any, index: number) => {
                              const medicationDetails = medications.find((m: any) => m.id === prescription.medication);
                              return (
                                <div key={index} className="p-3 bg-[#FCEEA8]/30 rounded-lg border border-[#F8E59A]">
                                  <div className="flex items-center justify-between mb-2">
                                    <div className="flex items-center gap-2">
                                      <p className="font-medium text-[#3D4E80]">
                                        {medicationDetails?.name || 'Medicamento no especificado'}
                                      </p>
                                      {prescription.dose && (
                                        <Badge variant="outline" className="text-xs bg-white text-[#3D4E80] border-[#F8E59A]">
                                          {prescription.dose}
                                        </Badge>
                                      )}
                                    </div>
                                  </div>
                                  {medicationDetails?.description && (
                                    <p className="text-sm text-[#3D4E80]/70 mb-2">{medicationDetails.description}</p>
                                  )}
                                  <div className="flex items-center gap-4 text-xs text-[#3D4E80]/80">
                                    {prescription.frequency && <span>Frecuencia: {prescription.frequency}</span>}
                                    {prescription.duration && <span>Duración: {prescription.duration}</span>}
                                  </div>
                                  {prescription.instructions && (
                                    <p className="text-xs text-[#3D4E80]/70 mt-1 italic">Instrucciones: {prescription.instructions}</p>
                                  )}
                                </div>
                              );
                            })
                          ) : (
                            <p className="text-sm text-gray-500">No hay medicamentos prescritos</p>
                          )}
                        </div>
                      )}

                      {/* Formulario de edición de prescripciones */}
                      {isSectionEditing('prescriptions') && formData.prescriptions.map((prescription: any, index: number) => (
                        <div key={index} className="p-4 border border-[#FCEEA8]/50 rounded-lg bg-white">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Medicamento</Label>
                              <MedicationSelector
                                value={prescription.medication}
                                onChange={(medicationId) => {
                                  const newPrescriptions = [...formData.prescriptions];
                                  newPrescriptions[index].medication = medicationId;
                                  setFormData({ ...formData, prescriptions: newPrescriptions });
                                  triggerAutoSave();
                                }}
                                medications={medications}
                                disabled={!isSectionEditing('prescriptions')}
                                placeholder="Seleccionar medicamento"
                                className="mt-1"
                                autoOpen={!prescription.medication && prescription.addedAt && 
                                  (new Date().getTime() - new Date(prescription.addedAt).getTime()) < 1000}
                              />
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Dosis</Label>
                              <Input
                                value={prescription.dose}
                                onChange={(e) => {
                                  const newPrescriptions = [...formData.prescriptions];
                                  newPrescriptions[index].dose = e.target.value;
                                  setFormData({ ...formData, prescriptions: newPrescriptions });
                                  triggerAutoSave();
                                }}
                                placeholder="Ej: 500mg"
                                disabled={!isSectionEditing('prescriptions')}
                                className="mt-1"
                              />
                            </div>
                          </div>
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Frecuencia</Label>
                              <Input
                                value={prescription.frequency}
                                onChange={(e) => {
                                  const newPrescriptions = [...formData.prescriptions];
                                  newPrescriptions[index].frequency = e.target.value;
                                  setFormData({ ...formData, prescriptions: newPrescriptions });
                                  triggerAutoSave();
                                }}
                                placeholder="Ej: Cada 8 horas"
                                disabled={!isSectionEditing('prescriptions')}
                                className="mt-1"
                              />
                            </div>
                            <div>
                              <Label className="text-sm font-medium text-gray-700">Duración</Label>
                              <Input
                                value={prescription.duration}
                                onChange={(e) => {
                                  const newPrescriptions = [...formData.prescriptions];
                                  newPrescriptions[index].duration = e.target.value;
                                  setFormData({ ...formData, prescriptions: newPrescriptions });
                                  triggerAutoSave();
                                }}
                                placeholder="Ej: 7 días"
                                disabled={!isSectionEditing('prescriptions')}
                                className="mt-1"
                              />
                            </div>
                          </div>
                          <div className="mt-4">
                            <Label className="text-sm font-medium text-gray-700">Instrucciones</Label>
                            <Textarea
                              value={prescription.instructions}
                              onChange={(e) => {
                                const newPrescriptions = [...formData.prescriptions];
                                newPrescriptions[index].instructions = e.target.value;
                                setFormData({ ...formData, prescriptions: newPrescriptions });
                                triggerAutoSave();
                              }}
                              placeholder="Instrucciones especiales para el paciente"
                              disabled={!isSectionEditing('prescriptions')}
                              className="mt-1"
                              rows={2}
                            />
                          </div>
                          {isSectionEditing('prescriptions') && (
                            <div className="mt-4 flex justify-end">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => removePrescription(index)}
                                className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              >
                                <X className="h-4 w-4 mr-1" />
                                Eliminar
                              </Button>
                            </div>
                          )}
                        </div>
                      ))}
                      
                      {isSectionEditing('prescriptions') && (
                        <Button
                          variant="outline"
                          onClick={addPrescription}
                          className="w-full border-dashed border-[#FCEEA8] text-[#3D4E80] hover:bg-[#FCEEA8]/30"
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Agregar Prescripción
                        </Button>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* Recomendaciones e Instrucciones */}
                <Card className="border-[#ADB6CA]/40 bg-[#ADB6CA]/15 hover:shadow-lg transition-all duration-300">
                  <SectionHeader 
                    title="Recomendaciones e Instrucciones"
                    icon={MessageSquare}
                    sectionKey="recommendations"
                    description="Instrucciones adicionales para el paciente"
                  />
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Recomendaciones Generales</Label>
                        <Textarea
                          value={formData.recommendations}
                          onChange={(e) => {
                            setFormData({ ...formData, recommendations: e.target.value });
                            triggerAutoSave();
                          }}
                          placeholder="Recomendaciones adicionales para el paciente"
                          disabled={!isSectionEditing('recommendations')}
                          className="mt-1"
                          rows={4}
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Instrucciones de Seguimiento</Label>
                        <Textarea
                          value={formData.followUpInstructions}
                          onChange={(e) => {
                            setFormData({ ...formData, followUpInstructions: e.target.value });
                            triggerAutoSave();
                          }}
                          placeholder="Cuándo regresar, señales de alarma, próxima cita"
                          disabled={!isSectionEditing('recommendations')}
                          className="mt-1"
                          rows={4}
                        />
                      </div>
                      <div className="space-y-3">
                        <Label className="text-sm font-medium text-gray-700">Próxima Cita</Label>
                        
                        {/* Selector estructurado */}
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                          <div>
                            <Label className="text-xs text-gray-600">Control en</Label>
                            <Input
                              type="number"
                              min="1"
                              value={formData.nextAppointment.value}
                              onChange={(e) => {
                                const value = parseInt(e.target.value) || 1;
                                const calculatedDate = calculateNextAppointmentDate(value, formData.nextAppointment.unit);
                                setFormData({ 
                                  ...formData, 
                                  nextAppointment: { 
                                    ...formData.nextAppointment, 
                                    value,
                                    calculatedDate: calculatedDate.toISOString()
                                  }
                                });
                                triggerAutoSave();
                              }}
                              disabled={!isSectionEditing('recommendations')}
                              className="mt-1"
                            />
                          </div>
                          
                          <div>
                            <Label className="text-xs text-gray-600">Unidad</Label>
                            <Select
                              value={formData.nextAppointment.unit}
                              onValueChange={(unit) => {
                                const calculatedDate = calculateNextAppointmentDate(formData.nextAppointment.value, unit);
                                setFormData({ 
                                  ...formData, 
                                  nextAppointment: { 
                                    ...formData.nextAppointment, 
                                    unit,
                                    calculatedDate: calculatedDate.toISOString()
                                  }
                                });
                                triggerAutoSave();
                              }}
                              disabled={!isSectionEditing('recommendations')}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="days">Día(s)</SelectItem>
                                <SelectItem value="weeks">Semana(s)</SelectItem>
                                <SelectItem value="months">Mes(es)</SelectItem>
                                <SelectItem value="years">Año(s)</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                          
                          <div>
                            <Label className="text-xs text-gray-600">Tipo</Label>
                            <Select
                              value={formData.nextAppointment.type}
                              onValueChange={(type) => {
                                setFormData({ 
                                  ...formData, 
                                  nextAppointment: { 
                                    ...formData.nextAppointment, 
                                    type
                                  }
                                });
                                triggerAutoSave();
                              }}
                              disabled={!isSectionEditing('recommendations')}
                            >
                              <SelectTrigger className="mt-1">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="control">Control rutinario</SelectItem>
                                <SelectItem value="follow_up">Seguimiento</SelectItem>
                                <SelectItem value="urgent">Urgente</SelectItem>
                                <SelectItem value="as_needed">Según necesidad</SelectItem>
                                <SelectItem value="discharged">Alta médica</SelectItem>
                              </SelectContent>
                            </Select>
                          </div>
                        </div>
                        
                        {/* Fecha calculada */}
                        {formData.nextAppointment.calculatedDate && (
                          <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-blue-600" />
                              <span className="text-sm font-medium text-blue-900">
                                Fecha estimada: {format(new Date(formData.nextAppointment.calculatedDate), "d 'de' MMMM 'de' yyyy", { locale: es })}
                              </span>
                            </div>
                          </div>
                        )}
                        
                        {/* Notas adicionales */}
                        <div>
                          <Label className="text-xs text-gray-600">Notas adicionales</Label>
                          <Textarea
                            value={formData.nextAppointment.notes}
                            onChange={(e) => {
                              setFormData({ 
                                ...formData, 
                                nextAppointment: { 
                                  ...formData.nextAppointment, 
                                  notes: e.target.value
                                }
                              });
                              triggerAutoSave();
                            }}
                            placeholder="Ej: Si persisten síntomas, evaluar medicación, etc."
                            disabled={!isSectionEditing('recommendations')}
                            className="mt-1"
                            rows={2}
                          />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Generar PDF de Prescripción */}
                {formData.prescriptions.length > 0 && (
                  <Card className="border-blue-200 bg-blue-50">
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <FileText className="h-6 w-6 text-blue-600" />
                          <div>
                            <h3 className="font-semibold text-blue-900">Prescription PDF</h3>
                            <p className="text-sm text-blue-700">
                              Generar receta médica en formato PDF
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={handleGeneratePrescriptionPDF}
                          disabled={!formData.prescriptions.length}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                        >
                          <Download className="h-4 w-4 mr-2" />
                          Descargar PDF
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            )}
          {/* Botones de acción - Simplificado */}
          <div className="flex justify-end gap-3 pt-4">
            <Button onClick={() => router.push('/dashboard/doctor/agenda')} variant="outline">
              Volver a Agenda
            </Button>
            {consultation.consultation.status !== 'completed' && (
              <Button 
                onClick={() => handleSave('completed')} 
                className="bg-[#ea6cb0] hover:bg-[#ea6cb0]/80 text-white"
                disabled={saving}
              >
                {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                Finalizar Consulta
              </Button>
            )}
          </div>

          {/* Advertencia de completar cita */}
          {consultation.consultation.status === 'completed' && (
            <Alert className="mt-4">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                Esta consulta ya ha sido completada. Si necesita hacer cambios, contacte al administrador.
              </AlertDescription>
            </Alert>
          )}
        </div>
          </div>

          {/* Sidebar con Cards Sticky - En móvil aparece primero, en desktop a la derecha */}
          <div className="space-y-4 order-1 xl:order-2">
            {/* Card de Total de Servicios */}
            <div className="xl:sticky xl:top-4 space-y-4">
              <Card className="border-[#50bed2]/30 bg-gradient-to-br from-[#50bed2]/5 to-[#50bed2]/10">
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg flex items-center gap-2 text-[#3D4E80]">
                    <Activity className="h-5 w-5 text-[#50bed2]" />
                    Total de Consulta
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-[#3D4E80]/70">Servicios:</span>
                      <span className="font-medium text-[#3D4E80]">
                        {formData.services?.length || 0}
                      </span>
                    </div>
                    <div className="pt-3 border-t border-[#50bed2]/20">
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-semibold text-[#3D4E80]">Total:</span>
                        <span className="text-2xl font-bold text-[#50bed2]">
                          {currency} {totalServices.toFixed(2)}
                        </span>
                      </div>
                    </div>
                    {formData.services && formData.services.length > 0 && (
                      <div className="pt-2 space-y-1">
                        <p className="text-xs text-[#3D4E80]/60 font-medium">Desglose:</p>
                        {formData.services.map((service: any, index: number) => (
                          <div key={index} className="flex justify-between text-xs">
                            <span className="text-[#3D4E80]/70 truncate max-w-[150px]">
                              {service.serviceName || 'Servicio'}
                            </span>
                            <span className="text-[#3D4E80]/90 font-medium">
                              {service.currency || currency} {parseFloat(service.price || 0).toFixed(2)}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Card de Resumen de Pre-checkin */}
              {consultation.preCheckin && (
                <Card className="border-[#FAC9D1]/60 bg-gradient-to-br from-[#FAC9D1]/20 to-[#FAC9D1]/30">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-lg flex items-center gap-2 text-[#3D4E80]">
                      <ClipboardList className="h-5 w-5 text-[#ea6cb0]" />
                      Pre-checkin Rápido
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-3 text-sm">
                      {/* Síntomas */}
                      {consultation.preCheckin.symptoms && (
                        <div className="pb-2 border-b border-[#ea6cb0]/20">
                          <p className="font-medium text-[#3D4E80] mb-1">Síntomas:</p>
                          <p className="text-[#3D4E80]/80 text-xs italic">
                            "{consultation.preCheckin.symptoms}"
                          </p>
                        </div>
                      )}
                      
                      {/* Medicamentos */}
                      {consultation.preCheckin.medications && (
                        <div className="pb-2 border-b border-[#ea6cb0]/20">
                          <p className="font-medium text-[#3D4E80] mb-1">Medicamentos:</p>
                          <p className="text-[#3D4E80]/80 text-xs italic">
                            "{consultation.preCheckin.medications}"
                          </p>
                        </div>
                      )}
                      
                      {/* Alergias */}
                      {consultation.preCheckin.allergies && (
                        <div className="p-2 bg-red-50 rounded-md border border-red-200">
                          <p className="font-medium text-red-800 mb-1 flex items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Alergias:
                          </p>
                          <p className="text-red-700 text-xs italic">
                            "{consultation.preCheckin.allergies}"
                          </p>
                        </div>
                      )}
                      
                      {/* Motivo de consulta */}
                      {consultation.preCheckin.chiefComplaint && (
                        <div>
                          <p className="font-medium text-[#3D4E80] mb-1">Motivo:</p>
                          <p className="text-[#3D4E80]/80 text-xs italic">
                            "{consultation.preCheckin.chiefComplaint}"
                          </p>
                        </div>
                      )}
                      
                      {/* Documentos */}
                      {consultation.preCheckin.documents && consultation.preCheckin.documents.length > 0 && (
                        <div className="pt-2 border-t border-[#ea6cb0]/20">
                          <Badge variant="secondary" className="text-xs bg-[#FCEEA8]/50">
                            📄 {consultation.preCheckin.documents.length} documento(s)
                          </Badge>
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Clinical History Summary */}
              {patientHistory.length > 0 && (
                <Card className="border-[#50bed2]/40 bg-[#50bed2]/10 hover:shadow-lg transition-all duration-300">
                  <CardHeader className="pb-3">
                    <CardTitle className="text-sm font-semibold text-[#3D4E80] flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-[#50bed2]" />
                      Historia Clínica Resumida
                    </CardTitle>
                    <CardDescription className="text-xs text-[#3D4E80]/70">
                      Últimas {patientHistory.length} consultas
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {patientHistory.map((consultation, index) => (
                      <div key={consultation.id} className="bg-white/60 rounded-lg p-3 space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs font-medium text-[#3D4E80]">
                            {format(new Date(consultation.date), 'dd/MM/yyyy', { locale: es })}
                          </span>
                          <Badge variant="outline" className="text-xs bg-[#50bed2]/10 text-[#50bed2] border-[#50bed2]/30">
                            Hace {consultation.daysSince} días
                          </Badge>
                        </div>
                        
                        <div className="space-y-1">
                          <div className="text-xs">
                            <span className="font-medium text-[#3D4E80]">Dx:</span>{' '}
                            <span className="text-[#3D4E80]/80">{consultation.diagnosis}</span>
                            {consultation.diagnosisCode && (
                              <span className="text-[#50bed2] ml-1">({consultation.diagnosisCode})</span>
                            )}
                          </div>
                          
                          {consultation.medication && (
                            <div className="text-xs">
                              <span className="font-medium text-[#3D4E80]">Rx:</span>{' '}
                              <span className="text-[#3D4E80]/80">
                                {consultation.medication.name}
                                {consultation.medication.dose && ` ${consultation.medication.dose}`}
                                {consultation.medication.frequency && ` - ${consultation.medication.frequency}`}
                              </span>
                            </div>
                          )}
                          
                          {(consultation.vitalSigns.weight || consultation.vitalSigns.bloodPressure || consultation.vitalSigns.temperature) && (
                            <div className="text-xs">
                              <span className="font-medium text-[#3D4E80]">SV:</span>{' '}
                              <span className="text-[#3D4E80]/80">
                                {consultation.vitalSigns.weight && `${consultation.vitalSigns.weight}kg `}
                                {consultation.vitalSigns.bloodPressure && `${consultation.vitalSigns.bloodPressure}mmHg `}
                                {consultation.vitalSigns.temperature && `${consultation.vitalSigns.temperature}°C`}
                              </span>
                            </div>
                          )}
                          
                          {consultation.alertNote && (
                            <div className="text-xs">
                              <span className="font-medium text-[#3D4E80]">Nota:</span>{' '}
                              <span className="text-[#3D4E80]/80">{consultation.alertNote}</span>
                            </div>
                          )}
                          
                          {consultation.nextAppointment && (
                            <div className="text-xs">
                              <span className="font-medium text-[#3D4E80]">Próx:</span>{' '}
                              <span className="text-[#3D4E80]/80">
                                {consultation.nextAppointment.value} {consultation.nextAppointment.unit} - {consultation.nextAppointment.type}
                              </span>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                    
                    {patientHistory.length === 3 && (
                      <div className="text-center pt-2">
                        <span className="text-xs text-[#3D4E80]/70">
                          Mostrando las últimas 3 consultas
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              )}
            </div>
          </div>
        </div>
      </div>
  );
}
