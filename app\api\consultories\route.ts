import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';

// Endpoint público para obtener el consultorio activo (para la web pública)
export async function GET() {
  try {
    // Buscar cualquier consultorio activo
    const activeConsultory = await db
      .select({
        id: consultories.id,
        name: consultories.name,
        phone: consultories.phone,
        email: consultories.email,
        address: consultories.address,
        logoUrl: consultories.logoUrl,
        logoPublicId: consultories.logoPublicId,
        regionalSettings: consultories.regionalSettings,
      })
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);

    if (!activeConsultory || activeConsultory.length === 0) {
      // Si no hay ninguno activo, buscar cualquier consultorio
      const anyConsultory = await db
        .select({
          id: consultories.id,
          name: consultories.name,
          phone: consultories.phone,
          email: consultories.email,
          address: consultories.address,
          logoUrl: consultories.logoUrl,
          logoPublicId: consultories.logoPublicId,
          regionalSettings: consultories.regionalSettings,
        })
        .from(consultories)
        .limit(1);

      if (!anyConsultory || anyConsultory.length === 0) {
        return NextResponse.json(
          { 
            success: false, 
            error: 'No hay consultorios disponibles' 
          },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: anyConsultory[0]
      });
    }

    return NextResponse.json({
      success: true,
      data: activeConsultory[0]
    });

  } catch (error) {
    console.error('Error fetching active consultory:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al obtener información del consultorio' 
      },
      { status: 500 }
    );
  }
}