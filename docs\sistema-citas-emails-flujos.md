# Sistema Unificado de Citas y Emails

## Estados de las Citas

| Estado | Descripción |
|--------|-------------|
| `scheduled` | Actividad en agenda sin paciente específico |
| `pending_confirmation` | Cita creada, esperando confirmación del paciente |
| `confirmed` | Paciente confirmó asistencia (explícita o implícitamente) |
| `checked_in` | Paciente llegó a la clínica (usando shortCode) |
| `in_progress` | Consulta iniciada |
| `completed` | Consulta terminada |
| `cancelled` | Cita cancelada |
| `no_show` | Paciente no llegó |

## Tipos de Emails

### 1. Email de Invitación 📧
- **Propósito**: Crear cuenta en el sistema
- **Destinatario**: 
  - Paciente mayor de edad (sin cuenta)
  - Guardián/Encargado (si paciente es menor)
- **Cuándo se envía**: Si el destinatario no tiene cuenta activa
- **Contenido**: Link de activación + bienvenida al sistema
- **Es opcional**: SÍ - Solo si no tienen cuenta

### 2. Email de Recordatorio Combinado ⏰📋❌
- **Propósito**: Recordar cita + Pre-checkin + Opción cancelar
- **Destinatario**: Mismo que email de invitación
- **Cuándo se envía**: 48h y 24h antes de la cita
- **Contenido**:
  - "Tu cita es en X horas"
  - "Completa tu información médica [LINK PRE-CHECKIN]"
  - "¿No puedes venir? [CANCELAR CITA]"
  - shortCode para check-in el día de la cita
- **Es obligatorio**: SÍ - Siempre se envía

## Flujos por Canal de Creación

### Flujo 1: VAPI (Agente de Voz) 📞

**Historia**: Paciente llama por teléfono, agente IA agenda la cita

**Proceso**:
1. **Verificación**: VAPI verifica si es paciente existente o nuevo
2. **Creación**: VAPI crea cita → Estado: `pending_confirmation`
3. **Confirmación**: Usuario va a `/confirmar` → Ingresa shortCode + email → Estado: `confirmed`
4. **Emails enviados AL CONFIRMAR**:
   - ✅ **Invitación** (solo si es paciente nuevo sin cuenta)
   - ✅ **Recordatorios programados** (48h y 24h antes)

**Características**:
- Puede ser paciente nuevo O existente
- Requiere confirmación manual con shortCode
- Emails se envían DESPUÉS de confirmar
- Detección automática: agente verifica si ya existe en el sistema

### Flujo 2: Paciente Backend 💻

**Historia**: Paciente con cuenta propia agenda desde su dashboard

**Proceso**:
1. **Creación**: Paciente crea su cita → Estado: `confirmed` (implícito)
2. **Emails enviados INMEDIATAMENTE**:
   - ❌ **Invitación** (ya tiene cuenta)
   - ✅ **Recordatorios programados** (48h y 24h antes)

**Características**:
- Paciente ya tiene cuenta
- No requiere confirmación adicional
- Emails se envían al crear la cita

### Flujo 3: Manual (Doctor/Asistente) 👩‍⚕️

**Historia**: Doctor/Asistente agenda cita porque paciente llamó directamente

**Proceso**:
1. **Creación**: Doctor/Asistente crea cita → Estado: `confirmed` (paciente ya habló directamente)
2. **Emails enviados INMEDIATAMENTE**:
   - ✅ **Invitación** (si paciente/guardián no tiene cuenta)
   - ✅ **Recordatorios programados** (48h y 24h antes)

**Características**:
- Puede ser paciente nuevo o existente
- No requiere confirmación adicional (ya se coordinó por teléfono)
- Emails se envían al crear la cita

## Lógica de Destinatarios de Emails

### Detección Automática de Destinatario

**Para cada cita, el sistema determina automáticamente:**

1. **¿Es menor de edad?** (fecha de nacimiento < 18 años)
   - **SÍ** → Buscar guardián/encargado → Enviar emails al guardián
   - **NO** → Enviar emails al paciente

2. **¿Tiene email válido?**
   - **SÍ** → Enviar emails
   - **NO** → NO enviar emails (log para seguimiento manual)

3. **¿Tiene cuenta activa?**
   - **SÍ** → NO enviar invitación
   - **NO** → Enviar invitación

### Casos Especiales

- **Menor sin guardián registrado**: NO se envían emails
- **Email temporal** (`@temp.local`): NO se envían emails  
- **Guardián con múltiples dependientes**: Emails mencionan al paciente específico

## ShortCode - Usos Múltiples

**El mismo shortCode sirve para**:
1. **Confirmar cita** (flujo VAPI en `/confirmar`)
2. **Check-in el día de la cita** (cuando llega a la clínica)
3. **Acceso rápido** a información de la cita

## Pre-checkin - Información Médica

**Propósito**: Recopilar información médica antes de la cita
**Cuándo**: En emails de recordatorio (48h y 24h antes)
**Contenido del formulario**:
- Síntomas actuales
- Medicamentos
- Alergias
- Antecedentes relevantes

**Realidad**: Es opcional - paciente puede llegar sin completarlo

## Cancelación de Citas

**Método**: Link en emails de recordatorio
**Proceso**:
1. Paciente hace clic en "Cancelar cita"
2. Sistema cambia estado a `cancelled`
3. Libera el horario para otros pacientes
4. Notifica al doctor/asistente

## Recordatorios Automáticos

**Programación**:
- **48 horas antes**: Email recordatorio + pre-checkin + cancelar
- **24 horas antes**: Email recordatorio + pre-checkin + cancelar

**Contenido incluye**:
- Información de la cita (fecha, hora, doctor)
- Link al pre-checkin médico
- shortCode para check-in
- Botón para cancelar cita

## Casos Edge y Excepciones

### Paciente llega sin confirmar
- **Estado actual**: `pending_confirmation`
- **Acción**: Doctor/Asistente puede hacer check-in manual
- **Resultado**: Estado cambia a `checked_in`

### Paciente no completa pre-checkin
- **Realidad**: Es común, no bloquea la consulta
- **Acción**: Se completa durante la consulta

### Emails fallan al enviarse
- **Log**: Sistema registra intentos fallidos
- **Seguimiento**: Staff puede hacer seguimiento manual

### Múltiples guardianes
- **Lógica**: Enviar al guardián principal
- **Fallback**: Si no hay principal, enviar al primero registrado

## Flujo Técnico VAPI - 5 Endpoints

### Conversación Completa del Agente

1. **Recopilar datos básicos**: Nombre, teléfono, motivo consulta
2. **Verificar paciente**: `POST /api/vapi/verificar-paciente`
   - Si existe: "Hola Juan Carlos, veo que ya es nuestro paciente"
   - Si nuevo: "Procederemos a crear su perfil" + pedir edad + guardián si menor
3. **Mostrar horarios**: `GET /api/vapi/horarios-disponibles` → 3 opciones
4. **Crear cita**: `POST /api/vapi/crear-cita` → shortCode + mensaje fonético
5. **Si reagenda**: `DELETE /api/vapi/cancelar-cita` + repetir proceso

### Endpoints VAPI

| Endpoint | Propósito | Cuándo se usa |
|----------|-----------|---------------|
| `POST /verificar-paciente` | ¿Es nuevo o existente? | Al inicio de conversación |
| `GET /horarios-disponibles` | 3 opciones de horarios | Después de verificar paciente |
| `POST /crear-cita` | Crear cita + shortCode | Cuando paciente elige horario |
| `DELETE /cancelar-cita` | Cancelar para reagendar | Si paciente quiere cambiar |
| `GET /estado-cita` | Verificar estado | Debugging/seguimiento |

**Documentación técnica completa**: Ver `/docs/vapi-endpoints-specification.md`

## Integraciones Futuras

### WhatsApp Bot 📱
- **Flujo**: Similar a VAPI
- **Estado inicial**: `pending_confirmation`
- **Confirmación**: Via WhatsApp + shortCode
- **Emails**: Al confirmar (invitación + recordatorios)

### Web Pública 🌐
- **Flujo**: Similar a Backend Usuario
- **Estado inicial**: `confirmed`
- **Emails**: Inmediatos (invitación si nuevo + recordatorios)

## Notas Técnicas

### Rate Limiting
- **Delay entre emails**: 90 segundos mínimo
- **Evita**: Bloqueos por spam en proveedores

### Base de Datos
- **Campo shortCode**: Único por cita
- **Campo emailsSent**: Track de emails enviados
- **Tabla appointment_logs**: Historial completo de eventos

### Seguridad
- **Links de cancelación**: Tokens únicos y temporales
- **ShortCodes**: No predicibles, generados aleatoriamente
- **Acceso a pre-checkin**: Requiere token válido

## Manejo de Fechas y Timezones 🕒

### Problema Resuelto: Fechas Incorrectas en Emails

**Historia del problema:**
- Emails mostraban fechas incorrectas (1 día antes)
- `createdAt` y `updatedAt` con horas UTC en lugar de Guatemala
- Inconsistencia entre agenda (correcta) y emails (incorrecta)

### Solución Implementada

#### 1. Configuración de Base de Datos
```sql
-- Configurar timezone permanente en Neon
ALTER DATABASE neondb SET timezone = 'America/Guatemala';
```

#### 2. Connection String
```env
DATABASE_URL="...?timezone=America/Guatemala"
```

#### 3. Corrección en Frontend (Doctor)
**Archivo**: `/app/(dashboard)/dashboard/doctor/agenda/appointment/[[...params]]/page.tsx`

**Problema**: `toISOString()` convertía fechas a UTC
```javascript
// ❌ INCORRECTO (convertía a UTC)
startTime: startDateTime.toISOString()
```

**Solución**: Crear fechas sin conversión UTC
```javascript
// ✅ CORRECTO (mantiene hora local)
startTime: `${year}-${month}-${day}T${hourStr}:${minuteStr}:00`
```

#### 4. Parseo de Parámetros URL
**Problema**: `new Date(preselectedDate)` interpretaba como UTC
```javascript
// ❌ INCORRECTO
scheduledDate: preselectedDate ? new Date(preselectedDate) : new Date()
```

**Solución**: Crear fecha local explícitamente
```javascript
// ✅ CORRECTO
const [year, month, day] = preselectedDate.split('-').map(Number);
dateToUse = new Date(year, month - 1, day);
```

### Verificación en Emails

**Configuración correcta en templates:**
```javascript
// Email usa timezone explícito
const appointmentDateFormatted = startTime.toLocaleDateString('es-GT', {
  weekday: 'long', 
  year: 'numeric', 
  month: 'long', 
  day: 'numeric',
  timeZone: 'America/Guatemala'  // ✅ Clave para mostrar correctamente
});
```

### Logs de Verificación

**Logs exitosos:**
```
🚨 FECHAS RECIBIDAS DEL FRONTEND: {
  startTime: '2025-08-02T10:30:00'  // ✅ Fecha local sin UTC
}

🚨 FECHAS DE LA CITA EN BD: {
  startTime: 2025-08-02T16:30:00.000Z  // ✅ UTC correcto (10:30 AM Guatemala = 4:30 PM UTC)
}

🚨 FECHA FORMATEADA PARA EMAIL: sábado, 2 de agosto de 2025  // ✅ Correcto
🚨 HORA FORMATEADA PARA EMAIL: 10:30 a. m.  // ✅ Correcto
```

### Resultados

✅ **Emails con fechas correctas**: "sábado, 2 de agosto de 2025 a las 10:30 a.m."  
✅ **Agenda muestra fechas correctas**: 2 de agosto 10:30 AM  
✅ **Base de datos**: Fechas UTC correctas para auditoria  
✅ **Verificación de disponibilidad**: Funciona con fechas correctas  

### Aplicabilidad

**Afecta a:**
- ✅ **Doctor**: Página de crear citas corregida
- ✅ **Asistente**: Hereda configuración de DB automáticamente  
- ✅ **VAPI**: Hereda configuración de DB automáticamente
- ✅ **Todos los emails**: Templates con timezone correcto

**No requiere cambios adicionales**: Los flujos que no crean citas desde frontend (VAPI, backend APIs) usan las fechas correctamente porque la DB ya maneja el timezone.

---

**Versión**: 1.2  
**Fecha**: 2025-08-02  
**Estado**: Documentación actualizada - Sistema de fechas y timezone solucionado  
**Cambios v1.2**: 
- VAPI ahora maneja pacientes nuevos Y existentes
- Agregados 5 endpoints VAPI específicos  
- Corrección: Email invitación solo si es paciente nuevo
- **NUEVO**: Sistema de timezone Guatemala implementado y documentado
- **NUEVO**: Fechas correctas en emails y registros de auditoria