# Integración API CIE-11 (WHO ICD-11)

## 📋 Resumen

**Objetivo**: Integración con la API oficial de la WHO para CIE-11 en lugar de crear un catálogo local de diagnósticos  
**API**: WHO ICD-11 API v2  
**Documentación**: https://icd.who.int/docs/icd-api/APIDoc-Version2/  
**Homepage**: https://icd.who.int/icdapi

## 🔐 Autenticación OAuth 2.0

### Credenciales Requeridas
1. **Registro**: https://icd.who.int/icdapi
2. **Client ID** y **Client Secret** obtenidos del portal
3. **Scope**: `icdapi_access`
4. **Token Endpoint**: `https://icdaccessmanagement.who.int/connect/token`

### Flujo de Autenticación
```typescript
interface CIE11AuthConfig {
  clientId: string;          // Obtenido del portal WHO
  clientSecret: string;      // Obtenido del portal WHO  
  tokenEndpoint: string;     // https://icdaccessmanagement.who.int/connect/token
  scope: string;             // "icdapi_access"
  apiBaseUrl: string;        // "https://id.who.int"
  language: string;          // "es" para español
  apiVersion: string;        // "v2"
}

interface TokenResponse {
  access_token: string;
  token_type: "Bearer";
  expires_in: number;        // ~3600 segundos (1 hora)
  scope: string;
}
```

## 🔧 Implementación en Next.js

### 1. Servicio de Autenticación
```typescript
// /lib/services/cie11-auth.ts
export class CIE11AuthService {
  private static instance: CIE11AuthService;
  private accessToken: string | null = null;
  private tokenExpiry: Date | null = null;

  static getInstance(): CIE11AuthService {
    if (!CIE11AuthService.instance) {
      CIE11AuthService.instance = new CIE11AuthService();
    }
    return CIE11AuthService.instance;
  }

  async getAccessToken(): Promise<string> {
    // Verificar si el token sigue vigente
    if (this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry) {
      return this.accessToken;
    }

    // Solicitar nuevo token
    const tokenData = new URLSearchParams({
      grant_type: 'client_credentials',
      scope: 'icdapi_access'
    });

    const response = await fetch('https://icdaccessmanagement.who.int/connect/token', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Authorization': `Basic ${Buffer.from(`${process.env.CIE11_CLIENT_ID}:${process.env.CIE11_CLIENT_SECRET}`).toString('base64')}`
      },
      body: tokenData
    });

    if (!response.ok) {
      throw new Error(`Error obteniendo token CIE-11: ${response.statusText}`);
    }

    const tokenResponse: TokenResponse = await response.json();
    
    this.accessToken = tokenResponse.access_token;
    this.tokenExpiry = new Date(Date.now() + (tokenResponse.expires_in * 1000) - 60000); // 1 minuto de margen

    return this.accessToken;
  }
}
```

### 2. Servicio de API CIE-11
```typescript
// /lib/services/cie11-api.ts
export interface CIE11SearchResult {
  id: string;                    // URI del concepto
  title: string;                 // Título del diagnóstico
  definition: string;            // Definición
  longDefinition: string;        // Definición extendida
  fullySpecifiedName: string;    // Nombre completo
  theCode: string;               // Código CIE-11
  source: string;                // Fuente
  breadcrumb: string[];          // Jerarquía de categorías
}

export interface CIE11Detail extends CIE11SearchResult {
  inclusions: string[];          // Incluye
  exclusions: string[];          // Excluye
  note: string;                  // Notas adicionales
  codingNote: string;            // Notas de codificación
  blockId: string;               // ID del bloque
  codeRange: string;             // Rango de códigos
  classKind: string;             // Tipo de clase
  parent: string[];              // Padres en la jerarquía
  child: string[];               // Hijos en la jerarquía
}

export class CIE11ApiService {
  private authService: CIE11AuthService;
  private baseUrl = 'https://id.who.int';

  constructor() {
    this.authService = CIE11AuthService.getInstance();
  }

  private async makeRequest(endpoint: string, params?: Record<string, string>) {
    const accessToken = await this.authService.getAccessToken();
    
    const url = new URL(`${this.baseUrl}${endpoint}`);
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        url.searchParams.append(key, value);
      });
    }

    const response = await fetch(url.toString(), {
      headers: {
        'Authorization': `Bearer ${accessToken}`,
        'Accept': 'application/json',
        'Accept-Language': 'es',  // Español
        'API-Version': 'v2'
      }
    });

    if (!response.ok) {
      throw new Error(`Error en API CIE-11: ${response.statusText}`);
    }

    return response.json();
  }

  // Búsqueda de diagnósticos
  async search(query: string, maxResults: number = 20): Promise<CIE11SearchResult[]> {
    const params = {
      q: query,
      subtreeFilterUsesFoundationDescendants: 'false',
      includeKeywordResult: 'true',
      useFlexisearch: 'false',
      flatResults: 'false',
      highlightingEnabled: 'false'
    };

    const results = await this.makeRequest('/icd/entity/search', params);
    
    return results.destinationEntities?.slice(0, maxResults) || [];
  }

  // Obtener detalles de un diagnóstico específico
  async getById(entityId: string): Promise<CIE11Detail> {
    const cleanId = entityId.replace('https://id.who.int/icd/entity/', '');
    return this.makeRequest(`/icd/entity/${cleanId}`);
  }

  // Buscar por código específico
  async getByCode(code: string): Promise<CIE11Detail | null> {
    try {
      const results = await this.search(code, 5);
      const exactMatch = results.find(r => r.theCode === code);
      
      if (exactMatch) {
        return this.getById(exactMatch.id);
      }
      
      return null;
    } catch (error) {
      console.error(`Error buscando código ${code}:`, error);
      return null;
    }
  }

  // Obtener linearización MMS (Medical Management Statistics)
  async getLinearization(entityId: string): Promise<any> {
    const cleanId = entityId.replace('https://id.who.int/icd/entity/', '');
    return this.makeRequest(`/icd/release/11/2024-01/mms/${cleanId}`);
  }
}
```

### 3. Cache Local para Performance
```typescript
// /lib/services/cie11-cache.ts
import { db } from '@/db';

export interface CachedDiagnosis {
  id: string;
  cie11Code: string;
  title: string;
  definition: string;
  searchTerms: string[];        // Términos que llevaron a este resultado
  consultoryId: string;         // Cache por consultorio
  lastUpdated: Date;
  rawData: any;                 // Respuesta completa de la API
  isActive: boolean;
}

export class CIE11CacheService {
  // Cache búsquedas para mejorar performance
  async cacheSearchResult(
    searchTerm: string, 
    results: CIE11SearchResult[], 
    consultoryId: string
  ): Promise<void> {
    for (const result of results) {
      await db.insert(cached_diagnosis).values({
        id: result.id,
        cie11Code: result.theCode,
        title: result.title,
        definition: result.definition,
        searchTerms: [searchTerm.toLowerCase()],
        consultoryId,
        lastUpdated: new Date(),
        rawData: result,
        isActive: true
      }).onConflictDoUpdate({
        target: [cached_diagnosis.id, cached_diagnosis.consultoryId],
        set: {
          searchTerms: sql`array_append(${cached_diagnosis.searchTerms}, ${searchTerm.toLowerCase()})`,
          lastUpdated: new Date(),
          rawData: result
        }
      });
    }
  }

  // Buscar en cache antes de consultar API
  async searchCache(
    searchTerm: string, 
    consultoryId: string
  ): Promise<CachedDiagnosis[]> {
    return db.select()
      .from(cached_diagnosis)
      .where(
        and(
          eq(cached_diagnosis.consultoryId, consultoryId),
          eq(cached_diagnosis.isActive, true),
          sql`${searchTerm.toLowerCase()} = ANY(${cached_diagnosis.searchTerms})`,
          // Cache válido por 30 días
          sql`${cached_diagnosis.lastUpdated} > NOW() - INTERVAL '30 days'`
        )
      )
      .limit(20);
  }

  // Limpiar cache antiguo
  async cleanOldCache(): Promise<void> {
    await db.delete(cached_diagnosis)
      .where(
        sql`${cached_diagnosis.lastUpdated} < NOW() - INTERVAL '90 days'`
      );
  }
}
```

## 🎯 Integración en Consultas Médicas

### 1. Componente de Búsqueda de Diagnósticos
```typescript
// /components/medical/diagnosis-search.tsx
'use client';

import { useState, useEffect } from 'react';
import { CIE11ApiService, CIE11SearchResult } from '@/lib/services/cie11-api';
import { CIE11CacheService } from '@/lib/services/cie11-cache';

interface DiagnosisSearchProps {
  onSelect: (diagnosis: CIE11SearchResult) => void;
  consultoryId: string;
}

export function DiagnosisSearch({ onSelect, consultoryId }: DiagnosisSearchProps) {
  const [searchTerm, setSearchTerm] = useState('');
  const [results, setResults] = useState<CIE11SearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [cie11Api] = useState(() => new CIE11ApiService());
  const [cacheService] = useState(() => new CIE11CacheService());

  const searchDiagnosis = async (query: string) => {
    if (query.length < 3) {
      setResults([]);
      return;
    }

    setIsLoading(true);
    
    try {
      // Buscar primero en cache
      const cachedResults = await cacheService.searchCache(query, consultoryId);
      
      if (cachedResults.length > 0) {
        setResults(cachedResults.map(cached => ({
          id: cached.id,
          title: cached.title,
          definition: cached.definition,
          theCode: cached.cie11Code,
          // ... otros campos del cache
        })));
        setIsLoading(false);
        return;
      }

      // Si no hay en cache, consultar API
      const apiResults = await cie11Api.search(query);
      setResults(apiResults);

      // Guardar en cache
      await cacheService.cacheSearchResult(query, apiResults, consultoryId);
      
    } catch (error) {
      console.error('Error buscando diagnósticos:', error);
      setResults([]);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      searchDiagnosis(searchTerm);
    }, 300); // Debounce de 300ms

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  return (
    <div className="space-y-4">
      <div className="relative">
        <input
          type="text"
          placeholder="Buscar diagnóstico CIE-11..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-3 border rounded-lg"
        />
        {isLoading && (
          <div className="absolute right-3 top-3">
            <div className="animate-spin h-5 w-5 border-2 border-blue-500 rounded-full border-t-transparent"></div>
          </div>
        )}
      </div>

      {results.length > 0 && (
        <div className="max-h-96 overflow-y-auto border rounded-lg">
          {results.map((result) => (
            <div
              key={result.id}
              onClick={() => onSelect(result)}
              className="p-4 hover:bg-gray-50 cursor-pointer border-b last:border-b-0"
            >
              <div className="flex justify-between items-start">
                <div className="flex-1">
                  <h4 className="font-medium text-gray-900">{result.title}</h4>
                  <p className="text-sm text-gray-600 mt-1">{result.definition}</p>
                </div>
                <span className="text-sm font-mono bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  {result.theCode}
                </span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

### 2. Gestión de Diagnósticos en Consulta
```typescript
// /components/medical/consultation-diagnosis.tsx
'use client';

import { useState } from 'react';
import { DiagnosisSearch } from './diagnosis-search';
import { CIE11SearchResult } from '@/lib/services/cie11-api';

interface PatientDiagnosis {
  id: string;
  cie11Code: string;
  title: string;
  type: 'primary' | 'secondary' | 'differential';
  notes: string;
}

interface ConsultationDiagnosisProps {
  consultationId: string;
  consultoryId: string;
  onDiagnosisChange: (diagnoses: PatientDiagnosis[]) => void;
}

export function ConsultationDiagnosis({ 
  consultationId, 
  consultoryId, 
  onDiagnosisChange 
}: ConsultationDiagnosisProps) {
  const [diagnoses, setDiagnoses] = useState<PatientDiagnosis[]>([]);
  const [showSearch, setShowSearch] = useState(false);

  const addDiagnosis = (diagnosis: CIE11SearchResult) => {
    const newDiagnosis: PatientDiagnosis = {
      id: `${consultationId}-${Date.now()}`,
      cie11Code: diagnosis.theCode,
      title: diagnosis.title,
      type: diagnoses.length === 0 ? 'primary' : 'secondary',
      notes: ''
    };

    const updatedDiagnoses = [...diagnoses, newDiagnosis];
    setDiagnoses(updatedDiagnoses);
    onDiagnosisChange(updatedDiagnoses);
    setShowSearch(false);
  };

  const updateDiagnosisType = (diagnosisId: string, type: PatientDiagnosis['type']) => {
    const updatedDiagnoses = diagnoses.map(d => 
      d.id === diagnosisId ? { ...d, type } : d
    );
    setDiagnoses(updatedDiagnoses);
    onDiagnosisChange(updatedDiagnoses);
  };

  const removeDiagnosis = (diagnosisId: string) => {
    const updatedDiagnoses = diagnoses.filter(d => d.id !== diagnosisId);
    setDiagnoses(updatedDiagnoses);
    onDiagnosisChange(updatedDiagnoses);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium">Diagnósticos CIE-11</h3>
        <button
          onClick={() => setShowSearch(true)}
          className="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600"
        >
          Agregar Diagnóstico
        </button>
      </div>

      {showSearch && (
        <div className="border rounded-lg p-4 bg-gray-50">
          <div className="flex justify-between items-center mb-4">
            <h4 className="font-medium">Buscar Diagnóstico</h4>
            <button
              onClick={() => setShowSearch(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ✕
            </button>
          </div>
          <DiagnosisSearch
            onSelect={addDiagnosis}
            consultoryId={consultoryId}
          />
        </div>
      )}

      {diagnoses.length > 0 && (
        <div className="space-y-4">
          {diagnoses.map((diagnosis) => (
            <div key={diagnosis.id} className="border rounded-lg p-4 bg-white">
              <div className="flex justify-between items-start mb-3">
                <div className="flex-1">
                  <h4 className="font-medium">{diagnosis.title}</h4>
                  <p className="text-sm text-gray-600 font-mono">
                    Código CIE-11: {diagnosis.cie11Code}
                  </p>
                </div>
                <div className="flex items-center space-x-2">
                  <select
                    value={diagnosis.type}
                    onChange={(e) => updateDiagnosisType(diagnosis.id, e.target.value as PatientDiagnosis['type'])}
                    className="text-sm border rounded px-2 py-1"
                  >
                    <option value="primary">Primario</option>
                    <option value="secondary">Secundario</option>
                    <option value="differential">Diferencial</option>
                  </select>
                  <button
                    onClick={() => removeDiagnosis(diagnosis.id)}
                    className="text-red-500 hover:text-red-700 text-sm"
                  >
                    Eliminar
                  </button>
                </div>
              </div>
              <textarea
                placeholder="Notas adicionales del diagnóstico..."
                className="w-full p-2 text-sm border rounded"
                rows={2}
                value={diagnosis.notes}
                onChange={(e) => {
                  const updatedDiagnoses = diagnoses.map(d =>
                    d.id === diagnosis.id ? { ...d, notes: e.target.value } : d
                  );
                  setDiagnoses(updatedDiagnoses);
                  onDiagnosisChange(updatedDiagnoses);
                }}
              />
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
```

## 🗃️ Esquema de Base de Datos

```sql
-- Cache local de diagnósticos CIE-11
CREATE TABLE cached_diagnosis (
  id VARCHAR(255) NOT NULL,
  consultory_id UUID NOT NULL,
  cie11_code VARCHAR(50) NOT NULL,
  title TEXT NOT NULL,
  definition TEXT,
  search_terms TEXT[],
  last_updated TIMESTAMP DEFAULT NOW(),
  raw_data JSONB,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  PRIMARY KEY (id, consultory_id)
);

-- Diagnósticos de pacientes en consultas
CREATE TABLE patient_diagnoses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  consultation_id UUID NOT NULL,
  patient_id UUID NOT NULL,
  doctor_id UUID NOT NULL,
  cie11_code VARCHAR(50) NOT NULL,
  diagnosis_title TEXT NOT NULL,
  diagnosis_type VARCHAR(20) CHECK (diagnosis_type IN ('primary', 'secondary', 'differential')),
  notes TEXT,
  diagnosis_date TIMESTAMP DEFAULT NOW(),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  FOREIGN KEY (consultation_id) REFERENCES consultations(id),
  FOREIGN KEY (patient_id) REFERENCES users(id),
  FOREIGN KEY (doctor_id) REFERENCES users(id)
);

-- Índices para performance
CREATE INDEX idx_cached_diagnosis_search ON cached_diagnosis USING GIN(search_terms);
CREATE INDEX idx_cached_diagnosis_consultory ON cached_diagnosis(consultory_id);
CREATE INDEX idx_patient_diagnoses_consultation ON patient_diagnoses(consultation_id);
CREATE INDEX idx_patient_diagnoses_patient ON patient_diagnoses(patient_id);
CREATE INDEX idx_patient_diagnoses_cie11 ON patient_diagnoses(cie11_code);
```

## 🔒 Variables de Entorno

```bash
# .env.local
CIE11_CLIENT_ID=tu_client_id_aqui
CIE11_CLIENT_SECRET=tu_client_secret_aqui
CIE11_API_BASE_URL=https://id.who.int
CIE11_TOKEN_ENDPOINT=https://icdaccessmanagement.who.int/connect/token
```

## 🚀 API Endpoints

### 1. Búsqueda de Diagnósticos
```typescript
// /app/api/medical/diagnosis/search/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { CIE11ApiService } from '@/lib/services/cie11-api';

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;
  const query = searchParams.get('q');
  const consultoryId = searchParams.get('consultoryId');

  if (!query || !consultoryId) {
    return NextResponse.json(
      { error: 'Parámetros query y consultoryId requeridos' },
      { status: 400 }
    );
  }

  try {
    const cie11Service = new CIE11ApiService();
    const results = await cie11Service.search(query);
    
    return NextResponse.json({ results });
  } catch (error) {
    console.error('Error buscando diagnósticos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
```

### 2. Detalles de Diagnóstico
```typescript
// /app/api/medical/diagnosis/[entityId]/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { CIE11ApiService } from '@/lib/services/cie11-api';

export async function GET(
  request: NextRequest,
  { params }: { params: { entityId: string } }
) {
  try {
    const cie11Service = new CIE11ApiService();
    const details = await cie11Service.getById(params.entityId);
    
    return NextResponse.json({ details });
  } catch (error) {
    console.error('Error obteniendo detalles del diagnóstico:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
```

## 📊 Métricas y Monitoreo

### 1. Logs de Uso de API
```typescript
// /lib/services/cie11-analytics.ts
export class CIE11AnalyticsService {
  static async logApiCall(
    endpoint: string,
    consultoryId: string,
    responseTime: number,
    success: boolean
  ): Promise<void> {
    await db.insert(api_usage_logs).values({
      service: 'cie11',
      endpoint,
      consultoryId,
      responseTime,
      success,
      timestamp: new Date()
    });
  }

  static async getUsageStats(consultoryId: string, days: number = 30) {
    return db.select({
      date: sql`DATE(timestamp)`,
      calls: sql`COUNT(*)`,
      avgResponseTime: sql`AVG(response_time)`,
      successRate: sql`(COUNT(*) FILTER (WHERE success = true)::float / COUNT(*)) * 100`
    })
    .from(api_usage_logs)
    .where(
      and(
        eq(api_usage_logs.consultoryId, consultoryId),
        eq(api_usage_logs.service, 'cie11'),
        sql`timestamp > NOW() - INTERVAL '${days} days'`
      )
    )
    .groupBy(sql`DATE(timestamp)`)
    .orderBy(sql`DATE(timestamp) DESC`);
  }
}
```

## ⚠️ Consideraciones Importantes

### 1. Rate Limiting
- La API WHO tiene límites de uso
- Implementar cache inteligente para reducir llamadas
- Usar debounce en búsquedas en tiempo real

### 2. Fallback Mode
- Cache local para funcionamiento offline
- Mensajes claros cuando la API no está disponible
- Opción manual de códigos CIE-11 como respaldo

### 3. Seguridad
- Credentials en variables de entorno
- No exponer Client Secret en frontend
- Validar y sanitizar todas las búsquedas

### 4. Performance
- Cache de resultados por 30 días
- Paginación en resultados grandes
- Lazy loading de detalles

### 5. Cumplimiento
- Respetar términos de uso de WHO
- Mantener logs de auditoría
- Actualizar regularmente la versión de la API

## 🎯 Próximos Pasos

1. **Obtener credenciales** del portal WHO
2. **Implementar autenticación** OAuth 2.0
3. **Crear servicios** de búsqueda y cache
4. **Integrar en consultas** médicas
5. **Probar thoroughly** con casos reales
6. **Monitorear uso** y performance
7. **Documentar** para el equipo médico

Esta integración permitirá tener acceso a la clasificación internacional más actualizada sin mantener un catálogo local que requiera actualizaciones constantes.