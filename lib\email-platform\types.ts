/**
 * PLATAFORMA ROBUSTA DE EMAILS
 * Sistema centralizado y escalable para todos los emails del sistema médico
 */

// Tipos de eventos de email disponibles
export type EmailEventType = 
  // Eventos de pacientes
  | 'patient_created'
  | 'patient_onboarding_completed'
  | 'patient_account_activated'
  
  // Eventos de citas
  | 'appointment_created'
  | 'appointment_confirmed'
  | 'appointment_combined_notification'
  | 'appointment_reminder_48h'
  | 'appointment_reminder_24h'
  | 'appointment_reminder_2h'
  | 'appointment_cancelled'
  | 'appointment_rescheduled'
  | 'precheckin_invitation'
  
  // Eventos de consulta
  | 'consultation_completed'
  | 'prescription_sent'
  | 'feedback_request'
  | 'follow_up_reminder'
  
  // Eventos administrativos
  | 'welcome_message'
  | 'password_reset'
  | 'account_suspended'
  | 'system_maintenance';

// Prioridades de email
export type EmailPriority = 'low' | 'normal' | 'high' | 'critical';

// Estados de email
export type EmailStatus = 'pending' | 'queued' | 'sent' | 'delivered' | 'failed' | 'bounced';

// Configuración base de email
export interface BaseEmailConfig {
  to: string;
  cc?: string[];
  bcc?: string[];
  priority: EmailPriority;
  scheduledAt?: Date; // Para emails programados
  retryAttempts?: number;
  tags?: string[]; // Para tracking y analytics
}

// Contexto común para todos los emails
export interface EmailContext {
  userId?: string;
  patientId?: string;
  doctorId?: string;
  appointmentId?: string;
  consultationId?: string;
  metadata?: Record<string, any>;
}

// Parámetros específicos por tipo de email
export interface PatientCreatedParams {
  patientName: string;
  isDependent: boolean;
  guardianName?: string;
  relationship?: string;
  activationLink?: string;
  hasAppointment?: boolean;
  appointmentDate?: string;
}

export interface AppointmentCreatedParams {
  patientName: string;
  doctorName: string;
  consultoryName: string;
  appointmentDate: string;
  appointmentTime: string;
  appointmentType: string;
  confirmationCode: string;
  preCheckinLink?: string;
}

export interface OnboardingCompletedParams {
  userName: string;
  userRole: string;
  welcomeMessage: string;
  nextSteps: string[];
}

export interface ConsultationCompletedParams {
  patientName: string;
  doctorName: string;
  consultationDate: string;
  prescriptionAttachment?: string;
  feedbackLink: string;
  followUpDate?: string;
}

// Union type para todos los parámetros posibles
export type EmailParams = 
  | PatientCreatedParams
  | AppointmentCreatedParams 
  | OnboardingCompletedParams
  | ConsultationCompletedParams
  | Record<string, any>; // Para flexibilidad futura

// Configuración completa para enviar un email
export interface EmailRequest {
  event: EmailEventType;
  config: BaseEmailConfig;
  context: EmailContext;
  params: EmailParams;
}

// Respuesta del sistema de emails
export interface EmailResponse {
  success: boolean;
  emailId?: string;
  status: EmailStatus;
  message?: string;
  error?: string;
  retryable?: boolean;
}

// Configuración de templates
export interface EmailTemplate {
  subject: (params: any) => string;
  html: (params: any) => string;
  text?: (params: any) => string;
  requiredParams: string[];
  tags: string[];
}

// Log de email para auditoría
export interface EmailLog {
  id: string;
  event: EmailEventType;
  to: string;
  status: EmailStatus;
  sentAt?: Date;
  deliveredAt?: Date;
  failedAt?: Date;
  error?: string;
  retryCount: number;
  context: EmailContext;
  createdAt: Date;
  updatedAt: Date;
}