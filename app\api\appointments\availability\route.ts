import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, doctorSchedules, activityTypes, doctorScheduleExceptions } from '@/db/schema';
import { eq, and, gte, lte, or } from 'drizzle-orm';

interface TimeSlot {
  time: string;
  available: boolean;
  reason?: string;
}

interface AvailabilityResponse {
  success: boolean;
  data: {
    date: string;
    doctorId: string;
    activityTypeId?: string;
    activityType?: {
      name: string;
      respectsSchedule: boolean;
      duration: number;
      category: string;
    };
    availableSlots: TimeSlot[];
    workingDay: boolean;
    workingHours?: {
      start: string;
      end: string;
      lunchBreak?: {
        start: string;
        end: string;
      };
    };
  };
}

// GET - Obtener disponibilidad de slots para un doctor en una fecha específica o próximos slots
export async function GET(request: NextRequest): Promise<NextResponse<AvailabilityResponse | { error: string }>> {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const doctorId = searchParams.get('doctorId');
    const date = searchParams.get('date');
    const limit = searchParams.get('limit');
    const activityTypeId = searchParams.get('activityTypeId');

    if (!doctorId) {
      return NextResponse.json({ 
        error: 'Parámetro requerido: doctorId' 
      }, { status: 400 });
    }
    
    // Si se solicita límite sin fecha, obtener próximos slots disponibles
    if (limit && !date) {
      return getNextAvailableSlots(doctorId, parseInt(limit), activityTypeId);
    }

    if (!date) {
      return NextResponse.json({ 
        error: 'Parámetros requeridos: doctorId, date (o doctorId, limit)' 
      }, { status: 400 });
    }

    // Validar fecha
    const targetDate = new Date(date);
    if (isNaN(targetDate.getTime())) {
      return NextResponse.json({ error: 'Fecha inválida' }, { status: 400 });
    }

    // Obtener información del tipo de actividad si se proporciona
    let activityType = null;
    if (activityTypeId) {
      const activityTypeResult = await db
        .select()
        .from(activityTypes)
        .where(and(eq(activityTypes.id, activityTypeId), eq(activityTypes.isActive, true)))
        .limit(1);

      if (activityTypeResult.length === 0) {
        return NextResponse.json({ error: 'Tipo de actividad no encontrado' }, { status: 400 });
      }

      activityType = activityTypeResult[0];
    }

    // Si no hay activityType o no respeta horarios, dar disponibilidad libre
    if (!activityType || !activityType.respectsSchedule) {
      return NextResponse.json({
        success: true,
        data: {
          date,
          doctorId,
          activityTypeId,
          activityType: activityType ? {
            name: activityType.name,
            respectsSchedule: activityType.respectsSchedule,
            duration: activityType.duration,
            category: activityType.category,
          } : undefined,
          availableSlots: generateFreeTimeSlots(activityType?.duration || 60),
          workingDay: true, // Horario libre = siempre disponible
        }
      });
    }

    // Obtener día de la semana (0=domingo, 1=lunes, ..., 6=sábado)
    const dayOfWeek = targetDate.getDay();

    // Obtener horario configurado del doctor para ese día
    const scheduleResult = await db
      .select()
      .from(doctorSchedules)
      .where(
        and(
          eq(doctorSchedules.doctorId, doctorId),
          eq(doctorSchedules.dayOfWeek, dayOfWeek),
          eq(doctorSchedules.isActive, true)
        )
      )
      .limit(1);

    if (scheduleResult.length === 0) {
      // No hay horario configurado para este día
      return NextResponse.json({
        success: true,
        data: {
          date,
          doctorId,
          activityTypeId,
          activityType: {
            name: activityType.name,
            respectsSchedule: activityType.respectsSchedule,
            duration: activityType.duration,
            category: activityType.category,
          },
          availableSlots: [],
          workingDay: false,
        }
      });
    }

    const schedule = scheduleResult[0];

    // Verificar excepciones para esta fecha específica
    const exceptionResult = await db
      .select()
      .from(doctorScheduleExceptions)
      .where(
        and(
          eq(doctorScheduleExceptions.doctorId, doctorId),
          eq(doctorScheduleExceptions.exceptionDate, targetDate)
        )
      )
      .limit(1);

    if (exceptionResult.length > 0) {
      const exception = exceptionResult[0];
      if (exception.type === 'vacation' || exception.type === 'sick_leave' || exception.type === 'closed') {
        // Día no disponible por excepción
        return NextResponse.json({
          success: true,
          data: {
            date,
            doctorId,
            activityTypeId,
            activityType: {
              name: activityType.name,
              respectsSchedule: activityType.respectsSchedule,
              duration: activityType.duration,
              category: activityType.category,
            },
            availableSlots: [],
            workingDay: false,
          }
        });
      }
      
      if (exception.type === 'special_hours' && exception.startTime && exception.endTime) {
        // Usar horarios especiales en lugar del horario regular
        schedule.startTime = exception.startTime;
        schedule.endTime = exception.endTime;
        schedule.lunchBreakStart = null;
        schedule.lunchBreakEnd = null;
      }
    }

    // Obtener citas ya agendadas para esta fecha
    const existingAppointments = await db
      .select({
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        status: appointments.status,
      })
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          eq(appointments.scheduledDate, targetDate),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'in_progress')
          )
        )
      );

    // Generar slots disponibles basados en el horario configurado
    const availableSlots = generateScheduledTimeSlots(
      schedule,
      activityType.duration,
      existingAppointments
    );

    return NextResponse.json({
      success: true,
      data: {
        date,
        doctorId,
        activityTypeId,
        activityType: {
          name: activityType.name,
          respectsSchedule: activityType.respectsSchedule,
          duration: activityType.duration,
          category: activityType.category,
        },
        availableSlots,
        workingDay: true,
        workingHours: {
          start: schedule.startTime,
          end: schedule.endTime,
          lunchBreak: schedule.lunchBreakStart && schedule.lunchBreakEnd ? {
            start: schedule.lunchBreakStart,
            end: schedule.lunchBreakEnd,
          } : undefined,
        },
      }
    });

  } catch (error) {
    console.error('Error fetching availability:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// Generar slots para horario libre (emergencias, cirugías, etc.)
function generateFreeTimeSlots(duration: number): TimeSlot[] {
  const slots: TimeSlot[] = [];
  const startHour = 0; // 00:00
  const endHour = 24; // 24:00
  
  for (let hour = startHour; hour < endHour; hour++) {
    for (let minute = 0; minute < 60; minute += 30) { // Slots cada 30 minutos
      const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      slots.push({
        time: timeString,
        available: true, // Horario libre = siempre disponible
      });
    }
  }
  
  return slots;
}

// Generar slots basados en horario configurado
function generateScheduledTimeSlots(
  schedule: any,
  duration: number,
  existingAppointments: any[]
): TimeSlot[] {
  const slots: TimeSlot[] = [];
  
  // Convertir horarios a minutos para facilitar cálculos
  const startMinutes = timeToMinutes(schedule.startTime);
  const endMinutes = timeToMinutes(schedule.endTime);
  const slotDuration = duration;
  
  // Horarios de almuerzo si existen
  const lunchStartMinutes = schedule.lunchBreakStart ? timeToMinutes(schedule.lunchBreakStart) : null;
  const lunchEndMinutes = schedule.lunchBreakEnd ? timeToMinutes(schedule.lunchBreakEnd) : null;
  
  // Convertir citas existentes a periodos ocupados
  const occupiedPeriods = existingAppointments.map(apt => ({
    start: timeToMinutes(apt.startTime.toTimeString().substring(0, 5)),
    end: timeToMinutes(apt.endTime.toTimeString().substring(0, 5)),
  }));
  
  // Generar slots desde inicio hasta fin del horario
  for (let currentMinutes = startMinutes; currentMinutes + slotDuration <= endMinutes; currentMinutes += 30) {
    const timeString = minutesToTime(currentMinutes);
    const slotEndMinutes = currentMinutes + slotDuration;
    
    let available = true;
    let reason = '';
    
    // Verificar si está en horario de almuerzo
    if (lunchStartMinutes && lunchEndMinutes) {
      if (currentMinutes < lunchEndMinutes && slotEndMinutes > lunchStartMinutes) {
        available = false;
        reason = 'Horario de almuerzo';
      }
    }
    
    // Verificar conflictos con citas existentes
    if (available) {
      for (const occupied of occupiedPeriods) {
        if (currentMinutes < occupied.end && slotEndMinutes > occupied.start) {
          available = false;
          reason = 'Cita ya agendada';
          break;
        }
      }
    }
    
    // Verificar límite de citas por hora si está configurado
    if (available && schedule.maxAppointmentsPerHour) {
      const hourStart = Math.floor(currentMinutes / 60) * 60;
      const hourEnd = hourStart + 60;
      const appointmentsInHour = occupiedPeriods.filter(occupied => 
        occupied.start < hourEnd && occupied.end > hourStart
      ).length;
      
      if (appointmentsInHour >= schedule.maxAppointmentsPerHour) {
        available = false;
        reason = 'Límite de citas por hora alcanzado';
      }
    }
    
    slots.push({
      time: timeString,
      available,
      reason: available ? undefined : reason,
    });
  }
  
  return slots;
}

// Utilidades para conversión de tiempo
function timeToMinutes(timeString: string): number {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

function minutesToTime(minutes: number): string {
  const hours = Math.floor(minutes / 60);
  const mins = minutes % 60;
  return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
}

// Función para obtener próximos slots disponibles para el wizard
async function getNextAvailableSlots(doctorId: string, limit: number, activityTypeId?: string | null) {
  try {
    // Obtener horarios del doctor
    const doctorSchedule = await db
      .select()
      .from(doctorSchedules)
      .where(
        and(
          eq(doctorSchedules.doctorId, doctorId),
          eq(doctorSchedules.isActive, true),
          eq(doctorSchedules.allowOnlineBooking, true)
        )
      );

    if (doctorSchedule.length === 0) {
      return NextResponse.json({
        success: true,
        data: [],
        message: 'Doctor no tiene horarios configurados para reserva online'
      });
    }

    const availableSlots = [];
    const now = new Date();
    const maxDaysToCheck = 30;
    const appointmentDuration = 30; // Duración por defecto para citas de pacientes

    for (let dayOffset = 0; dayOffset < maxDaysToCheck && availableSlots.length < limit; dayOffset++) {
      const currentDate = new Date(now);
      currentDate.setDate(now.getDate() + dayOffset);
      currentDate.setHours(0, 0, 0, 0);
      
      const dayOfWeek = currentDate.getDay();
      
      // Buscar horario para este día de la semana
      const daySchedule = doctorSchedule.find(schedule => schedule.dayOfWeek === dayOfWeek);
      if (!daySchedule) continue;
      
      // Verificar excepciones
      const exceptions = await db
        .select()
        .from(doctorScheduleExceptions)
        .where(
          and(
            eq(doctorScheduleExceptions.doctorId, doctorId),
            eq(doctorScheduleExceptions.exceptionDate, currentDate)
          )
        );
      
      if (exceptions.some(ex => ex.type === 'closed' || (ex.type === 'vacation' && ex.isAllDay))) {
        continue;
      }
      
      // Obtener citas ya programadas
      const startOfDay = new Date(currentDate);
      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);
      
      const existingAppointments = await db
        .select({
          startTime: appointments.startTime,
          endTime: appointments.endTime,
        })
        .from(appointments)
        .where(
          and(
            eq(appointments.doctorId, doctorId),
            gte(appointments.startTime, startOfDay),
            lte(appointments.startTime, endOfDay),
            or(
              eq(appointments.status, 'scheduled'),
              eq(appointments.status, 'confirmed'),
              eq(appointments.status, 'checked_in'),
              eq(appointments.status, 'in_progress')
            )
          )
        );
      
      // Generar slots para este día
      const workStartTime = daySchedule.startTime || '08:00';
      const workEndTime = daySchedule.endTime || '22:00'; // Extender hasta 10 PM
      
      const [startHour, startMinute] = workStartTime.split(':').map(Number);
      const [endHour, endMinute] = workEndTime.split(':').map(Number);
      
      const workStart = new Date(currentDate);
      workStart.setHours(startHour, startMinute, 0, 0);
      
      const workEnd = new Date(currentDate);
      workEnd.setHours(endHour, endMinute, 0, 0);
      
      // Solo ofrecer slots que sean al menos 30 minutos en el futuro (más razonable)
      const minBookingTime = new Date(now.getTime() + (30 * 60 * 1000));
      
      let currentSlot = new Date(workStart);
      
      while (currentSlot < workEnd && availableSlots.length < limit) {
        if (currentSlot < minBookingTime) {
          currentSlot = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
          continue;
        }
        
        const slotEnd = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
        
        // Verificar conflictos con citas existentes
        const hasConflict = existingAppointments.some(apt => {
          const aptStart = new Date(apt.startTime);
          const aptEnd = new Date(apt.endTime);
          return (currentSlot < aptEnd && slotEnd > aptStart);
        });
        
        // Verificar horario de almuerzo
        let inLunchBreak = false;
        if (daySchedule.lunchBreakStart && daySchedule.lunchBreakEnd) {
          const [lunchStartHour, lunchStartMinute] = daySchedule.lunchBreakStart.split(':').map(Number);
          const [lunchEndHour, lunchEndMinute] = daySchedule.lunchBreakEnd.split(':').map(Number);
          
          const lunchStart = new Date(currentDate);
          lunchStart.setHours(lunchStartHour, lunchStartMinute, 0, 0);
          
          const lunchEnd = new Date(currentDate);
          lunchEnd.setHours(lunchEndHour, lunchEndMinute, 0, 0);
          
          inLunchBreak = currentSlot < lunchEnd && slotEnd > lunchStart;
        }
        
        if (!hasConflict && !inLunchBreak) {
          const slotId = `${doctorId}-${currentSlot.getTime()}`;
          const displayDate = currentSlot.toLocaleDateString('es-GT', {
            weekday: 'long',
            day: 'numeric',
            month: 'long'
          });
          const displayTime = currentSlot.toLocaleTimeString('es-GT', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
          });
          
          // Crear string de hora local sin conversión UTC
          const year = currentSlot.getFullYear();
          const month = String(currentSlot.getMonth() + 1).padStart(2, '0');
          const day = String(currentSlot.getDate()).padStart(2, '0');
          const hour = String(currentSlot.getHours()).padStart(2, '0');
          const minute = String(currentSlot.getMinutes()).padStart(2, '0');
          const localTimeString = `${year}-${month}-${day}T${hour}:${minute}:00`;
          const localDateString = `${year}-${month}-${day}`; // Fecha local sin conversión UTC

          availableSlots.push({
            id: slotId,
            date: localDateString, // Usar fecha local en lugar de UTC
            time: localTimeString, // Usar hora local en lugar de UTC
            displayDate: displayDate.charAt(0).toUpperCase() + displayDate.slice(1),
            displayTime: displayTime,
            duration: appointmentDuration,
            available: true
          });
        }
        
        currentSlot = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
      }
    }

    return NextResponse.json({
      success: true,
      data: availableSlots,
      pagination: {
        page: 1,
        limit: availableSlots.length,
        total: availableSlots.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    });

  } catch (error) {
    console.error('Error fetching next available slots:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Error desconocido'
    }, { status: 500 });
  }
}