'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';
import { useConsultoryInfo } from '@/hooks/use-consultory-info';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Stethoscope, 
  UserCheck, 
  Heart, 
  Shield, 
  Building, 
  ArrowRight,
  CheckCircle,
  Clock,
  AlertCircle
} from 'lucide-react';
import { UserRole, OnboardingFormData } from '@/lib/types/onboarding';
import { OnboardingFlow } from '@/components/onboarding/onboarding-flow';
import { toast } from 'sonner';

const ROLES = [
  {
    id: UserRole.DOCTOR,
    title: 'Doctor',
    description: 'Médico pediatra especializado',
    icon: Stethoscope,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    requirements: [
      'Licencia médica vigente',
      'Especialización en pediatría',
      'Documento de identidad',
      'Certificado de universidad',
      'Foto de diploma'
    ],
    approxTime: '10-15 minutos'
  },
  {
    id: UserRole.ASSISTANT,
    title: 'Asistente Médico',
    description: 'Asistente o auxiliar médico',
    icon: UserCheck,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    requirements: [
      'Documento de identidad',
      'Experiencia médica',
      'Referencias laborales',
      'Asociación con doctor'
    ],
    approxTime: '8-10 minutos'
  },
  {
    id: UserRole.PATIENT,
    title: 'Paciente',
    description: 'Paciente individual',
    icon: Heart,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    requirements: [
      'Documento de identidad',
      'Información médica básica',
      'Contacto de emergencia',
      'Tutor legal (si es menor)'
    ],
    approxTime: '5-8 minutos'
  },
  {
    id: UserRole.GUARDIAN,
    title: 'Tutor Legal',
    description: 'Padre, madre o tutor legal',
    icon: Shield,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    requirements: [
      'Documento de identidad',
      'Documentos de tutela',
      'Código de asociación o crear menor',
      'Relación familiar'
    ],
    approxTime: '6-12 minutos'
  },
  {
    id: UserRole.PROVIDER,
    title: 'Proveedor',
    description: 'Proveedor de productos/servicios',
    icon: Building,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    requirements: [
      'Documento de identidad',
      'NIT de la empresa',
      'Licencia comercial',
      'Catálogo de productos'
    ],
    approxTime: '10-15 minutos'
  }
];

export default function OnboardingPage() {
  const router = useRouter();
  const { getToken } = useAuth();
  const { consultory, isLoading: consultoryLoading } = useConsultoryInfo();
  const [selectedRole, setSelectedRole] = useState<UserRole | null>(null);
  const [showFlow, setShowFlow] = useState(false);

  const handleRoleSelect = (role: UserRole) => {
    setSelectedRole(role);
    setShowFlow(true);
  };

  const handleFlowComplete = async (data: OnboardingFormData) => {
    // Aquí podrías guardar los datos localmente o hacer algún procesamiento adicional
    toast.success('Solicitud enviada exitosamente. Será revisada por un administrador.');
    
    // Redirigir a página de confirmación pendiente usando window.location.replace
    // para evitar problemas con el middleware y metadatos de Clerk
    window.location.replace('/onboarding/pending');
  };

  const handleBackToSelection = () => {
    setShowFlow(false);
    setSelectedRole(null);
  };

  if (showFlow && selectedRole) {
    return (
      <OnboardingFlow 
        selectedRole={selectedRole}
        onComplete={handleFlowComplete}
        onBackToSelection={handleBackToSelection}
      />
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Registro en <span className="text-[#ea6cb0]">{consultoryLoading ? 'Cargando...' : consultory?.name || 'Mundo Pediatra'}</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Seleccione su rol para iniciar el proceso de registro. 
            Cada rol tiene requisitos específicos y un proceso de aprobación.
          </p>
        </div>

        {/* Roles Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 mb-12">
          {ROLES.map((role) => {
            const Icon = role.icon;
            return (
              <Card 
                key={role.id}
                className={`cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-[1.02] ${role.borderColor}`}
                onClick={() => handleRoleSelect(role.id)}
              >
                <CardHeader className={`${role.bgColor} rounded-t-lg`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <Icon className={`h-8 w-8 ${role.color}`} />
                      <div>
                        <CardTitle className={`text-xl ${role.color}`}>
                          {role.title}
                        </CardTitle>
                        <p className="text-sm text-gray-600 mt-1">
                          {role.description}
                        </p>
                      </div>
                    </div>
                    <ArrowRight className={`h-5 w-5 ${role.color}`} />
                  </div>
                </CardHeader>
                <CardContent className="p-6">
                  <div className="space-y-4">
                    {/* Tiempo estimado */}
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium text-gray-700">
                        Tiempo estimado:
                      </span>
                      <Badge variant="outline" className="text-xs">
                        <Clock className="h-3 w-3 mr-1" />
                        {role.approxTime}
                      </Badge>
                    </div>

                    {/* Requisitos */}
                    <div className="space-y-2">
                      <span className="text-sm font-medium text-gray-700">
                        Requisitos:
                      </span>
                      <ul className="space-y-1">
                        {role.requirements.map((req, index) => (
                          <li key={index} className="flex items-start space-x-2 text-sm text-gray-600">
                            <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                            <span>{req}</span>
                          </li>
                        ))}
                      </ul>
                    </div>

                    {/* Botón de acción */}
                    <Button 
                      className={`w-full ${role.color.replace('text-', 'bg-').replace('600', '600')} hover:${role.color.replace('text-', 'bg-').replace('600', '700')}`}
                      onClick={() => handleRoleSelect(role.id)}
                    >
                      Registrarse como {role.title}
                    </Button>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>

        {/* Información adicional */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-3">
                <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-blue-800">
                  <div className="font-medium mb-2">Proceso de Aprobación</div>
                  <div className="text-sm">
                    Todas las solicitudes pasan por un proceso de revisión manual. 
                    Asegúrese de completar todos los campos y subir documentos legibles.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-6">
              <div className="flex items-start space-x-3">
                <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                <div className="text-green-800">
                  <div className="font-medium mb-2">Documentos Aceptados</div>
                  <div className="text-sm">
                    Imágenes: JPG, PNG (máx. 5MB) | Documentos: PDF (máx. 10MB). 
                    Asegúrese de que todos los documentos sean legibles.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="text-center mt-12 text-gray-500">
          <p className="text-sm">
            ¿Tienes problemas con el registro? {' '}
            <a href="mailto:<EMAIL>" className="text-blue-600 hover:underline">
              Contacta soporte
            </a>
          </p>
        </div>
      </div>
    </div>
  );
}