'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  User, 
  CheckCircle, 
  AlertCircle, 
  UserCheck,
  RefreshCw,
  Stethoscope,
  Home,
  Calendar,
  ChevronRight
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import Link from 'next/link';

interface WaitingPatient {
  id: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  serviceName: string;
  consultoryName: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  checkedInAt: string;
  title: string;
}

interface WaitingPatientsCardProps {
  patients: WaitingPatient[];
  onRefresh?: () => void;
  onStartConsultation?: (patient: WaitingPatient) => void;
  onViewDetails?: (patient: WaitingPatient) => void;
  userRole: 'assistant' | 'doctor';
  loading?: boolean;
  className?: string;
  maxPatients?: number; // Límite de pacientes a mostrar
  showViewAllLink?: boolean;
  viewAllHref?: string;
}

export function WaitingPatientsCard({
  patients,
  onRefresh,
  onStartConsultation,
  onViewDetails,
  userRole,
  loading = false,
  className,
  maxPatients = 6,
  showViewAllLink = true,
  viewAllHref = '/dashboard/assistant/waiting-room'
}: WaitingPatientsCardProps) {
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    if (onRefresh) {
      setRefreshing(true);
      await onRefresh();
      setTimeout(() => setRefreshing(false), 500);
    }
  };

  const getWaitingTime = (checkedInAt: string, startTime: string) => {
    const arrivalTime = new Date(checkedInAt || startTime);
    const waitingMinutes = Math.floor((Date.now() - arrivalTime.getTime()) / (1000 * 60));
    return waitingMinutes;
  };

  const getWaitingStatus = (waitingMinutes: number) => {
    if (waitingMinutes > 30) return { 
      level: 'critical', 
      color: 'bg-red-600', 
      textColor: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    };
    if (waitingMinutes > 15) return { 
      level: 'warning', 
      color: 'bg-orange-600', 
      textColor: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    };
    return { 
      level: 'normal', 
      color: 'bg-blue-600', 
      textColor: 'text-blue-600',
      bgColor: 'bg-white',
      borderColor: 'border-blue-200'
    };
  };

  const patientsToShow = patients.slice(0, maxPatients);
  const hasMorePatients = patients.length > maxPatients;

  if (patients.length === 0) {
    return (
      <Card className={cn("border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50", className)}>
        <CardHeader className="pb-4">
          <CardTitle className="text-xl font-bold text-blue-900 flex items-center justify-between">
            <div className="flex items-center">
              <UserCheck className="h-6 w-6 mr-3 text-blue-600" />
              Sala de Espera Virtual
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={loading || refreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", (loading || refreshing) && "animate-spin")} />
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-2">No hay pacientes en espera actualmente</p>
            <p className="text-sm text-gray-400">Los pacientes aparecerán aquí cuando lleguen a su cita</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn("border-2 border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-lg", className)}>
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-bold text-blue-900 flex items-center justify-between">
          <div className="flex items-center">
            <UserCheck className="h-6 w-6 mr-3 text-blue-600" />
            Sala de Espera Virtual
            <Badge className="ml-3 bg-blue-600 text-white text-sm px-3 py-1">
              {patients.length} paciente{patients.length !== 1 ? 's' : ''}
            </Badge>
          </div>
          <div className="flex items-center gap-2">
            {showViewAllLink && userRole === 'assistant' && (
              <Link 
                href={viewAllHref}
                className="text-sm text-blue-600 hover:text-blue-800 transition-colors font-medium"
              >
                Ver sala completa →
              </Link>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={loading || refreshing}
              className="h-8 w-8 p-0"
            >
              <RefreshCw className={cn("h-4 w-4", (loading || refreshing) && "animate-spin")} />
            </Button>
          </div>
        </CardTitle>
        <p className="text-sm text-blue-700 mt-1">
          {userRole === 'doctor' 
            ? 'Sus pacientes que han llegado y están esperando ser atendidos'
            : 'Pacientes que han llegado y están esperando ser atendidos'
          }
        </p>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {patientsToShow.map((patient, index) => {
            const arrivalTime = new Date(patient.checkedInAt || patient.startTime);
            const appointmentTime = new Date(patient.startTime);
            const waitingMinutes = getWaitingTime(patient.checkedInAt, patient.startTime);
            const status = getWaitingStatus(waitingMinutes);
            const isLate = arrivalTime > appointmentTime;
            
            return (
              <div 
                key={patient.id}
                className={cn(
                  "p-4 rounded-xl border-2 transition-all duration-200 hover:shadow-md",
                  status.bgColor,
                  status.borderColor
                )}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className={cn(
                      "h-12 w-12 rounded-full flex items-center justify-center text-white text-lg font-bold shadow-md",
                      status.color
                    )}>
                      {index + 1}
                    </div>
                    
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-1">
                        <div className="font-semibold text-gray-900 text-base">
                          {patient.patientFirstName} {patient.patientLastName}
                        </div>
                        {status.level === 'critical' && (
                          <Badge className="bg-red-600 text-white text-xs animate-pulse">
                            URGENTE
                          </Badge>
                        )}
                        {status.level === 'warning' && (
                          <Badge className="bg-orange-600 text-white text-xs">
                            PRIORIDAD
                          </Badge>
                        )}
                        {isLate && (
                          <Badge variant="outline" className="text-red-600 border-red-300 text-xs">
                            Llegó tarde
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-1">
                        {patient.serviceName || patient.title}
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs text-gray-500">
                        {userRole === 'assistant' && (
                          <div className="flex items-center gap-1">
                            <Stethoscope className="h-3 w-3" />
                            <span>Dr. {patient.doctorFirstName} {patient.doctorLastName}</span>
                          </div>
                        )}
                        <div className="flex items-center gap-1">
                          <Home className="h-3 w-3" />
                          <span>{patient.consultoryName}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>Cita: {format(appointmentTime, 'HH:mm')}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>Llegó: {format(arrivalTime, 'HH:mm')}</span>
                        </div>
                      </div>
                      
                      <div className="mt-2">
                        <span className={cn("text-sm font-medium", status.textColor)}>
                          Esperando: {waitingMinutes}min
                        </span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex flex-col gap-2 ml-4">
                    {userRole === 'doctor' && onStartConsultation && (
                      <Button
                        size="sm"
                        className="bg-green-600 hover:bg-green-700 text-white shadow-sm"
                        onClick={() => onStartConsultation(patient)}
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Iniciar Consulta
                      </Button>
                    )}
                    {onViewDetails && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-blue-600 border-blue-300 hover:bg-blue-50"
                        onClick={() => onViewDetails(patient)}
                      >
                        Ver detalles
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
        
        {hasMorePatients && showViewAllLink && (
          <div className="mt-4 pt-4 border-t border-blue-200 text-center">
            <Link 
              href={viewAllHref}
              className="inline-flex items-center text-sm text-blue-600 hover:text-blue-800 transition-colors font-medium"
            >
              Ver {patients.length - maxPatients} pacientes más
              <ChevronRight className="h-4 w-4 ml-1" />
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
}