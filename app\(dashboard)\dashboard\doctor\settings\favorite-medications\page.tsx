'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Search,
  RefreshCw,
  Download,
  ArrowLeft,
  Pill,
  Eye,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Heart,
} from 'lucide-react';
import { toast } from 'sonner';

interface Medication {
  id: string;
  name: string;
  activeIngredient?: string;
  presentation?: string;
  description?: string;
  isActive: boolean;
  isFavorite: boolean; // Calculado desde el backend
  createdAt: string;
  updatedAt: string;
}

export default function FavoriteMedicationsPage() {
  const router = useRouter();

  // Estados de datos
  const [medications, setMedications] = useState<Medication[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Estados de filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [favoritesFilter, setFavoritesFilter] = useState<'all' | 'favorites' | 'non-favorites'>('all');

  // Estados de paginación
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);

  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Estados de modales
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);

  // Estados de elementos seleccionados
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [selectedItem, setSelectedItem] = useState<Medication | null>(null);

  // Estados de loading para acciones
  const [togglingFavorites, setTogglingFavorites] = useState<Set<string>>(new Set());

  // Componente de icono de ordenamiento
  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // Handler de ordenamiento
  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  // Seleccionar/deseleccionar todos
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedItems(medications.map(item => item.id));
    } else {
      setSelectedItems([]);
    }
  };

  // Seleccionar individual
  const handleSelectItem = (itemId: string, checked: boolean) => {
    if (checked) {
      setSelectedItems(prev => [...prev, itemId]);
    } else {
      setSelectedItems(prev => prev.filter(id => id !== itemId));
    }
  };

  // Fetch de datos (medicamentos con información de favoritos)
  const fetchData = async () => {
    try {
      // Llamar a endpoint que retorna medicamentos con información de favoritos del doctor
      const response = await fetch('/api/doctor/medications-with-favorites');
      if (!response.ok) {
        throw new Error('Error al obtener los datos');
      }
      const data = await response.json();
      setMedications(data.data || []);
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Handle refresh
  const handleRefresh = () => {
    setRefreshing(true);
    fetchData();
  };

  // Toggle favorito
  const toggleFavorite = async (medication: Medication) => {
    const medicationId = medication.id;
    
    // Evitar múltiples requests simultáneos
    if (togglingFavorites.has(medicationId)) return;
    
    setTogglingFavorites(prev => new Set([...prev, medicationId]));
    
    try {
      const response = await fetch('/api/doctor/toggle-favorite-medication', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ medicationId }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error al cambiar estado favorito');
      }

      const result = await response.json();
      
      // Actualizar estado local inmediatamente
      setMedications(prev => prev.map(med => 
        med.id === medicationId 
          ? { ...med, isFavorite: result.isFavorite }
          : med
      ));
      
      toast.success(result.isFavorite ? 'Agregado a favoritos' : 'Removido de favoritos');
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setTogglingFavorites(prev => {
        const newSet = new Set(prev);
        newSet.delete(medicationId);
        return newSet;
      });
    }
  };

  // Abrir modal de detalles
  const openDetailsDialog = (item: Medication) => {
    setSelectedItem(item);
    setIsDetailsDialogOpen(true);
  };

  // Filtrar datos
  const filteredData = medications.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (item.activeIngredient && item.activeIngredient.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         (item.description && item.description.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesFavorites = favoritesFilter === 'all' || 
      (favoritesFilter === 'favorites' && item.isFavorite) ||
      (favoritesFilter === 'non-favorites' && !item.isFavorite);
    
    return matchesSearch && matchesFavorites && item.isActive; // Solo mostrar medicamentos activos
  });

  // Paginación
  const paginatedData = filteredData.slice((page - 1) * limit, page * limit);
  const totalPages = Math.ceil(filteredData.length / limit);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    setPage(1);
  }, [searchTerm, favoritesFilter]);

  if (loading) {
    return (
      <div className="space-y-6 p-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin" />
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header estándar */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/doctor/settings')}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Catálogo de Medicamentos
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Explora medicamentos y marca tus favoritos para recetas rápidas
              </p>
            </div>
          </div>
        </div>
        
        {/* Botones de acción */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button 
            onClick={() => setFavoritesFilter('favorites')}
            variant={favoritesFilter === 'favorites' ? 'default' : 'outline'}
          >
            <Heart className="h-4 w-4 mr-2" />
            Ver Favoritos
          </Button>
        </div>
      </div>

      {/* Card de filtros de búsqueda */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            {/* Campo de búsqueda principal */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre o principio activo..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filtros adicionales */}
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
              {/* Filtro por favoritos */}
              <Select value={favoritesFilter} onValueChange={setFavoritesFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Filtrar favoritos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los medicamentos</SelectItem>
                  <SelectItem value="favorites">Solo favoritos</SelectItem>
                  <SelectItem value="non-favorites">Solo no favoritos</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card principal con grid de datos */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Pill className="h-5 w-5 text-purple-600" />
            Medicamentos ({filteredData.length} 
            {favoritesFilter === 'favorites' && ` favoritos`}
            {favoritesFilter === 'non-favorites' && ` no favoritos`})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Vista Desktop */}
          <div className="hidden lg:block overflow-x-auto">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    {/* Checkbox de selección con COLOR VERDE ESTÁNDAR */}
                    <TableHead className="w-12">
                      <Checkbox 
                        className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                        checked={selectedItems.length === paginatedData.length && paginatedData.length > 0}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    
                    {/* Columnas ordenables */}
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Medicamento
                        <SortIcon column="name" />
                      </div>
                    </TableHead>
                    
                    <TableHead className="font-semibold text-gray-700">
                      Principio Activo
                    </TableHead>

                    <TableHead className="font-semibold text-gray-700">
                      Presentación
                    </TableHead>

                    <TableHead className="font-semibold text-gray-700">
                      Descripción
                    </TableHead>

                    <TableHead className="font-semibold text-gray-700 text-center">
                      Favorito
                    </TableHead>
                    
                    {/* Acciones */}
                    <TableHead className="font-semibold text-gray-700 text-right">
                      Acciones
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {paginatedData.map((item) => (
                    <TableRow key={item.id}>
                      <TableCell>
                        <Checkbox
                          className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                          checked={selectedItems.includes(item.id)}
                          onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                        />
                      </TableCell>
                      <TableCell className="font-medium">{item.name}</TableCell>
                      <TableCell>{item.activeIngredient || '-'}</TableCell>
                      <TableCell>{item.presentation || '-'}</TableCell>
                      <TableCell className="max-w-xs truncate">{item.description || '-'}</TableCell>
                      <TableCell className="text-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleFavorite(item)}
                          disabled={togglingFavorites.has(item.id)}
                          className={`${item.isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`}
                        >
                          {togglingFavorites.has(item.id) ? (
                            <RefreshCw className="h-4 w-4 animate-spin" />
                          ) : (
                            <Heart className={`h-4 w-4 ${item.isFavorite ? 'fill-current' : ''}`} />
                          )}
                        </Button>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => openDetailsDialog(item)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Vista Mobile */}
          <div className="lg:hidden space-y-4">
            {paginatedData.map((item) => (
              <Card key={item.id} className="hover:shadow-md transition-all duration-200">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start space-x-3">
                      <Checkbox 
                        className="mt-1 border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                        checked={selectedItems.includes(item.id)}
                        onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
                      />
                      <div className="space-y-1 flex-1">
                        <div className="flex items-center space-x-2">
                          <Pill className="h-4 w-4 text-purple-400" />
                          <span className="font-medium">{item.name}</span>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => toggleFavorite(item)}
                            disabled={togglingFavorites.has(item.id)}
                            className={`ml-2 ${item.isFavorite ? 'text-red-500 hover:text-red-600' : 'text-gray-400 hover:text-red-500'}`}
                          >
                            {togglingFavorites.has(item.id) ? (
                              <RefreshCw className="h-3 w-3 animate-spin" />
                            ) : (
                              <Heart className={`h-3 w-3 ${item.isFavorite ? 'fill-current' : ''}`} />
                            )}
                          </Button>
                        </div>
                        {item.activeIngredient && (
                          <p className="text-sm text-gray-600">
                            Principio activo: {item.activeIngredient}
                          </p>
                        )}
                        {item.presentation && (
                          <p className="text-sm text-gray-600">
                            Presentación: {item.presentation}
                          </p>
                        )}
                        {item.description && (
                          <p className="text-sm text-gray-600 truncate">
                            {item.description}
                          </p>
                        )}
                      </div>
                    </div>
                    {/* Botón de detalles */}
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => openDetailsDialog(item)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Paginación - SOLO visible cuando hay múltiples páginas */}
      {totalPages > 1 && (
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
          {/* Información de registros y selector */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, filteredData.length)} de {filteredData.length} medicamentos
            </div>
            
            {/* Selector de cantidad por página */}
            <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Controles de navegación */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            
            {/* Números de página con puntos suspensivos */}
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}


      {/* Modal de Detalles */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Detalles del Medicamento</DialogTitle>
            <DialogDescription>
              Información detallada del medicamento
            </DialogDescription>
          </DialogHeader>
          
          {selectedItem && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Medicamento</Label>
                  <p className="text-sm text-gray-900">{selectedItem.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Principio Activo</Label>
                  <p className="text-sm text-gray-900">{selectedItem.activeIngredient || 'No especificado'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Presentación</Label>
                  <p className="text-sm text-gray-900">{selectedItem.presentation || 'No especificada'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Estado</Label>
                  <Badge variant={selectedItem.isActive ? "default" : "secondary"}>
                    {selectedItem.isActive ? "Activo" : "Inactivo"}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Es Favorito</Label>
                  <Badge variant={selectedItem.isFavorite ? "default" : "secondary"}>
                    {selectedItem.isFavorite ? "Sí" : "No"}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Fecha de Creación</Label>
                  <p className="text-sm text-gray-900">{new Date(selectedItem.createdAt).toLocaleDateString()}</p>
                </div>
              </div>
              
              {selectedItem.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-700">Descripción</Label>
                  <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">{selectedItem.description}</p>
                  </div>
                </div>
              )}
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

    </div>
  );
}