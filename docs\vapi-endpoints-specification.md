# Especificación de Endpoints VAPI

## Flujo Completo del Agente VAPI

### 1. **Paciente llama** → Agente recopila información básica
### 2. **Agente verifica si es paciente existente** → Búsqueda por teléfono/nombre
### 3. **Agente consulta horarios** → Muestra 3 opciones al paciente  
### 4. **Paciente escoge horario** → Agente crea la cita
### 5. **Agente da shortCode** → Paciente puede confirmar en web
### 6. **Si reagenda** → Cancelar + crear nueva cita

---

## Endpoint 0: Verificar Paciente Existente

### `POST /api/vapi/verificar-paciente`

**Propósito**: Determinar si el paciente ya existe en el sistema

**Headers requeridos**:
```http
x-vapi-key: [API_KEY_VAPI]
Content-Type: application/json
```

**Body**:
```json
{
  "telefono": "+502 1234-5678",
  "nombres": "<PERSON>",
  "apellidos": "<PERSON>"
}
```

**Lógica del Endpoint**:
1. **Buscar por teléfono** (principal)
2. **Si no encuentra, buscar por nombre completo** (fallback)
3. **Verificar múltiples coincidencias** (desambiguación)

**Response - Paciente Existente**:
```json
{
  "success": true,
  "esNuevo": false,
  "paciente": {
    "id": "patient_abc123",
    "nombres": "Juan Carlos",
    "apellidos": "Pérez García",
    "telefono": "+502 1234-5678",
    "email": "<EMAIL>",
    "edad": 25,
    "tieneEmail": true,
    "esMenor": false,
    "ultimaCita": "2024-11-15",
    "totalCitas": 3
  },
  "mensaje": "Paciente encontrado: Juan Carlos Pérez García. Última cita: 15 de noviembre."
}
```

**Response - Paciente Nuevo**:
```json
{
  "success": true,
  "esNuevo": true,
  "mensaje": "Paciente nuevo. Procederemos a crear su perfil."
}
```

**Response - Múltiples Coincidencias**:
```json
{
  "success": true,
  "esNuevo": false,
  "multipleCoincidencias": true,
  "pacientes": [
    {
      "id": "patient_abc123",
      "nombres": "Juan Carlos",
      "apellidos": "Pérez García",
      "telefono": "+502 1234-5678",
      "ultimaCita": "2024-11-15"
    },
    {
      "id": "patient_def456", 
      "nombres": "Juan Carlos",
      "apellidos": "Pérez López",
      "telefono": "+502 1234-5678",
      "ultimaCita": "2024-10-20"
    }
  ],
  "mensaje": "Encontramos 2 pacientes con datos similares. ¿Cuál es correcto?"
}
```

---

## Endpoint 1: Consultar Horarios Disponibles

### `GET /api/vapi/horarios-disponibles`

**Propósito**: Obtener 3 opciones de horarios para que el paciente escoja

**Headers requeridos**:
```http
x-vapi-key: [API_KEY_VAPI]
```

**Query Parameters**:
```
doctorId: string (hardcoded en prompt VAPI)
consultorioId: string (hardcoded en prompt VAPI) 
fecha?: string (YYYY-MM-DD, opcional - default: próximos 7 días)
```

**Response**:
```json
{
  "success": true,
  "horarios": [
    {
      "slotId": "doc123-1704196800000",
      "fecha": "2024-01-02",
      "hora": "09:00 AM", 
      "disponible": true,
      "doctorNombre": "Dra. Barbara"
    },
    {
      "slotId": "doc123-1704200400000", 
      "fecha": "2024-01-02",
      "hora": "10:00 AM",
      "disponible": true,
      "doctorNombre": "Dra. Barbara"
    },
    {
      "slotId": "doc123-1704204000000",
      "fecha": "2024-01-02", 
      "hora": "11:00 AM",
      "disponible": true,
      "doctorNombre": "Dra. Barbara"
    }
  ]
}
```

---

## Endpoint 2: Crear Cita

### `POST /api/vapi/crear-cita`

**Propósito**: Crear cita (y paciente/guardián si es nuevo) con shortCode

**Headers requeridos**:
```http
x-vapi-key: [API_KEY_VAPI]
Content-Type: application/json
```

**Body - Paciente Existente**:
```json
{
  "slotId": "doc123-1704196800000",
  "pacienteExistente": {
    "id": "patient_abc123",
    "motivoConsulta": "Control mensual diabetes"
  }
}
```

**Body - Paciente Nuevo**:
```json
{
  "slotId": "doc123-1704196800000",
  "pacienteNuevo": {
    "nombres": "Juan Carlos",
    "apellidos": "Pérez García", 
    "telefono": "+502 1234-5678",
    "edad": 16,
    "motivoConsulta": "Dolor de garganta"
  },
  "guardian": {
    "nombres": "María Elena",
    "apellidos": "García López",
    "telefono": "+502 8765-4321", 
    "relacion": "madre"
  }
}
```

**Lógica del Endpoint**:

### **Si es Paciente Existente**:
1. **Validar slot disponible**
2. **Verificar que paciente existe y está activo**
3. **Crear cita**:
   - Estado: `pending_confirmation`
   - Generar shortCode único
   - Usar pacienteId existente
4. **Reservar slot**

### **Si es Paciente Nuevo**:
1. **Validar slot disponible**
2. **Determinar si es menor** (edad < 18)
3. **Crear paciente**:
   - Email temporal: `<EMAIL>`
   - Fecha nacimiento aproximada: año actual - edad
4. **Crear guardián** (si es menor):
   - Email temporal: `<EMAIL>` 
   - Crear relación guardián-paciente
5. **Crear cita**:
   - Estado: `pending_confirmation`
   - Generar shortCode único
   - Asignar paciente y guardián
6. **Reservar slot**

**Response**:
```json
{
  "success": true,
  "data": {
    "citaId": "cita_abc123",
    "shortCode": "SGC12345",
    "codigoFonetico": "S de sol, G de gato, C de casa, 1, 2, 3, 4, 5",
    "pacienteNombre": "Juan Carlos Pérez García",
    "doctorNombre": "Dra. Barbara Hernández",
    "fecha": "martes, 2 de enero",
    "hora": "9:00 AM",
    "consultorio": "Consultorio Principal",
    "linkConfirmacion": "www.doctorabarbara.com/confirmar",
    "validoHasta": "24 horas",
    "mensaje": "Cita agendada para Juan Carlos. Código: S de sol, G de gato, C de casa, 1, 2, 3, 4, 5. Confirme en www.doctorabarbara.com/confirmar en las próximas 24 horas."
  }
}
```

---

## Endpoint 3: Cancelar Cita

### `DELETE /api/vapi/cancelar-cita`

**Propósito**: Cancelar cita existente y liberar el horario

**Headers requeridos**:
```http
x-vapi-key: [API_KEY_VAPI]
Content-Type: application/json
```

**Body**:
```json
{
  "shortCode": "SGC12345",
  "motivo": "Reagendamiento solicitado por paciente"
}
```

**Lógica del Endpoint**:

1. **Buscar cita** por shortCode
2. **Validar estado** (solo cancelar si está `pending_confirmation` o `confirmed`)
3. **Cambiar estado** a `cancelled`
4. **Liberar slot** (marcar horario como disponible)
5. **Log del evento** (quién canceló, motivo)

**Response**:
```json
{
  "success": true,
  "data": {
    "citaCancelada": "SGC12345",
    "pacienteNombre": "Juan Carlos Pérez García", 
    "fechaOriginal": "martes, 2 de enero",
    "horaOriginal": "9:00 AM",
    "horarioLiberado": true,
    "mensaje": "Cita cancelada exitosamente. El horario está disponible nuevamente."
  }
}
```

---

## Endpoint 4: Consultar Estado de Cita

### `GET /api/vapi/estado-cita/:shortCode`

**Propósito**: Verificar estado actual de una cita (para debugging/seguimiento)

**Headers requeridos**:
```http
x-vapi-key: [API_KEY_VAPI]
```

**Response**:
```json
{
  "success": true,
  "data": {
    "shortCode": "SGC12345",
    "estado": "pending_confirmation",
    "pacienteNombre": "Juan Carlos Pérez García",
    "fecha": "2024-01-02",
    "hora": "09:00",
    "doctorNombre": "Dra. Barbara",
    "confirmadoEn": null,
    "emailCapturado": null,
    "validoHasta": "2024-01-03T09:00:00Z"
  }
}
```

---

## Flujo de Reagendamiento

**Cuando paciente quiere cambiar horario**:

1. **Agente llama**: `DELETE /api/vapi/cancelar-cita` (cita actual)
2. **Agente llama**: `GET /api/vapi/horarios-disponibles` (nuevas opciones)
3. **Paciente escoge**: Nueva opción
4. **Agente llama**: `POST /api/vapi/crear-cita` (nueva cita)
5. **Agente informa**: Nuevo shortCode al paciente

---

## Configuración en Prompt VAPI

**Variables hardcoded en el prompt**:
```
DOCTOR_ID = "doctor_123abc"
CONSULTORIO_ID = "consultorio_456def"  
API_BASE_URL = "https://doctorabarbara.com/api/vapi"
CONFIRMACION_URL = "www.doctorabarbara.com/confirmar"
```

**Headers que debe usar**:
```
x-vapi-key: [SECRET_VAPI_KEY]
```

---

## Casos Edge y Validaciones

### Validaciones por Endpoint

**Horarios Disponibles**:
- Doctor existe y está activo
- Consultorio existe y está activo  
- Fecha no es pasada
- API key válida

**Crear Cita**:
- Slot aún disponible (race condition)
- Nombres y teléfonos válidos
- Edad entre 0-120 años
- No hay citas duplicadas para el mismo paciente en el mismo día

**Cancelar Cita**:
- ShortCode existe
- Cita no está ya cancelada o completada
- Dentro del tiempo límite (24h para VAPI)

### Manejo de Errores

**Errores Comunes**:
```json
{
  "success": false,
  "error": "SLOT_NOT_AVAILABLE",
  "message": "El horario seleccionado ya no está disponible",
  "code": 409
}
```

```json
{
  "success": false, 
  "error": "INVALID_API_KEY",
  "message": "API key inválida o faltante",
  "code": 401
}
```

```json
{
  "success": false,
  "error": "APPOINTMENT_EXPIRED", 
  "message": "La cita expiró (más de 24 horas sin confirmar)",
  "code": 410
}
```

---

**Versión**: 1.0  
**Fecha**: 2025-01-02  
**Estado**: Especificación completa - Listo para implementación