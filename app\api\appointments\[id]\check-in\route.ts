import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, userRoles, notifications, user } from '@/db/schema';
import { eq, and, or } from 'drizzle-orm';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: appointmentId } = await params;

    // Obtener información completa de la cita
    const appointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (!appointment.length) {
      return NextResponse.json({ 
        error: 'Cita no encontrada' 
      }, { status: 404 });
    }

    const appointmentData = appointment[0];

    // Verificar permisos: el usuario debe ser el paciente de la cita, o un asistente/doctor
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    const isPatient = appointmentData.patientId === userId;
    const isStaff = userRole.length > 0 && ['assistant', 'doctor', 'admin'].includes(userRole[0].role);

    if (!isPatient && !isStaff) {
      return NextResponse.json({ 
        error: 'No tienes permiso para registrar esta llegada' 
      }, { status: 403 });
    }

    // Si es paciente, solo puede hacer check-in de su propia cita
    if (isPatient && appointmentData.patientId !== userId) {
      return NextResponse.json({ 
        error: 'Solo puedes registrar tu propia llegada' 
      }, { status: 403 });
    }

    // Verificar que la cita esté en estado válido para check-in
    if (appointmentData.status !== 'confirmed' && appointmentData.status !== 'pending_confirmation') {
      return NextResponse.json({ 
        error: 'Solo se puede registrar llegada de citas confirmadas o pendientes de confirmación' 
      }, { status: 400 });
    }

    // Verificar que no haya sido registrada ya
    if (appointmentData.checkedInAt) {
      return NextResponse.json({ 
        error: 'La llegada del paciente ya fue registrada' 
      }, { status: 400 });
    }

    // Registrar la llegada
    const updateResult = await db
      .update(appointments)
      .set({
        status: 'checked_in',
        checkedInAt: new Date(),
        checkedInBy: userId,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    console.log('✅ Check-in registrado exitosamente:', { appointmentId, userId });

    // Por ahora, saltamos las notificaciones para evitar el error
    /*
    // Crear notificaciones automáticas
    const patientName = `${appointmentData.patientFirstName || ''} ${appointmentData.patientLastName || ''}`.trim();
    const doctorName = `Dr. ${appointmentData.doctorFirstName || ''} ${appointmentData.doctorLastName || ''}`.trim();
    const serviceName = appointmentData.serviceName || appointmentData.title || 'Consulta';
    const appointmentTime = new Date(appointmentData.startTime).toLocaleTimeString('es-ES', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });

    try {
      // Obtener usuarios que deben recibir notificaciones (doctor y asistentes)
      const usersToNotify = await db
        .select({
          userId: userRoles.userId,
          role: userRoles.role
        })
        .from(userRoles)
        .where(
          and(
            eq(userRoles.status, 'active'),
            or(
              eq(userRoles.role, 'assistant'),
              and(
                eq(userRoles.role, 'doctor'),
                eq(userRoles.userId, appointmentData.doctorId)
              )
            )
          )
        );

      // Crear notificaciones para cada usuario
      const notificationPromises = usersToNotify.map(async (userToNotify) => {
        const isDoctor = userToNotify.role === 'doctor';
        const title = isDoctor 
          ? `Paciente llegó a su consulta`
          : `Nuevo paciente en sala de espera`;
        
        const message = isDoctor
          ? `${patientName} ha llegado para su cita de ${serviceName} programada a las ${appointmentTime}`
          : `${patientName} llegó para su cita con ${doctorName} (${serviceName}) programada a las ${appointmentTime}`;

        return db.insert(notifications).values({
          id: crypto.randomUUID(),
          userId: userToNotify.userId,
          type: 'check_in',
          title,
          message,
          data: JSON.stringify({
            appointmentId: appointmentData.id,
            patientName,
            doctorName,
            time: appointmentTime,
            serviceName
          }),
          read: false
        });
      });

      await Promise.all(notificationPromises);
      console.log(`✅ Notificaciones creadas para ${usersToNotify.length} usuarios sobre check-in de ${patientName}`);
      
    } catch (notificationError) {
      console.error('Error creando notificaciones de check-in:', notificationError);
      // No fallar la operación principal si las notificaciones fallan
    }
    */

    return NextResponse.json({
      success: true,
      message: 'Llegada del paciente registrada exitosamente',
      data: {
        appointmentId,
        status: 'checked_in',
        checkedInAt: new Date(),
        checkedInBy: userId
      }
    });

  } catch (error: any) {
    console.error('Error registering check-in:', error);
    console.error('Error stack:', error?.stack);
    
    // Manejar el error de forma segura
    const errorMessage = error?.message || 'Error interno del servidor';
    
    return NextResponse.json({ 
      success: false,
      error: errorMessage
    }, { status: 500 });
  }
}