'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X, Plus, Users, Mail, Phone, Heart, UserCheck, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface Guardian {
  id?: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  relationship: string;
  isPrimary: boolean;
  canMakeDecisions: boolean;
  sendInvitation: boolean;
}

interface GuardianManagementProps {
  patientId: string;
  patientName: string;
  patientAge?: number;
  isMinor: boolean;
  onGuardiansCreated: (guardians: Guardian[]) => void;
  onCancel: () => void;
}

const RELATIONSHIPS = [
  { value: 'padre', label: 'Padre' },
  { value: 'madre', label: 'Madre' },
  { value: 'abuelo', label: 'Abuelo' },
  { value: 'abuela', label: 'Abuela' },
  { value: 'tio', label: 'Tío' },
  { value: 'tia', label: 'Tía' },
  { value: 'hermano', label: 'Hermano' },
  { value: 'hermana', label: 'Hermana' },
  { value: 'tutor_legal', label: 'Tutor Legal' },
  { value: 'cuidador', label: 'Cuidador' },
  { value: 'otro', label: 'Otro' }
];

export function GuardianManagement({
  patientId,
  patientName,
  patientAge,
  isMinor,
  onGuardiansCreated,
  onCancel
}: GuardianManagementProps) {
  const [guardians, setGuardians] = useState<Guardian[]>([{
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    relationship: '',
    isPrimary: true,
    canMakeDecisions: true,
    sendInvitation: false
  }]);
  const [loading, setLoading] = useState(false);

  const handleGuardianChange = (index: number, field: keyof Guardian, value: any) => {
    setGuardians(prev => prev.map((guardian, i) => 
      i === index ? { ...guardian, [field]: value } : guardian
    ));
  };

  const addGuardian = () => {
    const newGuardian: Guardian = {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      relationship: '',
      isPrimary: false,
      canMakeDecisions: false,
      sendInvitation: false
    };
    setGuardians(prev => [...prev, newGuardian]);
  };

  const removeGuardian = (index: number) => {
    if (guardians.length > 1) {
      setGuardians(prev => prev.filter((_, i) => i !== index));
    }
  };

  const validateForm = (): boolean => {
    // Verificar que haya al menos un encargado
    if (guardians.length === 0) {
      toast.error('Debe agregar al menos un encargado');
      return false;
    }

    // Verificar campos requeridos
    for (let i = 0; i < guardians.length; i++) {
      const guardian = guardians[i];
      if (!guardian.firstName.trim()) {
        toast.error(`Nombre del encargado ${i + 1} es requerido`);
        return false;
      }
      if (!guardian.lastName.trim()) {
        toast.error(`Apellido del encargado ${i + 1} es requerido`);
        return false;
      }
      if (!guardian.email.trim()) {
        toast.error(`Email del encargado ${i + 1} es requerido`);
        return false;
      }
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(guardian.email)) {
        toast.error(`Email del encargado ${i + 1} no es válido`);
        return false;
      }
      if (!guardian.relationship) {
        toast.error(`Relación del encargado ${i + 1} es requerida`);
        return false;
      }
    }

    // Verificar que haya exactamente un encargado principal
    const primaryGuardians = guardians.filter(g => g.isPrimary);
    if (primaryGuardians.length === 0) {
      toast.error('Debe haber al menos un encargado principal');
      return false;
    }
    if (primaryGuardians.length > 1) {
      toast.error('Solo puede haber un encargado principal');
      return false;
    }

    return true;
  };

  const handleSubmit = async () => {
    if (!validateForm()) return;

    setLoading(true);
    try {
      const response = await fetch(`/api/patients/${patientId}/guardians`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          guardians: guardians.map(guardian => ({
            ...guardian,
            patientId
          }))
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(`${guardians.length} encargado(s) agregado(s) exitosamente`);
        
        // Mostrar información sobre invitaciones
        const guardiansWithInvitation = guardians.filter(g => g.sendInvitation);
        if (guardiansWithInvitation.length > 0) {
          setTimeout(() => {
            toast.info(
              `Se han enviado ${guardiansWithInvitation.length} invitación(es) por email`,
              { duration: 6000 }
            );
          }, 1000);
        }

        onGuardiansCreated(guardians);
      } else {
        toast.error(result.error || 'Error al crear encargados');
      }
    } catch (error) {
      console.error('Error creating guardians:', error);
      toast.error('Error de conexión con el servidor');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card className="bg-orange-50 border-orange-200">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5 text-orange-600" />
            Gestión de Encargados
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <p className="text-sm text-orange-800">
              <strong>Paciente:</strong> {patientName} 
              {patientAge && <span> ({patientAge} años)</span>}
            </p>
            {isMinor && (
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <span className="text-sm text-orange-700">
                  Como es menor de edad, debe tener al menos un encargado principal
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Lista de Encargados */}
      {guardians.map((guardian, index) => (
        <Card key={index} className="border-gray-200">
          <CardHeader className="pb-4">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg flex items-center gap-2">
                <UserCheck className="h-5 w-5 text-blue-600" />
                Encargado {index + 1}
                {guardian.isPrimary && (
                  <Badge variant="default" className="bg-blue-100 text-blue-800">
                    Principal
                  </Badge>
                )}
              </CardTitle>
              {guardians.length > 1 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removeGuardian(index)}
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <X className="h-4 w-4" />
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Información Personal */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`firstName-${index}`}>Nombre *</Label>
                <Input
                  id={`firstName-${index}`}
                  value={guardian.firstName}
                  onChange={(e) => handleGuardianChange(index, 'firstName', e.target.value)}
                  placeholder="Nombre del encargado"
                />
              </div>
              <div>
                <Label htmlFor={`lastName-${index}`}>Apellido *</Label>
                <Input
                  id={`lastName-${index}`}
                  value={guardian.lastName}
                  onChange={(e) => handleGuardianChange(index, 'lastName', e.target.value)}
                  placeholder="Apellido del encargado"
                />
              </div>
            </div>

            {/* Información de Contacto */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor={`email-${index}`}>Email *</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id={`email-${index}`}
                    type="email"
                    value={guardian.email}
                    onChange={(e) => handleGuardianChange(index, 'email', e.target.value)}
                    placeholder="<EMAIL>"
                    className="pl-10"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor={`phone-${index}`}>Teléfono</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id={`phone-${index}`}
                    value={guardian.phone}
                    onChange={(e) => handleGuardianChange(index, 'phone', e.target.value)}
                    placeholder="(502) 1234-5678"
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* Relación */}
            <div>
              <Label htmlFor={`relationship-${index}`}>Relación con el paciente *</Label>
              <Select 
                value={guardian.relationship} 
                onValueChange={(value) => handleGuardianChange(index, 'relationship', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar relación" />
                </SelectTrigger>
                <SelectContent>
                  {RELATIONSHIPS.map(rel => (
                    <SelectItem key={rel.value} value={rel.value}>
                      {rel.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Opciones */}
            <div className="space-y-3 bg-gray-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`isPrimary-${index}`}
                  checked={guardian.isPrimary}
                  onCheckedChange={(checked) => {
                    // Solo uno puede ser principal
                    if (checked) {
                      setGuardians(prev => prev.map((g, i) => ({
                        ...g,
                        isPrimary: i === index
                      })));
                    } else if (guardians.filter(g => g.isPrimary).length === 1) {
                      // No permitir desmarcar si es el único principal
                      toast.error('Debe haber al menos un encargado principal');
                    }
                  }}
                />
                <Label htmlFor={`isPrimary-${index}`} className="text-sm">
                  Encargado principal
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`canMakeDecisions-${index}`}
                  checked={guardian.canMakeDecisions}
                  onCheckedChange={(checked) => handleGuardianChange(index, 'canMakeDecisions', checked)}
                />
                <Label htmlFor={`canMakeDecisions-${index}`} className="text-sm">
                  Puede tomar decisiones médicas
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id={`sendInvitation-${index}`}
                  checked={guardian.sendInvitation}
                  onCheckedChange={(checked) => handleGuardianChange(index, 'sendInvitation', checked)}
                />
                <Label htmlFor={`sendInvitation-${index}`} className="text-sm">
                  Enviar invitación para crear cuenta en el sistema
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}

      {/* Botón Agregar Encargado */}
      <div className="text-center">
        <Button
          variant="outline"
          onClick={addGuardian}
          className="border-dashed"
        >
          <Plus className="h-4 w-4 mr-2" />
          Agregar otro encargado
        </Button>
      </div>

      {/* Botones de Acción */}
      <div className="flex flex-col sm:flex-row gap-3 sm:justify-end">
        <Button
          variant="outline"
          onClick={onCancel}
          disabled={loading}
        >
          Cancelar
        </Button>
        <Button
          onClick={handleSubmit}
          disabled={loading}
          className="bg-blue-600 hover:bg-blue-700"
        >
          {loading ? (
            <>
              <Heart className="h-4 w-4 mr-2 animate-pulse" />
              Guardando...
            </>
          ) : (
            <>
              <UserCheck className="h-4 w-4 mr-2" />
              Guardar Encargados
            </>
          )}
        </Button>
      </div>
    </div>
  );
}