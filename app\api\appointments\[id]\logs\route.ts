import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointmentLogs, appointments, user } from '@/db/schema';
import { eq, desc } from 'drizzle-orm';

// GET - Obtener logs de una cita específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const appointmentId = params.id;

    // Verificar que la cita existe y el usuario tiene acceso
    const appointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    // Obtener todos los logs de la cita
    const logs = await db
      .select({
        id: appointmentLogs.id,
        eventType: appointmentLogs.eventType,
        eventCategory: appointmentLogs.eventCategory,
        title: appointmentLogs.title,
        description: appointmentLogs.description,
        metadata: appointmentLogs.metadata,
        triggeredBy: appointmentLogs.triggeredBy,
        triggeredByRole: appointmentLogs.triggeredByRole,
        previousState: appointmentLogs.previousState,
        newState: appointmentLogs.newState,
        emailType: appointmentLogs.emailType,
        emailRecipient: appointmentLogs.emailRecipient,
        emailStatus: appointmentLogs.emailStatus,
        ipAddress: appointmentLogs.ipAddress,
        userAgent: appointmentLogs.userAgent,
        createdAt: appointmentLogs.createdAt,
        
        // Información del usuario que ejecutó la acción
        triggeredByFirstName: user.firstName,
        triggeredByLastName: user.lastName,
        triggeredByEmail: user.email,
      })
      .from(appointmentLogs)
      .leftJoin(user, eq(appointmentLogs.triggeredBy, user.id))
      .where(eq(appointmentLogs.appointmentId, appointmentId))
      .orderBy(desc(appointmentLogs.createdAt));

    // Agrupar logs por categoría para mejor visualización
    const logsByCategory = logs.reduce((acc, log) => {
      const category = log.eventCategory;
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(log);
      return acc;
    }, {} as Record<string, typeof logs>);

    // Estadísticas de la cita
    const stats = {
      totalEvents: logs.length,
      emailsSent: logs.filter(log => log.eventType === 'email_sent' && log.emailStatus === 'sent').length,
      emailsFailed: logs.filter(log => log.eventType === 'email_sent' && log.emailStatus === 'failed').length,
      statusChanges: logs.filter(log => log.eventType === 'status_changed').length,
      systemEvents: logs.filter(log => log.eventCategory === 'system').length,
      userActions: logs.filter(log => log.eventCategory === 'user_action').length,
    };

    return NextResponse.json({
      success: true,
      data: {
        appointmentId,
        logs,
        logsByCategory,
        stats,
        timeline: logs.map(log => ({
          id: log.id,
          timestamp: log.createdAt,
          title: log.title,
          description: log.description,
          type: log.eventType,
          category: log.eventCategory,
          user: log.triggeredByFirstName && log.triggeredByLastName 
            ? `${log.triggeredByFirstName} ${log.triggeredByLastName}`
            : log.triggeredByRole || 'Sistema',
          metadata: log.metadata
        }))
      }
    });

  } catch (error) {
    console.error('Error fetching appointment logs:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}