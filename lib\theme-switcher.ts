/**
 * Sistema de Temas Unificado
 * 
 * Este archivo proporciona utilidades para cambiar la paleta de colores
 * de toda la aplicación de forma fácil y segura.
 */

export type ThemeName = 'verde' | 'pantone' | 'azul';

export interface ThemeConfig {
  name: string;
  description: string;
  colors: {
    primary: string;
    primaryForeground: string;
    secondary: string;
    secondaryForeground: string;
    accent: string;
    accentForeground: string;
    ring: string;
  };
}

/**
 * Definición de todas las paletas de colores disponibles
 */
export const themes: Record<ThemeName, ThemeConfig> = {
  verde: {
    name: 'Verde Mundo Pediatra',
    description: 'Paleta original verde del sistema',
    colors: {
      primary: '158 64% 39%', // #059669
      primaryForeground: '0 0% 100%', // #ffffff
      secondary: '172 70% 41%', // #14b8a6
      secondaryForeground: '0 0% 100%', // #ffffff
      accent: '160 79% 41%', // #10b981
      accentForeground: '0 0% 100%', // #ffffff
      ring: '158 64% 39%', // #059669
    },
  },
  pantone: {
    name: 'Colores Pantone',
    description: 'Paleta basada en colores Pantone corporativos',
    colors: {
      primary: '169 38% 73%', // #A1D6CA
      primaryForeground: '220 18% 20%', // #252A36
      secondary: '221 32% 37%', // #3D4E80
      secondaryForeground: '0 0% 100%', // #ffffff
      accent: '51 67% 85%', // #FCEEA8
      accentForeground: '220 18% 20%', // #252A36
      ring: '221 32% 37%', // #3D4E80
    },
  },
  azul: {
    name: 'Azul Moderno',
    description: 'Paleta azul moderna y profesional',
    colors: {
      primary: '221 83% 53%', // #3b82f6
      primaryForeground: '0 0% 100%', // #ffffff
      secondary: '214 95% 93%', // #dbeafe
      secondaryForeground: '221 39% 11%', // #1e293b
      accent: '213 94% 68%', // #60a5fa
      accentForeground: '0 0% 100%', // #ffffff
      ring: '221 83% 53%', // #3b82f6
    },
  },
};

/**
 * Aplica un tema específico modificando las variables CSS
 * 
 * @param themeName - Nombre del tema a aplicar
 * @example
 * ```typescript
 * // Cambiar a la paleta Pantone
 * applyTheme('pantone');
 * 
 * // Cambiar a la paleta azul
 * applyTheme('azul');
 * 
 * // Volver al verde original
 * applyTheme('verde');
 * ```
 */
export function applyTheme(themeName: ThemeName): void {
  const theme = themes[themeName];
  if (!theme) {
    console.error(`Tema "${themeName}" no encontrado`);
    return;
  }

  const root = document.documentElement;
  
  // Aplicar las variables CSS del tema seleccionado
  root.style.setProperty('--theme-primary', theme.colors.primary);
  root.style.setProperty('--theme-primary-foreground', theme.colors.primaryForeground);
  root.style.setProperty('--theme-secondary', theme.colors.secondary);
  root.style.setProperty('--theme-secondary-foreground', theme.colors.secondaryForeground);
  root.style.setProperty('--theme-accent', theme.colors.accent);
  root.style.setProperty('--theme-accent-foreground', theme.colors.accentForeground);
  root.style.setProperty('--theme-ring', theme.colors.ring);

  console.log(`✅ Tema "${theme.name}" aplicado exitosamente`);
}

/**
 * Obtiene el tema actualmente aplicado
 * 
 * @returns El nombre del tema actual o null si no se puede determinar
 */
export function getCurrentTheme(): ThemeName | null {
  const root = document.documentElement;
  const currentPrimary = getComputedStyle(root).getPropertyValue('--theme-primary').trim();
  
  // Buscar qué tema coincide con el color primary actual
  for (const [themeName, theme] of Object.entries(themes)) {
    if (theme.colors.primary === currentPrimary) {
      return themeName as ThemeName;
    }
  }
  
  return null;
}

/**
 * Hook de React para manejar el cambio de temas
 * 
 * @example
 * ```typescript
 * function ThemeSwitcher() {
 *   const { currentTheme, switchTheme, availableThemes } = useTheme();
 *   
 *   return (
 *     <select value={currentTheme || ''} onChange={(e) => switchTheme(e.target.value as ThemeName)}>
 *       {availableThemes.map(theme => (
 *         <option key={theme} value={theme}>{themes[theme].name}</option>
 *       ))}
 *     </select>
 *   );
 * }
 * ```
 */
export function useTheme() {
  const [currentTheme, setCurrentTheme] = React.useState<ThemeName | null>(null);
  
  React.useEffect(() => {
    setCurrentTheme(getCurrentTheme());
  }, []);
  
  const switchTheme = (themeName: ThemeName) => {
    applyTheme(themeName);
    setCurrentTheme(themeName);
  };
  
  return {
    currentTheme,
    switchTheme,
    availableThemes: Object.keys(themes) as ThemeName[],
    themes,
  };
}

// Agregar React import para el hook
declare global {
  const React: any;
}