'use client';

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { GeneralProfileData } from "@/lib/types/onboarding";
import { getLocationLabels } from "@/lib/utils";
import { DateInput } from "@/components/ui/date-input";
import { PhoneInput } from "@/components/ui/phone-input";

interface GeneralInfoFormProps {
  data: GeneralProfileData;
  onDataChange: (data: GeneralProfileData) => void;
  onNext: () => void;
  onBack: () => void;
}

interface Country {
  id: number;
  name: string;
  code: string;
  phoneCode: string;
}

interface Department {
  id: number;
  name: string;
  countryId: number;
}

interface Municipality {
  id: number;
  name: string;
  departmentId: number;
}

interface Occupation {
  id: number;
  name: string;
}

interface Relationship {
  id: number;
  name: string;
}

export function GeneralInfoForm({ data, onDataChange, onNext, onBack }: GeneralInfoFormProps) {
  // Estados para catálogos
  const [countries, setCountries] = useState<Country[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [municipalities, setMunicipalities] = useState<Municipality[]>([]);
  const [occupations, setOccupations] = useState<Occupation[]>([]);
  const [relationships, setRelationships] = useState<Relationship[]>([]);
  
  // Estados para campos dependientes
  const [filteredDepartments, setFilteredDepartments] = useState<Department[]>([]);
  const [filteredMunicipalities, setFilteredMunicipalities] = useState<Municipality[]>([]);
  
  // Etiquetas dinámicas basadas en el país seleccionado
  const locationLabels = getLocationLabels(data.countryId);

  // Cargar catálogos al montar el componente
  useEffect(() => {
    const loadCatalogs = async () => {
      try {
        // Cargar todos los catálogos en paralelo
        const [
          countriesRes,
          departmentsRes, 
          municipalitiesRes,
          occupationsRes,
          relationshipsRes
        ] = await Promise.all([
          fetch('/api/catalogs/countries'),
          fetch('/api/catalogs/departments'),
          fetch('/api/catalogs/municipalities'),
          fetch('/api/catalogs/occupations'),
          fetch('/api/catalogs/relationships')
        ]);

        const [
          countriesData,
          departmentsData,
          municipalitiesData,
          occupationsData,
          relationshipsData
        ] = await Promise.all([
          countriesRes.json(),
          departmentsRes.json(),
          municipalitiesRes.json(),
          occupationsRes.json(),
          relationshipsRes.json()
        ]);

        // Extraer los datos según el formato de respuesta del API
        setCountries(countriesData.data || countriesData || []);
        setDepartments(departmentsData.data || departmentsData || []);
        setMunicipalities(municipalitiesData.data || municipalitiesData || []);
        setOccupations(occupationsData.data || occupationsData || []);
        setRelationships(relationshipsData.data || relationshipsData || []);


      } catch (error) {
        console.error('Error cargando catálogos:', error);
      }
    };

    loadCatalogs();
  }, [data.countryId]);

  // Filtrar departamentos cuando cambia el país
  useEffect(() => {
    if (data.countryId) {
      const filtered = departments.filter(dept => dept.countryId === data.countryId);
      setFilteredDepartments(filtered);
      
      // Limpiar departamento y municipio si no están en el nuevo país
      if (data.departmentId && !filtered.find(d => d.id === data.departmentId)) {
        onDataChange({
          ...data,
          departmentId: undefined,
          municipalityId: undefined
        });
      }
    } else {
      setFilteredDepartments([]);
    }
  }, [data.countryId, departments]);

  // Filtrar municipios cuando cambia el departamento
  useEffect(() => {
    if (data.departmentId) {
      const filtered = municipalities.filter(muni => muni.departmentId === data.departmentId);
      setFilteredMunicipalities(filtered);
      
      // Limpiar municipio si no está en el nuevo departamento
      if (data.municipalityId && !filtered.find(m => m.id === data.municipalityId)) {
        onDataChange({
          ...data,
          municipalityId: undefined
        });
      }
    } else {
      setFilteredMunicipalities([]);
    }
  }, [data.departmentId, municipalities]);

  const handleInputChange = (field: keyof GeneralProfileData, value: any) => {
    onDataChange({
      ...data,
      [field]: value
    });
  };

  const handleCountryChange = (countryId: string) => {
    onDataChange({
      ...data,
      countryId: parseInt(countryId),
      departmentId: undefined,
      municipalityId: undefined
    });
  };



  const isFormValid = () => {
    // Validaciones básicas de campos requeridos
    return data.firstName && 
           data.lastName && 
           data.dateOfBirth && 
           data.phone && 
           data.countryId && 
           data.departmentId && 
           data.municipalityId &&
           data.occupationId &&
           data.emergencyContact &&
           data.emergencyPhone &&
           data.emergencyRelationshipId;
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Información General</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Información Personal */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="firstName">Nombres *</Label>
            <Input
              id="firstName"
              value={data.firstName || ''}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              placeholder="Ingresa tus nombres"
            />
          </div>
          <div>
            <Label htmlFor="lastName">Apellidos *</Label>
            <Input
              id="lastName"
              value={data.lastName || ''}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              placeholder="Ingresa tus apellidos"
            />
          </div>
        </div>

        {/* Documento */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="documentType">Tipo de Documento</Label>
            <Select value={data.documentType || ''} onValueChange={(value) => handleInputChange('documentType', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona tipo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dpi">DPI</SelectItem>
                <SelectItem value="pasaporte">Pasaporte</SelectItem>
                <SelectItem value="cedula">Cédula</SelectItem>
                <SelectItem value="licencia">Licencia</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="documentNumber">Número de Documento</Label>
            <Input
              id="documentNumber"
              value={data.documentNumber || ''}
              onChange={(e) => handleInputChange('documentNumber', e.target.value)}
              placeholder="Número de documento"
            />
          </div>
        </div>

        {/* Información Personal */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <Label htmlFor="dateOfBirth">Fecha de Nacimiento *</Label>
            <DateInput
              value={data.dateOfBirth ? new Date(data.dateOfBirth) : undefined}
              onChange={(date) => handleInputChange('dateOfBirth', date || null)}
              maxDate={new Date()} // No puede ser fecha futura
            />
          </div>
          <div>
            <Label htmlFor="gender">Género</Label>
            <Select value={data.gender || ''} onValueChange={(value) => handleInputChange('gender', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona género" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="masculino">Masculino</SelectItem>
                <SelectItem value="femenino">Femenino</SelectItem>
                <SelectItem value="otro">Otro</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="occupation">Ocupación *</Label>
            <Select 
              value={data.occupationId ? data.occupationId.toString() : ''} 
              onValueChange={(value) => handleInputChange('occupationId', parseInt(value))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecciona ocupación" />
              </SelectTrigger>
              <SelectContent>
                {Array.isArray(occupations) && occupations.map((occupation) => (
                  <SelectItem key={occupation.id} value={occupation.id.toString()}>
                    {occupation.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Localización de Residencia */}
        <div className="space-y-4">
          <div className="border-b pb-2">
            <h3 className="text-lg font-semibold text-gray-700">Dirección de Residencia</h3>
            <p className="text-sm text-gray-500">Información de su lugar de residencia actual</p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label htmlFor="country">País de Residencia *</Label>
              <Select 
                value={data.countryId ? data.countryId.toString() : ''} 
                onValueChange={handleCountryChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona país" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(countries) && countries.map((country) => (
                    <SelectItem key={country.id} value={country.id.toString()}>
                      {country.name} ({country.phoneCode})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="department">
                {locationLabels.level1} *
              </Label>
              <Select 
                value={data.departmentId ? data.departmentId.toString() : ''} 
                onValueChange={(value) => handleInputChange('departmentId', parseInt(value))}
                disabled={!data.countryId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={locationLabels.level1Placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(filteredDepartments) && filteredDepartments.map((department) => (
                    <SelectItem key={department.id} value={department.id.toString()}>
                      {department.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="municipality">
                {locationLabels.level2} *
              </Label>
              <Select 
                value={data.municipalityId ? data.municipalityId.toString() : ''} 
                onValueChange={(value) => handleInputChange('municipalityId', parseInt(value))}
                disabled={!data.departmentId}
              >
                <SelectTrigger>
                  <SelectValue placeholder={locationLabels.level2Placeholder} />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(filteredMunicipalities) && filteredMunicipalities.map((municipality) => (
                    <SelectItem key={municipality.id} value={municipality.id.toString()}>
                      {municipality.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Dirección */}
        <div>
          <Label htmlFor="address">Dirección Completa</Label>
          <Input
            id="address"
            value={data.address || ''}
            onChange={(e) => handleInputChange('address', e.target.value)}
            placeholder="Ingresa tu dirección completa"
          />
        </div>

        {/* Teléfonos con código automático */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Label htmlFor="phone">Teléfono *</Label>
            <PhoneInput
              id="phone"
              value={data.phone || ''}
              onChange={(value) => handleInputChange('phone', value)}
            />
          </div>
          <div>
            <Label htmlFor="alternativePhone">Teléfono Alternativo</Label>
            <PhoneInput
              id="alternativePhone"
              value={data.alternativePhone || ''}
              onChange={(value) => handleInputChange('alternativePhone', value)}
            />
          </div>
        </div>

        {/* Contacto de Emergencia */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Contacto de Emergencia</h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="emergencyContact">Nombre del Contacto *</Label>
              <Input
                id="emergencyContact"
                value={data.emergencyContact || ''}
                onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                placeholder="Nombre completo del contacto"
              />
            </div>
            <div>
              <Label htmlFor="emergencyRelationship">Relación *</Label>
              <Select 
                value={data.emergencyRelationshipId ? data.emergencyRelationshipId.toString() : ''} 
                onValueChange={(value) => handleInputChange('emergencyRelationshipId', parseInt(value))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecciona relación" />
                </SelectTrigger>
                <SelectContent>
                  {Array.isArray(relationships) && relationships.map((relationship) => (
                    <SelectItem key={relationship.id} value={relationship.id.toString()}>
                      {relationship.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="emergencyPhone">Teléfono de Emergencia *</Label>
            <PhoneInput
              id="emergencyPhone"
              value={data.emergencyPhone || ''}
              onChange={(value) => handleInputChange('emergencyPhone', value)}
            />
          </div>
        </div>

        {/* Botones de navegación */}
        <div className="flex justify-between pt-6">
          <Button type="button" variant="outline" onClick={onBack}>
            Anterior
          </Button>
          <Button 
            type="button" 
            onClick={onNext}
            disabled={!isFormValid()}
          >
            Siguiente
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 