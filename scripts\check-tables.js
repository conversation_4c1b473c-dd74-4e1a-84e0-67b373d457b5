// Script para verificar qué tablas existen en la base de datos
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function checkTables() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🔍 Verificando tablas existentes...');
  
  try {
    // Listar todas las tablas
    const tables = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `;
    
    console.log('\n📋 Tablas encontradas:');
    tables.forEach(table => {
      console.log(`  - ${table.table_name}`);
    });
    
    // Verificar específicamente system_config
    const systemConfigExists = tables.some(t => t.table_name === 'system_config');
    
    console.log(`\n🎯 system_config existe: ${systemConfigExists ? '✅ SÍ' : '❌ NO'}`);
    
    if (!systemConfigExists) {
      console.log('\n💡 La tabla system_config no existe. Necesitas:');
      console.log('   1. Verificar que todas las migraciones se aplicaron');
      console.log('   2. Ejecutar: npx drizzle-kit push');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

checkTables();