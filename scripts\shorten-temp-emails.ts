#!/usr/bin/env tsx

import { db } from '../db/drizzle';
import { user } from '../db/schema';
import { like, sql } from 'drizzle-orm';
import { generateShortId } from '../lib/utils';

async function shortenTempEmails() {
  console.log('🔄 Iniciando migración de emails temporales largos...\n');

  try {
    // 1. Buscar usuarios con emails temporales largos
    const usersWithLongTempEmails = await db
      .select({
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(like(user.email, '%@temp.local'));

    console.log(`📊 Encontrados ${usersWithLongTempEmails.length} usuarios con emails temporales`);

    if (usersWithLongTempEmails.length === 0) {
      console.log('✅ No hay emails temporales que acortar');
      return;
    }

    // 2. Filtrar solo los emails que son largos (más de 20 caracteres antes del @)
    const longEmails = usersWithLongTempEmails.filter(u => {
      if (!u.email) return false;
      const beforeAt = u.email.split('@')[0];
      return beforeAt.length > 20; // Si el prefijo es muy largo
    });

    console.log(`📏 ${longEmails.length} emails necesitan ser acortados\n`);

    if (longEmails.length === 0) {
      console.log('✅ Todos los emails temporales ya son de longitud adecuada');
      return;
    }

    // 3. Procesar cada email largo
    for (const userData of longEmails) {
      const oldEmail = userData.email!;
      const isPatient = oldEmail.startsWith('patient_');
      const isGuardian = oldEmail.startsWith('guardian_');
      
      let newEmail: string;
      let attempts = 0;
      const maxAttempts = 10;

      // Generar nuevo email corto único
      do {
        const shortId = generateShortId();
        if (isPatient) {
          newEmail = `patient_${shortId}@temp.local`;
        } else if (isGuardian) {
          newEmail = `guardian_${shortId}@temp.local`;
        } else {
          newEmail = `user_${shortId}@temp.local`;
        }

        // Verificar que no exista
        const existing = await db
          .select({ id: user.id })
          .from(user)
          .where(sql`${user.email} = ${newEmail}`)
          .limit(1);

        if (existing.length === 0) break;
        attempts++;
      } while (attempts < maxAttempts);

      if (attempts >= maxAttempts) {
        console.log(`❌ No se pudo generar email único para ${userData.firstName} ${userData.lastName}`);
        continue;
      }

      // 4. Actualizar en la base de datos
      await db
        .update(user)
        .set({ email: newEmail })
        .where(sql`${user.id} = ${userData.id}`);

      console.log(`✅ ${userData.firstName} ${userData.lastName}:`);
      console.log(`   Antes: ${oldEmail}`);
      console.log(`   Después: ${newEmail}`);
      console.log(`   Reducción: ${oldEmail.length - newEmail.length} caracteres\n`);
    }

    console.log(`🎉 Migración completada exitosamente!`);
    console.log(`📈 ${longEmails.length} emails temporales fueron acortados`);

  } catch (error) {
    console.error('❌ Error en la migración:', error);
    throw error;
  }
}

// Ejecutar la migración
shortenTempEmails().then(() => {
  console.log('\n✅ Script completado');
  process.exit(0);
}).catch(error => {
  console.error('\n❌ Error ejecutando migración:', error);
  process.exit(1);
});