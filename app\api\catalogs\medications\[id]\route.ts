import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { medications } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/catalogs/medications/[id] - Obtener medicamento específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const data = await db
      .select()
      .from(medications)
      .where(eq(medications.id, id))
      .limit(1);

    if (!data.length) {
      return NextResponse.json(
        { error: 'Medicamento no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data[0],
    });

  } catch (error: any) {
    console.error('Error fetching medication:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/catalogs/medications/[id] - Eliminar medicamento
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'logical'; // logical o physical

    // Verificar que el medicamento existe
    const existingMedication = await db
      .select()
      .from(medications)
      .where(eq(medications.id, id))
      .limit(1);

    if (!existingMedication.length) {
      return NextResponse.json(
        { error: 'Medicamento no encontrado' },
        { status: 404 }
      );
    }

    if (type === 'logical') {
      // Eliminación lógica - marcar como inactivo
      await db
        .update(medications)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(medications.id, id));

      return NextResponse.json({
        success: true,
        message: 'Medicamento desactivado exitosamente',
      });

    } else if (type === 'physical') {
      // Eliminación física - borrar registro completamente
      await db
        .delete(medications)
        .where(eq(medications.id, id));

      return NextResponse.json({
        success: true,
        message: 'Medicamento eliminado permanentemente',
      });

    } else {
      return NextResponse.json(
        { error: 'Tipo de eliminación no válido. Use "logical" o "physical"' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error deleting medication:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}