'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X, User, Heart, Shield, AlertCircle } from 'lucide-react';
import { PatientSpecificData } from '@/lib/types/onboarding';

interface PatientSpecificFormProps {
  data: Partial<PatientSpecificData>;
  onChange: (data: Partial<PatientSpecificData>) => void;
  onNext: () => void;
  onBack: () => void;
}

interface Doctor {
  id: string;
  name: string;
  specialty: string;
  consultoryName: string;
}

const BLOOD_TYPES = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];
const COMMON_ALLERGIES = [
  'Penicilina',
  'Aspirina',
  'Mariscos',
  'Nueces',
  'Leche',
  'Huevo',
  'Polvo',
  'Polen',
  'Látex',
  'Anestesia'
];

export function PatientSpecificForm({ data, onChange, onNext, onBack }: PatientSpecificFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(true);
  const [currentAllergy, setCurrentAllergy] = useState('');
  const [currentMedication, setCurrentMedication] = useState('');
  const [currentCondition, setCurrentCondition] = useState('');
  const [age, setAge] = useState<number | null>(null);

  // Cargar doctores disponibles
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        const response = await fetch('/api/onboarding/doctors');
        const result = await response.json();
        if (result.success) {
          setDoctors(result.data);
        }
      } catch (error) {
        console.error('Error cargando doctores:', error);
      } finally {
        setLoadingDoctors(false);
      }
    };

    fetchDoctors();
  }, []);

  // Calcular edad y determinar si es menor
  useEffect(() => {
    if (data.dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(data.dateOfBirth);
      const calculatedAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        setAge(calculatedAge - 1);
      } else {
        setAge(calculatedAge);
      }
      
      const isMinor = calculatedAge < 18;
      if (isMinor !== data.isMinor) {
        onChange({ 
          ...data, 
          isMinor: isMinor,
          guardianRequired: isMinor
        });
      }
    }
  }, [data.dateOfBirth, data.isMinor, onChange]);

  const handleInputChange = (field: keyof PatientSpecificData, value: any) => {
    onChange({ ...data, [field]: value });
    // Limpiar error si existe
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const addAllergy = () => {
    if (currentAllergy.trim()) {
      const currentAllergies = data.allergies || [];
      if (!currentAllergies.includes(currentAllergy.trim())) {
        handleInputChange('allergies', [...currentAllergies, currentAllergy.trim()]);
        setCurrentAllergy('');
      }
    }
  };

  const removeAllergy = (allergyToRemove: string) => {
    const currentAllergies = data.allergies || [];
    handleInputChange('allergies', currentAllergies.filter(a => a !== allergyToRemove));
  };

  const addMedication = () => {
    if (currentMedication.trim()) {
      const currentMedications = data.currentMedications || [];
      if (!currentMedications.includes(currentMedication.trim())) {
        handleInputChange('currentMedications', [...currentMedications, currentMedication.trim()]);
        setCurrentMedication('');
      }
    }
  };

  const removeMedication = (medicationToRemove: string) => {
    const currentMedications = data.currentMedications || [];
    handleInputChange('currentMedications', currentMedications.filter(m => m !== medicationToRemove));
  };

  const addCondition = () => {
    if (currentCondition.trim()) {
      const currentConditions = data.chronicConditions || [];
      if (!currentConditions.includes(currentCondition.trim())) {
        handleInputChange('chronicConditions', [...currentConditions, currentCondition.trim()]);
        setCurrentCondition('');
      }
    }
  };

  const removeCondition = (conditionToRemove: string) => {
    const currentConditions = data.chronicConditions || [];
    handleInputChange('chronicConditions', currentConditions.filter(c => c !== conditionToRemove));
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validaciones requeridas
    if (!data.preferredDoctorId) newErrors.preferredDoctorId = 'Doctor preferido es requerido';

    // Validar que si es menor de edad, se marque como tal
    if (age !== null && age < 18 && !data.isMinor) {
      newErrors.isMinor = 'Debe marcar como menor de edad';
    }

    // Validar seguro si se proporciona
    if (data.insuranceCompany && !data.insuranceNumber?.trim()) {
      newErrors.insuranceNumber = 'Número de seguro es requerido si se especifica la compañía';
    }

    if (data.insuranceNumber && !data.insuranceCompany?.trim()) {
      newErrors.insuranceCompany = 'Compañía de seguro es requerida si se especifica el número';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Información Médica Básica */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Heart className="h-5 w-5 text-green-600" />
            <span>Información Médica Básica</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Edad y estado de menor */}
          {age !== null && (
            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <User className="h-5 w-5 text-blue-600" />
                <span className="font-medium">Edad: {age} años</span>
                {age < 18 && (
                  <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                    Menor de edad
                  </Badge>
                )}
              </div>
              {age < 18 && (
                <div className="mt-2 text-sm text-blue-700">
                  Como es menor de edad, se requerirá un tutor legal para completar el registro.
                </div>
              )}
            </div>
          )}

          {/* Tipo de sangre */}
          <div className="space-y-2">
            <Label>Tipo de Sangre</Label>
            <Select 
              value={data.bloodType} 
              onValueChange={(value) => handleInputChange('bloodType', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Seleccionar tipo de sangre" />
              </SelectTrigger>
              <SelectContent>
                {BLOOD_TYPES.map(type => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Alergias */}
          <div className="space-y-2">
            <Label>Alergias Conocidas</Label>
            <div className="flex space-x-2">
              <Select 
                value={currentAllergy} 
                onValueChange={setCurrentAllergy}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar alergia común" />
                </SelectTrigger>
                <SelectContent>
                  {COMMON_ALLERGIES.map(allergy => (
                    <SelectItem key={allergy} value={allergy}>
                      {allergy}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Button 
                type="button" 
                variant="outline" 
                onClick={addAllergy}
                disabled={!currentAllergy}
              >
                Agregar
              </Button>
            </div>
            
            <div className="flex space-x-2">
              <Input
                placeholder="O escribir alergia específica..."
                value={currentAllergy}
                onChange={(e) => setCurrentAllergy(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addAllergy()}
              />
            </div>
            
            {data.allergies && data.allergies.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {data.allergies.map(allergy => (
                  <Badge 
                    key={allergy} 
                    variant="secondary"
                    className="flex items-center space-x-1"
                  >
                    <span>{allergy}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeAllergy(allergy)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Medicamentos actuales */}
          <div className="space-y-2">
            <Label>Medicamentos Actuales</Label>
            <div className="flex space-x-2">
              <Input
                placeholder="Escribir nombre del medicamento..."
                value={currentMedication}
                onChange={(e) => setCurrentMedication(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addMedication()}
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={addMedication}
                disabled={!currentMedication}
              >
                Agregar
              </Button>
            </div>
            
            {data.currentMedications && data.currentMedications.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {data.currentMedications.map(medication => (
                  <Badge 
                    key={medication} 
                    variant="secondary"
                    className="flex items-center space-x-1"
                  >
                    <span>{medication}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeMedication(medication)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>

          {/* Condiciones crónicas */}
          <div className="space-y-2">
            <Label>Condiciones Crónicas</Label>
            <div className="flex space-x-2">
              <Input
                placeholder="Escribir condición médica..."
                value={currentCondition}
                onChange={(e) => setCurrentCondition(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addCondition()}
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={addCondition}
                disabled={!currentCondition}
              >
                Agregar
              </Button>
            </div>
            
            {data.chronicConditions && data.chronicConditions.length > 0 && (
              <div className="flex flex-wrap gap-2 mt-2">
                {data.chronicConditions.map(condition => (
                  <Badge 
                    key={condition} 
                    variant="secondary"
                    className="flex items-center space-x-1"
                  >
                    <span>{condition}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeCondition(condition)}
                    />
                  </Badge>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Preferencia de Doctor */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5 text-green-600" />
            <span>Preferencia de Doctor</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>Doctor Preferido *</Label>
            {loadingDoctors ? (
              <div className="text-center py-4">Cargando doctores...</div>
            ) : (
              <Select 
                value={data.preferredDoctorId} 
                onValueChange={(value) => handleInputChange('preferredDoctorId', value)}
              >
                <SelectTrigger className={errors.preferredDoctorId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Seleccionar doctor preferido" />
                </SelectTrigger>
                <SelectContent>
                  {doctors.map(doctor => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      <div>
                        <div className="font-medium">{doctor.name}</div>
                        <div className="text-sm text-gray-500">{doctor.specialty}</div>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {errors.preferredDoctorId && <p className="text-red-500 text-sm">{errors.preferredDoctorId}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Información del Seguro */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-green-600" />
            <span>Información del Seguro (Opcional)</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="insuranceCompany">Compañía de Seguro</Label>
              <Input
                id="insuranceCompany"
                value={data.insuranceCompany || ''}
                onChange={(e) => handleInputChange('insuranceCompany', e.target.value)}
                className={errors.insuranceCompany ? 'border-red-500' : ''}
                placeholder="Ej: Seguros Universales"
              />
              {errors.insuranceCompany && <p className="text-red-500 text-size-sm">{errors.insuranceCompany}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="insuranceNumber">Número de Póliza</Label>
              <Input
                id="insuranceNumber"
                value={data.insuranceNumber || ''}
                onChange={(e) => handleInputChange('insuranceNumber', e.target.value)}
                className={errors.insuranceNumber ? 'border-red-500' : ''}
                placeholder="Ej: *********"
              />
              {errors.insuranceNumber && <p className="text-red-500 text-sm">{errors.insuranceNumber}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Información para menores */}
      {age !== null && age < 18 && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-yellow-600" />
              <span>Información para Menores de Edad</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={data.isMinor || false}
                  onCheckedChange={(checked) => {
                    handleInputChange('isMinor', checked);
                    handleInputChange('guardianRequired', checked);
                  }}
                />
                <Label className="text-sm">
                  Confirmo que es menor de edad y requiere tutor legal
                </Label>
              </div>
              
              <div className="text-sm text-yellow-700">
                <strong>Importante:</strong> Para completar el registro, un tutor legal deberá:
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Crear su propia cuenta como Guardian</li>
                  <li>Proporcionar documentos de tutela legal</li>
                  <li>Asociarse con esta cuenta usando un código especial</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <span>← Volver</span>
        </Button>
        <Button 
          onClick={handleNext}
          className="bg-green-600 hover:bg-green-700 flex items-center space-x-2"
        >
          <span>Continuar →</span>
        </Button>
      </div>
    </div>
  );
} 