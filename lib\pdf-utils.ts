import { pdf } from '@react-pdf/renderer';
import { PrescriptionPDF } from '@/components/pdf/prescription-pdf';

interface PrescriptionData {
  medication: string;
  dose: string;
  frequency: string;
  duration: string;
  route: string;
  instructions: string;
}

interface PatientData {
  name: string;
  documentNumber?: string;
  age?: number;
  phone?: string;
}

interface DoctorData {
  name: string;
  medicalLicense?: string;
  specialty?: string;
  phone?: string;
  email?: string;
}

interface ConsultoryData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  logoUrl?: string;
}

interface GeneratePrescriptionPDFOptions {
  prescriptions: PrescriptionData[];
  patient: PatientData;
  doctor: DoctorD<PERSON>;
  consultory: ConsultoryData;
  consultationDate: Date;
  diagnoses?: (string | { code?: string; type?: string; description?: string })[];
}

/**
 * Genera y descarga un PDF de prescripción médica
 */
export const generatePrescriptionPDF = async ({
  prescriptions,
  patient,
  doctor,
  consultory,
  consultationDate,
  diagnoses = []
}: GeneratePrescriptionPDFOptions): Promise<void> => {
  try {
    // Crear el documento PDF
    const pdfDoc = PrescriptionPDF({
      prescriptions,
      patient,
      doctor,
      consultory,
      consultationDate,
      diagnoses
    });

    // Generar el blob del PDF
    const blob = await pdf(pdfDoc).toBlob();

    // Crear el nombre del archivo
    const fileName = `prescripcion_${patient.name.replace(/\s+/g, '_')}_${consultationDate.toISOString().split('T')[0]}.pdf`;

    // Crear URL temporal para descarga
    const url = URL.createObjectURL(blob);

    // Crear elemento de descarga temporal
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    link.style.display = 'none';

    // Agregar al DOM, hacer clic y remover
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Limpiar URL temporal
    URL.revokeObjectURL(url);

  } catch (error) {
    console.error('Error generando PDF de prescripción:', error);
    throw new Error('No se pudo generar el PDF de la prescripción');
  }
};

/**
 * Genera el PDF como blob para uso en APIs o vista previa
 */
export const generatePrescriptionPDFBlob = async ({
  prescriptions,
  patient,
  doctor,
  consultory,
  consultationDate,
  diagnoses = []
}: GeneratePrescriptionPDFOptions): Promise<Blob> => {
  try {
    const pdfDoc = PrescriptionPDF({
      prescriptions,
      patient,
      doctor,
      consultory,
      consultationDate,
      diagnoses
    });

    return await pdf(pdfDoc).toBlob();
  } catch (error) {
    console.error('Error generando blob PDF de prescripción:', error);
    throw new Error('No se pudo generar el PDF de la prescripción');
  }
};

/**
 * Calcula la edad a partir de la fecha de nacimiento
 */
export const calculateAge = (dateOfBirth: Date): number => {
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
};

/**
 * Valida que los datos requeridos para el PDF estén presentes
 */
export const validatePrescriptionData = ({
  prescriptions,
  patient,
  doctor,
  consultory
}: Omit<GeneratePrescriptionPDFOptions, 'consultationDate'>): string[] => {
  const errors: string[] = [];

  // Validar prescripciones
  if (!prescriptions || prescriptions.length === 0) {
    errors.push('Se requiere al menos una prescripción');
  } else {
    prescriptions.forEach((prescription, index) => {
      if (!prescription.medication?.trim()) {
        errors.push(`Medicamento ${index + 1}: El nombre del medicamento es requerido`);
      }
      if (!prescription.dose?.trim()) {
        errors.push(`Medicamento ${index + 1}: La dosis es requerida`);
      }
      if (!prescription.frequency?.trim()) {
        errors.push(`Medicamento ${index + 1}: La frecuencia es requerida`);
      }
    });
  }

  // Validar datos del paciente
  if (!patient?.name?.trim()) {
    errors.push('El nombre del paciente es requerido');
  }

  // Validar datos del doctor
  if (!doctor?.name?.trim()) {
    errors.push('El nombre del doctor es requerido');
  }

  // Validar datos del consultorio
  if (!consultory?.name?.trim()) {
    errors.push('El nombre del consultorio es requerido');
  }

  return errors;
};