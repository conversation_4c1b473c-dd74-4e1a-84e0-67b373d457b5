'use client';

import { useAuth } from '@clerk/nextjs';
import { useEffect, useRef, ReactNode } from 'react';

export function useAuthCleanup() {
  const { isSignedIn, isLoaded } = useAuth();
  const wasSignedIn = useRef<boolean | null>(null);

  useEffect(() => {
    // Solo actuar cuando Clerk haya terminado de cargar
    if (!isLoaded) return;

    // Si es la primera vez que se carga y no está autenticado, no limpiar
    // (podría ser una recarga de página)
    if (wasSignedIn.current === null) {
      wasSignedIn.current = isSignedIn;
      return;
    }

    // Solo limpiar si había estado autenticado y ahora no lo está
    // (logout real, no recarga de página)
    if (wasSignedIn.current === true && isSignedIn === false) {
      localStorage.removeItem('userContext');
      localStorage.removeItem('userContextRemembered');
      console.log('🧹 localStorage cleaned on real sign out');
    }

    wasSignedIn.current = isSignedIn;
  }, [isSignedIn, isLoaded]);
}

// Componente provider que usa el hook
export function AuthCleanupProvider({ children }: { children: ReactNode }) {
  useAuthCleanup();
  return <>{children}</>;
}