'use client';

import { useState, useEffect, use } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  ArrowLeft,
  CheckCircle, 
  XCircle, 
  Clock, 
  User,
  FileText,
  Calendar,
  Phone,
  Mail,
  MapPin,
  AlertCircle,
  Heart,
  Briefcase,
  Shield
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';
import { formatPhoneForDisplay } from '@/lib/phone-utils';
import { DocumentViewer } from '@/components/admin/document-viewer';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface RequestDetail {
  id: string;
  userId: string;
  role: string;
  status: string;
  submittedAt: string;
  reviewedAt?: string;
  reviewedBy?: string;
  reviewNotes?: string;
  rejectionReason?: string;
  user: {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    documentType: string;
    documentNumber: string;
    dateOfBirth?: string;
    gender?: string;
    phone?: string;
    alternativePhone?: string;
    address?: string;
    emergencyContact?: string;
    emergencyPhone?: string;
    overallStatus: string;
  };
  roleData?: {
    status: string;
    medicalLicense?: string;
    roleData?: any;
    specialty?: {
      name: string;
      description?: string;
    };
    consultory?: {
      name: string;
      address?: string;
    };
  };
  generalData?: any;
  specificData?: any;
  additionalInfo?: {
    doctorDetails?: Array<{
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      specialty?: string;
      specialtyId?: number;
      consultory?: string;
      consultoryId?: string;
      medicalLicense?: string;
      status?: string;
    }>;
    assignedDoctors?: string[];
  };
}

const roleLabels: { [key: string]: string } = {
  doctor: 'Doctor',
  assistant: 'Asistente',
  patient: 'Paciente',
  guardian: 'Guardian',
  provider: 'Proveedor'
};

const statusColors: { [key: string]: string } = {
  pending: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  reviewing: 'bg-blue-100 text-blue-800'
};

export default function RequestDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const resolvedParams = use(params);
  const [data, setData] = useState<RequestDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  
  // Modal states
  const [showApproveDialog, setShowApproveDialog] = useState(false);
  const [showRejectDialog, setShowRejectDialog] = useState(false);
  const [approveNotes, setApproveNotes] = useState('');
  const [rejectReason, setRejectReason] = useState('');
  const [rejectNotes, setRejectNotes] = useState('');
  const [activeTab, setActiveTab] = useState('general');

  const fetchRequestDetail = async () => {
    try {
      setError('');
      const response = await fetch(`/api/admin/requests/${resolvedParams.id}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar la solicitud');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar solicitud');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchRequestDetail();
  }, [resolvedParams.id]);

  // Si es un paciente y el tab activo es 'specific', cambiar a 'general'
  useEffect(() => {
    if (data?.role === 'patient' && activeTab === 'specific') {
      setActiveTab('general');
    }
  }, [data?.role, activeTab]);

  const handleApprove = async () => {
    if (!data) return;
    
    setProcessing(true);
    try {
      const response = await fetch(`/api/admin/requests/${resolvedParams.id}/approve`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notes: approveNotes })
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('La solicitud ha sido aprobada exitosamente.');
        router.push('/dashboard/admin/requests');
      } else {
        throw new Error(result.message || 'Error al aprobar');
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Error al aprobar la solicitud');
    } finally {
      setProcessing(false);
      setShowApproveDialog(false);
    }
  };

  const handleReject = async () => {
    if (!data || !rejectReason.trim()) return;
    
    setProcessing(true);
    try {
      const response = await fetch(`/api/admin/requests/${resolvedParams.id}/reject`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          reason: rejectReason,
          notes: rejectNotes 
        })
      });

      const result = await response.json();
      
      if (result.success) {
        toast.success('La solicitud ha sido rechazada.');
        router.push('/dashboard/admin/requests');
      } else {
        throw new Error(result.message || 'Error al rechazar');
      }
    } catch (err) {
      toast.error(err instanceof Error ? err.message : 'Error al rechazar la solicitud');
    } finally {
      setProcessing(false);
      setShowRejectDialog(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !data) {
    return (
      <div className="space-y-8">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="text-center text-red-600">
              <AlertCircle className="h-12 w-12 mx-auto mb-4" />
              <p className="text-gray-900 mb-2">{error || 'No se pudo cargar la solicitud'}</p>
              <Button 
                variant="outline" 
                className="mt-4 hover:bg-gray-50"
                onClick={() => router.push('/dashboard/admin/requests')}
              >
                Volver al listado
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const canProcess = data.status === 'pending' || data.status === 'reviewing';

  return (
    <div className="space-y-6 p-6">
      {/* Header del perfil */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push('/dashboard/admin/requests')}
            className="hover:bg-green-100 hover:text-green-600 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          
          <Avatar className="h-20 w-20">
            <AvatarFallback className="text-lg">
              {data.user.firstName?.[0]}{data.user.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {data.user.firstName} {data.user.lastName}
            </h1>
            <p className="text-gray-600">Solicitud de {roleLabels[data.role] || data.role}</p>
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge className={statusColors[data.status]}>
                {data.status === 'pending' ? 'Pendiente' : 
                 data.status === 'approved' ? 'Aprobada' :
                 data.status === 'rejected' ? 'Rechazada' : 
                 'En revisión'}
              </Badge>
              <Badge variant="outline" className="border-gray-300">
                {roleLabels[data.role] || data.role}
              </Badge>
            </div>
          </div>
        </div>

        <Card className="lg:w-80">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Información de Solicitud
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div>
                <p className="text-sm text-gray-600">Fecha de solicitud</p>
                <p className="font-medium">{format(new Date(data.submittedAt), "dd 'de' MMMM 'de' yyyy", { locale: es })}</p>
              </div>
              <div>
                <p className="text-sm text-gray-600">ID de solicitud</p>
                <p className="font-medium text-xs">{data.id}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Información rápida */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Mail className="h-8 w-8 text-blue-600" />
            <div>
              <p className="text-sm text-gray-600">Email</p>
              <p className="font-medium">{data.user.email}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Phone className="h-8 w-8 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">Teléfono</p>
              <p className="font-medium">{data.user.phone ? formatPhoneForDisplay(data.user.phone) : 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <FileText className="h-8 w-8 text-purple-600" />
            <div>
              <p className="text-sm text-gray-600">Documento</p>
              <p className="font-medium">{data.user.documentType?.toUpperCase()} {data.user.documentNumber || 'No especificado'}</p>
            </div>
          </CardContent>
        </Card>
      </div>


      {/* Sistema de Tabs Personalizado siguiendo el estándar */}
      <div className="space-y-4">
        {/* Custom Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('general')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'general'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Información General</div>
              </div>
            </button>
{data.role !== 'patient' && (
              <button
                onClick={() => setActiveTab('specific')}
                className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === 'specific'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <div className="text-center">
                  <div className="font-semibold">Información Específica</div>
                </div>
              </button>
            )}
            <button
              onClick={() => setActiveTab('documents')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'documents'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Documentos</div>
              </div>
            </button>
            {data.status !== 'pending' && (
              <button
                onClick={() => setActiveTab('history')}
                className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeTab === 'history'
                    ? 'bg-blue-500 text-white shadow-md'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <div className="text-center">
                  <div className="font-semibold">Historial</div>
                </div>
              </button>
            )}
          </div>
        </div>
        
        {/* Contenido de los tabs organizados verticalmente */}

        {/* Información General */}
        {activeTab === 'general' && (
          <div className="space-y-4">
            {/* Datos Personales */}
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-600" />
                  Datos Personales
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-gray-500 text-sm">Nombre Completo</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.firstName} {data.user.lastName}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Documento</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.documentType?.toUpperCase()} {data.user.documentNumber}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Fecha de Nacimiento</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.dateOfBirth ? 
                      formatDate(data.user.dateOfBirth) : 
                      'No especificada'}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Género</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.gender === 'male' || data.user.gender === 'masculino' ? 'Masculino' : 
                     data.user.gender === 'female' || data.user.gender === 'femenino' ? 'Femenino' : 
                     data.user.gender === 'other' || data.user.gender === 'otro' ? 'Otro' : 
                     data.user.gender ? data.user.gender.charAt(0).toUpperCase() + data.user.gender.slice(1).toLowerCase() : 
                     'No especificado'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Contacto */}
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5 text-emerald-600" />
                  Información de Contacto
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-gray-500 text-sm">Email</Label>
                  <p className="font-medium text-gray-900">{data.user.email}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Teléfono</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.phone ? formatPhoneForDisplay(data.user.phone) : 'No especificado'}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Teléfono Alternativo</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.alternativePhone ? formatPhoneForDisplay(data.user.alternativePhone) : 'No especificado'}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Dirección</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.address || 'No especificada'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Emergencia */}
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  Contacto de Emergencia
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-gray-500 text-sm">Nombre</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.emergencyContact || 'No especificado'}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Teléfono</Label>
                  <p className="font-medium text-gray-900">
                    {data.user.emergencyPhone ? formatPhoneForDisplay(data.user.emergencyPhone) : 'No especificado'}
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Solicitud */}
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-purple-600" />
                  Información de Solicitud
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-gray-500 text-sm">Fecha de Solicitud</Label>
                  <p className="font-medium text-gray-900">
                    {format(new Date(data.submittedAt), "dd 'de' MMMM 'de' yyyy", { locale: es })}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Hora</Label>
                  <p className="font-medium text-gray-900">
                    {format(new Date(data.submittedAt), 'HH:mm:ss')}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Rol Solicitado</Label>
                  <p className="font-medium text-gray-900">
                    {roleLabels[data.role] || data.role}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Información Específica del Rol */}
        {activeTab === 'specific' && data.role !== 'patient' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Briefcase className="h-5 w-5 text-orange-600" />
                  Información Específica de {roleLabels[data.role]}
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Detalles específicos del rol solicitado
                </p>
              </CardHeader>
              <CardContent>
              {data.role === 'doctor' && (
                <div className="space-y-6">
                  {/* Información Profesional */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-gray-500 text-sm">Licencia Médica</Label>
                      <p className="font-medium text-gray-900">
                        {data.roleData?.medicalLicense || data.specificData?.medicalLicense || 'No especificada'}
                      </p>
                    </div>
                    <div>
                      <Label className="text-gray-500 text-sm">Especialidad Principal</Label>
                      <p className="font-medium text-gray-900">
                        {data.roleData?.specialty?.name || data.specificData?.specialty || 'No especificada'}
                      </p>
                    </div>
                  </div>

                  {/* Sub-especialidades */}
                  {data.specificData?.subSpecialties?.length > 0 && (
                    <div>
                      <Label className="text-gray-500 text-sm">Sub-especialidades</Label>
                      <div className="mt-2 flex flex-wrap gap-2">
                        {data.specificData.subSpecialties.map((subSpec: string, index: number) => (
                          <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-sm">
                            {subSpec}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Formación Académica */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {data.specificData?.university && (
                      <div>
                        <Label className="text-gray-500 text-sm">Universidad</Label>
                        <p className="font-medium text-gray-900">{data.specificData.university}</p>
                      </div>
                    )}
                    {data.specificData?.graduationYear && (
                      <div>
                        <Label className="text-gray-500 text-sm">Año de Graduación</Label>
                        <p className="font-medium text-gray-900">{data.specificData.graduationYear}</p>
                      </div>
                    )}
                  </div>

                  {/* Consultorio */}
                  {(data.roleData?.consultory?.name || data.specificData?.consultoryId) && (
                    <div>
                      <Label className="text-gray-500 text-sm">Consultorio Asignado</Label>
                      <div className="mt-2 p-3 bg-gray-50 rounded-lg border">
                        <div className="font-medium text-gray-900">
                          {data.roleData?.consultory?.name || `Consultorio ID: ${data.specificData?.consultoryId}`}
                        </div>
                        {data.roleData?.consultory?.address && (
                          <div className="text-sm text-gray-600 mt-1">
                            <MapPin className="h-3 w-3 inline mr-1" />
                            {data.roleData.consultory.address}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Horarios de Trabajo */}
                  {data.specificData?.workSchedule && (
                    <div>
                      <Label className="text-gray-500 text-sm">Horarios de Disponibilidad</Label>
                      <div className="mt-2 space-y-2">
                        {Object.entries(data.specificData.workSchedule).map(([day, schedule]: [string, any]) => {
                          if (!schedule?.available) return null;
                          
                          const dayLabels: { [key: string]: string } = {
                            monday: 'Lunes',
                            tuesday: 'Martes', 
                            wednesday: 'Miércoles',
                            thursday: 'Jueves',
                            friday: 'Viernes',
                            saturday: 'Sábado',
                            sunday: 'Domingo'
                          };

                          return (
                            <div key={day} className="flex items-center justify-between p-2 bg-green-50 border border-green-200 rounded-lg">
                              <span className="font-medium text-green-800">
                                {dayLabels[day] || day}
                              </span>
                              <span className="text-green-700">
                                <Clock className="h-3 w-3 inline mr-1" />
                                {schedule.start} - {schedule.end}
                              </span>
                            </div>
                          );
                        })}
                        {!Object.values(data.specificData.workSchedule).some((schedule: any) => schedule?.available) && (
                          <p className="text-gray-500 italic">No se especificaron horarios de disponibilidad</p>
                        )}
                      </div>
                    </div>
                  )}
                </div>
              )}

              {data.role === 'assistant' && (
                <div className="space-y-4">
                  {/* Información Profesional */}
                  <div>
                    <Label className="text-gray-500 text-sm">Puesto/Posición</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.position || 'No especificado'}
                    </p>
                  </div>
                  
                  <div>
                    <Label className="text-gray-500 text-sm">Años de Experiencia</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.yearsExperience ? `${data.specificData.yearsExperience} años` : 'No especificado'}
                    </p>
                  </div>

                  <div>
                    <Label className="text-gray-500 text-sm">Consultorio</Label>
                    <p className="font-medium text-gray-900">
                      {data.additionalInfo?.doctorDetails?.length > 0 
                        ? data.additionalInfo.doctorDetails[0].consultory 
                        : data.specificData?.consultoryId || 'No asignado'}
                    </p>
                  </div>

                  {/* Doctores Asignados */}
                  <div>
                    <Label className="text-gray-500 text-sm">Doctores Asignados</Label>
                    <div className="mt-2">
                      {data.additionalInfo?.doctorDetails?.length > 0 ? (
                        <div className="space-y-3">
                          {data.additionalInfo.doctorDetails.map((doctor: any, index: number) => (
                            <div key={doctor.id} className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                              <div className="flex items-start space-x-3">
                                <User className="h-5 w-5 text-blue-600 mt-1 flex-shrink-0" />
                                <div className="flex-1">
                                  <div className="font-medium text-gray-900">
                                    Dr. {doctor.firstName} {doctor.lastName}
                                  </div>
                                  <div className="text-sm text-gray-600 mt-1">
                                    <div className="flex items-center gap-4">
                                      <span><strong>Especialidad:</strong> {doctor.specialty || 'No especificada'}</span>
                                    </div>
                                    <div className="flex items-center gap-4 mt-1">
                                      <span><strong>Consultorio:</strong> {doctor.consultory || 'No asignado'}</span>
                                    </div>
                                    {doctor.medicalLicense && (
                                      <div className="mt-1">
                                        <span><strong>Licencia:</strong> {doctor.medicalLicense}</span>
                                      </div>
                                    )}
                                    <div className="mt-1 text-gray-500">
                                      <Mail className="h-3 w-3 inline mr-1" />
                                      {doctor.email}
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : data.specificData?.assignedDoctors?.length > 0 ? (
                        <div className="space-y-2">
                          {data.specificData.assignedDoctors.map((doctorId: string, index: number) => (
                            <div key={doctorId} className="flex items-center p-2 bg-gray-50 rounded-lg">
                              <User className="h-4 w-4 text-gray-500 mr-2" />
                              <span className="text-gray-900 text-sm">
                                Doctor ID: {doctorId}
                              </span>
                            </div>
                          ))}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No se asignaron doctores</p>
                      )}
                    </div>
                  </div>

                  {/* Permisos Solicitados */}
                  <div>
                    <Label className="text-gray-500 text-sm">Permisos Solicitados</Label>
                    <div className="mt-2 space-y-2">
                      {data.specificData?.permissions ? (
                        <div className="grid grid-cols-1 gap-2">
                          {data.specificData.permissions.canScheduleAppointments && (
                            <div className="flex items-center p-2 bg-green-50 rounded-lg">
                              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                              <span className="text-sm text-green-800">Agendar Citas</span>
                            </div>
                          )}
                          {data.specificData.permissions.canHandlePayments && (
                            <div className="flex items-center p-2 bg-green-50 rounded-lg">
                              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                              <span className="text-sm text-green-800">Manejar Pagos</span>
                            </div>
                          )}
                          {data.specificData.permissions.canAccessMedicalRecords && (
                            <div className="flex items-center p-2 bg-green-50 rounded-lg">
                              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                              <span className="text-sm text-green-800">Acceso a Expedientes Médicos</span>
                            </div>
                          )}
                          {data.specificData.permissions.canManageInventory && (
                            <div className="flex items-center p-2 bg-green-50 rounded-lg">
                              <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                              <span className="text-sm text-green-800">Gestionar Inventario</span>
                            </div>
                          )}
                          {!Object.values(data.specificData.permissions).some(Boolean) && (
                            <p className="text-gray-500 italic">No se solicitaron permisos especiales</p>
                          )}
                        </div>
                      ) : (
                        <p className="text-gray-500 italic">No se especificaron permisos</p>
                      )}
                    </div>
                  </div>
                </div>
              )}

              {data.role === 'patient' && (
                <div className="space-y-4">
                  <div className="text-center py-8">
                    <Heart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 text-sm">
                      La información médica específica se recopila durante el pre-checkin y la primera consulta médica.
                    </p>
                    <p className="text-gray-500 text-xs mt-2">
                      No se requiere información médica adicional para aprobar esta solicitud.
                    </p>
                  </div>
                </div>
              )}

              {data.role === 'provider' && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-gray-500 text-sm">Nombre de Empresa</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.companyName || 'No especificado'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-gray-500 text-sm">NIT</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.nit || 'No especificado'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-gray-500 text-sm">Tipos de Servicio</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.serviceTypes?.join(', ') || 'No especificados'}
                    </p>
                  </div>
                </div>
              )}

              {data.role === 'guardian' && (
                <div className="space-y-4">
                  <div>
                    <Label className="text-gray-500 text-sm">Código de Asociación</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.associationCode || 'No especificado'}
                    </p>
                  </div>
                  <div>
                    <Label className="text-gray-500 text-sm">Relación con el Paciente</Label>
                    <p className="font-medium text-gray-900">
                      {data.specificData?.relationship || 'No especificada'}
                    </p>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          </div>
        )}

        {/* Documentos */}
        {activeTab === 'documents' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Documentos Adjuntos
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Documentos proporcionados para la verificación
                </p>
              </CardHeader>
              <CardContent>
                <DocumentViewer 
                  documents={data.specificData?.documentUrls || {}} 
                  role={data.role}
                />
              </CardContent>
            </Card>
          </div>
        )}

        {/* Historial */}
        {data.status !== 'pending' && activeTab === 'history' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Clock className="h-5 w-5 text-gray-600" />
                  Historial de Revisión
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Registro completo de las acciones realizadas en esta solicitud
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                {data.reviewedAt && (
                  <div>
                    <Label className="text-gray-500 text-sm">Fecha de Revisión</Label>
                    <p className="font-medium text-gray-900">
                      {format(new Date(data.reviewedAt), "dd 'de' MMMM 'de' yyyy HH:mm", { locale: es })}
                    </p>
                  </div>
                )}
                {data.reviewNotes && (
                  <div>
                    <Label className="text-gray-500 text-sm">Notas de Revisión</Label>
                    <p className="font-medium text-gray-900">{data.reviewNotes}</p>
                  </div>
                )}
                {data.rejectionReason && (
                  <div>
                    <Label className="text-gray-500 text-sm">Razón de Rechazo</Label>
                    <p className="font-medium text-red-600">{data.rejectionReason}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Action Buttons - Following CRUD standard */}
      {canProcess && (
        <div className="flex justify-end gap-3 pt-4">
          <Button 
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center space-x-2"
          >
            <ArrowLeft className="h-4 w-4" />
            <span>Volver</span>
          </Button>
          
          <Button 
            variant="destructive"
            onClick={() => setShowRejectDialog(true)}
            disabled={processing}
            className="flex items-center space-x-2"
          >
            <XCircle className="h-4 w-4" />
            <span>{processing ? 'Procesando...' : 'Rechazar'}</span>
          </Button>
          
          <Button 
            onClick={() => setShowApproveDialog(true)}
            disabled={processing}
            className="bg-emerald-600 hover:bg-emerald-700 text-white flex items-center space-x-2"
          >
            <CheckCircle className="h-4 w-4" />
            <span>{processing ? 'Procesando...' : 'Aprobar'}</span>
          </Button>
        </div>
      )}

      {/* Approve Dialog */}
      <Dialog open={showApproveDialog} onOpenChange={setShowApproveDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Aprobar Solicitud</DialogTitle>
            <DialogDescription>
              ¿Estás seguro de que quieres aprobar esta solicitud? Esta acción no se puede deshacer.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="approve-notes">Notas (opcional)</Label>
              <Textarea
                id="approve-notes"
                value={approveNotes}
                onChange={(e) => setApproveNotes(e.target.value)}
                placeholder="Añade notas sobre la aprobación..."
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowApproveDialog(false)}
              disabled={processing}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleApprove}
              disabled={processing}
            >
              {processing ? 'Procesando...' : 'Aprobar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Reject Dialog */}
      <Dialog open={showRejectDialog} onOpenChange={setShowRejectDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rechazar Solicitud</DialogTitle>
            <DialogDescription>
              Por favor, proporciona una razón para el rechazo. El usuario recibirá esta información.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="reject-reason">Razón del rechazo *</Label>
              <Textarea
                id="reject-reason"
                value={rejectReason}
                onChange={(e) => setRejectReason(e.target.value)}
                placeholder="Explica por qué se rechaza la solicitud..."
                rows={3}
                required
              />
            </div>
            <div>
              <Label htmlFor="reject-notes">Notas adicionales (opcional)</Label>
              <Textarea
                id="reject-notes"
                value={rejectNotes}
                onChange={(e) => setRejectNotes(e.target.value)}
                placeholder="Notas internas sobre el rechazo..."
                rows={3}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowRejectDialog(false)}
              disabled={processing}
            >
              Cancelar
            </Button>
            <Button
              variant="destructive"
              onClick={handleReject}
              disabled={processing || !rejectReason.trim()}
            >
              {processing ? 'Procesando...' : 'Rechazar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}