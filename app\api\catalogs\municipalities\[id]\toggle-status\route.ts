import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { municipalities } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar municipalidad
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de municipalidades.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que la municipalidad existe
    const existingMunicipality = await db
      .select()
      .from(municipalities)
      .where(eq(municipalities.id, id))
      .limit(1);

    if (existingMunicipality.length === 0) {
      return NextResponse.json({ error: 'Municipalidad no encontrada' }, { status: 404 });
    }

    // Cambiar el estado
    const newStatus = !existingMunicipality[0].isActive;
    const [updatedMunicipality] = await db
      .update(municipalities)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(municipalities.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedMunicipality,
      message: `Municipalidad ${newStatus ? 'activada' : 'desactivada'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling municipality status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}