import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  medicalServices,
  medications,
  medicationPrices,
  doctorServicePrices,
  companies,
  consultories
} from '@/db/schema';
import { eq, and, inArray } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

interface ServiceSummary {
  id: string;
  name: string;
  description?: string;
  quantity: number;
  basePrice: number;
  customPrice?: number;
  finalPrice: number;
  currency: string;
  companyId?: string;
  companyName?: string;
  companyNit?: string;
}

interface MedicationSummary {
  id: string;
  name: string;
  presentation: string;
  quantity: number;
  salePrice: number;
  totalPrice: number;
  currency: string;
  requiresPrescription: boolean;
}

interface SalesSummary {
  consultationId: string;
  services: ServiceSummary[];
  medications: MedicationSummary[];
  subtotal: number;
  taxAmount: number;
  totalAmount: number;
  currency: string;
  hasItems: boolean;
  invoiceIssuers: {
    companyId: string;
    companyName: string;
    nit: string;
    services: ServiceSummary[];
    medications: MedicationSummary[];
    subtotal: number;
  }[];
}

// GET - Obtener resumen de venta de una consulta
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const consultationId = id;
    
    console.log('Getting sales summary for consultation:', consultationId);

    // Obtener la consulta con sus servicios y prescripciones
    const consultation = await db
      .select({
        id: medicalConsultations.id,
        doctorId: medicalConsultations.doctorId,
        consultoryId: medicalConsultations.consultoryId,
        services: medicalConsultations.services,
        prescriptions: medicalConsultations.prescriptions,
        status: medicalConsultations.status,
      })
      .from(medicalConsultations)
      .where(eq(medicalConsultations.id, consultationId))
      .limit(1);

    if (!consultation.length) {
      return NextResponse.json({ 
        error: 'Consulta no encontrada' 
      }, { status: 404 });
    }

    const consultationData = consultation[0];
    
    // Verificar que el usuario tenga acceso a esta consulta
    // TODO: Agregar verificación de permisos más completa
    
    const salesSummary: SalesSummary = {
      consultationId,
      services: [],
      medications: [],
      subtotal: 0,
      taxAmount: 0,
      totalAmount: 0,
      currency: 'GTQ',
      hasItems: false,
      invoiceIssuers: []
    };

    // ========================================
    // PROCESAR SERVICIOS MÉDICOS
    // ========================================
    if (consultationData.services && Array.isArray(consultationData.services)) {
      const serviceIds = consultationData.services
        .filter((service: any) => service?.id)
        .map((service: any) => service.id);

      if (serviceIds.length > 0) {
        // Obtener información de servicios con compañías
        const servicesData = await db
          .select({
            serviceId: medicalServices.id,
            serviceName: medicalServices.name,
            serviceDescription: medicalServices.description,
            basePrice: medicalServices.basePrice,
            currency: medicalServices.currency,
            companyId: medicalServices.companyId,
            companyName: companies.businessName,
            companyNit: companies.nit,
          })
          .from(medicalServices)
          .leftJoin(companies, eq(medicalServices.companyId, companies.id))
          .where(inArray(medicalServices.id, serviceIds));

        // Obtener precios personalizados del doctor para estos servicios
        const doctorPrices = await db
          .select({
            serviceId: doctorServicePrices.serviceId,
            customPrice: doctorServicePrices.customPrice,
          })
          .from(doctorServicePrices)
          .where(and(
            eq(doctorServicePrices.doctorId, consultationData.doctorId),
            inArray(doctorServicePrices.serviceId, serviceIds),
            eq(doctorServicePrices.isActive, true)
          ));

        const doctorPricesMap = new Map(
          doctorPrices.map(dp => [dp.serviceId, dp.customPrice])
        );

        // Procesar cada servicio
        for (const consultationService of consultationData.services) {
          if (!consultationService?.id) continue;

          const serviceData = servicesData.find(s => s.serviceId === consultationService.id);
          if (!serviceData) continue;

          const customPrice = doctorPricesMap.get(consultationService.id);
          const basePrice = parseFloat(serviceData.basePrice?.toString() || '0');
          const finalPrice = customPrice ? parseFloat(customPrice.toString()) : basePrice;
          const quantity = consultationService.quantity || 1;

          const serviceSummary: ServiceSummary = {
            id: serviceData.serviceId,
            name: serviceData.serviceName,
            description: serviceData.serviceDescription || undefined,
            quantity,
            basePrice,
            customPrice: customPrice ? parseFloat(customPrice.toString()) : undefined,
            finalPrice,
            currency: serviceData.currency || 'GTQ',
            companyId: serviceData.companyId || undefined,
            companyName: serviceData.companyName || undefined,
            companyNit: serviceData.companyNit || undefined,
          };

          salesSummary.services.push(serviceSummary);
          salesSummary.subtotal += finalPrice * quantity;
        }
      }
    }

    // ========================================
    // PROCESAR MEDICAMENTOS
    // ========================================
    if (consultationData.prescriptions && Array.isArray(consultationData.prescriptions)) {
      const medicationIds = consultationData.prescriptions
        .filter((prescription: any) => prescription?.medicationId)
        .map((prescription: any) => prescription.medicationId);

      if (medicationIds.length > 0) {
        // Obtener precios de medicamentos para este consultorio
        const medicationPricesData = await db
          .select({
            medicationId: medicationPrices.medicationId,
            salePrice: medicationPrices.salePrice,
            currency: medicationPrices.currency,
            medicationName: medications.name,
            presentation: medications.presentation,
            requiresPrescription: medications.requiresPrescription,
          })
          .from(medicationPrices)
          .innerJoin(medications, eq(medicationPrices.medicationId, medications.id))
          .where(and(
            eq(medicationPrices.consultoryId, consultationData.consultoryId),
            inArray(medicationPrices.medicationId, medicationIds),
            eq(medicationPrices.isActive, true)
          ));

        const medicationPricesMap = new Map(
          medicationPricesData.map(mp => [mp.medicationId, mp])
        );

        // Procesar cada medicamento prescrito
        for (const prescription of consultationData.prescriptions) {
          if (!prescription?.medicationId) continue;

          const medicationPrice = medicationPricesMap.get(prescription.medicationId);
          if (!medicationPrice) continue; // Solo incluir medicamentos con precio

          const quantity = prescription.quantity || 1;
          const salePrice = parseFloat(medicationPrice.salePrice.toString());
          const totalPrice = salePrice * quantity;

          const medicationSummary: MedicationSummary = {
            id: prescription.medicationId,
            name: medicationPrice.medicationName,
            presentation: medicationPrice.presentation || '',
            quantity,
            salePrice,
            totalPrice,
            currency: medicationPrice.currency || 'GTQ',
            requiresPrescription: medicationPrice.requiresPrescription || false,
          };

          salesSummary.medications.push(medicationSummary);
          salesSummary.subtotal += totalPrice;
        }
      }
    }

    // ========================================
    // CALCULAR TOTALES E IMPUESTOS
    // ========================================
    salesSummary.hasItems = salesSummary.services.length > 0 || salesSummary.medications.length > 0;
    
    if (salesSummary.hasItems) {
      // Calcular IVA (12% en Guatemala)
      const TAX_RATE = 0.12;
      salesSummary.taxAmount = salesSummary.subtotal * TAX_RATE;
      salesSummary.totalAmount = salesSummary.subtotal + salesSummary.taxAmount;

      // ========================================
      // AGRUPAR POR EMISORES DE FACTURA
      // ========================================
      const issuersMap = new Map<string, {
        companyId: string;
        companyName: string;
        nit: string;
        services: ServiceSummary[];
        medications: MedicationSummary[];
        subtotal: number;
      }>();

      // Agrupar servicios por compañía emisora
      for (const service of salesSummary.services) {
        if (!service.companyId || !service.companyName || !service.companyNit) continue;

        const key = service.companyId;
        if (!issuersMap.has(key)) {
          issuersMap.set(key, {
            companyId: service.companyId,
            companyName: service.companyName,
            nit: service.companyNit,
            services: [],
            medications: [],
            subtotal: 0,
          });
        }

        const issuer = issuersMap.get(key)!;
        issuer.services.push(service);
        issuer.subtotal += service.finalPrice * service.quantity;
      }

      // TODO: Agrupar medicamentos por compañía (cuando implementemos inventario)
      // Por ahora, todos los medicamentos van a la misma compañía por defecto

      salesSummary.invoiceIssuers = Array.from(issuersMap.values());
    }

    console.log(`Sales summary: ${salesSummary.services.length} services, ${salesSummary.medications.length} medications, Total: Q${salesSummary.totalAmount.toFixed(2)}`);

    return NextResponse.json({
      success: true,
      data: salesSummary,
    });

  } catch (error) {
    console.error('Error getting sales summary:', error);
    
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}