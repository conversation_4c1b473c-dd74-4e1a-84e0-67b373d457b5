import { useState, useCallback } from 'react';

export interface Toast {
  id: string;
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

interface ToastProps {
  title?: string;
  description?: string;
  variant?: 'default' | 'destructive';
  duration?: number;
}

let toastCount = 0;

export function useToast() {
  const [toasts, setToasts] = useState<Toast[]>([]);

  const toast = useCallback(({ title, description, variant = 'default', duration = 5000 }: ToastProps) => {
    const id = (++toastCount).toString();
    const newToast: Toast = {
      id,
      title,
      description,
      variant,
      duration,
    };

    setToasts((prevToasts) => [...prevToasts, newToast]);

    // Auto-remove toast after duration
    setTimeout(() => {
      setToasts((prevToasts) => prevToasts.filter((t) => t.id !== id));
    }, duration);

    return {
      id,
      dismiss: () => setToasts((prevToasts) => prevToasts.filter((t) => t.id !== id)),
      update: (props: Partial<ToastProps>) => {
        setToasts((prevToasts) =>
          prevToasts.map((t) => (t.id === id ? { ...t, ...props } : t))
        );
      },
    };
  }, []);

  const dismiss = useCallback((toastId?: string) => {
    setToasts((prevToasts) =>
      toastId ? prevToasts.filter((t) => t.id !== toastId) : []
    );
  }, []);

  return {
    toast,
    dismiss,
    toasts,
  };
}