import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, users } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET() {
  try {
    console.log('🧪 TEST: Iniciando test de usuario');
    
    const authData = await auth();
    console.log('🧪 TEST AuthData:', { userId: authData?.userId });
    
    if (!authData?.userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Test 1: Verificar si db.query está disponible
    console.log('🧪 TEST 1: Verificando db.query');
    console.log('🧪 db.query existe?', !!db.query);
    console.log('🧪 db.query.userRoles existe?', !!db.query?.userRoles);

    // Test 2: Consulta simple usando SQL directo
    console.log('🧪 TEST 2: Consulta SQL directa');
    const directQuery = await db.select().from(userRoles).where(eq(userRoles.userId, authData.userId)).limit(1);
    console.log('🧪 TEST 2 Result:', directQuery);

    // Test 3: Consulta con query builder si está disponible
    let queryBuilderResult = null;
    if (db.query?.userRoles) {
      console.log('🧪 TEST 3: Consulta con query builder');
      queryBuilderResult = await db.query.userRoles.findFirst({
        where: eq(userRoles.userId, authData.userId),
        with: {
          user: true
        }
      });
      console.log('🧪 TEST 3 Result:', queryBuilderResult);
    }

    return NextResponse.json({
      success: true,
      userId: authData.userId,
      hasQueryBuilder: !!db.query,
      directQuery: directQuery[0] || null,
      queryBuilderResult
    });

  } catch (error) {
    console.error('🧪 TEST ERROR:', error);
    return NextResponse.json(
      { error: 'Error en test', details: error.message, stack: error.stack },
      { status: 500 }
    );
  }
}