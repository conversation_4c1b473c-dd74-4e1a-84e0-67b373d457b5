import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalServices, serviceTags, medicalServiceTags } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or, sql } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET - Listar servicios médicos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const onlyActive = searchParams.get('active') === 'true';
    const requiresEquipment = searchParams.get('requiresEquipment');
    const requiresSpecialist = searchParams.get('requiresSpecialist');
    const orderBy = searchParams.get('orderBy') || 'name';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(medicalServices);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(medicalServices.name, `%${search}%`),
          ilike(medicalServices.description, `%${search}%`),
          ilike(medicalServices.code, `%${search}%`),
          ilike(medicalServices.category, `%${search}%`)
        )
      );
    }

    if (category && category !== 'all') {
      conditions.push(eq(medicalServices.category, category));
    }

    if (onlyActive) {
      conditions.push(eq(medicalServices.isActive, true));
    }

    if (requiresEquipment === 'true') {
      conditions.push(eq(medicalServices.requiresEquipment, true));
    } else if (requiresEquipment === 'false') {
      conditions.push(eq(medicalServices.requiresEquipment, false));
    }

    if (requiresSpecialist === 'true') {
      conditions.push(eq(medicalServices.requiresSpecialist, true));
    } else if (requiresSpecialist === 'false') {
      conditions.push(eq(medicalServices.requiresSpecialist, false));
    }

    // Aplicar condiciones si existen
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'name' ? medicalServices.name :
                       orderBy === 'category' ? medicalServices.category :
                       orderBy === 'basePrice' ? medicalServices.basePrice :
                       orderBy === 'duration' ? medicalServices.duration :
                       orderBy === 'createdAt' ? medicalServices.createdAt :
                       medicalServices.name;

    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query
    const result = await query;

    // Obtener tags para cada servicio
    const servicesWithTags = await Promise.all(
      result.map(async (service) => {
        const tagsResult = await db
          .select({
            id: serviceTags.id,
            name: serviceTags.name,
            color: serviceTags.color,
            category: serviceTags.category,
          })
          .from(medicalServiceTags)
          .innerJoin(serviceTags, eq(medicalServiceTags.tagId, serviceTags.id))
          .where(eq(medicalServiceTags.serviceId, service.id));

        return {
          ...service,
          tags: tagsResult,
        };
      })
    );

    // Obtener conteo total
    let countQuery = db.select({ count: count() }).from(medicalServices);
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Obtener categorías únicas para filtros
    const categoriesResult = await db.select({ category: medicalServices.category })
      .from(medicalServices)
      .where(eq(medicalServices.isActive, true))
      .groupBy(medicalServices.category);
    
    const categories = categoriesResult.map(r => r.category).filter(Boolean);

    return NextResponse.json({
      data: servicesWithTags,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      categories,
    });
  } catch (error) {
    console.error('Error fetching medical services:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Crear nuevo servicio médico
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario tenga permisos de admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'No tienes permisos para crear servicios médicos' }, { status: 403 });
    }

    const body = await request.json();
    const { name, description, code, category, basePrice, currency, duration, requiresEquipment, requiresSpecialist, tags } = body;

    // Validaciones
    if (!name || !category) {
      return NextResponse.json({ error: 'Nombre y categoría son requeridos' }, { status: 400 });
    }

    // Validar categoría
    const validCategories = ['Consulta', 'Procedimiento', 'Emergencia', 'Preventivo', 'Diagnóstico'];
    if (!validCategories.includes(category)) {
      return NextResponse.json({ error: 'Categoría no válida' }, { status: 400 });
    }

    // Verificar que el código no exista si se proporciona
    if (code) {
      const existingService = await db.select().from(medicalServices).where(eq(medicalServices.code, code));
      if (existingService.length > 0) {
        return NextResponse.json({ error: 'El código ya existe' }, { status: 400 });
      }
    }

    // Crear el nuevo servicio
    const newService = {
      id: nanoid(),
      name,
      description: description || null,
      code: code || null,
      category,
      basePrice: basePrice ? parseFloat(basePrice) : null,
      currency: currency || 'GTQ',
      duration: duration ? parseInt(duration) : null,
      requiresEquipment: requiresEquipment || false,
      requiresSpecialist: requiresSpecialist || false,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Usar transacción para insertar servicio y tags
    const result = await db.transaction(async (tx) => {
      // Insertar el servicio
      const serviceResult = await tx.insert(medicalServices).values(newService).returning();
      const createdService = serviceResult[0];

      // Insertar tags si se proporcionaron
      if (tags && Array.isArray(tags) && tags.length > 0) {
        const tagRelations = tags.map(tagId => ({
          serviceId: createdService.id,
          tagId: tagId,
        }));
        await tx.insert(medicalServiceTags).values(tagRelations);
      }

      return createdService;
    });

    return NextResponse.json({
      message: 'Servicio médico creado exitosamente',
      data: result,
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating medical service:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// PUT - Actualizar servicio médico
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario tenga permisos de admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'No tienes permisos para actualizar servicios médicos' }, { status: 403 });
    }

    const body = await request.json();
    const { id, name, description, code, category, basePrice, currency, duration, requiresEquipment, requiresSpecialist, tags } = body;

    // Validaciones
    if (!id || !name || !category) {
      return NextResponse.json({ error: 'ID, nombre y categoría son requeridos' }, { status: 400 });
    }

    // Validar categoría
    const validCategories = ['Consulta', 'Procedimiento', 'Emergencia', 'Preventivo', 'Diagnóstico'];
    if (!validCategories.includes(category)) {
      return NextResponse.json({ error: 'Categoría no válida' }, { status: 400 });
    }

    // Verificar que el servicio existe
    const existingService = await db.select().from(medicalServices).where(eq(medicalServices.id, id));
    if (existingService.length === 0) {
      return NextResponse.json({ error: 'Servicio médico no encontrado' }, { status: 404 });
    }

    // Verificar que el código no exista si se proporciona y es diferente
    if (code && code !== existingService[0].code) {
      const duplicateCode = await db.select().from(medicalServices).where(eq(medicalServices.code, code));
      if (duplicateCode.length > 0) {
        return NextResponse.json({ error: 'El código ya existe' }, { status: 400 });
      }
    }

    // Actualizar el servicio
    const updateData = {
      name,
      description: description || null,
      code: code || null,
      category,
      basePrice: basePrice ? parseFloat(basePrice) : null,
      currency: currency || 'GTQ',
      duration: duration ? parseInt(duration) : null,
      requiresEquipment: requiresEquipment || false,
      requiresSpecialist: requiresSpecialist || false,
      updatedAt: new Date(),
    };

    // Usar transacción para actualizar servicio y tags
    const result = await db.transaction(async (tx) => {
      // Actualizar el servicio
      const serviceResult = await tx.update(medicalServices)
        .set(updateData)
        .where(eq(medicalServices.id, id))
        .returning();

      // Actualizar tags si se proporcionaron
      if (tags && Array.isArray(tags)) {
        // Eliminar tags existentes
        await tx.delete(medicalServiceTags).where(eq(medicalServiceTags.serviceId, id));

        // Insertar nuevos tags
        if (tags.length > 0) {
          const tagRelations = tags.map(tagId => ({
            serviceId: id,
            tagId: tagId,
          }));
          await tx.insert(medicalServiceTags).values(tagRelations);
        }
      }

      return serviceResult[0];
    });

    return NextResponse.json({
      message: 'Servicio médico actualizado exitosamente',
      data: result,
    });
  } catch (error) {
    console.error('Error updating medical service:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}