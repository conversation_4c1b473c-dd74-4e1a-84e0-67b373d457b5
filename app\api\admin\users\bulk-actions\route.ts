import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { clerkClient } from '@clerk/nextjs/server';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  // Obtener rol de los session claims (igual que en el middleware)
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// POST - Ejecutar acciones masivas
export async function POST(request: NextRequest) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const body = await request.json();
    const { action, userIds } = body;

    // Validaciones
    if (!action || !userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Acción y lista de usuarios son requeridos' },
        { status: 400 }
      );
    }

    const validActions = ['activate', 'deactivate', 'suspend', 'delete'];
    if (!validActions.includes(action)) {
      return NextResponse.json(
        { success: false, error: 'Acción no válida' },
        { status: 400 }
      );
    }

    // Verificar que los usuarios existen
    const existingUsers = await db
      .select()
      .from(user)
      .where(sql`${user.id} IN ${userIds}`);

    if (existingUsers.length !== userIds.length) {
      return NextResponse.json(
        { success: false, error: 'Algunos usuarios no fueron encontrados' },
        { status: 404 }
      );
    }

    let results = {
      success: 0,
      failed: 0,
      errors: [] as string[]
    };

    try {
      // Obtener usuarios de Clerk para sincronización
      const clerkUsers = await clerkClient.users.getUserList({
        limit: 500
      });

      for (const userId of userIds) {
        try {
          const clerkUser = clerkUsers.find(
            u => u.privateMetadata?.internalUserId === userId
          );

          switch (action) {
            case 'activate':
              // Actualizar en DB
              await db
                .update(user)
                .set({
                  overallStatus: 'active',
                  updatedAt: new Date(),
                })
                .where(eq(user.id, userId));

              // Actualizar roles
              await db
                .update(userRoles)
                .set({
                  status: 'active',
                  updatedAt: new Date(),
                })
                .where(eq(userRoles.userId, userId));

              // Actualizar en Clerk
              if (clerkUser) {
                await clerkClient.users.updateUser(clerkUser.id, {
                  publicMetadata: {
                    ...clerkUser.publicMetadata,
                    status: 'active',
                    lastStatusChange: new Date().toISOString(),
                  },
                });

                if (clerkUser.banned) {
                  await clerkClient.users.unbanUser(clerkUser.id);
                }
              }
              break;

            case 'deactivate':
              // Actualizar en DB
              await db
                .update(user)
                .set({
                  overallStatus: 'inactive',
                  updatedAt: new Date(),
                })
                .where(eq(user.id, userId));

              // Actualizar roles
              await db
                .update(userRoles)
                .set({
                  status: 'inactive',
                  updatedAt: new Date(),
                })
                .where(eq(userRoles.userId, userId));

              // Actualizar en Clerk
              if (clerkUser) {
                await clerkClient.users.updateUser(clerkUser.id, {
                  publicMetadata: {
                    ...clerkUser.publicMetadata,
                    status: 'inactive',
                    lastStatusChange: new Date().toISOString(),
                  },
                });

                if (!clerkUser.banned) {
                  await clerkClient.users.banUser(clerkUser.id);
                }
              }
              break;

            case 'suspend':
              // Actualizar en DB
              await db
                .update(user)
                .set({
                  overallStatus: 'suspended',
                  updatedAt: new Date(),
                })
                .where(eq(user.id, userId));

              // Actualizar roles
              await db
                .update(userRoles)
                .set({
                  status: 'inactive',
                  updatedAt: new Date(),
                })
                .where(eq(userRoles.userId, userId));

              // Actualizar en Clerk
              if (clerkUser) {
                await clerkClient.users.updateUser(clerkUser.id, {
                  publicMetadata: {
                    ...clerkUser.publicMetadata,
                    status: 'suspended',
                    lastStatusChange: new Date().toISOString(),
                  },
                });

                if (!clerkUser.banned) {
                  await clerkClient.users.banUser(clerkUser.id);
                }
              }
              break;

            case 'delete':
              // Eliminación lógica
              await db
                .update(user)
                .set({
                  overallStatus: 'deleted',
                  updatedAt: new Date(),
                })
                .where(eq(user.id, userId));

              // Inactivar roles
              await db
                .update(userRoles)
                .set({
                  status: 'inactive',
                  updatedAt: new Date(),
                })
                .where(eq(userRoles.userId, userId));

              // Eliminar de Clerk (opcional)
              if (clerkUser) {
                await clerkClient.users.deleteUser(clerkUser.id);
              }
              break;
          }

          results.success++;
        } catch (userError) {
          console.error(`Error processing user ${userId}:`, userError);
          results.failed++;
          results.errors.push(`Error procesando usuario ${userId}: ${userError}`);
        }
      }

      const actionMessages = {
        activate: 'activados',
        deactivate: 'inactivados',
        suspend: 'suspendidos',
        delete: 'eliminados'
      };

      return NextResponse.json({
        success: true,
        data: {
          results,
          message: `${results.success} usuarios ${actionMessages[action as keyof typeof actionMessages]} exitosamente${results.failed > 0 ? `, ${results.failed} fallaron` : ''}`
        }
      });

    } catch (clerkError) {
      console.error('Error in bulk action with Clerk:', clerkError);
      return NextResponse.json(
        { success: false, error: 'Error en la sincronización con el sistema de autenticación' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error in bulk action:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}