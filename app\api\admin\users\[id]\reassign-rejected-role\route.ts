import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { userRoles, user } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { nanoid } from 'nanoid';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    const { role, rejectionReason } = body;

    console.log(`🔧 Reasignando rol rechazado para usuario: ${id}`);

    // Verificar que el usuario existe
    const userRecord = await db.select().from(user).where(eq(user.id, id)).limit(1);
    
    if (!userRecord || userRecord.length === 0) {
      return NextResponse.json({ error: 'Usuario no encontrado' }, { status: 404 });
    }

    console.log(`👤 Usuario encontrado: ${JSON.stringify(userRecord[0])}`);

    // Verificar si ya tiene un rol rechazado
    const existingRejectedRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, id),
          eq(userRoles.role, role),
          eq(userRoles.status, 'rejected')
        )
      )
      .limit(1);

    if (existingRejectedRole.length > 0) {
      console.log(`⚠️ Usuario ya tiene rol rechazado: ${JSON.stringify(existingRejectedRole[0])}`);
      return NextResponse.json({ 
        message: 'Usuario ya tiene este rol rechazado',
        role: existingRejectedRole[0]
      });
    }

    // Crear el rol rechazado
    const newRejectedRole = await db
      .insert(userRoles)
      .values({
        id: nanoid(),
        userId: id,
        role,
        status: 'rejected',
        rejectionReason: rejectionReason || 'Rol rechazado reasignado por administrador',
        rejectedAt: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    console.log(`✅ Rol rechazado reasignado: ${JSON.stringify(newRejectedRole[0])}`);

    // Actualizar el status del usuario a rejected si no lo está
    if (userRecord[0].overallStatus !== 'rejected') {
      await db
        .update(user)
        .set({ 
          overallStatus: 'rejected', 
          updatedAt: new Date() 
        })
        .where(eq(user.id, id));
      
      console.log(`📝 Status del usuario actualizado a 'rejected'`);
    }

    return NextResponse.json({
      message: 'Rol rechazado reasignado exitosamente',
      role: newRejectedRole[0],
      userStatus: 'rejected'
    });

  } catch (error) {
    console.error('❌ Error reasignando rol rechazado:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 