"use client";

import { useState } from "react";
import { useSignIn } from "@clerk/nextjs";
import { useRouter } from "next/navigation";
import { <PERSON>, Spark<PERSON>, Eye, EyeOff, Mail, Lock, Chrome, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useConsultoryInfo } from "@/hooks/use-consultory-info";

interface SignInModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToSignUp: () => void;
}

export default function SignInModal({ isOpen, onClose, onSwitchToSignUp }: SignInModalProps) {
  const { isLoaded, signIn, setActive } = useSignIn();
  const { consultory, isLoading: consultoryLoading } = useConsultoryInfo();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!isLoaded) return;

    setIsLoading(true);
    setError("");

    try {
      const result = await signIn.create({
        identifier: email,
        password,
      });

      if (result.status === "complete") {
        await setActive({ session: result.createdSessionId });
        onClose();
        // Permitir que el middleware maneje la redirección
        router.push("/");
      }
    } catch (err: any) {
      setError(err.errors?.[0]?.message || "Error al iniciar sesión");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignIn = async () => {
    if (!isLoaded) return;
    
    setIsLoading(true);
    try {
      await signIn.authenticateWithRedirect({
        strategy: "oauth_google",
        redirectUrl: "/",
        redirectUrlComplete: "/",
      });
    } catch (err) {
      setError("Error al conectar con Google");
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail("");
    setPassword("");
    setError("");
    setShowPassword(false);
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-0 border-0 bg-gradient-to-br from-green-50 via-emerald-50 to-green-100 overflow-hidden">
        {/* Background Effects */}
        <div className="absolute top-0 left-0 w-32 h-32 bg-green-200/20 rounded-full blur-2xl -ml-16 -mt-16 animate-pulse"></div>
        <div className="absolute bottom-0 right-0 w-28 h-28 bg-emerald-200/20 rounded-full blur-2xl -mr-14 -mb-14 animate-pulse delay-1000"></div>
        
        {/* Close Button */}
        <button
          onClick={handleClose}
          className="absolute top-4 right-4 z-20 w-8 h-8 rounded-full bg-white/80 hover:bg-white shadow-md flex items-center justify-center transition-all duration-200"
        >
          <X className="h-4 w-4 text-gray-500" />
        </button>
        
        <div className="relative z-10 p-6">
          {/* Header */}
          <DialogHeader className="text-center mb-6">
            <div className="inline-flex items-center gap-3 mb-4 justify-center">
              <div className="w-10 h-10 bg-gradient-to-br from-[#ea6cb0] to-[#ea6cb0]/80 rounded-3xl flex items-center justify-center shadow-lg">
                <Baby className="h-5 w-5 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-[#ea6cb0]">
                  {consultoryLoading ? 'Cargando...' : consultory?.name || 'Mundo Pediatra'}
                </h1>
                <div className="flex items-center gap-1 mt-0.5 justify-center">
                  <Sparkles className="h-1.5 w-1.5 text-emerald-500" />
                  <span className="text-xs text-gray-600">Tu clínica de confianza</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-1">
              <DialogTitle className="text-lg font-bold text-gray-800">¡Bienvenido de vuelta!</DialogTitle>
              <p className="text-gray-600 text-sm">Inicia sesión para acceder a tu portal</p>
            </div>
          </DialogHeader>

          {/* Form Card */}
          <Card className="border-0 shadow-lg bg-white/95 backdrop-blur-sm">
            <CardContent className="p-5 space-y-4">
              
              {/* Google Sign In */}
              <Button
                onClick={handleGoogleSignIn}
                disabled={isLoading}
                variant="outline"
                className="w-full h-10 border-2 border-gray-200 hover:border-[#50bed2] hover:bg-[#50bed2]/10 transition-all duration-200"
              >
                <Chrome className="h-4 w-4 mr-2 text-red-500" />
                <span className="font-medium text-gray-700">Google</span>
              </Button>

              {/* Divider */}
              <div className="relative">
                <div className="absolute inset-0 flex items-center">
                  <div className="w-full border-t border-gray-200"></div>
                </div>
                <div className="relative flex justify-center text-xs uppercase">
                  <span className="bg-white px-2 text-gray-500">O con email</span>
                </div>
              </div>

              {/* Error Message */}
              {error && (
                <div className="p-2.5 bg-red-50 border border-red-200 rounded-lg">
                  <p className="text-red-600 text-sm">{error}</p>
                </div>
              )}

              {/* Sign In Form */}
              <form onSubmit={handleSubmit} className="space-y-3">
                <div className="space-y-1.5">
                  <Label htmlFor="email" className="text-gray-700 font-medium text-sm">
                    Email
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                      required
                      className="pl-10 h-10 border-2 border-gray-200 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-100 transition-all"
                    />
                  </div>
                </div>

                <div className="space-y-1.5">
                  <Label htmlFor="password" className="text-gray-700 font-medium text-sm">
                    Contraseña
                  </Label>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type={showPassword ? "text" : "password"}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Tu contraseña"
                      required
                      className="pl-10 pr-10 h-10 border-2 border-gray-200 rounded-lg focus:border-green-500 focus:ring-2 focus:ring-green-100 transition-all"
                    />
                    <button
                      type="button"
                      onClick={() => setShowPassword(!showPassword)}
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </button>
                  </div>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading}
                  className="w-full h-10 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 text-white font-medium rounded-lg shadow-md hover:shadow-lg transition-all duration-200"
                >
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      Iniciando...
                    </div>
                  ) : (
                    "Iniciar Sesión"
                  )}
                </Button>
              </form>

              {/* Footer */}
              <div className="text-center space-y-2">
                <p className="text-sm text-gray-600">
                  ¿No tienes cuenta?{" "}
                  <button 
                    onClick={onSwitchToSignUp}
                    className="text-green-600 hover:text-green-700 font-medium"
                  >
                    Regístrate
                  </button>
                </p>
                <button className="text-xs text-gray-500 hover:text-green-600">
                  ¿Olvidaste tu contraseña?
                </button>
              </div>
            </CardContent>
          </Card>
        </div>
      </DialogContent>
    </Dialog>
  );
} 