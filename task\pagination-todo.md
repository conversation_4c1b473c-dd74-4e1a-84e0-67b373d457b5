# Plan de Implementación de Paginación en Catálogos

## Objetivo
Implementar el patrón de paginación en los siguientes catálogos:
1. occupations (ocupaciones)
2. relationships (relaciones)  
3. consultories (consultorios)

## Patrón a Implementar

### 1. Variables de estado necesarias:
- page (número de página actual)
- limit (elementos por página) 
- totalPages (total de páginas)
- totalCount (total de elementos)

### 2. Actualizar fetchData:
- Agregar parámetros de paginación a la URL
- Recibir y procesar respuesta con estructura de paginación

### 3. useEffect para paginación:
- Agregar page, limit a las dependencias
- Agregar useEffect separado para resetear página cuando cambien filtros

### 4. Eliminar filtrado del cliente:
- Remover toda lógica de filtrado local
- El servidor debe manejar todo el filtrado

### 5. Controles de paginación:
- <PERSON><PERSON>: "Mostrando X a Y de Z [items]" + selector de límite
- Lado derecho: botones Previous/Next + números de página

### 6. Actualizar badge:
- Mostrar totalCount en lugar de filtered.length

## Tareas

### 1. Análisis inicial
- [ ] Leer occupations/page.tsx para entender estructura actual
- [ ] Leer relationships/page.tsx para entender estructura actual
- [ ] Leer consultories/page.tsx para entender estructura actual
- [ ] Verificar las APIs correspondientes

### 2. Implementar en occupations
- [ ] Agregar estados de paginación
- [ ] Actualizar fetchData con parámetros
- [ ] Agregar useEffect para dependencias
- [ ] Agregar useEffect para reset de página
- [ ] Eliminar filtrado del cliente
- [ ] Agregar controles de paginación
- [ ] Actualizar badge a totalCount

### 3. Implementar en relationships
- [ ] Agregar estados de paginación
- [ ] Actualizar fetchData con parámetros
- [ ] Agregar useEffect para dependencias
- [ ] Agregar useEffect para reset de página
- [ ] Eliminar filtrado del cliente
- [ ] Agregar controles de paginación
- [ ] Actualizar badge a totalCount

### 4. Implementar en consultories
- [ ] Agregar estados de paginación
- [ ] Actualizar fetchData con parámetros
- [ ] Agregar useEffect para dependencias
- [ ] Agregar useEffect para reset de página
- [ ] Eliminar filtrado del cliente
- [ ] Agregar controles de paginación
- [ ] Actualizar badge a totalCount

### 5. Verificación final
- [ ] Probar paginación en occupations
- [ ] Probar paginación en relationships
- [ ] Probar paginación en consultories
- [ ] Confirmar que los filtros funcionan correctamente