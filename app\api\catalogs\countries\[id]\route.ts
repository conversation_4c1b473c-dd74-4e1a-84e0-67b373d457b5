import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { countries } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener país por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const country = await db
      .select()
      .from(countries)
      .where(eq(countries.id, id))
      .limit(1);

    if (country.length === 0) {
      return NextResponse.json({ error: 'País no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: country[0] 
    });

  } catch (error) {
    console.error('Error fetching country:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar país
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar países.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, code, phoneCode, currency, isActive } = body;

    // Validar datos requeridos
    if (!name || !code) {
      return NextResponse.json({ 
        error: 'Nombre y código son requeridos' 
      }, { status: 400 });
    }

    // Verificar que el país existe
    const existingCountry = await db
      .select()
      .from(countries)
      .where(eq(countries.id, id))
      .limit(1);

    if (existingCountry.length === 0) {
      return NextResponse.json({ error: 'País no encontrado' }, { status: 404 });
    }

    // Verificar que el código no esté en uso por otro país
    const duplicateCode = await db
      .select()
      .from(countries)
      .where(eq(countries.code, code))
      .limit(1);

    if (duplicateCode.length > 0 && duplicateCode[0].id !== id) {
      return NextResponse.json({ 
        error: 'El código del país ya está en uso' 
      }, { status: 400 });
    }

    // Actualizar país
    const [updatedCountry] = await db
      .update(countries)
      .set({
        name,
        code,
        phoneCode: phoneCode || null,
        currency: currency || null,
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(countries.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedCountry,
      message: 'País actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating country:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar país
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar países.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que el país existe
    const existingCountry = await db
      .select()
      .from(countries)
      .where(eq(countries.id, id))
      .limit(1);

    if (existingCountry.length === 0) {
      return NextResponse.json({ error: 'País no encontrado' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(countries)
        .where(eq(countries.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'País eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedCountry] = await db
        .update(countries)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(countries.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedCountry,
        message: 'País desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting country:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}