-- Add audit fields to relationships table
DO $$ 
BEGIN
  -- Check if createdAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'relationships' AND column_name = 'createdAt') THEN
    ALTER TABLE "relationships" ADD COLUMN "createdAt" timestamp DEFAULT now() NOT NULL;
  END IF;
  
  -- Check if updatedAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'relationships' AND column_name = 'updatedAt') THEN
    ALTER TABLE "relationships" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;
  END IF;
END $$;