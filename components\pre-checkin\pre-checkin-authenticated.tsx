'use client';

import { useState, useEffect } from 'react';
import { PreCheckinFormImproved } from '@/components/pre-checkin/pre-checkin-form-improved';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Clock, User, Shield, ArrowLeft, Home, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { translateRelationship } from '@/lib/utils';

interface PreCheckinAuthenticatedProps {
  appointmentData: {
    id: string;
    title: string | null;
    scheduledDate: Date;
    startTime: Date;
    endTime: Date;
    status: string;
    preCheckinCompleted: boolean | null;
    preCheckinCompletedAt: Date | null;
    patientId: string | null;
    patientFirstName: string | null;
    patientLastName: string | null;
    patientEmail: string | null;
    doctorFirstName: string | null;
    doctorLastName: string | null;
    isDependent: boolean;
    guardianInfo?: {
      guardianId: string;
      relationship: string;
      guardianFirstName: string | null;
      guardianLastName: string | null;
      guardianEmail: string | null;
    } | null;
  };
  appointmentDate: string;
  appointmentTime: string;
  appointmentEndTime: string;
  isAlreadyCompleted: boolean;
  user: any; // Clerk user object
}

export function PreCheckinAuthenticated({
  appointmentData,
  appointmentDate,
  appointmentTime,
  appointmentEndTime,
  isAlreadyCompleted,
  user,
}: PreCheckinAuthenticatedProps) {
  const router = useRouter();
  const [dashboardUrl, setDashboardUrl] = useState<string>('/dashboard');
  const [dashboardContext, setDashboardContext] = useState<string>('loading');
  const [isLoadingDashboard, setIsLoadingDashboard] = useState(true);

  // Determinar el dashboard correcto usando el API
  useEffect(() => {
    const fetchDashboardContext = async () => {
      try {
        const params = new URLSearchParams({
          action: 'precheckin',
          patientId: appointmentData.patientId || '',
          isDependent: appointmentData.isDependent.toString(),
        });

        const response = await fetch(`/api/user/dashboard-context?${params}`);
        const data = await response.json();

        if (data.success) {
          setDashboardUrl(data.dashboardUrl);
          setDashboardContext(data.context);
          console.log('✅ Dashboard context determinado:', data);
        } else {
          console.error('❌ Error obteniendo dashboard context:', data.error);
          setDashboardUrl('/dashboard'); // fallback
          setDashboardContext('fallback');
        }
      } catch (error) {
        console.error('❌ Error fetching dashboard context:', error);
        setDashboardUrl('/dashboard'); // fallback
        setDashboardContext('error');
      } finally {
        setIsLoadingDashboard(false);
      }
    };

    fetchDashboardContext();
  }, [appointmentData.patientId, appointmentData.isDependent]);

  const getDashboardLink = () => dashboardUrl;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header con navegación */}
      <div className="bg-white border-b border-gray-200 px-4 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push(getDashboardLink())}
              className="text-gray-600 hover:text-gray-900"
              disabled={isLoadingDashboard}
            >
              {isLoadingDashboard ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cargando...
                </>
              ) : (
                <>
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Volver al Dashboard
                </>
              )}
            </Button>
            <div className="h-6 w-px bg-gray-300" />
            <div>
              <h1 className="text-xl font-semibold text-gray-900">
                Pre-checkin Médico
              </h1>
              <p className="text-sm text-gray-600">
                {appointmentData.isDependent 
                  ? `Completando para ${appointmentData.patientFirstName} ${appointmentData.patientLastName}`
                  : 'Confirma tu información para la cita'
                }
              </p>
            </div>
          </div>
          
          {/* Usuario logueado */}
          <div className="flex items-center space-x-3">
            <div className="text-right">
              <p className="text-sm font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500">
                {appointmentData.isDependent && user.id === appointmentData.guardianInfo?.guardianId
                  ? `Responsable de ${appointmentData.patientFirstName}`
                  : 'Paciente'
                }
              </p>
            </div>
            {user.imageUrl && (
              <img
                src={user.imageUrl}
                alt="Usuario"
                className="h-8 w-8 rounded-full"
              />
            )}
          </div>
        </div>
      </div>

      {/* Contenido principal */}
      <div className="max-w-7xl mx-auto py-8 px-4">
        {/* Alert de contexto de usuario autenticado */}
        <div className="mb-6">
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div className="flex items-center space-x-3">
              <Shield className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  ¡Hola {user.firstName}! Estás completando el pre-checkin 
                  {appointmentData.isDependent && user.id === appointmentData.guardianInfo?.guardianId
                    ? ` como responsable de ${appointmentData.patientFirstName} ${appointmentData.patientLastName}` 
                    : ' para tu cita médica'
                  }
                </p>
                <p className="text-xs text-blue-700 mt-1">
                  {isLoadingDashboard ? (
                    <span className="flex items-center gap-2">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Determinando dashboard de destino...
                    </span>
                  ) : (
                    `Al finalizar, serás redirigido a tu ${
                      dashboardContext === 'guardian' ? 'dashboard de guardián' :
                      dashboardContext === 'doctor' ? 'dashboard de doctor' :
                      dashboardContext === 'assistant' ? 'dashboard de asistente' :
                      dashboardContext === 'admin' ? 'dashboard de administrador' :
                      'dashboard personal'
                    }.`
                  )}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Estado del pre-checkin */}
        {isAlreadyCompleted ? (
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center space-x-2 text-green-700">
                <Shield className="h-6 w-6" />
                <span className="font-semibold">Pre-checkin ya completado</span>
              </div>
              <p className="text-center text-sm text-green-600 mt-2">
                Completado el {format(new Date(appointmentData.preCheckinCompletedAt!), 'dd/MM/yyyy HH:mm', { locale: es })}
              </p>
              <div className="flex justify-center mt-4">
                <Button asChild variant="outline">
                  <Link href={getDashboardLink()}>
                    <Home className="h-4 w-4 mr-2" />
                    Ir al Dashboard
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : null}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Información de la cita */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <span>Información de la Cita</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Paciente */}
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {appointmentData.patientFirstName} {appointmentData.patientLastName}
                    </p>
                    <p className="text-sm text-gray-500">Paciente</p>
                  </div>
                </div>

                {/* Guardián (si aplica) */}
                {appointmentData.isDependent && appointmentData.guardianInfo && (
                  <div className="flex items-center space-x-3 bg-amber-50 p-3 rounded-lg">
                    <Shield className="h-4 w-4 text-amber-600" />
                    <div>
                      <p className="font-medium text-amber-900">
                        {appointmentData.guardianInfo.guardianFirstName} {appointmentData.guardianInfo.guardianLastName}
                      </p>
                      <p className="text-sm text-amber-700 capitalize">
                        {translateRelationship(appointmentData.guardianInfo.relationship)}
                      </p>
                    </div>
                  </div>
                )}

                {/* Doctor */}
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">
                      Dr. {appointmentData.doctorFirstName} {appointmentData.doctorLastName}
                    </p>
                    <p className="text-sm text-gray-500">Médico tratante</p>
                  </div>
                </div>

                {/* Fecha y hora */}
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900 capitalize">{appointmentDate}</p>
                    <p className="text-sm text-gray-500">{appointmentTime} - {appointmentEndTime}</p>
                  </div>
                </div>

                {/* Estado */}
                <div className="flex items-center space-x-3">
                                  <Badge variant={appointmentData.status === 'confirmed' ? 'default' : 'secondary'}>
                  {appointmentData.status === 'scheduled' && 'Programada'}
                  {appointmentData.status === 'pending_confirmation' && 'Pendiente de Confirmación'}
                  {appointmentData.status === 'confirmed' && 'Confirmada'}
                  {appointmentData.status === 'checked_in' && 'Paciente llegó'}
                  {appointmentData.status === 'in_progress' && 'En consulta'}
                  {appointmentData.status === 'completed' && 'Completada'}
                  {appointmentData.status === 'cancelled' && 'Cancelada'}
                  {appointmentData.status === 'no_show' && 'No asistió'}
                </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Formulario de pre-checkin */}
          <div className="lg:col-span-2">
            <PreCheckinFormImproved 
              appointmentData={appointmentData}
              isCompleted={isAlreadyCompleted}
              showUserContext={true}
              dashboardLink={getDashboardLink()}
            />
          </div>
        </div>
      </div>
    </div>
  );
}