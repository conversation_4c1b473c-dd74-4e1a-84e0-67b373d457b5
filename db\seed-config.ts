import { db } from './drizzle';
import { systemConfig } from './schema';

async function seedSystemConfig() {
  console.log('⚙️ Iniciando seed de configuración del sistema...');
  
  const configData = [
    {
      id: 1,
      key: 'system_country',
      value: {
        countryId: 1,
        countryCode: 'GT',
        countryName: 'Guatemala'
      },
      description: 'País principal del sistema',
      category: 'localization',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      key: 'system_timezone',
      value: {
        timezone: 'America/Guatemala',
        offset: '-06:00'
      },
      description: 'Zona horaria del sistema',
      category: 'localization',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      key: 'system_locale',
      value: {
        locale: 'es-GT',
        language: 'es',
        country: 'GT'
      },
      description: 'Configuración regional del sistema',
      category: 'localization',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      key: 'location_labels',
      value: {
        level1: 'Departamento',
        level2: 'Municipio',
        level1Placeholder: 'Selecciona departamento',
        level2Placeholder: 'Selecciona municipio'
      },
      description: 'Etiquetas de localización según país',
      category: 'localization',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const config of configData) {
    try {
      await db.insert(systemConfig).values(config);
      console.log(`✅ Configuración creada: ${config.key}`);
    } catch (error) {
      console.log(`⚠️ Configuración ya existe: ${config.key}`);
    }
  }
  
  console.log('🎉 Seed de configuración completado!');
}

// Ejecutar el seed si el script se ejecuta directamente
if (require.main === module) {
  seedSystemConfig()
    .then(() => {
      console.log('✨ Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error:', error);
      process.exit(1);
    });
} 