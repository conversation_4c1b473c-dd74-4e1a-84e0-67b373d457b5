CREATE TABLE "activity_types" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"color" text NOT NULL,
	"duration" integer NOT NULL,
	"requiresPatient" boolean DEFAULT true,
	"allowsRecurrence" boolean DEFAULT false,
	"icon" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "assistant_doctor_relations" (
	"id" text PRIMARY KEY NOT NULL,
	"assistantId" text NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text,
	"permissions" jsonb,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "association_codes" (
	"id" text PRIMARY KEY NOT NULL,
	"code" text NOT NULL,
	"patientId" text NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"usedBy" text,
	"usedAt" timestamp,
	"createdAt" timestamp DEFAULT now(),
	CONSTRAINT "association_codes_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "companies" (
	"id" text PRIMARY KEY NOT NULL,
	"businessName" text NOT NULL,
	"commercialName" text NOT NULL,
	"nit" text NOT NULL,
	"address" text NOT NULL,
	"phone" text,
	"email" text,
	"website" text,
	"legalRepresentative" text NOT NULL,
	"activityType" text NOT NULL,
	"taxRegime" text,
	"fiscalAddress" text,
	"contactPerson" text,
	"contactPhone" text,
	"contactEmail" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "companies_nit_unique" UNIQUE("nit")
);
--> statement-breakpoint
CREATE TABLE "consultories" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text,
	"type" text,
	"specialty" text,
	"capacity" integer DEFAULT 1,
	"floor" integer,
	"building" text,
	"address" text,
	"phone" text,
	"email" text,
	"countryId" integer,
	"departmentId" integer,
	"municipalityId" integer,
	"businessHours" jsonb,
	"equipment" jsonb,
	"services" jsonb,
	"isEmergencyCapable" boolean DEFAULT false,
	"hasAirConditioning" boolean DEFAULT true,
	"hasWaitingRoom" boolean DEFAULT true,
	"accessibility" jsonb,
	"active" boolean DEFAULT true,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "consultories_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "countries" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text,
	"phoneCode" text,
	"currency" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "countries_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "currencies" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"symbol" text NOT NULL,
	"exchangeRate" real DEFAULT 1 NOT NULL,
	"isDefault" boolean DEFAULT false,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "currencies_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "departments" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"countryId" integer NOT NULL,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "doctor_service_prices" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"serviceId" text NOT NULL,
	"customPrice" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ',
	"isActive" boolean DEFAULT true,
	"effectiveFrom" timestamp DEFAULT now(),
	"effectiveUntil" timestamp,
	"notes" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "document_types" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"shortName" text NOT NULL,
	"countryId" integer NOT NULL,
	"countryName" text NOT NULL,
	"format" text NOT NULL,
	"maxLength" integer NOT NULL,
	"minLength" integer NOT NULL,
	"isRequired" boolean DEFAULT false,
	"description" text,
	"example" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "education_levels" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"order" integer NOT NULL,
	"description" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "guardian_patient_relations" (
	"id" text PRIMARY KEY NOT NULL,
	"guardianId" text NOT NULL,
	"patientId" text NOT NULL,
	"relationship" text,
	"isPrimary" boolean DEFAULT false,
	"canMakeDecisions" boolean DEFAULT true,
	"validUntil" timestamp,
	"createdAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "marital_status" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"allowsSpouse" boolean DEFAULT false,
	"legalImplications" text,
	"description" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "media_sources" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"category" text NOT NULL,
	"trackingEnabled" boolean DEFAULT false,
	"cost" real DEFAULT 0,
	"description" text,
	"icon" text,
	"color" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "medical_service_tags" (
	"serviceId" text NOT NULL,
	"tagId" text NOT NULL,
	"createdAt" timestamp DEFAULT now(),
	CONSTRAINT "medical_service_tags_serviceId_tagId_pk" PRIMARY KEY("serviceId","tagId")
);
--> statement-breakpoint
CREATE TABLE "medical_services" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"code" text,
	"category" text NOT NULL,
	"basePrice" numeric(10, 2),
	"currency" text DEFAULT 'GTQ',
	"duration" integer,
	"requiresEquipment" boolean DEFAULT false,
	"requiresSpecialist" boolean DEFAULT false,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "medical_services_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "medical_specialties" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "medications" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"genericName" text NOT NULL,
	"brandName" text,
	"activeIngredient" text NOT NULL,
	"dosageForm" text NOT NULL,
	"strength" text NOT NULL,
	"concentration" text,
	"therapeuticClass" text NOT NULL,
	"pharmacologicalGroup" text,
	"atcCode" text,
	"indication" text,
	"contraindications" jsonb DEFAULT '[]'::jsonb,
	"sideEffects" jsonb DEFAULT '[]'::jsonb,
	"dosageInstructions" text,
	"warnings" jsonb DEFAULT '[]'::jsonb,
	"requiresPrescription" boolean DEFAULT false,
	"isControlled" boolean DEFAULT false,
	"controlledCategory" text,
	"manufacturer" text,
	"barcode" text,
	"ndc" text,
	"presentation" text,
	"storageConditions" text,
	"shelfLife" text,
	"description" text,
	"notes" text,
	"order" integer DEFAULT 0,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "municipalities" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"departmentId" integer NOT NULL,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "non_pathological_history" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"subcategory" text,
	"isPositive" boolean DEFAULT true,
	"importance" text NOT NULL,
	"ageRelevant" text NOT NULL,
	"description" text,
	"benefits" jsonb,
	"risks" jsonb,
	"duration" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "notifications" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"type" text NOT NULL,
	"title" text NOT NULL,
	"message" text NOT NULL,
	"read" boolean DEFAULT false,
	"data" jsonb,
	"createdAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "occupations" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pathological_history" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"icd11Code" text,
	"severity" text NOT NULL,
	"isHereditary" boolean DEFAULT false,
	"requiresSpecialistFollow" boolean DEFAULT false,
	"commonInChildren" boolean DEFAULT false,
	"riskLevel" text NOT NULL,
	"description" text,
	"symptoms" jsonb,
	"treatments" jsonb,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "registrationRequests" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"role" text NOT NULL,
	"status" text DEFAULT 'pending',
	"generalData" jsonb,
	"specificData" jsonb,
	"reviewedBy" text,
	"reviewedAt" timestamp,
	"reviewNotes" text,
	"rejectionReason" text,
	"submittedAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "relationships" (
	"id" integer PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "religions" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"description" text,
	"order" integer DEFAULT 0,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "service_tags" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"color" text DEFAULT 'blue',
	"category" text DEFAULT 'general',
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "service_tags_name_unique" UNIQUE("name")
);
--> statement-breakpoint
CREATE TABLE "symptoms" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"subcategory" text,
	"icdCode" text,
	"isSymptom" boolean DEFAULT true,
	"description" text,
	"commonCauses" jsonb DEFAULT '[]'::jsonb,
	"severity" text DEFAULT 'low',
	"bodySystem" text,
	"order" integer DEFAULT 0,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "user" (
	"id" text PRIMARY KEY NOT NULL,
	"email" text NOT NULL,
	"emailVerified" boolean DEFAULT false,
	"name" text,
	"image" text,
	"firstName" text,
	"lastName" text,
	"documentType" text,
	"documentNumber" text,
	"dateOfBirth" timestamp,
	"gender" text,
	"phone" text,
	"alternativePhone" text,
	"address" text,
	"countryId" integer,
	"departmentId" integer,
	"municipalityId" integer,
	"occupationId" integer,
	"emergencyContact" text,
	"emergencyPhone" text,
	"emergencyRelationshipId" integer,
	"overallStatus" text DEFAULT 'pending',
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "user_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE "user_roles" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"role" text NOT NULL,
	"status" text DEFAULT 'pending',
	"consultoryId" text,
	"specialtyId" integer,
	"preferredDoctorId" text,
	"medicalLicense" text,
	"roleData" jsonb,
	"approvedBy" text,
	"approvedAt" timestamp,
	"rejectedBy" text,
	"rejectedAt" timestamp,
	"rejectionReason" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_assistantId_user_id_fk" FOREIGN KEY ("assistantId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "association_codes" ADD CONSTRAINT "association_codes_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "association_codes" ADD CONSTRAINT "association_codes_usedBy_user_id_fk" FOREIGN KEY ("usedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consultories" ADD CONSTRAINT "consultories_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consultories" ADD CONSTRAINT "consultories_departmentId_departments_id_fk" FOREIGN KEY ("departmentId") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "consultories" ADD CONSTRAINT "consultories_municipalityId_municipalities_id_fk" FOREIGN KEY ("municipalityId") REFERENCES "public"."municipalities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "departments" ADD CONSTRAINT "departments_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_service_prices" ADD CONSTRAINT "doctor_service_prices_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_service_prices" ADD CONSTRAINT "doctor_service_prices_serviceId_medical_services_id_fk" FOREIGN KEY ("serviceId") REFERENCES "public"."medical_services"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_types" ADD CONSTRAINT "document_types_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "guardian_patient_relations" ADD CONSTRAINT "guardian_patient_relations_guardianId_user_id_fk" FOREIGN KEY ("guardianId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "guardian_patient_relations" ADD CONSTRAINT "guardian_patient_relations_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_service_tags" ADD CONSTRAINT "medical_service_tags_serviceId_medical_services_id_fk" FOREIGN KEY ("serviceId") REFERENCES "public"."medical_services"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_service_tags" ADD CONSTRAINT "medical_service_tags_tagId_service_tags_id_fk" FOREIGN KEY ("tagId") REFERENCES "public"."service_tags"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "municipalities" ADD CONSTRAINT "municipalities_departmentId_departments_id_fk" FOREIGN KEY ("departmentId") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD CONSTRAINT "registrationRequests_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD CONSTRAINT "registrationRequests_reviewedBy_user_id_fk" FOREIGN KEY ("reviewedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_departmentId_departments_id_fk" FOREIGN KEY ("departmentId") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_municipalityId_municipalities_id_fk" FOREIGN KEY ("municipalityId") REFERENCES "public"."municipalities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_occupationId_occupations_id_fk" FOREIGN KEY ("occupationId") REFERENCES "public"."occupations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_emergencyRelationshipId_relationships_id_fk" FOREIGN KEY ("emergencyRelationshipId") REFERENCES "public"."relationships"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_specialtyId_medical_specialties_id_fk" FOREIGN KEY ("specialtyId") REFERENCES "public"."medical_specialties"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_preferredDoctorId_user_id_fk" FOREIGN KEY ("preferredDoctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_approvedBy_user_id_fk" FOREIGN KEY ("approvedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_rejectedBy_user_id_fk" FOREIGN KEY ("rejectedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "activity_types_category_idx" ON "activity_types" USING btree ("category");--> statement-breakpoint
CREATE INDEX "activity_types_active_idx" ON "activity_types" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "activity_types_order_idx" ON "activity_types" USING btree ("order");--> statement-breakpoint
CREATE UNIQUE INDEX "assistant_doctor_idx" ON "assistant_doctor_relations" USING btree ("assistantId","doctorId");--> statement-breakpoint
CREATE INDEX "assistant_relations_idx" ON "assistant_doctor_relations" USING btree ("assistantId");--> statement-breakpoint
CREATE INDEX "doctor_relations_idx" ON "assistant_doctor_relations" USING btree ("doctorId");--> statement-breakpoint
CREATE UNIQUE INDEX "association_code_idx" ON "association_codes" USING btree ("code");--> statement-breakpoint
CREATE INDEX "association_patient_idx" ON "association_codes" USING btree ("patientId");--> statement-breakpoint
CREATE INDEX "association_expires_idx" ON "association_codes" USING btree ("expiresAt");--> statement-breakpoint
CREATE UNIQUE INDEX "companies_nit_idx" ON "companies" USING btree ("nit");--> statement-breakpoint
CREATE INDEX "companies_active_idx" ON "companies" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "companies_business_name_idx" ON "companies" USING btree ("businessName");--> statement-breakpoint
CREATE INDEX "companies_activity_type_idx" ON "companies" USING btree ("activityType");--> statement-breakpoint
CREATE INDEX "consultories_active_idx" ON "consultories" USING btree ("active");--> statement-breakpoint
CREATE INDEX "consultories_is_active_idx" ON "consultories" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "consultories_code_idx" ON "consultories" USING btree ("code");--> statement-breakpoint
CREATE INDEX "consultories_type_idx" ON "consultories" USING btree ("type");--> statement-breakpoint
CREATE INDEX "consultories_building_idx" ON "consultories" USING btree ("building");--> statement-breakpoint
CREATE INDEX "consultories_emergency_idx" ON "consultories" USING btree ("isEmergencyCapable");--> statement-breakpoint
CREATE UNIQUE INDEX "currencies_code_idx" ON "currencies" USING btree ("code");--> statement-breakpoint
CREATE INDEX "currencies_active_idx" ON "currencies" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "currencies_default_idx" ON "currencies" USING btree ("isDefault");--> statement-breakpoint
CREATE UNIQUE INDEX "doctor_service_active_idx" ON "doctor_service_prices" USING btree ("doctorId","serviceId") WHERE "doctor_service_prices"."isActive" = true;--> statement-breakpoint
CREATE INDEX "doctor_service_prices_doctor_idx" ON "doctor_service_prices" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "doctor_service_prices_service_idx" ON "doctor_service_prices" USING btree ("serviceId");--> statement-breakpoint
CREATE INDEX "doctor_service_prices_active_idx" ON "doctor_service_prices" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "doctor_service_prices_price_idx" ON "doctor_service_prices" USING btree ("customPrice");--> statement-breakpoint
CREATE INDEX "doctor_service_prices_effective_idx" ON "doctor_service_prices" USING btree ("effectiveFrom","effectiveUntil");--> statement-breakpoint
CREATE INDEX "document_types_country_idx" ON "document_types" USING btree ("countryId");--> statement-breakpoint
CREATE INDEX "document_types_active_idx" ON "document_types" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "document_types_required_idx" ON "document_types" USING btree ("isRequired");--> statement-breakpoint
CREATE UNIQUE INDEX "education_levels_order_idx" ON "education_levels" USING btree ("order");--> statement-breakpoint
CREATE INDEX "education_levels_active_idx" ON "education_levels" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "guardian_patient_idx" ON "guardian_patient_relations" USING btree ("guardianId","patientId");--> statement-breakpoint
CREATE INDEX "guardian_relations_idx" ON "guardian_patient_relations" USING btree ("guardianId");--> statement-breakpoint
CREATE INDEX "patient_relations_idx" ON "guardian_patient_relations" USING btree ("patientId");--> statement-breakpoint
CREATE UNIQUE INDEX "marital_status_order_idx" ON "marital_status" USING btree ("order");--> statement-breakpoint
CREATE INDEX "marital_status_active_idx" ON "marital_status" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "marital_status_spouse_idx" ON "marital_status" USING btree ("allowsSpouse");--> statement-breakpoint
CREATE INDEX "media_sources_type_idx" ON "media_sources" USING btree ("type");--> statement-breakpoint
CREATE INDEX "media_sources_category_idx" ON "media_sources" USING btree ("category");--> statement-breakpoint
CREATE INDEX "media_sources_active_idx" ON "media_sources" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "media_sources_tracking_idx" ON "media_sources" USING btree ("trackingEnabled");--> statement-breakpoint
CREATE INDEX "medical_service_tags_service_idx" ON "medical_service_tags" USING btree ("serviceId");--> statement-breakpoint
CREATE INDEX "medical_service_tags_tag_idx" ON "medical_service_tags" USING btree ("tagId");--> statement-breakpoint
CREATE UNIQUE INDEX "medical_services_code_idx" ON "medical_services" USING btree ("code");--> statement-breakpoint
CREATE INDEX "medical_services_category_idx" ON "medical_services" USING btree ("category");--> statement-breakpoint
CREATE INDEX "medical_services_active_idx" ON "medical_services" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "medical_services_name_idx" ON "medical_services" USING btree ("name");--> statement-breakpoint
CREATE INDEX "medical_services_price_idx" ON "medical_services" USING btree ("basePrice");--> statement-breakpoint
CREATE INDEX "medications_name_idx" ON "medications" USING btree ("name");--> statement-breakpoint
CREATE INDEX "medications_generic_idx" ON "medications" USING btree ("genericName");--> statement-breakpoint
CREATE INDEX "medications_active_ingredient_idx" ON "medications" USING btree ("activeIngredient");--> statement-breakpoint
CREATE INDEX "medications_therapeutic_idx" ON "medications" USING btree ("therapeuticClass");--> statement-breakpoint
CREATE INDEX "medications_controlled_idx" ON "medications" USING btree ("isControlled");--> statement-breakpoint
CREATE INDEX "medications_active_idx" ON "medications" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "non_pathological_history_category_idx" ON "non_pathological_history" USING btree ("category");--> statement-breakpoint
CREATE INDEX "non_pathological_history_subcategory_idx" ON "non_pathological_history" USING btree ("subcategory");--> statement-breakpoint
CREATE INDEX "non_pathological_history_positive_idx" ON "non_pathological_history" USING btree ("isPositive");--> statement-breakpoint
CREATE INDEX "non_pathological_history_importance_idx" ON "non_pathological_history" USING btree ("importance");--> statement-breakpoint
CREATE INDEX "non_pathological_history_age_idx" ON "non_pathological_history" USING btree ("ageRelevant");--> statement-breakpoint
CREATE INDEX "non_pathological_history_active_idx" ON "non_pathological_history" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "notifications_user_idx" ON "notifications" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "notifications_read_idx" ON "notifications" USING btree ("read");--> statement-breakpoint
CREATE INDEX "notifications_type_idx" ON "notifications" USING btree ("type");--> statement-breakpoint
CREATE INDEX "pathological_history_category_idx" ON "pathological_history" USING btree ("category");--> statement-breakpoint
CREATE INDEX "pathological_history_severity_idx" ON "pathological_history" USING btree ("severity");--> statement-breakpoint
CREATE INDEX "pathological_history_risk_idx" ON "pathological_history" USING btree ("riskLevel");--> statement-breakpoint
CREATE INDEX "pathological_history_hereditary_idx" ON "pathological_history" USING btree ("isHereditary");--> statement-breakpoint
CREATE INDEX "pathological_history_children_idx" ON "pathological_history" USING btree ("commonInChildren");--> statement-breakpoint
CREATE INDEX "pathological_history_active_idx" ON "pathological_history" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "pathological_history_icd11_idx" ON "pathological_history" USING btree ("icd11Code");--> statement-breakpoint
CREATE INDEX "registration_requests_user_idx" ON "registrationRequests" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "registration_requests_status_idx" ON "registrationRequests" USING btree ("status");--> statement-breakpoint
CREATE INDEX "registration_requests_submitted_idx" ON "registrationRequests" USING btree ("submittedAt");--> statement-breakpoint
CREATE INDEX "religions_name_idx" ON "religions" USING btree ("name");--> statement-breakpoint
CREATE INDEX "religions_category_idx" ON "religions" USING btree ("category");--> statement-breakpoint
CREATE INDEX "religions_active_idx" ON "religions" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "service_tags_name_idx" ON "service_tags" USING btree ("name");--> statement-breakpoint
CREATE INDEX "service_tags_category_idx" ON "service_tags" USING btree ("category");--> statement-breakpoint
CREATE INDEX "service_tags_active_idx" ON "service_tags" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "symptoms_name_idx" ON "symptoms" USING btree ("name");--> statement-breakpoint
CREATE INDEX "symptoms_category_idx" ON "symptoms" USING btree ("category");--> statement-breakpoint
CREATE INDEX "symptoms_icd_idx" ON "symptoms" USING btree ("icdCode");--> statement-breakpoint
CREATE INDEX "symptoms_active_idx" ON "symptoms" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "email_idx" ON "user" USING btree ("email");--> statement-breakpoint
CREATE INDEX "overall_status_idx" ON "user" USING btree ("overallStatus");--> statement-breakpoint
CREATE UNIQUE INDEX "document_idx" ON "user" USING btree ("documentType","documentNumber");--> statement-breakpoint
CREATE UNIQUE INDEX "user_role_idx" ON "user_roles" USING btree ("userId","role");--> statement-breakpoint
CREATE INDEX "user_roles_user_idx" ON "user_roles" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "user_roles_role_idx" ON "user_roles" USING btree ("role");--> statement-breakpoint
CREATE INDEX "user_roles_status_idx" ON "user_roles" USING btree ("status");