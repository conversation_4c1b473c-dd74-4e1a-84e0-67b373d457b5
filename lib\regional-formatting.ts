import { formatDate, formatDateTime, formatTime, formatCurrency, formatNumber } from './utils';

// Interface para la configuración regional
interface RegionalConfig {
  dateFormat: string;
  dateTimeFormat: string;
  timeFormat: string;
  currency: string;
  currencySymbol: string;
  currencyPosition: 'before' | 'after';
  locale: string;
  timezone: string;
  decimalSeparator: string;
  thousandsSeparator: string;
  weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
}

// Configuración por defecto
const defaultConfig: RegionalConfig = {
  dateFormat: 'dd/MM/yyyy',
  dateTimeFormat: 'dd/MM/yyyy HH:mm',
  timeFormat: 'HH:mm',
  currency: 'GTQ',
  currencySymbol: 'Q',
  currencyPosition: 'before',
  locale: 'es-GT',
  timezone: 'America/Guatemala',
  decimalSeparator: '.',
  thousandsSeparator: ',',
  weekStartsOn: 1
};

// Variable global para almacenar la configuración actual
let currentConfig: RegionalConfig = defaultConfig;

// Función para establecer la configuración regional actual
export function setRegionalConfig(config: Partial<RegionalConfig>) {
  currentConfig = { ...currentConfig, ...config };
}

// Función para obtener la configuración regional actual
export function getRegionalConfig(): RegionalConfig {
  return currentConfig;
}

// Funciones de formateo que usan automáticamente la configuración regional

export function formatRegionalDate(date: string | Date | null | undefined, customFormat?: string): string {
  return formatDate(date, customFormat || currentConfig.dateFormat);
}

export function formatRegionalDateTime(date: string | Date | null | undefined, customFormat?: string): string {
  return formatDate(date, customFormat || currentConfig.dateTimeFormat);
}

export function formatRegionalTime(date: string | Date | null | undefined, customFormat?: string): string {
  return formatDate(date, customFormat || currentConfig.timeFormat);
}

export function formatRegionalCurrency(amount: number | string | null | undefined): string {
  return formatCurrency(
    amount,
    currentConfig.currency,
    currentConfig.currencySymbol,
    currentConfig.currencyPosition,
    currentConfig.decimalSeparator,
    currentConfig.thousandsSeparator
  );
}

export function formatRegionalNumber(
  value: number | string | null | undefined,
  decimals: number = 2
): string {
  return formatNumber(
    value,
    decimals,
    currentConfig.decimalSeparator,
    currentConfig.thousandsSeparator
  );
}

// Función para obtener el placeholder del formato de fecha
export function getDateFormatPlaceholder(): string {
  // Convert the date format to a readable placeholder
  // dd/MM/yyyy -> dd/mm/aaaa (Spanish)
  // MM/dd/yyyy -> mm/dd/yyyy (English)
  // yyyy-MM-dd -> aaaa-mm-dd
  return currentConfig.dateFormat
    .toLowerCase()
    .replace(/yyyy/g, 'aaaa')
    .replace(/yy/g, 'aa');
}

// Hook personalizado para usar en componentes React
export function useRegionalFormatting() {
  return {
    formatDate: formatRegionalDate,
    formatDateTime: formatRegionalDateTime,
    formatTime: formatRegionalTime,
    formatCurrency: formatRegionalCurrency,
    formatNumber: formatRegionalNumber,
    getDatePlaceholder: getDateFormatPlaceholder,
    config: currentConfig
  };
}