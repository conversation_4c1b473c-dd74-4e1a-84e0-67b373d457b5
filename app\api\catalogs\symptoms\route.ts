import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { symptoms } from '@/db/schema';
import { eq, and, desc, ilike, count, or } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET /api/catalogs/symptoms - Listar síntomas con filtros
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all'; // all, active, inactive
    const category = searchParams.get('category') || '';
    const bodySystem = searchParams.get('bodySystem') || '';
    const severity = searchParams.get('severity') || '';
    const isSymptom = searchParams.get('isSymptom') || 'all'; // all, symptom, sign
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const offset = (page - 1) * limit;

    // Construir condiciones WHERE
    const conditions = [];
    
    if (status === 'active') {
      conditions.push(eq(symptoms.isActive, true));
    } else if (status === 'inactive') {
      conditions.push(eq(symptoms.isActive, false));
    }

    if (category && category !== 'all') {
      conditions.push(eq(symptoms.category, category));
    }

    if (bodySystem && bodySystem !== 'all') {
      conditions.push(eq(symptoms.bodySystem, bodySystem));
    }

    if (severity && severity !== 'all') {
      conditions.push(eq(symptoms.severity, severity));
    }

    if (isSymptom === 'symptom') {
      conditions.push(eq(symptoms.isSymptom, true));
    } else if (isSymptom === 'sign') {
      conditions.push(eq(symptoms.isSymptom, false));
    }

    if (search) {
      // Buscar en nombre, descripción y causas comunes
      conditions.push(
        or(
          ilike(symptoms.name, `%${search}%`),
          ilike(symptoms.description, `%${search}%`),
          ilike(symptoms.subcategory, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Obtener total de registros
    const totalResult = await db
      .select({ count: count() })
      .from(symptoms)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Obtener datos con paginación
    const orderColumn = sortBy === 'category' ? symptoms.category : 
                       sortBy === 'severity' ? symptoms.severity :
                       sortBy === 'bodySystem' ? symptoms.bodySystem :
                       sortBy === 'type' ? symptoms.isSymptom :
                       symptoms.name;

    const data = await db
      .select()
      .from(symptoms)
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(orderColumn) : orderColumn)
      .limit(limit)
      .offset(offset);

    // Obtener listas para filtros
    const categories = await db
      .selectDistinct({ category: symptoms.category })
      .from(symptoms)
      .where(eq(symptoms.isActive, true))
      .orderBy(symptoms.category);

    const bodySystems = await db
      .selectDistinct({ bodySystem: symptoms.bodySystem })
      .from(symptoms)
      .where(eq(symptoms.isActive, true))
      .orderBy(symptoms.bodySystem);

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        categories: categories.map(c => c.category).filter(Boolean),
        bodySystems: bodySystems.map(b => b.bodySystem).filter(Boolean),
      },
    });

  } catch (error: any) {
    console.error('Error fetching symptoms:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/catalogs/symptoms - Crear nuevo síntoma
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validaciones básicas
    if (!body.name || !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre del síntoma es requerido' },
        { status: 400 }
      );
    }

    if (!body.category || !body.category.trim()) {
      return NextResponse.json(
        { error: 'La categoría es requerida' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un síntoma con el mismo nombre
    const existingSymptom = await db
      .select()
      .from(symptoms)
      .where(eq(symptoms.name, body.name.trim()))
      .limit(1);

    if (existingSymptom.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un síntoma con ese nombre' },
        { status: 400 }
      );
    }

    // Crear nuevo síntoma
    const newSymptom = {
      id: nanoid(),
      name: body.name.trim(),
      category: body.category.trim(),
      subcategory: body.subcategory?.trim() || null,
      icdCode: body.icdCode?.trim() || null,
      isSymptom: body.isSymptom !== undefined ? body.isSymptom : true,
      description: body.description?.trim() || null,
      commonCauses: Array.isArray(body.commonCauses) ? body.commonCauses : [],
      severity: body.severity || 'low',
      bodySystem: body.bodySystem?.trim() || null,
      order: body.order || 0,
      isActive: body.isActive !== undefined ? body.isActive : true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await db.insert(symptoms).values(newSymptom);

    return NextResponse.json({
      success: true,
      message: 'Síntoma creado exitosamente',
      data: newSymptom,
    });

  } catch (error: any) {
    console.error('Error creating symptom:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/catalogs/symptoms - Actualizar síntoma
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { error: 'El ID es requerido para actualizar' },
        { status: 400 }
      );
    }

    // Verificar que el síntoma existe
    const existingSymptom = await db
      .select()
      .from(symptoms)
      .where(eq(symptoms.id, body.id))
      .limit(1);

    if (!existingSymptom.length) {
      return NextResponse.json(
        { error: 'Síntoma no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones básicas para actualización
    if (body.name && !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre del síntoma no puede estar vacío' },
        { status: 400 }
      );
    }

    // Preparar datos para actualizar
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Solo actualizar campos que se proporcionan
    if (body.name !== undefined) updateData.name = body.name.trim();
    if (body.category !== undefined) updateData.category = body.category.trim();
    if (body.subcategory !== undefined) updateData.subcategory = body.subcategory?.trim() || null;
    if (body.icdCode !== undefined) updateData.icdCode = body.icdCode?.trim() || null;
    if (body.isSymptom !== undefined) updateData.isSymptom = body.isSymptom;
    if (body.description !== undefined) updateData.description = body.description?.trim() || null;
    if (body.commonCauses !== undefined) updateData.commonCauses = Array.isArray(body.commonCauses) ? body.commonCauses : [];
    if (body.severity !== undefined) updateData.severity = body.severity;
    if (body.bodySystem !== undefined) updateData.bodySystem = body.bodySystem?.trim() || null;
    if (body.order !== undefined) updateData.order = body.order || 0;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;

    // Actualizar síntoma
    await db
      .update(symptoms)
      .set(updateData)
      .where(eq(symptoms.id, body.id));

    return NextResponse.json({
      success: true,
      message: 'Síntoma actualizado exitosamente',
    });

  } catch (error: any) {
    console.error('Error updating symptom:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}