import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { departments, countries } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener departamento por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const department = await db
      .select({
        id: departments.id,
        name: departments.name,
        countryId: departments.countryId,
        countryName: countries.name,
        isActive: departments.isActive,
        createdAt: departments.createdAt,
        updatedAt: departments.updatedAt
      })
      .from(departments)
      .leftJoin(countries, eq(departments.countryId, countries.id))
      .where(eq(departments.id, id))
      .limit(1);

    if (department.length === 0) {
      return NextResponse.json({ error: 'Departamento no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: department[0] 
    });

  } catch (error) {
    console.error('Error fetching department:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar departamento
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar departamentos.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, countryId, isActive } = body;

    // Validar datos requeridos
    if (!name || !countryId) {
      return NextResponse.json({ 
        error: 'Nombre y país son requeridos' 
      }, { status: 400 });
    }

    // Verificar que el departamento existe
    const existingDepartment = await db
      .select()
      .from(departments)
      .where(eq(departments.id, id))
      .limit(1);

    if (existingDepartment.length === 0) {
      return NextResponse.json({ error: 'Departamento no encontrado' }, { status: 404 });
    }

    // Verificar que el país existe
    const country = await db
      .select()
      .from(countries)
      .where(eq(countries.id, countryId))
      .limit(1);

    if (country.length === 0) {
      return NextResponse.json({ error: 'País no encontrado' }, { status: 404 });
    }

    // Actualizar departamento
    const [updatedDepartment] = await db
      .update(departments)
      .set({
        name,
        countryId: parseInt(countryId),
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(departments.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedDepartment,
      message: 'Departamento actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar departamento
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar departamentos.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que el departamento existe
    const existingDepartment = await db
      .select()
      .from(departments)
      .where(eq(departments.id, id))
      .limit(1);

    if (existingDepartment.length === 0) {
      return NextResponse.json({ error: 'Departamento no encontrado' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(departments)
        .where(eq(departments.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Departamento eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedDepartment] = await db
        .update(departments)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(departments.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedDepartment,
        message: 'Departamento desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting department:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}