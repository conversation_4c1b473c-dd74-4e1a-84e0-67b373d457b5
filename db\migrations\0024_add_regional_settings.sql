-- Migration: Add regional settings to consultories and seed system config
-- Description: Implements hybrid regional configuration system

-- Add regional_settings column to consultories table
ALTER TABLE "consultories" ADD COLUMN "regional_settings" jsonb DEFAULT '{
  "dateFormat": "dd/MM/yyyy",
  "dateTimeFormat": "dd/MM/yyyy HH:mm",
  "timeFormat": "HH:mm",
  "currency": "GTQ",
  "currencySymbol": "Q",
  "currencyPosition": "before",
  "locale": "es-GT",
  "timezone": "America/Guatemala",
  "decimalSeparator": ".",
  "thousandsSeparator": ",",
  "weekStartsOn": 1
}'::jsonb;

-- Insert default regional configurations into system_config
INSERT INTO "system_config" (key, value, description, category, active) VALUES
('regional.dateFormat', '"dd/MM/yyyy"', 'Formato de fecha predeterminado del sistema', 'regional', true),
('regional.dateTimeFormat', '"dd/MM/yyyy HH:mm"', 'Formato de fecha y hora predeterminado', 'regional', true),
('regional.timeFormat', '"HH:mm"', 'Formato de hora predeterminado (24 horas)', 'regional', true),
('regional.currency', '"GTQ"', 'Moneda predeterminada del sistema', 'regional', true),
('regional.currencySymbol', '"Q"', 'Símbolo de moneda predeterminado', 'regional', true),
('regional.currencyPosition', '"before"', 'Posición del símbolo de moneda (before/after)', 'regional', true),
('regional.locale', '"es-GT"', 'Configuración regional predeterminada', 'regional', true),
('regional.timezone', '"America/Guatemala"', 'Zona horaria predeterminada', 'regional', true),
('regional.decimalSeparator', '"."', 'Separador decimal para números', 'regional', true),
('regional.thousandsSeparator', '","', 'Separador de miles para números', 'regional', true),
('regional.weekStartsOn', '1', 'Día de inicio de semana (0=Domingo, 1=Lunes)', 'regional', true)
ON CONFLICT (key) DO UPDATE SET
  value = EXCLUDED.value,
  description = EXCLUDED.description,
  category = EXCLUDED.category,
  active = EXCLUDED.active,
  updated_at = now();

-- Create index for better performance
CREATE INDEX IF NOT EXISTS "consultories_regional_settings_idx" ON "consultories" USING gin("regional_settings");
CREATE INDEX IF NOT EXISTS "system_config_category_idx" ON "system_config" ("category") WHERE active = true;