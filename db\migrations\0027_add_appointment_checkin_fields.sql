-- Migración para agregar campos de check-in y no-show a appointments
-- Fecha: 2024-07-26

-- Agregar nuevos campos para el flujo mejorado de citas
ALTER TABLE "appointments" ADD COLUMN "checkedInBy" text;
ALTER TABLE "appointments" ADD COLUMN "noShowReason" text;

-- Agregar foreign key constraint para checkedInBy
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_checkedInBy_user_id_fk" FOREIGN KEY ("checkedInBy") REFERENCES "user"("id") ON DELETE no action ON UPDATE no action;

-- Actualizar el comentario del status para incluir checked_in
-- No se puede actualizar comentarios en PostgreSQL, pero documentamos aquí:
-- status puede ser: scheduled, confirmed, checked_in, in_progress, completed, cancelled, no_show