import { Resend } from 'resend';
import crypto from 'crypto';

const resend = new Resend(process.env.RESEND_API_KEY);

// Función para generar token de check-in
export function generateCheckInToken(appointmentId: string): string {
  const secret = process.env.CHECKIN_SECRET || 'default-secret-key';
  const timestamp = Date.now().toString();
  const data = `${appointmentId}-${timestamp}`;
  
  return crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('hex')
    .substring(0, 32);
}

// Función para generar enlace de check-in completo
export function generateCheckInLink(appointmentId: string): string {
  const token = generateCheckInToken(appointmentId);
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
  return `${baseUrl}/check-in/${appointmentId}/${token}`;
}

// Función para traducir relaciones de inglés a español
function translateRelationship(relationship?: string): string {
  if (!relationship) return 'responsable';
  
  const translations: Record<string, string> = {
    'parent': 'padre/madre',
    'father': 'padre', 
    'mother': 'madre',
    'guardian': 'tutor',
    'grandparent': 'abuelo/abuela',
    'grandfather': 'abuelo',
    'grandmother': 'abuela',
    'uncle': 'tío',
    'aunt': 'tía',
    'sibling': 'hermano/hermana',
    'brother': 'hermano',
    'sister': 'hermana',
    'spouse': 'cónyuge',
    'caregiver': 'cuidador',
    'legal_guardian': 'tutor legal',
    'responsable': 'responsable',
    'tutor': 'tutor',
    'encargado': 'encargado'
  };
  
  // Buscar traducción (insensible a mayúsculas)
  const key = relationship.toLowerCase();
  return translations[key] || relationship || 'responsable';
}

export interface EmailConfig {
  to: string;
  subject: string;
  html: string;
  from?: string;
}

export async function sendEmail({ to, subject, html, from = process.env.RESEND_FROM_EMAIL || 'Doctora Barbara <<EMAIL>>' }: EmailConfig) {
  try {
    if (!process.env.RESEND_API_KEY) {
      console.warn('RESEND_API_KEY not configured - email not sent');
      return { success: false, error: 'Email service not configured' };
    }

    const data = await resend.emails.send({
      from,
      to: [to],
      subject,
      html,
    });

    console.log('Email sent successfully:', { to, subject, messageId: data.id });
    return { success: true, data };
  } catch (error) {
    console.error('Error sending email:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
  }
}

// Templates de email específicos
export const emailTemplates = {
  // Template para invitación de activación de cuenta
  accountActivation: (data: {
    patientName: string;
    doctorName: string;
    consultoryName: string;
    activationLink: string;
    appointmentDate: string;
    recipientType?: 'guardian' | 'patient';
    guardianName?: string;
    relationship?: string;
  }) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Invitación para Crear Cuenta Médica</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { background: #10b981; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>Invitación para Crear Cuenta Médica</h1>
        </div>
        <div class="content">
          ${data.recipientType === 'guardian' ? `
            <h2>Hola ${data.guardianName || 'Encargado'},</h2>
            <p>Como <strong>${translateRelationship(data.relationship)}</strong> de <strong>${data.patientName}</strong>, el Dr. <strong>${data.doctorName}</strong> de <strong>${data.consultoryName}</strong> ha programado una cita médica para el <strong>${data.appointmentDate}</strong>.</p>
            
            <p>Hemos creado una cuenta para ${data.patientName} que requiere activación. Tienes dos opciones:</p>
            
            <div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h3>Opción 1: Gestionar como responsable</h3>
              <p>Puedes continuar gestionando las citas desde tu cuenta actual sin necesidad de activar la cuenta del paciente.</p>
            </div>
            
            <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h3>Opción 2: Activar cuenta del paciente</h3>
              <p>Si deseas que ${data.patientName} tenga su propia cuenta para acceder a su información médica:</p>
              <a href="${data.activationLink}" class="button">Activar Cuenta de ${data.patientName}</a>
            </div>
          ` : `
            <h2>Hola ${data.patientName},</h2>
            <p>El Dr. <strong>${data.doctorName}</strong> de <strong>${data.consultoryName}</strong> te ha programado una cita médica para el <strong>${data.appointmentDate}</strong>.</p>
            
            <p>Para confirmar tu asistencia y completar tu información médica, necesitas activar tu cuenta:</p>
            
            <a href="${data.activationLink}" class="button">Activar Mi Cuenta</a>
          `}
          
          <p><strong>¿Qué permite la cuenta del paciente?</strong></p>
          <ul>
            <li>Confirmar asistencia a citas</li>
            <li>Ver historial médico</li>
            <li>Recibir recordatorios automáticos</li>
            <li>Acceder a recetas y tratamientos</li>
          </ul>
          
          <p>Si tienes alguna pregunta, no dudes en contactarnos.</p>
        </div>
        <div class="footer">
          <p>Este enlace expira en 7 días. Si no puedes activar tu cuenta, contacta al consultorio.</p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Template para pre-checkin de paciente normal
  preCheckinNormal: (data: {
    patientName: string;
    doctorName: string;
    appointmentDate: string;
    appointmentTime: string;
    preCheckinLink: string;
    consultoryName: string;
  }) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Pre-checkin para tu cita médica</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
        .appointment-info { background: white; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #3b82f6; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>📋 Pre-checkin para tu cita</h1>
        </div>
        <div class="content">
          <h2>Hola ${data.patientName},</h2>
          <p>Tu cita médica está próxima. Por favor completa el pre-checkin para agilizar tu atención:</p>
          
          <div class="appointment-info">
            <h3>📅 Detalles de tu cita:</h3>
            <p><strong>Doctor:</strong> ${data.doctorName}</p>
            <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
            <p><strong>Hora:</strong> ${data.appointmentTime}</p>
            <p><strong>Lugar:</strong> ${data.consultoryName}</p>
          </div>
          
          <a href="${data.preCheckinLink}" class="button">Completar Pre-checkin</a>
          
          <p><strong>El pre-checkin incluye:</strong></p>
          <ul>
            <li>✅ Confirmar tu asistencia</li>
            <li>📝 Actualizar síntomas actuales</li>
            <li>💊 Informar medicamentos que tomas</li>
            <li>📋 Revisar tu información personal</li>
          </ul>
          
          <p><em>Completar el pre-checkin toma solo 2-3 minutos y hace tu cita más eficiente.</em></p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Template para pre-checkin de dependiente (enviado al guardián)
  preCheckinDependent: (data: {
    guardianName: string;
    patientName: string;
    relationship: string;
    doctorName: string;
    appointmentDate: string;
    appointmentTime: string;
    preCheckinLink: string;
    consultoryName: string;
  }) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Pre-checkin para ${data.patientName}</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .button { background: #f59e0b; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
        .appointment-info { background: white; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        .guardian-note { background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>👨‍👩‍👧‍👦 Pre-checkin para ${data.patientName}</h1>
        </div>
        <div class="content">
          <h2>Hola ${data.guardianName},</h2>
          <p>Como <strong>${translateRelationship(data.relationship)}</strong> de <strong>${data.patientName}</strong>, necesitamos que completes el pre-checkin para su próxima cita médica:</p>
          
          <div class="appointment-info">
            <h3>📅 Detalles de la cita:</h3>
            <p><strong>Paciente:</strong> ${data.patientName}</p>
            <p><strong>Doctor:</strong> ${data.doctorName}</p>
            <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
            <p><strong>Hora:</strong> ${data.appointmentTime}</p>
            <p><strong>Lugar:</strong> ${data.consultoryName}</p>
          </div>
          
          <div class="guardian-note">
            <p><strong>📝 Importante:</strong> Estarás completando el pre-checkin en nombre de ${data.patientName}.</p>
          </div>
          
          <a href="${data.preCheckinLink}" class="button">Completar Pre-checkin de ${data.patientName}</a>
          
          <p><strong>Información que necesitarás proporcionar:</strong></p>
          <ul>
            <li>✅ Confirmar asistencia de ${data.patientName}</li>
            <li>📝 Síntomas o molestias actuales</li>
            <li>💊 Medicamentos que está tomando</li>
            <li>👤 Quién acompañará a la cita</li>
          </ul>
        </div>
      </div>
    </body>
    </html>
  `,

  // Template de recordatorio 24h
  reminder24h: (data: {
    patientName: string;
    doctorName: string;
    appointmentDate: string;
    appointmentTime: string;
    consultoryAddress: string;
    consultoryName: string;
    appointmentId?: string;
    checkInLink?: string;
  }) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Recordatorio: Tu cita es mañana</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .appointment-info { background: white; padding: 15px; border-radius: 6px; margin: 20px 0; border-left: 4px solid #dc2626; }
        .checklist { background: #fef2f2; padding: 15px; border-radius: 6px; margin: 20px 0; }
        .checkin-section { background: #10b981; color: white; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center; }
        .checkin-button { background: white; color: #10b981; padding: 15px 30px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 15px 0; font-weight: bold; font-size: 16px; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>⏰ Tu cita es mañana</h1>
        </div>
        <div class="content">
          <h2>Hola ${data.patientName},</h2>
          <p>Este es un recordatorio de que tienes una cita médica programada para <strong>mañana</strong>:</p>
          
          <div class="appointment-info">
            <h3>📅 Detalles de tu cita:</h3>
            <p><strong>Doctor:</strong> ${data.doctorName}</p>
            <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
            <p><strong>Hora:</strong> ${data.appointmentTime}</p>
            <p><strong>Lugar:</strong> ${data.consultoryName}</p>
            <p><strong>Dirección:</strong> ${data.consultoryAddress}</p>
          </div>
          
          ${data.checkInLink ? `
            <div class="checkin-section">
              <h3>✅ ¡Facilita tu llegada!</h3>
              <p>Cuando llegues al consultorio, usa este botón para registrar tu llegada:</p>
              <a href="${data.checkInLink}" class="checkin-button">Registrar Mi Llegada</a>
              <p style="font-size: 14px; margin-top: 15px;">Este enlace te permitirá hacer check-in fácilmente cuando llegues al consultorio. El personal médico será notificado automáticamente.</p>
            </div>
          ` : ''}
          
          <div class="checklist">
            <h3>📋 Lista de verificación:</h3>
            <ul>
              <li>□ Llegar 15 minutos antes</li>
              <li>□ Traer documento de identidad</li>
              <li>□ Traer tarjeta de seguro (si aplica)</li>
              <li>□ Lista de medicamentos actuales</li>
              <li>□ Exámenes o estudios previos</li>
              ${data.checkInLink ? '<li>□ Usar el botón de check-in cuando llegues</li>' : ''}
            </ul>
          </div>
          
          <p>Si necesitas reprogramar tu cita, contacta al consultorio lo antes posible.</p>
          <p><strong>¡Te esperamos mañana!</strong></p>
        </div>
      </div>
    </body>
    </html>
  `,

  // Template para confirmación de cita médica
  appointmentConfirmation: (data: {
    patientName: string;
    doctorName: string;
    consultoryName: string;
    appointmentDate: string;
    appointmentTime: string;
    confirmationCode: string;
    appointmentType: string;
  }) => `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Confirmación de Cita Médica</title>
      <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
        .container { max-width: 600px; margin: 0 auto; padding: 20px; }
        .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
        .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
        .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669; }
        .confirmation-code { background: #fbbf24; color: #92400e; padding: 15px; border-radius: 6px; text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; }
        .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        .highlight { color: #059669; font-weight: bold; }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h1>✅ Cita Confirmada</h1>
        </div>
        <div class="content">
          <h2>¡Hola ${data.patientName}!</h2>
          <p>Tu cita médica ha sido <span class="highlight">confirmada exitosamente</span>. Aquí tienes todos los detalles:</p>
          
          <div class="appointment-card">
            <h3>📅 Detalles de tu cita</h3>
            <p><strong>Tipo de consulta:</strong> ${data.appointmentType}</p>
            <p><strong>Doctor:</strong> ${data.doctorName}</p>
            <p><strong>Fecha:</strong> ${data.appointmentDate}</p>
            <p><strong>Hora:</strong> ${data.appointmentTime}</p>
            <p><strong>Lugar:</strong> ${data.consultoryName}</p>
          </div>

          <div class="confirmation-code">
            <p>Tu código de confirmación:</p>
            <div style="font-size: 24px; letter-spacing: 2px;">${data.confirmationCode}</div>
            <p style="font-size: 12px; margin-top: 10px;">Presenta este código al llegar a tu cita</p>
          </div>

          <h3>📝 Importante:</h3>
          <ul>
            <li>Llega <strong>15 minutos antes</strong> de tu hora programada</li>
            <li>Trae tu <strong>documento de identidad</strong></li>
            <li>Si necesitas cancelar o reprogramar, hazlo con <strong>24 horas de anticipación</strong></li>
            <li>Recibirás un recordatorio 24 horas antes de tu cita</li>
          </ul>

          <p>Si tienes alguna pregunta o necesitas hacer cambios, no dudes en contactarnos.</p>
          
          <div class="footer">
            <p>¡Esperamos verte pronto!</p>
            <p><strong>${data.consultoryName}</strong></p>
          </div>
        </div>
      </div>
    </body>
    </html>
  `
};