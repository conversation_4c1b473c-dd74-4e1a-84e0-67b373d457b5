import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  appointments, 
  medicalRecords, 
  user,
  userRoles,
  medicalServices,
  doctorServicePrices
} from '@/db/schema';
import { eq, and, desc, count } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// POST - Crear nueva consulta médica
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que es doctor
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.role, 'doctor'),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    if (!userRole.length) {
      return NextResponse.json({ 
        error: 'Solo los doctores pueden iniciar consultas médicas' 
      }, { status: 403 });
    }

    const body = await request.json();
    const { appointmentId } = body;

    if (!appointmentId) {
      return NextResponse.json({ 
        error: 'ID de cita requerido' 
      }, { status: 400 });
    }

    // Obtener información de la cita
    const appointmentInfo = await db
      .select({
        id: appointments.id,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        consultoryId: appointments.consultoryId,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        status: appointments.status,
        chiefComplaint: appointments.chiefComplaint,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinData: appointments.preCheckinData,
        serviceId: appointments.serviceId,
        duration: appointments.duration,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (!appointmentInfo.length) {
      return NextResponse.json({ 
        error: 'Cita no encontrada' 
      }, { status: 404 });
    }

    const appointment = appointmentInfo[0];

    // Verificar que es el doctor de la cita
    if (appointment.doctorId !== userId) {
      return NextResponse.json({ 
        error: 'No autorizado para esta cita' 
      }, { status: 403 });
    }

    // Obtener información del doctor para los servicios
    const doctorInfo = await db
      .select({
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    const doctorName = doctorInfo.length > 0 
      ? `${doctorInfo[0].firstName || ''} ${doctorInfo[0].lastName || ''}`.trim() || 'Doctor'
      : 'Doctor';

    // Verificar que la cita esté en estado adecuado
    if (!['confirmed', 'checked_in', 'in_progress'].includes(appointment.status)) {
      return NextResponse.json({ 
        error: 'La cita debe estar confirmada o el paciente debe haber llegado para iniciar consulta' 
      }, { status: 400 });
    }

    // Buscar o crear expediente médico del paciente
    let medicalRecord = await db
      .select()
      .from(medicalRecords)
      .where(and(
        eq(medicalRecords.patientId, appointment.patientId!),
        eq(medicalRecords.status, 'active')
      ))
      .limit(1);

    if (!medicalRecord.length) {
      // Crear expediente médico si no existe
      const recordNumber = await generateRecordNumber();
      
      const newRecord = {
        id: nanoid(),
        patientId: appointment.patientId!,
        consultoryId: appointment.consultoryId!,
        primaryDoctorId: appointment.doctorId!,
        recordNumber,
        openDate: new Date(),
        status: 'active' as const,
        isMinor: false, // TODO: Determinar basado en edad
        totalConsultations: 0,
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const recordResult = await db.insert(medicalRecords).values(newRecord).returning();
      medicalRecord = recordResult;
    }

    // Extraer información del pre-checkin
    const preCheckinData = appointment.preCheckinData as any;
    let initialAssessment = '';
    
    if (preCheckinData) {
      const assessmentParts = [];
      
      if (preCheckinData.symptoms) {
        assessmentParts.push(`Síntomas reportados: ${preCheckinData.symptoms}`);
      }
      
      if (preCheckinData.medications) {
        assessmentParts.push(`Medicamentos actuales: ${preCheckinData.medications}`);
      }
      
      if (preCheckinData.allergies) {
        assessmentParts.push(`Alergias conocidas: ${preCheckinData.allergies}`);
      }
      
      if (preCheckinData.additionalNotes) {
        assessmentParts.push(`Notas adicionales: ${preCheckinData.additionalNotes}`);
      }
      
      initialAssessment = assessmentParts.join('\n\n');
    }

    // Crear consulta médica
    const consultationId = nanoid();
    
    // Crear servicio inicial de la cita
    const initialServices = [];
    console.log('🔍 DEBUG: Procesando serviceId de la cita:', appointment.serviceId);
    if (appointment.serviceId) {
      // Obtener información del servicio médico
      const serviceInfo = await db
        .select()
        .from(medicalServices)
        .where(eq(medicalServices.id, appointment.serviceId))
        .limit(1);
      
      let serviceName = 'Servicio médico';
      let servicePrice = 0;
      let serviceDuration = 30;
      
      if (serviceInfo.length > 0) {
        const service = serviceInfo[0];
        serviceName = service.name;
        serviceDuration = service.duration || 30;
        
        // Buscar precio personalizado del doctor
        const doctorPrice = await db
          .select()
          .from(doctorServicePrices)
          .where(and(
            eq(doctorServicePrices.doctorId, userId),
            eq(doctorServicePrices.serviceId, appointment.serviceId),
            eq(doctorServicePrices.isActive, true)
          ))
          .limit(1);
        
        // Usar precio del doctor si existe, sino usar precio base
        servicePrice = doctorPrice.length > 0 
          ? parseFloat(doctorPrice[0].customPrice) 
          : parseFloat(service.basePrice);
      }
      
      const serviceToAdd = {
        serviceId: appointment.serviceId,
        serviceName: serviceName,
        category: 'Consulta',
        price: servicePrice,
        duration: serviceDuration,
        performedBy: doctorName,
        addedAt: new Date().toISOString(),
        notes: 'Servicio de la cita original'
      };
      
      console.log('🔍 DEBUG: Servicio a agregar:', serviceToAdd);
      initialServices.push(serviceToAdd);
    }
    
    console.log('🔍 DEBUG: initialServices final:', initialServices);
    
    const consultationData = {
      id: consultationId,
      medicalRecordId: medicalRecord[0].id,
      appointmentId: appointment.id,
      doctorId: appointment.doctorId,
      consultoryId: appointment.consultoryId,
      consultationDate: new Date(),
      services: initialServices,
      chiefComplaint: appointment.chiefComplaint || 'Consulta programada',
      currentIllness: preCheckinData?.symptoms || '',
      
      // Campos pre-cargados del pre-checkin
      vitalSigns: {},
      physicalExam: {},
      diagnoses: [],
      treatment: {},
      prescriptions: [],
      
      recommendations: '',
      followUpInstructions: '',
      attachments: [],
      status: 'draft' as const,
      
      createdBy: userId,
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('🔍 DEBUG: consultationData.services antes de insertar:', consultationData.services);
    const consultation = await db.insert(medicalConsultations).values(consultationData).returning();
    console.log('🔍 DEBUG: Consulta creada con servicios:', consultation[0].services);

    // Actualizar estado de la cita
    await db
      .update(appointments)
      .set({
        status: 'in_progress',
        startedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    // Actualizar expediente médico
    await db
      .update(medicalRecords)
      .set({
        lastAccessDate: new Date(),
        totalConsultations: (medicalRecord[0].totalConsultations || 0) + 1,
        updatedAt: new Date(),
        updatedBy: userId
      })
      .where(eq(medicalRecords.id, medicalRecord[0].id));

    return NextResponse.json({
      success: true,
      message: 'Consulta médica iniciada exitosamente',
      data: {
        consultationId: consultation[0].id,
        medicalRecordId: medicalRecord[0].id,
        initialAssessment,
        preCheckinData: preCheckinData || null,
        appointment: {
          id: appointment.id,
          patientId: appointment.patientId,
          scheduledDate: appointment.scheduledDate,
          chiefComplaint: appointment.chiefComplaint
        }
      }
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating medical consultation:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// GET - Obtener consultas médicas del doctor
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que es doctor
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.role, 'doctor'),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    if (!userRole.length) {
      return NextResponse.json({ 
        error: 'Solo los doctores pueden ver consultas médicas' 
      }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const offset = (page - 1) * limit;
    const appointmentId = searchParams.get('appointmentId');

    // Construir condiciones de filtro
    const whereConditions = [eq(medicalConsultations.doctorId, userId)];
    
    if (appointmentId) {
      whereConditions.push(eq(medicalConsultations.appointmentId, appointmentId));
    }

    // Obtener consultas del doctor
    const consultations = await db
      .select({
        id: medicalConsultations.id,
        consultationDate: medicalConsultations.consultationDate,
        services: medicalConsultations.services,
        chiefComplaint: medicalConsultations.chiefComplaint,
        status: medicalConsultations.status,
        patientName: user.firstName,
        patientLastName: user.lastName,
        appointmentId: medicalConsultations.appointmentId,
        createdAt: medicalConsultations.createdAt,
      })
      .from(medicalConsultations)
      .leftJoin(medicalRecords, eq(medicalConsultations.medicalRecordId, medicalRecords.id))
      .leftJoin(user, eq(medicalRecords.patientId, user.id))
      .where(and(...whereConditions))
      .orderBy(desc(medicalConsultations.consultationDate))
      .limit(limit)
      .offset(offset);

    // Obtener conteo total
    const totalResult = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(...whereConditions));
    
    const total = totalResult[0]?.count || 0;

    return NextResponse.json({
      data: consultations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      }
    });

  } catch (error) {
    console.error('Error fetching medical consultations:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// Función auxiliar para generar número de expediente único
async function generateRecordNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `EXP-${year}-`;
  
  const lastRecord = await db
    .select({ recordNumber: medicalRecords.recordNumber })
    .from(medicalRecords)
    .where(eq(medicalRecords.recordNumber, `${prefix}%`))
    .orderBy(desc(medicalRecords.recordNumber))
    .limit(1);

  if (lastRecord.length === 0) {
    return `${prefix}001`;
  }

  const lastNumber = lastRecord[0].recordNumber.split('-')[2];
  const nextNumber = (parseInt(lastNumber) + 1).toString().padStart(3, '0');
  
  return `${prefix}${nextNumber}`;
}