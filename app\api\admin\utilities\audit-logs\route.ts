import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { adminAuditLogs, user, userRoles } from '@/db/schema';
import { eq, and, desc, gte, lte } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario es administrador
    const adminUser = await db
      .select({ id: user.id })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, userId),
          eq(userRoles.role, 'admin'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (adminUser.length === 0) {
      return NextResponse.json(
        { error: 'Solo administradores pueden acceder a los logs de auditoría' },
        { status: 403 }
      );
    }

    // Obtener parámetros de consulta
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const action = searchParams.get('action');
    const success = searchParams.get('success');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');

    // Construir filtros
    const conditions = [];
    
    if (action) {
      conditions.push(eq(adminAuditLogs.action, action));
    }
    
    if (success !== null && success !== '') {
      conditions.push(eq(adminAuditLogs.success, success === 'true'));
    }
    
    if (dateFrom) {
      conditions.push(gte(adminAuditLogs.timestamp, new Date(dateFrom)));
    }
    
    if (dateTo) {
      conditions.push(lte(adminAuditLogs.timestamp, new Date(dateTo)));
    }

    // Calcular offset
    const offset = (page - 1) * limit;

    // Obtener logs con paginación
    const logs = await db
      .select({
        id: adminAuditLogs.id,
        action: adminAuditLogs.action,
        executedBy: adminAuditLogs.executedBy,
        executedByName: adminAuditLogs.executedByName,
        timestamp: adminAuditLogs.timestamp,
        details: adminAuditLogs.details,
        success: adminAuditLogs.success,
        errorMessage: adminAuditLogs.errorMessage,
        ipAddress: adminAuditLogs.ipAddress,
        userAgent: adminAuditLogs.userAgent,
        createdAt: adminAuditLogs.createdAt
      })
      .from(adminAuditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined)
      .orderBy(desc(adminAuditLogs.timestamp))
      .limit(limit)
      .offset(offset);

    // Contar total de registros para paginación
    const totalCountResult = await db
      .select()
      .from(adminAuditLogs)
      .where(conditions.length > 0 ? and(...conditions) : undefined);

    const totalCount = totalCountResult.length;
    const totalPages = Math.ceil(totalCount / limit);

    // Obtener estadísticas generales
    const stats = await db
      .select({
        action: adminAuditLogs.action,
        success: adminAuditLogs.success
      })
      .from(adminAuditLogs);

    const actionStats = stats.reduce((acc: Record<string, { total: number; successful: number; failed: number }>, log) => {
      if (!acc[log.action]) {
        acc[log.action] = { total: 0, successful: 0, failed: 0 };
      }
      acc[log.action].total++;
      if (log.success) {
        acc[log.action].successful++;
      } else {
        acc[log.action].failed++;
      }
      return acc;
    }, {});

    return NextResponse.json({
      success: true,
      data: logs,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      },
      filters: {
        action,
        success,
        dateFrom,
        dateTo
      },
      stats: {
        totalLogs: totalCount,
        actionBreakdown: actionStats
      }
    });

  } catch (error) {
    console.error('Error obteniendo logs de auditoría:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}