'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn, formatDate } from '@/lib/utils';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday } from 'date-fns';
import { es } from 'date-fns/locale';
import { useRegionalConfig } from '@/hooks/use-regional-config';

interface DatePickerProps {
  value: Date;
  onChange: (date: Date) => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
}

export function DatePicker({ value, onChange, disabled = false, className, placeholder }: DatePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(value || new Date());
  const { config, getDatePlaceholder } = useRegionalConfig();
  
  // Use dynamic placeholder if not provided
  const displayPlaceholder = placeholder || getDatePlaceholder();

  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart, { weekStartsOn: config?.weekStartsOn || 1 });
  const endDate = endOfWeek(monthEnd, { weekStartsOn: config?.weekStartsOn || 1 });

  const dateFormat = "d";
  const rows = [];
  let days = [];
  let day = startDate;
  let formattedDate = "";

  // Días de la semana
  const weekDays = ['L', 'M', 'X', 'J', 'V', 'S', 'D'];

  // Generar el calendario
  while (day <= endDate) {
    for (let i = 0; i < 7; i++) {
      formattedDate = format(day, dateFormat);
      const cloneDay = day;
      
      days.push(
        <div
          key={day.toString()}
          className={cn(
            "w-10 h-10 flex items-center justify-center text-sm cursor-pointer rounded-md transition-colors",
            !isSameMonth(day, monthStart) && "text-gray-300",
            isSameDay(day, value) && "bg-blue-500 text-white font-semibold",
            isToday(day) && !isSameDay(day, value) && "bg-blue-100 text-blue-800 font-medium",
            isSameMonth(day, monthStart) && !isSameDay(day, value) && !isToday(day) && "hover:bg-gray-100",
            day < new Date(new Date().setHours(0, 0, 0, 0)) && "text-gray-300 cursor-not-allowed"
          )}
          onClick={() => {
            if (day >= new Date(new Date().setHours(0, 0, 0, 0))) {
              onChange(cloneDay);
              setIsOpen(false);
            }
          }}
        >
          {formattedDate}
        </div>
      );
      day = addDays(day, 1);
    }
    rows.push(
      <div key={day.toString()} className="grid grid-cols-7 gap-1">
        {days}
      </div>
    );
    days = [];
  }

  const handlePrevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  if (disabled) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        {formatDate(value)}
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <Button
        type="button"
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-11 justify-start text-left font-normal"
      >
        <Calendar className="mr-2 h-4 w-4" />
        {value ? (
          <span className="text-sm font-medium">
            {formatDate(value)}
          </span>
        ) : (
          <span className="text-gray-500">{displayPlaceholder}</span>
        )}
      </Button>
      
      {isOpen && (
        <>
          {/* Overlay para cerrar */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Selector de fecha */}
          <Card className="absolute z-50 top-12 left-0 w-80 shadow-lg border">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Header con navegación */}
                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevMonth}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <h3 className="font-semibold text-lg">
                    {format(currentMonth, 'MMMM yyyy', { locale: es })}
                  </h3>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleNextMonth}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Días de la semana */}
                <div className="grid grid-cols-7 gap-1 mb-2">
                  {weekDays.map((day) => (
                    <div
                      key={day}
                      className="w-10 h-8 flex items-center justify-center text-xs font-semibold text-gray-600"
                    >
                      {day}
                    </div>
                  ))}
                </div>
                
                {/* Calendario */}
                <div className="space-y-1">
                  {rows}
                </div>
                
                {/* Botón para hoy */}
                <div className="border-t pt-3">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const today = new Date();
                      onChange(today);
                      setCurrentMonth(today);
                      setIsOpen(false);
                    }}
                    className="w-full text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                  >
                    Seleccionar hoy
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}