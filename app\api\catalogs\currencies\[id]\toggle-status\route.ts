import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { currencies } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar moneda
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de monedas.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que la moneda existe
    const existingCurrency = await db
      .select()
      .from(currencies)
      .where(eq(currencies.id, id))
      .limit(1);

    if (existingCurrency.length === 0) {
      return NextResponse.json({ error: 'Moneda no encontrada' }, { status: 404 });
    }

    // No permitir desactivar la moneda predeterminada
    if (existingCurrency[0].isDefault && existingCurrency[0].isActive) {
      return NextResponse.json({ 
        error: 'No se puede desactivar la moneda predeterminada. Primero cambia la moneda predeterminada.' 
      }, { status: 400 });
    }

    // Cambiar el estado
    const newStatus = !existingCurrency[0].isActive;
    const [updatedCurrency] = await db
      .update(currencies)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(currencies.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedCurrency,
      message: `Moneda ${newStatus ? 'activada' : 'desactivada'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling currency status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}