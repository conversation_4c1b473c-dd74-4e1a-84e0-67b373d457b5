import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq, and, gte, lte, isNull, or, isNotNull } from 'drizzle-orm';
import { sendPreCheckinEmail } from '@/lib/guardian-utils';

// POST - Enviar pre-checkin emails programados (para ser ejecutado por cron job)
export async function POST(request: NextRequest) {
  try {
    // Verificar header de autorización para cron jobs
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'dev-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🕐 Iniciando envío de pre-checkin emails programados...');

    // Calcular el rango de fechas para citas que necesitan pre-checkin
    // Buscar citas que son en exactamente 48 horas (con margen de 1 hora)
    // PERO también incluir citas para el mismo día que aún no han recibido pre-checkin
    const now = new Date();
    const in48Hours = new Date(now.getTime() + (48 * 60 * 60 * 1000));
    const in47Hours = new Date(now.getTime() + (47 * 60 * 60 * 1000));
    const in49Hours = new Date(now.getTime() + (49 * 60 * 60 * 1000));
    
    // Para citas del mismo día - buscar citas que sean en las próximas 2-4 horas
    const in2Hours = new Date(now.getTime() + (2 * 60 * 60 * 1000));
    const in4Hours = new Date(now.getTime() + (4 * 60 * 60 * 1000));

    console.log('📅 Buscando citas entre:', {
      normalPrecheckin: `${in47Hours.toISOString()} - ${in49Hours.toISOString()}`,
      sameDayPrecheckin: `${in2Hours.toISOString()} - ${in4Hours.toISOString()}`
    });

    // Buscar citas que necesitan pre-checkin (48h antes O mismo día)
    const appointmentsNeedingPrecheckin = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        consultoryId: appointments.consultoryId,
        preCheckinSent: appointments.preCheckinSent,
        status: appointments.status,
      })
      .from(appointments)
      .where(
        and(
          // Que tengan paciente asignado
          isNotNull(appointments.patientId),
          // Que no se haya enviado pre-checkin aún
          or(
            isNull(appointments.preCheckinSent),
            eq(appointments.preCheckinSent, false)
          ),
          // Solo citas activas
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed')
          ),
          // CONDICIÓN MEJORADA: 48h antes O mismo día (2-4h antes)
          or(
            // Citas programadas entre 47-49 horas desde ahora (pre-checkin normal)
            and(
              gte(appointments.startTime, in47Hours),
              lte(appointments.startTime, in49Hours)
            ),
            // Citas del mismo día que son en 2-4 horas (pre-checkin inmediato)
            and(
              gte(appointments.startTime, in2Hours),
              lte(appointments.startTime, in4Hours)
            )
          )
        )
      );

    console.log(`📋 Encontradas ${appointmentsNeedingPrecheckin.length} citas que necesitan pre-checkin`);

    let emailsSent = 0;
    let emailsError = 0;

    // Procesar cada cita
    for (const appointment of appointmentsNeedingPrecheckin) {
      try {
        if (!appointment.patientId) {
          console.log(`⏭️ Saltando cita ${appointment.id} - sin paciente asignado`);
          continue;
        }

        // Obtener información completa de la cita
        const appointmentInfo = await db
          .select({
            id: appointments.id,
            title: appointments.title,
            scheduledDate: appointments.scheduledDate,
            startTime: appointments.startTime,
            endTime: appointments.endTime,
            doctorFirstName: user.firstName,
            doctorLastName: user.lastName,
            consultoryName: consultories.name,
          })
          .from(appointments)
          .leftJoin(user, eq(appointments.doctorId, user.id))
          .leftJoin(consultories, eq(appointments.consultoryId, consultories.id))
          .where(eq(appointments.id, appointment.id))
          .limit(1);

        if (appointmentInfo.length === 0) {
          console.error(`❌ No se pudo obtener información completa de la cita ${appointment.id}`);
          continue;
        }

        const apptInfo = appointmentInfo[0];

        // Determinar si es cita del mismo día
        const appointmentTime = new Date(appointment.startTime);
        const isSameDay = appointmentTime >= in2Hours && appointmentTime <= in4Hours;
        
        console.log(`📧 Enviando pre-checkin para cita ${appointment.id}... ${isSameDay ? '(MISMO DÍA)' : '(48H ANTES)'}`);
        
        const emailResult = await sendPreCheckinEmail(
          appointment.id,
          {
            id: apptInfo.id,
            title: apptInfo.title,
            scheduledDate: new Date(apptInfo.scheduledDate),
            startTime: new Date(apptInfo.startTime),
            endTime: new Date(apptInfo.endTime),
            doctorFirstName: apptInfo.doctorFirstName,
            doctorLastName: apptInfo.doctorLastName,
            consultoryName: apptInfo.consultoryName,
          },
          appointment.patientId,
          isSameDay // Pasar flag para personalizar el mensaje
        );

        if (emailResult.success) {
          emailsSent++;
          console.log(`✅ Pre-checkin enviado exitosamente para cita ${appointment.id}`);
        } else {
          emailsError++;
          console.error(`❌ Error enviando pre-checkin para cita ${appointment.id}:`, emailResult.error);
        }

      } catch (error) {
        emailsError++;
        console.error(`❌ Error procesando cita ${appointment.id}:`, error);
      }

      // Pequeña pausa entre emails para no sobrecargar el servicio
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const result = {
      message: 'Proceso de pre-checkin completado',
      totalAppointments: appointmentsNeedingPrecheckin.length,
      emailsSent,
      emailsError,
      timestamp: new Date().toISOString(),
    };

    console.log('📊 Resumen del proceso:', result);

    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('Error en proceso de pre-checkin:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, 
      { status: 500 }
    );
  }
}