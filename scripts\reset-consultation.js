const { drizzle } = require('drizzle-orm/neon-http');
const { neon } = require('@neondatabase/serverless');
const { eq } = require('drizzle-orm');
const schema = require('../db/schema');

// Cargar variables de entorno
require('dotenv').config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql, { schema });

async function resetConsultation() {
  const consultationId = 't08PIg2y_tdo54fUFnomg';
  const appointmentId = 'i7EgTkGSq1RUJWsHmRZN8';
  
  try {
    console.log('🔍 Buscando consulta médica...');
    
    // Buscar la consulta
    const consultation = await db
      .select()
      .from(schema.medicalConsultations)
      .where(eq(schema.medicalConsultations.id, consultationId))
      .limit(1);
    
    if (consultation.length > 0) {
      console.log('✅ Consulta encontrada:', consultation[0]);
      
      // Eliminar la consulta médica
      console.log('🗑️ Eliminando consulta médica...');
      await db
        .delete(schema.medicalConsultations)
        .where(eq(schema.medicalConsultations.id, consultationId));
      
      console.log('✅ Consulta médica eliminada');
    } else {
      console.log('⚠️ No se encontró la consulta médica');
    }
    
    // Actualizar el estado de la cita a 'checked_in'
    console.log('🔄 Actualizando estado de la cita...');
    const result = await db
      .update(schema.appointments)
      .set({
        status: 'checked_in',
        startedAt: null,
        updatedAt: new Date()
      })
      .where(eq(schema.appointments.id, appointmentId))
      .returning();
    
    if (result.length > 0) {
      console.log('✅ Cita actualizada a estado checked_in');
      console.log('📋 Datos de la cita:', {
        id: result[0].id,
        status: result[0].status,
        serviceId: result[0].serviceId,
        serviceName: result[0].serviceName,
        servicePrice: result[0].servicePrice
      });
    } else {
      console.log('⚠️ No se encontró la cita');
    }
    
    console.log('\n✅ Proceso completado. Ahora puedes iniciar la consulta nuevamente.');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

resetConsultation();