import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalConsultations } from '@/db/schema';
import { eq, and, gte, lte, count } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener fecha del mes actual
    const now = new Date();
    const currentMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
    const currentMonthEnd = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59);

    console.log('Debugging stats API...');
    console.log('User ID:', userId);
    console.log('Current month start:', currentMonthStart);
    console.log('Current month end:', currentMonthEnd);

    // Probar diferentes filtros
    const allCompletedForDoctor = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed')
      ));

    const withDateFilter = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.doctorId, userId),
        eq(medicalConsultations.status, 'completed'),
        gte(medicalConsultations.consultationDate, currentMonthStart),
        lte(medicalConsultations.consultationDate, currentMonthEnd)
      ));

    console.log('All completed for doctor:', allCompletedForDoctor[0]?.count);
    console.log('With date filter:', withDateFilter[0]?.count);

    return NextResponse.json({
      userId,
      currentMonthStart,
      currentMonthEnd,
      allCompletedForDoctor: allCompletedForDoctor[0]?.count || 0,
      withDateFilter: withDateFilter[0]?.count || 0
    });

  } catch (error) {
    console.error('Error in debug stats:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}