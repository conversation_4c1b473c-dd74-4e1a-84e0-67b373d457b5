import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { maritalStatus } from '@/db/schema';
import { eq, and, count, asc, desc, ilike } from 'drizzle-orm';

// GET - Listar estados civiles
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const onlyActive = searchParams.get('active') === 'true';
    const orderBy = searchParams.get('orderBy') || 'order';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(maritalStatus);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        ilike(maritalStatus.name, `%${search}%`),
        ilike(maritalStatus.description, `%${search}%`)
      );
    }

    if (onlyActive) {
      conditions.push(eq(maritalStatus.isActive, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'name' ? maritalStatus.name :
                       orderBy === 'createdAt' ? maritalStatus.createdAt :
                       maritalStatus.order;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const maritalStatusData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(maritalStatus);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    return NextResponse.json({
      data: maritalStatusData,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: maritalStatusData.filter(status => status.isActive).length
      }
    });

  } catch (error) {
    console.error('Error obteniendo estados civiles:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo estado civil (solo admin)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden crear estados civiles' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, order, description, isActive } = body;

    // Validaciones
    if (!name || order === undefined) {
      return NextResponse.json(
        { error: 'Nombre y orden son requeridos' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un estado con el mismo ID
    const existingStatus = await db.select().from(maritalStatus).where(eq(maritalStatus.id, id)).limit(1);
    if (existingStatus.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un estado civil con este nombre' },
        { status: 400 }
      );
    }

    const newMaritalStatus = {
      id,
      name: name.trim(),
      order: parseInt(order),
      description: description?.trim() || '',
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedMaritalStatus] = await db.insert(maritalStatus).values(newMaritalStatus).returning();

    return NextResponse.json({
      message: 'Estado civil creado exitosamente',
      data: insertedMaritalStatus
    });

  } catch (error) {
    console.error('Error creando estado civil:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar estado civil (solo admin)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden actualizar estados civiles' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, order, description, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de estado civil requerido' },
        { status: 400 }
      );
    }

    // Buscar estado existente
    const existingStatus = await db.select().from(maritalStatus).where(eq(maritalStatus.id, id)).limit(1);
    if (existingStatus.length === 0) {
      return NextResponse.json(
        { error: 'Estado civil no encontrado' },
        { status: 404 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(order !== undefined && { order: parseInt(order) }),
      ...(description !== undefined && { description: description.trim() }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedMaritalStatus] = await db.update(maritalStatus)
      .set(updateData)
      .where(eq(maritalStatus.id, id))
      .returning();

    return NextResponse.json({
      message: 'Estado civil actualizado exitosamente',
      data: updatedMaritalStatus
    });

  } catch (error) {
    console.error('Error actualizando estado civil:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}