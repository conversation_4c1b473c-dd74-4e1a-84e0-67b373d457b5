import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { appointments } from '@/db/schema';
import { and, or, eq, lt, isNull } from 'drizzle-orm';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener la hora actual menos 30 minutos de margen
    const thirtyMinutesAgo = new Date();
    thirtyMinutesAgo.setMinutes(thirtyMinutesAgo.getMinutes() - 30);

    // Buscar citas que:
    // 1. Estén en estado 'confirmed' o 'scheduled'
    // 2. Su hora de inicio ya haya pasado (con 30 min de margen)
    // 3. No tengan check-in registrado
    const pastAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          or(
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'scheduled')
          ),
          lt(appointments.startTime, thirtyMinutesAgo),
          isNull(appointments.checkedInAt)
        )
      );

    console.log(`🔍 Encontradas ${pastAppointments.length} citas pasadas sin check-in`);

    // Actualizar cada cita a no-show
    const updatedAppointments = [];
    for (const appointment of pastAppointments) {
      await db
        .update(appointments)
        .set({
          status: 'no_show',
          noShowReason: 'Paciente no se presentó (actualización automática)',
          updatedAt: new Date(),
          updatedBy: userId
        })
        .where(eq(appointments.id, appointment.id));

      updatedAppointments.push({
        id: appointment.id,
        title: appointment.title,
        startTime: appointment.startTime
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        processed: updatedAppointments.length,
        appointments: updatedAppointments
      },
      message: `${updatedAppointments.length} citas actualizadas a no-show`
    });

  } catch (error) {
    console.error('Error processing past appointments:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error al procesar citas pasadas' 
      },
      { status: 500 }
    );
  }
}

// GET para verificar qué citas serían procesadas sin hacer cambios
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener la hora actual menos 30 minutos de margen
    const thirtyMinutesAgo = new Date();
    thirtyMinutesAgo.setMinutes(thirtyMinutesAgo.getMinutes() - 30);

    // Buscar citas que serían procesadas
    const pastAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          or(
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'scheduled')
          ),
          lt(appointments.startTime, thirtyMinutesAgo),
          isNull(appointments.checkedInAt)
        )
      );

    return NextResponse.json({
      success: true,
      data: {
        count: pastAppointments.length,
        appointments: pastAppointments.map(apt => ({
          id: apt.id,
          title: apt.title,
          status: apt.status,
          startTime: apt.startTime,
          scheduledDate: apt.scheduledDate
        }))
      }
    });

  } catch (error) {
    console.error('Error checking past appointments:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error al verificar citas pasadas' 
      },
      { status: 500 }
    );
  }
}