import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalRecords, 
  user, 
  consultories, 
  medicalConsultations, 
  patientMedicalHistory,
  medicalDocuments,
  medications
} from '@/db/schema';
import { eq, and, desc, count } from 'drizzle-orm';

// GET - Obtener expediente médico por ID con información completa
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: recordId } = await params;

    // Obtener expediente base
    const recordResult = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (recordResult.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    const record = recordResult[0];

    // Obtener información del paciente
    const patientData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        address: user.address,
        emergencyContact: user.emergencyContact,
        emergencyPhone: user.emergencyPhone,
      })
      .from(user)
      .where(eq(user.id, record.patientId))
      .limit(1);

    // Obtener información del médico primario
    const doctorData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
      })
      .from(user)
      .where(eq(user.id, record.primaryDoctorId))
      .limit(1);

    // Obtener información del consultorio
    const consultoryData = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, record.consultoryId))
      .limit(1);

    // Obtener antecedentes médicos - buscar en tabla separada y en el expediente
    const medicalHistoryFromTable = await db
      .select()
      .from(patientMedicalHistory)
      .where(eq(patientMedicalHistory.medicalRecordId, recordId))
      .limit(1);

    // Combinar antecedentes de la tabla separada con los del expediente (evitando duplicaciones)
    let medicalHistoryData = null;
    
    // Función para combinar arrays sin duplicados con lógica mejorada
    const combineArrays = (arr1: any[] = [], arr2: any[] = []) => {
      const combined = [...arr1];
      
      arr2.forEach(item => {
        const exists = combined.some(existing => {
          // Comparación por ID único (más confiable)
          if (existing.id && item.id && existing.id === item.id) {
            return true;
          }
          
          // Comparación por historyId para antecedentes médicos
          if (existing.historyId && item.historyId && existing.historyId === item.historyId) {
            return true;
          }
          
          // Comparación por nombre y categoría para antecedentes
          if (existing.historyName && item.historyName && 
              existing.historyName === item.historyName &&
              existing.category === item.category) {
            return true;
          }
          
          // Comparación por nombre simple para elementos sin categoría
          if (existing.name && item.name && existing.name === item.name) {
            return true;
          }
          
          // Comparación específica para alergias
          if (existing.allergen && item.allergen && 
              existing.allergen === item.allergen &&
              existing.type === item.type) {
            return true;
          }
          
          // Comparación específica para vacunas
          if (existing.vaccine && item.vaccine && 
              existing.vaccine === item.vaccine &&
              existing.date === item.date) {
            return true;
          }
          
          return false;
        });
        
        if (!exists) {
          combined.push(item);
        }
      });
      
      return combined;
    };

    // Datos de la tabla separada
    const tableHistory = medicalHistoryFromTable.length > 0 ? medicalHistoryFromTable[0] : null;
    
    // Datos del expediente principal
    const recordHistory = {
      pathologicalHistory: record.pathologicalHistory || [],
      nonPathologicalHistory: record.nonPathologicalHistory || [],
      allergies: record.allergies || [],
      vaccinations: record.vaccinations || [],
      hospitalizations: record.hospitalizations || [],
      surgeries: record.surgeries || [],
      familyHistory: record.familyHistory || []
    };

    // Función para deduplicar arrays
    const deduplicateArray = (arr: any[] = []) => {
      const seen = new Set();
      return arr.filter(item => {
        // Crear clave única para cada elemento
        const key = JSON.stringify({
          historyName: item.historyName,
          name: item.name,
          category: item.category,
          severity: item.severity,
          status: item.status,
          allergen: item.allergen,
          type: item.type
        });
        
        if (seen.has(key)) {
          return false; // Ya existe, filtrar
        }
        seen.add(key);
        return true; // Es único, mantener
      });
    };

    // Priorizar tabla separada (patientMedicalHistory) y solo usar recordHistory como fallback si no existe tableHistory
    if (tableHistory) {
      // Si existe tabla separada, usar solo esos datos pero deduplicados
      medicalHistoryData = {
        pathologicalHistory: deduplicateArray(tableHistory.pathologicalHistory),
        nonPathologicalHistory: deduplicateArray(tableHistory.nonPathologicalHistory),
        allergies: deduplicateArray(tableHistory.allergies),
        vaccinations: deduplicateArray(tableHistory.vaccinations),
        hospitalizations: deduplicateArray(tableHistory.hospitalizations),
        surgeries: deduplicateArray(tableHistory.surgeries),
        familyHistory: deduplicateArray(tableHistory.familyHistory)
      };
    } else if (recordHistory.pathologicalHistory.length > 0 || recordHistory.nonPathologicalHistory.length > 0) {
      // Solo usar datos del expediente principal pero deduplicados
      medicalHistoryData = {
        pathologicalHistory: deduplicateArray(recordHistory.pathologicalHistory),
        nonPathologicalHistory: deduplicateArray(recordHistory.nonPathologicalHistory),
        allergies: deduplicateArray(recordHistory.allergies),
        vaccinations: deduplicateArray(recordHistory.vaccinations),
        hospitalizations: deduplicateArray(recordHistory.hospitalizations),
        surgeries: deduplicateArray(recordHistory.surgeries),
        familyHistory: deduplicateArray(recordHistory.familyHistory)
      };
    }

    // Obtener consultas recientes (últimas 10)
    const recentConsultations = await db
      .select()
      .from(medicalConsultations)
      .where(eq(medicalConsultations.medicalRecordId, recordId))
      .orderBy(desc(medicalConsultations.consultationDate))
      .limit(10);

    // Enriquecer consultas con información del médico y medicamentos
    const enrichedConsultations = await Promise.all(
      recentConsultations.map(async (consultation) => {
        const consultationDoctor = await db
          .select({
            firstName: user.firstName,
            lastName: user.lastName,
            email: user.email,
          })
          .from(user)
          .where(eq(user.id, consultation.doctorId))
          .limit(1);

        // Enriquecer prescripciones con nombres de medicamentos
        let enrichedPrescriptions = consultation.prescriptions as any[] || [];
        
        if (enrichedPrescriptions.length > 0) {
          enrichedPrescriptions = await Promise.all(
            enrichedPrescriptions.map(async (prescription) => {
              let medicationInfo = null;
              
              // Si medication es un ID, buscar en la tabla medications
              if (prescription.medication && typeof prescription.medication === 'string') {
                const medicationResult = await db
                  .select({
                    id: medications.id,
                    name: medications.name,
                    genericName: medications.genericName,
                    brandName: medications.brandName,
                    dosageForm: medications.dosageForm,
                    strength: medications.strength,
                  })
                  .from(medications)
                  .where(eq(medications.id, prescription.medication))
                  .limit(1);
                
                if (medicationResult.length > 0) {
                  medicationInfo = medicationResult[0];
                }
              }
              
              return {
                ...prescription,
                medicationInfo, // Información completa del medicamento
                medicationDisplayName: medicationInfo 
                  ? `${medicationInfo.name} (${medicationInfo.dosageForm} ${medicationInfo.strength})`
                  : prescription.medicationName || prescription.medication || 'Medicamento no especificado'
              };
            })
          );
        }

        return {
          ...consultation,
          doctor: consultationDoctor[0] || null,
          prescriptions: enrichedPrescriptions,
        };
      })
    );

    // Obtener documentos del expediente
    const documents = await db
      .select()
      .from(medicalDocuments)
      .where(eq(medicalDocuments.medicalRecordId, recordId))
      .orderBy(desc(medicalDocuments.uploadedAt))
      .limit(20);

    // Calcular estadísticas
    const totalConsultationsResult = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(eq(medicalConsultations.medicalRecordId, recordId));

    const activePrescriptionsCount = enrichedConsultations.reduce((acc, consultation) => {
      const prescriptions = consultation.prescriptions as any[] || [];
      return acc + prescriptions.filter(p => p.status === 'active').length;
    }, 0);

    const activeDiagnosesCount = enrichedConsultations.reduce((acc, consultation) => {
      const diagnoses = consultation.diagnoses as any[] || [];
      return acc + diagnoses.filter(d => d.status === 'active').length;
    }, 0);

    const lastConsultationDate = enrichedConsultations.length > 0 
      ? enrichedConsultations[0].consultationDate 
      : null;

    // Construir respuesta completa
    const response = {
      record,
      patient: patientData[0] || null,
      primaryDoctor: doctorData[0] || null,
      consultory: consultoryData[0] || null,
      medicalHistory: medicalHistoryData || null,
      recentConsultations: enrichedConsultations,
      documents,
      stats: {
        totalConsultations: totalConsultationsResult[0]?.count || 0,
        lastConsultationDate,
        activeDiagnoses: activeDiagnosesCount,
        activePrescriptions: activePrescriptionsCount,
        pendingFollowUps: enrichedConsultations.filter(c => 
          c.nextAppointment && new Date(c.nextAppointment) > new Date()
        ).length,
      },
    };

    // Actualizar fecha de último acceso
    await db
      .update(medicalRecords)
      .set({ 
        lastAccessDate: new Date(),
        updatedAt: new Date(),
        updatedBy: userId
      })
      .where(eq(medicalRecords.id, recordId));

    return NextResponse.json({
      data: response,
    });
  } catch (error) {
    console.error('Error fetching medical record:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// PUT - Actualizar expediente médico
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: recordId } = await params;
    const body = await request.json();

    // Verificar que el expediente existe
    const existingRecord = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (existingRecord.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Campos permitidos para actualización
    const allowedFields = [
      'status',
      'patientSummary',
      'demographics',
      'administrative',
      'emergencyContact',
      'isMinor',
      'guardianInfo',
      'primaryDoctorId',
      'consultoryId',
      'selectedSymptoms',
      'pathologicalHistory',
      'nonPathologicalHistory'
    ];

    const updateData: any = {
      updatedAt: new Date(),
      updatedBy: userId,
    };

    // Solo incluir campos permitidos que estén presentes en el body
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    // Validaciones adicionales
    if (body.primaryDoctorId) {
      const doctorExists = await db
        .select()
        .from(user)
        .where(eq(user.id, body.primaryDoctorId))
        .limit(1);

      if (doctorExists.length === 0) {
        return NextResponse.json({ error: 'Médico no encontrado' }, { status: 400 });
      }
    }

    if (body.consultoryId) {
      const consultoryExists = await db
        .select()
        .from(consultories)
        .where(and(eq(consultories.id, body.consultoryId), eq(consultories.isActive, true)))
        .limit(1);

      if (consultoryExists.length === 0) {
        return NextResponse.json({ error: 'Consultorio no encontrado o inactivo' }, { status: 400 });
      }
    }

    // Actualizar expediente
    const result = await db
      .update(medicalRecords)
      .set(updateData)
      .where(eq(medicalRecords.id, recordId))
      .returning();

    // Si hay alergias en el body, actualizar la tabla de historial médico del paciente
    if (body.allergies !== undefined) {
      // Buscar registro existente de historial médico del paciente
      const existingHistory = await db
        .select()
        .from(patientMedicalHistory)
        .where(eq(patientMedicalHistory.medicalRecordId, recordId))
        .limit(1);

      if (existingHistory.length > 0) {
        // Actualizar registro existente
        await db
          .update(patientMedicalHistory)
          .set({
            allergies: body.allergies,
            updatedAt: new Date(),
            updatedBy: userId,
          })
          .where(eq(patientMedicalHistory.medicalRecordId, recordId));
      } else {
        // Crear nuevo registro de historial médico
        await db
          .insert(patientMedicalHistory)
          .values({
            id: `hist_${recordId}_${Date.now()}`,
            medicalRecordId: recordId,
            allergies: body.allergies,
            pathologicalHistory: [],
            nonPathologicalHistory: [],
            vaccinations: [],
            hospitalizations: [],
            surgeries: [],
            familyHistory: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            createdBy: userId,
            updatedBy: userId,
          });
      }
    }

    return NextResponse.json({
      message: 'Expediente actualizado exitosamente',
      data: result[0],
    });
  } catch (error) {
    console.error('Error updating medical record:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// DELETE - Archivar expediente médico (eliminación lógica)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id: recordId } = await params;

    // Verificar que el expediente existe
    const existingRecord = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (existingRecord.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Verificar que no tiene consultas activas
    const activeConsultations = await db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.medicalRecordId, recordId),
        eq(medicalConsultations.status, 'in_progress')
      ));

    if (activeConsultations[0]?.count > 0) {
      return NextResponse.json({ 
        error: 'No se puede archivar un expediente con consultas activas' 
      }, { status: 400 });
    }

    // Archivar expediente (eliminación lógica)
    const result = await db
      .update(medicalRecords)
      .set({
        status: 'archived',
        updatedAt: new Date(),
        updatedBy: userId,
      })
      .where(eq(medicalRecords.id, recordId))
      .returning();

    return NextResponse.json({
      message: 'Expediente archivado exitosamente',
      data: result[0],
    });
  } catch (error) {
    console.error('Error archiving medical record:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}