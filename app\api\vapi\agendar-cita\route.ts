import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories, temporaryAppointments } from '@/db/schema';
import { eq, and, or, gte, lte } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { generateShortCode } from '@/lib/short-codes';

// Tabla temporal para citas VAPI (crear en migración)
// export const temporaryAppointments = pgTable("temporary_appointments", {
//   id: text("id").primaryKey(),
//   shortCode: text("shortCode").notNull().unique(),
//   doctorId: text("doctorId").notNull(),
//   patientName: text("patientName").notNull(),
//   patientPhone: text("patientPhone"),
//   chiefComplaint: text("chiefComplaint"),
//   scheduledDate: timestamp("scheduledDate").notNull(),
//   startTime: timestamp("startTime").notNull(),
//   endTime: timestamp("endTime").notNull(),
//   duration: integer("duration").notNull(),
//   createdAt: timestamp("createdAt").defaultNow(),
//   expiresAt: timestamp("expiresAt").notNull(), // 24 horas
// });

export async function POST(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      doctorId, 
      slotId, 
      nombrePaciente, 
      telefonoPaciente,
      motivoConsulta = 'Consulta médica programada por teléfono'
    } = body;

    // Validaciones básicas
    if (!doctorId || !slotId || !nombrePaciente) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Campos requeridos: doctorId, slotId, nombrePaciente' 
        },
        { status: 400 }
      );
    }

    // Extraer información del slotId (formato: doctorId-timestamp)
    const [slotDoctorId, timestampStr] = slotId.split('-');
    
    if (slotDoctorId !== doctorId) {
      return NextResponse.json(
        { success: false, error: 'Slot ID no corresponde al doctor seleccionado' },
        { status: 400 }
      );
    }

    const slotTimestamp = parseInt(timestampStr);
    if (isNaN(slotTimestamp)) {
      return NextResponse.json(
        { success: false, error: 'Slot ID inválido' },
        { status: 400 }
      );
    }

    const startDateTime = new Date(slotTimestamp);
    const endDateTime = new Date(startDateTime.getTime() + (30 * 60 * 1000)); // 30 minutos
    const scheduledDate = new Date(startDateTime);
    scheduledDate.setHours(0, 0, 0, 0);

    // Verificar que el doctor existe y está activo
    const doctor = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(
        and(
          eq(user.id, doctorId),
          eq(user.role, 'doctor'),
          eq(user.isActive, true)
        )
      )
      .limit(1);

    if (doctor.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Doctor no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const doctorInfo = doctor[0];

    // Verificar que el slot aún está disponible
    const conflictingAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          eq(appointments.scheduledDate, scheduledDate),
          or(
            and(
              gte(appointments.startTime, startDateTime),
              lte(appointments.startTime, endDateTime)
            ),
            and(
              gte(appointments.endTime, startDateTime),
              lte(appointments.endTime, endDateTime)
            ),
            and(
              lte(appointments.startTime, startDateTime),
              gte(appointments.endTime, endDateTime)
            )
          ),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'in_progress'),
            eq(appointments.status, 'pending_confirmation')
          )
        )
      );

    if (conflictingAppointments.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'El horario ya no está disponible. Por favor seleccione otro.',
          code: 'SLOT_NOT_AVAILABLE'
        },
        { status: 409 }
      );
    }

    // Generar código único
    const shortCode = generateShortCode();
    
    // Obtener consultorio por defecto del doctor (simplificado por ahora)
    const consultories = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);
    
    const consultoryId = consultories.length > 0 ? consultories[0].id : null;

    if (!consultoryId) {
      return NextResponse.json(
        { success: false, error: 'No hay consultorios disponibles' },
        { status: 500 }
      );
    }

    // Crear cita temporal (válida por 24 horas)
    const expiresAt = new Date(Date.now() + (24 * 60 * 60 * 1000)); // 24 horas
    
    const tempAppointment = {
      id: nanoid(),
      shortCode,
      doctorId,
      patientName: nombrePaciente,
      patientPhone: telefonoPaciente || null,
      chiefComplaint: motivoConsulta,
      scheduledDate,
      startTime: startDateTime,
      endTime: endDateTime,
      duration: 30,
      consultoryId,
      createdAt: new Date(),
      expiresAt,
    };

    // Por ahora, crear directamente en appointments hasta que implementemos temporaryAppointments
    const newAppointment = {
      id: nanoid(),
      title: `Consulta con ${doctorInfo.firstName} ${doctorInfo.lastName} - ${nombrePaciente}`,
      description: `Cita programada por teléfono para ${nombrePaciente}`,
      doctorId,
      patientId: null, // Se asignará cuando confirme en /confirmar
      consultoryId,
      serviceId: null,
      activityTypeId: null,
      chiefComplaint: motivoConsulta,
      scheduledDate,
      startTime: startDateTime,
      endTime: endDateTime,
      duration: 30,
      status: 'pending_confirmation' as const, // Estado especial para citas VAPI
      confirmationStatus: 'pending' as const,
      shortCode,
      estimatedPrice: null,
      currency: 'GTQ',
      paymentStatus: 'pending' as const,
      isFollowUp: false,
      isEmergency: false,
      requiresReminder: true,
      // Guardar datos temporales del paciente
      tempPatientData: JSON.stringify({
        name: nombrePaciente,
        phone: telefonoPaciente,
        source: 'vapi'
      }),
      createdBy: 'vapi-system',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(appointments).values(newAppointment).returning();
    const createdAppointment = result[0];

    // Generar mensaje fonético para el código
    const phoneticCode = generatePhoneticCode(shortCode);

    // Formatear fecha y hora para el mensaje
    const appointmentDate = startDateTime.toLocaleDateString('es-GT', {
      weekday: 'long',
      day: 'numeric',
      month: 'long'
    });
    
    const appointmentTime = startDateTime.toLocaleTimeString('es-GT', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    });

    return NextResponse.json({
      success: true,
      data: {
        appointmentId: createdAppointment.id,
        shortCode,
        phoneticCode,
        doctorName: `${doctorInfo.firstName} ${doctorInfo.lastName}`,
        patientName: nombrePaciente,
        appointmentDate,
        appointmentTime,
        confirmUrl: 'www.doctorabarbara.com/confirmar',
        expiresIn: '24 horas',
        message: `Cita agendada exitosamente. Su código de confirmación es ${phoneticCode}. 
                 Tiene 24 horas para confirmar en www.doctorabarbara.com/confirmar`
      }
    });

  } catch (error) {
    console.error('Error en agendar-cita:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// Función para generar código fonético
function generatePhoneticCode(code: string): string {
  const phonetic: Record<string, string> = {
    'A': 'A de avión',
    'B': 'B de barco',
    'C': 'C de casa',
    'D': 'D de dado',
    'E': 'E de elefante',
    'F': 'F de fuego',
    'G': 'G de gato',
    'H': 'H de hotel',
    'J': 'J de jardín',
    'K': 'K de kilo',
    'L': 'L de luna',
    'M': 'M de mesa',
    'N': 'N de nube',
    'P': 'P de papel',
    'Q': 'Q de queso',
    'R': 'R de radio',
    'T': 'T de taxi',
    'U': 'U de uva',
    'V': 'V de vaso',
    'W': 'W de whisky',
    'X': 'X de xilófono',
    'Y': 'Y de yate'
  };

  const parts = [];
  
  for (const char of code) {
    if (phonetic[char]) {
      parts.push(phonetic[char]);
    } else if (/\d/.test(char)) {
      parts.push(char);
    }
  }

  return parts.join(', ');
}