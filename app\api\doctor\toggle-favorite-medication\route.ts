import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { doctorFavoriteMedications, medications } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { medicationId } = body;

    // Validaciones
    if (!medicationId) {
      return NextResponse.json(
        { error: 'El ID del medicamento es requerido' },
        { status: 400 }
      );
    }

    // Verificar que el medicamento existe y está activo
    const medication = await db
      .select()
      .from(medications)
      .where(and(
        eq(medications.id, medicationId),
        eq(medications.isActive, true)
      ))
      .limit(1);

    if (medication.length === 0) {
      return NextResponse.json(
        { error: 'Medicamento no encontrado o inactivo' },
        { status: 404 }
      );
    }

    // Verificar si ya existe el favorito
    const existingFavorite = await db
      .select()
      .from(doctorFavoriteMedications)
      .where(
        and(
          eq(doctorFavoriteMedications.doctorId, userId),
          eq(doctorFavoriteMedications.medicationId, medicationId)
        )
      )
      .limit(1);

    let isFavorite: boolean;

    if (existingFavorite.length > 0) {
      // Si existe, eliminarlo (quitar de favoritos)
      await db
        .delete(doctorFavoriteMedications)
        .where(eq(doctorFavoriteMedications.id, existingFavorite[0].id));
      
      isFavorite = false;
    } else {
      // Si no existe, agregarlo (agregar a favoritos)
      await db
        .insert(doctorFavoriteMedications)
        .values({
          id: crypto.randomUUID(),
          doctorId: userId,
          medicationId,
          createdAt: new Date(),
          updatedAt: new Date()
        });
      
      isFavorite = true;
    }

    return NextResponse.json({
      success: true,
      isFavorite,
      message: isFavorite ? 'Medicamento agregado a favoritos' : 'Medicamento removido de favoritos'
    });
  } catch (error) {
    console.error('Error al alternar favorito de medicamento:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}