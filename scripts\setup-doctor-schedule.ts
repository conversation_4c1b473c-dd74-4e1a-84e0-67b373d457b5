import { db } from '@/db/drizzle';
import { doctorSchedules } from '@/db/schema';

async function setupDoctorSchedule() {
  try {
    const doctorId = 'user_30XBoyr3lbcqYXyXGJSUIwrV40H';
    
    console.log('🔧 Configurando horarios para Dr<PERSON>...\n');

    // Horario de Lunes a Viernes: 8:00 AM - 5:00 PM con almuerzo 12:00-1:00 PM
    const schedules = [
      // <PERSON><PERSON>
      {
        doctorId,
        dayOfWeek: 1,
        startTime: '08:00',
        endTime: '17:00',
        lunchBreakStart: '12:00',
        lunchBreakEnd: '13:00',
        isActive: true,
        allowOnlineBooking: true,
        maxAppointmentsPerHour: 2,
      },
      // Martes
      {
        doctorId,
        dayOfWeek: 2,
        startTime: '08:00',
        endTime: '17:00',
        lunchBreakStart: '12:00',
        lunchBreakEnd: '13:00',
        isActive: true,
        allowOnlineBooking: true,
        maxAppointmentsPerHour: 2,
      },
      // Miércoles
      {
        doctorId,
        dayOfWeek: 3,
        startTime: '08:00',
        endTime: '17:00',
        lunchBreakStart: '12:00',
        lunchBreakEnd: '13:00',
        isActive: true,
        allowOnlineBooking: true,
        maxAppointmentsPerHour: 2,
      },
      // Jueves
      {
        doctorId,
        dayOfWeek: 4,
        startTime: '08:00',
        endTime: '17:00',
        lunchBreakStart: '12:00',
        lunchBreakEnd: '13:00',
        isActive: true,
        allowOnlineBooking: true,
        maxAppointmentsPerHour: 2,
      },
      // Viernes
      {
        doctorId,
        dayOfWeek: 5,
        startTime: '08:00',
        endTime: '17:00',
        lunchBreakStart: '12:00',
        lunchBreakEnd: '13:00',
        isActive: true,
        allowOnlineBooking: true,
        maxAppointmentsPerHour: 2,
      },
    ];

    for (const schedule of schedules) {
      await db.insert(doctorSchedules).values(schedule);
      const dayNames = ['Dom', 'Lun', 'Mar', 'Mie', 'Jue', 'Vie', 'Sab'];
      console.log(`✅ ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime}`);
    }

    console.log('\n🎉 Horarios configurados exitosamente!');
    console.log('📱 Reserva online habilitada');
    console.log('🍽️ Almuerzo: 12:00-13:00');
    console.log('⏰ Máximo 2 citas por hora');

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

setupDoctorSchedule();