CREATE TABLE "activity_types" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"color" text NOT NULL,
	"duration" integer NOT NULL,
	"requiresPatient" boolean DEFAULT true,
	"allowsRecurrence" boolean DEFAULT false,
	"icon" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "assistant_doctor_relations" (
	"id" text PRIMARY KEY NOT NULL,
	"assistantId" text NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text,
	"permissions" jsonb,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "association_codes" (
	"id" text PRIMARY KEY NOT NULL,
	"code" text NOT NULL,
	"patientId" text NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"usedBy" text,
	"usedAt" timestamp,
	"createdAt" timestamp DEFAULT now(),
	CONSTRAINT "association_codes_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "currencies" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"symbol" text NOT NULL,
	"exchangeRate" real DEFAULT 1 NOT NULL,
	"isDefault" boolean DEFAULT false,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "currencies_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "document_types" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"shortName" text NOT NULL,
	"countryId" integer NOT NULL,
	"countryName" text NOT NULL,
	"format" text NOT NULL,
	"maxLength" integer NOT NULL,
	"minLength" integer NOT NULL,
	"isRequired" boolean DEFAULT false,
	"description" text,
	"example" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "education_levels" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"order" integer NOT NULL,
	"description" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "guardian_patient_relations" (
	"id" text PRIMARY KEY NOT NULL,
	"guardianId" text NOT NULL,
	"patientId" text NOT NULL,
	"relationship" text,
	"isPrimary" boolean DEFAULT false,
	"canMakeDecisions" boolean DEFAULT true,
	"validUntil" timestamp,
	"createdAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "marital_status" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"allowsSpouse" boolean DEFAULT false,
	"legalImplications" text,
	"description" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "media_sources" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"type" text NOT NULL,
	"category" text NOT NULL,
	"trackingEnabled" boolean DEFAULT false,
	"cost" real DEFAULT 0,
	"description" text,
	"icon" text,
	"color" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "non_pathological_history" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"subcategory" text,
	"isPositive" boolean DEFAULT true,
	"importance" text NOT NULL,
	"ageRelevant" text NOT NULL,
	"description" text,
	"benefits" jsonb,
	"risks" jsonb,
	"duration" text,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "pathological_history" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"category" text NOT NULL,
	"icd11Code" text,
	"severity" text NOT NULL,
	"isHereditary" boolean DEFAULT false,
	"requiresSpecialistFollow" boolean DEFAULT false,
	"commonInChildren" boolean DEFAULT false,
	"riskLevel" text NOT NULL,
	"description" text,
	"symptoms" jsonb,
	"treatments" jsonb,
	"order" integer,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "account" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "appointments" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "associationCodes" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "doctorAssistantRelations" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "medical_records" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "patientGuardianRelations" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "session" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "subscription" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "system_config" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_consultories" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_documents" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "user_specialties" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "verification" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "account" CASCADE;--> statement-breakpoint
DROP TABLE "appointments" CASCADE;--> statement-breakpoint
DROP TABLE "associationCodes" CASCADE;--> statement-breakpoint
DROP TABLE "doctorAssistantRelations" CASCADE;--> statement-breakpoint
DROP TABLE "medical_records" CASCADE;--> statement-breakpoint
DROP TABLE "patientGuardianRelations" CASCADE;--> statement-breakpoint
DROP TABLE "session" CASCADE;--> statement-breakpoint
DROP TABLE "subscription" CASCADE;--> statement-breakpoint
DROP TABLE "system_config" CASCADE;--> statement-breakpoint
DROP TABLE "user_consultories" CASCADE;--> statement-breakpoint
DROP TABLE "user_documents" CASCADE;--> statement-breakpoint
DROP TABLE "user_specialties" CASCADE;--> statement-breakpoint
DROP TABLE "verification" CASCADE;--> statement-breakpoint
ALTER TABLE "medical_specialties" DROP CONSTRAINT "medical_specialties_code_unique";--> statement-breakpoint
ALTER TABLE "occupations" DROP CONSTRAINT "occupations_code_unique";--> statement-breakpoint
ALTER TABLE "relationships" DROP CONSTRAINT "relationships_code_unique";--> statement-breakpoint
ALTER TABLE "departments" DROP CONSTRAINT "departments_country_id_countries_id_fk";
--> statement-breakpoint
ALTER TABLE "municipalities" DROP CONSTRAINT "municipalities_department_id_departments_id_fk";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_country_id_countries_id_fk";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_department_id_departments_id_fk";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_municipality_id_municipalities_id_fk";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_occupation_id_occupations_id_fk";
--> statement-breakpoint
ALTER TABLE "user" DROP CONSTRAINT "user_emergency_relationship_id_relationships_id_fk";
--> statement-breakpoint
ALTER TABLE "user_roles" DROP CONSTRAINT "user_roles_user_id_user_id_fk";
--> statement-breakpoint
DROP INDEX "reg_user_idx";--> statement-breakpoint
DROP INDEX "reg_status_idx";--> statement-breakpoint
DROP INDEX "reg_submitted_idx";--> statement-breakpoint
DROP INDEX "role_idx";--> statement-breakpoint
DROP INDEX "status_idx";--> statement-breakpoint
DROP INDEX "user_role_idx";--> statement-breakpoint
ALTER TABLE "consultories" ALTER COLUMN "createdAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "consultories" ALTER COLUMN "updatedAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "countries" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "countries" ALTER COLUMN "code" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "departments" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "medical_specialties" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "municipalities" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "notifications" ALTER COLUMN "createdAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "occupations" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "registrationRequests" ALTER COLUMN "submittedAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "registrationRequests" ALTER COLUMN "updatedAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "relationships" ALTER COLUMN "id" SET DATA TYPE integer;--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "name" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "emailVerified" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "createdAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ALTER COLUMN "updatedAt" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "user_roles" ALTER COLUMN "status" SET DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "code" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "type" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "specialty" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "capacity" integer DEFAULT 1;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "floor" integer;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "building" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "businessHours" jsonb;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "equipment" jsonb;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "isEmergencyCapable" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "hasAirConditioning" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "hasWaitingRoom" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "accessibility" jsonb;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "countries" ADD COLUMN "phoneCode" text;--> statement-breakpoint
ALTER TABLE "countries" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "departments" ADD COLUMN "countryId" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "departments" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "medical_specialties" ADD COLUMN "description" text;--> statement-breakpoint
ALTER TABLE "medical_specialties" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "municipalities" ADD COLUMN "departmentId" integer NOT NULL;--> statement-breakpoint
ALTER TABLE "municipalities" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "occupations" ADD COLUMN "category" text;--> statement-breakpoint
ALTER TABLE "occupations" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "role" text NOT NULL;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "generalData" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "specificData" jsonb;--> statement-breakpoint
ALTER TABLE "relationships" ADD COLUMN "category" text;--> statement-breakpoint
ALTER TABLE "relationships" ADD COLUMN "isActive" boolean DEFAULT true;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "firstName" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "lastName" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "documentType" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "documentNumber" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "dateOfBirth" timestamp;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "alternativePhone" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "countryId" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "departmentId" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "municipalityId" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "occupationId" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergencyContact" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergencyPhone" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergencyRelationshipId" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "overallStatus" text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "userId" text NOT NULL;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "consultoryId" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "specialtyId" integer;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "preferredDoctorId" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "medicalLicense" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "roleData" jsonb;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "approvedBy" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "approvedAt" timestamp;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "rejectedBy" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "rejectedAt" timestamp;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "rejectionReason" text;--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "createdAt" timestamp DEFAULT now();--> statement-breakpoint
ALTER TABLE "user_roles" ADD COLUMN "updatedAt" timestamp DEFAULT now();--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_assistantId_user_id_fk" FOREIGN KEY ("assistantId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "assistant_doctor_relations" ADD CONSTRAINT "assistant_doctor_relations_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "association_codes" ADD CONSTRAINT "association_codes_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "association_codes" ADD CONSTRAINT "association_codes_usedBy_user_id_fk" FOREIGN KEY ("usedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "document_types" ADD CONSTRAINT "document_types_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "guardian_patient_relations" ADD CONSTRAINT "guardian_patient_relations_guardianId_user_id_fk" FOREIGN KEY ("guardianId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "guardian_patient_relations" ADD CONSTRAINT "guardian_patient_relations_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "activity_types_category_idx" ON "activity_types" USING btree ("category");--> statement-breakpoint
CREATE INDEX "activity_types_active_idx" ON "activity_types" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "activity_types_order_idx" ON "activity_types" USING btree ("order");--> statement-breakpoint
CREATE UNIQUE INDEX "assistant_doctor_idx" ON "assistant_doctor_relations" USING btree ("assistantId","doctorId");--> statement-breakpoint
CREATE INDEX "assistant_relations_idx" ON "assistant_doctor_relations" USING btree ("assistantId");--> statement-breakpoint
CREATE INDEX "doctor_relations_idx" ON "assistant_doctor_relations" USING btree ("doctorId");--> statement-breakpoint
CREATE UNIQUE INDEX "association_code_idx" ON "association_codes" USING btree ("code");--> statement-breakpoint
CREATE INDEX "association_patient_idx" ON "association_codes" USING btree ("patientId");--> statement-breakpoint
CREATE INDEX "association_expires_idx" ON "association_codes" USING btree ("expiresAt");--> statement-breakpoint
CREATE UNIQUE INDEX "currencies_code_idx" ON "currencies" USING btree ("code");--> statement-breakpoint
CREATE INDEX "currencies_active_idx" ON "currencies" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "currencies_default_idx" ON "currencies" USING btree ("isDefault");--> statement-breakpoint
CREATE INDEX "document_types_country_idx" ON "document_types" USING btree ("countryId");--> statement-breakpoint
CREATE INDEX "document_types_active_idx" ON "document_types" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "document_types_required_idx" ON "document_types" USING btree ("isRequired");--> statement-breakpoint
CREATE UNIQUE INDEX "education_levels_order_idx" ON "education_levels" USING btree ("order");--> statement-breakpoint
CREATE INDEX "education_levels_active_idx" ON "education_levels" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "guardian_patient_idx" ON "guardian_patient_relations" USING btree ("guardianId","patientId");--> statement-breakpoint
CREATE INDEX "guardian_relations_idx" ON "guardian_patient_relations" USING btree ("guardianId");--> statement-breakpoint
CREATE INDEX "patient_relations_idx" ON "guardian_patient_relations" USING btree ("patientId");--> statement-breakpoint
CREATE UNIQUE INDEX "marital_status_order_idx" ON "marital_status" USING btree ("order");--> statement-breakpoint
CREATE INDEX "marital_status_active_idx" ON "marital_status" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "marital_status_spouse_idx" ON "marital_status" USING btree ("allowsSpouse");--> statement-breakpoint
CREATE INDEX "media_sources_type_idx" ON "media_sources" USING btree ("type");--> statement-breakpoint
CREATE INDEX "media_sources_category_idx" ON "media_sources" USING btree ("category");--> statement-breakpoint
CREATE INDEX "media_sources_active_idx" ON "media_sources" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "media_sources_tracking_idx" ON "media_sources" USING btree ("trackingEnabled");--> statement-breakpoint
CREATE INDEX "non_pathological_history_category_idx" ON "non_pathological_history" USING btree ("category");--> statement-breakpoint
CREATE INDEX "non_pathological_history_subcategory_idx" ON "non_pathological_history" USING btree ("subcategory");--> statement-breakpoint
CREATE INDEX "non_pathological_history_positive_idx" ON "non_pathological_history" USING btree ("isPositive");--> statement-breakpoint
CREATE INDEX "non_pathological_history_importance_idx" ON "non_pathological_history" USING btree ("importance");--> statement-breakpoint
CREATE INDEX "non_pathological_history_age_idx" ON "non_pathological_history" USING btree ("ageRelevant");--> statement-breakpoint
CREATE INDEX "non_pathological_history_active_idx" ON "non_pathological_history" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "pathological_history_category_idx" ON "pathological_history" USING btree ("category");--> statement-breakpoint
CREATE INDEX "pathological_history_severity_idx" ON "pathological_history" USING btree ("severity");--> statement-breakpoint
CREATE INDEX "pathological_history_risk_idx" ON "pathological_history" USING btree ("riskLevel");--> statement-breakpoint
CREATE INDEX "pathological_history_hereditary_idx" ON "pathological_history" USING btree ("isHereditary");--> statement-breakpoint
CREATE INDEX "pathological_history_children_idx" ON "pathological_history" USING btree ("commonInChildren");--> statement-breakpoint
CREATE INDEX "pathological_history_active_idx" ON "pathological_history" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "pathological_history_icd11_idx" ON "pathological_history" USING btree ("icd11Code");--> statement-breakpoint
ALTER TABLE "departments" ADD CONSTRAINT "departments_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "municipalities" ADD CONSTRAINT "municipalities_departmentId_departments_id_fk" FOREIGN KEY ("departmentId") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_countryId_countries_id_fk" FOREIGN KEY ("countryId") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_departmentId_departments_id_fk" FOREIGN KEY ("departmentId") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_municipalityId_municipalities_id_fk" FOREIGN KEY ("municipalityId") REFERENCES "public"."municipalities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_occupationId_occupations_id_fk" FOREIGN KEY ("occupationId") REFERENCES "public"."occupations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_emergencyRelationshipId_relationships_id_fk" FOREIGN KEY ("emergencyRelationshipId") REFERENCES "public"."relationships"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_specialtyId_medical_specialties_id_fk" FOREIGN KEY ("specialtyId") REFERENCES "public"."medical_specialties"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_preferredDoctorId_user_id_fk" FOREIGN KEY ("preferredDoctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_approvedBy_user_id_fk" FOREIGN KEY ("approvedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_rejectedBy_user_id_fk" FOREIGN KEY ("rejectedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "consultories_active_idx" ON "consultories" USING btree ("active");--> statement-breakpoint
CREATE INDEX "consultories_is_active_idx" ON "consultories" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "consultories_code_idx" ON "consultories" USING btree ("code");--> statement-breakpoint
CREATE INDEX "consultories_type_idx" ON "consultories" USING btree ("type");--> statement-breakpoint
CREATE INDEX "consultories_building_idx" ON "consultories" USING btree ("building");--> statement-breakpoint
CREATE INDEX "consultories_emergency_idx" ON "consultories" USING btree ("isEmergencyCapable");--> statement-breakpoint
CREATE INDEX "notifications_user_idx" ON "notifications" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "notifications_read_idx" ON "notifications" USING btree ("read");--> statement-breakpoint
CREATE INDEX "notifications_type_idx" ON "notifications" USING btree ("type");--> statement-breakpoint
CREATE INDEX "registration_requests_user_idx" ON "registrationRequests" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "registration_requests_status_idx" ON "registrationRequests" USING btree ("status");--> statement-breakpoint
CREATE INDEX "registration_requests_submitted_idx" ON "registrationRequests" USING btree ("submittedAt");--> statement-breakpoint
CREATE UNIQUE INDEX "email_idx" ON "user" USING btree ("email");--> statement-breakpoint
CREATE INDEX "overall_status_idx" ON "user" USING btree ("overallStatus");--> statement-breakpoint
CREATE UNIQUE INDEX "document_idx" ON "user" USING btree ("documentType","documentNumber");--> statement-breakpoint
CREATE INDEX "user_roles_user_idx" ON "user_roles" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "user_roles_role_idx" ON "user_roles" USING btree ("role");--> statement-breakpoint
CREATE INDEX "user_roles_status_idx" ON "user_roles" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "user_role_idx" ON "user_roles" USING btree ("userId","role");--> statement-breakpoint
ALTER TABLE "consultories" DROP COLUMN "description";--> statement-breakpoint
ALTER TABLE "consultories" DROP COLUMN "workingHours";--> statement-breakpoint
ALTER TABLE "countries" DROP COLUMN "phone_code";--> statement-breakpoint
ALTER TABLE "countries" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "countries" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "countries" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "departments" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "departments" DROP COLUMN "country_id";--> statement-breakpoint
ALTER TABLE "departments" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "departments" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "departments" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "medical_specialties" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "medical_specialties" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "medical_specialties" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "medical_specialties" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "municipalities" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "municipalities" DROP COLUMN "department_id";--> statement-breakpoint
ALTER TABLE "municipalities" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "municipalities" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "municipalities" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "occupations" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "occupations" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "occupations" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "occupations" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "requestedRoles";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "userData";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "rolesData";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "documentsData";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "approvedRoles";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "rejectedRoles";--> statement-breakpoint
ALTER TABLE "relationships" DROP COLUMN "code";--> statement-breakpoint
ALTER TABLE "relationships" DROP COLUMN "active";--> statement-breakpoint
ALTER TABLE "relationships" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "relationships" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "status";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "statusReason";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "activatedAt";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "activatedBy";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "first_name";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "last_name";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "document_type";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "document_number";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "date_of_birth";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "alternative_phone";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "country_id";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "department_id";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "municipality_id";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "occupation_id";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "emergency_contact";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "emergency_phone";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "emergency_relationship_id";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "preferences";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "metadata";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "user_id";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "role_data";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "created_at";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "updated_at";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "activated_at";--> statement-breakpoint
ALTER TABLE "user_roles" DROP COLUMN "deactivated_at";--> statement-breakpoint
ALTER TABLE "consultories" ADD CONSTRAINT "consultories_code_unique" UNIQUE("code");