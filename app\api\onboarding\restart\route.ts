import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, registrationRequests, user } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  return POST(request);
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🔄 Reiniciando onboarding para usuario:', userId);

    // 1. Verificar qué hay que eliminar antes de empezar
    const rolesToDelete = await db.execute(sql`
      SELECT COUNT(*) as count FROM user_roles 
      WHERE "userId" = ${userId} AND status = 'rejected'
    `);
    
    const requestsToDelete = await db.execute(sql`
      SELECT COUNT(*) as count FROM "registrationRequests" 
      WHERE "userId" = ${userId} AND status = 'rejected'
    `);

    console.log('📊 Elementos a eliminar:', {
      rolesRechazados: rolesToDelete.rows[0].count,
      solicitudesRechazadas: requestsToDelete.rows[0].count
    });

    // 2. Eliminar roles rechazados de la base de datos
    const deletedRoles = await db.delete(userRoles).where(
      and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'rejected')
      )
    );
    console.log('✅ Roles rechazados eliminados');

    // 3. Eliminar solicitudes rechazadas
    const deletedRequests = await db.delete(registrationRequests).where(
      and(
        eq(registrationRequests.userId, userId),
        eq(registrationRequests.status, 'rejected')
      )
    );
    console.log('✅ Solicitudes rechazadas eliminadas');

    // 4. Limpiar el status del usuario en la DB (como usuario nuevo)
    await db.update(user)
      .set({ 
        overallStatus: null,
        updatedAt: new Date()
      })
      .where(eq(user.id, userId));
    console.log('✅ Status del usuario limpiado (como usuario nuevo)');

    // 5. Limpiar los metadatos de Clerk para permitir nuevo onboarding
    const clerk = await clerkClient();
    await clerk.users.updateUserMetadata(userId, {
      publicMetadata: {
        onboardingCompleted: false,
        role: null,
        status: null
      }
    });
    console.log('✅ Metadatos de Clerk limpiados (como usuario nuevo)');

    console.log('✅ Onboarding reiniciado exitosamente');

    // Retornar información para que el frontend cierre la sesión
    return NextResponse.json({
      success: true,
      message: 'Onboarding reiniciado exitosamente. Tu sesión se cerrará para empezar desde cero.',
      logoutRequired: true
    });

  } catch (error) {
    console.error('❌ Error restarting onboarding:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}