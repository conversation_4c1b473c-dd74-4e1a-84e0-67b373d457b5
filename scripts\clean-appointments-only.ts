#!/usr/bin/env npx tsx

import { db } from '@/db/drizzle';
import { 
  appointments, 
  medicalConsultations, 
  medicalRecords, 
  patientInvitations 
} from '@/db/schema';
import { sql } from 'drizzle-orm';

/**
 * <PERSON><PERSON>t para limpiar solo las citas médicas manteniendo usuarios y relaciones
 * ⚠️ Este script borra las citas pero mantiene usuarios, doctores, pacientes, etc.
 */

async function cleanAppointmentsOnly() {
  console.log('🏥 LIMPIEZA SELECTIVA: Solo citas médicas');
  console.log('📋 Tablas que serán limpiadas:');
  console.log('   - appointments (citas médicas)');
  console.log('   - medical_consultations (consultas médicas)');
  console.log('   - medical_records (expedientes médicos)');
  console.log('   - patient_invitations (invitaciones de pacientes)');
  console.log('');
  console.log('✅ SE MANTIENEN:');
  console.log('   - users (usuarios/pacientes/doctores)');
  console.log('   - guardian_patient_relations (relaciones familiares)');
  console.log('   - consultories (consultorios)');
  console.log('   - activity_types (tipos de actividad)');
  console.log('   - medical_services (servicios médicos)');
  console.log('   - todos los catálogos');
  console.log('');

  try {
    console.log('🧹 Iniciando limpieza selectiva...');

    // Verificar cantidad de registros antes de borrar
    console.log('📊 Contando registros actuales...');
    
    const counts = await Promise.all([
      db.select({ count: sql<number>`count(*)` }).from(appointments),
      db.select({ count: sql<number>`count(*)` }).from(medicalConsultations),
      db.select({ count: sql<number>`count(*)` }).from(medicalRecords),
      db.select({ count: sql<number>`count(*)` }).from(patientInvitations)
    ]);

    console.log('📋 Registros encontrados:');
    console.log(`   - Citas: ${counts[0][0].count}`);
    console.log(`   - Consultas médicas: ${counts[1][0].count}`);
    console.log(`   - Expedientes médicos: ${counts[2][0].count}`);
    console.log(`   - Invitaciones de pacientes: ${counts[3][0].count}`);
    console.log('');

    const totalRecords = counts.reduce((sum, count) => sum + count[0].count, 0);
    
    if (totalRecords === 0) {
      console.log('✅ No hay citas para borrar. Base de datos ya limpia.');
      return;
    }

    console.log(`🗑️ Se borrarán ${totalRecords} registros de citas`);
    console.log('');

    // PASO 1: Borrar en orden correcto para evitar problemas de foreign keys
    console.log('1️⃣ Borrando consultas médicas...');
    const consultationsResult = await db.delete(medicalConsultations);
    console.log(`   ✅ Consultas médicas borradas`);

    console.log('2️⃣ Borrando expedientes médicos...');
    const recordsResult = await db.delete(medicalRecords);
    console.log(`   ✅ Expedientes médicos borrados`);

    console.log('3️⃣ Borrando citas médicas...');
    const appointmentsResult = await db.delete(appointments);
    console.log(`   ✅ Citas médicas borradas`);

    console.log('4️⃣ Borrando invitaciones relacionadas con citas...');
    const invitationsResult = await db.delete(patientInvitations);
    console.log(`   ✅ Invitaciones borradas`);

    console.log('');
    console.log('🎉 ¡LIMPIEZA SELECTIVA COMPLETADA!');
    console.log('');
    console.log('📋 Resumen de registros borrados:');
    console.log(`   - Citas médicas: ${counts[0][0].count}`);
    console.log(`   - Consultas médicas: ${counts[1][0].count}`);
    console.log(`   - Expedientes médicos: ${counts[2][0].count}`);
    console.log(`   - Invitaciones: ${counts[3][0].count}`);
    console.log('');
    console.log('✅ Las citas han sido borradas');
    console.log('👥 Usuarios, doctores, pacientes y relaciones se mantienen intactos');
    console.log('🏥 Consultorios y catálogos se mantienen intactos');
    console.log('💡 Puedes empezar a crear nuevas citas inmediatamente');
    console.log('');

    // Verificar que las citas se borraron
    console.log('🔍 Verificando limpieza...');
    const finalCounts = await Promise.all([
      db.select({ count: sql<number>`count(*)` }).from(appointments),
      db.select({ count: sql<number>`count(*)` }).from(medicalConsultations),
      db.select({ count: sql<number>`count(*)` }).from(medicalRecords),
      db.select({ count: sql<number>`count(*)` }).from(patientInvitations)
    ]);

    const remainingRecords = finalCounts.reduce((sum, count) => sum + count[0].count, 0);
    
    if (remainingRecords === 0) {
      console.log('✅ VERIFICACIÓN EXITOSA: Todas las citas fueron borradas');
    } else {
      console.log(`⚠️ ADVERTENCIA: Quedan ${remainingRecords} registros de citas`);
      console.log('📋 Registros restantes:');
      console.log(`   - Citas: ${finalCounts[0][0].count}`);
      console.log(`   - Consultas médicas: ${finalCounts[1][0].count}`);
      console.log(`   - Expedientes médicos: ${finalCounts[2][0].count}`);
      console.log(`   - Invitaciones: ${finalCounts[3][0].count}`);
    }

  } catch (error) {
    console.error('❌ ERROR durante la limpieza:', error);
    console.log('');
    console.log('🔧 Posibles soluciones:');
    console.log('1. Verificar que la base de datos esté disponible');
    console.log('2. Verificar permisos de base de datos');
    console.log('3. Revisar foreign key constraints');
    
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  cleanAppointmentsOnly()
    .then(() => {
      console.log('🏁 Script completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script falló:', error);
      process.exit(1);
    });
}

export { cleanAppointmentsOnly };