'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  User, 
  Plus, 
  Search, 
  Filter, 
  RefreshCw, 
  Download,
  ArrowLeft,
  Eye,
  Edit,
  Mail,
  Phone,
  Calendar,
  Shield,
  MoreHorizontal,
  UserPlus,
  Send,
  X,
  Check,
  Users,
  AlertCircle
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import { cn, formatDate } from '@/lib/utils';

import { es } from 'date-fns/locale';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  documentType?: string;
  documentNumber?: string;
  dateOfBirth?: string;
  gender?: string;
  overallStatus: string;
  hasClerkAccount: boolean;
  createdAt: string;
  age?: number;
}

interface CreatePatientFormData {
  // Datos básicos del paciente
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  alternativePhone: string;
  dateOfBirth: string;
  gender: string;
  
  // Documentación
  documentType: string;
  documentNumber: string;
  
  // Ubicación
  address: string;
  countryId: string;
  departmentId: string;
  municipalityId: string;
  occupationId: string;
  
  // Contacto de emergencia
  emergencyContact: string;
  emergencyPhone: string;
  emergencyRelationshipId: string;
  
  // Si es menor de edad
  isMinor: boolean;
  guardianInfo: GuardianInfo;
  
  // Opciones
  sendInvitation: boolean;
}

interface GuardianInfo {
  createNewGuardian: boolean;
  existingGuardianId: string;
  
  // Para guardian nuevo
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  documentType: string;
  documentNumber: string;
  
  // Relación
  relationship: string;
  isPrimary: boolean;
  canMakeDecisions: boolean;
}

interface CatalogItem {
  id: string | number;
  name: string;
  category?: string;
  isActive?: boolean;
}

export default function DoctorPacientesPage() {
  const router = useRouter();
  const { user } = useUser();
  
  // Estados principales
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [accountFilter, setAccountFilter] = useState('all');
  const [showFilters, setShowFilters] = useState(false);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  
  // Estados del modal de crear paciente
  const [creating, setCreating] = useState(false);
  const [formData, setFormData] = useState<CreatePatientFormData>({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    alternativePhone: '',
    dateOfBirth: '',
    gender: '',
    documentType: '',
    documentNumber: '',
    address: '',
    countryId: '',
    departmentId: '',
    municipalityId: '',
    occupationId: '',
    emergencyContact: '',
    emergencyPhone: '',
    emergencyRelationshipId: '',
    isMinor: false,
    guardianInfo: {
      createNewGuardian: true,
      existingGuardianId: '',
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      documentType: '',
      documentNumber: '',
      relationship: '',
      isPrimary: true,
      canMakeDecisions: true
    },
    sendInvitation: false
  });
  
  // Estados para catálogos
  const [catalogs, setCatalogs] = useState({
    documentTypes: [] as CatalogItem[],
    countries: [] as CatalogItem[],
    departments: [] as CatalogItem[],
    municipalities: [] as CatalogItem[],
    occupations: [] as CatalogItem[],
    relationships: [] as CatalogItem[],
    guardians: [] as CatalogItem[]
  });
  
  const [catalogsLoading, setCatalogsLoading] = useState(false);
  const [guardianSearch, setGuardianSearch] = useState('');
  
  // Estados de paginación
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  
  // Estadísticas
  const [stats, setStats] = useState({
    total: 0,
    withAccount: 0,
    withoutAccount: 0,
    pending: 0,
    invited: 0,
  });

  // Obtener pacientes
  const fetchPatients = async () => {
    try {
      setLoading(true);
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        ...(accountFilter !== 'all' && { hasAccount: accountFilter }),
        orderBy: 'updatedAt',
        orderDirection: 'desc'
      });

      const response = await fetch(`/api/catalogs/patients?${params}`);
      const data = await response.json();

      if (response.ok) {
        // Calcular edad y estado de cuenta para cada paciente
        const patientsWithAge = data.data.map((patient: any) => ({
          ...patient,
          age: patient.dateOfBirth ? calculateAge(patient.dateOfBirth) : null,
          hasClerkAccount: patient.overallStatus === 'active',
        }));
        
        setPatients(patientsWithAge);
        setTotalPages(data.pagination?.totalPages || 1);
        setTotal(data.pagination?.total || 0);
        
        // Calcular estadísticas
        const withAccount = patientsWithAge.filter((p: Patient) => p.hasClerkAccount).length;
        const pending = patientsWithAge.filter((p: Patient) => p.overallStatus === 'pending').length;
        const invited = patientsWithAge.filter((p: Patient) => p.overallStatus === 'invited').length;
        
        setStats({
          total: patientsWithAge.length,
          withAccount,
          withoutAccount: patientsWithAge.length - withAccount,
          pending,
          invited,
        });
      } else {
        toast.error('Error al cargar pacientes');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Funciones para cargar catálogos
  const loadCatalogs = async () => {
    try {
      setCatalogsLoading(true);
      
      const [documentTypesRes, countriesRes, relationshipsRes, occupationsRes] = await Promise.all([
        fetch('/api/catalogs/document-types?active=true&limit=100'),
        fetch('/api/catalogs/countries?active=true&limit=100'),
        fetch('/api/catalogs/relationships?active=true&limit=100'),
        fetch('/api/catalogs/occupations?active=true&limit=100')
      ]);

      const [documentTypes, countries, relationships, occupations] = await Promise.all([
        documentTypesRes.json(),
        countriesRes.json(),
        relationshipsRes.json(),
        occupationsRes.json()
      ]);

      setCatalogs({
        ...catalogs,
        documentTypes: documentTypes.data || [],
        countries: countries.data || [],
        relationships: relationships.data || [],
        occupations: occupations.data || [],
      });
    } catch (error) {
      console.error('Error loading catalogs:', error);
      toast.error('Error cargando catálogos');
    } finally {
      setCatalogsLoading(false);
    }
  };

  const loadDepartments = async (countryId: string) => {
    try {
      const response = await fetch(`/api/catalogs/departments?countryId=${countryId}&active=true&limit=100`);
      const data = await response.json();
      setCatalogs(prev => ({
        ...prev,
        departments: data.data || [],
        municipalities: [] // Reset municipalities
      }));
    } catch (error) {
      console.error('Error loading departments:', error);
    }
  };

  const loadMunicipalities = async (departmentId: string) => {
    try {
      const response = await fetch(`/api/catalogs/municipalities?departmentId=${departmentId}&active=true&limit=100`);
      const data = await response.json();
      setCatalogs(prev => ({
        ...prev,
        municipalities: data.data || []
      }));
    } catch (error) {
      console.error('Error loading municipalities:', error);
    }
  };

  const searchGuardians = async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < 2) {
      setCatalogs(prev => ({ ...prev, guardians: [] }));
      return;
    }
    
    try {
      const response = await fetch(`/api/catalogs/patients?search=${encodeURIComponent(searchTerm)}&role=guardian&limit=10`);
      const data = await response.json();
      setCatalogs(prev => ({
        ...prev,
        guardians: data.data?.map((guardian: any) => ({
          id: guardian.id,
          name: `${guardian.firstName} ${guardian.lastName}`,
          email: guardian.email
        })) || []
      }));
    } catch (error) {
      console.error('Error searching guardians:', error);
    }
  };

  // Effects
  useEffect(() => {
    fetchPatients();
  }, [page, searchTerm, statusFilter, accountFilter]);

  useEffect(() => {
    if (showCreateModal) {
      loadCatalogs();
    }
  }, [showCreateModal]);

  useEffect(() => {
    if (formData.countryId) {
      loadDepartments(formData.countryId);
    }
  }, [formData.countryId]);

  useEffect(() => {
    if (formData.departmentId) {
      loadMunicipalities(formData.departmentId);
    }
  }, [formData.departmentId]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (guardianSearch && !formData.guardianInfo.createNewGuardian) {
        searchGuardians(guardianSearch);
      }
    }, 300);
    
    return () => clearTimeout(timeoutId);
  }, [guardianSearch, formData.guardianInfo.createNewGuardian]);

  // Handlers
  const handleRefresh = () => {
    setPage(1);
    fetchPatients();
  };

  const handleCreatePatient = () => {
    setShowCreateModal(true);
  };

  const updateFormData = (field: keyof CreatePatientFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const updateGuardianData = (field: keyof GuardianInfo, value: any) => {
    setFormData(prev => ({
      ...prev,
      guardianInfo: {
        ...prev.guardianInfo,
        [field]: value
      }
    }));
  };

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      alternativePhone: '',
      dateOfBirth: '',
      gender: '',
      documentType: '',
      documentNumber: '',
      address: '',
      countryId: '',
      departmentId: '',
      municipalityId: '',
      occupationId: '',
      emergencyContact: '',
      emergencyPhone: '',
      emergencyRelationshipId: '',
      isMinor: false,
      guardianInfo: {
        createNewGuardian: true,
        existingGuardianId: '',
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        documentType: '',
        documentNumber: '',
        relationship: '',
        isPrimary: true,
        canMakeDecisions: true
      },
      sendInvitation: false
    });
    setGuardianSearch('');
  };

  const validateForm = (): boolean => {
    // Validaciones básicas
    if (!formData.firstName || !formData.lastName) {
      toast.error('Nombre y apellido son requeridos');
      return false;
    }

    if (!formData.dateOfBirth) {
      toast.error('Fecha de nacimiento es requerida');
      return false;
    }

    // Validar email
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      toast.error('Email inválido');
      return false;
    }

    // Si es menor, validar guardian
    if (formData.isMinor) {
      if (formData.guardianInfo.createNewGuardian) {
        if (!formData.guardianInfo.firstName || !formData.guardianInfo.lastName) {
          toast.error('Datos del encargado son requeridos');
          return false;
        }
        if (!formData.guardianInfo.relationship) {
          toast.error('Parentesco con el paciente es requerido');
          return false;
        }
      } else if (!formData.guardianInfo.existingGuardianId) {
        toast.error('Debe seleccionar un encargado existente');
        return false;
      }
    }

    return true;
  };

  const handleSubmitPatient = async () => {
    if (!validateForm()) return;

    setCreating(true);
    try {
      const response = await fetch('/api/patients/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          // Datos del paciente
          firstName: formData.firstName,
          lastName: formData.lastName,
          email: formData.email || undefined,
          phone: formData.phone || undefined,
          alternativePhone: formData.alternativePhone || undefined,
          dateOfBirth: formData.dateOfBirth,
          gender: formData.gender || undefined,
          documentType: formData.documentType || undefined,
          documentNumber: formData.documentNumber || undefined,
          address: formData.address || undefined,
          countryId: formData.countryId ? parseInt(formData.countryId) : undefined,
          departmentId: formData.departmentId ? parseInt(formData.departmentId) : undefined,
          municipalityId: formData.municipalityId ? parseInt(formData.municipalityId) : undefined,
          occupationId: formData.occupationId ? parseInt(formData.occupationId) : undefined,
          emergencyContact: formData.emergencyContact || undefined,
          emergencyPhone: formData.emergencyPhone || undefined,
          emergencyRelationshipId: formData.emergencyRelationshipId ? parseInt(formData.emergencyRelationshipId) : undefined,
          
          // Guardian info si es menor
          isMinor: formData.isMinor,
          guardianInfo: formData.isMinor ? {
            createNewGuardian: formData.guardianInfo.createNewGuardian,
            existingGuardianId: formData.guardianInfo.existingGuardianId || undefined,
            firstName: formData.guardianInfo.firstName || undefined,
            lastName: formData.guardianInfo.lastName || undefined,
            email: formData.guardianInfo.email || undefined,
            phone: formData.guardianInfo.phone || undefined,
            documentType: formData.guardianInfo.documentType || undefined,
            documentNumber: formData.guardianInfo.documentNumber || undefined,
            relationship: formData.guardianInfo.relationship,
            isPrimary: formData.guardianInfo.isPrimary,
            canMakeDecisions: formData.guardianInfo.canMakeDecisions
          } : undefined,

          createdBy: user?.id
        }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast.success('Paciente creado exitosamente');
        
        // Enviar invitación si está marcada
        if (formData.sendInvitation && data.data.patient.email && !data.data.patient.email.includes('@temp.local')) {
          try {
            await fetch(`/api/patients/${data.data.patient.id}/invite`, {
              method: 'POST',
            });
            toast.success('Invitación enviada al paciente');
          } catch (error) {
            toast.error('Paciente creado pero error al enviar invitación');
          }
        }

        setShowCreateModal(false);
        resetForm();
        fetchPatients();
      } else {
        toast.error(data.error || 'Error al crear paciente');
      }
    } catch (error) {
      console.error('Error creating patient:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setCreating(false);
    }
  };

  const handleViewPatient = (patient: Patient) => {
    // Por ahora mostrar detalles, más tarde podría ir a expediente
    toast.info(`Ver detalles de ${patient.firstName} ${patient.lastName}`);
  };

  const handleEditPatient = (patient: Patient) => {
    toast.info(`Editar ${patient.firstName} ${patient.lastName}`);
  };

  const handleInvitePatient = (patient: Patient) => {
    setSelectedPatient(patient);
    setShowInviteModal(true);
  };

  const handleSendInvitation = async () => {
    if (!selectedPatient) return;
    
    try {
      const response = await fetch(`/api/patients/${selectedPatient.id}/invite`, {
        method: 'POST',
      });

      const data = await response.json();

      if (response.ok) {
        toast.success('Invitación enviada exitosamente');
        setShowInviteModal(false);
        setSelectedPatient(null);
        fetchPatients();
      } else {
        toast.error(data.error || 'Error al enviar invitación');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'invited':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'inactive':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active':
        return 'Con cuenta';
      case 'pending':
        return 'Sin cuenta';
      case 'invited':
        return 'Invitado';
      case 'inactive':
        return 'Inactivo';
      default:
        return status;
    }
  };

  return (
    <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Gestión de Pacientes
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Administrar pacientes, cuentas e invitaciones
              </p>
            </div>
          </div>
        </div>
        
        {/* Botones de acción */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <RefreshCw className="h-4 w-4 mr-2" />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button onClick={handleCreatePatient}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Paciente
          </Button>
        </div>
      </div>

      {/* Estadísticas rápidas */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Pacientes
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <User className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Con Cuenta
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <Shield className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.withAccount}</div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Sin Cuenta
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-yellow-100 flex items-center justify-center">
              <UserPlus className="h-4 w-4 text-yellow-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{stats.withoutAccount}</div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Pendientes
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
              <Calendar className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pending}</div>
          </CardContent>
        </Card>
        
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Invitados
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <Send className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.invited}</div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros de búsqueda */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-600" />
              Filtros de Búsqueda
            </CardTitle>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
            >
              <Filter className="h-4 w-4 mr-2" />
              {showFilters ? 'Ocultar' : 'Mostrar'} Filtros
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Búsqueda principal */}
            <div>
              <Label className="text-sm font-medium text-gray-700">Buscar paciente</Label>
              <div className="relative mt-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <DateInput
              id="firstName"
              className="pl-10"
              disabled={page === 1}
              value={searchTerm ? new Date(searchTerm) : undefined}
            />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="gender" className="text-sm font-medium">Género</Label>
                    <Select value={formData.gender} onValueChange={(value) => updateFormData('gender', value)}>
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar género" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="male">Masculino</SelectItem>
                        <SelectItem value="female">Femenino</SelectItem>
                        <SelectItem value="other">Otro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-sm font-medium">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateFormData('email', e.target.value)}
                      placeholder="<EMAIL>"
                      className="border-gray-300"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="phone" className="text-sm font-medium">Teléfono</Label>
                    <Input
                      id="phone"
                      value={formData.phone}
                      onChange={(e) => updateFormData('phone', e.target.value)}
                      placeholder="+503 0000-0000"
                      className="border-gray-300"
                    />
                  </div>
                </div>
              </div>

              {/* Documentación */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <AlertCircle className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg text-gray-900">Documentación</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="documentType" className="text-sm font-medium">Tipo de Documento</Label>
                    <Select value={formData.documentType} onValueChange={(value) => updateFormData('documentType', value)}>
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        {catalogs.documentTypes.map((docType) => (
                          <SelectItem key={docType.id} value={docType.id.toString()}>
                            {docType.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="documentNumber" className="text-sm font-medium">Número de Documento</Label>
                    <Input
                      id="documentNumber"
                      value={formData.documentNumber}
                      onChange={(e) => updateFormData('documentNumber', e.target.value)}
                      placeholder="00000000-0"
                      className="border-gray-300"
                    />
                  </div>
                </div>
              </div>

              {/* Ubicación */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <Users className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg text-gray-900">Ubicación y Contacto</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="address" className="text-sm font-medium">Dirección</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => updateFormData('address', e.target.value)}
                      placeholder="Dirección completa"
                      rows={2}
                      className="border-gray-300"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="alternativePhone" className="text-sm font-medium">Teléfono Alternativo</Label>
                    <Input
                      id="alternativePhone"
                      value={formData.alternativePhone}
                      onChange={(e) => updateFormData('alternativePhone', e.target.value)}
                      placeholder="+503 0000-0000"
                      className="border-gray-300"
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="country" className="text-sm font-medium">País</Label>
                    <Select value={formData.countryId} onValueChange={(value) => updateFormData('countryId', value)}>
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar país" />
                      </SelectTrigger>
                      <SelectContent>
                        {catalogs.countries.map((country) => (
                          <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="department" className="text-sm font-medium">Departamento</Label>
                    <Select 
                      value={formData.departmentId} 
                      onValueChange={(value) => updateFormData('departmentId', value)}
                      disabled={!formData.countryId}
                    >
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar departamento" />
                      </SelectTrigger>
                      <SelectContent>
                        {catalogs.departments.map((department) => (
                          <SelectItem key={department.id} value={department.id.toString()}>
                            {department.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="municipality" className="text-sm font-medium">Municipio</Label>
                    <Select 
                      value={formData.municipalityId} 
                      onValueChange={(value) => updateFormData('municipalityId', value)}
                      disabled={!formData.departmentId}
                    >
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar municipio" />
                      </SelectTrigger>
                      <SelectContent>
                        {catalogs.municipalities.map((municipality) => (
                          <SelectItem key={municipality.id} value={municipality.id.toString()}>
                            {municipality.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Contacto de Emergencia */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <Phone className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg text-gray-900">Contacto de Emergencia</h3>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="emergencyContact" className="text-sm font-medium">Nombre de Contacto</Label>
                    <Input
                      id="emergencyContact"
                      value={formData.emergencyContact}
                      onChange={(e) => updateFormData('emergencyContact', e.target.value)}
                      placeholder="Nombre completo"
                      className="border-gray-300"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="emergencyPhone" className="text-sm font-medium">Teléfono de Emergencia</Label>
                    <Input
                      id="emergencyPhone"
                      value={formData.emergencyPhone}
                      onChange={(e) => updateFormData('emergencyPhone', e.target.value)}
                      placeholder="+503 0000-0000"
                      className="border-gray-300"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="emergencyRelationship" className="text-sm font-medium">Parentesco</Label>
                    <Select value={formData.emergencyRelationshipId} onValueChange={(value) => updateFormData('emergencyRelationshipId', value)}>
                      <SelectTrigger className="border-gray-300">
                        <SelectValue placeholder="Seleccionar parentesco" />
                      </SelectTrigger>
                      <SelectContent>
                        {catalogs.relationships.map((relationship) => (
                          <SelectItem key={relationship.id} value={relationship.id.toString()}>
                            {relationship.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Menor de Edad y Guardian */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <Shield className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg text-gray-900">Información del Encargado</h3>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="isMinor"
                    checked={formData.isMinor}
                    onCheckedChange={(checked) => updateFormData('isMinor', checked)}
                    className="border-blue-500 data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"
                  />
                  <Label htmlFor="isMinor" className="text-sm font-medium">
                    El paciente es menor de edad (requiere encargado)
                  </Label>
                </div>

                {formData.isMinor && (
                  <div className="space-y-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="space-y-3">
                      <div className="flex items-center space-x-4">
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="createNewGuardian"
                            checked={formData.guardianInfo.createNewGuardian}
                            onCheckedChange={(checked) => updateGuardianData('createNewGuardian', checked)}
                            className="border-blue-500 data-[state=checked]:bg-blue-500"
                          />
                          <Label htmlFor="createNewGuardian" className="text-sm">
                            Crear nuevo encargado
                          </Label>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Checkbox
                            id="selectExistingGuardian"
                            checked={!formData.guardianInfo.createNewGuardian}
                            onCheckedChange={(checked) => updateGuardianData('createNewGuardian', !checked)}
                            className="border-blue-500 data-[state=checked]:bg-blue-500"
                          />
                          <Label htmlFor="selectExistingGuardian" className="text-sm">
                            Seleccionar encargado existente
                          </Label>
                        </div>
                      </div>

                      {formData.guardianInfo.createNewGuardian ? (
                        <div className="space-y-4">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">
                                Nombre del Encargado <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                value={formData.guardianInfo.firstName}
                                onChange={(e) => updateGuardianData('firstName', e.target.value)}
                                placeholder="Nombre"
                                className="border-blue-300"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">
                                Apellido del Encargado <span className="text-red-500">*</span>
                              </Label>
                              <Input
                                value={formData.guardianInfo.lastName}
                                onChange={(e) => updateGuardianData('lastName', e.target.value)}
                                placeholder="Apellido"
                                className="border-blue-300"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Email del Encargado</Label>
                              <Input
                                type="email"
                                value={formData.guardianInfo.email}
                                onChange={(e) => updateGuardianData('email', e.target.value)}
                                placeholder="<EMAIL>"
                                className="border-blue-300"
                              />
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Teléfono del Encargado</Label>
                              <Input
                                value={formData.guardianInfo.phone}
                                onChange={(e) => updateGuardianData('phone', e.target.value)}
                                placeholder="+503 0000-0000"
                                className="border-blue-300"
                              />
                            </div>
                          </div>
                          
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Tipo de Documento</Label>
                              <Select value={formData.guardianInfo.documentType} onValueChange={(value) => updateGuardianData('documentType', value)}>
                                <SelectTrigger className="border-blue-300">
                                  <SelectValue placeholder="Seleccionar tipo" />
                                </SelectTrigger>
                                <SelectContent>
                                  {catalogs.documentTypes.map((docType) => (
                                    <SelectItem key={docType.id} value={docType.id.toString()}>
                                      {docType.name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            </div>
                            <div className="space-y-2">
                              <Label className="text-sm font-medium">Número de Documento</Label>
                              <Input
                                value={formData.guardianInfo.documentNumber}
                                onChange={(e) => updateGuardianData('documentNumber', e.target.value)}
                                placeholder="00000000-0"
                                className="border-blue-300"
                              />
                            </div>
                          </div>
                        </div>
                      ) : (
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Buscar Encargado Existente <span className="text-red-500">*</span>
                          </Label>
                          <Input
                            value={guardianSearch}
                            onChange={(e) => setGuardianSearch(e.target.value)}
                            placeholder="Escriba nombre o email del encargado..."
                            className="border-blue-300"
                          />
                          {catalogs.guardians.length > 0 && (
                            <div className="border border-blue-300 rounded-md max-h-32 overflow-y-auto">
                              {catalogs.guardians.map((guardian) => (
                                <div
                                  key={guardian.id}
                                  className={`p-2 cursor-pointer hover:bg-blue-100 ${
                                    formData.guardianInfo.existingGuardianId === guardian.id ? 'bg-blue-100' : ''
                                  }`}
                                  onClick={() => updateGuardianData('existingGuardianId', guardian.id)}
                                >
                                  <div className="font-medium">{guardian.name}</div>
                                  <div className="text-sm text-gray-600">{guardian.email}</div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      )}
                      
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label className="text-sm font-medium">
                            Parentesco <span className="text-red-500">*</span>
                          </Label>
                          <Select value={formData.guardianInfo.relationship} onValueChange={(value) => updateGuardianData('relationship', value)}>
                            <SelectTrigger className="border-blue-300">
                              <SelectValue placeholder="Seleccionar parentesco" />
                            </SelectTrigger>
                            <SelectContent>
                              {catalogs.relationships.map((relationship) => (
                                <SelectItem key={relationship.id} value={relationship.id.toString()}>
                                  {relationship.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        
                        <div className="space-y-3">
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="isPrimary"
                              checked={formData.guardianInfo.isPrimary}
                              onCheckedChange={(checked) => updateGuardianData('isPrimary', checked)}
                              className="border-blue-500 data-[state=checked]:bg-blue-500"
                            />
                            <Label htmlFor="isPrimary" className="text-sm">
                              Encargado principal
                            </Label>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Checkbox
                              id="canMakeDecisions"
                              checked={formData.guardianInfo.canMakeDecisions}
                              onCheckedChange={(checked) => updateGuardianData('canMakeDecisions', checked)}
                              className="border-blue-500 data-[state=checked]:bg-blue-500"
                            />
                            <Label htmlFor="canMakeDecisions" className="text-sm">
                              Puede tomar decisiones médicas
                            </Label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>

              {/* Opciones */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 pb-2 border-b">
                  <Mail className="h-5 w-5 text-blue-600" />
                  <h3 className="font-semibold text-lg text-gray-900">Opciones</h3>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sendInvitation"
                    checked={formData.sendInvitation}
                    onCheckedChange={(checked) => updateFormData('sendInvitation', checked)}
                    className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    disabled={!formData.email || formData.email.length === 0}
                  />
                  <Label htmlFor="sendInvitation" className="text-sm">
                    Enviar invitación por email inmediatamente
                    {(!formData.email || formData.email.length === 0) && (
                      <span className="text-gray-500"> (requiere email)</span>
                    )}
                  </Label>
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="border-t p-6">
            <Button 
              variant="outline" 
              onClick={() => setShowCreateModal(false)}
              disabled={creating}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleSubmitPatient}
              disabled={creating || catalogsLoading}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              {creating ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Creando...
                </>
              ) : (
                <>
                  <Check className="h-4 w-4 mr-2" />
                  Crear Paciente
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de invitación */}
      <Dialog open={showInviteModal} onOpenChange={setShowInviteModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Send className="h-5 w-5 text-blue-600" />
              Enviar Invitación
            </DialogTitle>
            <DialogDescription>
              Enviar invitación para activar cuenta en el portal médico
            </DialogDescription>
          </DialogHeader>

          {selectedPatient && (
            <div className="space-y-4">
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="font-semibold text-blue-900">
                  {selectedPatient.firstName} {selectedPatient.lastName}
                </div>
                <div className="text-sm text-blue-700">
                  {selectedPatient.email}
                </div>
              </div>

              <div className="text-sm text-gray-600">
                <p>Se enviará un email con un enlace para que el paciente pueda:</p>
                <ul className="list-disc list-inside mt-2 space-y-1">
                  <li>Crear su contraseña</li>
                  <li>Activar su cuenta en el portal</li>
                  <li>Acceder a sus expedientes y citas</li>
                </ul>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowInviteModal(false)}>
              Cancelar
            </Button>
            <Button onClick={handleSendInvitation}>
              <Send className="h-4 w-4 mr-2" />
              Enviar Invitación
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}