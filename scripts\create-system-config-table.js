// Script para crear la tabla system_config manualmente
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function createSystemConfigTable() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🔧 Creando tabla system_config...');
  
  try {
    // Crear la tabla system_config
    await sql`
      CREATE TABLE IF NOT EXISTS "system_config" (
        "id" serial PRIMARY KEY NOT NULL,
        "key" text NOT NULL,
        "value" jsonb NOT NULL,
        "description" text,
        "category" text DEFAULT 'general',
        "active" boolean DEFAULT true,
        "created_at" timestamp DEFAULT now(),
        "updated_at" timestamp DEFAULT now(),
        CONSTRAINT "system_config_key_unique" UNIQUE("key")
      );
    `;
    
    console.log('✅ Tabla system_config creada exitosamente');
    
    // Verificar que se creó
    const tableExists = await sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' AND table_name = 'system_config';
    `;
    
    if (tableExists.length > 0) {
      console.log('✅ Verificación: tabla system_config existe');
    } else {
      console.log('❌ Error: tabla no se pudo crear');
      return false;
    }
    
    return true;
    
  } catch (error) {
    console.error('❌ Error creando tabla:', error.message);
    return false;
  }
}

// Ejecutar
createSystemConfigTable().then(success => {
  if (success) {
    console.log('\n🎉 ¡Tabla system_config lista!');
    console.log('📝 Próximo paso: ejecutar el script de configuración regional completa');
  } else {
    console.log('\n💥 Error: no se pudo crear la tabla');
  }
});