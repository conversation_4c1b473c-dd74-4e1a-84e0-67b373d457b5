// Script completo para configurar todo el sistema regional
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function setupCompleteRegionalSystem() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🚀 Configurando sistema completo de configuración regional...\n');
  
  try {
    // ===============================================
    // PASO 1: Insertar configuraciones en system_config
    // ===============================================
    console.log('📊 Paso 1: Configuraciones globales en system_config...');
    
    const configs = [
      {
        key: 'regional.dateFormat',
        value: '"dd/MM/yyyy"',
        description: 'Formato de fecha predeterminado del sistema',
        category: 'regional'
      },
      {
        key: 'regional.dateTimeFormat', 
        value: '"dd/MM/yyyy HH:mm"',
        description: 'Formato de fecha y hora predeterminado',
        category: 'regional'
      },
      {
        key: 'regional.timeFormat',
        value: '"HH:mm"',
        description: 'Formato de hora predeterminado (24 horas)',
        category: 'regional'
      },
      {
        key: 'regional.currency',
        value: '"GTQ"',
        description: 'Moneda predeterminada del sistema',
        category: 'regional'
      },
      {
        key: 'regional.currencySymbol',
        value: '"Q"',
        description: 'Símbolo de moneda predeterminado',
        category: 'regional'
      },
      {
        key: 'regional.currencyPosition',
        value: '"before"',
        description: 'Posición del símbolo de moneda (before/after)',
        category: 'regional'
      },
      {
        key: 'regional.locale',
        value: '"es-GT"',
        description: 'Configuración regional predeterminada',
        category: 'regional'
      },
      {
        key: 'regional.timezone',
        value: '"America/Guatemala"',
        description: 'Zona horaria predeterminada',
        category: 'regional'
      },
      {
        key: 'regional.decimalSeparator',
        value: '"."',
        description: 'Separador decimal para números',
        category: 'regional'
      },
      {
        key: 'regional.thousandsSeparator',
        value: '","',
        description: 'Separador de miles para números',
        category: 'regional'
      },
      {
        key: 'regional.weekStartsOn',
        value: '1',
        description: 'Día de inicio de semana (0=Domingo, 1=Lunes)',
        category: 'regional'
      }
    ];

    let configsInserted = 0;
    for (const config of configs) {
      try {
        await sql`
          INSERT INTO system_config (key, value, description, category, active, created_at, updated_at) 
          VALUES (${config.key}, ${config.value}::jsonb, ${config.description}, ${config.category}, true, now(), now())
          ON CONFLICT (key) DO UPDATE SET 
            value = ${config.value}::jsonb,
            description = ${config.description},
            category = ${config.category},
            active = true,
            updated_at = now()
        `;
        console.log(`   ✅ ${config.key}`);
        configsInserted++;
      } catch (error) {
        console.error(`   ❌ Error con ${config.key}: ${error.message}`);
      }
    }
    
    console.log(`\n📊 Resultado Paso 1: ${configsInserted}/${configs.length} configuraciones insertadas\n`);

    // ===============================================
    // PASO 2: Agregar columna regional_settings a consultories
    // ===============================================
    console.log('🏥 Paso 2: Configurando consultories...');
    
    // Verificar si la columna existe
    const columnExists = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'consultories' AND column_name = 'regional_settings';
    `;
    
    if (columnExists.length === 0) {
      console.log('   ➕ Agregando columna regional_settings...');
      await sql`
        ALTER TABLE consultories 
        ADD COLUMN regional_settings jsonb DEFAULT '{
          "dateFormat": "dd/MM/yyyy",
          "dateTimeFormat": "dd/MM/yyyy HH:mm", 
          "timeFormat": "HH:mm",
          "currency": "GTQ",
          "currencySymbol": "Q",
          "currencyPosition": "before",
          "locale": "es-GT",
          "timezone": "America/Guatemala",
          "decimalSeparator": ".",
          "thousandsSeparator": ",",
          "weekStartsOn": 1
        }'::jsonb;
      `;
      console.log('   ✅ Columna agregada exitosamente');
    } else {
      console.log('   ℹ️ Columna regional_settings ya existe');
    }

    // ===============================================  
    // PASO 3: Actualizar consultories existentes
    // ===============================================
    console.log('\n🔄 Paso 3: Actualizando consultories existentes...');
    
    const defaultSettings = {
      dateFormat: "dd/MM/yyyy",
      dateTimeFormat: "dd/MM/yyyy HH:mm", 
      timeFormat: "HH:mm",
      currency: "GTQ",
      currencySymbol: "Q",
      currencyPosition: "before",
      locale: "es-GT",
      timezone: "America/Guatemala",
      decimalSeparator: ".",
      thousandsSeparator: ",",
      weekStartsOn: 1
    };

    const updateResult = await sql`
      UPDATE consultories 
      SET 
        regional_settings = ${JSON.stringify(defaultSettings)}::jsonb,
        "updatedAt" = now()
      WHERE 
        regional_settings IS NULL 
        OR regional_settings = '{}'::jsonb
        OR jsonb_array_length(regional_settings) = 0;
    `;
    
    console.log(`   ✅ ${updateResult.count || 0} consultories actualizados`);

    // ===============================================
    // PASO 4: Crear índices para performance
    // ===============================================
    console.log('\n🚀 Paso 4: Creando índices...');
    
    try {
      await sql`CREATE INDEX IF NOT EXISTS "consultories_regional_settings_idx" ON "consultories" USING gin("regional_settings");`;
      console.log('   ✅ Índice consultories_regional_settings_idx creado');
    } catch (e) {
      console.log('   ℹ️ Índice consultories ya existe');
    }
    
    try {
      await sql`CREATE INDEX IF NOT EXISTS "system_config_category_idx" ON "system_config" ("category") WHERE active = true;`;
      console.log('   ✅ Índice system_config_category_idx creado');
    } catch (e) {
      console.log('   ℹ️ Índice system_config ya existe');
    }

    // ===============================================
    // PASO 5: Verificación final
    // ===============================================
    console.log('\n🔍 Paso 5: Verificación final...');
    
    const systemConfigCount = await sql`SELECT COUNT(*) as count FROM system_config WHERE category = 'regional' AND active = true;`;
    console.log(`   📊 Configuraciones globales: ${systemConfigCount[0].count}`);
    
    const consultoriesWithSettings = await sql`
      SELECT COUNT(*) as count 
      FROM consultories 
      WHERE regional_settings IS NOT NULL AND regional_settings != '{}'::jsonb;
    `;
    console.log(`   🏥 Consultories configurados: ${consultoriesWithSettings[0].count}`);
    
    // Mostrar algunos consultories como ejemplo
    const sampleConsultories = await sql`
      SELECT id, name, 
        CASE 
          WHEN regional_settings IS NOT NULL AND regional_settings != '{}'::jsonb 
          THEN '✅ Configurado' 
          ELSE '❌ Sin configurar' 
        END as status
      FROM consultories 
      WHERE (active = true OR "isActive" = true)
      LIMIT 3;
    `;
    
    console.log('\n📋 Ejemplos de consultories:');
    sampleConsultories.forEach(c => {
      console.log(`   - ${c.name}: ${c.status}`);
    });

    // ===============================================
    // RESUMEN FINAL
    // ===============================================
    console.log('\n🎉 ¡SISTEMA REGIONAL COMPLETAMENTE CONFIGURADO!');
    console.log('\n📋 Resumen:');
    console.log(`   ✅ ${configsInserted} configuraciones globales en system_config`);
    console.log(`   ✅ ${consultoriesWithSettings[0].count} consultories con configuración regional`);
    console.log('   ✅ Índices de performance creados');
    console.log('   ✅ RegionalConfigProvider agregado al layout');
    
    console.log('\n🚀 El sistema está listo para usar!');
    console.log('\n📝 Próximos pasos:');
    console.log('   1. ✅ Ya puedes usar useRegionalConfig() en cualquier componente');
    console.log('   2. ✅ Las funciones de formateo usarán configuración automáticamente');  
    console.log('   3. 🔧 Opcional: configurar consultories específicos con diferentes regiones');
    
    return true;
    
  } catch (error) {
    console.error('\n💥 Error durante la configuración:', error.message);
    console.error(error);
    return false;
  }
}

// Ejecutar
setupCompleteRegionalSystem().then(success => {
  if (success) {
    console.log('\n✨ ¡Todo completado exitosamente!');
  } else {
    console.log('\n😞 Hubo errores durante la configuración');
  }
});