'use client';

import { useState, useEffect } from 'react';
import { usePara<PERSON>, useSearchParams, useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { ArrowLeft, Save, Edit, Calendar, User, Stethoscope, Home, Clock, DollarSign, FileText, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Badge } from '@/components/ui/badge';
import { TimePicker } from '@/components/ui/time-picker';
import { DurationPicker } from '@/components/ui/duration-picker';
import { ServiceSelector } from '@/components/ui/service-selector';
import { PatientSelector } from '@/components/ui/patient-selector';
import { DatePicker } from '@/components/ui/date-picker';
import { cn } from '@/lib/utils';
import { format, setHours, setMinutes, addMinutes } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';

export default function AssistantAppointmentPage() {
  const { user } = useUser();
  const router = useRouter();
  const params = useParams();
  const searchParams = useSearchParams();

  // Determinar modo de operación
  const isNew = params.params?.[0] === 'new';
  const appointmentId = !isNew ? params.params?.[0] : null;
  const isEditing = isNew || searchParams.get('mode') === 'edit';

  // Estados principales
  const [appointment, setAppointment] = useState(null);
  const [loading, setLoading] = useState(!isNew);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');

  // Estados para catálogos
  const [doctors, setDoctors] = useState([]);
  const [patients, setPatients] = useState([]);
  const [consultories, setConsultories] = useState([]);
  const [services, setServices] = useState([]);
  const [activityTypes, setActivityTypes] = useState([]);
  const [doctorConsultory, setDoctorConsultory] = useState(null);

  // Estados para rastrear campos pre-seleccionados (no editables)
  const [isPreselectedDoctor, setIsPreselectedDoctor] = useState(false);
  const [isPreselectedConsultory, setIsPreselectedConsultory] = useState(false);

  // Estado del formulario
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    doctorId: '',
    patientId: '',
    consultoryId: '',
    serviceId: '',
    activityTypeId: '',
    chiefComplaint: '',
    scheduledDate: new Date(),
    startTime: '09:00',
    duration: '30',
    estimatedPrice: '',
    currency: 'GTQ',
    isEmergency: false,
    requiresReminder: true,
    status: 'scheduled'
  });

  // Cargar datos iniciales
  useEffect(() => {
    const loadInitialData = async () => {
      await fetchCatalogs();
      
      if (!isNew && appointmentId) {
        await fetchAppointment();
      } else {
        // Para nuevas citas, manejar parámetros de URL
        const preselectedDate = searchParams.get('date');
        const preselectedTime = searchParams.get('time');
        const preselectedDoctor = searchParams.get('doctorId');
        
        setFormData(prev => ({
          ...prev,
          doctorId: preselectedDoctor || '',
          scheduledDate: preselectedDate ? new Date(preselectedDate) : new Date(),
          startTime: preselectedTime || '09:00'
        }));
        
        // Si hay un doctor pre-seleccionado, marcar como no editable y cargar su consultorio
        if (preselectedDoctor) {
          setIsPreselectedDoctor(true);
          await fetchDoctorConsultoryById(preselectedDoctor);
        }
        
        setLoading(false);
      }
    };
    
    loadInitialData();
  }, [isNew, appointmentId, user?.id, searchParams]);

  // Establecer actividad por defecto cuando se cargan los tipos
  useEffect(() => {
    if (isNew && activityTypes.length > 0 && !formData.activityTypeId) {
      // Buscar por ID específico 'consultation' o por nombre "Consulta médica"
      let defaultActivity = activityTypes.find(
        at => at.id === 'consultation' || 
        at.name.toLowerCase() === 'consulta médica' ||
        at.name.toLowerCase().includes('consulta médica')
      );
      
      // Si no encontramos, buscar la primera actividad médica con paciente ordenada por 'order'
      if (!defaultActivity) {
        const sortedActivities = [...activityTypes].sort((a, b) => (a.order || 999) - (b.order || 999));
        defaultActivity = sortedActivities.find(
          at => at.category === 'medical' && at.requiresPatient
        );
      }
      
      // Como última opción, usar cualquier actividad que requiera paciente
      if (!defaultActivity) {
        defaultActivity = activityTypes.find(at => at.requiresPatient);
      }
      
      if (defaultActivity) {
        setFormData(prev => ({
          ...prev,
          activityTypeId: defaultActivity.id,
          duration: defaultActivity.duration.toString(),
          title: defaultActivity.name
        }));
      }
    }
  }, [isNew, activityTypes, formData.activityTypeId]);

  // Establecer servicio médico por defecto cuando se selecciona una actividad médica
  useEffect(() => {
    const selectedActivityType = activityTypes.find(at => at.id === formData.activityTypeId);
    
    // Solo proceder si es una actividad médica con paciente y no hay servicio seleccionado
    if (selectedActivityType?.category === 'medical' && 
        selectedActivityType?.requiresPatient && 
        !formData.serviceId &&
        services.length > 0) {
      
      // 1. Buscar "Consulta General" o "Consulta Pediátrica"
      let defaultService = services.find(
        (s: any) => s.name?.toLowerCase() === 'consulta general' ||
                    s.name?.toLowerCase() === 'consulta pediátrica' ||
                    s.name?.toLowerCase().includes('consulta general')
      );
      
      // 2. Si no existe, buscar servicios con category = "Consulta"
      if (!defaultService) {
        defaultService = services.find((s: any) => s.category?.toLowerCase() === 'consulta');
      }
      
      // 3. Si no hay, tomar el primer servicio activo
      if (!defaultService && services.length > 0) {
        defaultService = services[0];
      }
      
      // Aplicar el servicio predeterminado
      if (defaultService) {
        setFormData(prev => ({
          ...prev,
          serviceId: defaultService.id,
          estimatedPrice: defaultService.basePrice?.toString() || '',
          duration: defaultService.duration?.toString() || prev.duration
        }));
      }
    }
  }, [formData.activityTypeId, activityTypes, services]);

  // Cargar consultorio del doctor (para asistentes puede no aplicar, pero mantenemos la estructura)
  const fetchDoctorConsultory = async () => {
    try {
      if (!user?.id) return;
      
      const response = await fetch('/api/doctor/consultory');
      const data = await response.json();
      
      if (response.ok && data.success && data.data) {
        setDoctorConsultory(data.data);
        // No precargar consultorio para asistentes
      }
    } catch (error) {
      console.error('Error al cargar consultorio del doctor:', error);
    }
  };

  // Cargar consultorio de un médico específico por ID
  const fetchDoctorConsultoryById = async (doctorId: string) => {
    try {
      console.log('🏥 Cargando consultorio para médico:', doctorId);
      
      const response = await fetch(`/api/doctors/${doctorId}/consultory`);
      const data = await response.json();
      
      if (response.ok && data.success && data.data) {
        console.log('✅ Consultorio cargado:', data.data);
        
        // Pre-seleccionar el consultorio en el formulario
        setFormData(prev => ({
          ...prev,
          consultoryId: data.data.id
        }));
        
        // Marcar que este consultorio vino pre-seleccionado y no es editable
        setIsPreselectedConsultory(true);
        setDoctorConsultory(data.data);
      } else {
        console.error('❌ Error al cargar consultorio:', data.error);
      }
    } catch (error) {
      console.error('Error al cargar consultorio del médico:', error);
    }
  };

  // Cargar catálogos
  const fetchCatalogs = async () => {
    try {
      // Cargar doctores
      const doctorsResponse = await fetch('/api/catalogs/doctor-service-prices/doctors');
      if (doctorsResponse.ok) {
        const doctorsData = await doctorsResponse.json();
        setDoctors(doctorsData.data || []);
      }

      // Cargar pacientes
      const patientsResponse = await fetch('/api/catalogs/patients');
      if (patientsResponse.ok) {
        const patientsData = await patientsResponse.json();
        setPatients(patientsData.data || []);
      }

      // Cargar consultorios
      const consultoriesResponse = await fetch('/api/catalogs/consultories?isActive=true');
      if (consultoriesResponse.ok) {
        const consultoriesData = await consultoriesResponse.json();
        setConsultories(consultoriesData.data || []);
      }

      // Cargar servicios
      const servicesResponse = await fetch('/api/catalogs/medical-services?isActive=true');
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json();
        setServices(servicesData.data || []);
      }

      // Cargar tipos de actividad
      const activityTypesResponse = await fetch('/api/catalogs/activity-types?isActive=true');
      if (activityTypesResponse.ok) {
        const activityTypesData = await activityTypesResponse.json();
        setActivityTypes(activityTypesData.data || []);
      }
    } catch (error) {
      console.error('Error cargando catálogos:', error);
    }
  };

  // Cargar cita existente
  const fetchAppointment = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/appointments/${appointmentId}`);
      
      if (response.ok) {
        const data = await response.json();
        setAppointment(data.data);
        
        // Cargar datos en el formulario
        setFormData({
          title: data.data.title || '',
          description: data.data.description || '',
          doctorId: data.data.doctorId || '',
          patientId: data.data.patientId || '',
          consultoryId: data.data.consultoryId || '',
          serviceId: data.data.serviceId || '',
          activityTypeId: data.data.activityTypeId || '',
          chiefComplaint: data.data.chiefComplaint || '',
          scheduledDate: new Date(data.data.scheduledDate),
          startTime: format(new Date(data.data.startTime), 'HH:mm'),
          duration: data.data.duration?.toString() || '30',
          estimatedPrice: data.data.estimatedPrice?.toString() || '',
          currency: data.data.currency || 'GTQ',
          isEmergency: data.data.isEmergency || false,
          requiresReminder: data.data.requiresReminder !== false,
          status: data.data.status || 'scheduled'
        });
      } else {
        setError('Error al cargar la cita');
      }
    } catch (error) {
      console.error('Error:', error);
      setError('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  // Actualizar campo del formulario
  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Guardar cita
  const handleSave = async () => {
    try {
      setProcessing(true);
      
      // Validaciones básicas antes de enviar
      if (!formData.title) {
        toast.error('El título es requerido');
        return;
      }
      if (!formData.doctorId) {
        toast.error('Debe seleccionar un médico');
        return;
      }
      if (!formData.consultoryId) {
        toast.error('Debe seleccionar un consultorio');
        return;
      }
      if (!formData.activityTypeId) {
        toast.error('Debe seleccionar un tipo de actividad');
        return;
      }
      
      // Preparar las fechas y horas SIN conversión UTC (mantener hora local)
      const [hours, minutes] = formData.startTime.split(':').map(Number);
      
      // Crear fechas en formato que la DB interprete como hora local de Guatemala
      const year = formData.scheduledDate.getFullYear();
      const month = String(formData.scheduledDate.getMonth() + 1).padStart(2, '0');
      const day = String(formData.scheduledDate.getDate()).padStart(2, '0');
      const hourStr = String(hours).padStart(2, '0');
      const minuteStr = String(minutes).padStart(2, '0');
      
      // Calcular hora de fin
      const endHours = hours + Math.floor(parseInt(formData.duration) / 60);
      const endMinutes = minutes + (parseInt(formData.duration) % 60);
      const finalEndHours = endHours + Math.floor(endMinutes / 60);
      const finalEndMinutes = endMinutes % 60;
      
      const payload = {
        ...formData,
        scheduledDate: `${year}-${month}-${day}T00:00:00`,
        startTime: `${year}-${month}-${day}T${hourStr}:${minuteStr}:00`,
        endTime: `${year}-${month}-${day}T${String(finalEndHours).padStart(2, '0')}:${String(finalEndMinutes).padStart(2, '0')}:00`,
        duration: parseInt(formData.duration),
        estimatedPrice: formData.estimatedPrice ? parseFloat(formData.estimatedPrice) : null,
      };
      
      console.log('🚀 Enviando payload:', payload);
      
      const url = isNew ? '/api/appointments' : `/api/appointments/${appointmentId}`;
      const method = isNew ? 'POST' : 'PUT';
      
      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
      
      if (response.ok) {
        toast.success(isNew ? 'Cita creada correctamente' : 'Cita actualizada correctamente');
        router.push('/dashboard/assistant/agenda');
      } else {
        const error = await response.json();
        console.error('Error al guardar cita:', error);
        toast.error(error.error || 'Error al guardar la cita');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setProcessing(false);
    }
  };


  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-red-600">{error}</p>
        <Button onClick={() => router.push('/dashboard/assistant/agenda')} className="mt-4">
          Volver a la agenda
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header responsivo */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
        <div className="flex items-center space-x-3">
          <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/assistant/agenda')}>
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {isNew ? 'Nueva Cita Médica' : formData.title || 'Cita Médica'}
            </h1>
            <p className="text-gray-600 text-sm">
              {isNew ? 'Complete la información para programar la cita' : `ID: ${appointmentId}`}
            </p>
            
            {/* Badges en móvil */}
            {!isNew && (
              <div className="flex items-center gap-2 mt-2 lg:hidden">
                <Badge className={formData.status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 
                                 formData.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                                 formData.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                                 'bg-red-100 text-red-800'}>
                  {formData.status === 'scheduled' ? 'Programada' :
                   formData.status === 'confirmed' ? 'Confirmada' :
                   formData.status === 'completed' ? 'Completada' : 'Cancelada'}
                </Badge>
                {formData.isEmergency && (
                  <Badge variant="secondary">Emergencia</Badge>
                )}
              </div>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Badges en desktop */}
          {!isNew && (
            <div className="hidden lg:flex items-center gap-2">
              <Badge className={formData.status === 'scheduled' ? 'bg-blue-100 text-blue-800' : 
                               formData.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                               formData.status === 'completed' ? 'bg-gray-100 text-gray-800' :
                               'bg-red-100 text-red-800'}>
                {formData.status === 'scheduled' ? 'Programada' :
                 formData.status === 'confirmed' ? 'Confirmada' :
                 formData.status === 'completed' ? 'Completada' : 'Cancelada'}
              </Badge>
              {formData.isEmergency && (
                <Badge variant="secondary">Emergencia</Badge>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Todas las secciones en una sola columna */}
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Información Básica - Solo tipo de actividad inicialmente */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              Tipo de Actividad
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="activityType" className="text-sm font-medium text-gray-700">
                  Seleccione el tipo de actividad <span className="text-red-500">*</span>
                </Label>
                {isEditing ? (
                  <Select
                    value={formData.activityTypeId}
                    onValueChange={(activityTypeId) => {
                      const selectedActivityType = activityTypes.find(at => at.id === activityTypeId);
                      
                      // Actualizar todos los campos relacionados
                      const updates: any = {
                        activityTypeId,
                      };
                      
                      if (selectedActivityType) {
                        updates.duration = selectedActivityType.duration.toString();
                        
                        // Siempre actualizar el título con el nombre de la actividad
                        // a menos que el usuario haya personalizado el título
                        const currentActivity = activityTypes.find(at => at.id === formData.activityTypeId);
                        if (!formData.title.trim() || formData.title === currentActivity?.name) {
                          updates.title = selectedActivityType.name;
                        }
                        
                        // Limpiar campos que no aplican
                        if (!selectedActivityType.requiresPatient) {
                          updates.patientId = '';
                          updates.serviceId = '';
                          updates.estimatedPrice = '';
                          updates.chiefComplaint = '';
                        }
                        
                        if (selectedActivityType.category !== 'medical' || !selectedActivityType.requiresPatient) {
                          updates.serviceId = '';
                          updates.estimatedPrice = '';
                        }
                      }
                      
                      // Actualizar todo el estado de una vez
                      setFormData(prev => ({ ...prev, ...updates }));
                      
                      // Si es una actividad médica con paciente, el useEffect se encargará de establecer el servicio predeterminado
                    }}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Primero seleccione el tipo de actividad" />
                    </SelectTrigger>
                    <SelectContent>
                      {activityTypes.map((activityType: any) => (
                        <SelectItem key={activityType.id} value={activityType.id}>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: activityType.color }}
                            />
                            <span>{activityType.name}</span>
                            <span className="text-xs text-gray-500">
                              ({activityType.duration}min)
                            </span>
                            {!activityType.respectsSchedule && (
                              <span className="text-xs bg-red-100 text-red-600 px-1 rounded">
                                Libre
                              </span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md">
                    {activityTypes.find((at: any) => at.id === formData.activityTypeId)?.name || 'No especificado'}
                  </p>
                )}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Mostrar el resto del formulario siempre */}
        {(() => {
          const selectedActivityType = activityTypes.find(at => at.id === formData.activityTypeId) || {};
          const showMedicalFields = selectedActivityType.requiresPatient && selectedActivityType.category === 'medical';
          const showPatientField = selectedActivityType.requiresPatient !== false; // Por defecto mostrar paciente

          return (
            <>
              {/* Información Básica de la Cita */}
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    Información de la Cita
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="title" className="text-sm font-medium text-gray-700">
                        Título de la cita <span className="text-red-500">*</span>
                      </Label>
                      {isEditing ? (
                        <Input
                          id="title"
                          value={formData.title}
                          onChange={(e) => updateFormData('title', e.target.value)}
                          placeholder="Ej: Consulta General"
                          className="h-11"
                        />
                      ) : (
                        <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md">
                          {formData.title || 'No especificado'}
                        </p>
                      )}
                    </div>

                    {/* Solo mostrar servicio médico para actividades médicas con paciente */}
                    {showMedicalFields && (
                      <div className="space-y-2">
                        <Label htmlFor="service" className="text-sm font-medium text-gray-700">
                          Servicio médico
                        </Label>
                        <ServiceSelector
                          services={services}
                          value={formData.serviceId}
                          onChange={(serviceId) => {
                            const service = services.find((s: any) => s.id === serviceId);
                            updateFormData('serviceId', serviceId);
                            if (service?.basePrice) {
                              updateFormData('estimatedPrice', service.basePrice.toString());
                            }
                            if (service?.duration) {
                              updateFormData('duration', service.duration.toString());
                            }
                          }}
                          disabled={!isEditing}
                          placeholder="Buscar y seleccionar servicio médico..."
                        />
                        <p className="text-xs text-gray-600">
                          El servicio determina el precio y se trasladará al expediente médico
                        </p>
                      </div>
                    )}

                    <div className="space-y-2">
                      <Label htmlFor="description" className="text-sm font-medium text-gray-700">
                        Descripción adicional
                      </Label>
                      {isEditing ? (
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => updateFormData('description', e.target.value)}
                          placeholder="Información adicional sobre la cita..."
                          rows={2}
                          className="resize-none"
                        />
                      ) : (
                        <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md min-h-[60px]">
                          {formData.description || 'No especificado'}
                        </p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Participantes */}
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <User className="h-5 w-5 text-blue-600" />
                    Participantes
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="consultory" className="text-sm font-medium text-gray-700">
                          <Home className="h-4 w-4 inline mr-1" />
                          Consultorio <span className="text-red-500">*</span>
                        </Label>
                        {isEditing && !isPreselectedConsultory ? (
                          <Select
                            value={formData.consultoryId}
                            onValueChange={(value) => updateFormData('consultoryId', value)}
                          >
                            <SelectTrigger className="h-11">
                              <SelectValue placeholder="Seleccionar consultorio" />
                            </SelectTrigger>
                            <SelectContent>
                              {consultories.map((consultory: any) => (
                                <SelectItem key={consultory.id} value={consultory.id}>
                                  {consultory.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className={cn(
                            "text-sm p-3 rounded-md",
                            isPreselectedConsultory 
                              ? "text-blue-900 bg-blue-50 border border-blue-200" 
                              : "text-gray-900 bg-gray-50"
                          )}>
                            {isPreselectedConsultory && (
                              <div className="flex items-center gap-2 mb-1">
                                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                                <span className="text-xs font-medium text-blue-700">Pre-seleccionado desde agenda</span>
                              </div>
                            )}
                            {consultories.find((c: any) => c.id === formData.consultoryId)?.name || doctorConsultory?.name || 'No especificado'}
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="doctor" className="text-sm font-medium text-gray-700">
                          <Stethoscope className="h-4 w-4 inline mr-1" />
                          Médico <span className="text-red-500">*</span>
                        </Label>
                        {isEditing && !isPreselectedDoctor ? (
                          <Select
                            value={formData.doctorId}
                            onValueChange={(value) => updateFormData('doctorId', value)}
                          >
                            <SelectTrigger className="h-11">
                              <SelectValue placeholder="Seleccionar médico" />
                            </SelectTrigger>
                            <SelectContent>
                              {doctors.map((doctor: any) => (
                                <SelectItem key={doctor.id} value={doctor.id}>
                                  Dr. {doctor.firstName} {doctor.lastName}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        ) : (
                          <div className={cn(
                            "text-sm p-3 rounded-md",
                            isPreselectedDoctor 
                              ? "text-blue-900 bg-blue-50 border border-blue-200" 
                              : "text-gray-900 bg-gray-50"
                          )}>
                            {isPreselectedDoctor && (
                              <div className="flex items-center gap-2 mb-1">
                                <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                                <span className="text-xs font-medium text-blue-700">Pre-seleccionado desde agenda</span>
                              </div>
                            )}
                            {doctors.find((d: any) => d.id === formData.doctorId) ? 
                              `Dr. ${doctors.find((d: any) => d.id === formData.doctorId)?.firstName} ${doctors.find((d: any) => d.id === formData.doctorId)?.lastName}` :
                              'No especificado'
                            }
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Campo de paciente condicional */}
                    {showPatientField ? (
                      <div className="space-y-2">
                        <Label htmlFor="patient" className="text-sm font-medium text-gray-700">
                          Paciente <span className="text-red-500">*</span>
                        </Label>
                        <PatientSelector
                          patients={patients}
                          value={formData.patientId}
                          onChange={(patientId) => updateFormData('patientId', patientId)}
                          onPatientCreated={(newPatient) => {
                            // Agregar el nuevo paciente a la lista
                            setPatients(prev => [...prev, newPatient]);
                            toast.success(`Paciente ${newPatient.firstName} ${newPatient.lastName} creado y seleccionado`);
                          }}
                          disabled={!isEditing}
                          placeholder="Buscar y seleccionar paciente..."
                          autoFocus={isEditing}
                          returnUrl={`/dashboard/assistant/agenda/appointment${isNew ? '/new' : `/${appointmentId}`}${searchParams.toString() ? `?${searchParams.toString()}` : ''}`}
                        />
                      </div>
                    ) : (
                      <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-blue-600" />
                          <span className="text-sm text-blue-800 font-medium">
                            Esta actividad no requiere paciente
                          </span>
                        </div>
                        <p className="text-xs text-blue-600 mt-1">
                          {selectedActivityType.category === 'administrative' 
                            ? 'Actividad administrativa interna del consultorio'
                            : selectedActivityType.category === 'personal'
                            ? 'Actividad personal (vacaciones, tiempo libre)'
                            : 'Actividad que no involucra atención médica directa'
                          }
                        </p>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Programación */}
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Clock className="h-5 w-5 text-blue-600" />
                    Programación
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {selectedActivityType.respectsSchedule === false && (
                      <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg mb-4">
                        <div className="flex items-center gap-2">
                          <Clock className="h-4 w-4 text-yellow-600" />
                          <span className="text-sm text-yellow-800 font-medium">
                            Horario Libre
                          </span>
                        </div>
                        <p className="text-xs text-yellow-700 mt-1">
                          Esta actividad no está sujeta a horarios configurados
                        </p>
                      </div>
                    )}
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="date" className="text-sm font-medium text-gray-700">
                          <Calendar className="h-4 w-4 inline mr-1" />
                          Fecha <span className="text-red-500">*</span>
                        </Label>
                        <DatePicker
                          value={formData.scheduledDate}
                          onChange={(date) => updateFormData('scheduledDate', date)}
                          disabled={!isEditing}
                          placeholder="Seleccionar fecha de la cita"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="time" className="text-sm font-medium text-gray-700">
                          Hora <span className="text-red-500">*</span>
                        </Label>
                        <TimePicker
                          value={formData.startTime}
                          onChange={(time) => updateFormData('startTime', time)}
                          disabled={!isEditing}
                        />
                      </div>
                    </div>

                    <div>
                      <DurationPicker
                        value={formData.duration}
                        onChange={(duration) => updateFormData('duration', duration)}
                        disabled={!isEditing}
                      />
                      {selectedActivityType.duration && (
                        <p className="text-xs text-gray-600 mt-1">
                          Duración sugerida: {selectedActivityType.duration} minutos
                        </p>
                      )}
                    </div>

                    {/* Solo mostrar precio para actividades médicas con paciente */}
                    {showMedicalFields && (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label htmlFor="price" className="text-sm font-medium text-gray-700">
                            <DollarSign className="h-4 w-4 inline mr-1" />
                            Precio estimado
                          </Label>
                          {isEditing ? (
                            <div className="flex gap-2">
                              <Select
                                value={formData.currency}
                                onValueChange={(value) => updateFormData('currency', value)}
                              >
                                <SelectTrigger className="w-24 h-11">
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="GTQ">GTQ</SelectItem>
                                  <SelectItem value="USD">USD</SelectItem>
                                </SelectContent>
                              </Select>
                              <Input
                                type="number"
                                step="0.01"
                                value={formData.estimatedPrice}
                                onChange={(e) => updateFormData('estimatedPrice', e.target.value)}
                                placeholder="0.00"
                                className="flex-1 h-11"
                              />
                            </div>
                          ) : (
                            <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md">
                              {formData.currency} {formData.estimatedPrice || '0.00'}
                            </p>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Información Médica - Solo para actividades con paciente */}
              {showPatientField && (
                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="h-5 w-5 text-blue-600" />
                      Información Médica
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="chiefComplaint" className="text-sm font-medium text-gray-700">
                          Motivo de consulta
                        </Label>
                        {isEditing ? (
                          <Textarea
                            id="chiefComplaint"
                            value={formData.chiefComplaint}
                            onChange={(e) => updateFormData('chiefComplaint', e.target.value)}
                            placeholder="Describa el motivo principal de la consulta..."
                            rows={3}
                            className="resize-none"
                          />
                        ) : (
                          <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md min-h-[80px]">
                            {formData.chiefComplaint || 'No especificado'}
                          </p>
                        )}
                      </div>

                      {!isNew && (
                        <div className="space-y-2">
                          <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                            Estado de la cita
                          </Label>
                          {isEditing ? (
                            <Select
                              value={formData.status}
                              onValueChange={(value) => updateFormData('status', value)}
                            >
                              <SelectTrigger className="h-11">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="scheduled">📅 Programada</SelectItem>
                                <SelectItem value="pending_confirmation">⚠️ Pendiente de Confirmación</SelectItem>
                                <SelectItem value="confirmed">✅ Confirmada</SelectItem>
                                <SelectItem value="checked_in">👤 Paciente llegó</SelectItem>
                                <SelectItem value="in_progress">🟢 En consulta</SelectItem>
                                <SelectItem value="completed">✅ Completada</SelectItem>
                                <SelectItem value="cancelled">❌ Cancelada</SelectItem>
                                <SelectItem value="no_show">❌ No asistió</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : (
                            <p className="text-sm text-gray-900 p-3 bg-gray-50 rounded-md">
                              {formData.status === 'scheduled' ? '📅 Programada' :
                               formData.status === 'pending_confirmation' ? '⚠️ Pendiente de Confirmación' :
                               formData.status === 'confirmed' ? '✅ Confirmada' :
                               formData.status === 'checked_in' ? '👤 Paciente llegó' :
                               formData.status === 'in_progress' ? '🟢 En consulta' :
                               formData.status === 'completed' ? '✅ Completada' :
                               formData.status === 'cancelled' ? '❌ Cancelada' :
                               formData.status === 'no_show' ? '❌ No asistió' : '❌ Cancelada'}
                            </p>
                          )}
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          );
        })()}

        {/* Botones de acción */}
        {isEditing ? (
          <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-100">
            <Button variant="outline" onClick={() => router.push('/dashboard/assistant/agenda')}>
              Cancelar
            </Button>
            <Button 
              onClick={handleSave} 
              disabled={processing}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              {processing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Guardando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {isNew ? 'Crear Cita' : 'Guardar Cambios'}
                </>
              )}
            </Button>
          </div>
        ) : (
          <div className="flex justify-end gap-3 pt-6 mt-6 border-t border-gray-100">
            <Button 
              onClick={() => router.push(`/dashboard/assistant/agenda/appointment/${appointmentId}?mode=edit`)}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}