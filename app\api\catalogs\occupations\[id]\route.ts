import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { occupations } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener ocupación por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const occupation = await db
      .select()
      .from(occupations)
      .where(eq(occupations.id, parseInt(id)))
      .limit(1);

    if (occupation.length === 0) {
      return NextResponse.json({ error: 'Ocupación no encontrada' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: occupation[0] 
    });

  } catch (error) {
    console.error('Error fetching occupation:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar ocupación
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar ocupaciones.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, category, isActive } = body;

    // Validar datos requeridos
    if (!name) {
      return NextResponse.json({ 
        error: 'El nombre es requerido' 
      }, { status: 400 });
    }

    // Verificar que la ocupación existe
    const existingOccupation = await db
      .select()
      .from(occupations)
      .where(eq(occupations.id, parseInt(id)))
      .limit(1);

    if (existingOccupation.length === 0) {
      return NextResponse.json({ error: 'Ocupación no encontrada' }, { status: 404 });
    }

    // Actualizar ocupación
    const [updatedOccupation] = await db
      .update(occupations)
      .set({
        name,
        category: category || null,
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(occupations.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedOccupation,
      message: 'Ocupación actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error updating occupation:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar ocupación
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar ocupaciones.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que la ocupación existe
    const existingOccupation = await db
      .select()
      .from(occupations)
      .where(eq(occupations.id, parseInt(id)))
      .limit(1);

    if (existingOccupation.length === 0) {
      return NextResponse.json({ error: 'Ocupación no encontrada' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(occupations)
        .where(eq(occupations.id, parseInt(id)));

      return NextResponse.json({ 
        success: true, 
        message: 'Ocupación eliminada físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedOccupation] = await db
        .update(occupations)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(occupations.id, parseInt(id)))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedOccupation,
        message: 'Ocupación desactivada exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting occupation:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}