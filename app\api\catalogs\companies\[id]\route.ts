import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { companies } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener empresa por ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = params;

    const result = await db
      .select()
      .from(companies)
      .where(eq(companies.id, id))
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json(
        { error: 'Empresa no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: result[0],
    });

  } catch (error) {
    console.error('Error fetching company:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar empresa (lógica o física)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'logical';

    // Verificar que la empresa existe
    const existingCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.id, id))
      .limit(1);

    if (existingCompany.length === 0) {
      return NextResponse.json(
        { error: 'Empresa no encontrada' },
        { status: 404 }
      );
    }

    if (type === 'physical') {
      // Eliminación física
      await db.delete(companies).where(eq(companies.id, id));
      
      return NextResponse.json({
        success: true,
        message: 'Empresa eliminada físicamente',
      });
    } else {
      // Eliminación lógica
      await db
        .update(companies)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(companies.id, id));

      return NextResponse.json({
        success: true,
        message: 'Empresa desactivada exitosamente',
      });
    }

  } catch (error) {
    console.error('Error deleting company:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}