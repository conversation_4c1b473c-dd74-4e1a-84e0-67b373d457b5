import { Card, CardContent } from "@/components/ui/card";
import { Star, Quote } from "lucide-react";
import Image from "next/image";

const testimonials = [
  {
    id: 1,
    name: "<PERSON>",
    role: "<PERSON><PERSON> de <PERSON>fía (4 años)",
    content: "El seguimiento que han dado a mi hija ha sido excepcional. La doctora siempre está disponible para resolver dudas y el sistema digital facilita mucho el acceso a la información médica.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b47c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  },
  {
    id: 2,
    name: "<PERSON>",
    role: "<PERSON><PERSON> de gemelos (2 años)",
    content: "Tener gemelos es todo un reto, pero el equipo de Mundo Pediatra nos ha acompañado desde el primer día. Su experiencia en neonatología fue fundamental para nosotros.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  },
  {
    id: 3,
    name: "Ana Gabriela Vega",
    role: "Madre de Diego (6 años)",
    content: "Lo que más me gusta es la atención personalizada y cómo explican todo de manera clara. Mi hijo ya no tiene miedo de ir al doctor gracias a su trato tan amable.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  },
  {
    id: 4,
    name: "Carlos Eduardo Ruiz",
    role: "Padre de Valentina (8 años)",
    content: "El sistema de citas online y la posibilidad de consultar el historial médico desde casa nos ha facilitado mucho la vida. Recomiendo Mundo Pediatra al 100%.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  },
  {
    id: 5,
    name: "Isabel Morales",
    role: "Madre de Mateo (1 año)",
    content: "Como madre primeriza, tenía muchas dudas. El equipo me tranquilizó y me dio toda la información necesaria para cuidar bien a mi bebé. Estoy muy agradecida.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1544005313-94ddf0286df2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  },
  {
    id: 6,
    name: "Fernando García",
    role: "Padre de Emma (5 años)",
    content: "La dedicación y profesionalismo del equipo es admirable. Siempre se toman el tiempo necesario para cada consulta y nunca te hacen sentir apurado.",
    rating: 5,
    image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
  }
];

function StarRating({ rating }: { rating: number }) {
  return (
    <div className="flex gap-1">
      {[...Array(5)].map((_, i) => (
        <Star
          key={i}
          className={`h-4 w-4 ${
            i < rating ? 'fill-yellow-400 text-yellow-400' : 'text-gray-300'
          }`}
        />
      ))}
    </div>
  );
}

export default function TestimonialsSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-green-50/30 to-white">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Testimoniales
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Lo que dicen los padres que confían en nosotros para el cuidado de sus hijos
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3">
          {testimonials.map((testimonial) => (
            <Card key={testimonial.id} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-6">
                <div className="mb-4">
                  <Quote className="h-8 w-8 text-primary/30" />
                </div>
                
                <div className="mb-4">
                  <StarRating rating={testimonial.rating} />
                </div>

                <blockquote className="text-muted-foreground mb-6 leading-relaxed">
                  "{testimonial.content}"
                </blockquote>

                <div className="flex items-center gap-3">
                  <div className="h-10 w-10 overflow-hidden rounded-full ring-2 ring-primary/10">
                    <Image
                      src={testimonial.image}
                      alt={testimonial.name}
                      width={40}
                      height={40}
                      className="h-full w-full object-cover"
                    />
                  </div>
                  <div>
                    <div className="font-semibold text-foreground text-sm">
                      {testimonial.name}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {testimonial.role}
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center rounded-full bg-primary/10 px-6 py-3 text-sm font-medium text-primary">
            <Star className="mr-2 h-4 w-4 fill-current" />
            <span>4.9/5 estrellas basado en más de 500 reseñas</span>
          </div>
        </div>
      </div>
    </section>
  );
} 