'use client';

import { useState, useEffect } from 'react';

interface ConsultoryInfo {
  id: string;
  name: string;
  phone: string;
  email: string;
  address: string;
  logoUrl: string;
  logoPublicId: string;
  regionalSettings: any;
}

interface UseConsultoryInfoReturn {
  consultory: ConsultoryInfo | null;
  isLoading: boolean;
  error: string | null;
}

export function useConsultoryInfo(): UseConsultoryInfoReturn {
  const [consultory, setConsultory] = useState<ConsultoryInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchConsultoryInfo() {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch('/api/consultories');
        
        if (!response.ok) {
          throw new Error('Error al obtener información del consultorio');
        }

        const result = await response.json();
        
        if (!result.success) {
          throw new Error(result.error || 'Error desconocido');
        }

        setConsultory(result.data);
      } catch (err) {
        console.error('Error fetching consultory info:', err);
        setError(err instanceof Error ? err.message : 'Error desconocido');
        
        // Fallback data in case of error
        setConsultory({
          id: 'fallback',
          name: 'Consultorio Médico',
          phone: '+502 2234-5678',
          email: '<EMAIL>',
          address: 'Dirección no disponible',
          logoUrl: '',
          logoPublicId: '',
          regionalSettings: null
        });
      } finally {
        setIsLoading(false);
      }
    }

    fetchConsultoryInfo();
  }, []);

  return { consultory, isLoading, error };
}