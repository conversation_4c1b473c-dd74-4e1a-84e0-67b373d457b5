import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { pathologicalHistory } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener antecedente patológico por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;

    const pathologicalHistoryData = await db.select()
      .from(pathologicalHistory)
      .where(eq(pathologicalHistory.id, id))
      .limit(1);

    if (pathologicalHistoryData.length === 0) {
      return NextResponse.json({ error: 'Antecedente patológico no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: pathologicalHistoryData[0] 
    });

  } catch (error) {
    console.error('Error obteniendo antecedente patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar antecedente patológico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para eliminar antecedentes patológicos' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical';

    // Verificar que el antecedente patológico existe
    const existingHistory = await db.select()
      .from(pathologicalHistory)
      .where(eq(pathologicalHistory.id, id))
      .limit(1);

    if (existingHistory.length === 0) {
      return NextResponse.json({ error: 'Antecedente patológico no encontrado' }, { status: 404 });
    }

    const pathologicalHistoryData = existingHistory[0];

    // Validación: No permitir eliminar antecedente activo SOLO para eliminación lógica
    if (pathologicalHistoryData.isActive && deleteType !== 'physical') {
      return NextResponse.json({ 
        error: 'No se puede eliminar un antecedente patológico activo', 
        suggestion: 'Desactive el antecedente patológico antes de eliminarlo',
        code: 'ACTIVE_PATHOLOGICAL_HISTORY'
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física
      await db.delete(pathologicalHistory)
        .where(eq(pathologicalHistory.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Antecedente patológico eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedHistory] = await db.update(pathologicalHistory)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(pathologicalHistory.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedHistory,
        message: 'Antecedente patológico desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error eliminando antecedente patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}