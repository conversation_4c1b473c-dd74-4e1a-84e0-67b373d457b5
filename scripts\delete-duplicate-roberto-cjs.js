const { config } = require('dotenv');
const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const { user, userRoles, guardianPatientRelations, appointments } = require('../db/schema.js');
const { eq, and } = require('drizzle-orm');

config({ path: '.env.local' });

const sql = postgres(process.env.DATABASE_URL);
const db = drizzle(sql);

async function deleteDuplicateRoberto() {
  console.log('🔍 Buscando pacientes con nombre "<PERSON>"...\n');
  
  try {
    // Buscar todos los usuarios con ese nombre
    const duplicates = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        documentNumber: user.documentNumber,
        createdAt: user.createdAt,
        role: userRoles.role,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.firstName, '<PERSON>'),
          eq(user.lastName, '<PERSON>varado <PERSON>'),
          eq(userRoles.role, 'patient')
        )
      );

    console.log(`📊 Encontrados ${duplicates.length} registros:\n`);

    for (const dup of duplicates) {
      console.log('─'.repeat(60));
      console.log(`ID: ${dup.id}`);
      console.log(`Nombre: ${dup.firstName} ${dup.lastName}`);
      console.log(`Email: ${dup.email}`);
      console.log(`Documento: ${dup.documentNumber}`);
      console.log(`Creado: ${dup.createdAt}`);
    }

    if (duplicates.length === 0) {
      console.log('✅ No se encontraron registros duplicados.');
      process.exit(0);
    }

    console.log('\n⚠️  ¿Deseas eliminar TODOS estos registros? (s/n)');
    
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    process.stdin.on('data', async function (text) {
      const answer = text.trim().toLowerCase();
      
      if (answer === 's' || answer === 'si') {
        console.log('\n🗑️  Eliminando registros...');
        
        for (const dup of duplicates) {
          try {
            // Eliminar relaciones de guardián
            const deletedRelations = await db
              .delete(guardianPatientRelations)
              .where(eq(guardianPatientRelations.patientId, dup.id))
              .returning();
            
            if (deletedRelations.length > 0) {
              console.log(`   - Eliminadas ${deletedRelations.length} relaciones de guardián para ${dup.id}`);
            }
            
            // Verificar si tiene citas (no eliminar si tiene citas)
            const hasAppointments = await db
              .select({ id: appointments.id })
              .from(appointments)
              .where(eq(appointments.patientId, dup.id))
              .limit(1);
            
            if (hasAppointments.length > 0) {
              console.log(`   ⚠️  El paciente ${dup.id} tiene citas. No se puede eliminar.`);
              continue;
            }
            
            // Eliminar rol
            await db
              .delete(userRoles)
              .where(eq(userRoles.userId, dup.id));
            
            // Eliminar usuario
            await db
              .delete(user)
              .where(eq(user.id, dup.id));
            
            console.log(`   ✅ Eliminado paciente ${dup.id}`);
            
          } catch (error) {
            console.error(`   ❌ Error eliminando ${dup.id}:`, error.message);
          }
        }
        
        console.log('\n✅ Proceso completado');
      } else {
        console.log('❌ Operación cancelada');
      }
      
      await sql.end();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
    await sql.end();
    process.exit(1);
  }
}

deleteDuplicateRoberto();