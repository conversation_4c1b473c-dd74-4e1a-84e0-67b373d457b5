import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, notifications, user } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { validateShortCode } from '@/lib/short-codes';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { shortCode, appointmentId } = body;

    // Validar que se proporcione al menos uno de los dos
    if (!shortCode && !appointmentId) {
      return NextResponse.json({
        success: false,
        error: 'Se requiere shortCode o appointmentId'
      }, { status: 400 });
    }

    // Si se proporciona shortCode, validar formato
    if (shortCode && !validateShortCode(shortCode)) {
      return NextResponse.json({
        success: false,
        error: 'Código inválido. Debe tener el formato SGC1234567'
      }, { status: 400 });
    }

    // Buscar la cita
    let whereClause;
    if (shortCode) {
      whereClause = eq(appointments.shortCode, shortCode);
    } else {
      whereClause = eq(appointments.id, appointmentId);
    }

    const appointment = await db
      .select({
        id: appointments.id,
        shortCode: appointments.shortCode,
        status: appointments.status,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        checkedInAt: appointments.checkedInAt,
        patientFirstName: appointments.patientFirstName,
        patientLastName: appointments.patientLastName,
        doctorFirstName: appointments.doctorFirstName,
        doctorLastName: appointments.doctorLastName,
        serviceName: appointments.serviceName,
        consultoryName: appointments.consultoryName,
      })
      .from(appointments)
      .where(whereClause)
      .limit(1);

    if (!appointment.length) {
      return NextResponse.json({
        success: false,
        error: 'Cita no encontrada'
      }, { status: 404 });
    }

    const appointmentData = appointment[0];

    // Verificar que la cita esté en estado válido para check-in
    if (!['confirmed', 'pending_confirmation'].includes(appointmentData.status)) {
      if (appointmentData.status === 'checked_in') {
        return NextResponse.json({
          success: false,
          error: 'Ya registraste tu llegada anteriormente'
        }, { status: 400 });
      } else if (appointmentData.status === 'completed') {
        return NextResponse.json({
          success: false,
          error: 'Esta cita ya fue completada'
        }, { status: 400 });
      } else if (appointmentData.status === 'cancelled') {
        return NextResponse.json({
          success: false,
          error: 'Esta cita fue cancelada'
        }, { status: 400 });
      } else {
        return NextResponse.json({
          success: false,
          error: 'La cita no está en un estado válido para check-in'
        }, { status: 400 });
      }
    }

    // Verificar que no sea muy temprano (más de 2 horas antes)
    const appointmentTime = new Date(appointmentData.startTime);
    const now = new Date();
    const twoHoursBefore = new Date(appointmentTime.getTime() - (2 * 60 * 60 * 1000));
    
    if (now < twoHoursBefore) {
      return NextResponse.json({
        success: false,
        error: 'Es muy temprano para hacer check-in. Puedes registrar tu llegada hasta 2 horas antes de tu cita.'
      }, { status: 400 });
    }

    // Actualizar el estado de la cita a 'checked_in'
    const updatedAppointment = await db
      .update(appointments)
      .set({
        status: 'checked_in',
        checkedInAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentData.id))
      .returning();

    if (!updatedAppointment.length) {
      return NextResponse.json({
        success: false,
        error: 'Error al registrar check-in'
      }, { status: 500 });
    }

    // Crear notificación para el doctor
    try {
      await db.insert(notifications).values({
        id: crypto.randomUUID(),
        userId: appointmentData.doctorId,
        type: 'appointment_checkin',
        title: 'Paciente en check-in',
        message: `${appointmentData.patientFirstName} ${appointmentData.patientLastName} ha llegado para su cita de ${appointmentData.serviceName}`,
        data: {
          appointmentId: appointmentData.id,
          patientName: `${appointmentData.patientFirstName} ${appointmentData.patientLastName}`,
          service: appointmentData.serviceName,
          checkedInAt: new Date().toISOString()
        },
        isRead: false,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    } catch (notificationError) {
      console.error('Error creating check-in notification:', notificationError);
      // No fallar el check-in por error de notificación
    }

    console.log(`✅ CHECK-IN EXITOSO - Paciente: ${appointmentData.patientFirstName} ${appointmentData.patientLastName}, Código: ${appointmentData.shortCode}`);

    return NextResponse.json({
      success: true,
      message: 'Check-in realizado exitosamente',
      data: {
        appointmentId: appointmentData.id,
        shortCode: appointmentData.shortCode,
        patientName: `${appointmentData.patientFirstName} ${appointmentData.patientLastName}`,
        doctorName: `Dr. ${appointmentData.doctorFirstName} ${appointmentData.doctorLastName}`,
        service: appointmentData.serviceName,
        consultory: appointmentData.consultoryName,
        checkedInAt: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Error en check-in público:', error);
    return NextResponse.json({
      success: false,
      error: 'Error interno del servidor'
    }, { status: 500 });
  }
}