// Script para verificar cómo se manejan las fechas y zonas horarias

console.log('🕒 Verificando manejo de fechas y zonas horarias...\n');

const now = new Date();
console.log('new Date():', now);
console.log('now.toISOString():', now.toISOString());
console.log('now.toDateString():', now.toDateString());
console.log('now.getTimezoneOffset():', now.getTimezoneOffset(), 'minutos');

// Simular lo que hace el appointment modal
const scheduledDate = new Date();
const [hours, minutes] = ['16', '00']; // Ejemplo: 4:00 PM
const startDateTime = new Date(scheduledDate);
startDateTime.setHours(parseInt(hours), parseInt(minutes), 0, 0);

console.log('\n📅 Simulación de creación de cita:');
console.log('scheduledDate:', scheduledDate);
console.log('scheduledDate.toISOString():', scheduledDate.toISOString());
console.log('startDateTime:', startDateTime);
console.log('startDateTime.toISOString():', startDateTime.toISOString());

// Verificar cómo se vería en formato de fecha
console.log('\n📋 Formatos:');
console.log('scheduledDate para DB:', scheduledDate.toISOString().split('T')[0]);
console.log('startDateTime para DB:', startDateTime.toISOString());

process.exit(0);