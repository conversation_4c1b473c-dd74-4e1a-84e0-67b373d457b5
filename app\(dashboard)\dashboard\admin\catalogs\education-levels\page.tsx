'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { 
  Plus,
  Search,
  Filter,
  RefreshCw,
  GraduationCap,
  Edit,
  Trash2,
  MoreHorizontal,
  Download,
  ArrowLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  ToggleLeft,
  ToggleRight,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface EducationLevel {
  id: string;
  name: string;
  order: number;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function EducationLevelsPage() {
  const router = useRouter();
  const [educationLevels, setEducationLevels] = useState<EducationLevel[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [selectedLevels, setSelectedLevels] = useState<string[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingLevel, setEditingLevel] = useState<EducationLevel | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [viewingLevel, setViewingLevel] = useState<EducationLevel | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingLevel, setDeletingLevel] = useState<EducationLevel | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('order');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    order: '',
    description: '',
    isActive: true
  });
  
  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    name: '',
    order: ''
  });

  // Fetch education levels
  const fetchEducationLevels = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(activeFilter !== 'all' && { active: (activeFilter === 'active').toString() })
      });

      const response = await fetch(`/api/catalogs/education-levels?${params}`);
      if (!response.ok) throw new Error('Error fetching education levels');
      
      const data = await response.json();
      setEducationLevels(data.data || []);
      
      // Actualizar información de paginación
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages || 1);
        setTotalCount(data.pagination.total || 0);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar los niveles de educación');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchEducationLevels();
  };

  useEffect(() => {
    fetchEducationLevels();
  }, [page, limit, searchTerm, activeFilter]);

  useEffect(() => {
    // Resetear a la primera página cuando cambien los filtros
    if (page !== 1) {
      setPage(1);
    }
  }, [searchTerm, activeFilter]);

  // Handle create
  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const response = await fetch('/api/catalogs/education-levels', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          order: parseInt(formData.order)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error creating education level');
      }

      toast.success('Nivel de educación creado exitosamente');
      setIsCreateDialogOpen(false);
      resetForm();
      fetchEducationLevels();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  // Handle edit
  const handleEdit = async () => {
    if (!editingLevel) return;

    if (!validateForm()) {
      return;
    }

    try {
      const response = await fetch('/api/catalogs/education-levels', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingLevel.id,
          ...formData,
          order: parseInt(formData.order)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error updating education level');
      }

      toast.success('Nivel de educación actualizado exitosamente');
      setIsEditDialogOpen(false);
      setEditingLevel(null);
      resetForm();
      fetchEducationLevels();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      order: '',
      description: '',
      isActive: true
    });
    setFormErrors({
      name: '',
      order: ''
    });
  };

  // Form validation
  const validateForm = () => {
    const errors = { name: '', order: '' };
    let isValid = true;

    if (!formData.name || formData.name.trim() === '') {
      errors.name = 'El nombre del nivel de educación es requerido';
      isValid = false;
    }

    if (!formData.order || formData.order.trim() === '') {
      errors.order = 'El orden es requerido';
      isValid = false;
    } else {
      const orderNum = parseInt(formData.order);
      if (isNaN(orderNum) || orderNum < 0 || orderNum > 99) {
        errors.order = 'El orden debe ser un número entre 0 y 99';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const openEditDialog = (level: EducationLevel) => {
    setEditingLevel(level);
    setFormData({
      name: level.name || '',
      order: level.order?.toString() || '',
      description: level.description || '',
      isActive: level.isActive
    });
    setIsEditDialogOpen(true);
  };

  // Ver detalles del nivel
  const openViewDialog = (level: EducationLevel) => {
    setViewingLevel(level);
    setIsViewDialogOpen(true);
  };

  // Confirmar eliminación
  const openDeleteDialog = (level: EducationLevel) => {
    setDeletingLevel(level);
    setIsDeleteDialogOpen(true);
  };

  // Activar/Desactivar nivel
  const handleToggleStatus = async (level: EducationLevel) => {
    try {
      setIsProcessing(true);
      const response = await fetch('/api/catalogs/education-levels', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: level.id,
          name: level.name,
          order: level.order,
          description: level.description,
          isActive: !level.isActive
        })
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Error al cambiar estado');
      }

      toast.success(`Nivel ${!level.isActive ? 'activado' : 'desactivado'} exitosamente`);
      fetchEducationLevels();
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // Eliminar nivel (físico o lógico)
  const handleDelete = async (type: 'hard' | 'soft') => {
    if (!deletingLevel) return;

    try {
      setIsProcessing(true);
      const response = await fetch(`/api/catalogs/education-levels?id=${deletingLevel.id}&type=${type}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Error al eliminar nivel de educación');
      }

      toast.success(result.message);
      setIsDeleteDialogOpen(false);
      setDeletingLevel(null);
      fetchEducationLevels();
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // La paginación y filtrado ahora se maneja en el servidor
  const filteredLevels = educationLevels.sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'order':
        aValue = a.order;
        bValue = b.order;
        break;
      case 'isActive':
        aValue = a.isActive ? 1 : 0;
        bValue = b.isActive ? 1 : 0;
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  })

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="hover:bg-green-100 hover:text-green-600 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestión de Niveles de Educación</h1>
              <p className="text-gray-600 text-sm lg:text-base">Administra los niveles de educación del sistema</p>
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            disabled={refreshing}
            className="hover:bg-gray-50 transition-colors w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto" onClick={() => resetForm()}>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Nivel
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre o descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            <div>
              <Select value={activeFilter} onValueChange={(value: 'all' | 'active' | 'inactive') => setActiveFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="active">Activos</SelectItem>
                  <SelectItem value="inactive">Inactivos</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <GraduationCap className="h-5 w-5 text-blue-600" />
                Lista de Niveles de Educación
              </CardTitle>
              <Badge variant="outline" className="border-gray-300">
                {totalCount} niveles
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block overflow-x-auto">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Nivel
                        <SortIcon column="name" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('order')}
                    >
                      <div className="flex items-center gap-2">
                        Orden
                        <SortIcon column="order" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('isActive')}
                    >
                      <div className="flex items-center gap-2">
                        Estado
                        <SortIcon column="isActive" />
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700 text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-gray-600">Cargando niveles...</p>
                      </TableCell>
                    </TableRow>
                  ) : filteredLevels.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8">
                        <GraduationCap className="h-12 w-12 text-gray-400 mx-auto" />
                        <p className="mt-2 text-gray-600">No se encontraron niveles</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredLevels.map((level) => (
                      <TableRow key={level.id}>
                        <TableCell>
                          <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <GraduationCap className="h-4 w-4 text-blue-400" />
                            <span className="font-medium">{level.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{level.order}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={level.isActive ? "default" : "secondary"}>
                            {level.isActive ? "Activo" : "Inactivo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openViewDialog(level)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Ver detalles
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openEditDialog(level)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleToggleStatus(level)}
                                disabled={isProcessing}
                                className={level.isActive ? "text-orange-600" : "text-green-600"}
                              >
                                {level.isActive ? (
                                  <>
                                    <ToggleLeft className="h-4 w-4 mr-2" />
                                    Desactivar
                                  </>
                                ) : (
                                  <>
                                    <ToggleRight className="h-4 w-4 mr-2" />
                                    Activar
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600" 
                                onClick={() => openDeleteDialog(level)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Eliminar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Cards View */}
          <div className="lg:hidden space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Cargando...</p>
              </div>
            ) : filteredLevels.length === 0 ? (
              <div className="text-center py-8">
                <GraduationCap className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="mt-2 text-gray-600">No se encontraron niveles</p>
              </div>
            ) : (
              filteredLevels.map((level) => (
                <Card key={level.id} className="hover:shadow-md transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <Checkbox className="mt-1 border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <GraduationCap className="h-4 w-4 text-blue-400" />
                            <span className="font-medium">{level.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">Orden:</span>
                            <Badge variant="outline" className="text-xs">{level.order}</Badge>
                          </div>
                          <Badge variant={level.isActive ? "default" : "secondary"} className="text-xs">
                            {level.isActive ? "Activo" : "Inactivo"}
                          </Badge>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openViewDialog(level)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openEditDialog(level)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(level)}
                            disabled={isProcessing}
                            className={level.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            {level.isActive ? (
                              <>
                                <ToggleLeft className="h-4 w-4 mr-2" />
                                Desactivar
                              </>
                            ) : (
                              <>
                                <ToggleRight className="h-4 w-4 mr-2" />
                                Activar
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600" 
                            onClick={() => openDeleteDialog(level)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, totalCount)} de {totalCount} niveles
            </div>
            <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}

      {/* Dialog Crear */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5 text-blue-600" />
              Crear Nivel de Educación
            </DialogTitle>
            <DialogDescription>
              Completa la información para crear un nuevo nivel de educación.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="create-name">Nombre *</Label>
              <Input
                id="create-name"
                placeholder="Ej: Primaria, Secundaria, Universidad..."
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className={formErrors.name ? 'border-red-500' : ''}
              />
              {formErrors.name && <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>}
            </div>
            <div>
              <Label htmlFor="create-order">Orden *</Label>
              <Input
                id="create-order"
                type="number"
                placeholder="0-99"
                min="0"
                max="99"
                value={formData.order}
                onChange={(e) => setFormData({...formData, order: e.target.value})}
                className={formErrors.order ? 'border-red-500' : ''}
              />
              {formErrors.order && <p className="text-sm text-red-500 mt-1">{formErrors.order}</p>}
              <p className="text-xs text-gray-500 mt-1">Orden jerárquico (0 = más bajo, 99 = más alto)</p>
            </div>
            <div>
              <Label htmlFor="create-description">Descripción</Label>
              <Textarea
                id="create-description"
                placeholder="Descripción opcional..."
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="create-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
              />
              <Label htmlFor="create-isActive">Activo</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreate}>
              Crear Nivel
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Editar */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5 text-blue-600" />
              Editar Nivel de Educación
            </DialogTitle>
            <DialogDescription>
              Modifica la información del nivel de educación.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Nombre *</Label>
              <Input
                id="edit-name"
                placeholder="Ej: Primaria, Secundaria, Universidad..."
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className={formErrors.name ? 'border-red-500' : ''}
              />
              {formErrors.name && <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>}
            </div>
            <div>
              <Label htmlFor="edit-order">Orden *</Label>
              <Input
                id="edit-order"
                type="number"
                placeholder="0-99"
                min="0"
                max="99"
                value={formData.order}
                onChange={(e) => setFormData({...formData, order: e.target.value})}
                className={formErrors.order ? 'border-red-500' : ''}
              />
              {formErrors.order && <p className="text-sm text-red-500 mt-1">{formErrors.order}</p>}
              <p className="text-xs text-gray-500 mt-1">Orden jerárquico (0 = más bajo, 99 = más alto)</p>
            </div>
            <div>
              <Label htmlFor="edit-description">Descripción</Label>
              <Textarea
                id="edit-description"
                placeholder="Descripción opcional..."
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
              />
              <Label htmlFor="edit-isActive">Activo</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit}>
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Ver Detalles */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <GraduationCap className="h-5 w-5 text-blue-600" />
              Detalles del Nivel de Educación
            </DialogTitle>
          </DialogHeader>
          {viewingLevel && (
            <div className="space-y-4">
              <div className="space-y-3">
                <div>
                  <Label className="text-gray-500 text-sm">Nombre</Label>
                  <p className="font-medium text-gray-900">{viewingLevel.name}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Orden</Label>
                  <p className="font-medium text-gray-900">{viewingLevel.order}</p>
                </div>
                {viewingLevel.description && (
                  <div>
                    <Label className="text-gray-500 text-sm">Descripción</Label>
                    <p className="font-medium text-gray-900">{viewingLevel.description}</p>
                  </div>
                )}
                <div>
                  <Label className="text-gray-500 text-sm">Estado</Label>
                  <div className="mt-1">
                    <Badge variant={viewingLevel.isActive ? "default" : "secondary"}>
                      {viewingLevel.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Fecha de Creación</Label>
                  <p className="text-sm text-gray-600">
                    {new Date(viewingLevel.createdAt).toLocaleDateString('es-ES', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Última Actualización</Label>
                  <p className="text-sm text-gray-600">
                    {new Date(viewingLevel.updatedAt).toLocaleDateString('es-ES', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Confirmar Eliminación */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Selecciona el tipo de eliminación para el nivel {deletingLevel?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
              <h4 className="font-semibold text-amber-800 mb-2">Eliminación Lógica (Recomendado)</h4>
              <p className="text-sm text-amber-700">
                El nivel se marcará como inactivo pero se mantendrá en la base de datos para preservar la integridad de los datos históricos.
              </p>
            </div>
            
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Eliminación Física (Permanente)</h4>
              <p className="text-sm text-red-700">
                El nivel se eliminará completamente de la base de datos. Esta acción no se puede deshacer.
              </p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              variant="secondary"
              onClick={() => handleDelete('soft')}
              disabled={isProcessing}
            >
              {isProcessing ? 'Procesando...' : 'Eliminación Lógica'}
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDelete('hard')}
              disabled={isProcessing}
            >
              {isProcessing ? 'Procesando...' : 'Eliminación Física'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}