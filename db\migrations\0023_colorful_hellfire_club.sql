ALTER TABLE "appointments" ADD COLUMN "shortCode" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "emailCaptured" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "confirmedAt" timestamp;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "confirmedVia" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "color_palette" jsonb;--> statement-breakpoint
CREATE UNIQUE INDEX "appointments_shortcode_idx" ON "appointments" USING btree ("shortCode");--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_shortCode_unique" UNIQUE("shortCode");