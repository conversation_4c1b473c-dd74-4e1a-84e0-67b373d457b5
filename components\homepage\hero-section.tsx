import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  Calendar, 
  Baby,
  Phone, 
  BookOpen, 
  Users, 
  Heart, 
  Shield,
  Clock,
  MessageSquare,
  Stethoscope,
  Activity,
  FileText,
  <PERSON>rkles,
  ArrowRight,
  Star,
  <PERSON><PERSON>,
  <PERSON>r<PERSON>he<PERSON>
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function HeroSection() {
  return (
    <section className="py-8 bg-gradient-to-br from-[#ADB6CA]/20 via-[#FCEEA8]/30 to-[#F8E59A]/40">
      {/* Main Content */}
      <div className="px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 max-w-7xl mx-auto">
          
          {/* Left Side - Main Card */}
          <div className="lg:col-span-5">
            <Card className="bg-gradient-to-br from-[#3D4E80] to-[#3D4E80]/90 border-0 hover:shadow-2xl transition-all duration-300 h-full relative overflow-hidden group">
              <div className="absolute top-0 right-0 w-64 h-64 bg-[#FCEEA8] rounded-full blur-3xl opacity-30 -mr-32 -mt-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-[#ADB6CA] rounded-full blur-2xl opacity-40 -ml-24 -mb-24"></div>
              <CardContent className="p-12 h-full flex flex-col justify-center text-white relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="transform group-hover:rotate-6 transition-transform">
                    <Baby className="h-20 w-20 text-[#FCEEA8]" />
                  </div>
                  <div className="transform group-hover:rotate-12 transition-transform">
                    <Sparkles className="h-12 w-12 text-[#F8E59A]" />
                  </div>
                </div>
                <h1 className="text-5xl font-bold leading-tight mb-6">
                  Cuidamos lo más
                  <br />
                  <span className="text-[#FCEEA8]">importante de tu vida</span>
                </h1>
                
                <p className="text-white/90 text-lg mb-8 max-w-lg leading-relaxed">
                  Brindamos atención pediátrica especializada con amor, tecnología y un toque de magia para acompañar a tus pequeños en cada etapa de su desarrollo. Aquí, cada sonrisa es parte de la medicina.
                </p>
                
                <Button 
                  size="lg" 
                  className="bg-[#FCEEA8] text-[#3D4E80] hover:bg-[#F8E59A] hover:text-[#3D4E80] rounded-full px-8 py-4 text-lg w-fit font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all"
                  asChild
                >
                  <Link href="/dashboard">
                    Haz Tu Cita
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Right Side - Bento Grid */}
          <div className="lg:col-span-7">
            <div className="grid grid-cols-2 gap-4 h-full">
              
              {/* Blog */}
              <Link href="/blog">
                <Card className="bg-gradient-to-br from-[#ADB6CA] to-[#ADB6CA]/80 border-0 hover:shadow-xl transition-all duration-300 group cursor-pointer h-48 relative overflow-hidden">
                  <div className="absolute top-0 right-0 opacity-10">
                    <BookOpen className="h-32 w-32 -mr-8 -mt-8 text-[#3D4E80]" />
                  </div>
                  <CardContent className="p-6 h-full flex flex-col justify-center text-white relative z-10">
                    <div className="w-16 h-16 bg-[#3D4E80]/20 backdrop-blur rounded-xl flex items-center justify-center mb-4 transform group-hover:rotate-6 transition-transform">
                      <BookOpen className="h-10 w-10 text-[#3D4E80]" />
                    </div>
                    <h3 className="text-xl font-bold mb-2 text-[#3D4E80]">Blog de Salud</h3>
                    <p className="text-[#3D4E80]/80 text-sm">
                      Consejos y artículos
                    </p>
                    <div className="flex justify-between items-center mt-auto">
                      <span className="text-sm font-medium text-[#3D4E80]">Leer más</span>
                      <div className="w-8 h-8 bg-[#3D4E80]/20 backdrop-blur rounded-full flex items-center justify-center group-hover:scale-110 group-hover:bg-[#3D4E80]/30 transition-all">
                        <ArrowRight className="h-4 w-4 text-[#3D4E80]" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              {/* Check-In */}
              <Link href="/check-in">
                <Card className="bg-gradient-to-br from-[#50bed2] to-[#50bed2]/80 border-0 hover:shadow-xl transition-all duration-300 group cursor-pointer h-48 relative overflow-hidden">
                  <div className="absolute bottom-0 right-0 opacity-20">
                    <UserCheck className="h-24 w-24 text-white -mr-8 -mb-8" />
                  </div>
                  <CardContent className="p-6 h-full flex flex-col justify-center items-center relative z-10">
                    <div className="w-20 h-20 bg-white/20 backdrop-blur rounded-2xl flex items-center justify-center mb-4 shadow-lg transform group-hover:rotate-6 transition-transform">
                      <UserCheck className="h-12 w-12 text-white" />
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-white mb-1">Check-In</div>
                      <p className="text-xs text-white/80 mb-3">Registrar Llegada</p>
                      <div className="flex justify-center items-center">
                        <span className="text-sm font-medium text-white">Ingresar</span>
                        <div className="w-6 h-6 bg-white/30 backdrop-blur rounded-full flex items-center justify-center ml-2 group-hover:scale-110 transition-transform">
                          <ArrowRight className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>

              {/* Card Grande - Servicios */}
              <Card className="col-span-2 bg-gradient-to-br from-[#ADB6CA]/30 via-[#FCEEA8]/40 to-[#F8E59A]/50 border-0 hover:shadow-2xl transition-all duration-300 group cursor-pointer h-72 relative overflow-hidden">
                <div className="absolute top-0 left-0 w-48 h-48 bg-[#FCEEA8] rounded-full blur-3xl opacity-30 -ml-24 -mt-24"></div>
                <div className="absolute bottom-0 right-0 w-64 h-64 bg-[#FAC9D1] rounded-full blur-3xl opacity-30 -mr-32 -mb-32"></div>
                <CardContent className="p-8 h-full flex items-center justify-between relative z-10">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-16 h-16 bg-white/80 backdrop-blur rounded-2xl flex items-center justify-center shadow-lg">
                        <Stethoscope className="h-10 w-10 text-[#3D4E80]" />
                      </div>
                      <div className="flex gap-1">
                        <Star className="h-5 w-5 text-[#F8E59A] fill-[#F8E59A]" />
                        <Star className="h-5 w-5 text-[#F8E59A] fill-[#F8E59A]" />
                        <Star className="h-5 w-5 text-[#F8E59A] fill-[#F8E59A]" />
                      </div>
                    </div>
                    <h3 className="text-3xl font-bold text-[#3D4E80] mb-3">
                      Especialidades Pediátricas
                    </h3>
                    <p className="text-[#3D4E80]/80 mb-4">
                      Cuidado especializado con diversión y cariño en cada consulta
                    </p>
                    <div className="flex gap-2">
                      <Badge className="bg-[#3D4E80]/10 text-[#3D4E80] hover:bg-[#3D4E80]/20 transition-colors">Neonatología</Badge>
                      <Badge className="bg-[#ADB6CA]/30 text-[#3D4E80] hover:bg-[#ADB6CA]/50 transition-colors">Vacunas</Badge>
                      <Badge className="bg-[#FAC9D1]/50 text-[#3D4E80] hover:bg-[#FAC9D1]/70 transition-colors">Nutrición</Badge>
                    </div>
                  </div>
                  <div className="flex-1 flex justify-center">
                    <div className="relative">
                      <div className="absolute inset-0 bg-gradient-to-r from-[#FAC9D1]/50 to-[#ADB6CA]/50 rounded-2xl blur-2xl opacity-60"></div>
                      <Image
                        src="https://images.unsplash.com/photo-1503454537195-1dcabb73ffb9?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Niña feliz en consulta pediátrica"
                        width={280}
                        height={220}
                        className="rounded-2xl object-cover shadow-xl relative z-10 transform group-hover:scale-105 transition-transform"
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Estadísticas */}
              <Card className="bg-gradient-to-br from-[#FAC9D1]/40 to-[#FAC9D1]/60 border-2 border-[#FAC9D1] hover:shadow-xl hover:border-[#FAC9D1]/80 transition-all duration-300 h-48 relative overflow-hidden">
                <div className="absolute top-0 right-0 opacity-10">
                  <Heart className="h-32 w-32 text-[#3D4E80] -mr-12 -mt-12" />
                </div>
                <CardContent className="p-6 h-full flex flex-col justify-center relative z-10">
                  <div className="flex items-center gap-1 mb-3">
                    <Heart className="h-7 w-7 text-[#3D4E80] fill-[#3D4E80] animate-pulse" />
                    <Heart className="h-7 w-7 text-[#3D4E80] fill-[#3D4E80] animate-pulse delay-75" />
                    <Heart className="h-7 w-7 text-[#3D4E80] fill-[#3D4E80] animate-pulse delay-150" />
                  </div>
                  <div className="text-4xl font-bold text-[#3D4E80] mb-2">2,500+</div>
                  <div className="text-sm text-[#3D4E80]/80 font-medium">
                    Pequeños felices y sanos
                  </div>
                </CardContent>
              </Card>

              {/* Tecnología */}
              <Card className="bg-gradient-to-br from-[#3D4E80] via-[#3D4E80]/90 to-[#ADB6CA] border-0 hover:shadow-xl transition-all duration-300 group cursor-pointer h-48 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-[#FCEEA8]/10 via-[#F8E59A]/10 to-[#FAC9D1]/10 animate-pulse"></div>
                <CardContent className="p-6 h-full flex flex-col justify-center items-center text-white relative z-10">
                  <div className="w-16 h-16 bg-[#FCEEA8]/20 backdrop-blur rounded-xl flex items-center justify-center mb-4 transform group-hover:rotate-6 transition-transform">
                    <Sparkles className="h-10 w-10 text-[#FCEEA8]" />
                  </div>
                  <h3 className="text-lg font-bold text-center mb-4">
                    Asistente mágico
                    <br />
                    <span className="text-[#ADB6CA]">para padres</span>
                  </h3>
                  <div className="flex gap-3 items-center">
                    <Button 
                      size="sm" 
                      className="bg-[#FCEEA8]/20 hover:bg-[#FCEEA8]/30 text-white border-0 rounded-full px-4 backdrop-blur"
                    >
                      <MessageSquare className="h-4 w-4 mr-2" />
                      Preguntar
                    </Button>
                    <div className="w-8 h-8 bg-[#F8E59A]/30 backdrop-blur rounded-full flex items-center justify-center group-hover:scale-110 group-hover:rotate-12 transition-all">
                      <Palette className="h-4 w-4 text-[#3D4E80]" />
                    </div>
                  </div>
                </CardContent>
              </Card>

            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
