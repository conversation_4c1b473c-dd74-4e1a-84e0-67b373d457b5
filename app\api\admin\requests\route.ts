import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    console.log('🔍 Admin requests - userId:', userId);
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar que el usuario es admin usando SQL directo
    const adminRole = await db.execute(sql`
      SELECT * FROM user_roles 
      WHERE "userId" = ${userId} 
      AND role = 'admin' 
      AND status = 'active'
      LIMIT 1
    `);

    console.log('🔍 Admin role check:', {
      userId,
      adminRoleFound: adminRole.rowCount > 0,
      adminRole: adminRole.rows[0]
    });

    if (adminRole.rowCount === 0) {
      return NextResponse.json(
        { success: false, message: 'Acceso denegado. Solo administradores.' },
        { status: 403 }
      );
    }

    // Obtener solicitudes reales usando SQL directo
    console.log('🔍 Fetching registration requests...');
    
    const requestsQuery = await db.execute(sql`
      SELECT 
        r.id,
        r."userId",
        r.role,
        r.status,
        r."submittedAt",
        r."reviewedAt",
        r."generalData",
        r."specificData",
        u.email,
        u."firstName",
        u."lastName",
        u."documentType",
        u."documentNumber",
        u.phone
      FROM "registrationRequests" r
      INNER JOIN "user" u ON r."userId" = u.id
      ORDER BY r."submittedAt" DESC
      LIMIT 10
    `);

    console.log('🔍 Raw SQL result:', requestsQuery.rows);

    // Formatear solicitudes
    const requests = requestsQuery.rows.map((row: any) => ({
      id: row.id,
      userId: row.userId,
      role: row.role || 'unknown',
      status: row.status,
      submittedAt: row.submittedAt,
      reviewedAt: row.reviewedAt,
      user: {
        email: row.email || '',
        firstName: row.firstName || '',
        lastName: row.lastName || '',
        documentType: row.documentType || '',
        documentNumber: row.documentNumber || '',
        phone: row.phone || ''
      }
    }));

    // Estadísticas básicas
    const statsQuery = await db.execute(sql`
      SELECT status, COUNT(*) as count
      FROM "registrationRequests"
      GROUP BY status
    `);

    const statistics = {
      total: 0,
      pending: 0,
      approved: 0,
      rejected: 0,
      reviewing: 0
    };

    statsQuery.rows.forEach((stat: any) => {
      const count = parseInt(stat.count);
      statistics.total += count;
      if (stat.status in statistics) {
        (statistics as any)[stat.status] = count;
      }
    });

    console.log('✅ Found', requests.length, 'requests');

    return NextResponse.json({
      success: true,
      data: {
        requests,
        pagination: {
          page: 1,
          limit: 10,
          totalPages: Math.ceil(statistics.total / 10),
          totalCount: statistics.total
        },
        statistics
      }
    });

  } catch (error) {
    console.error('❌ Error en admin requests:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { 
        success: false, 
        message: 'Error interno del servidor',
        error: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}