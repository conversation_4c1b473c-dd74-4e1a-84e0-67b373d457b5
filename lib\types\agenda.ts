// Tipos para la agenda médica
export interface AppointmentStatus {
  SCHEDULED: 'scheduled';
  CONFIRMED: 'confirmed';
  IN_PROGRESS: 'in_progress';
  COMPLETED: 'completed';
  CANCELLED: 'cancelled';
  NO_SHOW: 'no_show';
}

export interface ConfirmationStatus {
  PENDING: 'pending';
  CONFIRMED: 'confirmed';
  REMINDED: 'reminded';
}

export interface PaymentStatus {
  PENDING: 'pending';
  PARTIAL: 'partial';
  PAID: 'paid';
  REFUNDED: 'refunded';
}

// Datos para crear una nueva cita
export interface CreateAppointmentData {
  title: string;
  description?: string;
  doctorId: string;
  patientId: string;
  consultoryId: string;
  serviceId?: string;
  chiefComplaint?: string;
  scheduledDate: Date;
  startTime: Date;
  endTime: Date;
  duration: number;
  isEmergency?: boolean;
  requiresReminder?: boolean;
  estimatedPrice?: number;
  currency?: string;
}

// Datos para crear un slot de tiempo
export interface CreateAppointmentSlotData {
  doctorId: string;
  consultoryId: string;
  date: Date;
  startTime: Date;
  endTime: Date;
  duration: number;
  isRecurring?: boolean;
  recurrencePattern?: string;
  maxAppointments?: number;
  allowedServiceTypes?: string[];
  isEmergencyOnly?: boolean;
}

// Datos completos de una cita con relaciones
export interface AppointmentWithRelations {
  id: string;
  title: string;
  description?: string;
  
  // Participantes
  doctor: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
  };
  patient: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  consultory: {
    id: string;
    name: string;
    type?: string;
    floor?: number;
    building?: string;
  };
  
  // Información médica
  service?: {
    id: string;
    name: string;
    category: string;
    basePrice?: number;
  };
  chiefComplaint?: string;
  
  // Programación temporal
  scheduledDate: Date;
  startTime: Date;
  endTime: Date;
  duration: number;
  
  // Estado
  status: string;
  confirmationStatus?: string;
  
  // Asistencia
  checkedInAt?: Date;
  startedAt?: Date;
  completedAt?: Date;
  
  // Información financiera
  estimatedPrice?: number;
  finalPrice?: number;
  currency?: string;
  paymentStatus?: string;
  
  // Notas
  doctorNotes?: string;
  adminNotes?: string;
  cancellationReason?: string;
  
  // Seguimiento
  isFollowUp: boolean;
  parentAppointmentId?: string;
  
  // Configuración
  isEmergency: boolean;
  requiresReminder: boolean;
  
  // Auditoría
  createdAt: Date;
  updatedAt: Date;
}

// Filtros para la agenda
export interface AgendaFilters {
  doctorId?: string;
  patientId?: string;
  consultoryId?: string;
  status?: string;
  dateFrom?: Date;
  dateTo?: Date;
  isEmergency?: boolean;
  serviceId?: string;
}

// Vista de calendario
export type CalendarView = 'day' | 'week' | 'month' | 'agenda';

// Configuración de la agenda
export interface AgendaConfig {
  defaultView: CalendarView;
  workingHours: {
    start: string;
    end: string;
  };
  appointmentDuration: number;
  allowOverlapping: boolean;
  reminderMinutes: number[];
}

// Disponibilidad de un doctor
export interface DoctorAvailability {
  doctorId: string;
  date: Date;
  slots: {
    startTime: Date;
    endTime: Date;
    isAvailable: boolean;
    isBlocked: boolean;
    blockReason?: string;
    maxAppointments: number;
    currentAppointments: number;
  }[];
}

// Resumen de estadísticas de la agenda
export interface AgendaStats {
  totalAppointments: number;
  scheduledAppointments: number;
  completedAppointments: number;
  cancelledAppointments: number;
  noShowAppointments: number;
  revenue: number;
  averageAppointmentDuration: number;
  occupancyRate: number;
}