'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Shield, User, Key, UserPlus, AlertCircle, CheckCircle, Clock } from 'lucide-react';
import { GuardianSpecificData, RelationshipType, PatientSpecificData } from '@/lib/types/onboarding';
import { DateInput } from '@/components/ui/date-input';

interface GuardianSpecificFormProps {
  data: Partial<GuardianSpecificData>;
  onChange: (data: Partial<GuardianSpecificData>) => void;
  onNext: () => void;
  onBack: () => void;
}

export function GuardianSpecificForm({ data, onChange, onNext, onBack }: GuardianSpecificFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [validatingCode, setValidatingCode] = useState(false);
  const [codeValidationStatus, setCodeValidationStatus] = useState<'idle' | 'valid' | 'invalid'>('idle');
  const [associatedPatientName, setAssociatedPatientName] = useState('');
  const [showCreateMinor, setShowCreateMinor] = useState(false);

  const handleInputChange = (field: keyof GuardianSpecificData, value: any) => {
    onChange({ ...data, [field]: value });
    // Limpiar error si existe
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const handleMinorDataChange = (field: keyof PatientSpecificData, value: any) => {
    const currentMinorData = data.minorData || {};
    const updatedMinorData = { ...currentMinorData, [field]: value };
    handleInputChange('minorData', updatedMinorData);
  };

  const validateAssociationCode = async (code: string) => {
    if (!code || code.length !== 6) {
      setCodeValidationStatus('invalid');
      return;
    }

    setValidatingCode(true);
    setCodeValidationStatus('idle');

    try {
      const response = await fetch('/api/onboarding/validate-code', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code })
      });

      const result = await response.json();
      
      if (result.success) {
        setCodeValidationStatus('valid');
        setAssociatedPatientName(result.data.patientName);
      } else {
        setCodeValidationStatus('invalid');
      }
    } catch (error) {
      console.error('Error validating code:', error);
      setCodeValidationStatus('invalid');
    } finally {
      setValidatingCode(false);
    }
  };

  const handleCodeChange = (code: string) => {
    handleInputChange('associationCode', code);
    if (code.length === 6) {
      validateAssociationCode(code);
    } else {
      setCodeValidationStatus('idle');
      setAssociatedPatientName('');
    }
  };

  const handleCreateMinorToggle = (checked: boolean) => {
    setShowCreateMinor(checked);
    handleInputChange('createMinor', checked);
    
    if (checked) {
      // Limpiar código de asociación si decide crear menor
      handleInputChange('associationCode', '');
      setCodeValidationStatus('idle');
      setAssociatedPatientName('');
    } else {
      // Limpiar datos del menor si decide no crear
      handleInputChange('minorData', undefined);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validar relación
    if (!data.relationship) newErrors.relationship = 'Tipo de relación es requerido';

    // Validar que tenga código de asociación O crear menor, pero no ambos
    if (!data.associationCode && !data.createMinor) {
      newErrors.association = 'Debe proporcionar código de asociación o crear nuevo menor';
    }

    if (data.associationCode && data.createMinor) {
      newErrors.association = 'No puede usar código de asociación y crear menor al mismo tiempo';
    }

    // Validar código de asociación si se proporciona
    if (data.associationCode) {
      if (data.associationCode.length !== 6) {
        newErrors.associationCode = 'Código debe tener 6 dígitos';
      } else if (codeValidationStatus === 'invalid') {
        newErrors.associationCode = 'Código inválido o expirado';
      } else if (codeValidationStatus === 'idle') {
        newErrors.associationCode = 'Verificando código...';
      }
    }

    // Validar datos del menor si se está creando
    if (data.createMinor && data.minorData) {
      if (!data.minorData.firstName?.trim()) newErrors.minorFirstName = 'Nombre del menor es requerido';
      if (!data.minorData.lastName?.trim()) newErrors.minorLastName = 'Apellido del menor es requerido';
      if (!data.minorData.dateOfBirth) newErrors.minorDateOfBirth = 'Fecha de nacimiento es requerida';
      if (!data.minorData.gender) newErrors.minorGender = 'Género es requerido';
      
      // Validar que realmente es menor de edad
      if (data.minorData.dateOfBirth) {
        const age = new Date().getFullYear() - new Date(data.minorData.dateOfBirth).getFullYear();
        if (age >= 18) {
          newErrors.minorDateOfBirth = 'Debe ser menor de 18 años';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Información de Relación */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <User className="h-5 w-5 text-orange-600" />
            <span>Información de Relación</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>Tipo de Relación *</Label>
            <Select 
              value={data.relationship} 
              onValueChange={(value) => handleInputChange('relationship', value as RelationshipType)}
            >
              <SelectTrigger className={errors.relationship ? 'border-red-500' : ''}>
                <SelectValue placeholder="Seleccionar relación" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value={RelationshipType.PADRE}>Padre</SelectItem>
                <SelectItem value={RelationshipType.MADRE}>Madre</SelectItem>
                <SelectItem value={RelationshipType.TUTOR_LEGAL}>Tutor Legal</SelectItem>
                <SelectItem value={RelationshipType.OTRO}>Otro</SelectItem>
              </SelectContent>
            </Select>
            {errors.relationship && <p className="text-red-500 text-sm">{errors.relationship}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Método de Asociación */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Key className="h-5 w-5 text-orange-600" />
            <span>Método de Asociación</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              Seleccione una opción para asociar con el menor:
            </div>

            {/* Opción 1: Código de asociación */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Checkbox
                  checked={!data.createMinor && (data.associationCode || '').length > 0}
                  onCheckedChange={(checked) => {
                    if (checked) {
                      setShowCreateMinor(false);
                      handleInputChange('createMinor', false);
                    } else {
                      handleInputChange('associationCode', '');
                      setCodeValidationStatus('idle');
                      setAssociatedPatientName('');
                    }
                  }}
                />
                <Label className="font-medium">Tengo un código de asociación</Label>
              </div>
              
              {!data.createMinor && (
                <div className="space-y-2">
                  <Label htmlFor="associationCode">Código de 6 dígitos</Label>
                  <div className="flex items-center space-x-2">
                    <Input
                      id="associationCode"
                      type="text"
                      maxLength={6}
                      value={data.associationCode || ''}
                      onChange={(e) => handleCodeChange(e.target.value.replace(/\D/g, ''))}
                      className={errors.associationCode ? 'border-red-500' : ''}
                      placeholder="123456"
                    />
                    {validatingCode && (
                      <div className="flex items-center space-x-2">
                        <Clock className="h-4 w-4 animate-spin" />
                        <span className="text-sm text-gray-500">Validando...</span>
                      </div>
                    )}
                    {codeValidationStatus === 'valid' && (
                      <Badge className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Válido
                      </Badge>
                    )}
                    {codeValidationStatus === 'invalid' && (
                      <Badge className="bg-red-100 text-red-800">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        Inválido
                      </Badge>
                    )}
                  </div>
                  {errors.associationCode && <p className="text-red-500 text-sm">{errors.associationCode}</p>}
                  
                  {associatedPatientName && (
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-sm font-medium text-green-800">
                        Se asociará con: {associatedPatientName}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Opción 2: Crear nuevo menor */}
            <div className="border rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <Checkbox
                  checked={data.createMinor || false}
                  onCheckedChange={handleCreateMinorToggle}
                />
                <Label className="font-medium">Crear nuevo menor de edad</Label>
              </div>
              
              {data.createMinor && (
                <div className="space-y-4 mt-4">
                  <div className="text-sm text-gray-600">
                    Complete los datos básicos del menor:
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minorFirstName">Nombres *</Label>
                      <Input
                        id="minorFirstName"
                        value={data.minorData?.firstName || ''}
                        onChange={(e) => handleMinorDataChange('firstName', e.target.value)}
                        className={errors.minorFirstName ? 'border-red-500' : ''}
                        placeholder="Juan Carlos"
                      />
                      {errors.minorFirstName && <p className="text-red-500 text-sm">{errors.minorFirstName}</p>}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="minorLastName">Apellidos *</Label>
                      <Input
                        id="minorLastName"
                        value={data.minorData?.lastName || ''}
                        onChange={(e) => handleMinorDataChange('lastName', e.target.value)}
                        className={errors.minorLastName ? 'border-red-500' : ''}
                        placeholder="García López"
                      />
                      {errors.minorLastName && <p className="text-red-500 text-sm">{errors.minorLastName}</p>}
                    </div>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="minorDateOfBirth">Fecha de Nacimiento *</Label>
                      <DateInput
                        value={data.minorData?.dateOfBirth ? new Date(data.minorData.dateOfBirth) : undefined}
                        onChange={(date) => handleMinorDataChange('dateOfBirth', date || null)}
                        maxDate={new Date()} // No puede ser fecha futura
                        className={errors.minorDateOfBirth ? 'border-red-500' : ''}
                      />
                      {errors.minorDateOfBirth && <p className="text-red-500 text-sm">{errors.minorDateOfBirth}</p>}
                    </div>
                    
                    <div className="space-y-2">
                      <Label>Género *</Label>
                      <Select 
                        value={data.minorData?.gender} 
                        onValueChange={(value) => handleMinorDataChange('gender', value)}
                      >
                        <SelectTrigger className={errors.minorGender ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Seleccionar género" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="M">Masculino</SelectItem>
                          <SelectItem value="F">Femenino</SelectItem>
                          <SelectItem value="OTRO">Otro</SelectItem>
                        </SelectContent>
                      </Select>
                      {errors.minorGender && <p className="text-red-500 text-sm">{errors.minorGender}</p>}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
          
          {errors.association && <p className="text-red-500 text-sm">{errors.association}</p>}
        </CardContent>
      </Card>

      {/* Documentos legales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-orange-600" />
            <span>Documentos Legales (Opcional)</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-sm text-gray-600">
              Si es tutor legal (no padre/madre), puede subir documentos que respalden la tutela:
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="legalGuardianshipDoc">Documento de Tutela Legal (PDF)</Label>
              <Input
                id="legalGuardianshipDoc"
                type="file"
                accept=".pdf"
                onChange={(e) => {
                  const file = e.target.files?.[0];
                  if (file) {
                    handleInputChange('legalGuardianshipDoc', file);
                  }
                }}
              />
              <div className="text-sm text-gray-500">
                Documentos como poder notarial, resolución judicial, etc.
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Información importante */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="pt-6">
          <div className="flex items-start space-x-3">
            <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-blue-800">
              <div className="font-medium mb-2">Información Importante:</div>
              <ul className="space-y-1 list-disc list-inside">
                <li>Como tutor legal, podrá tomar decisiones médicas por el menor</li>
                <li>Tendrá acceso completo al historial médico del menor</li>
                <li>Podrá agendar citas y manejar pagos en nombre del menor</li>
                <li>La relación de tutela puede tener fecha de expiración</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <span>← Volver</span>
        </Button>
        <Button 
          onClick={handleNext}
          className="bg-orange-600 hover:bg-orange-700 flex items-center space-x-2"
          disabled={validatingCode}
        >
          <span>Continuar →</span>
        </Button>
      </div>
    </div>
  );
} 