@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
    --color-background: var(--background);
    --color-foreground: var(--foreground);
    --font-sans: var(--font-apple-system);
    --font-mono: var(--font-sf-mono);
    --color-sidebar-ring: var(--sidebar-ring);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar: var(--sidebar);
    --color-chart-5: var(--chart-5);
    --color-chart-4: var(--chart-4);
    --color-chart-3: var(--chart-3);
    --color-chart-2: var(--chart-2);
    --color-chart-1: var(--chart-1);
    --color-ring: var(--ring);
    --color-input: var(--input);
    --color-border: var(--border);
    --color-destructive: var(--destructive);
    --color-accent-foreground: var(--accent-foreground);
    --color-accent: var(--accent);
    --color-muted-foreground: var(--muted-foreground);
    --color-muted: var(--muted);
    --color-secondary-foreground: var(--secondary-foreground);
    --color-secondary: var(--secondary);
    --color-primary-foreground: var(--primary-foreground);
    --color-primary: var(--primary);
    --color-popover-foreground: var(--popover-foreground);
    --color-popover: var(--popover);
    --color-card-foreground: var(--card-foreground);
    --color-card: var(--card);
    --radius-sm: 8px;
    --radius-md: 10px;
    --radius-lg: 12px;
    --radius-xl: 20px;
}

:root {
    /* ========================================
       SISTEMA DE TEMAS UNIFICADO
       ======================================== */
    
    /* TEMA ACTUAL: Verde Mundo Pediatra (por defecto) */
    /* Para cambiar a los colores Pantone, cambia las variables de tema al final */
    
    /* Colores base del sistema - Restaurados al formato original */
    --background: #ffffff;
    --foreground: #1e293b;
    --card: #ffffff;
    --card-foreground: #1e293b;
    --popover: #ffffff;
    --popover-foreground: #1e293b;
    --primary: #059669; /* Verde principal del logo */
    --primary-foreground: #ffffff;
    --secondary: #14b8a6; /* Verde aguamarina del logo */
    --secondary-foreground: #ffffff;
    --muted: #f0fdf4;
    --muted-foreground: #64748b;
    --accent: #10b981; /* Verde medio del logo */
    --accent-foreground: #ffffff;
    --destructive: #ef4444; /* Rojo para errores */
    --destructive-foreground: #ffffff;
    --border: #e2e8f0;
    --input: #f8fafc;
    --ring: #059669;
    
    /* Colores de gráficos */
    --chart-1: #059669; /* Verde principal */
    --chart-2: #14b8a6; /* Verde aguamarina */
    --chart-3: #10b981; /* Verde esmeralda */
    --chart-4: #06b6d4; /* Azul cielo */
    --chart-5: #ec4899; /* Rosa/magenta del logo */
    
    /* Sidebar */
    --sidebar: #f8fafc;
    --sidebar-foreground: #1e293b;
    --sidebar-primary: #059669;
    --sidebar-primary-foreground: #ffffff;
    --sidebar-accent: #f0fdf4;
    --sidebar-accent-foreground: #1e293b;
    --sidebar-border: #e2e8f0;
    --sidebar-ring: #059669;
    
    /* Colores adicionales para el diseño */
    --mundo-green: #059669;
    --mundo-teal: #14b8a6;
    --mundo-emerald: #10b981;
}

/* ========================================
   CLASES DE UTILIDAD PARA ICONOS CONSISTENTES
   ======================================== */

@layer utilities {
  /* ESTÁNDAR BASADO EN FORMULARIO DE PACIENTES */
  
  /* Comunicación (Mail, Phone, Message) */
  .icon-communication {
    @apply text-blue-600;
  }
  
  /* Contacto/Teléfono */
  .icon-phone {
    @apply text-green-600;
  }
  
  /* Documentos/ID/Archivos */
  .icon-document {
    @apply text-purple-600;
  }
  
  /* Usuario/Personal */
  .icon-user {
    @apply text-blue-600;
  }
  
  /* Tiempo/Fechas */
  .icon-time {
    @apply text-blue-600;
  }
  
  /* Médico/Salud */
  .icon-medical {
    @apply text-emerald-600;
  }
  
  /* Ubicación */
  .icon-location {
    @apply text-orange-600;
  }
  
  /* Estados del sistema */
  .icon-muted {
    @apply text-gray-500;
  }
  
  .icon-success {
    @apply text-green-600;
  }
  
  .icon-warning {
    @apply text-yellow-600;
  }
  
  .icon-error {
    @apply text-red-600;
  }
  
  .icon-info {
    @apply text-blue-600;
  }
  
  /* Tamaños estándar de iconos */
  .icon-xs {
    @apply h-3 w-3;
  }
  
  .icon-sm {
    @apply h-4 w-4;
  }
  
  .icon-md {
    @apply h-5 w-5;
  }
  
  .icon-lg {
    @apply h-6 w-6;
  }
  
  .icon-xl {
    @apply h-8 w-8;
  }
}

/* Continuación de variables CSS del root */
:root {
    --mundo-cyan: #06b6d4;
    --mundo-pink: #ec4899;
    --mundo-orange: #f97316;
    
    /* ========================================
       COLORES PERSONALIZADOS ADICIONALES
       ======================================== */
    
    /* Colores adicionales en formato HEX para uso directo */
    --custom-turquoise: #A1D6CA; /* Pantone 565 C */
    --custom-navy: #3D4E80; /* Pantone 4142 C */
    --custom-yellow-light: #FCEEA8; /* Pantone 938 C */
    --custom-yellow-medium: #F8E59A; /* Pantone 2001 C */
    --custom-pink-soft: #FAC9D1; /* Pantone 9284 C */
    --custom-dark: #252A36; /* Pantone 4280 C */
    
    /* Mapeo de colores para componentes específicos */
    --hero-gradient-start: var(--custom-turquoise);
    --hero-gradient-end: var(--custom-navy);
    --button-primary: var(--custom-navy);
    --button-primary-hover: var(--custom-dark);
    --highlight-color: var(--custom-yellow-light);
    --accent-color: var(--custom-pink-soft);
    
    /* Fonts - Using Tailwind's default sans-serif stack */
    --font-sans: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    --font-serif: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
    --font-mono: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
    
    /* Rounded corners - más moderno */
    --radius: 16px;
    
    /* Shadows - más sutiles y modernas */
    --shadow-2xs: 0px 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-xs: 0px 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-sm: 0px 2px 4px rgba(0, 0, 0, 0.1);
    --shadow: 0px 2px 6px rgba(0, 0, 0, 0.1);
    --shadow-md: 0px 4px 8px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0px 8px 16px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0px 12px 24px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0px 16px 32px rgba(0, 0, 0, 0.1);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground font-sans;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }
    button,
    input,
    select,
    textarea {
        @apply focus:outline-none focus:ring-2 focus:ring-primary/50 transition-all duration-200;
        caret-color: #6b7280 !important; /* Cursor gris para TODOS los inputs */
    }
    button {
        @apply hover:cursor-pointer;
    }
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        @apply font-medium tracking-tight;
    }
}

/* Custom styles for react-select */
.react-select__control input:focus {
    caret-color: #6b7280 !important; /* Cursor gris en lugar de verde */
}

.react-select__input-container input {
    caret-color: #6b7280 !important; /* Cursor gris en lugar de verde */
}

.react-select__input {
    color: #1f2937 !important; /* Texto gris oscuro */
}

/* Forzar el color del cursor en TODOS los inputs de react-select */
input[id^="react-select"] {
    caret-color: #6b7280 !important;
}

/* Custom styles for Clerk modals */
.clerk-modal-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.clerk-modal-wrapper .cl-rootBox {
    width: 100%;
    display: flex;
    justify-content: center;
}

.clerk-modal-wrapper .cl-card {
    box-shadow: none;
    border: none;
    background: transparent;
    width: 100%;
    max-width: 400px;
    padding: 0;
    margin: 0;
}

.clerk-modal-wrapper .cl-headerTitle,
.clerk-modal-wrapper .cl-headerSubtitle {
    display: none;
}

.clerk-modal-wrapper .cl-socialButtonsBlockButton {
    background: white;
    border: 1px solid #d1d5db;
    color: #374151;
    width: 100%;
    margin-bottom: 6px;
    padding: 10px 16px;
    font-size: 14px;
    height: auto;
}

.clerk-modal-wrapper .cl-socialButtonsBlockButton:hover {
    background: #f9fafb;
}

.clerk-modal-wrapper .cl-formButtonPrimary {
    background: linear-gradient(to right, #059669, #0d9488);
    width: 100%;
    padding: 12px 16px;
    font-size: 14px;
    height: auto;
}

.clerk-modal-wrapper .cl-formButtonPrimary:hover {
    background: linear-gradient(to right, #047857, #0f766e);
}

.clerk-modal-wrapper .cl-footerActionLink {
    color: #059669;
    font-size: 14px;
}

.clerk-modal-wrapper .cl-footerActionLink:hover {
    color: #047857;
}

.clerk-modal-wrapper .cl-formFieldInput {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #d1d5db;
    border-radius: 6px;
    margin-bottom: 6px;
    font-size: 14px;
}

.clerk-modal-wrapper .cl-formFieldInput:focus {
    outline: none;
    border-color: #059669;
    box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.1);
}

.clerk-modal-wrapper .cl-formFieldLabel {
    font-size: 14px;
    margin-bottom: 4px;
    color: #374151;
}

.clerk-modal-wrapper .cl-dividerLine {
    margin: 12px 0;
}

.clerk-modal-wrapper .cl-dividerText {
    font-size: 12px;
    color: #6b7280;
}

.clerk-modal-wrapper .cl-formFieldRow {
    margin-bottom: 12px;
}

.clerk-modal-wrapper .cl-footer {
    margin-top: 16px;
    text-align: center;
}

.clerk-modal-wrapper .cl-socialButtonsBlock {
    margin-bottom: 16px;
}

.clerk-modal-wrapper .cl-form {
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    .clerk-modal-wrapper .cl-formFieldInput {
        padding: 8px 10px;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .clerk-modal-wrapper .cl-formButtonPrimary {
        padding: 10px 16px;
        font-size: 16px;
    }
    
    .clerk-modal-wrapper .cl-socialButtonsBlockButton {
        padding: 8px 14px;
        font-size: 16px;
    }
}
