import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que es asistente o doctor
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    if (!userRole.length || !['assistant', 'doctor'].includes(userRole[0].role)) {
      return NextResponse.json({ 
        error: 'Solo asistentes y doctores pueden marcar no show' 
      }, { status: 403 });
    }

    const appointmentId = params.id;
    const body = await request.json();
    const { reason } = body;

    // Obtener información de la cita
    const appointment = await db
      .select({
        id: appointments.id,
        status: appointments.status,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        scheduledDate: appointments.scheduledDate,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (!appointment.length) {
      return NextResponse.json({ 
        error: 'Cita no encontrada' 
      }, { status: 404 });
    }

    const appointmentData = appointment[0];

    // Verificar que la cita esté en estado válido para no-show
    if (!['confirmed', 'checked_in'].includes(appointmentData.status)) {
      return NextResponse.json({ 
        error: 'Solo se puede marcar no show en citas confirmadas o con llegada registrada' 
      }, { status: 400 });
    }

    // Marcar como no show
    await db
      .update(appointments)
      .set({
        status: 'no_show',
        noShowReason: reason || 'Paciente no se presentó a la cita',
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    return NextResponse.json({
      success: true,
      message: 'Cita marcada como no show exitosamente',
      data: {
        appointmentId,
        status: 'no_show',
        reason: reason || 'Paciente no se presentó a la cita'
      }
    });

  } catch (error) {
    console.error('Error marking no-show:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}