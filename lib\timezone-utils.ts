import { formatInTimeZone, toZonedTime, fromZonedTime } from 'date-fns-tz';
import { format, parse, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';

// Configuración centralizada de timezone
// Por defecto Guatemala (UTC-6), pero configurable
export const DEFAULT_TIMEZONE = 'America/Guatemala';

// Función para obtener el timezone del consultorio (configurable en el futuro)
export const getConsultoryTimezone = (): string => {
  // TODO: En el futuro esto se puede obtener de la configuración del consultorio
  // o de una variable de entorno
  return process.env.NEXT_PUBLIC_CONSULTORY_TIMEZONE || DEFAULT_TIMEZONE;
};

/**
 * Convierte una fecha local a la zona horaria del consultorio
 * Esta función asegura que una fecha/hora seleccionada por el usuario
 * se mantenga exactamente igual en la zona horaria del consultorio
 */
export const toConsultoryTimezone = (date: Date): Date => {
  const timezone = getConsultoryTimezone();
  return toZonedTime(date, timezone);
};

/**
 * Convierte una fecha de la zona horaria del consultorio a UTC para almacenamiento
 * IMPORTANTE: Solo usar para almacenamiento en DB, no para display
 */
export const fromConsultoryTimezone = (date: Date): Date => {
  const timezone = getConsultoryTimezone();
  return fromZonedTime(date, timezone);
};

/**
 * Crea una fecha desde strings de fecha y hora en timezone del consultorio
 * Ejemplo: createConsultoryDate('2025-01-15', '15:30') 
 * Retorna una fecha que representa exactamente 15:30 en Guatemala
 */
export const createConsultoryDate = (dateString: string, timeString: string): Date => {
  const timezone = getConsultoryTimezone();
  
  // Combinar fecha y hora
  const dateTimeString = `${dateString} ${timeString}`;
  
  // Parsear como fecha local y luego convertir a timezone del consultorio
  const localDate = parse(dateTimeString, 'yyyy-MM-dd HH:mm', new Date());
  
  // Tratar la fecha como si ya estuviera en el timezone del consultorio
  return fromZonedTime(localDate, timezone);
};

/**
 * Formatea una fecha para mostrar en la zona horaria del consultorio
 * Esta función debe usarse para MOSTRAR fechas al usuario
 */
export const formatInConsultoryTimezone = (
  date: Date | string, 
  formatStr: string = 'yyyy-MM-dd HH:mm'
): string => {
  const timezone = getConsultoryTimezone();
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  
  return formatInTimeZone(dateObj, timezone, formatStr, { locale: es });
};

/**
 * Formatea solo la hora en timezone del consultorio
 */
export const formatTimeInConsultoryTimezone = (date: Date | string): string => {
  return formatInConsultoryTimezone(date, 'HH:mm');
};

/**
 * Formatea solo la fecha en timezone del consultorio
 */
export const formatDateInConsultoryTimezone = (date: Date | string): string => {
  return formatInConsultoryTimezone(date, 'yyyy-MM-dd');
};

/**
 * Crea una fecha/hora ISO string manteniendo el tiempo local del consultorio
 * Esta función es crítica para guardar correctamente las fechas en la BD
 * NO convierte a UTC, mantiene la hora local como si fuera UTC
 */
export const createConsultoryDateString = (dateString: string, timeString: string): string => {
  // Simplemente concatena fecha y hora en formato ISO
  // Esto preserva la hora local sin conversión a UTC
  return `${dateString}T${timeString}:00.000Z`;
};

/**
 * Crea un ISO string desde una fecha manteniendo la hora local
 */
export const toConsultoryISOString = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');
  
  return `${year}-${month}-${day}T${hours}:${minutes}:${seconds}.000Z`;
};

/**
 * Formatea una fecha ISO que ya está en hora local (no hace conversión de timezone)
 * Úsala cuando las fechas en la BD ya están en hora local de Guatemala
 */
export const formatLocalDateTime = (
  dateStr: string | Date, 
  formatStr: string = 'yyyy-MM-dd HH:mm'
): string => {
  // Si es string ISO con Z, tratarlo como hora local (no UTC)
  if (typeof dateStr === 'string' && dateStr.endsWith('Z')) {
    // Extraer componentes de fecha/hora del string ISO
    const match = dateStr.match(/(\d{4})-(\d{2})-(\d{2})T(\d{2}):(\d{2}):(\d{2})/);
    if (match) {
      const [_, year, month, day, hours, minutes, seconds] = match;
      const localDate = new Date(
        parseInt(year),
        parseInt(month) - 1,
        parseInt(day),
        parseInt(hours),
        parseInt(minutes),
        parseInt(seconds)
      );
      return format(localDate, formatStr, { locale: es });
    }
  }
  
  // Si es Date o string sin Z, usar directamente
  const date = typeof dateStr === 'string' ? new Date(dateStr) : dateStr;
  return format(date, formatStr, { locale: es });
};

/**
 * Formatea fecha completa legible en español para Guatemala
 */
export const formatReadableDateInConsultoryTimezone = (date: Date | string): string => {
  return formatInConsultoryTimezone(date, "EEEE, d 'de' MMMM 'de' yyyy");
};

/**
 * Obtiene la fecha actual en el timezone del consultorio
 */
export const nowInConsultoryTimezone = (): Date => {
  const timezone = getConsultoryTimezone();
  return toZonedTime(new Date(), timezone);
};

/**
 * Convierte una fecha ISO string a Date manteniendo el timezone del consultorio
 * Útil para procesar datos que vienen de la API
 */
export const parseConsultoryDate = (isoString: string): Date => {
  const timezone = getConsultoryTimezone();
  const date = parseISO(isoString);
  return toZonedTime(date, timezone);
};

/**
 * Formatea hora directamente desde UTC sin conversión de timezone
 * Usar para fechas que ya están guardadas como hora local en formato UTC
 */
export const formatLocalTimeFromUTC = (date: Date | string, formatStr: string = 'HH:mm'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Extraer componentes UTC directamente (que representan hora local)
  const hours = dateObj.getUTCHours();
  const minutes = dateObj.getUTCMinutes();

  // Formatear manualmente para evitar conversión de timezone
  if (formatStr === 'HH:mm') {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
  }

  // Para otros formatos, usar format pero con UTC
  return format(dateObj, formatStr.replace(/H/g, 'H').replace(/h/g, 'h'), { locale: es });
};

/**
 * Formatea fecha directamente desde UTC sin conversión de timezone
 * Usar para fechas que ya están guardadas como hora local en formato UTC
 */
export const formatLocalDateFromUTC = (date: Date | string, formatStr: string = 'dd/MM/yyyy'): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  // Extraer componentes UTC directamente (que representan fecha local)
  const year = dateObj.getUTCFullYear();
  const month = dateObj.getUTCMonth() + 1; // getUTCMonth() es 0-indexado
  const day = dateObj.getUTCDate();

  // Formatear manualmente para formatos comunes
  if (formatStr === 'dd/MM/yyyy') {
    return `${day.toString().padStart(2, '0')}/${month.toString().padStart(2, '0')}/${year}`;
  }
  if (formatStr === 'yyyy-MM-dd') {
    return `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
  }

  // Para otros formatos, crear una fecha local con los componentes UTC
  const localDate = new Date(year, month - 1, day);
  return format(localDate, formatStr, { locale: es });
};

/**
 * Crea un objeto con fecha y hora separados en timezone del consultorio
 * Útil para componentes que necesitan fecha y hora por separado
 */
export const splitConsultoryDateTime = (date: Date | string): {
  dateString: string;
  timeString: string;
  fullDateTime: Date;
} => {
  const timezone = getConsultoryTimezone();
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const zonedDate = toZonedTime(dateObj, timezone);
  
  return {
    dateString: format(zonedDate, 'yyyy-MM-dd'),
    timeString: format(zonedDate, 'HH:mm'),
    fullDateTime: zonedDate
  };
};

/**
 * Valida si una fecha está en el rango permitido del consultorio
 * Por ejemplo, no permitir citas en el pasado
 */
export const isValidConsultoryDate = (date: Date): boolean => {
  const now = nowInConsultoryTimezone();
  const compareDate = toConsultoryTimezone(date);
  
  // No permitir fechas en el pasado (considerando solo el día)
  const nowDateOnly = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const compareDateOnly = new Date(compareDate.getFullYear(), compareDate.getMonth(), compareDate.getDate());
  
  return compareDateOnly >= nowDateOnly;
};

/**
 * Obtiene el offset de timezone en minutos para el consultorio
 * Útil para debugging o configuraciones avanzadas
 */
export const getConsultoryTimezoneOffset = (): number => {
  const timezone = getConsultoryTimezone();
  const now = new Date();
  const zonedNow = toZonedTime(now, timezone);
  
  return zonedNow.getTimezoneOffset();
};

/**
 * Debug: Información completa de una fecha en diferentes formatos
 * Útil para debugging de problemas de timezone
 */
export const debugDate = (date: Date | string, label: string = 'Debug Date'): void => {
  const dateObj = typeof date === 'string' ? parseISO(date) : date;
  const timezone = getConsultoryTimezone();
  
  console.group(`🐛 ${label}`);
  console.log('Original Date:', dateObj);
  console.log('Original ISO:', dateObj.toISOString());
  console.log('Consultory Timezone:', timezone);
  console.log('In Consultory TZ:', formatInConsultoryTimezone(dateObj, 'yyyy-MM-dd HH:mm:ss xxx'));
  console.log('Zoned Time:', toZonedTime(dateObj, timezone));
  console.groupEnd();
};