import { db } from '@/db/drizzle';
import { nonPathologicalHistory } from '@/db/schema';

export const nonPathologicalHistoryData = [
  // Hábitos alimentarios
  {
    id: 'breastfeeding',
    name: '<PERSON><PERSON><PERSON> materna',
    category: 'Alimentac<PERSON>',
    subcategory: '<PERSON><PERSON><PERSON>',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'infant',
    description: 'Alimentación con leche materna exclusiva o mixta',
    benefits: ['Inmunidad natural', 'Vínculo madre-hijo', 'Nutrición óptima'],
    duration: 'primeros 6 meses de vida',
    order: 1
  },
  {
    id: 'bottle-feeding',
    name: 'Alimentación con fórmula',
    category: 'Alimentación',
    subcategory: 'Fórmula',
    isPositive: true,
    importance: 'medium',
    ageRelevant: 'infant',
    description: 'Alimentación con leche de fórmula infantil',
    benefits: ['Nutrición controlada', 'Flexibilidad en horarios'],
    duration: 'primeros 12 meses de vida',
    order: 2
  },
  {
    id: 'balanced-diet',
    name: 'Dieta balanceada',
    category: '<PERSON><PERSON>ac<PERSON>',
    subcategory: 'Hábitos',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Alimentación variada incluyendo todos los grupos alimentarios',
    benefits: ['Crecimiento adecuado', 'Desarrollo cognitivo', 'Prevención de enfermedades'],
    duration: 'toda la vida',
    order: 3
  },

  // Actividad física
  {
    id: 'regular-exercise',
    name: 'Ejercicio regular',
    category: 'Actividad Física',
    subcategory: 'Ejercicio',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'child-adult',
    description: 'Actividad física regular adaptada a la edad',
    benefits: ['Desarrollo muscular', 'Salud cardiovascular', 'Bienestar mental'],
    duration: 'mínimo 60 min diarios en niños',
    order: 4
  },
  {
    id: 'sports-participation',
    name: 'Participación en deportes',
    category: 'Actividad Física',
    subcategory: 'Deportes',
    isPositive: true,
    importance: 'medium',
    ageRelevant: 'child-adolescent',
    description: 'Participación activa en deportes organizados',
    benefits: ['Disciplina', 'Trabajo en equipo', 'Coordinación motora'],
    duration: 'según temporadas deportivas',
    order: 5
  },

  // Hábitos de sueño
  {
    id: 'adequate-sleep',
    name: 'Sueño adecuado',
    category: 'Sueño',
    subcategory: 'Horarios',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Horarios regulares de sueño según la edad',
    benefits: ['Desarrollo cerebral', 'Crecimiento', 'Rendimiento escolar'],
    duration: '10-14h lactantes, 9-11h escolares, 8-10h adolescentes',
    order: 6
  },
  {
    id: 'sleep-routine',
    name: 'Rutina de sueño',
    category: 'Sueño',
    subcategory: 'Rutinas',
    isPositive: true,
    importance: 'medium',
    ageRelevant: 'all',
    description: 'Rutinas consistentes antes de dormir',
    benefits: ['Mejor calidad del sueño', 'Reducción de ansiedad'],
    duration: 'rutina diaria',
    order: 7
  },

  // Hábitos nocivos
  {
    id: 'smoking-exposure',
    name: 'Exposición al humo de cigarrillo',
    category: 'Sustancias',
    subcategory: 'Tabaco',
    isPositive: false,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Exposición pasiva o activa al humo del tabaco',
    risks: ['Problemas respiratorios', 'Asma', 'Infecciones frecuentes'],
    duration: 'variable',
    order: 8
  },
  {
    id: 'alcohol-consumption',
    name: 'Consumo de alcohol',
    category: 'Sustancias',
    subcategory: 'Alcohol',
    isPositive: false,
    importance: 'high',
    ageRelevant: 'adolescent-adult',
    description: 'Consumo de bebidas alcohólicas',
    risks: ['Daño hepático', 'Problemas de conducta', 'Accidentes'],
    duration: 'variable',
    order: 9
  },

  // Exposición ambiental
  {
    id: 'pollution-exposure',
    name: 'Exposición a contaminación',
    category: 'Ambiental',
    subcategory: 'Contaminación',
    isPositive: false,
    importance: 'medium',
    ageRelevant: 'all',
    description: 'Exposición a contaminación del aire o agua',
    risks: ['Problemas respiratorios', 'Alergias', 'Desarrollo cognitivo'],
    duration: 'según ubicación',
    order: 10
  },
  {
    id: 'chemical-exposure',
    name: 'Exposición a químicos',
    category: 'Ambiental',
    subcategory: 'Químicos',
    isPositive: false,
    importance: 'medium',
    ageRelevant: 'all',
    description: 'Exposición a productos químicos domésticos o industriales',
    risks: ['Intoxicaciones', 'Problemas de piel', 'Desarrollo neurológico'],
    duration: 'variable',
    order: 11
  },

  // Higiene
  {
    id: 'good-hygiene',
    name: 'Buena higiene personal',
    category: 'Higiene',
    subcategory: 'Personal',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Hábitos regulares de higiene personal',
    benefits: ['Prevención de infecciones', 'Autoestima', 'Aceptación social'],
    duration: 'rutina diaria',
    order: 12
  },
  {
    id: 'dental-hygiene',
    name: 'Higiene dental',
    category: 'Higiene',
    subcategory: 'Dental',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Cepillado regular y cuidado dental',
    benefits: ['Prevención de caries', 'Salud gingival', 'Aliento fresco'],
    duration: 'mínimo 2 veces al día',
    order: 13
  },

  // Tiempo de pantalla
  {
    id: 'screen-time',
    name: 'Tiempo de pantalla excesivo',
    category: 'Tecnología',
    subcategory: 'Pantallas',
    isPositive: false,
    importance: 'medium',
    ageRelevant: 'child-adolescent',
    description: 'Uso excesivo de dispositivos electrónicos',
    risks: ['Problemas de visión', 'Sedentarismo', 'Problemas de sueño'],
    duration: 'más de 2h diarias recomendadas',
    order: 14
  },

  // Socialización
  {
    id: 'social-interaction',
    name: 'Interacción social saludable',
    category: 'Social',
    subcategory: 'Interacción',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Participación en actividades sociales apropiadas para la edad',
    benefits: ['Desarrollo emocional', 'Habilidades comunicativas', 'Autoconfianza'],
    duration: 'actividades regulares',
    order: 15
  },

  // Estimulación temprana
  {
    id: 'early-stimulation',
    name: 'Estimulación temprana',
    category: 'Desarrollo',
    subcategory: 'Estimulación',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'infant-toddler',
    description: 'Actividades que estimulan el desarrollo motor y cognitivo',
    benefits: ['Desarrollo neurológico', 'Coordinación motora', 'Aprendizaje'],
    duration: 'primeros 3 años de vida',
    order: 16
  },

  // Educación
  {
    id: 'reading-habits',
    name: 'Hábitos de lectura',
    category: 'Educación',
    subcategory: 'Lectura',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'child-adult',
    description: 'Lectura regular apropiada para la edad',
    benefits: ['Desarrollo del lenguaje', 'Imaginación', 'Concentración'],
    duration: 'mínimo 30 min diarios',
    order: 17
  },

  // Uso de medicamentos
  {
    id: 'medication-compliance',
    name: 'Adherencia a medicamentos',
    category: 'Medicamentos',
    subcategory: 'Adherencia',
    isPositive: true,
    importance: 'high',
    ageRelevant: 'all',
    description: 'Cumplimiento adecuado de tratamientos médicos',
    benefits: ['Efectividad del tratamiento', 'Prevención de complicaciones'],
    duration: 'según prescripción médica',
    order: 18
  },

  // Viajes y exposiciones
  {
    id: 'travel-history',
    name: 'Historial de viajes',
    category: 'Exposición',
    subcategory: 'Viajes',
    isPositive: true,
    importance: 'medium',
    ageRelevant: 'all',
    description: 'Viajes a diferentes regiones geográficas',
    benefits: ['Exposición cultural', 'Desarrollo personal'],
    duration: 'variable',
    order: 19
  },

  // Actividades extracurriculares
  {
    id: 'extracurricular-activities',
    name: 'Actividades extracurriculares',
    category: 'Desarrollo',
    subcategory: 'Actividades',
    isPositive: true,
    importance: 'medium',
    ageRelevant: 'child-adolescent',
    description: 'Participación en actividades fuera del horario escolar',
    benefits: ['Desarrollo de talentos', 'Disciplina', 'Socialización'],
    duration: 'según programas',
    order: 20
  }
];

export async function seedNonPathologicalHistory() {
  console.log('🌱 Seeding non-pathological history...');
  
  try {
    await db.insert(nonPathologicalHistory).values(nonPathologicalHistoryData).onConflictDoNothing();
    console.log('✅ Non-pathological history seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding non-pathological history:', error);
    throw error;
  }
}