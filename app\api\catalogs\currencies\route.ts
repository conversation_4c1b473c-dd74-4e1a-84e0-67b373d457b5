import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { currencies } from '@/db/schema';
import { eq, and, count, asc, desc, ilike } from 'drizzle-orm';

// GET - Listar monedas
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const onlyActive = searchParams.get('active') === 'true';
    const defaultFilter = searchParams.get('default');
    const orderBy = searchParams.get('orderBy') || 'name'; // 'name', 'code', 'createdAt'
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(currencies);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        ilike(currencies.name, `%${search}%`),
        ilike(currencies.code, `%${search}%`),
        ilike(currencies.symbol, `%${search}%`)
      );
    }

    if (onlyActive) {
      conditions.push(eq(currencies.isActive, true));
    }

    // Filtro por moneda predeterminada
    if (defaultFilter === 'true') {
      conditions.push(eq(currencies.isDefault, true));
    } else if (defaultFilter === 'false') {
      conditions.push(eq(currencies.isDefault, false));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'code' ? currencies.code : 
                       orderBy === 'createdAt' ? currencies.createdAt : 
                       currencies.name;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const currenciesData = await query;

    // Obtener total para paginación
    let totalQuery = db.select({ count: count() }).from(currencies);
    if (conditions.length > 0) {
      totalQuery = totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    return NextResponse.json({
      data: currenciesData,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: currenciesData.filter(c => c.isActive).length,
        default: currenciesData.find(c => c.isDefault)?.code || null
      }
    });

  } catch (error) {
    console.error('Error obteniendo monedas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nueva moneda (solo admin)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden crear monedas' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, code, symbol, exchangeRate, isDefault, isActive } = body;

    // Validaciones
    if (!name || !code || !symbol || !exchangeRate) {
      return NextResponse.json(
        { error: 'Nombre, código, símbolo y tasa de cambio son requeridos' },
        { status: 400 }
      );
    }

    if (code.length !== 3) {
      return NextResponse.json(
        { error: 'El código de moneda debe tener 3 caracteres' },
        { status: 400 }
      );
    }

    if (exchangeRate <= 0) {
      return NextResponse.json(
        { error: 'La tasa de cambio debe ser mayor a 0' },
        { status: 400 }
      );
    }

    // Verificar que no exista una moneda con el mismo código
    const existingCurrency = await db.select().from(currencies).where(eq(currencies.code, code.toUpperCase())).limit(1);
    if (existingCurrency.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe una moneda con este código' },
        { status: 400 }
      );
    }

    // Si es moneda por defecto, primero desactivar la actual
    if (isDefault) {
      await db.update(currencies)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(eq(currencies.isDefault, true));
    }

    const newCurrency = {
      id: code.toLowerCase(),
      name: name.trim(),
      code: code.toUpperCase(),
      symbol: symbol.trim(),
      exchangeRate: parseFloat(exchangeRate),
      isDefault: Boolean(isDefault),
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedCurrency] = await db.insert(currencies).values(newCurrency).returning();

    return NextResponse.json({
      message: 'Moneda creada exitosamente',
      data: insertedCurrency
    });

  } catch (error) {
    console.error('Error creando moneda:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar moneda (solo admin)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden actualizar monedas' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, symbol, exchangeRate, isDefault, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de moneda requerido' },
        { status: 400 }
      );
    }

    // Buscar moneda existente
    const existingCurrency = await db.select().from(currencies).where(eq(currencies.id, id)).limit(1);
    if (existingCurrency.length === 0) {
      return NextResponse.json(
        { error: 'Moneda no encontrada' },
        { status: 404 }
      );
    }

    // Validaciones
    if (exchangeRate && exchangeRate <= 0) {
      return NextResponse.json(
        { error: 'La tasa de cambio debe ser mayor a 0' },
        { status: 400 }
      );
    }

    // Si se está marcando como default, desactivar la actual
    if (isDefault) {
      await db.update(currencies)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(eq(currencies.isDefault, true));
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(symbol && { symbol: symbol.trim() }),
      ...(exchangeRate && { exchangeRate: parseFloat(exchangeRate) }),
      ...(isDefault !== undefined && { isDefault: Boolean(isDefault) }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedCurrency] = await db.update(currencies)
      .set(updateData)
      .where(eq(currencies.id, id))
      .returning();

    return NextResponse.json({
      message: 'Moneda actualizada exitosamente',
      data: updatedCurrency
    });

  } catch (error) {
    console.error('Error actualizando moneda:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}