/**
 * FLUJOS UNIFICADOS DE EMAILS PARA PACIENTES
 * Lógica específica para los 3 tipos de creación de pacientes
 */

import { sendPatientEmail, scheduleEmail } from './core';
import { determinePreCheckinRecipient } from '../guardian-utils';
import { generateShortCode } from '../short-codes';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { db } from '../../db/drizzle';
import { patientInvitations, appointments } from '../../db/schema';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';
import { nanoid } from 'nanoid';

/**
 * FLUJO 1: PACIENTE CREADO MANUALMENTE
 * Por doctor o admin en el sistema
 */
export async function handlePatientCreatedManually(params: {
  patientId: string;
  patientName: string;
  createdBy: string;
  hasAppointment?: boolean;
  appointmentDate?: Date;
}) {
  console.log('🔧 Iniciando flujo: Paciente creado manualmente');
  
  try {
    // 1. Determinar destinatario del email
    const recipientInfo = await determinePreCheckinRecipient(params.patientId);
    
    // 2. Preparar datos del email
    const emailParams = {
      patientName: params.patientName,
      isDependent: recipientInfo.isDependent,
      guardianName: recipientInfo.isDependent ? 
        `${recipientInfo.guardianInfo?.firstName} ${recipientInfo.guardianInfo?.lastName}` : 
        undefined,
      relationship: recipientInfo.guardianInfo?.relationship,
      activationLink: `${process.env.NEXT_PUBLIC_APP_URL}/activate-account?patientId=${params.patientId}`,
      hasAppointment: params.hasAppointment,
      appointmentDate: params.appointmentDate ? 
        format(params.appointmentDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: es }) : 
        undefined
    };

    // 3. Enviar email de creación
    const result = await sendPatientEmail(
      'patient_created',
      recipientInfo.email,
      emailParams,
      {
        patientId: params.patientId,
        createdBy: params.createdBy,
        flowType: 'manual_creation'
      }
    );

    if (result.success) {
      console.log('✅ Email de paciente creado manualmente enviado');
    } else {
      console.error('❌ Error enviando email de paciente manual:', result.error);
    }

    return result;

  } catch (error) {
    console.error('❌ Error en flujo de paciente manual:', error);
    return {
      success: false,
      status: 'failed' as const,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

/**
 * FLUJO 2: PACIENTE COMPLETÓ ONBOARDING
 * Usuario se registró por su cuenta
 */
export async function handlePatientOnboardingCompleted(params: {
  userId: string;
  userName: string;
  userEmail: string;
  userRole: string;
  requestId?: string;
}) {
  console.log('📝 Iniciando flujo: Onboarding completado');
  
  try {
    // 1. Preparar mensaje de bienvenida según el rol
    const roleMessages = {
      patient: 'Tu cuenta como paciente ha sido creada exitosamente. Ya puedes agendar citas y acceder a todos nuestros servicios médicos.',
      doctor: 'Tu solicitud como doctor ha sido recibida. Nuestro equipo la revisará y te notificaremos el estado de tu aprobación.',
      assistant: 'Tu solicitud como asistente médico ha sido recibida. Te notificaremos cuando sea aprobada.',
      guardian: 'Tu cuenta como responsable ha sido creada. Podrás gestionar las citas médicas de tus dependientes.'
    };

    const nextStepsByRole = {
      patient: [
        'Completa tu perfil médico',
        'Agenda tu primera cita',
        'Explora nuestros servicios disponibles'
      ],
      doctor: [
        'Espera la aprobación de tu solicitud',
        'Prepara tu documentación médica',
        'Revisa nuestras políticas de atención'
      ],
      assistant: [
        'Espera la aprobación de tu solicitud',
        'Familiarízate con nuestro sistema',
        'Prepara tu documentación profesional'
      ],
      guardian: [
        'Agrega los dependientes a tu cargo',
        'Agenda citas para tus dependientes',
        'Completa los perfiles médicos familiares'
      ]
    };

    // 2. Preparar datos del email
    const emailParams = {
      userName: params.userName,
      userRole: params.userRole,
      welcomeMessage: roleMessages[params.userRole as keyof typeof roleMessages] || roleMessages.patient,
      nextSteps: nextStepsByRole[params.userRole as keyof typeof nextStepsByRole] || nextStepsByRole.patient
    };

    // 3. Enviar email de bienvenida
    const result = await sendPatientEmail(
      'patient_onboarding_completed',
      params.userEmail,
      emailParams,
      {
        patientId: params.userId,
        requestId: params.requestId,
        flowType: 'onboarding_completed'
      }
    );

    if (result.success) {
      console.log('✅ Email de onboarding completado enviado');
    } else {
      console.error('❌ Error enviando email de onboarding:', result.error);
    }

    return result;

  } catch (error) {
    console.error('❌ Error en flujo de onboarding:', error);
    return {
      success: false,
      status: 'failed' as const,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

/**
 * FLUJO 3: PACIENTE CREADO DESDE CITA
 * Al agendar una cita se crea el paciente
 */
export async function handlePatientCreatedFromAppointment(params: {
  patientId: string;
  appointmentId: string;
  appointmentData: {
    title: string;
    scheduledDate: Date;
    startTime: Date;
    endTime: Date;
    doctorFirstName: string;
    doctorLastName: string;
    consultoryName: string;
  };
  shortCode: string;
}) {
  console.log('📅 Iniciando flujo: Paciente creado desde cita');
  
  try {
    // 1. Determinar destinatario del email
    const recipientInfo = await determinePreCheckinRecipient(params.patientId);
    
    // 2. Preparar datos del email de confirmación de cita
    const appointmentEmailParams = {
      patientName: `${recipientInfo.patientInfo.firstName} ${recipientInfo.patientInfo.lastName}`,
      doctorName: `${params.appointmentData.doctorFirstName} ${params.appointmentData.doctorLastName}`,
      consultoryName: params.appointmentData.consultoryName,
      appointmentDate: format(params.appointmentData.scheduledDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: es }),
      appointmentTime: format(params.appointmentData.startTime, 'HH:mm', { locale: es }),
      appointmentType: params.appointmentData.title,
      confirmationCode: params.shortCode,
      preCheckinLink: `${process.env.NEXT_PUBLIC_APP_URL}/pre-checkin/${params.appointmentId}`
    };

    // 3. Enviar email de confirmación de cita
    const appointmentResult = await sendPatientEmail(
      'appointment_created',
      recipientInfo.email,
      appointmentEmailParams,
      {
        patientId: params.patientId,
        appointmentId: params.appointmentId,
        flowType: 'appointment_creation'
      }
    );

    // 4. Si el paciente tiene email temporal, enviar también email de activación
    if (recipientInfo.patientInfo.email?.includes('@temp.local') || recipientInfo.isDependent) {
      // Generar token de activación y guardar en BD
      const activationToken = crypto.randomUUID();
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + 7);

      // Guardar invitación en BD
      await db.insert(patientInvitations).values({
        id: nanoid(),
        patientUserId: params.patientId,
        guardianEmail: recipientInfo.email,
        invitationToken: activationToken,
        status: 'pending',
        expiresAt,
        createdAt: new Date(),
      });
      
      const activationParams = {
        patientName: `${recipientInfo.patientInfo.firstName} ${recipientInfo.patientInfo.lastName}`,
        isDependent: recipientInfo.isDependent,
        guardianName: recipientInfo.isDependent ? 
          `${recipientInfo.guardianInfo?.firstName} ${recipientInfo.guardianInfo?.lastName}` : 
          undefined,
        relationship: recipientInfo.guardianInfo?.relationship,
        activationLink: `${process.env.NEXT_PUBLIC_APP_URL}/activate-account?token=${activationToken}`,
        hasAppointment: true,
        appointmentDate: format(params.appointmentData.scheduledDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: es })
      };

      await sendPatientEmail(
        'patient_created',
        recipientInfo.email,
        activationParams,
        {
          patientId: params.patientId,
          appointmentId: params.appointmentId,
          flowType: 'appointment_patient_creation'
        }
      );
      
      // Marcar invitación como enviada
      await db
        .update(appointments)
        .set({ invitationSent: true })
        .where(eq(appointments.id, params.appointmentId));
    }

    // 5. Programar recordatorio 24h antes si la cita es en más de 24h
    const hoursUntilAppointment = (params.appointmentData.startTime.getTime() - Date.now()) / (1000 * 60 * 60);
    
    if (hoursUntilAppointment > 24) {
      const reminderDate = new Date(params.appointmentData.startTime.getTime() - (24 * 60 * 60 * 1000));
      
      await scheduleEmail({
        event: 'appointment_reminder_24h',
        config: {
          to: recipientInfo.email,
          priority: 'normal'
        },
        context: {
          patientId: params.patientId,
          appointmentId: params.appointmentId
        },
        params: {
          patientName: appointmentEmailParams.patientName,
          doctorName: appointmentEmailParams.doctorName,
          consultoryName: appointmentEmailParams.consultoryName,
          appointmentDate: appointmentEmailParams.appointmentDate,
          appointmentTime: appointmentEmailParams.appointmentTime
        }
      }, reminderDate);
      
      console.log(`⏰ Recordatorio programado para ${reminderDate.toISOString()}`);
    }

    if (appointmentResult.success) {
      console.log('✅ Emails de cita y activación enviados');
    } else {
      console.error('❌ Error enviando emails de cita:', appointmentResult.error);
    }

    return appointmentResult;

  } catch (error) {
    console.error('❌ Error en flujo de cita:', error);
    return {
      success: false,
      status: 'failed' as const,
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

/**
 * FUNCIÓN UNIVERSAL - PUNTO DE ENTRADA ÚNICO
 * Esta función determina qué flujo usar basado en el contexto
 */
export async function sendPatientFlowEmails(params: {
  type: 'manual' | 'onboarding' | 'appointment';
  patientId?: string;
  userId?: string;
  appointmentId?: string;
  [key: string]: any;
}) {
  console.log(`🚀 Iniciando flujo unificado de emails: ${params.type}`);
  
  switch (params.type) {
    case 'manual':
      return handlePatientCreatedManually({
        patientId: params.patientId!,
        patientName: params.patientName,
        createdBy: params.createdBy,
        hasAppointment: params.hasAppointment,
        appointmentDate: params.appointmentDate
      });
      
    case 'onboarding':
      return handlePatientOnboardingCompleted({
        userId: params.userId!,
        userName: params.userName,
        userEmail: params.userEmail,
        userRole: params.userRole,
        requestId: params.requestId
      });
      
    case 'appointment':
      return handlePatientCreatedFromAppointment({
        patientId: params.patientId!,
        appointmentId: params.appointmentId!,
        appointmentData: params.appointmentData,
        shortCode: params.shortCode
      });
      
    default:
      throw new Error(`Tipo de flujo no soportado: ${params.type}`);
  }
}