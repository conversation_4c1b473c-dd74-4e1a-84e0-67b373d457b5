import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, consultories, userRoles, medicalSpecialties } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { formatPhoneForStorage, formatPhoneForDisplay } from '@/lib/phone-utils';

// GET - Obtener perfil del doctor
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener información básica del usuario
    const userData = await db.select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (userData.length === 0) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const userInfo = userData[0];

    // Obtener información del rol de doctor
    const doctorRoleData = await db.select({
      id: userRoles.id,
      consultoryId: userRoles.consultoryId,
      specialtyId: userRoles.specialtyId,
      medicalLicense: userRoles.medicalLicense,
      roleData: userRoles.roleData,
      consultoryName: consultories.name,
      consultoryAddress: consultories.address,
      specialtyName: medicalSpecialties.name,
    })
    .from(userRoles)
    .leftJoin(consultories, eq(userRoles.consultoryId, consultories.id))
    .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
    .where(
      and(
        eq(userRoles.userId, userId),
        eq(userRoles.role, 'doctor'),
        eq(userRoles.status, 'active')
      )
    )
    .limit(1);

    // Obtener todos los roles del usuario
    const allRoles = await db.select({
      role: userRoles.role,
    })
    .from(userRoles)
    .where(
      and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'active')
      )
    );

    const doctorRole = doctorRoleData[0];

    // Formatear la respuesta
    const profile = {
      id: userInfo.id,
      firstName: userInfo.firstName,
      lastName: userInfo.lastName,
      email: userInfo.email,
      phone: userInfo.phone ? formatPhoneForDisplay(userInfo.phone) : null,
      licenseNumber: doctorRole?.medicalLicense || null,
      specialties: doctorRole?.specialtyName ? [doctorRole.specialtyName] : [],
      roles: allRoles.map(r => r.role),
      consultory: doctorRole?.consultoryId ? {
        id: doctorRole.consultoryId,
        name: doctorRole.consultoryName,
        address: doctorRole.consultoryAddress,
      } : null,
    };

    return NextResponse.json({
      success: true,
      data: profile,
    });

  } catch (error) {
    console.error('Error fetching doctor profile:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar perfil del doctor
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      firstName,
      lastName,
      phone,
      licenseNumber,
      specialties,
      bio,
    } = body;

    // Validaciones básicas
    if (!firstName || !lastName) {
      return NextResponse.json(
        { error: 'Nombre y apellido son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que el usuario existe
    const existingUser = await db.select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Actualizar información básica del usuario
    const updatedUser = await db.update(user)
      .set({
        firstName,
        lastName,
        phone: phone ? formatPhoneForStorage(phone) : null,
        updatedAt: new Date(),
      })
      .where(eq(user.id, userId))
      .returning();

    // Actualizar información específica del doctor en userRoles
    const doctorRole = await db.select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (doctorRole.length > 0) {
      // Procesar especialidades - convertir nombres a IDs
      let specialtyId = null;
      if (specialties && specialties.length > 0) {
        const specialty = await db.select()
          .from(medicalSpecialties)
          .where(eq(medicalSpecialties.name, specialties[0]))
          .limit(1);
        
        if (specialty.length > 0) {
          specialtyId = specialty[0].id;
        }
      }

      // Actualizar rol de doctor
      await db.update(userRoles)
        .set({
          medicalLicense: licenseNumber || null,
          specialtyId: specialtyId,
          roleData: { bio: bio || null },
          updatedAt: new Date(),
        })
        .where(eq(userRoles.id, doctorRole[0].id));
    }

    return NextResponse.json({
      success: true,
      data: updatedUser[0],
      message: 'Perfil actualizado correctamente',
    });

  } catch (error) {
    console.error('Error updating doctor profile:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}