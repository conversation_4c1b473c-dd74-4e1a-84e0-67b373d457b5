import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { eq, and, desc } from 'drizzle-orm';
import { sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🔍 Buscando información de rechazo para usuario:', userId);

    // 1. Primero buscar en userRoles rechazados (prioridad alta)
    const rejectedRoles = await db.execute(sql`
      SELECT role, status, "rejectionReason", "rejectedAt" as "reviewedAt"
      FROM user_roles 
      WHERE "userId" = ${userId} AND status = 'rejected'
      ORDER BY "rejectedAt" DESC
      LIMIT 1
    `);

    // 2. Si no hay roles rechazados, buscar en solicitudes rechazadas (respaldo)
    let rejectionInfo = null;
    
    if (rejectedRoles.rows.length > 0) {
      const roleData = rejectedRoles.rows[0];
      rejectionInfo = {
        reason: (roleData.rejectionReason as string) || 'No se especificó razón',
        notes: null,
        rejectedAt: roleData.reviewedAt,
        rejectedBy: null, // userRoles no tiene rejectedBy, solo rejectedAt
        role: roleData.role as string,
        source: 'userRoles'
      };
      console.log('✅ Información de rechazo encontrada en userRoles:', rejectionInfo);
    } else {
      // Buscar en solicitudes rechazadas
      const rejectedRequests = await db.execute(sql`
        SELECT role, status, "rejectionReason", "reviewedAt", "reviewedBy", "reviewNotes"
        FROM "registrationRequests" 
        WHERE "userId" = ${userId} AND status = 'rejected'
        ORDER BY "reviewedAt" DESC
        LIMIT 1
      `);

      if (rejectedRequests.rows.length > 0) {
        const requestData = rejectedRequests.rows[0];
        rejectionInfo = {
          reason: (requestData.rejectionReason as string) || 'No se especificó razón',
          notes: (requestData.reviewNotes as string) || null,
          rejectedAt: requestData.reviewedAt,
          rejectedBy: (requestData.reviewedBy as string) || null,
          role: requestData.role as string,
          source: 'registrationRequests'
        };
        console.log('✅ Información de rechazo encontrada en registrationRequests:', rejectionInfo);
      }
    }

    if (!rejectionInfo) {
      console.log('❌ No se encontró información de rechazo para el usuario');
      return NextResponse.json(
        { success: false, message: 'No se encontró información de rechazo' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: rejectionInfo
    });

  } catch (error) {
    console.error('❌ Error fetching rejection info:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}