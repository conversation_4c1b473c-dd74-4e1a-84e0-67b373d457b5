import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalRecords, user, consultories, medicalConsultations, patientMedicalHistory, userRoles } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or, gte, lte, sql } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET - Listar expedientes médicos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar rol del usuario
    const userRoleRecord = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, userId))
      .limit(1);

    if (!userRoleRecord.length) {
      return NextResponse.json({ error: 'Usuario sin roles asignados' }, { status: 403 });
    }

    const userRole = userRoleRecord[0].role;

    // Solo doctores, asistentes y admins pueden ver expedientes
    if (!['doctor', 'assistant', 'admin'].includes(userRole)) {
      return NextResponse.json({ error: 'No autorizado para ver expedientes' }, { status: 403 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const status = searchParams.get('status');
    const doctorId = searchParams.get('doctorId');
    const consultoryId = searchParams.get('consultoryId');
    const patientId = searchParams.get('patientId');
    const isMinor = searchParams.get('isMinor');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const orderBy = searchParams.get('orderBy') || 'createdAt';
    const orderDirection = searchParams.get('orderDirection') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
    const offset = (page - 1) * limit;

    // Query base con información enriquecida
    let query = db
      .select({
        // Información básica del expediente
        id: medicalRecords.id,
        recordNumber: medicalRecords.recordNumber,
        openDate: medicalRecords.openDate,
        status: medicalRecords.status,
        patientSummary: medicalRecords.patientSummary,
        isMinor: medicalRecords.isMinor,
        guardianInfo: medicalRecords.guardianInfo,
        lastAccessDate: medicalRecords.lastAccessDate,
        totalConsultations: medicalRecords.totalConsultations,
        
        // IDs de relaciones
        patientId: medicalRecords.patientId,
        primaryDoctorId: medicalRecords.primaryDoctorId,
        consultoryId: medicalRecords.consultoryId,
        
        // Auditoría
        createdAt: medicalRecords.createdAt,
        updatedAt: medicalRecords.updatedAt,
        createdBy: medicalRecords.createdBy,
        updatedBy: medicalRecords.updatedBy,
      })
      .from(medicalRecords);

    // Aplicar filtros
    const conditions = [];
    
    // Filtro por rol: doctores solo ven sus propios expedientes
    if (userRole === 'doctor') {
      conditions.push(eq(medicalRecords.primaryDoctorId, userId));
    }
    
    if (search) {
      conditions.push(
        or(
          ilike(medicalRecords.recordNumber, `%${search}%`),
          sql`${medicalRecords.patientSummary}->>'fullName' ILIKE ${`%${search}%`}`
        )
      );
    }

    if (status && status !== 'all') {
      conditions.push(eq(medicalRecords.status, status));
    }

    if (doctorId && doctorId !== 'all') {
      conditions.push(eq(medicalRecords.primaryDoctorId, doctorId));
    }

    if (consultoryId && consultoryId !== 'all') {
      conditions.push(eq(medicalRecords.consultoryId, consultoryId));
    }

    if (patientId && patientId !== 'all') {
      conditions.push(eq(medicalRecords.patientId, patientId));
    }

    if (isMinor === 'true') {
      conditions.push(eq(medicalRecords.isMinor, true));
    } else if (isMinor === 'false') {
      conditions.push(eq(medicalRecords.isMinor, false));
    }

    if (dateFrom) {
      const fromDate = new Date(dateFrom + 'T00:00:00.000Z');
      conditions.push(gte(medicalRecords.openDate, fromDate));
    }

    if (dateTo) {
      const toDate = new Date(dateTo + 'T23:59:59.999Z');
      conditions.push(lte(medicalRecords.openDate, toDate));
    }

    // Aplicar condiciones si existen
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'recordNumber' ? medicalRecords.recordNumber :
                       orderBy === 'openDate' ? medicalRecords.openDate :
                       orderBy === 'status' ? medicalRecords.status :
                       orderBy === 'totalConsultations' ? medicalRecords.totalConsultations :
                       medicalRecords.createdAt;

    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query
    const result = await query;

    // Enriquecer con datos de pacientes, doctores y consultorios
    const enrichedResults = await Promise.all(
      result.map(async (record) => {
        let patientName = '';
        let patientAge = 0;
        let primaryDoctorName = '';
        let consultoryName = '';
        let lastConsultationDate = undefined;
        
        // Obtener información del paciente
        if (record.patientId) {
          try {
            const patientData = await db
              .select({
                firstName: user.firstName,
                lastName: user.lastName,
                dateOfBirth: user.dateOfBirth,
              })
              .from(user)
              .where(eq(user.id, record.patientId))
              .limit(1);
            
            if (patientData.length > 0) {
              const patient = patientData[0];
              patientName = `${patient.firstName} ${patient.lastName}`;
              
              // Calcular edad
              if (patient.dateOfBirth) {
                const today = new Date();
                const birthDate = new Date(patient.dateOfBirth);
                patientAge = today.getFullYear() - birthDate.getFullYear();
                const monthDiff = today.getMonth() - birthDate.getMonth();
                if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                  patientAge--;
                }
              }
            }
          } catch (error) {
            console.error('Error fetching patient data:', error);
          }
        }
        
        // Obtener información del médico primario
        if (record.primaryDoctorId) {
          try {
            const doctorData = await db
              .select({
                firstName: user.firstName,
                lastName: user.lastName,
              })
              .from(user)
              .where(eq(user.id, record.primaryDoctorId))
              .limit(1);
            
            if (doctorData.length > 0) {
              const doctor = doctorData[0];
              primaryDoctorName = `${doctor.firstName} ${doctor.lastName}`;
            }
          } catch (error) {
            console.error('Error fetching doctor data:', error);
          }
        }
        
        // Obtener información del consultorio
        if (record.consultoryId) {
          try {
            const consultoryData = await db
              .select({
                name: consultories.name,
              })
              .from(consultories)
              .where(eq(consultories.id, record.consultoryId))
              .limit(1);
            
            if (consultoryData.length > 0) {
              consultoryName = consultoryData[0].name;
            }
          } catch (error) {
            console.error('Error fetching consultory data:', error);
          }
        }

        // Obtener fecha de última consulta
        if (record.lastAccessDate) {
          lastConsultationDate = record.lastAccessDate.toISOString();
        }
        
        return {
          id: record.id,
          recordNumber: record.recordNumber,
          patientName,
          patientAge,
          status: record.status || 'active',
          openDate: record.openDate?.toISOString() || new Date().toISOString(),
          totalConsultations: record.totalConsultations || 0,
          lastConsultationDate,
          primaryDoctorName,
          consultoryName,
        };
      })
    );

    // Obtener conteo total
    let countQuery = db.select({ count: count() }).from(medicalRecords);
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Obtener estadísticas adicionales
    const statsQuery = await db
      .select({
        status: medicalRecords.status,
        count: count(),
      })
      .from(medicalRecords)
      .groupBy(medicalRecords.status);

    const stats = {
      total,
      byStatus: statsQuery.reduce((acc, stat) => {
        acc[stat.status] = stat.count;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      data: enrichedResults,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching medical records:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Crear nuevo expediente médico
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar rol del usuario
    const userRoleRecord = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, userId))
      .limit(1);

    if (!userRoleRecord.length) {
      return NextResponse.json({ error: 'Usuario sin roles asignados' }, { status: 403 });
    }

    const userRole = userRoleRecord[0].role;

    // Solo doctores, asistentes y admins pueden crear expedientes
    if (!['doctor', 'assistant', 'admin'].includes(userRole)) {
      return NextResponse.json({ error: 'No autorizado para crear expedientes' }, { status: 403 });
    }

    const body = await request.json();
    const {
      patientId,
      consultoryId,
      primaryDoctorId,
      patientSummary,
      isMinor,
      guardianInfo,
    } = body;

    // Validaciones básicas
    if (!patientId || !consultoryId || !primaryDoctorId) {
      return NextResponse.json({ 
        error: 'Campos requeridos: patientId, consultoryId, primaryDoctorId' 
      }, { status: 400 });
    }

    // Verificar que el paciente existe
    const patientResult = await db
      .select()
      .from(user)
      .where(eq(user.id, patientId))
      .limit(1);

    if (patientResult.length === 0) {
      return NextResponse.json({ error: 'Paciente no encontrado' }, { status: 400 });
    }

    // Verificar que el médico existe
    const doctorResult = await db
      .select()
      .from(user)
      .where(eq(user.id, primaryDoctorId))
      .limit(1);

    if (doctorResult.length === 0) {
      return NextResponse.json({ error: 'Médico no encontrado' }, { status: 400 });
    }

    // Verificar que el consultorio existe
    const consultoryResult = await db
      .select()
      .from(consultories)
      .where(and(eq(consultories.id, consultoryId), eq(consultories.isActive, true)))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado o inactivo' }, { status: 400 });
    }

    // Verificar que no existe expediente activo para el paciente
    const existingRecord = await db
      .select()
      .from(medicalRecords)
      .where(and(
        eq(medicalRecords.patientId, patientId),
        eq(medicalRecords.status, 'active')
      ))
      .limit(1);

    if (existingRecord.length > 0) {
      return NextResponse.json({ 
        error: 'El paciente ya tiene un expediente activo',
        existingRecordId: existingRecord[0].id
      }, { status: 400 });
    }

    // Generar número de expediente único
    const recordNumber = await generateRecordNumber();

    // Crear el nuevo expediente
    const newRecord = {
      id: nanoid(),
      patientId,
      consultoryId,
      primaryDoctorId,
      recordNumber,
      openDate: new Date(),
      status: 'active' as const,
      patientSummary: patientSummary || {},
      isMinor: isMinor || false,
      guardianInfo: guardianInfo || null,
      totalConsultations: 0,
      createdBy: userId,
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(medicalRecords).values(newRecord).returning();

    // Crear estructura inicial de antecedentes médicos
    await db.insert(patientMedicalHistory).values({
      id: nanoid(),
      medicalRecordId: result[0].id,
      pathologicalHistory: [],
      nonPathologicalHistory: [],
      familyHistory: [],
      allergies: [],
      hospitalizations: [],
      surgeries: [],
      vaccinations: [],
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      message: 'Expediente médico creado exitosamente',
      data: result[0],
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating medical record:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// Función auxiliar para generar número de expediente único
async function generateRecordNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `EXP-${year}-`;
  
  // Buscar el último número para este año
  const lastRecord = await db
    .select({ recordNumber: medicalRecords.recordNumber })
    .from(medicalRecords)
    .where(ilike(medicalRecords.recordNumber, `${prefix}%`))
    .orderBy(desc(medicalRecords.recordNumber))
    .limit(1);

  if (lastRecord.length === 0) {
    return `${prefix}001`;
  }

  // Extraer el número secuencial y incrementar
  const lastNumber = lastRecord[0].recordNumber.split('-')[2];
  const nextNumber = (parseInt(lastNumber) + 1).toString().padStart(3, '0');
  
  return `${prefix}${nextNumber}`;
}