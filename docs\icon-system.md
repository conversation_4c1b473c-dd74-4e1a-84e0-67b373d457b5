# 🎨 Sistema de Iconos Consistente

## Problema Resuelto
La aplicación tenía iconos con colores inconsistentes usando diferentes enfoques:
- `text-blue-600` (clases hardcodeadas de Tailwind)
- `text-primary` (variables CSS que no siempre funcionaban)
- Colores específicos como `text-emerald-600`

## Solución Implementada

### 1. Clases de Utilidad CSS (`globals.css`)
```css
@layer utilities {
  /* Variantes de color */
  .icon-primary    { @apply text-emerald-600; }
  .icon-secondary  { @apply text-teal-600; }
  .icon-accent     { @apply text-emerald-500; }
  .icon-muted      { @apply text-gray-500; }
  .icon-success    { @apply text-green-600; }
  .icon-warning    { @apply text-yellow-600; }
  .icon-error      { @apply text-red-600; }
  .icon-info       { @apply text-blue-600; }
  
  /* Tamaños estándar */
  .icon-xs  { @apply h-3 w-3; }
  .icon-sm  { @apply h-4 w-4; }
  .icon-md  { @apply h-5 w-5; }
  .icon-lg  { @apply h-6 w-6; }
  .icon-xl  { @apply h-8 w-8; }
}
```

### 2. Componente Icon (`components/ui/icon.tsx`)
```tsx
import { Icon } from '@/components/ui/icon';

// Uso básico
<Icon icon={Mail} variant="primary" size="md" />

// Con clases adicionales
<Icon icon={Phone} variant="success" size="lg" className="animate-spin" />
```

### 3. Hook para Clases Directas
```tsx
import { useIconClasses } from '@/components/ui/icon';

const iconClasses = useIconClasses('primary', 'md');
<Mail className={iconClasses} />
```

### 4. Constantes para Uso Directo
```tsx
import { ICON_VARIANTS, ICON_SIZES } from '@/components/ui/icon';

<Calendar className={`${ICON_VARIANTS.primary} ${ICON_SIZES.md}`} />
```

## Migración Realizada

### ✅ Archivos Actualizados:
- `app/(dashboard)/dashboard/doctor/profile/page.tsx`
- `app/(dashboard)/dashboard/doctor/agenda/appointment/[[...params]]/page.tsx`

### 🔄 Cambios Realizados:

**Antes:**
```tsx
<Mail className="h-8 w-8 text-primary" />
<Calendar className="h-5 w-5 text-blue-600" />
<RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
```

**Después:**
```tsx
<Mail className="icon-xl icon-primary" />
<Calendar className="icon-md icon-primary" />
<RefreshCw className="icon-xl icon-primary animate-spin" />
```

## Patrones de Uso

### 1. Iconos de Card Headers
```tsx
<CardTitle className="text-lg flex items-center gap-2">
  <User className="icon-md icon-primary" />
  Título de la Card  
</CardTitle>
```

### 2. Iconos en Labels de Formulario
```tsx
<Label className="text-sm font-medium text-gray-700">
  <Calendar className="icon-sm icon-primary inline mr-1" />
  Fecha <span className="text-red-500">*</span>
</Label>
```

### 3. Iconos de Estado/Loading
```tsx
<RefreshCw className="icon-xl icon-primary animate-spin" />
```

### 4. Iconos Grandes en Cards de Información
```tsx
<Mail className="icon-xl icon-primary" />
<Phone className="icon-xl icon-primary" />  
<Shield className="icon-xl icon-primary" />
```

## Variantes de Color - Sistema Semántico  

| Variante | Color | Uso | Ejemplos |
|----------|-------|-----|----------|
| `icon-user` | Azul | Información personal, usuarios | User, Profile, Person |
| `icon-contact` | Púrpura | Comunicación, contacto | Mail, Phone, Message |  
| `icon-document` | Verde | Documentación, archivos | FileText, Shield, IdCard |
| `icon-time` | Azul | Tiempo, calendario, fechas | Calendar, Clock, Schedule |
| `icon-medical` | Verde esmeralda | Médico, salud | Stethoscope, Heart, Medical |
| `icon-location` | Naranja | Ubicación, lugares | MapPin, Home, Building |
| `icon-muted` | Gris | Deshabilitado, secundario | Iconos inactivos |
| `icon-success` | Verde | Éxito, dinero, completado | Check, DollarSign |
| `icon-warning` | Amarillo | Advertencias | AlertTriangle |
| `icon-error` | Rojo | Errores, peligro | X, AlertCircle |
| `icon-info` | Azul | Información, ayuda | Info, Settings, Help |

## Tamaños Estándar

| Tamaño | Dimensiones | Uso |
|--------|-------------|-----|
| `icon-xs` | 12px (3x3) | Iconos muy pequeños, inline text |
| `icon-sm` | 16px (4x4) | Labels, botones pequeños |
| `icon-md` | 20px (5x5) | Card headers, navegación |
| `icon-lg` | 24px (6x6) | Botones principales |
| `icon-xl` | 32px (8x8) | Cards de información, loading |

## Próximos Pasos

### 🎯 Archivos Pendientes de Migrar:
- Sidebar/navegación principal
- Formularios de pacientes
- Dashboard principal
- Modales y diálogos
- Botones de acción
- Agenda/calendario

### 📋 Checklist de Migración:
1. ✅ Crear sistema de clases CSS
2. ✅ Crear componente Icon
3. ✅ Migrar perfil del doctor
4. ✅ Migrar formulario de citas
5. ⏳ Migrar sidebar principal
6. ⏳ Migrar dashboard principal
7. ⏳ Migrar formularios de pacientes
8. ⏳ Audit completo de la aplicación

## Beneficios Obtenidos

### ✅ Consistencia Visual
- Todos los iconos usan la misma paleta de colores
- Tamaños estandarizados y predecibles
- Apariencia uniforme en toda la aplicación

### ✅ Mantenibilidad
- Cambio de colores centralizado en un solo lugar
- Fácil agregar nuevas variantes
- Menos código duplicado

### ✅ Escalabilidad  
- Sistema preparado para temas (dark mode)
- Fácil agregar nuevos tamaños o variantes
- Componente reutilizable en toda la app

### ✅ Developer Experience
- IntelliSense para variantes y tamaños
- Menos decisiones que tomar (qué color usar)
- Código más legible y semántico