'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Stethoscope, Building, Clock, Upload, FileText, CheckCircle, AlertCircle } from 'lucide-react';
import { DoctorSpecificData, WorkSchedule, Consultory } from '@/lib/types/onboarding';
import { validateDocumentFile } from '@/lib/upload-helpers';

interface DoctorSpecificFormProps {
  data: Partial<DoctorSpecificData>;
  onChange: (data: Partial<DoctorSpecificData>) => void;
  onNext: () => void;
  onBack: () => void;
}

interface MedicalSpecialty {
  id: number;
  name: string;
  code: string;
}

const DAYS_OF_WEEK = [
  { key: 'monday', label: 'Lunes' },
  { key: 'tuesday', label: 'Martes' },
  { key: 'wednesday', label: 'Miércoles' },
  { key: 'thursday', label: 'Jueves' },
  { key: 'friday', label: 'Viernes' },
  { key: 'saturday', label: 'Sábado' },
  { key: 'sunday', label: 'Domingo' },
];

export function DoctorSpecificForm({ data, onChange, onNext, onBack }: DoctorSpecificFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [consultories, setConsultories] = useState<Consultory[]>([]);
  const [loadingConsultories, setLoadingConsultories] = useState(true);
  const [specialties, setSpecialties] = useState<MedicalSpecialty[]>([]);
  const [loadingSpecialties, setLoadingSpecialties] = useState(true);
  const [documents, setDocuments] = useState<{
    licensePhoto?: File;
    diplomaPhoto?: File;
    cvPdf?: File;
  }>({});

  // Cargar consultorios y especialidades disponibles
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Cargar consultorios
        const consultoriesResponse = await fetch('/api/onboarding/consultories');
        const consultoriesResult = await consultoriesResponse.json();
        if (consultoriesResult.success) {
          setConsultories(consultoriesResult.data);
        }

        // Cargar especialidades
        const specialtiesResponse = await fetch('/api/catalogs/medical-specialties');
        const specialtiesResult = await specialtiesResponse.json();
        if (specialtiesResult && specialtiesResult.data) {
          setSpecialties(specialtiesResult.data);
        }
      } catch (error) {
        console.error('Error cargando datos:', error);
      } finally {
        setLoadingConsultories(false);
        setLoadingSpecialties(false);
      }
    };

    fetchData();
  }, []);

  // Asegurar que workSchedule siempre exista
  const ensureWorkSchedule = (): WorkSchedule => {
    if (!data.workSchedule || Object.keys(data.workSchedule).length === 0) {
      const defaultSchedule: WorkSchedule = {};
      DAYS_OF_WEEK.forEach(day => {
        defaultSchedule[day.key] = {
          start: '08:00',
          end: '17:00',
          available: false
        };
      });
      return defaultSchedule;
    }
    return data.workSchedule;
  };

  // Inicializar horarios si no existen
  useEffect(() => {
    const currentSchedule = ensureWorkSchedule();
    if (!data.workSchedule || Object.keys(data.workSchedule).length === 0) {
      onChange({ ...data, workSchedule: currentSchedule });
    }
  }, []);

  // Debug: Log para verificar el estado
  useEffect(() => {
    console.log('Doctor form - WorkSchedule state:', data.workSchedule);
    console.log('Doctor form - All data:', data);
  }, [data]);

  const handleInputChange = (field: keyof DoctorSpecificData, value: any) => {
    onChange({ ...data, [field]: value });
    // Limpiar error si existe
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const handleScheduleChange = (day: string, field: 'start' | 'end' | 'available', value: string | boolean) => {
    console.log(`Changing schedule: ${day}.${field} = ${value}`);
    
    const currentSchedule = ensureWorkSchedule();
    const currentDaySchedule = currentSchedule[day] || { start: '08:00', end: '17:00', available: false };
    
    const updatedSchedule = {
      ...currentSchedule,
      [day]: {
        ...currentDaySchedule,
        [field]: value
      }
    };
    
    console.log('Updated schedule:', updatedSchedule);
    handleInputChange('workSchedule', updatedSchedule);
  };

  const handleFileChange = (field: 'licensePhoto' | 'diplomaPhoto' | 'cvPdf', file: File | null) => {
    if (file) {
      // Validar el archivo
      const validation = validateDocumentFile(file, field);
      if (!validation.valid) {
        setErrors({ ...errors, [field]: validation.error });
        return;
      }
      
      // Limpiar error si existe
      if (errors[field]) {
        const newErrors = { ...errors };
        delete newErrors[field];
        setErrors(newErrors);
      }
      
      setDocuments({ ...documents, [field]: file });
    } else {
      // Remover archivo
      const newDocuments = { ...documents };
      delete newDocuments[field];
      setDocuments(newDocuments);
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validaciones requeridas
    if (!data.medicalLicense?.trim()) newErrors.medicalLicense = 'Número de licencia médica es requerido';
    if (!data.specialty) newErrors.specialty = 'Especialidad es requerida';

    // Validar año de graduación solo si se proporciona
    const currentYear = new Date().getFullYear();
    if (data.graduationYear && (data.graduationYear < 1980 || data.graduationYear > currentYear)) {
      newErrors.graduationYear = `Año debe estar entre 1980 y ${currentYear}`;
    }

    // Validar que tenga al menos un día disponible
    const schedule = data.workSchedule;
    if (schedule) {
      const hasAvailableDay = Object.values(schedule).some(day => day.available);
      if (!hasAvailableDay) {
        newErrors.workSchedule = 'Debe seleccionar al menos un día de disponibilidad';
      }
    }

    // Documentos opcionales para testing
    // if (!documents.licensePhoto) newErrors.licensePhoto = 'Foto de licencia médica es requerida';
    // if (!documents.diplomaPhoto) newErrors.diplomaPhoto = 'Foto de diploma es requerida';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      // Agregar documentos a los datos
      const dataWithDocuments = {
        ...data,
        documents: documents
      };
      onChange(dataWithDocuments);
      onNext();
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Información Profesional */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Stethoscope className="h-5 w-5 text-blue-600" />
            <span>Información Profesional</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Licencia Médica */}
          <div className="space-y-2">
            <Label htmlFor="medicalLicense">Número de Licencia Médica *</Label>
            <Input
              id="medicalLicense"
              value={data.medicalLicense || ''}
              onChange={(e) => handleInputChange('medicalLicense', e.target.value)}
              className={errors.medicalLicense ? 'border-red-500' : ''}
              placeholder="Ej: 12345-MD-GT"
            />
            {errors.medicalLicense && <p className="text-red-500 text-sm">{errors.medicalLicense}</p>}
          </div>

          {/* Especialidad */}
          <div className="space-y-2">
            <Label>Especialidad Principal *</Label>
            <Select 
              value={data.specialty} 
              onValueChange={(value) => handleInputChange('specialty', value)}
            >
              <SelectTrigger className={errors.specialty ? 'border-red-500' : ''}>
                <SelectValue placeholder="Seleccionar especialidad" />
              </SelectTrigger>
              <SelectContent>
                {loadingSpecialties ? (
                  <SelectItem value="loading">Cargando especialidades...</SelectItem>
                ) : Array.isArray(specialties) && specialties.length > 0 ? (
                  specialties.map(specialty => (
                    <SelectItem key={specialty.id} value={specialty.name}>
                      {specialty.name}
                    </SelectItem>
                  ))
                ) : (
                  <SelectItem value="no-specialties">No hay especialidades disponibles</SelectItem>
                )}
              </SelectContent>
            </Select>
            {errors.specialty && <p className="text-red-500 text-sm">{errors.specialty}</p>}
          </div>

          {/* Sub-especialidades */}
          <div className="space-y-2">
            <Label htmlFor="subSpecialties">Sub-especialidades (opcional)</Label>
            <Textarea
              id="subSpecialties"
              value={typeof data.subSpecialties === 'string' ? data.subSpecialties : data.subSpecialties?.join(', ') || ''}
              onChange={(e) => {
                // Guardar como string para permitir escritura libre
                const value = e.target.value;
                handleInputChange('subSpecialties', value);
              }}
              onBlur={(e) => {
                // Al perder el foco, convertir a array para el almacenamiento final
                const value = e.target.value;
                const arrayValue = value.split(',').map(s => s.trim()).filter(s => s);
                handleInputChange('subSpecialties', arrayValue);
              }}
              placeholder="Ej: Cuidados Intensivos, Emergencias Pediátricas (separadas por comas)"
              rows={2}
            />
            <p className="text-xs text-gray-500">Separa múltiples especialidades con comas</p>
          </div>

          {/* Universidad y Año */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="university">Universidad (opcional)</Label>
              <Input
                id="university"
                value={data.university || ''}
                onChange={(e) => handleInputChange('university', e.target.value)}
                className={errors.university ? 'border-red-500' : ''}
                placeholder="Universidad de San Carlos"
              />
              {errors.university && <p className="text-red-500 text-sm">{errors.university}</p>}
            </div>
            <div className="space-y-2">
              <Label htmlFor="graduationYear">Año de Graduación (opcional)</Label>
              <Input
                id="graduationYear"
                type="number"
                min="1980"
                max={new Date().getFullYear()}
                value={data.graduationYear || ''}
                onChange={(e) => handleInputChange('graduationYear', parseInt(e.target.value))}
                className={errors.graduationYear ? 'border-red-500' : ''}
                placeholder="2015"
              />
              {errors.graduationYear && <p className="text-red-500 text-sm">{errors.graduationYear}</p>}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Consultorio */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-blue-600" />
            <span>Asociación a Consultorio</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label>Consultorio donde trabajará *</Label>
            {loadingConsultories ? (
              <div className="text-center py-4">Cargando consultorios...</div>
            ) : (
              <Select 
                value={data.consultoryId} 
                onValueChange={(value) => handleInputChange('consultoryId', value)}
              >
                <SelectTrigger className={errors.consultoryId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Seleccionar consultorio" />
                </SelectTrigger>
                <SelectContent>
                  {consultories.map(consultory => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      <div>
                        <div className="font-medium">{consultory.name}</div>
                        {consultory.address && (
                          <div className="text-sm text-gray-500">{consultory.address}</div>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            )}
            {errors.consultoryId && <p className="text-red-500 text-sm">{errors.consultoryId}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Horarios de Trabajo */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Clock className="h-5 w-5 text-blue-600" />
            <span>Horarios de Disponibilidad</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {DAYS_OF_WEEK.map(day => {
              const currentSchedule = ensureWorkSchedule();
              const daySchedule = currentSchedule[day.key] || { start: '08:00', end: '17:00', available: false };
              
              return (
                <div key={day.key} className="flex items-center space-x-6 p-4 border-2 rounded-lg bg-white hover:bg-gray-50/50 transition-colors">
                  <div className="flex items-center space-x-3 min-w-[160px]">
                    <Checkbox
                      id={`day-${day.key}`}
                      checked={daySchedule.available}
                      onCheckedChange={(checked) => {
                        console.log(`Checkbox ${day.key} changed to:`, checked);
                        handleScheduleChange(day.key, 'available', checked as boolean)
                      }}
                      className="w-5 h-5 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 border-2 border-green-300 hover:border-green-400"
                    />
                    <Label 
                      htmlFor={`day-${day.key}`}
                      className="font-medium text-gray-700 cursor-pointer select-none hover:text-blue-600 transition-colors"
                    >
                      {day.label}
                    </Label>
                  </div>
                  
                  {daySchedule.available && (
                    <div className="flex items-center space-x-4 flex-1 animate-in slide-in-from-left duration-200">
                      <Label className="text-sm font-medium text-gray-600 min-w-[50px]">Desde:</Label>
                      <Input
                        type="time"
                        value={daySchedule.start}
                        onChange={(e) => handleScheduleChange(day.key, 'start', e.target.value)}
                        className="w-32 h-10 border-2 focus:border-blue-500"
                      />
                      <Label className="text-sm font-medium text-gray-600 min-w-[50px]">Hasta:</Label>
                      <Input
                        type="time"
                        value={daySchedule.end}
                        onChange={(e) => handleScheduleChange(day.key, 'end', e.target.value)}
                        className="w-32 h-10 border-2 focus:border-blue-500"
                      />
                    </div>
                  )}
                </div>
              );
            })}
            {errors.workSchedule && <p className="text-red-500 text-sm">{errors.workSchedule}</p>}
            
            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <p className="text-sm text-blue-700">
                💡 <strong>Consejo:</strong> Selecciona los días que estarás disponible para consultas médicas. 
                Puedes modificar estos horarios más tarde desde tu perfil.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documentos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-blue-600" />
            <span>Documentos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Licencia Médica */}
          <div className="space-y-2">
            <Label htmlFor="licensePhoto" className="flex items-center space-x-2">
              <span>Foto de Licencia Médica (JPG, PNG)</span>
              {documents.licensePhoto && <CheckCircle className="h-4 w-4 text-green-500" />}
            </Label>
            <Input
              id="licensePhoto"
              type="file"
              accept="image/*"
              onChange={(e) => handleFileChange('licensePhoto', e.target.files?.[0] || null)}
              className={errors.licensePhoto ? 'border-red-500' : documents.licensePhoto ? 'border-green-500' : ''}
            />
            {documents.licensePhoto && (
              <p className="text-sm text-green-600 flex items-center space-x-1">
                <CheckCircle className="h-3 w-3" />
                <span>Archivo seleccionado: {documents.licensePhoto.name}</span>
              </p>
            )}
            {errors.licensePhoto && (
              <p className="text-red-500 text-sm flex items-center space-x-1">
                <AlertCircle className="h-3 w-3" />
                <span>{errors.licensePhoto}</span>
              </p>
            )}
          </div>

          {/* Diploma */}
          <div className="space-y-2">
            <Label htmlFor="diplomaPhoto" className="flex items-center space-x-2">
              <span>Foto de Diploma (JPG, PNG)</span>
              {documents.diplomaPhoto && <CheckCircle className="h-4 w-4 text-green-500" />}
            </Label>
            <Input
              id="diplomaPhoto"
              type="file"
              accept="image/*"
              onChange={(e) => handleFileChange('diplomaPhoto', e.target.files?.[0] || null)}
              className={errors.diplomaPhoto ? 'border-red-500' : documents.diplomaPhoto ? 'border-green-500' : ''}
            />
            {documents.diplomaPhoto && (
              <p className="text-sm text-green-600 flex items-center space-x-1">
                <CheckCircle className="h-3 w-3" />
                <span>Archivo seleccionado: {documents.diplomaPhoto.name}</span>
              </p>
            )}
            {errors.diplomaPhoto && (
              <p className="text-red-500 text-sm flex items-center space-x-1">
                <AlertCircle className="h-3 w-3" />
                <span>{errors.diplomaPhoto}</span>
              </p>
            )}
          </div>

          {/* CV */}
          <div className="space-y-2">
            <Label htmlFor="cvPdf" className="flex items-center space-x-2">
              <span>Currículum Vitae (PDF, opcional)</span>
              {documents.cvPdf && <CheckCircle className="h-4 w-4 text-green-500" />}
            </Label>
            <Input
              id="cvPdf"
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileChange('cvPdf', e.target.files?.[0] || null)}
              className={documents.cvPdf ? 'border-green-500' : ''}
            />
            {documents.cvPdf && (
              <p className="text-sm text-green-600 flex items-center space-x-1">
                <CheckCircle className="h-3 w-3" />
                <span>Archivo seleccionado: {documents.cvPdf.name}</span>
              </p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <span>← Volver</span>
        </Button>
        <Button 
          onClick={handleNext}
          className="bg-blue-600 hover:bg-blue-700 flex items-center space-x-2"
        >
          <span>Continuar →</span>
        </Button>
      </div>
    </div>
  );
} 