import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { countries } from '@/db/schema';
import { and, eq, ilike, or, desc, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(countries.name, `%${search}%`),
          ilike(countries.code, `%${search}%`)
        )
      );
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(countries.isActive, active === 'true'));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(countries)
      .where(whereClause);

    // Get paginated data
    const countriesData = await db
      .select({
        id: countries.id,
        name: countries.name,
        code: countries.code,
        phoneCode: countries.phoneCode,
        currency: countries.currency,
        isActive: countries.isActive,
        createdAt: countries.createdAt,
        updatedAt: countries.updatedAt
      })
      .from(countries)
      .where(whereClause)
      .orderBy(countries.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: countriesData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching countries:', error);
    return NextResponse.json(
      { error: 'Failed to fetch countries' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, code, phoneCode, currency, isActive = true } = body;

    if (!name || !code) {
      return NextResponse.json(
        { error: 'Name and code are required' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(countries);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newCountry = await db.insert(countries).values({
      id: nextId,
      name,
      code: code.toUpperCase(),
      phoneCode,
      currency,
      isActive
    }).returning();

    return NextResponse.json(newCountry[0], { status: 201 });
  } catch (error) {
    console.error('Error creating country:', error);
    return NextResponse.json(
      { error: 'Failed to create country' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, phoneCode, currency, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedCountry = await db
      .update(countries)
      .set({
        name,
        phoneCode,
        currency,
        isActive,
        updatedAt: new Date()
      })
      .where(eq(countries.id, parseInt(id)))
      .returning();

    if (updatedCountry.length === 0) {
      return NextResponse.json(
        { error: 'Country not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedCountry[0]);
  } catch (error) {
    console.error('Error updating country:', error);
    return NextResponse.json(
      { error: 'Failed to update country' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedCountry = await db
      .update(countries)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(countries.id, parseInt(id)))
      .returning();

    if (deletedCountry.length === 0) {
      return NextResponse.json(
        { error: 'Country not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Country deleted successfully' });
  } catch (error) {
    console.error('Error deleting country:', error);
    return NextResponse.json(
      { error: 'Failed to delete country' },
      { status: 500 }
    );
  }
} 