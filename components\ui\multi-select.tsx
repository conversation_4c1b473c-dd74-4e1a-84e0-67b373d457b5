'use client';

import * as React from 'react';
import { Check, ChevronsUpDown, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';

interface MultiSelectProps {
  options: {
    value: string;
    label: string;
    color?: string;
    category?: string;
  }[];
  value: string[];
  onChange: (value: string[]) => void;
  placeholder?: string;
  emptyText?: string;
  className?: string;
}

export function MultiSelect({
  options,
  value,
  onChange,
  placeholder = 'Seleccionar...',
  emptyText = 'No se encontraron opciones.',
  className,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false);

  const handleSelect = (optionValue: string) => {
    const newValue = value.includes(optionValue)
      ? value.filter((v) => v !== optionValue)
      : [...value, optionValue];
    onChange(newValue);
  };

  const handleRemove = (optionValue: string) => {
    onChange(value.filter((v) => v !== optionValue));
  };

  const selectedOptions = options.filter((option) => value.includes(option.value));

  // Agrupar opciones por categoría
  const groupedOptions = options.reduce((acc, option) => {
    const category = option.category || 'general';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(option);
    return acc;
  }, {} as Record<string, typeof options>);

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      requirement: 'Requerimientos',
      specialty: 'Especialidades',
      type: 'Tipo de Servicio',
      duration: 'Duración',
      general: 'General',
    };
    return labels[category] || category;
  };

  const getBadgeColor = (color?: string) => {
    const colorMap: Record<string, string> = {
      red: 'bg-red-100 text-red-800 border-red-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200',
      pink: 'bg-pink-100 text-pink-800 border-pink-200',
      cyan: 'bg-cyan-100 text-cyan-800 border-cyan-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200',
    };
    return colorMap[color || 'blue'] || colorMap.blue;
  };

  return (
    <div className={cn('w-full', className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            role="combobox"
            aria-expanded={open}
            className="w-full justify-between min-h-[38px] h-auto px-3 py-2"
          >
            {selectedOptions.length > 0 ? (
              <div className="flex items-center justify-start w-full">
                <span className="text-sm truncate">
                  {selectedOptions.length} {selectedOptions.length === 1 ? 'tag seleccionado' : 'tags seleccionados'}
                </span>
              </div>
            ) : (
              <span className="text-muted-foreground text-sm">{placeholder}</span>
            )}
            <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent 
          className="w-[calc(100vw-2rem)] max-w-md p-0" 
          align="start"
          sideOffset={5}
          alignOffset={-8}
        >
          <Command className="max-h-[60vh]">
            <CommandInput placeholder="Buscar tags..." className="h-9 !border-none !ring-0 !ring-offset-0 focus:!border-none focus:!ring-0 focus:!ring-offset-0" />
            <CommandEmpty>{emptyText}</CommandEmpty>
            <div className="overflow-y-auto max-h-[50vh]">
              {Object.entries(groupedOptions).map(([category, categoryOptions]) => (
                <CommandGroup key={category} heading={getCategoryLabel(category)}>
                  {categoryOptions.map((option) => (
                    <CommandItem
                      key={option.value}
                      value={option.label}
                      onSelect={() => handleSelect(option.value)}
                      className="flex items-center py-2"
                    >
                      <Check
                        className={cn(
                          'mr-2 h-4 w-4 flex-shrink-0',
                          value.includes(option.value) ? 'opacity-100' : 'opacity-0'
                        )}
                      />
                      <Badge
                        variant="outline"
                        className={cn('text-xs', getBadgeColor(option.color))}
                      >
                        {option.label}
                      </Badge>
                    </CommandItem>
                  ))}
                </CommandGroup>
              ))}
            </div>
          </Command>
        </PopoverContent>
      </Popover>
      
      {/* Tags seleccionados con opción de remover */}
      {selectedOptions.length > 0 && (
        <div className="mt-2 space-y-2">
          <div className="flex flex-wrap gap-1">
            {selectedOptions.map((option) => (
              <Badge
                key={option.value}
                variant="outline"
                className={cn(
                  'text-xs inline-flex items-center gap-1 px-2 py-0.5',
                  getBadgeColor(option.color)
                )}
              >
                <span className="truncate max-w-[120px]">{option.label}</span>
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    handleRemove(option.value);
                  }}
                  className="ml-0.5 hover:text-red-600 focus:outline-none"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
          <p className="text-xs text-muted-foreground">
            Toca los tags para removerlos
          </p>
        </div>
      )}
    </div>
  );
}