import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db';
import { appointments } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import crypto from 'crypto';

// Función para generar token de check-in
export function generateCheckInToken(appointmentId: string): string {
  const secret = process.env.CHECKIN_SECRET || 'default-secret-key';
  const timestamp = Date.now().toString();
  const data = `${appointmentId}-${timestamp}`;
  
  return crypto
    .createHmac('sha256', secret)
    .update(data)
    .digest('hex')
    .substring(0, 32);
}

// Función para validar token de check-in
function validateCheckInToken(appointmentId: string, token: string): boolean {
  const secret = process.env.CHECKIN_SECRET || 'default-secret-key';
  
  // Para simplicidad, generamos el token esperado con timestamp actual
  // En producción, podrías almacenar tokens con expiración en base de datos
  const currentTime = Date.now();
  const timeWindow = 24 * 60 * 60 * 1000; // 24 horas
  
  // Verificamos tokens generados en las últimas 24 horas
  for (let i = 0; i < timeWindow; i += 60000) { // cada minuto
    const testTimestamp = (currentTime - i).toString();
    const testData = `${appointmentId}-${testTimestamp}`;
    const testToken = crypto
      .createHmac('sha256', secret)
      .update(testData)
      .digest('hex')
      .substring(0, 32);
      
    if (testToken === token) {
      return true;
    }
  }
  
  return false;
}

// GET: Obtener información de la cita para check-in
export async function GET(
  request: NextRequest,
  { params }: { params: { appointmentId: string; token: string } }
) {
  try {
    const { appointmentId, token } = params;

    // Validar token
    if (!validateCheckInToken(appointmentId, token)) {
      return NextResponse.json(
        { error: 'Token inválido o expirado' },
        { status: 401 }
      );
    }

    // Buscar la cita
    const appointment = await db
      .select({
        id: appointments.id,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        status: appointments.status,
        patientFirstName: appointments.patientFirstName,
        patientLastName: appointments.patientLastName,
        patientEmail: appointments.patientEmail,
        doctorFirstName: appointments.doctorFirstName,
        doctorLastName: appointments.doctorLastName,
        serviceName: appointments.serviceName,
        consultoryName: appointments.consultoryName,
        estimatedPrice: appointments.estimatedPrice,
        currency: appointments.currency,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { error: 'Cita no encontrada' },
        { status: 404 }
      );
    }

    const appointmentData = appointment[0];

    // Verificar que la cita esté en estado válido para check-in
    if (!['confirmed', 'checked_in'].includes(appointmentData.status)) {
      return NextResponse.json(
        { error: 'Esta cita no puede procesarse para check-in' },
        { status: 400 }
      );
    }

    return NextResponse.json({
      appointment: appointmentData
    });

  } catch (error) {
    console.error('Error fetching appointment for check-in:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST: Realizar check-in del paciente
export async function POST(
  request: NextRequest,
  { params }: { params: { appointmentId: string; token: string } }
) {
  try {
    const { appointmentId, token } = params;

    // Validar token
    if (!validateCheckInToken(appointmentId, token)) {
      return NextResponse.json(
        { error: 'Token inválido o expirado' },
        { status: 401 }
      );
    }

    // Verificar que la cita existe y está en estado confirmado
    const appointment = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.id, appointmentId),
          eq(appointments.status, 'confirmed')
        )
      )
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { error: 'Cita no encontrada o no está en estado confirmado' },
        { status: 404 }
      );
    }

    // Actualizar el estado de la cita a checked_in
    await db
      .update(appointments)
      .set({
        status: 'checked_in',
        checkedInBy: 'self-checkin', // Indicar que fue auto check-in
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId));

    return NextResponse.json({
      message: 'Check-in realizado exitosamente',
      appointment: {
        id: appointmentId,
        status: 'checked_in'
      }
    });

  } catch (error) {
    console.error('Error during check-in:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}