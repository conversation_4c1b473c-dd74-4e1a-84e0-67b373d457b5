{"id": "86d8134f-e1a6-4bfd-86c5-57d0cc487fdb", "prevId": "8a670aa8-3aa6-4a49-ba59-3d15607aebbf", "version": "7", "dialect": "postgresql", "tables": {"public.account": {"name": "account", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"account_userId_user_id_fk": {"name": "account_userId_user_id_fk", "tableFrom": "account", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "patient_id": {"name": "patient_id", "type": "text", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "text", "primaryKey": false, "notNull": true}, "consultory_id": {"name": "consultory_id", "type": "text", "primaryKey": false, "notNull": true}, "scheduled_date": {"name": "scheduled_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "scheduled_time": {"name": "scheduled_time", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false, "default": 30}, "appointment_type": {"name": "appointment_type", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'scheduled'"}, "appointment_data": {"name": "appointment_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "confirmed_at": {"name": "confirmed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancelled_at": {"name": "cancelled_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "cancellation_reason": {"name": "cancellation_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "last_modified_by": {"name": "last_modified_by", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"patient_appointments_idx": {"name": "patient_appointments_idx", "columns": [{"expression": "patient_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scheduled_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_appointments_idx": {"name": "doctor_appointments_idx", "columns": [{"expression": "doctor_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scheduled_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultory_appointments_idx": {"name": "consultory_appointments_idx", "columns": [{"expression": "consultory_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scheduled_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_status_idx": {"name": "appointment_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_type_idx": {"name": "appointment_type_idx", "columns": [{"expression": "appointment_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_datetime_idx": {"name": "appointment_datetime_idx", "columns": [{"expression": "scheduled_date", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "scheduled_time", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"appointments_patient_id_user_id_fk": {"name": "appointments_patient_id_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "appointments_doctor_id_user_id_fk": {"name": "appointments_doctor_id_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "appointments_consultory_id_consultories_id_fk": {"name": "appointments_consultory_id_consultories_id_fk", "tableFrom": "appointments", "tableTo": "consultories", "columnsFrom": ["consultory_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "appointments_created_by_user_id_fk": {"name": "appointments_created_by_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_last_modified_by_user_id_fk": {"name": "appointments_last_modified_by_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["last_modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.associationCodes": {"name": "associationCodes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "usedBy": {"name": "usedBy", "type": "text", "primaryKey": false, "notNull": false}, "usedAt": {"name": "usedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"associationCodes_patientId_user_id_fk": {"name": "associationCodes_patientId_user_id_fk", "tableFrom": "associationCodes", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "associationCodes_usedBy_user_id_fk": {"name": "associationCodes_usedBy_user_id_fk", "tableFrom": "associationCodes", "tableTo": "user", "columnsFrom": ["usedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"associationCodes_code_unique": {"name": "associationCodes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.consultories": {"name": "consultories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "services": {"name": "services", "type": "jsonb", "primaryKey": false, "notNull": false}, "workingHours": {"name": "workingHours", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "phone_code": {"name": "phone_code", "type": "text", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"countries_code_unique": {"name": "countries_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.departments": {"name": "departments", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"departments_country_id_countries_id_fk": {"name": "departments_country_id_countries_id_fk", "tableFrom": "departments", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.doctorAssistantRelations": {"name": "doctorAssistantRelations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "assistantId": {"name": "assistantId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"doctorAssistantRelations_doctorId_user_id_fk": {"name": "doctorAssistant<PERSON><PERSON><PERSON>_doctorId_user_id_fk", "tableFrom": "doctorAssistantRelations", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "doctorAssistantRelations_assistantId_user_id_fk": {"name": "doctor<PERSON><PERSON>ant<PERSON><PERSON><PERSON>_assistantId_user_id_fk", "tableFrom": "doctorAssistantRelations", "tableTo": "user", "columnsFrom": ["assistantId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "doctorAssistantRelations_consultoryId_consultories_id_fk": {"name": "doctorAssistantRelations_consultoryId_consultories_id_fk", "tableFrom": "doctorAssistantRelations", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_records": {"name": "medical_records", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "patient_id": {"name": "patient_id", "type": "text", "primaryKey": false, "notNull": true}, "doctor_id": {"name": "doctor_id", "type": "text", "primaryKey": false, "notNull": true}, "consultory_id": {"name": "consultory_id", "type": "text", "primaryKey": false, "notNull": true}, "record_type": {"name": "record_type", "type": "text", "primaryKey": false, "notNull": true}, "appointment_id": {"name": "appointment_id", "type": "text", "primaryKey": false, "notNull": false}, "primary_diagnosis": {"name": "primary_diagnosis", "type": "text", "primaryKey": false, "notNull": false}, "secondary_diagnoses": {"name": "secondary_diagnoses", "type": "jsonb", "primaryKey": false, "notNull": false}, "vitals": {"name": "vitals", "type": "jsonb", "primaryKey": false, "notNull": false}, "record_data": {"name": "record_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'draft'"}, "created_by": {"name": "created_by", "type": "text", "primaryKey": false, "notNull": true}, "last_modified_by": {"name": "last_modified_by", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_by": {"name": "reviewed_by", "type": "text", "primaryKey": false, "notNull": false}, "reviewed_at": {"name": "reviewed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"patient_records_idx": {"name": "patient_records_idx", "columns": [{"expression": "patient_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_records_idx": {"name": "doctor_records_idx", "columns": [{"expression": "doctor_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultory_records_idx": {"name": "consultory_records_idx", "columns": [{"expression": "consultory_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "record_type_idx": {"name": "record_type_idx", "columns": [{"expression": "record_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "record_status_idx": {"name": "record_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "record_date_idx": {"name": "record_date_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "primary_diagnosis_idx": {"name": "primary_diagnosis_idx", "columns": [{"expression": "primary_diagnosis", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"medical_records_patient_id_user_id_fk": {"name": "medical_records_patient_id_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["patient_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "medical_records_doctor_id_user_id_fk": {"name": "medical_records_doctor_id_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["doctor_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "medical_records_consultory_id_consultories_id_fk": {"name": "medical_records_consultory_id_consultories_id_fk", "tableFrom": "medical_records", "tableTo": "consultories", "columnsFrom": ["consultory_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}, "medical_records_created_by_user_id_fk": {"name": "medical_records_created_by_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_records_last_modified_by_user_id_fk": {"name": "medical_records_last_modified_by_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["last_modified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_records_reviewed_by_user_id_fk": {"name": "medical_records_reviewed_by_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["reviewed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_specialties": {"name": "medical_specialties", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"medical_specialties_code_unique": {"name": "medical_specialties_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.municipalities": {"name": "municipalities", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"municipalities_department_id_departments_id_fk": {"name": "municipalities_department_id_departments_id_fk", "tableFrom": "municipalities", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notifications_userId_user_id_fk": {"name": "notifications_userId_user_id_fk", "tableFrom": "notifications", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.occupations": {"name": "occupations", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"occupations_code_unique": {"name": "occupations_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patientGuardianRelations": {"name": "patientGuardian<PERSON>elations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "guardianId": {"name": "guardianId", "type": "text", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "text", "primaryKey": false, "notNull": false}, "isPrimary": {"name": "isPrimary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "canMakeDecisions": {"name": "canMakeDecisions", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "validUntil": {"name": "validUntil", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"patientGuardianRelations_patientId_user_id_fk": {"name": "patientGuardianRelations_patientId_user_id_fk", "tableFrom": "patientGuardian<PERSON>elations", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "patientGuardianRelations_guardianId_user_id_fk": {"name": "patientG<PERSON>ian<PERSON>ela<PERSON>_guardianId_user_id_fk", "tableFrom": "patientGuardian<PERSON>elations", "tableTo": "user", "columnsFrom": ["guardianId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.registrationRequests": {"name": "registrationRequests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "requestedRoles": {"name": "requestedRoles", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "userData": {"name": "userData", "type": "jsonb", "primaryKey": false, "notNull": false}, "rolesData": {"name": "rolesData", "type": "jsonb", "primaryKey": false, "notNull": false}, "documentsData": {"name": "documentsData", "type": "jsonb", "primaryKey": false, "notNull": false}, "reviewedBy": {"name": "reviewedBy", "type": "text", "primaryKey": false, "notNull": false}, "reviewedAt": {"name": "reviewedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewNotes": {"name": "reviewNotes", "type": "text", "primaryKey": false, "notNull": false}, "rejectionReason": {"name": "rejectionReason", "type": "text", "primaryKey": false, "notNull": false}, "approvedRoles": {"name": "approvedRoles", "type": "jsonb", "primaryKey": false, "notNull": false}, "rejectedRoles": {"name": "rejectedRoles", "type": "jsonb", "primaryKey": false, "notNull": false}, "submittedAt": {"name": "submittedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"reg_user_idx": {"name": "reg_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reg_status_idx": {"name": "reg_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "reg_submitted_idx": {"name": "reg_submitted_idx", "columns": [{"expression": "submittedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"registrationRequests_userId_user_id_fk": {"name": "registrationRequests_userId_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "registrationRequests_reviewedBy_user_id_fk": {"name": "registrationRequests_reviewedBy_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["reviewedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.relationships": {"name": "relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"relationships_code_unique": {"name": "relationships_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.session": {"name": "session", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"session_userId_user_id_fk": {"name": "session_userId_user_id_fk", "tableFrom": "session", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"session_token_unique": {"name": "session_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.subscription": {"name": "subscription", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "modifiedAt": {"name": "modifiedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": true}, "recurringInterval": {"name": "recurringInterval", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true}, "currentPeriodStart": {"name": "currentPeriodStart", "type": "timestamp", "primaryKey": false, "notNull": true}, "currentPeriodEnd": {"name": "currentPeriodEnd", "type": "timestamp", "primaryKey": false, "notNull": true}, "cancelAtPeriodEnd": {"name": "cancelAtPeriodEnd", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "canceledAt": {"name": "canceledAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "startedAt": {"name": "startedAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "endsAt": {"name": "endsAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "endedAt": {"name": "endedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "customerId": {"name": "customerId", "type": "text", "primaryKey": false, "notNull": true}, "productId": {"name": "productId", "type": "text", "primaryKey": false, "notNull": true}, "discountId": {"name": "discountId", "type": "text", "primaryKey": false, "notNull": false}, "checkoutId": {"name": "checkoutId", "type": "text", "primaryKey": false, "notNull": true}, "customerCancellationReason": {"name": "customerCancellationReason", "type": "text", "primaryKey": false, "notNull": false}, "customerCancellationComment": {"name": "customerCancellationComment", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "customFieldData": {"name": "customFieldData", "type": "text", "primaryKey": false, "notNull": false}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"subscription_userId_user_id_fk": {"name": "subscription_userId_user_id_fk", "tableFrom": "subscription", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_config": {"name": "system_config", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"system_config_key_unique": {"name": "system_config_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "statusReason": {"name": "statusReason", "type": "text", "primaryKey": false, "notNull": false}, "activatedAt": {"name": "activatedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "activatedBy": {"name": "activatedBy", "type": "text", "primaryKey": false, "notNull": false}, "first_name": {"name": "first_name", "type": "text", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "text", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "text", "primaryKey": false, "notNull": true}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "alternative_phone": {"name": "alternative_phone", "type": "text", "primaryKey": false, "notNull": false}, "country_id": {"name": "country_id", "type": "integer", "primaryKey": false, "notNull": false}, "department_id": {"name": "department_id", "type": "integer", "primaryKey": false, "notNull": false}, "municipality_id": {"name": "municipality_id", "type": "integer", "primaryKey": false, "notNull": false}, "occupation_id": {"name": "occupation_id", "type": "integer", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "text", "primaryKey": false, "notNull": false}, "emergency_phone": {"name": "emergency_phone", "type": "text", "primaryKey": false, "notNull": false}, "emergency_relationship_id": {"name": "emergency_relationship_id", "type": "integer", "primaryKey": false, "notNull": false}, "preferences": {"name": "preferences", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_country_id_countries_id_fk": {"name": "user_country_id_countries_id_fk", "tableFrom": "user", "tableTo": "countries", "columnsFrom": ["country_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_department_id_departments_id_fk": {"name": "user_department_id_departments_id_fk", "tableFrom": "user", "tableTo": "departments", "columnsFrom": ["department_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_municipality_id_municipalities_id_fk": {"name": "user_municipality_id_municipalities_id_fk", "tableFrom": "user", "tableTo": "municipalities", "columnsFrom": ["municipality_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_occupation_id_occupations_id_fk": {"name": "user_occupation_id_occupations_id_fk", "tableFrom": "user", "tableTo": "occupations", "columnsFrom": ["occupation_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_emergency_relationship_id_relationships_id_fk": {"name": "user_emergency_relationship_id_relationships_id_fk", "tableFrom": "user", "tableTo": "relationships", "columnsFrom": ["emergency_relationship_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_consultories": {"name": "user_consultories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "consultory_id": {"name": "consultory_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_consultory_idx": {"name": "user_consultory_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consultory_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "consultory_idx": {"name": "consultory_idx", "columns": [{"expression": "consultory_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultory_role_idx": {"name": "consultory_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultory_active_idx": {"name": "consultory_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_consultories_user_id_user_id_fk": {"name": "user_consultories_user_id_user_id_fk", "tableFrom": "user_consultories", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_consultories_consultory_id_consultories_id_fk": {"name": "user_consultories_consultory_id_consultories_id_fk", "tableFrom": "user_consultories", "tableTo": "consultories", "columnsFrom": ["consultory_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_documents": {"name": "user_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": false}, "document_type": {"name": "document_type", "type": "text", "primaryKey": false, "notNull": true}, "file_name": {"name": "file_name", "type": "text", "primaryKey": false, "notNull": true}, "file_path": {"name": "file_path", "type": "text", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "mime_type": {"name": "mime_type", "type": "text", "primaryKey": false, "notNull": false}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_verified": {"name": "is_verified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "verified_by": {"name": "verified_by", "type": "text", "primaryKey": false, "notNull": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expiration_date": {"name": "expiration_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "uploaded_by": {"name": "uploaded_by", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_doc_type_idx": {"name": "user_doc_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "document_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "role_doc_idx": {"name": "role_doc_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "document_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "verification_idx": {"name": "verification_idx", "columns": [{"expression": "is_verified", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doc_active_idx": {"name": "doc_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_documents_user_id_user_id_fk": {"name": "user_documents_user_id_user_id_fk", "tableFrom": "user_documents", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_documents_verified_by_user_id_fk": {"name": "user_documents_verified_by_user_id_fk", "tableFrom": "user_documents", "tableTo": "user", "columnsFrom": ["verified_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_documents_uploaded_by_user_id_fk": {"name": "user_documents_uploaded_by_user_id_fk", "tableFrom": "user_documents", "tableTo": "user", "columnsFrom": ["uploaded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "role_data": {"name": "role_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "activated_at": {"name": "activated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deactivated_at": {"name": "deactivated_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"user_role_idx": {"name": "user_role_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "role_idx": {"name": "role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "status_idx": {"name": "status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_roles_user_id_user_id_fk": {"name": "user_roles_user_id_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_specialties": {"name": "user_specialties", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "text", "primaryKey": false, "notNull": true}, "specialty_id": {"name": "specialty_id", "type": "integer", "primaryKey": false, "notNull": true}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "certification_date": {"name": "certification_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "certification_number": {"name": "certification_number", "type": "text", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_specialty_idx": {"name": "user_specialty_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "specialty_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "specialty_idx": {"name": "specialty_idx", "columns": [{"expression": "specialty_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "primary_specialty_idx": {"name": "primary_specialty_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "is_primary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_specialties_user_id_user_id_fk": {"name": "user_specialties_user_id_user_id_fk", "tableFrom": "user_specialties", "tableTo": "user", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_specialties_specialty_id_medical_specialties_id_fk": {"name": "user_specialties_specialty_id_medical_specialties_id_fk", "tableFrom": "user_specialties", "tableTo": "medical_specialties", "columnsFrom": ["specialty_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification": {"name": "verification", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}