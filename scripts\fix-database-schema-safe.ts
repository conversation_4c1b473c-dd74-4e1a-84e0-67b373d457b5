import { config } from 'dotenv';
import { neon } from '@neondatabase/serverless';

// Cargar variables de entorno
config({ path: '.env.local' });

const connectionString = process.env.DATABASE_URL;
if (!connectionString) {
  throw new Error('DATABASE_URL not found in environment variables');
}

const sql = neon(connectionString);

async function fixDatabaseSchema() {
  console.log('🔧 Iniciando corrección segura de esquema de base de datos...');

  try {
    // 1. Primero, limpiar datos problemáticos en medical_consultations
    console.log('🧹 Limpiando datos problemáticos en medical_consultations...');
    
    // Verificar si la tabla existe
    const tableExists = await sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_name = 'medical_consultations'
      );
    `;
    
    if (tableExists[0]?.exists) {
      console.log('📋 Tabla medical_consultations encontrada, limpiando datos...');
      
      // Limpiar datos no JSON válidos en la columna services
      await sql`
        UPDATE medical_consultations 
        SET services = '[]'::jsonb 
        WHERE services IS NOT NULL 
        AND services != '' 
        AND services !~ '^\\s*\\[.*\\]\\s*$' 
        AND services !~ '^\\s*\\{.*\\}\\s*$';
      `;
      
      console.log('✅ Datos problemáticos limpiados');
      
      // Ahora convertir la columna a jsonb de forma segura
      await sql`
        DO $$
        BEGIN
            -- Verificar si la columna services existe y necesita conversión
            IF EXISTS (SELECT FROM information_schema.columns 
                      WHERE table_name = 'medical_consultations' 
                      AND column_name = 'services' 
                      AND data_type != 'jsonb') THEN
                -- Primero, establecer NULL para valores que no se pueden convertir
                UPDATE medical_consultations 
                SET services = NULL 
                WHERE services IS NOT NULL 
                AND services::text !~ '^\\s*(\\[.*\\]|\\{.*\\}|null)\\s*$';
                
                -- Ahora convertir a jsonb
                ALTER TABLE medical_consultations 
                ALTER COLUMN services TYPE jsonb USING 
                  CASE 
                    WHEN services IS NULL OR services::text = '' THEN '[]'::jsonb
                    ELSE services::jsonb
                  END;
                  
                RAISE NOTICE 'Columna services convertida a jsonb';
            END IF;
            
            -- Si la columna no existe, agregarla
            IF NOT EXISTS (SELECT FROM information_schema.columns 
                          WHERE table_name = 'medical_consultations' 
                          AND column_name = 'services') THEN
                ALTER TABLE medical_consultations 
                ADD COLUMN services jsonb DEFAULT '[]'::jsonb;
                RAISE NOTICE 'Columna services agregada';
            END IF;
        END $$;
      `;
    }

    // 2. Agregar columnas faltantes a medical_records
    console.log('📋 Verificando columnas en medical_records...');
    await sql`
      DO $$
      BEGIN
          IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'medical_records') THEN
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_records' 
                            AND column_name = 'demographics') THEN
                  ALTER TABLE medical_records 
                  ADD COLUMN demographics jsonb DEFAULT '{}'::jsonb;
                  RAISE NOTICE 'Columna demographics agregada';
              END IF;
              
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_records' 
                            AND column_name = 'administrative') THEN
                  ALTER TABLE medical_records 
                  ADD COLUMN administrative jsonb DEFAULT '{}'::jsonb;
                  RAISE NOTICE 'Columna administrative agregada';
              END IF;
              
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_records' 
                            AND column_name = 'emergency_contact') THEN
                  ALTER TABLE medical_records 
                  ADD COLUMN emergency_contact jsonb DEFAULT '{}'::jsonb;
                  RAISE NOTICE 'Columna emergency_contact agregada';
              END IF;
          END IF;
      END $$;
    `;

    // 3. Agregar otras columnas jsonb faltantes en medical_consultations
    console.log('📋 Verificando otras columnas JSONB en medical_consultations...');
    await sql`
      DO $$
      BEGIN
          IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'medical_consultations') THEN
              -- vitalSigns
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'vitalSigns') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN "vitalSigns" jsonb;
                  RAISE NOTICE 'Columna vitalSigns agregada';
              END IF;
              
              -- physicalExam
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'physicalExam') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN "physicalExam" jsonb;
                  RAISE NOTICE 'Columna physicalExam agregada';
              END IF;
              
              -- diagnoses
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'diagnoses') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN diagnoses jsonb;
                  RAISE NOTICE 'Columna diagnoses agregada';
              END IF;
              
              -- treatment
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'treatment') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN treatment jsonb;
                  RAISE NOTICE 'Columna treatment agregada';
              END IF;
              
              -- prescriptions
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'prescriptions') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN prescriptions jsonb;
                  RAISE NOTICE 'Columna prescriptions agregada';
              END IF;
              
              -- attachments
              IF NOT EXISTS (SELECT FROM information_schema.columns 
                            WHERE table_name = 'medical_consultations' 
                            AND column_name = 'attachments') THEN
                  ALTER TABLE medical_consultations 
                  ADD COLUMN attachments jsonb;
                  RAISE NOTICE 'Columna attachments agregada';
              END IF;
          END IF;
      END $$;
    `;

    console.log('✅ Corrección segura de esquema completada exitosamente');
    
    // Verificar el estado final
    const columns = await sql`
      SELECT column_name, data_type 
      FROM information_schema.columns 
      WHERE table_name IN ('medical_records', 'medical_consultations')
      AND column_name IN ('demographics', 'administrative', 'emergency_contact', 'services', 'vitalSigns', 'physicalExam', 'diagnoses', 'treatment', 'prescriptions', 'attachments')
      ORDER BY table_name, column_name;
    `;
    
    console.log('📊 Columnas verificadas:');
    columns.forEach(col => {
      console.log(`  - ${col.column_name}: ${col.data_type}`);
    });

  } catch (error) {
    console.error('❌ Error ejecutando corrección de esquema:', error);
    throw error;
  }
}

// Ejecutar el script
fixDatabaseSchema()
  .then(() => {
    console.log('🎉 Script completado exitosamente');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error en el script:', error);
    process.exit(1);
  }); 