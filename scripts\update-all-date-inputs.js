// Script mejorado para actualizar TODOS los inputs type="date" a DateInput
const fs = require('fs');
const path = require('path');

// Buscar recursivamente archivos .tsx que contengan type="date"
function findFilesWithDateInputs(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    // Ignorar node_modules, .next y otros directorios no relevantes
    if (stat.isDirectory() && !file.startsWith('.') && file !== 'node_modules' && file !== '.next') {
      findFilesWithDateInputs(filePath, fileList);
    } else if (stat.isFile() && file.endsWith('.tsx')) {
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('type="date"')) {
        fileList.push(filePath);
      }
    }
  });
  
  return fileList;
}

function updateFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let modified = false;
    const originalContent = content;

    // Verificar si el archivo ya importa DateInput
    const hasDateInputImport = content.includes('DateInput');
    
    // Patrón mejorado para encontrar inputs con type="date"
    // Captura todo el elemento Input incluyendo saltos de línea
    const dateInputRegex = /<Input\s+([\s\S]*?)type="date"([\s\S]*?)\/>/g;
    
    content = content.replace(dateInputRegex, (match, before, after) => {
      modified = true;
      
      // Combinar todos los atributos
      const allAttrs = before + ' ' + after;
      
      // Extraer atributos importantes
      const idMatch = allAttrs.match(/id="([^"]+)"/);
      const classNameMatch = allAttrs.match(/className="([^"]+)"/);
      const valueMatch = allAttrs.match(/value={([^}]+)}/);
      const onChangeMatch = allAttrs.match(/onChange={\(([^)]+)\)\s*=>\s*([^}]+)}/);
      const disabledMatch = allAttrs.match(/disabled={([^}]+)}/);
      const placeholderMatch = allAttrs.match(/placeholder="([^"]+)"/);
      
      let attrs = [];
      
      // Agregar id si existe
      if (idMatch) {
        attrs.push(`id="${idMatch[1]}"`);
      }
      
      // Agregar className si existe
      if (classNameMatch) {
        attrs.push(`className="${classNameMatch[1]}"`);
      }
      
      // Agregar disabled si existe
      if (disabledMatch) {
        attrs.push(`disabled={${disabledMatch[1]}}`);
      }
      
      // Convertir value
      if (valueMatch) {
        const valueExpr = valueMatch[1];
        
        // Casos comunes de conversión
        if (valueExpr.includes('toISOString().split')) {
          // Formato: value={data.dateOfBirth ? new Date(data.dateOfBirth).toISOString().split('T')[0] : ''}
          const cleanValue = valueExpr
            .replace(/\s*\?\s*new Date\(([^)]+)\)\.toISOString\(\)\.split\([^)]+\)\[[^\]]+\]\s*:\s*['"]?['"]?/, ' ? new Date($1) : undefined');
          attrs.push(`value={${cleanValue}}`);
        } else if (valueExpr.includes('new Date(')) {
          // Ya tiene new Date
          attrs.push(`value={${valueExpr.replace(/:\s*['"]?['"]?/, ': undefined')}}`);
        } else {
          // Formato simple
          attrs.push(`value={${valueExpr} ? new Date(${valueExpr}) : undefined}`);
        }
      }
      
      // Convertir onChange
      if (onChangeMatch) {
        const param = onChangeMatch[1];
        const body = onChangeMatch[2];
        
        // Detectar el patrón común de handleInputChange o setters
        if (body.includes('handleInputChange') || body.includes('onChange')) {
          const funcMatch = body.match(/(\w+)\(['"]([\w]+)['"],\s*([^)]+)\)/);
          if (funcMatch) {
            attrs.push(`onChange={(date) => ${funcMatch[1]}('${funcMatch[2]}', date || null)}`);
          } else {
            // Patrón setFormData o similar
            const setterMatch = body.match(/(\w+)\(.*?([^)]+)\)/);
            if (setterMatch) {
              attrs.push(`onChange={(date) => ${setterMatch[1]}(date || null)}`);
            }
          }
        } else if (body.includes('new Date(')) {
          // Ya convierte a Date
          attrs.push(`onChange={(date) => ${body.replace(/new Date\([^)]+\)/, 'date || null')}}`);
        }
      }
      
      // Para fechas de nacimiento, agregar maxDate
      if (idMatch && idMatch[1].toLowerCase().includes('birth')) {
        attrs.push('maxDate={new Date()}');
      }
      
      return `<DateInput\n              ${attrs.join('\n              ')}\n            />`;
    });
    
    if (modified) {
      // Agregar import si no existe
      if (!hasDateInputImport) {
        // Buscar el mejor lugar para insertar el import
        const importsSection = content.match(/^(import[\s\S]*?)\n\n/m);
        
        if (importsSection) {
          const imports = importsSection[1];
          const uiImports = imports.match(/import.*from\s+['"]@\/components\/ui\/[^'"]+['"]/g) || [];
          
          if (uiImports.length > 0) {
            // Insertar después del último import de UI
            const lastUIImport = uiImports[uiImports.length - 1];
            content = content.replace(
              lastUIImport,
              `${lastUIImport}\nimport { DateInput } from '@/components/ui/date-input';`
            );
          } else {
            // Insertar al final de los imports
            content = content.replace(
              importsSection[1],
              `${importsSection[1]}\nimport { DateInput } from '@/components/ui/date-input';`
            );
          }
        }
      }
      
      fs.writeFileSync(filePath, content);
      const relativePath = path.relative(process.cwd(), filePath);
      console.log(`✅ ${relativePath}`);
      return true;
    }
    
    return false;
  } catch (error) {
    const relativePath = path.relative(process.cwd(), filePath);
    console.error(`❌ Error en ${relativePath}:`, error.message);
    return false;
  }
}

console.log('🔍 Buscando archivos con inputs type="date"...\n');

// Directorios donde buscar
const searchDirs = [
  'app',
  'components'
];

let allFiles = [];
searchDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    findFilesWithDateInputs(dir, allFiles);
  }
});

console.log(`📋 Encontrados ${allFiles.length} archivos con campos de fecha\n`);
console.log('🔄 Actualizando archivos...\n');

let updatedCount = 0;
allFiles.forEach(file => {
  if (updateFile(file)) {
    updatedCount++;
  }
});

console.log(`\n✅ Actualización completada: ${updatedCount}/${allFiles.length} archivos modificados`);

if (updatedCount > 0) {
  console.log('\n🎯 Mejoras aplicadas:');
  console.log('   ✅ Entrada manual de fechas (escribir directamente)');
  console.log('   ✅ Selector de calendario opcional');
  console.log('   ✅ Validación automática del formato');
  console.log('   ✅ Restricción de fechas futuras para fechas de nacimiento');
  console.log('   ✅ Formato dinámico según configuración regional');
}