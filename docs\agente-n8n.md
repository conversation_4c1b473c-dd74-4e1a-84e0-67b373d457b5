# Agente Médico Multimodal con n8n

## Resumen Ejecutivo

Este documento describe la implementación de un agente de IA médico usando n8n como orquestador, que permitirá a los doctores hacer consultas médicas por voz o texto desde la aplicación SGC, con planes de expansión a WhatsApp y Telegram.

## Arquitectura General

```
[Frontend SGC] ←→ [API Gateway] ←→ [n8n Workflows] ←→ [APIs Médicas]
     ↓                                    ↓              ↓
[Speech APIs] ←→ [Base de Datos SGC] ←→ [OpenAI GPT-4] ←→ [Logs/Analytics]
```

## Fase 1: MVP - Tool de Consultas Médicas

### Objetivo
Implementar un workflow básico en n8n que permita a los doctores hacer consultas médicas generales usando APIs públicas de información médica.

### Funcionalidades del MVP
1. **Consulta de medicamentos**: Efectos secundarios, dosis, contraindicaciones
2. **Interacciones medicamentosas**: Verificar compatibilidad entre fármacos
3. **Información de diagnósticos**: Códigos ICD-10, síntomas asociados
4. **Cálculo de dosis pediátricas**: Basado en peso y edad

## Especificaciones Técnicas

### 1. Configuración de n8n

#### Requisitos Previos
- n8n instalado (versión 1.0+)
- Acceso a OpenAI API (GPT-4)
- Conexión a base de datos SGC
- APIs médicas configuradas

#### Variables de Entorno Necesarias
```env
# OpenAI
OPENAI_API_KEY=sk-...

# Base de Datos SGC
SGC_DB_HOST=localhost
SGC_DB_PORT=5432
SGC_DB_NAME=sgc_database
SGC_DB_USER=sgc_user
SGC_DB_PASSWORD=sgc_password

# APIs Médicas
OPENFDA_API_KEY=optional
RXNORM_API_BASE=https://rxnav.nlm.nih.gov/REST
DRUGBANK_API_KEY=your_drugbank_key

# Configuración del Sistema
N8N_WEBHOOK_URL=https://your-n8n.com/webhook
SGC_API_BASE=https://your-sgc.com/api
```

### 2. Workflow Principal: "Agente Médico"

#### Estructura del Workflow

```
[Webhook: /webhook/agente-medico]
    ↓
[Validar Request]
    ↓
[Extraer Contexto Usuario]
    ↓
[OpenAI: Analizar Intención]
    ↓
[Switch: Determinar Tool]
    ↓
[Tool: consulta_medica]
    ↓
[Formatear Respuesta]
    ↓
[Return Response]
```

#### Nodos Detallados

##### Nodo 1: Webhook Trigger
```json
{
  "httpMethod": "POST",
  "path": "/webhook/agente-medico",
  "responseMode": "responseNode",
  "options": {
    "noResponseBody": false
  }
}
```

**Payload Esperado:**
```json
{
  "message": "¿Cuáles son los efectos secundarios de la amoxicilina?",
  "userId": "doctor_123",
  "context": "agenda",
  "timestamp": "2025-01-16T10:30:00Z",
  "sessionId": "session_456"
}
```

##### Nodo 2: Validar Request
```javascript
// Código JavaScript para validación
const { message, userId, context } = $json;

if (!message || !userId) {
  return {
    error: true,
    message: "Faltan parámetros requeridos: message, userId"
  };
}

if (message.length > 1000) {
  return {
    error: true,
    message: "Mensaje demasiado largo (máximo 1000 caracteres)"
  };
}

return {
  error: false,
  validatedData: {
    message: message.trim(),
    userId,
    context: context || "general",
    timestamp: new Date().toISOString()
  }
};
```

##### Nodo 3: Extraer Contexto Usuario
```sql
-- Query para obtener información del doctor
SELECT 
  d.id,
  d.first_name,
  d.last_name,
  d.specialty,
  d.license_number,
  p.name as practice_name
FROM doctors d
LEFT JOIN practices p ON d.practice_id = p.id
WHERE d.id = $json.validatedData.userId
```

##### Nodo 4: OpenAI - Analizar Intención
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "system",
      "content": "Eres un asistente médico especializado. Analiza la consulta del doctor y determina:\n1. Tipo de consulta (medicamento, diagnóstico, dosis, interacción)\n2. Entidades médicas mencionadas\n3. Parámetros necesarios para la búsqueda\n\nResponde SOLO en formato JSON:\n{\n  \"tipo\": \"medicamento|diagnostico|dosis|interaccion\",\n  \"entidades\": [\"medicamento1\", \"medicamento2\"],\n  \"parametros\": {\n    \"medicamento\": \"nombre\",\n    \"edad\": \"número\",\n    \"peso\": \"número\"\n  },\n  \"confianza\": 0.95\n}"
    },
    {
      "role": "user", 
      "content": "{{ $json.validatedData.message }}"
    }
  ],
  "temperature": 0.1
}
```

##### Nodo 5: Switch - Determinar Tool
```javascript
const analisis = JSON.parse($json.choices[0].message.content);

switch(analisis.tipo) {
  case 'medicamento':
    return [{ json: { ...analisis, tool: 'consulta_medicamento' } }];
  case 'interaccion':
    return [{ json: { ...analisis, tool: 'verificar_interacciones' } }];
  case 'dosis':
    return [{ json: { ...analisis, tool: 'calcular_dosis' } }];
  case 'diagnostico':
    return [{ json: { ...analisis, tool: 'buscar_diagnostico' } }];
  default:
    return [{ json: { ...analisis, tool: 'consulta_general' } }];
}
```

### 3. Sub-workflow: Tool "Consulta Medicamento"

#### Estructura
```
[Recibir Parámetros]
    ↓
[Buscar en OpenFDA API]
    ↓
[Buscar en RxNorm API]
    ↓
[Combinar Información]
    ↓
[Formatear con OpenAI]
    ↓
[Return Respuesta]
```

#### Implementación Detallada

##### Nodo: Buscar en OpenFDA API
```javascript
// Preparar búsqueda en OpenFDA
const medicamento = $json.parametros.medicamento;
const searchTerm = medicamento.toLowerCase().replace(/\s+/g, '+');

return {
  url: `https://api.fda.gov/drug/label.json?search=openfda.brand_name:"${searchTerm}"+OR+openfda.generic_name:"${searchTerm}"&limit=5`,
  method: 'GET',
  headers: {
    'User-Agent': 'SGC-Medical-Assistant/1.0'
  }
};
```

##### Nodo: Procesar Respuesta OpenFDA
```javascript
const response = $json;

if (!response.results || response.results.length === 0) {
  return {
    found: false,
    message: "No se encontró información en OpenFDA"
  };
}

const drug = response.results[0];

return {
  found: true,
  medicamento: {
    nombre_comercial: drug.openfda?.brand_name?.[0] || 'N/A',
    nombre_generico: drug.openfda?.generic_name?.[0] || 'N/A',
    fabricante: drug.openfda?.manufacturer_name?.[0] || 'N/A',
    efectos_secundarios: drug.adverse_reactions?.[0] || 'No especificados',
    contraindicaciones: drug.contraindications?.[0] || 'No especificadas',
    dosis_administracion: drug.dosage_and_administration?.[0] || 'Consultar prospecto',
    advertencias: drug.warnings?.[0] || 'No especificadas'
  }
};
```

##### Nodo: Formatear Respuesta Final
```json
{
  "model": "gpt-4",
  "messages": [
    {
      "role": "system",
      "content": "Eres un asistente médico. Presenta la información del medicamento de forma clara y profesional para un doctor. Incluye:\n1. Nombre y fabricante\n2. Efectos secundarios principales\n3. Contraindicaciones importantes\n4. Recomendaciones de dosis\n5. Advertencias relevantes\n\nUsa formato médico profesional pero accesible."
    },
    {
      "role": "user",
      "content": "Información del medicamento: {{ JSON.stringify($json.medicamento) }}"
    }
  ],
  "temperature": 0.3
}
```

### 4. APIs Médicas Recomendadas

#### OpenFDA API (Gratuita)
- **URL Base**: `https://api.fda.gov/`
- **Endpoints útiles**:
  - `/drug/label.json` - Información de medicamentos
  - `/drug/event.json` - Eventos adversos reportados
- **Límites**: 240 requests/minuto, 120,000/día
- **Documentación**: https://open.fda.gov/apis/

#### RxNorm API (Gratuita)
- **URL Base**: `https://rxnav.nlm.nih.gov/REST/`
- **Endpoints útiles**:
  - `/drugs.json` - Búsqueda de medicamentos
  - `/interaction/interaction.json` - Interacciones
- **Sin límites de rate**
- **Documentación**: https://rxnav.nlm.nih.gov/RxNormAPIs.html

#### DrugBank API (Freemium)
- **Plan gratuito**: 500 requests/mes
- **Información detallada**: Farmacocinética, interacciones, targets
- **URL**: https://go.drugbank.com/releases/latest

### 5. Integración con Frontend SGC

#### Endpoint en la Aplicación
```typescript
// app/api/chat-n8n/route.ts
export async function POST(req: Request) {
  try {
    const { message, userId, context } = await req.json();
    
    // Validaciones básicas
    if (!message || !userId) {
      return NextResponse.json(
        { error: 'Faltan parámetros requeridos' },
        { status: 400 }
      );
    }
    
    // Llamada a n8n
    const n8nResponse = await fetch(`${process.env.N8N_WEBHOOK_URL}/webhook/agente-medico`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.N8N_API_KEY}`
      },
      body: JSON.stringify({
        message,
        userId,
        context: context || 'general',
        timestamp: new Date().toISOString(),
        sessionId: `session_${Date.now()}`
      })
    });
    
    if (!n8nResponse.ok) {
      throw new Error(`n8n error: ${n8nResponse.status}`);
    }
    
    const result = await n8nResponse.json();
    
    return NextResponse.json({
      id: `msg_${Date.now()}`,
      type: 'assistant',
      content: result.response,
      timestamp: new Date(),
      source: 'agente_medico',
      confidence: result.confidence || 0.9
    });
    
  } catch (error) {
    console.error('Error en chat-n8n:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
```

#### Modificación del Chat Component
```typescript
// Agregar a components/agenda/ai-assistant-chat.tsx
const handleSendMessage = async () => {
  if (!inputMessage.trim()) return;

  const userMessage: ChatMessage = {
    id: Date.now().toString(),
    type: 'user',
    content: inputMessage,
    timestamp: new Date()
  };

  setMessages(prev => [...prev, userMessage]);
  setInputMessage('');
  setIsTyping(true);

  try {
    // Llamada real a n8n en lugar de mockResponses
    const response = await fetch('/api/chat-n8n', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: inputMessage,
        userId: doctorInfo?.id,
        context: 'agenda'
      })
    });

    if (!response.ok) {
      throw new Error('Error en la respuesta del servidor');
    }

    const assistantMessage = await response.json();
    setMessages(prev => [...prev, assistantMessage]);

    // Opcional: Reproducir respuesta por voz
    if (voiceEnabled) {
      speakResponse(assistantMessage.content);
    }

  } catch (error) {
    console.error('Error:', error);
    const errorMessage: ChatMessage = {
      id: (Date.now() + 1).toString(),
      type: 'assistant',
      content: 'Lo siento, hubo un error al procesar tu consulta. Por favor intenta nuevamente.',
      timestamp: new Date()
    };
    setMessages(prev => [...prev, errorMessage]);
  } finally {
    setIsTyping(false);
  }
};
```

### 6. Casos de Uso de Prueba

#### Consultas de Ejemplo para Testing

1. **Información de Medicamento**:
   - "¿Cuáles son los efectos secundarios de la amoxicilina?"
   - "Dime sobre las contraindicaciones del ibuprofeno"
   - "¿Qué dosis de paracetamol para un adulto?"

2. **Interacciones Medicamentosas**:
   - "¿Hay interacciones entre warfarina y aspirina?"
   - "¿Puedo recetar metformina con enalapril?"

3. **Cálculo de Dosis**:
   - "Dosis de amoxicilina para niño de 8 años, 25kg"
   - "¿Cuánto ibuprofeno para bebé de 6 meses?"

4. **Información Diagnóstica**:
   - "¿Cuál es el código ICD-10 para diabetes tipo 2?"
   - "Síntomas asociados con hipertensión arterial"

### 7. Monitoreo y Logs

#### Métricas a Trackear
- Número de consultas por doctor
- Tipos de consultas más frecuentes
- Tiempo de respuesta promedio
- Tasa de error por API
- Satisfacción del usuario (feedback)

#### Configuración de Logs en n8n
```javascript
// Nodo de logging al final de cada workflow
const logData = {
  timestamp: new Date().toISOString(),
  userId: $json.userId,
  query: $json.message,
  tool_used: $json.tool,
  response_time: Date.now() - $json.start_time,
  success: !$json.error,
  api_calls: $json.api_calls_made || [],
  session_id: $json.sessionId
};

// Guardar en base de datos o servicio de logs
return { logData };
```

### 8. Seguridad y Compliance

#### Consideraciones HIPAA/Médicas
- **No almacenar información de pacientes** en n8n
- **Encriptar comunicaciones** (HTTPS obligatorio)
- **Logs sin PII** (información personal identificable)
- **Rate limiting** por usuario
- **Validación de entrada** estricta

#### Configuración de Seguridad
```javascript
// Sanitización de entrada
const sanitizeInput = (input) => {
  // Remover información potencialmente sensible
  return input
    .replace(/\b\d{3}-\d{2}-\d{4}\b/g, '[SSN_REDACTED]')
    .replace(/\b\d{4}\s?\d{4}\s?\d{4}\s?\d{4}\b/g, '[CARD_REDACTED]')
    .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]');
};
```

### 9. Plan de Implementación

#### Fase 1: Setup Básico (Semana 1)
- [ ] Configurar n8n en servidor
- [ ] Crear workflow principal
- [ ] Implementar tool de consulta medicamento
- [ ] Conectar con OpenFDA API
- [ ] Testing básico

#### Fase 2: Integración (Semana 2)
- [ ] Modificar frontend SGC
- [ ] Crear endpoint /api/chat-n8n
- [ ] Testing de integración
- [ ] Configurar logs y monitoreo

#### Fase 3: Expansión (Semana 3-4)
- [ ] Agregar más APIs médicas
- [ ] Implementar tools adicionales
- [ ] Optimizar respuestas
- [ ] Testing con doctores reales

### 10. Próximos Pasos

Una vez funcionando el MVP, se pueden agregar:

1. **Más Tools**:
   - Búsqueda en expedientes
   - Generación de recetas
   - Programación de citas
   - Cálculos médicos avanzados

2. **Más Canales**:
   - WhatsApp Business API
   - Telegram Bot
   - SMS/Twilio

3. **Funcionalidades Avanzadas**:
   - Speech-to-text profesional
   - Text-to-speech natural
   - Análisis de imágenes médicas
   - Integración con laboratorios

## Conclusión

Esta implementación proporciona una base sólida y escalable para el agente médico, permitiendo expansión gradual sin afectar el sistema en producción. La arquitectura modular de n8n facilita el mantenimiento y la adición de nuevas funcionalidades según las necesidades de los doctores.
