import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const patientId = params.id;
    const { status } = await request.json();

    // Validar que el status es válido
    if (!status || !['active', 'inactive'].includes(status)) {
      return NextResponse.json(
        { error: 'Estado inválido. Debe ser "active" o "inactive"' },
        { status: 400 }
      );
    }

    // Verificar que el usuario existe y es un paciente
    const patientExists = await db
      .select()
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient')
        )
      )
      .limit(1);

    if (!patientExists.length) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    // Actualizar el estado del paciente
    await db
      .update(userRoles)
      .set({ 
        status,
        updatedAt: new Date()
      })
      .where(
        and(
          eq(userRoles.userId, patientId),
          eq(userRoles.role, 'patient')
        )
      );

    return NextResponse.json({
      message: `Paciente ${status === 'active' ? 'activado' : 'desactivado'} correctamente`,
      status
    });

  } catch (error) {
    console.error('Error updating patient status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}