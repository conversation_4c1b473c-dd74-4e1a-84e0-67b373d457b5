import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";

const team = [
  {
    name: "<PERSON><PERSON><PERSON>",
    role: "Pediatra General",
    specialty: "Neonatología",
    experience: "15 años",
    image: "https://images.unsplash.com/photo-1559839734-2b71ea197ec2?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    description: "Especialista en atención neonatal y cuidados intensivos pediátricos."
  },
  {
    name: "Dr. <PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    specialty: "Cardiología Pediátrica",
    experience: "12 años",
    image: "https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    description: "Experto en diagnóstico y tratamiento de cardiopatías congénitas."
  },
  {
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    role: "<PERSON><PERSON><PERSON>",
    specialty: "Desarrollo Infantil",
    experience: "10 años",
    image: "https://images.unsplash.com/photo-1594824848637-114e4c67363c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    description: "Especializada en seguimiento del crecimiento y desarrollo infantil."
  },
  {
    name: "Dr. Roberto Silva",
    role: "Pediatra",
    specialty: "Infectología Pediátrica",
    experience: "8 años",
    image: "https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    description: "Especialista en enfermedades infecciosas y programas de vacunación."
  }
];

export default function TeamSection() {
  return (
    <section className="py-20 bg-gradient-to-b from-white to-green-50/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Nuestro Equipo
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Profesionales especializados comprometidos con la salud y bienestar de los más pequeños
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
          {team.map((member) => (
            <Card key={member.name} className="group hover:shadow-lg transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
              <CardContent className="p-6 text-center">
                <div className="relative mb-6">
                  <div className="mx-auto h-24 w-24 overflow-hidden rounded-full ring-4 ring-primary/10 group-hover:ring-primary/20 transition-all duration-300">
                    <Image
                      src={member.image}
                      alt={member.name}
                      width={96}
                      height={96}
                      className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-110"
                    />
                  </div>
                </div>
                
                <div className="space-y-3">
                  <div>
                    <h3 className="text-lg font-semibold text-foreground">
                      {member.name}
                    </h3>
                    <p className="text-sm font-medium text-primary">
                      {member.role}
                    </p>
                  </div>

                  <div className="space-y-2">
                    <Badge variant="secondary" className="text-xs">
                      {member.specialty}
                    </Badge>
                    <p className="text-xs text-muted-foreground">
                      {member.experience} de experiencia
                    </p>
                  </div>

                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {member.description}
                  </p>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-16 text-center">
          <div className="inline-flex items-center rounded-full bg-primary/10 px-6 py-3 text-sm font-medium text-primary">
            <span>Más de 45 años de experiencia combinada en pediatría</span>
          </div>
        </div>
      </div>
    </section>
  );
} 