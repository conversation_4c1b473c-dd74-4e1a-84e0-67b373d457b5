/**
 * PLATAFORMA ROBUSTA DE EMAILS - PUNTO DE ENTRADA PRINCIPAL
 * Exporta todas las funciones públicas del sistema
 */

// Core functions
export { 
  sendSystemEmail, 
  sendPatientEmail, 
  scheduleEmail,
  getEmailStats,
  getRecentEmails 
} from './core';

// Patient flow functions
export { 
  sendPatientFlowEmails,
  handlePatientCreatedManually,
  handlePatientOnboardingCompleted,
  handlePatientCreatedFromAppointment 
} from './patient-flows';

// Types
export type {
  EmailEventType,
  EmailPriority,
  EmailStatus,
  EmailRequest,
  EmailResponse,
  EmailContext,
  PatientCreatedParams,
  AppointmentCreatedParams,
  OnboardingCompletedParams,
  ConsultationCompletedParams
} from './types';

// Templates (para referencia externa si es necesario)
export { emailTemplates } from './templates';

/**
 * FUNCIONES DE CONVENIENCIA PARA USO COMÚN
 */

import { sendPatientFlowEmails } from './patient-flows';
import { sendPatientEmail } from './core';

/**
 * Enviar email de bienvenida post-onboarding
 */
export async function sendWelcomeEmail(
  userId: string,
  userName: string,
  userEmail: string,
  userRole: string,
  requestId?: string
) {
  return sendPatientFlowEmails({
    type: 'onboarding',
    userId,
    userName,
    userEmail,
    userRole,
    requestId
  });
}

/**
 * Enviar email de confirmación de cita
 */
export async function sendAppointmentConfirmation(
  patientId: string,
  appointmentId: string,
  appointmentData: any,
  shortCode: string
) {
  return sendPatientFlowEmails({
    type: 'appointment',
    patientId,
    appointmentId,
    appointmentData,
    shortCode
  });
}

/**
 * Enviar email de paciente creado manualmente
 */
export async function sendPatientCreatedEmail(
  patientId: string,
  patientName: string,
  createdBy: string,
  hasAppointment?: boolean,
  appointmentDate?: Date
) {
  return sendPatientFlowEmails({
    type: 'manual',
    patientId,
    patientName,
    createdBy,
    hasAppointment,
    appointmentDate
  });
}

/**
 * Enviar recordatorio de cita
 */
export async function sendAppointmentReminder(
  recipientEmail: string,
  reminderType: '48h' | '24h' | '2h',
  appointmentData: {
    patientName: string;
    doctorName: string;
    consultoryName: string;
    appointmentDate: string;
    appointmentTime: string;
  },
  context: {
    patientId: string;
    appointmentId: string;
  }
) {
  const eventMap = {
    '48h': 'appointment_reminder_48h' as const,
    '24h': 'appointment_reminder_24h' as const,
    '2h': 'appointment_reminder_2h' as const
  };

  return sendPatientEmail(
    eventMap[reminderType],
    recipientEmail,
    appointmentData,
    context
  );
}

/**
 * Enviar email post-consulta
 */
export async function sendConsultationCompleted(
  recipientEmail: string,
  consultationData: {
    patientName: string;
    doctorName: string;
    consultationDate: string;
    feedbackLink: string;
    prescriptionAttachment?: string;
    followUpDate?: string;
  },
  context: {
    patientId: string;
    consultationId: string;
  }
) {
  return sendPatientEmail(
    'consultation_completed',
    recipientEmail,
    consultationData,
    context
  );
}

/**
 * FUNCIÓN DE MIGRACIÓN
 * Para reemplazar gradualmente el sistema anterior
 */
export async function migrateFromOldEmailSystem(
  oldFunction: 'sendPreCheckinEmail' | 'sendActivationEmail' | 'sendConfirmationEmail',
  ...args: any[]
) {
  console.log(`🔄 Migrando desde función antigua: ${oldFunction}`);
  
  // TODO: Mapear las funciones antiguas a las nuevas
  // Por ahora, solo loggeamos para identificar qué necesita migración
  
  return {
    success: true,
    status: 'sent' as const,
    message: `Migración pendiente para ${oldFunction}`
  };
}