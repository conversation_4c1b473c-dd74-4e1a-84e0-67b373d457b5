import { neon } from '@neondatabase/serverless';
import { config } from 'dotenv';

// Cargar variables de entorno
config({ path: '.env.local' });

const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL no está configurado');
  process.exit(1);
}

const sql = neon(DATABASE_URL);

async function seedCatalogs() {
  try {
    console.log('🌱 Iniciando seed de catálogos...');

    // 1. Países
    console.log('🌍 Seeding países...');
    await sql`
      INSERT INTO countries (id, name, code, phone_code, active) VALUES
      (1, 'Guatemala', 'GT', '+502', true),
      (2, 'México', 'MX', '+52', true),
      (3, 'Estados Unidos', 'US', '+1', true)
      ON CONFLICT (id) DO NOTHING
    `;

    // 2. Departamentos (Guatemala)
    console.log('🏛️ Seeding departamentos...');
    await sql`
      INSERT INTO departments (id, name, code, country_id, active) VALUES
      (1, 'Guatemala', 'GT', 1, true),
      (2, 'Antigua Guatemala', 'SA', 1, true),
      (3, 'Chimaltenango', 'CM', 1, true),
      (4, 'Escuintla', 'ES', 1, true),
      (5, 'Santa Rosa', 'SR', 1, true),
      (6, 'Sololá', 'SO', 1, true),
      (7, 'Totonicapán', 'TO', 1, true),
      (8, 'Quetzaltenango', 'QZ', 1, true),
      (9, 'Suchitepéquez', 'SU', 1, true),
      (10, 'Retalhuleu', 'RE', 1, true),
      (11, 'San Marcos', 'SM', 1, true),
      (12, 'Huehuetenango', 'HU', 1, true),
      (13, 'Quiché', 'QI', 1, true),
      (14, 'Baja Verapaz', 'BV', 1, true),
      (15, 'Alta Verapaz', 'AV', 1, true),
      (16, 'Petén', 'PE', 1, true),
      (17, 'Izabal', 'IZ', 1, true),
      (18, 'Zacapa', 'ZA', 1, true),
      (19, 'Chiquimula', 'CH', 1, true),
      (20, 'Jalapa', 'JA', 1, true),
      (21, 'Jutiapa', 'JU', 1, true),
      (22, 'El Progreso', 'PR', 1, true)
      ON CONFLICT (id) DO NOTHING
    `;

    // 3. Municipios principales
    console.log('🏘️ Seeding municipios...');
    await sql`
      INSERT INTO municipalities (id, name, code, department_id, active) VALUES
      (1, 'Guatemala', 'GT01', 1, true),
      (2, 'Mixco', 'GT02', 1, true),
      (3, 'Villa Nueva', 'GT03', 1, true),
      (4, 'Antigua Guatemala', 'SA01', 2, true),
      (5, 'Chimaltenango', 'CM01', 3, true),
      (6, 'Escuintla', 'ES01', 4, true),
      (7, 'Quetzaltenango', 'QZ01', 8, true),
      (8, 'Huehuetenango', 'HU01', 12, true),
      (9, 'Cobán', 'AV01', 15, true),
      (10, 'Flores', 'PE01', 16, true)
      ON CONFLICT (id) DO NOTHING
    `;

    // 4. Especialidades médicas
    console.log('🩺 Seeding especialidades médicas...');
    await sql`
      INSERT INTO medical_specialties (id, name, code, active) VALUES
      (1, 'Pediatría', 'PED', true),
      (2, 'Neonatología', 'NEO', true),
      (3, 'Cardiología Pediátrica', 'CAP', true),
      (4, 'Neurología Pediátrica', 'NEP', true),
      (5, 'Endocrinología Pediátrica', 'ENP', true),
      (6, 'Gastroenterología Pediátrica', 'GAP', true),
      (7, 'Medicina General', 'MED', true),
      (8, 'Enfermería', 'ENF', true)
      ON CONFLICT (id) DO NOTHING
    `;

    // 5. Ocupaciones
    console.log('💼 Seeding ocupaciones...');
    await sql`
      INSERT INTO occupations (id, name, code, active) VALUES
      (1, 'Médico', 'MED', true),
      (2, 'Enfermero/a', 'ENF', true),
      (3, 'Maestro/a', 'MAE', true),
      (4, 'Ingeniero/a', 'ING', true),
      (5, 'Abogado/a', 'ABO', true),
      (6, 'Comerciante', 'COM', true),
      (7, 'Estudiante', 'EST', true),
      (8, 'Ama de casa', 'AMA', true),
      (9, 'Empleado/a', 'EMP', true),
      (10, 'Otro', 'OTR', true)
      ON CONFLICT (id) DO NOTHING
    `;

    // 6. Relaciones familiares
    console.log('👨‍👩‍👧‍👦 Seeding relaciones...');
    await sql`
      INSERT INTO relationships (id, name, code, active) VALUES
      (1, 'Padre', 'PAD', true),
      (2, 'Madre', 'MAD', true),
      (3, 'Hermano/a', 'HER', true),
      (4, 'Abuelo/a', 'ABU', true),
      (5, 'Tío/a', 'TIO', true),
      (6, 'Primo/a', 'PRI', true),
      (7, 'Cónyuge', 'CON', true),
      (8, 'Amigo/a', 'AMI', true),
      (9, 'Tutor legal', 'TUT', true),
      (10, 'Otro', 'OTR', true)
      ON CONFLICT (id) DO NOTHING
    `;

    console.log('✅ Seed de catálogos completado exitosamente');

    // Verificar datos
    const countriesCount = await sql`SELECT COUNT(*) as count FROM countries WHERE active = true`;
    const departmentsCount = await sql`SELECT COUNT(*) as count FROM departments WHERE active = true`;
    const municipalitiesCount = await sql`SELECT COUNT(*) as count FROM municipalities WHERE active = true`;
    const specialtiesCount = await sql`SELECT COUNT(*) as count FROM medical_specialties WHERE active = true`;
    const occupationsCount = await sql`SELECT COUNT(*) as count FROM occupations WHERE active = true`;
    const relationshipsCount = await sql`SELECT COUNT(*) as count FROM relationships WHERE active = true`;

    console.log('📊 Resumen de datos:');
    console.log(`   - Países: ${countriesCount[0].count}`);
    console.log(`   - Departamentos: ${departmentsCount[0].count}`);
    console.log(`   - Municipios: ${municipalitiesCount[0].count}`);
    console.log(`   - Especialidades médicas: ${specialtiesCount[0].count}`);
    console.log(`   - Ocupaciones: ${occupationsCount[0].count}`);
    console.log(`   - Relaciones: ${relationshipsCount[0].count}`);

  } catch (error) {
    console.error('❌ Error al hacer seed de catálogos:', error);
    process.exit(1);
  }
}

seedCatalogs(); 