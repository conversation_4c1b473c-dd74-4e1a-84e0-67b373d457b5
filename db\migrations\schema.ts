import { pgTable, foreignKey, unique, text, timestamp, jsonb, boolean, index, integer, serial, uniqueIndex } from "drizzle-orm/pg-core"
import { sql } from "drizzle-orm"



export const associationCodes = pgTable("associationCodes", {
	id: text().primaryKey().notNull(),
	code: text().notNull(),
	patientId: text().notNull(),
	expiresAt: timestamp({ mode: 'string' }).notNull(),
	usedBy: text(),
	usedAt: timestamp({ mode: 'string' }),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [user.id],
			name: "associationCodes_patientId_user_id_fk"
		}),
	foreignKey({
			columns: [table.usedBy],
			foreignColumns: [user.id],
			name: "associationCodes_usedBy_user_id_fk"
		}),
	unique("associationCodes_code_unique").on(table.code),
]);

export const doctorAssistantRelations = pgTable("doctorAssistantRelations", {
	id: text().primaryKey().notNull(),
	doctorId: text().notNull(),
	assistantId: text().notNull(),
	consultoryId: text(),
	permissions: jsonb(),
	active: boolean().default(true),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [user.id],
			name: "doctorAssistantRelations_doctorId_user_id_fk"
		}),
	foreignKey({
			columns: [table.assistantId],
			foreignColumns: [user.id],
			name: "doctorAssistantRelations_assistantId_user_id_fk"
		}),
	foreignKey({
			columns: [table.consultoryId],
			foreignColumns: [consultories.id],
			name: "doctorAssistantRelations_consultoryId_consultories_id_fk"
		}),
]);

export const notifications = pgTable("notifications", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	type: text().notNull(),
	title: text().notNull(),
	message: text().notNull(),
	read: boolean().default(false),
	data: jsonb(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "notifications_userId_user_id_fk"
		}),
]);

export const consultories = pgTable("consultories", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	address: text(),
	phone: text(),
	email: text(),
	active: boolean().default(true),
	description: text(),
	services: jsonb(),
	workingHours: jsonb(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
});

export const patientGuardianRelations = pgTable("patientGuardianRelations", {
	id: text().primaryKey().notNull(),
	patientId: text().notNull(),
	guardianId: text().notNull(),
	relationship: text(),
	isPrimary: boolean().default(false),
	canMakeDecisions: boolean().default(true),
	validUntil: timestamp({ mode: 'string' }),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [user.id],
			name: "patientGuardianRelations_patientId_user_id_fk"
		}),
	foreignKey({
			columns: [table.guardianId],
			foreignColumns: [user.id],
			name: "patientGuardianRelations_guardianId_user_id_fk"
		}),
]);

export const registrationRequests = pgTable("registrationRequests", {
	id: text().primaryKey().notNull(),
	userId: text().notNull(),
	status: text().default('pending'),
	reviewedBy: text(),
	reviewedAt: timestamp({ mode: 'string' }),
	reviewNotes: text(),
	rejectionReason: text(),
	submittedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	requestedRoles: jsonb(),
	userData: jsonb(),
	rolesData: jsonb(),
	documentsData: jsonb(),
	approvedRoles: jsonb(),
	rejectedRoles: jsonb(),
}, (table) => [
	index("reg_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("reg_submitted_idx").using("btree", table.submittedAt.asc().nullsLast().op("timestamp_ops")),
	index("reg_user_idx").using("btree", table.userId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "registrationRequests_userId_user_id_fk"
		}),
	foreignKey({
			columns: [table.reviewedBy],
			foreignColumns: [user.id],
			name: "registrationRequests_reviewedBy_user_id_fk"
		}),
]);

export const session = pgTable("session", {
	id: text().primaryKey().notNull(),
	expiresAt: timestamp({ mode: 'string' }).notNull(),
	token: text().notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	ipAddress: text(),
	userAgent: text(),
	userId: text().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "session_userId_user_id_fk"
		}).onDelete("cascade"),
	unique("session_token_unique").on(table.token),
]);

export const subscription = pgTable("subscription", {
	id: text().primaryKey().notNull(),
	createdAt: timestamp({ mode: 'string' }).notNull(),
	modifiedAt: timestamp({ mode: 'string' }),
	amount: integer().notNull(),
	currency: text().notNull(),
	recurringInterval: text().notNull(),
	status: text().notNull(),
	currentPeriodStart: timestamp({ mode: 'string' }).notNull(),
	currentPeriodEnd: timestamp({ mode: 'string' }).notNull(),
	cancelAtPeriodEnd: boolean().default(false).notNull(),
	canceledAt: timestamp({ mode: 'string' }),
	startedAt: timestamp({ mode: 'string' }).notNull(),
	endsAt: timestamp({ mode: 'string' }),
	endedAt: timestamp({ mode: 'string' }),
	customerId: text().notNull(),
	productId: text().notNull(),
	discountId: text(),
	checkoutId: text().notNull(),
	customerCancellationReason: text(),
	customerCancellationComment: text(),
	metadata: text(),
	customFieldData: text(),
	userId: text(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "subscription_userId_user_id_fk"
		}),
]);

export const user = pgTable("user", {
	id: text().primaryKey().notNull(),
	name: text().notNull(),
	email: text().notNull(),
	emailVerified: boolean().default(false).notNull(),
	image: text(),
	status: text().default('pending'),
	statusReason: text(),
	activatedAt: timestamp({ mode: 'string' }),
	activatedBy: text(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	firstName: text("first_name").notNull(),
	lastName: text("last_name").notNull(),
	documentType: text("document_type").notNull(),
	documentNumber: text("document_number").notNull(),
	dateOfBirth: timestamp("date_of_birth", { mode: 'string' }),
	gender: text(),
	phone: text(),
	alternativePhone: text("alternative_phone"),
	countryId: integer("country_id"),
	departmentId: integer("department_id"),
	municipalityId: integer("municipality_id"),
	occupationId: integer("occupation_id"),
	address: text(),
	emergencyContact: text("emergency_contact"),
	emergencyPhone: text("emergency_phone"),
	emergencyRelationshipId: integer("emergency_relationship_id"),
	preferences: jsonb(),
	metadata: jsonb(),
}, (table) => [
	foreignKey({
			columns: [table.countryId],
			foreignColumns: [countries.id],
			name: "user_country_id_countries_id_fk"
		}),
	foreignKey({
			columns: [table.departmentId],
			foreignColumns: [departments.id],
			name: "user_department_id_departments_id_fk"
		}),
	foreignKey({
			columns: [table.municipalityId],
			foreignColumns: [municipalities.id],
			name: "user_municipality_id_municipalities_id_fk"
		}),
	foreignKey({
			columns: [table.occupationId],
			foreignColumns: [occupations.id],
			name: "user_occupation_id_occupations_id_fk"
		}),
	foreignKey({
			columns: [table.emergencyRelationshipId],
			foreignColumns: [relationships.id],
			name: "user_emergency_relationship_id_relationships_id_fk"
		}),
	unique("user_email_unique").on(table.email),
]);

export const verification = pgTable("verification", {
	id: text().primaryKey().notNull(),
	identifier: text().notNull(),
	value: text().notNull(),
	expiresAt: timestamp({ mode: 'string' }).notNull(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
});

export const account = pgTable("account", {
	id: text().primaryKey().notNull(),
	accountId: text().notNull(),
	providerId: text().notNull(),
	userId: text().notNull(),
	accessToken: text(),
	refreshToken: text(),
	idToken: text(),
	accessTokenExpiresAt: timestamp({ mode: 'string' }),
	refreshTokenExpiresAt: timestamp({ mode: 'string' }),
	scope: text(),
	password: text(),
	createdAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
	updatedAt: timestamp({ mode: 'string' }).defaultNow().notNull(),
}, (table) => [
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "account_userId_user_id_fk"
		}).onDelete("cascade"),
]);

export const relationships = pgTable("relationships", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("relationships_code_unique").on(table.code),
]);

export const appointments = pgTable("appointments", {
	id: text().primaryKey().notNull(),
	patientId: text("patient_id").notNull(),
	doctorId: text("doctor_id").notNull(),
	consultoryId: text("consultory_id").notNull(),
	scheduledDate: timestamp("scheduled_date", { mode: 'string' }).notNull(),
	scheduledTime: text("scheduled_time").notNull(),
	duration: integer().default(30),
	appointmentType: text("appointment_type").notNull(),
	status: text().default('scheduled'),
	appointmentData: jsonb("appointment_data"),
	confirmedAt: timestamp("confirmed_at", { mode: 'string' }),
	startedAt: timestamp("started_at", { mode: 'string' }),
	completedAt: timestamp("completed_at", { mode: 'string' }),
	cancelledAt: timestamp("cancelled_at", { mode: 'string' }),
	cancellationReason: text("cancellation_reason"),
	createdBy: text("created_by").notNull(),
	lastModifiedBy: text("last_modified_by"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("appointment_datetime_idx").using("btree", table.scheduledDate.asc().nullsLast().op("timestamp_ops"), table.scheduledTime.asc().nullsLast().op("timestamp_ops")),
	index("appointment_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("appointment_type_idx").using("btree", table.appointmentType.asc().nullsLast().op("text_ops")),
	index("consultory_appointments_idx").using("btree", table.consultoryId.asc().nullsLast().op("text_ops"), table.scheduledDate.asc().nullsLast().op("text_ops")),
	index("doctor_appointments_idx").using("btree", table.doctorId.asc().nullsLast().op("timestamp_ops"), table.scheduledDate.asc().nullsLast().op("text_ops")),
	index("patient_appointments_idx").using("btree", table.patientId.asc().nullsLast().op("timestamp_ops"), table.scheduledDate.asc().nullsLast().op("timestamp_ops")),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [user.id],
			name: "appointments_patient_id_user_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [user.id],
			name: "appointments_doctor_id_user_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.consultoryId],
			foreignColumns: [consultories.id],
			name: "appointments_consultory_id_consultories_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [user.id],
			name: "appointments_created_by_user_id_fk"
		}),
	foreignKey({
			columns: [table.lastModifiedBy],
			foreignColumns: [user.id],
			name: "appointments_last_modified_by_user_id_fk"
		}),
]);

export const medicalRecords = pgTable("medical_records", {
	id: text().primaryKey().notNull(),
	patientId: text("patient_id").notNull(),
	doctorId: text("doctor_id").notNull(),
	consultoryId: text("consultory_id").notNull(),
	recordType: text("record_type").notNull(),
	appointmentId: text("appointment_id"),
	primaryDiagnosis: text("primary_diagnosis"),
	secondaryDiagnoses: jsonb("secondary_diagnoses"),
	vitals: jsonb(),
	recordData: jsonb("record_data"),
	status: text().default('draft'),
	createdBy: text("created_by").notNull(),
	lastModifiedBy: text("last_modified_by"),
	reviewedBy: text("reviewed_by"),
	reviewedAt: timestamp("reviewed_at", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("consultory_records_idx").using("btree", table.consultoryId.asc().nullsLast().op("text_ops")),
	index("doctor_records_idx").using("btree", table.doctorId.asc().nullsLast().op("text_ops")),
	index("patient_records_idx").using("btree", table.patientId.asc().nullsLast().op("text_ops")),
	index("primary_diagnosis_idx").using("btree", table.primaryDiagnosis.asc().nullsLast().op("text_ops")),
	index("record_date_idx").using("btree", table.createdAt.asc().nullsLast().op("timestamp_ops")),
	index("record_status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	index("record_type_idx").using("btree", table.recordType.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.patientId],
			foreignColumns: [user.id],
			name: "medical_records_patient_id_user_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.doctorId],
			foreignColumns: [user.id],
			name: "medical_records_doctor_id_user_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.consultoryId],
			foreignColumns: [consultories.id],
			name: "medical_records_consultory_id_consultories_id_fk"
		}).onDelete("restrict"),
	foreignKey({
			columns: [table.createdBy],
			foreignColumns: [user.id],
			name: "medical_records_created_by_user_id_fk"
		}),
	foreignKey({
			columns: [table.lastModifiedBy],
			foreignColumns: [user.id],
			name: "medical_records_last_modified_by_user_id_fk"
		}),
	foreignKey({
			columns: [table.reviewedBy],
			foreignColumns: [user.id],
			name: "medical_records_reviewed_by_user_id_fk"
		}),
]);

export const municipalities = pgTable("municipalities", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	departmentId: integer("department_id"),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.departmentId],
			foreignColumns: [departments.id],
			name: "municipalities_department_id_departments_id_fk"
		}),
]);

export const occupations = pgTable("occupations", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("occupations_code_unique").on(table.code),
]);

export const medicalSpecialties = pgTable("medical_specialties", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("medical_specialties_code_unique").on(table.code),
]);

export const countries = pgTable("countries", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	phoneCode: text("phone_code"),
}, (table) => [
	unique("countries_code_unique").on(table.code),
]);

export const userRoles = pgTable("user_roles", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	role: text().notNull(),
	status: text().default('active'),
	roleData: jsonb("role_data"),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
	activatedAt: timestamp("activated_at", { mode: 'string' }),
	deactivatedAt: timestamp("deactivated_at", { mode: 'string' }),
}, (table) => [
	index("role_idx").using("btree", table.role.asc().nullsLast().op("text_ops")),
	index("status_idx").using("btree", table.status.asc().nullsLast().op("text_ops")),
	uniqueIndex("user_role_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.role.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_roles_user_id_user_id_fk"
		}).onDelete("cascade"),
]);

export const departments = pgTable("departments", {
	id: serial().primaryKey().notNull(),
	name: text().notNull(),
	code: text().notNull(),
	countryId: integer("country_id"),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	foreignKey({
			columns: [table.countryId],
			foreignColumns: [countries.id],
			name: "departments_country_id_countries_id_fk"
		}),
]);

export const userConsultories = pgTable("user_consultories", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	consultoryId: text("consultory_id").notNull(),
	role: text().notNull(),
	permissions: jsonb(),
	isActive: boolean("is_active").default(true),
	startDate: timestamp("start_date", { mode: 'string' }).defaultNow(),
	endDate: timestamp("end_date", { mode: 'string' }),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("consultory_active_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("consultory_idx").using("btree", table.consultoryId.asc().nullsLast().op("text_ops")),
	index("consultory_role_idx").using("btree", table.role.asc().nullsLast().op("text_ops")),
	uniqueIndex("user_consultory_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.consultoryId.asc().nullsLast().op("text_ops"), table.role.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_consultories_user_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.consultoryId],
			foreignColumns: [consultories.id],
			name: "user_consultories_consultory_id_consultories_id_fk"
		}).onDelete("cascade"),
]);

export const userDocuments = pgTable("user_documents", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	role: text(),
	documentType: text("document_type").notNull(),
	fileName: text("file_name").notNull(),
	filePath: text("file_path").notNull(),
	fileSize: integer("file_size"),
	mimeType: text("mime_type"),
	title: text(),
	description: text(),
	isVerified: boolean("is_verified").default(false),
	verifiedBy: text("verified_by"),
	verifiedAt: timestamp("verified_at", { mode: 'string' }),
	expirationDate: timestamp("expiration_date", { mode: 'string' }),
	uploadedBy: text("uploaded_by"),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("doc_active_idx").using("btree", table.isActive.asc().nullsLast().op("bool_ops")),
	index("role_doc_idx").using("btree", table.role.asc().nullsLast().op("text_ops"), table.documentType.asc().nullsLast().op("text_ops")),
	index("user_doc_type_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.documentType.asc().nullsLast().op("text_ops")),
	index("verification_idx").using("btree", table.isVerified.asc().nullsLast().op("bool_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_documents_user_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.verifiedBy],
			foreignColumns: [user.id],
			name: "user_documents_verified_by_user_id_fk"
		}),
	foreignKey({
			columns: [table.uploadedBy],
			foreignColumns: [user.id],
			name: "user_documents_uploaded_by_user_id_fk"
		}),
]);

export const userSpecialties = pgTable("user_specialties", {
	id: text().primaryKey().notNull(),
	userId: text("user_id").notNull(),
	specialtyId: integer("specialty_id").notNull(),
	isPrimary: boolean("is_primary").default(false),
	certificationDate: timestamp("certification_date", { mode: 'string' }),
	certificationNumber: text("certification_number"),
	isActive: boolean("is_active").default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	index("primary_specialty_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.isPrimary.asc().nullsLast().op("bool_ops")),
	index("specialty_idx").using("btree", table.specialtyId.asc().nullsLast().op("int4_ops")),
	uniqueIndex("user_specialty_idx").using("btree", table.userId.asc().nullsLast().op("text_ops"), table.specialtyId.asc().nullsLast().op("text_ops")),
	foreignKey({
			columns: [table.userId],
			foreignColumns: [user.id],
			name: "user_specialties_user_id_user_id_fk"
		}).onDelete("cascade"),
	foreignKey({
			columns: [table.specialtyId],
			foreignColumns: [medicalSpecialties.id],
			name: "user_specialties_specialty_id_medical_specialties_id_fk"
		}).onDelete("restrict"),
]);

export const systemConfig = pgTable("system_config", {
	id: serial().primaryKey().notNull(),
	key: text().notNull(),
	value: jsonb().notNull(),
	description: text(),
	category: text().default('general'),
	active: boolean().default(true),
	createdAt: timestamp("created_at", { mode: 'string' }).defaultNow(),
	updatedAt: timestamp("updated_at", { mode: 'string' }).defaultNow(),
}, (table) => [
	unique("system_config_key_unique").on(table.key),
]);
