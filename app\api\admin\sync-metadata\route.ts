import { NextRequest, NextResponse } from 'next/server';
import { clerkClient } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const adminUserId = 'user_2zgdfxMtulphGc3A4vkwDRhq4bc';
    
    console.log('🔄 Actualizando metadatos de Clerk para usuario:', adminUserId);
    
    const clerk = await clerkClient();
    
    await clerk.users.updateUserMetadata(adminUserId, {
      publicMetadata: {
        role: 'admin',
        roles: ['admin'],
        status: 'active',
        onboardingCompleted: true
      }
    });
    
    console.log('✅ Metadatos de Clerk actualizados');
    
    return NextResponse.json({
      success: true,
      message: 'Metadatos de Clerk actualizados exitosamente',
      metadata: {
        role: 'admin',
        status: 'active',
        onboardingCompleted: true
      }
    });
    
  } catch (error) {
    console.error('Error actualizando metadatos de Clerk:', error);
    return NextResponse.json(
      { success: false, message: 'Error actualizando metadatos', error: error.message },
      { status: 500 }
    );
  }
}