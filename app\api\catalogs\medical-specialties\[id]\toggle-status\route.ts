import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalSpecialties } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar especialidad médica
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de especialidades médicas.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que la especialidad existe
    const existingSpecialty = await db
      .select()
      .from(medicalSpecialties)
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .limit(1);

    if (existingSpecialty.length === 0) {
      return NextResponse.json({ error: 'Especialidad médica no encontrada' }, { status: 404 });
    }

    // Cambiar el estado
    const newStatus = !existingSpecialty[0].isActive;
    const [updatedSpecialty] = await db
      .update(medicalSpecialties)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedSpecialty,
      message: `Especialidad médica ${newStatus ? 'activada' : 'desactivada'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling medical specialty status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}