'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { 
  Users, 
  Calendar, 
  TrendingUp, 
  Clock,
  Baby,
  Stethoscope,
  FileText,
  CheckCircle,
  AlertCircle,
  DollarSign,
  ArrowUp,
  ArrowDown,
  ChevronRight,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { WaitingPatientsCard } from '@/components/waiting-room/waiting-patients-card';

export default function DoctorDashboard() {
  const { user } = useUser();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [waitingPatients, setWaitingPatients] = useState([]);
  const [inProgressConsultations, setInProgressConsultations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    todayAppointments: 0,
    pendingAppointments: 0,
    monthlyConsultations: 0,
    monthlyRevenue: 0,
    consultationsChange: 0,
    revenueChange: 0,
    consultationsTrend: 'up',
    revenueTrend: 'up',
    currency: 'GTQ',
    weeklyConsultationsCompleted: 0,
    weeklyNewPatients: 0,
    weeklyServicesByCategory: [] as any[],
    recentPatients: [] as any[]
  });

  useEffect(() => {
    if (user) {
      fetchTodayAppointments();
      fetchWaitingPatients();
      fetchInProgressConsultations();
      fetchDoctorStats();
    }
  }, [user]);

  const fetchWaitingPatients = async () => {
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      const response = await fetch(`/api/appointments?status=checked_in&dateFrom=${today}&dateTo=${today}&orderBy=checkedInAt&orderDirection=asc`);
      if (response.ok) {
        const data = await response.json();
        // Filtrar solo las citas del doctor actual
        const myWaitingPatients = (data.data || []).filter(apt => apt.doctorId === user?.id);
        setWaitingPatients(myWaitingPatients);
      }
    } catch (error) {
      console.error('Error fetching waiting patients:', error);
    }
  };

  const fetchInProgressConsultations = async () => {
    try {
      const response = await fetch('/api/medical-consultations');
      if (response.ok) {
        const data = await response.json();
        // Filtrar consultas en draft o in_progress (la API ya filtra por doctor)
        const consultations = (data.data || []).filter((consultation: any) => 
          consultation.status === 'draft' || consultation.status === 'in_progress'
        );
        setInProgressConsultations(consultations);
      }
    } catch (error) {
      console.error('Error fetching in-progress consultations:', error);
    }
  };

  const fetchTodayAppointments = async () => {
    try {
      setLoading(true);
      const today = format(new Date(), 'yyyy-MM-dd');
      
      // Agregar filtro por doctorId
      const doctorId = user?.id;
      if (!doctorId) {
        setLoading(false);
        return;
      }
      
      // Obtener citas de hoy para la lista
      const todayResponse = await fetch(`/api/appointments?dateFrom=${today}&dateTo=${today}&doctorId=${doctorId}`);
      if (todayResponse.ok) {
        const todayData = await todayResponse.json();
        const todayAppointments = todayData.data || [];
        setAppointments(todayAppointments);
        
        // Obtener citas pendientes del mes actual (desde hoy hacia adelante)
        const currentDate = new Date();
        const endOfMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
        const monthEnd = format(endOfMonth, 'yyyy-MM-dd');
        
        const monthResponse = await fetch(`/api/appointments?dateFrom=${today}&dateTo=${monthEnd}&doctorId=${doctorId}`);
        if (monthResponse.ok) {
          const monthData = await monthResponse.json();
          const monthAppointments = monthData.data || [];
          
          // Calcular estadísticas
          const pendingThisMonth = monthAppointments.filter(apt => 
            apt.status === 'scheduled' || apt.status === 'pending_confirmation'
          ).length;
          
          setStats(prev => ({
            ...prev,
            todayAppointments: todayAppointments.length,
            pendingAppointments: pendingThisMonth
          }));
        } else {
          // Si falla la consulta del mes, usar solo las de hoy
          const pending = todayAppointments.filter(apt => 
            apt.status === 'scheduled' || apt.status === 'pending_confirmation'
          ).length;
          
          setStats(prev => ({
            ...prev,
            todayAppointments: todayAppointments.length,
            pendingAppointments: pending
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchDoctorStats = async () => {
    try {
      const response = await fetch('/api/doctor/stats');
      if (response.ok) {
        const data = await response.json();
        const statsData = data.data;
        
        setStats(prev => ({
          ...prev,
          monthlyConsultations: statsData.consultations.current,
          monthlyRevenue: statsData.revenue.current,
          consultationsChange: statsData.consultations.changePercent,
          revenueChange: statsData.revenue.changePercent,
          consultationsTrend: statsData.consultations.trend,
          revenueTrend: statsData.revenue.trend,
          currency: statsData.revenue.currency,
          weeklyConsultationsCompleted: statsData.weeklyStats.consultationsCompleted,
          weeklyNewPatients: statsData.weeklyStats.newPatients,
          weeklyServicesByCategory: statsData.weeklyStats.servicesByCategory,
          recentPatients: statsData.recentPatients
        }));
      }
    } catch (error) {
      console.error('Error fetching doctor stats:', error);
    }
  };

  const getStatusConfig = (status: string) => {
    const configs = {
      scheduled: { icon: AlertCircle, label: 'Programada', color: 'text-[#ADB6CA]', bgColor: 'bg-[#ADB6CA]/10' },
      confirmed: { icon: CheckCircle, label: 'Confirmada', color: 'text-[#50bed2]', bgColor: 'bg-[#50bed2]/10' },
      in_progress: { icon: Clock, label: 'En consulta', color: 'text-[#3D4E80]', bgColor: 'bg-[#3D4E80]/10' },
      completed: { icon: CheckCircle, label: 'Completada', color: 'text-[#ea6cb0]', bgColor: 'bg-[#ea6cb0]/10' },
      cancelled: { icon: AlertCircle, label: 'Cancelada', color: 'text-[#FAC9D1]', bgColor: 'bg-[#FAC9D1]/20' }
    };
    return configs[status] || configs.scheduled;
  };

  const handleStartConsultation = async (appointment: any) => {
    try {
      toast.info('Iniciando consulta médica...');
      
      // Llamar API para crear consulta médica
      const response = await fetch('/api/medical-consultations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          appointmentId: appointment.id
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Consulta médica iniciada exitosamente');
        await fetchWaitingPatients(); // Refrescar para mostrar el nuevo estado
        await fetchInProgressConsultations(); // Actualizar consultas en progreso
        
        // Redirigir al formulario de consulta médica
        router.push(`/dashboard/doctor/consultations/${result.data.consultationId}?appointment=${appointment.id}`);
      } else {
        toast.error(result.error || 'Error al iniciar la consulta');
      }
    } catch (error) {
      console.error('Error starting consultation:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleContinueConsultation = (consultation: any) => {
    router.push(`/dashboard/doctor/consultations/${consultation.id}?appointment=${consultation.appointmentId || ''}`);
  };
  return (
    <div className="space-y-4 md:space-y-6 lg:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-[#3D4E80]">Dashboard</h1>
          <p className="text-sm md:text-base text-[#3D4E80]/70 mt-1 md:mt-2">Bienvenido de vuelta, Dr. Usuario</p>
        </div>
        <div className="hidden sm:block text-xs md:text-sm text-[#3D4E80]/70 bg-white px-3 md:px-4 py-1.5 md:py-2 rounded-lg border border-[#ADB6CA]/30">
          {new Date().toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-[#ea6cb0]/20 bg-[#ea6cb0]/5 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Pacientes Hoy
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#ea6cb0]/20 flex items-center justify-center">
              <Baby className="h-5 w-5 md:h-6 md:w-6 text-[#ea6cb0]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.todayAppointments}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-[#3D4E80]/70">
                {loading ? 'Cargando...' : `${stats.todayAppointments} citas programadas hoy`}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#F8E59A]/50 bg-[#FCEEA8]/20 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Pendientes Este Mes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#F8E59A]/30 flex items-center justify-center">
              <Calendar className="h-5 w-5 md:h-6 md:w-6 text-[#3D4E80]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.pendingAppointments}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-[#3D4E80]/70">
                {loading ? 'Cargando...' : `${stats.pendingAppointments} citas por confirmar este mes`}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#3D4E80]/20 bg-[#3D4E80]/5 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Consultas Mes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#3D4E80]/20 flex items-center justify-center">
              <Stethoscope className="h-5 w-5 md:h-6 md:w-6 text-[#3D4E80]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.monthlyConsultations}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              {!loading && (
                <div className={`flex items-center mr-1 md:mr-2 ${
                  stats.consultationsTrend === 'up' ? 'text-[#50bed2]' : 'text-[#FAC9D1]'
                }`}>
                  {stats.consultationsTrend === 'up' ? (
                    <ArrowUp className="h-3 w-3 md:h-4 md:w-4 mr-0.5 md:mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 md:h-4 md:w-4 mr-0.5 md:mr-1" />
                  )}
                  <span className="font-semibold">{Math.abs(stats.consultationsChange)}%</span>
                </div>
              )}
              <span className="text-[#3D4E80]/70">
                {loading ? 'Cargando...' : 'vs mes anterior'}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#50bed2]/30 bg-[#50bed2]/10 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Ingresos Mes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#50bed2]/20 flex items-center justify-center">
              <DollarSign className="h-5 w-5 md:h-6 md:w-6 text-[#50bed2]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : `${stats.currency === 'USD' ? '$' : 'Q'}${stats.monthlyRevenue.toLocaleString()}`}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              {!loading && (
                <div className={`flex items-center mr-1 md:mr-2 ${
                  stats.revenueTrend === 'up' ? 'text-[#50bed2]' : 'text-[#FAC9D1]'
                }`}>
                  {stats.revenueTrend === 'up' ? (
                    <ArrowUp className="h-3 w-3 md:h-4 md:w-4 mr-0.5 md:mr-1" />
                  ) : (
                    <ArrowDown className="h-3 w-3 md:h-4 md:w-4 mr-0.5 md:mr-1" />
                  )}
                  <span className="font-semibold">{Math.abs(stats.revenueChange)}%</span>
                </div>
              )}
              <span className="text-[#3D4E80]/70">
                {loading ? 'Cargando...' : 'vs mes anterior'}
              </span>
            </div>
          </CardContent>
        </Card>
      </div>


      {/* Main Content Grid - Sala de Espera y Consultas en Progreso */}
      <div className="grid gap-4 md:gap-6 lg:gap-8 xl:grid-cols-3">
        {/* Sala de Espera */}
        <Card className="xl:col-span-2 border-0 shadow-sm border-[#FAC9D1]/20 bg-[#FAC9D1]/5">
          <CardHeader className="pb-3 md:pb-4">
            <CardTitle className="text-lg md:text-xl font-semibold text-[#3D4E80] flex items-center justify-between">
              <div className="flex items-center">
                <Users className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-[#ea6cb0]" />
                Sala de Espera
                {waitingPatients.length > 0 && (
                  <Badge className="ml-2 bg-[#ea6cb0]/10 text-[#ea6cb0] border-[#ea6cb0]/20">
                    {waitingPatients.length}
                  </Badge>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={fetchWaitingPatients}
                disabled={loading}
                className="h-8 w-8 p-0"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Clock className="h-8 w-8 animate-spin text-[#ADB6CA] mx-auto mb-2" />
                  <p className="text-[#3D4E80]/70">Cargando pacientes en espera...</p>
                </div>
              </div>
            ) : waitingPatients.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-[#ADB6CA]/50 mx-auto mb-4" />
                <p className="text-[#3D4E80]/70">No hay pacientes en sala de espera</p>
              </div>
            ) : (
              <div className="space-y-4">
                {waitingPatients.map((patient) => (
                  <div key={patient.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-3 px-4 md:py-4 md:px-5 bg-white rounded-xl border border-[#ADB6CA]/20 hover:bg-[#FAC9D1]/10 hover:border-[#ea6cb0]/30 transition-colors">
                    <div className="flex items-center space-x-3 md:space-x-4">
                      <div className="w-2 h-2 bg-[#ea6cb0] rounded-full animate-pulse"></div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-[#3D4E80] text-sm md:text-base">
                            {patient.patientName || `${patient.patientFirstName || ''} ${patient.patientLastName || ''}`.trim()}
                          </p>
                          {patient.isDependent && (
                            <Badge variant="outline" className="text-xs bg-[#FCEEA8]/30 text-[#3D4E80] border-[#F8E59A]">
                              <Baby className="h-3 w-3 mr-1" />
                              Menor
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-2 text-xs md:text-sm text-[#3D4E80]/70 mt-1">
                          <span>Llegó: {format(new Date(patient.checkedInAt), 'HH:mm', { locale: es })}</span>
                          <span>•</span>
                          <span>Esperando: {Math.floor((new Date().getTime() - new Date(patient.checkedInAt).getTime()) / (1000 * 60))} min</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="bg-[#FAC9D1]/30 text-[#ea6cb0] border-[#FAC9D1]">
                        En espera
                      </Badge>
                      <Button 
                        size="sm" 
                        onClick={() => handleStartConsultation(patient)}
                        className="bg-[#ea6cb0] hover:bg-[#ea6cb0]/80 text-white"
                      >
                        Atender
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Consultas en Progreso - Sidebar compacto */}
        <Card className="border-0 shadow-sm border-[#3D4E80]/20 bg-[#3D4E80]/5">
          <CardHeader className="pb-3">
            <CardTitle className="text-lg font-semibold text-[#3D4E80] flex items-center">
              <Stethoscope className="h-4 w-4 mr-2 text-[#50bed2]" />
              En Progreso
              {inProgressConsultations.length > 0 && (
                <Badge className="ml-2 bg-[#3D4E80]/10 text-[#3D4E80] border-[#3D4E80]/20">
                  {inProgressConsultations.length}
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {inProgressConsultations.length === 0 ? (
              <div className="text-center py-6">
                <Stethoscope className="h-8 w-8 text-[#ADB6CA]/50 mx-auto mb-3" />
                <p className="text-[#3D4E80]/70 text-sm">Sin consultas activas</p>
              </div>
            ) : (
              <div className="space-y-3">
                {inProgressConsultations.map((consultation) => (
                  <div 
                    key={consultation.id} 
                    className="p-3 bg-white rounded-lg border border-[#ADB6CA]/30 hover:shadow-md transition-shadow cursor-pointer hover:border-[#50bed2]/50"
                    onClick={() => handleContinueConsultation(consultation)}
                  >
                    <div className="flex items-center space-x-2 mb-2">
                      <div className="w-2 h-2 bg-[#50bed2] rounded-full animate-pulse"></div>
                      <p className="font-medium text-[#3D4E80] text-sm truncate">
                        {consultation.patientName || consultation.patientFirstName + ' ' + consultation.patientLastName}
                      </p>
                    </div>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-[#3D4E80]/70">
                        {format(new Date(consultation.createdAt), 'HH:mm')}
                      </p>
                      <Badge variant="outline" className="bg-[#FCEEA8]/30 text-[#3D4E80] border-[#F8E59A] text-xs">
                        {consultation.status === 'draft' ? 'Borrador' : 'Activa'}
                      </Badge>
                    </div>
                    <Button size="sm" className="w-full mt-2 bg-[#3D4E80] hover:bg-[#3D4E80]/80 text-white text-xs">
                      Continuar <ChevronRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Citas de Hoy - Ahora en segunda fila */}
      <div className="grid gap-4 md:gap-6 lg:gap-8 xl:grid-cols-3">
        {/* Citas de Hoy */}
        <Card className="xl:col-span-2 border-0 shadow-sm border-[#50bed2]/20 bg-[#50bed2]/5">
          <CardHeader className="pb-3 md:pb-4">
            <CardTitle className="text-lg md:text-xl font-semibold text-[#3D4E80] flex items-center">
              <Calendar className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-[#50bed2]" />
              Citas de Hoy
            </CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="text-center">
                  <Clock className="h-8 w-8 animate-spin text-[#ADB6CA] mx-auto mb-2" />
                  <p className="text-[#3D4E80]/70">Cargando citas de hoy...</p>
                </div>
              </div>
            ) : appointments.length === 0 ? (
              <div className="text-center py-8">
                <Calendar className="h-12 w-12 text-[#ADB6CA]/50 mx-auto mb-4" />
                <p className="text-[#3D4E80]/70">No tienes citas programadas para hoy</p>
              </div>
            ) : (
              <div className="space-y-4">
                {appointments.map((appointment) => {
                  const statusConfig = getStatusConfig(appointment.status);
                  const StatusIcon = statusConfig.icon;
                  
                  return (
                    <div key={appointment.id} className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 py-3 px-4 md:py-4 md:px-5 bg-white rounded-xl border border-[#ADB6CA]/20 hover:bg-[#FCEEA8]/10 hover:border-[#50bed2]/30 transition-colors">
                      <div className="flex items-center space-x-3 md:space-x-4">
                        <div className="text-sm md:text-base font-semibold text-[#3D4E80] min-w-[50px] md:min-w-[60px] bg-[#50bed2]/10 px-2 md:px-3 py-0.5 md:py-1 rounded-lg">
                          {format(new Date(appointment.startTime), 'HH:mm')}
                        </div>
                        <div className="flex-1">
                          <p className="text-sm md:text-base font-semibold text-[#3D4E80]">
                            {appointment.patientFirstName && appointment.patientLastName 
                              ? `${appointment.patientFirstName} ${appointment.patientLastName}`
                              : appointment.activityTypeName || 'Actividad sin paciente'
                            }
                          </p>
                          <p className="text-xs md:text-sm text-[#3D4E80]/70">
                            {appointment.serviceName || appointment.activityTypeName || appointment.title || 'Consulta'}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Badge className={`${statusConfig.bgColor} ${statusConfig.color} border-0`}>
                          <StatusIcon className="h-3 w-3 mr-1" />
                          {statusConfig.label}
                        </Badge>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card className="border-0 shadow-sm border-[#ADB6CA]/20 bg-[#ADB6CA]/5">
          <CardHeader className="pb-3 md:pb-4">
            <CardTitle className="text-lg md:text-xl font-semibold text-[#3D4E80]">Acciones Rápidas</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {[
                { icon: Calendar, label: 'Agendar Cita', color: 'bg-[#50bed2] hover:bg-[#50bed2]/80', href: '/dashboard/doctor/agenda' },
                { icon: Users, label: 'Nuevo Paciente', color: 'bg-[#ea6cb0] hover:bg-[#ea6cb0]/80', href: '/dashboard/doctor/pacientes/create' },
                { icon: FileText, label: 'Ver Expedientes', color: 'bg-[#3D4E80] hover:bg-[#3D4E80]/80', href: '/dashboard/doctor/expedientes' },
                { icon: Stethoscope, label: 'Mi Agenda', color: 'bg-[#F8E59A] hover:bg-[#F8E59A]/80 text-[#3D4E80]', href: '/dashboard/doctor/agenda' },
              ].map((action, index) => {
                const Icon = action.icon;
                return (
                  <Link
                    key={index}
                    href={action.href}
                    className={`w-full flex items-center space-x-3 md:space-x-4 p-3 md:p-4 rounded-xl ${action.color} ${action.color.includes('text-[#3D4E80]') ? '' : 'text-white'} transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5 block`}
                  >
                    <Icon className="h-4 w-4 md:h-5 md:w-5" />
                    <span className="text-sm md:text-base font-medium">{action.label}</span>
                  </Link>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Bottom Section */}
      <div className="grid gap-4 md:gap-6 lg:gap-8 xl:grid-cols-2">
        {/* Pacientes Recientes */}
        <Card className="border-0 shadow-sm border-[#ea6cb0]/20 bg-[#ea6cb0]/5">
          <CardHeader className="pb-3 md:pb-4">
            <CardTitle className="text-lg md:text-xl font-semibold text-[#3D4E80] flex items-center">
              <Users className="h-4 w-4 md:h-5 md:w-5 mr-2 md:mr-3 text-[#ea6cb0]" />
              Pacientes Recientes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.recentPatients.length > 0 ? stats.recentPatients.map((patient, index) => (
                <div key={index} className="flex items-center space-x-3 md:space-x-4 p-2.5 md:p-3 rounded-lg hover:bg-[#ea6cb0]/10 transition-colors">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#ea6cb0] to-[#50bed2] rounded-full flex items-center justify-center text-white text-xs md:text-sm font-semibold">
                    {patient.avatar}
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="text-sm md:text-base font-semibold text-[#3D4E80] truncate">{patient.name}</p>
                    <p className="text-xs md:text-sm text-[#3D4E80]/70">{patient.age} • Última visita: {patient.lastVisit}</p>
                  </div>
                </div>
              )) : (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-[#ADB6CA]/50 mx-auto mb-4" />
                  <p className="text-[#3D4E80]/70">No hay pacientes recientes</p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Resumen Semanal - Servicios Prestados */}
        <Card className="border-0 shadow-sm border-[#FCEEA8]/50 bg-[#FCEEA8]/20">
          <CardHeader className="pb-3 md:pb-4">
            <CardTitle className="text-lg md:text-xl font-semibold text-[#3D4E80]">Resumen Semanal - Servicios Prestados</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 md:space-y-6">
              {/* Servicios por categoría */}
              {stats.weeklyServicesByCategory.map(([category, count], index) => (
                <div key={category} className="flex items-center justify-between">
                  <span className="text-base font-medium text-[#3D4E80]/80">{category}</span>
                  <span className="text-2xl font-bold text-[#3D4E80]">{count}</span>
                </div>
              ))}
              {stats.weeklyServicesByCategory.length === 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-base font-medium text-[#3D4E80]/80">Sin servicios esta semana</span>
                  <span className="text-2xl font-bold text-[#3D4E80]">0</span>
                </div>
              )}
              
              {/* Índice de satisfacción */}
              <div className="pt-4 border-t border-[#F8E59A]/50">
                <div className="flex items-center justify-between">
                  <span className="text-base font-semibold text-[#3D4E80]">Satisfacción</span>
                  <div className="flex items-center space-x-2">
                    <div className="flex text-[#F8E59A]">
                      {[...Array(5)].map((_, i) => (
                        <span key={i} className="text-lg">★</span>
                      ))}
                    </div>
                    <span className="text-lg font-bold text-[#3D4E80]">4.9</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}