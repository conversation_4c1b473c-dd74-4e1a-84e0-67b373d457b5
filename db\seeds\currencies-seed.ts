import { db } from '@/db/drizzle';
import { currencies } from '@/db/schema';

export const currenciesData = [
  {
    id: 'gtq',
    name: 'Quetzal Guatemalteco',
    code: 'GTQ',
    symbol: 'Q',
    exchangeRate: 1.0,
    isDefault: true,
    isActive: true
  },
  {
    id: 'usd',
    name: 'D<PERSON>lar Estadounidense',
    code: 'USD',
    symbol: '$',
    exchangeRate: 7.75,
    isDefault: false,
    isActive: true
  },
  {
    id: 'eur',
    name: 'Euro',
    code: 'EUR',
    symbol: '€',
    exchangeRate: 8.45,
    isDefault: false,
    isActive: true
  }
];

export async function seedCurrencies() {
  console.log('🪙 Seeding currencies...');
  
  try {
    await db.insert(currencies).values(currenciesData).onConflictDoNothing();
    console.log('✅ Currencies seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding currencies:', error);
    throw error;
  }
}