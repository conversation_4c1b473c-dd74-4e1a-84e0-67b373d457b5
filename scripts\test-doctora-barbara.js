require('dotenv').config({ path: '.env.local' });
const { Resend } = require('resend');

async function testWithDomain() {
  const email = process.argv[2] || '<EMAIL>';
  const apiKey = process.env.RESEND_API_KEY;
  const fromEmail = process.env.RESEND_FROM_EMAIL || '<PERSON><PERSON> <<EMAIL>>';
  
  console.log('🏥 Sistema: Doctor<PERSON>');
  console.log('📧 Email destino:', email);
  console.log('📤 Email remitente:', fromEmail);
  console.log('🔑 API Key:', apiKey.substring(0, 20) + '...');
  console.log('');

  try {
    const resend = new Resend(apiKey);
    
    console.log('📤 Enviando email con dominio verificado...');
    
    const { data, error } = await resend.emails.send({
      from: fromEmail,
      to: [email],
      subject: '🏥 Test - Sistema <PERSON>',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="text-align: center; margin-bottom: 30px;">
            <h1 style="color: #10b981; margin-bottom: 10px;">🏥 Sistema Doctora Barbara</h1>
            <p style="color: #666; font-size: 16px;">Email de Prueba Exitoso</p>
          </div>
          
          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
            <h2 style="color: #333; margin-top: 0;">¡Resend Configurado Correctamente! ✅</h2>
            <p>Si estás viendo este mensaje, el sistema de emails está funcionando perfectamente.</p>
          </div>
          
          <div style="border-left: 4px solid #10b981; padding-left: 15px; margin: 20px 0;">
            <h3 style="color: #333; margin-top: 0;">Detalles Técnicos:</h3>
            <ul style="color: #666;">
              <li><strong>Remitente:</strong> ${fromEmail}</li>
              <li><strong>Destinatario:</strong> ${email}</li>
              <li><strong>Fecha:</strong> ${new Date().toLocaleString()}</li>
              <li><strong>Dominio:</strong> doctorabarbara.com</li>
            </ul>
          </div>
          
          <div style="background: #e8f5e8; padding: 15px; border-radius: 6px; margin: 20px 0;">
            <p style="margin: 0; color: #2d5a2d; font-weight: 500;">
              🎉 ¡Ahora puedes crear citas y recibirás emails automáticamente!
            </p>
          </div>
          
          <hr style="border: none; border-top: 1px solid #eee; margin: 30px 0;">
          <p style="color: #999; font-size: 14px; text-align: center;">
            Sistema Médico Doctora Barbara • Emails automáticos habilitados
          </p>
        </div>
      `,
    });

    if (error) {
      console.error('❌ Error de Resend:', error);
      
      if (error.message && error.message.includes('domain')) {
        console.log('\n💡 El dominio doctorabarbara.com debe estar verificado en Resend');
        console.log('   Ve a: https://resend.com/domains');
      }
      return;
    }

    console.log('✅ Email enviado exitosamente!');
    console.log('📬 ID del mensaje:', data.id);
    console.log('\n👉 Revisa tu <NAME_EMAIL>');
    console.log('📁 También revisa: Spam, Promociones, Todas las bandejas');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    
    if (error.message.includes('domain')) {
      console.log('\n💡 Asegúrate de que doctorabarbara.com esté verificado en Resend');
    }
  }
}

testWithDomain();