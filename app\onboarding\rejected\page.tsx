'use client';

import { useState, useEffect } from 'react';
import { useUser, useClerk } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { 
  XCircle, 
  RefreshCw, 
  FileText, 
  Clock,
  AlertCircle,
  AlertTriangle
} from 'lucide-react';

interface RejectionInfo {
  reason: string;
  notes?: string;
  rejectedAt: string;
  rejectedBy?: string;
  role: string;
}

export default function RejectedPage() {
  const { user } = useUser();
  const { signOut } = useClerk();
  const router = useRouter();
  const [rejectionInfo, setRejectionInfo] = useState<RejectionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [restarting, setRestarting] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);

  useEffect(() => {
    const fetchRejectionInfo = async () => {
      try {
        const response = await fetch('/api/onboarding/rejection-info');
        const result = await response.json();
        
        if (result.success) {
          setRejectionInfo(result.data);
        } else {
          // Si no se encuentra información específica, mostrar información genérica
          setRejectionInfo({
            reason: 'Tu solicitud anterior fue rechazada. Puedes intentar nuevamente.',
            rejectedAt: new Date().toISOString(),
            role: 'unknown'
          });
        }
      } catch (error) {
        console.error('Error fetching rejection info:', error);
        // Mostrar información genérica en caso de error
        setRejectionInfo({
          reason: 'Tu solicitud anterior fue rechazada. Puedes intentar nuevamente.',
          rejectedAt: new Date().toISOString(),
          role: 'unknown'
        });
      } finally {
        setLoading(false);
      }
    };

    if (user) {
      fetchRejectionInfo();
    }
  }, [user]);

  const handleRestartOnboarding = () => {
    console.log('🎯 Botón "Reiniciar Registro" presionado');
    setShowConfirmDialog(true);
  };

  const handleConfirmRestart = async () => {
    console.log('🎯 Botón "Sí, Reiniciar" presionado en diálogo');
    setShowConfirmDialog(false);
    setRestarting(true);
    try {
      console.log('🔄 Iniciando restart del onboarding...');
      
      const response = await fetch('/api/onboarding/restart', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();
      console.log('📡 Respuesta del restart:', result);
      
      if (result.success && result.logoutRequired) {
        console.log('✅ Restart exitoso, cerrando sesión...');
        // Mostrar mensaje y cerrar sesión
        await signOut({ redirectUrl: '/' });
      } else {
        console.error('❌ Error restarting onboarding:', result.message);
      }
    } catch (error) {
      console.error('❌ Error restarting onboarding:', error);
    } finally {
      setRestarting(false);
    }
  };



  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-orange-50 flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-4">
          <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
            <XCircle className="h-8 w-8 text-red-600" />
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Solicitud Rechazada
            </h1>
            <p className="text-gray-600 mt-2">
              Tu solicitud de registro no fue aprobada, pero puedes intentar nuevamente
            </p>
          </div>
        </div>

        {/* Rejection Details */}
        {rejectionInfo && (
          <Card className="border-red-200 bg-red-50/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-800">
                <FileText className="h-5 w-5" />
                Detalles del Rechazo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-red-700">Rol Solicitado:</label>
                <p className="text-red-900">
                  {rejectionInfo.role === 'doctor' ? 'Doctor' :
                   rejectionInfo.role === 'assistant' ? 'Asistente' :
                   rejectionInfo.role === 'patient' ? 'Paciente' :
                   rejectionInfo.role === 'guardian' ? 'Guardian' :
                   rejectionInfo.role === 'provider' ? 'Proveedor' :
                   rejectionInfo.role}
                </p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-red-700">Razón del Rechazo:</label>
                <p className="text-red-900 bg-white/50 p-3 rounded-lg border border-red-200">
                  {rejectionInfo.reason}
                </p>
              </div>


              <div className="flex items-center gap-2 text-sm text-red-600">
                <Clock className="h-4 w-4" />
                <span>
                  Rechazado el {new Date(rejectionInfo.rejectedAt).toLocaleDateString('es-ES', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="border-blue-200 bg-blue-50/50">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-semibold text-blue-900 mb-2">¿Qué puedes hacer ahora?</h3>
                <ul className="space-y-1 text-sm text-blue-800">
                  <li>• Revisa la razón del rechazo</li>
                  <li>• Corrige la información que fue señalada</li>
                  <li>• Prepara mejor documentación si es necesario</li>
                  <li>• Reinicia el proceso de registro con la información corregida</li>
                  <li>• Explora nuestro sitio y servicios mientras tanto</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Actions */}
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  ¿Listo para intentar de nuevo?
                </h3>
                <p className="text-gray-600 text-sm mb-6">
                  Reinicia tu proceso de registro corrigiendo los puntos señalados en el rechazo, 
                  o explora nuestro sitio haciendo clic en el logo.
                </p>
              </div>

              {/* Alerta importante */}
              <div className="bg-amber-50 p-4 rounded-lg border border-amber-200">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-semibold text-amber-900 mb-1">
                      Importante antes de reiniciar
                    </h4>
                    <p className="text-sm text-amber-800">
                      Al reiniciar tu registro se eliminará permanentemente toda la información del rechazo. 
                      <strong> Asegúrate de haber anotado los puntos a corregir</strong> antes de continuar.
                    </p>
                  </div>
                </div>
              </div>

              <div className="flex justify-center">
                <Button
                  onClick={handleRestartOnboarding}
                  disabled={restarting}
                  className="bg-green-600 hover:bg-green-700 text-white px-8"
                >
                  {restarting ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Reiniciando...
                    </>
                  ) : (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2" />
                      Reiniciar Registro
                    </>
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Support */}
        <div className="text-center text-sm text-gray-500">
          <p>
            ¿Tienes dudas sobre el rechazo?{' '}
            <a href="mailto:<EMAIL>" className="text-green-600 hover:underline">
              Contacta a soporte
            </a>
          </p>
        </div>
      </div>

      {/* Dialog de confirmación */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Confirmar Reinicio de Registro
            </DialogTitle>
            <DialogDescription>
              Al reiniciar tu registro se realizarán las siguientes acciones:
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <ul className="list-disc list-inside space-y-1 text-sm">
              <li><strong>Se eliminará permanentemente</strong> toda la información del rechazo</li>
              <li><strong>Ya no podrás consultar</strong> la razón específica del rechazo</li>
              <li><strong>Se limpiará</strong> tu historial de solicitudes anteriores</li>
              <li><strong>Se cerrará tu sesión</strong> y deberás iniciar sesión nuevamente</li>
              <li><strong>Podrás iniciar</strong> un nuevo proceso de onboarding desde cero</li>
            </ul>
            <div className="bg-amber-50 p-3 rounded-lg border border-amber-200">
              <p className="text-sm text-amber-800 font-medium">
                ⚠️ Importante: Asegúrate de haber tomado nota de los puntos a corregir antes de continuar. Tu sesión se cerrará y deberás volver a iniciar sesión.
              </p>
            </div>
          </div>
          <DialogFooter className="flex-col sm:flex-row gap-2">
            <Button 
              variant="outline" 
              onClick={() => setShowConfirmDialog(false)}
              className="w-full sm:w-auto"
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleConfirmRestart}
              disabled={restarting}
              className="w-full sm:w-auto bg-green-600 hover:bg-green-700"
            >
              {restarting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Reiniciando...
                </>
              ) : (
                <>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Sí, Reiniciar
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}