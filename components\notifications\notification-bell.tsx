'use client';

import { useState, useEffect } from 'react';
import { Bell, Check, X, Clock, UserCheck, Calendar, AlertCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ScrollArea } from '@/components/ui/scroll-area';
import { formatDistanceToNow } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface Notification {
  id: string;
  type: 'check_in' | 'appointment' | 'system' | 'alert';
  title: string;
  message: string;
  read: boolean;
  createdAt: string;
  data?: {
    appointmentId?: string;
    patientName?: string;
    doctorName?: string;
    time?: string;
  };
}

interface NotificationBellProps {
  className?: string;
}

const notificationIcons = {
  check_in: UserCheck,
  appointment: Calendar,
  system: Bell,
  alert: AlertCircle
};

const notificationColors = {
  check_in: 'text-teal-600 bg-teal-50',
  appointment: 'text-blue-600 bg-blue-50',
  system: 'text-gray-600 bg-gray-50',
  alert: 'text-orange-600 bg-orange-50'
};

export function NotificationBell({ className }: NotificationBellProps) {
  // Versión simplificada temporal para identificar el problema
  return (
    <Button
      variant="ghost"
      size="lg"
      className={cn(
        "text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 w-10 md:h-12 md:w-12 p-0 rounded-xl relative",
        className
      )}
    >
      <Bell className="h-5 w-5 md:h-6 md:w-6" />
    </Button>
  );

  // Código original comentado temporalmente
  /*
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  /*
  const fetchNotifications = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/notifications');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.notifications || []);
        setUnreadCount(data.unreadCount || 0);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}/read`, {
        method: 'PUT'
      });
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => 
            notif.id === notificationId 
              ? { ...notif, read: true }
              : notif
          )
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications/read-all', {
        method: 'PUT'
      });
      
      if (response.ok) {
        setNotifications(prev => 
          prev.map(notif => ({ ...notif, read: true }))
        );
        setUnreadCount(0);
        toast.success('Todas las notificaciones marcadas como leídas');
      }
    } catch (error) {
      console.error('Error marking all as read:', error);
      toast.error('Error al marcar notificaciones');
    }
  };

  const deleteNotification = async (notificationId: string) => {
    try {
      const response = await fetch(`/api/notifications/${notificationId}`, {
        method: 'DELETE'
      });
      
      if (response.ok) {
        const deletedNotif = notifications.find(n => n.id === notificationId);
        setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
        if (deletedNotif && !deletedNotif.read) {
          setUnreadCount(prev => Math.max(0, prev - 1));
        }
      }
    } catch (error) {
      console.error('Error deleting notification:', error);
    }
  };

  // Actualizar notificaciones cada 30 segundos
  useEffect(() => {
    fetchNotifications();
    const interval = setInterval(fetchNotifications, 30000);
    return () => clearInterval(interval);
  }, []);

  // Simular nuevas notificaciones por now (se reemplazará con WebSocket)
  useEffect(() => {
    // Simulación de polling para nuevas notificaciones
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch('/api/notifications/poll');
        if (response.ok) {
          const data = await response.json();
          if (data.hasNew) {
            fetchNotifications();
            // Mostrar toast para nuevas notificaciones de check-in
            if (data.newNotifications?.some((n: any) => n.type === 'check_in')) {
              toast.info('Nuevo paciente ha llegado!', {
                description: 'Revisa las notificaciones para más detalles'
              });
            }
          }
        }
      } catch (error) {
        // Silencioso para no spam de errores
      }
    }, 15000); // Cada 15 segundos

    return () => clearInterval(pollInterval);
  }, []);

  return (
    <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="lg"
          className={cn(
            "text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 w-10 md:h-12 md:w-12 p-0 rounded-xl relative",
            className
          )}
        >
          <Bell className="h-5 w-5 md:h-6 md:w-6" />
          {unreadCount > 0 && (
            <Badge 
              className="absolute -top-1 -right-1 h-5 w-5 md:h-6 md:w-6 p-0 bg-red-500 hover:bg-red-500 text-[10px] md:text-xs flex items-center justify-center rounded-full border-2 border-white font-semibold"
            >
              {unreadCount > 99 ? '99+' : unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      
      <DropdownMenuContent 
        align="end" 
        className="w-80 md:w-96 max-h-[500px] p-0"
        sideOffset={8}
      >
        <div className="flex items-center justify-between p-4 border-b">
          <DropdownMenuLabel className="text-base font-semibold p-0">
            Notificaciones
            {unreadCount > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {unreadCount} nuevas
              </Badge>
            )}
          </DropdownMenuLabel>
          {unreadCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={markAllAsRead}
              className="text-blue-600 hover:text-blue-800 h-auto p-1 text-xs"
            >
              Marcar todas
            </Button>
          )}
        </div>

        <ScrollArea className="max-h-[400px]">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <Clock className="h-6 w-6 animate-spin text-gray-400" />
            </div>
          ) : notifications.length === 0 ? (
            <div className="text-center py-8 px-4">
              <Bell className="h-12 w-12 mx-auto mb-3 text-gray-300" />
              <p className="text-sm text-gray-500">No hay notificaciones</p>
            </div>
          ) : (
            <div className="divide-y">
              {notifications.map((notification) => {
                const Icon = notificationIcons[notification.type];
                const colorClass = notificationColors[notification.type];
                
                return (
                  <div
                    key={notification.id}
                    className={cn(
                      "p-4 hover:bg-gray-50 transition-colors relative group",
                      !notification.read && "bg-blue-50/50"
                    )}
                  >
                    <div className="flex items-start gap-3">
                      <div className={cn(
                        "h-8 w-8 rounded-full flex items-center justify-center flex-shrink-0",
                        colorClass
                      )}>
                        <Icon className="h-4 w-4" />
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-2">
                          <div className="flex-1 min-w-0">
                            <p className={cn(
                              "text-sm font-medium text-gray-900 truncate",
                              !notification.read && "font-semibold"
                            )}>
                              {notification.title}
                            </p>
                            <p className="text-sm text-gray-600 mt-1 line-clamp-2">
                              {notification.message}
                            </p>
                            <p className="text-xs text-gray-400 mt-2">
                              {formatDistanceToNow(new Date(notification.createdAt), { 
                                addSuffix: true,
                                locale: es 
                              })}
                            </p>
                          </div>
                          
                          <div className="flex gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            {!notification.read && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => markAsRead(notification.id)}
                                className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800"
                                title="Marcar como leída"
                              >
                                <Check className="h-3 w-3" />
                              </Button>
                            )}
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteNotification(notification.id)}
                              className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                              title="Eliminar"
                            >
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                    
                    {!notification.read && (
                      <div className="absolute left-2 top-4 w-2 h-2 bg-blue-600 rounded-full" />
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </ScrollArea>
        
        {notifications.length > 0 && (
          <div className="border-t p-3">
            <Button
              variant="ghost"
              className="w-full text-sm text-blue-600 hover:text-blue-800"
              onClick={() => {
                setIsOpen(false);
                // TODO: Navegar a página de notificaciones completa
              }}
            >
              Ver todas las notificaciones
            </Button>
          </div>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
  */
}