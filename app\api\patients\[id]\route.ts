import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  user, 
  userRoles, 
  medicalRecords, 
  guardianPatientRelations,
  consultories,
  medicalConsultations,
  appointments,
  patientMedicalHistory,
  countries,
  departments,
  municipalities,
  occupations,
  relationships
} from '@/db/schema';
import { eq, and, desc, sql } from 'drizzle-orm';
import { alias } from 'drizzle-orm/pg-core';
import { formatPhoneForDisplay, formatPhoneForStorage } from '@/lib/phone-utils';

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;

    // 1. DATOS BÁSICOS DEL PACIENTE con geolocalización
    const patientData = await db
      .select({
        // Datos del usuario
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        alternativePhone: user.alternativePhone,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        address: user.address,
        countryId: user.countryId,
        departmentId: user.departmentId,
        municipalityId: user.municipalityId,
        occupationId: user.occupationId,
        emergencyContact: user.emergencyContact,
        emergencyPhone: user.emergencyPhone,
        emergencyRelationshipId: user.emergencyRelationshipId,
        overallStatus: user.overallStatus,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
        // Nombres de las relaciones
        countryName: countries.name,
        departmentName: departments.name,
        municipalityName: municipalities.name,
        occupationName: occupations.name,
        emergencyRelationshipName: relationships.name
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .leftJoin(countries, eq(user.countryId, countries.id))
      .leftJoin(departments, eq(user.departmentId, departments.id))
      .leftJoin(municipalities, eq(user.municipalityId, municipalities.id))
      .leftJoin(occupations, eq(user.occupationId, occupations.id))
      .leftJoin(relationships, eq(user.emergencyRelationshipId, relationships.id))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient')
        )
      )
      .limit(1);

    if (!patientData.length) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    const patient = patientData[0];
    console.log('🔍 Datos del paciente en API:', {
      id: patient.id,
      firstName: patient.firstName,
      lastName: patient.lastName,
      dateOfBirth: patient.dateOfBirth,
      email: patient.email
    });
    
    // Calcular edad
    const patientAge = patient.dateOfBirth ? 
      Math.floor((new Date().getTime() - new Date(patient.dateOfBirth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 
      null;

    // 2. EXPEDIENTE MÉDICO (OPCIONAL)
    const medicalRecordData = await db
      .select({
        id: medicalRecords.id,
        recordNumber: medicalRecords.recordNumber,
        status: medicalRecords.status,
        openDate: medicalRecords.openDate,
        primaryDoctorId: medicalRecords.primaryDoctorId,
        consultoryId: medicalRecords.consultoryId,
        demographics: medicalRecords.demographics,
        administrative: medicalRecords.administrative,
        isMinor: medicalRecords.isMinor,
        totalConsultations: medicalRecords.totalConsultations,
        // Doctor
        doctorFirstName: user.firstName,
        doctorLastName: user.lastName,
        // Consultorio
        consultoryName: consultories.name,
        consultoryAddress: consultories.address
      })
      .from(medicalRecords)
      .leftJoin(user, eq(medicalRecords.primaryDoctorId, user.id))
      .leftJoin(consultories, eq(medicalRecords.consultoryId, consultories.id))
      .where(eq(medicalRecords.patientId, patientId))
      .limit(1);

    // 3. HISTORIAL MÉDICO DETALLADO
    let medicalHistoryData = null;
    if (medicalRecordData.length > 0) {
      const historyData = await db
        .select()
        .from(patientMedicalHistory)
        .where(eq(patientMedicalHistory.medicalRecordId, medicalRecordData[0].id))
        .limit(1);
      
      medicalHistoryData = historyData.length > 0 ? historyData[0] : null;
    }

    // 4. GUARDIAN (solo si es menor de edad)
    let guardianInfo = null;
    if (patientAge && patientAge < 18) {
      const guardianUser = alias(user, 'guardian_user'); // Alias correcto para la tabla del guardián
      const guardianData = await db
        .select({
          guardianId: guardianPatientRelations.guardianId,
          relationship: guardianPatientRelations.relationship,
          isPrimary: guardianPatientRelations.isPrimary,
          canMakeDecisions: guardianPatientRelations.canMakeDecisions,
          validUntil: guardianPatientRelations.validUntil,
          // Datos del guardian (usando alias)
          firstName: guardianUser.firstName,
          lastName: guardianUser.lastName,
          email: guardianUser.email,
          phone: guardianUser.phone
        })
        .from(guardianPatientRelations)
        .innerJoin(guardianUser, eq(guardianPatientRelations.guardianId, guardianUser.id))
        .where(
          and(
            eq(guardianPatientRelations.patientId, patientId),
            eq(guardianPatientRelations.isPrimary, true)
          )
        )
        .limit(1);
      
      if (guardianData.length > 0) {
        const guardian = guardianData[0];
        
        guardianInfo = {
          id: guardian.guardianId,
          name: `${guardian.firstName} ${guardian.lastName}`,
          email: guardian.email,
          phone: guardian.phone ? formatPhoneForDisplay(guardian.phone) : null,
          relationship: guardian.relationship,
          canMakeDecisions: guardian.canMakeDecisions,
          validUntil: guardian.validUntil
        };
      }
    }

    // 5. ÚLTIMA CONSULTA
    let lastConsultationData = null;
    if (medicalRecordData.length > 0) {
      const lastConsultation = await db
        .select({
          date: medicalConsultations.consultationDate,
          chiefComplaint: medicalConsultations.chiefComplaint
        })
        .from(medicalConsultations)
        .where(eq(medicalConsultations.medicalRecordId, medicalRecordData[0].id))
        .orderBy(desc(medicalConsultations.consultationDate))
        .limit(1);
      
      lastConsultationData = lastConsultation.length > 0 ? lastConsultation[0] : null;
    }

    // 6. PRÓXIMA CITA
    const nextAppointmentData = await db
      .select({
        date: appointments.scheduledDate,
        time: appointments.startTime
      })
      .from(appointments)
      .where(
        and(
          eq(appointments.patientId, patientId),
          eq(appointments.status, 'confirmed'),
          sql`${appointments.scheduledDate} >= CURRENT_DATE`
        )
      )
      .orderBy(appointments.scheduledDate)
      .limit(1);

    // 7. CONSTRUIR RESPUESTA COMPLETA
    const response = {
      // DATOS BÁSICOS
      id: patient.id,
      firstName: patient.firstName,
      lastName: patient.lastName,
      email: patient.email,
      phone: patient.phone ? formatPhoneForDisplay(patient.phone) : null,
      alternativePhone: patient.alternativePhone ? formatPhoneForDisplay(patient.alternativePhone) : null,
      dateOfBirth: patient.dateOfBirth,
      gender: patient.gender,
      documentType: patient.documentType,
      documentNumber: patient.documentNumber,
      address: patient.address,
      
      // UBICACIÓN
      location: {
        country: patient.countryName,
        department: patient.departmentName,
        municipality: patient.municipalityName
      },
      
      // INFORMACIÓN PERSONAL
      occupation: patient.occupationName,
      emergencyContact: patient.emergencyContact ? {
        name: patient.emergencyContact,
        phone: patient.emergencyPhone ? formatPhoneForDisplay(patient.emergencyPhone) : null,
        relationship: patient.emergencyRelationshipName
      } : null,
      
      // METADATA
      status: patient.overallStatus || 'active',
      createdAt: patient.createdAt,
      updatedAt: patient.updatedAt,
      isMinor: patientAge && patientAge < 18,
      age: patientAge,
      
      // EXPEDIENTE MÉDICO (null si no existe)
      hasMedicalRecord: medicalRecordData.length > 0,
      medicalRecord: medicalRecordData.length > 0 ? {
        id: medicalRecordData[0].id,
        recordNumber: medicalRecordData[0].recordNumber,
        status: medicalRecordData[0].status,
        openDate: medicalRecordData[0].openDate,
        primaryDoctor: medicalRecordData[0].doctorFirstName ? {
          id: medicalRecordData[0].primaryDoctorId,
          name: `Dr. ${medicalRecordData[0].doctorFirstName} ${medicalRecordData[0].doctorLastName}`
        } : null,
        consultory: medicalRecordData[0].consultoryName ? {
          id: medicalRecordData[0].consultoryId,
          name: medicalRecordData[0].consultoryName,
          address: medicalRecordData[0].consultoryAddress
        } : null,
        demographics: medicalRecordData[0].demographics || {},
        administrative: medicalRecordData[0].administrative || {}
      } : null,
      
      // GUARDIAN
      guardian: guardianInfo,
      
      // HISTORIAL MÉDICO
      medicalHistory: {
        allergies: medicalHistoryData?.allergies || [],
        pathologicalHistory: medicalHistoryData?.pathologicalHistory || [],
        nonPathologicalHistory: medicalHistoryData?.nonPathologicalHistory || [],
        familyHistory: medicalHistoryData?.familyHistory || [],
        hospitalizations: medicalHistoryData?.hospitalizations || [],
        surgeries: medicalHistoryData?.surgeries || [],
        vaccinations: medicalHistoryData?.vaccinations || []
      },
      
      // ACTIVIDAD RECIENTE
      recentActivity: {
        lastConsultation: lastConsultationData,
        nextAppointment: nextAppointmentData.length > 0 ? nextAppointmentData[0] : null,
        totalConsultations: medicalRecordData.length > 0 ? (medicalRecordData[0].totalConsultations || 0) : 0
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error fetching patient:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;

    // Verificar que el usuario existe y es un paciente
    const patientExists = await db
      .select()
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient')
        )
      )
      .limit(1);

    if (!patientExists.length) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    // Eliminar el rol del paciente (eliminación lógica)
    await db
      .update(userRoles)
      .set({ 
        status: 'deleted',
        updatedAt: new Date()
      })
      .where(
        and(
          eq(userRoles.userId, patientId),
          eq(userRoles.role, 'patient')
        )
      );

    return NextResponse.json({
      message: 'Paciente eliminado correctamente'
    });

  } catch (error) {
    console.error('Error deleting patient:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;
    const updateData = await request.json();

    // Verificar que el paciente existe
    const patientExists = await db
      .select()
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient')
        )
      )
      .limit(1);

    if (!patientExists.length) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones básicas
    if (!updateData.firstName || !updateData.lastName || !updateData.email) {
      return NextResponse.json(
        { error: 'Nombre, apellido y email son requeridos' },
        { status: 400 }
      );
    }

    // Preparar datos para actualizar
    const updateFields: any = {
      firstName: updateData.firstName,
      lastName: updateData.lastName,
      email: updateData.email,
      phone: updateData.phone ? formatPhoneForStorage(updateData.phone) : null,
      dateOfBirth: updateData.dateOfBirth ? new Date(updateData.dateOfBirth) : null,
      gender: updateData.gender || null,
      documentType: updateData.documentType || null,
      documentNumber: updateData.documentNumber || null,
      address: updateData.address || null,
      updatedAt: new Date()
    };

    // Actualizar contacto de emergencia si se proporciona
    if (updateData.emergencyContact) {
      updateFields.emergencyContact = updateData.emergencyContact.name || null;
      updateFields.emergencyPhone = updateData.emergencyContact.phone ? formatPhoneForStorage(updateData.emergencyContact.phone) : null;
    }

    // Actualizar usuario
    await db
      .update(user)
      .set(updateFields)
      .where(eq(user.id, patientId));

    // Obtener datos actualizados
    const updatedPatient = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        address: user.address,
        emergencyContact: user.emergencyContact,
        emergencyPhone: user.emergencyPhone,
        updatedAt: user.updatedAt
      })
      .from(user)
      .where(eq(user.id, patientId))
      .limit(1);

    const formattedPatient = {
      ...updatedPatient[0],
      phone: updatedPatient[0].phone ? formatPhoneForDisplay(updatedPatient[0].phone) : null,
      emergencyPhone: updatedPatient[0].emergencyPhone ? formatPhoneForDisplay(updatedPatient[0].emergencyPhone) : null
    };

    return NextResponse.json(formattedPatient);

  } catch (error) {
    console.error('Error updating patient:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}