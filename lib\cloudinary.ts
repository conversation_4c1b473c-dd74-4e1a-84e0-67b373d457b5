import { v2 as cloudinary } from 'cloudinary';

// Configuración de Cloudinary
cloudinary.config({
  cloud_name: process.env.CLOUDINARY_CLOUD_NAME,
  api_key: process.env.CLOUDINARY_API_KEY,
  api_secret: process.env.CLOUDINARY_API_SECRET,
});

export default cloudinary;

// Tipos para archivos
export interface UploadResult {
  secure_url: string;
  public_id: string;
  original_filename: string;
  format: string;
  resource_type: string;
  bytes: number;
  width?: number;
  height?: number;
}

// Configuraciones predefinidas para diferentes tipos de archivos
export const uploadPresets = {
  consultorio_logo: {
    folder: 'consultorios/logos',
    transformation: [
      { width: 300, height: 300, crop: 'limit', quality: 'auto', format: 'auto' }
    ],
    allowed_formats: ['png', 'jpg', 'jpeg', 'webp'],
  },
  medical_documents: {
    folder: 'documentos/medicos',
    resource_type: 'auto', // Permite PDFs y otros documentos
    transformation: [
      { quality: 'auto', format: 'auto' }
    ],
    allowed_formats: ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'csv', 'png', 'jpg', 'jpeg', 'webp'],
  },
  patient_images: {
    folder: 'pacientes/imagenes',
    transformation: [
      { width: 1000, height: 1000, crop: 'limit', quality: 'auto', format: 'auto' }
    ],
    allowed_formats: ['png', 'jpg', 'jpeg', 'webp'],
  },
} as const;

export type UploadPresetType = keyof typeof uploadPresets;