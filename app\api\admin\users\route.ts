import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, like, and, or, desc, asc, count, sql } from 'drizzle-orm';
import { clerkClient } from '@clerk/nextjs/server';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  // Obtener rol de los session claims (igual que en el middleware)
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// GET - Listar usuarios con filtros y paginación
export async function GET(request: NextRequest) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const { searchParams } = new URL(request.url);
    
    // Parámetros de paginación
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = (page - 1) * limit;
    
    // Parámetros de filtrado
    const search = searchParams.get('search') || '';
    const role = searchParams.get('role') || '';
    const status = searchParams.get('status') || '';
    const consultoryId = searchParams.get('consultoryId') || '';
    
    // Parámetros de ordenamiento
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    // Construir query base
    let whereConditions = [];
    
    // Filtro de búsqueda global
    if (search) {
      whereConditions.push(
        or(
          like(user.firstName, `%${search}%`),
          like(user.lastName, `%${search}%`),
          like(user.email, `%${search}%`),
          like(user.documentNumber, `%${search}%`)
        )
      );
    }
    
    // Filtro por estado
    if (status && status !== 'all') {
      whereConditions.push(eq(user.overallStatus, status));
    }

    const whereClause = whereConditions.length > 0 ? and(...whereConditions) : undefined;

    // Query para obtener usuarios con sus roles
    const usersQuery = db
      .select({
        id: user.id,
        email: user.email,
        emailVerified: user.emailVerified,
        name: user.name,
        image: user.image,
        firstName: user.firstName,
        lastName: user.lastName,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        phone: user.phone,
        alternativePhone: user.alternativePhone,
        address: user.address,
        countryId: user.countryId,
        departmentId: user.departmentId,
        municipalityId: user.municipalityId,
        occupationId: user.occupationId,
        emergencyContact: user.emergencyContact,
        emergencyPhone: user.emergencyPhone,
        emergencyRelationshipId: user.emergencyRelationshipId,
        overallStatus: user.overallStatus,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt,
      })
      .from(user)
      .where(whereClause)
      .orderBy(
        sortOrder === 'desc' 
          ? desc(user[sortBy as keyof typeof user] || user.createdAt)
          : asc(user[sortBy as keyof typeof user] || user.createdAt)
      )
      .limit(limit)
      .offset(offset);

    const users = await usersQuery;

    // Obtener roles para cada usuario
    const userIds = users.map(u => u.id);
    const roles = userIds.length > 0 ? await db
      .select()
      .from(userRoles)
      .where(sql`${userRoles.userId} IN ${userIds}`) : [];

    // Agrupar roles por usuario
    const rolesMap = roles.reduce((acc, role) => {
      if (!acc[role.userId]) {
        acc[role.userId] = [];
      }
      acc[role.userId].push(role);
      return acc;
    }, {} as Record<string, typeof roles>);

    // Combinar usuarios con sus roles
    const usersWithRoles = users.map(user => ({
      ...user,
      roles: rolesMap[user.id] || []
    }));

    // Aplicar filtro por rol si es necesario
    let filteredUsers = usersWithRoles;
    if (role && role !== 'all') {
      filteredUsers = usersWithRoles.filter(user => 
        user.roles.some(r => r.role === role)
      );
    }

    // Aplicar filtro por consultorio si es necesario
    if (consultoryId && consultoryId !== 'all') {
      filteredUsers = filteredUsers.filter(user => 
        user.roles.some(r => r.consultoryId === consultoryId)
      );
    }

    // Contar total de usuarios
    const totalCountResult = await db
      .select({ count: count() })
      .from(user)
      .where(whereClause);
    
    const totalCount = totalCountResult[0]?.count || 0;
    const totalPages = Math.ceil(totalCount / limit);

    // Estadísticas rápidas
    const statsQuery = await db
      .select({
        status: user.overallStatus,
        count: count()
      })
      .from(user)
      .groupBy(user.overallStatus);

    const stats = {
      total: totalCount,
      active: statsQuery.find(s => s.status === 'active')?.count || 0,
      inactive: statsQuery.find(s => s.status === 'inactive')?.count || 0,
      pending: statsQuery.find(s => s.status === 'pending')?.count || 0,
      suspended: statsQuery.find(s => s.status === 'suspended')?.count || 0,
    };

    return NextResponse.json({
      success: true,
      data: {
        users: filteredUsers,
        pagination: {
          page,
          limit,
          total: totalCount,
          totalPages,
          from: offset + 1,
          to: Math.min(offset + limit, totalCount)
        },
        stats
      }
    });

  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo usuario
export async function POST(request: NextRequest) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const body = await request.json();
    const {
      // Datos personales
      firstName,
      lastName,
      email,
      documentType,
      documentNumber,
      dateOfBirth,
      gender,
      phone,
      alternativePhone,
      address,
      
      // Ubicación
      countryId,
      departmentId,
      municipalityId,
      occupationId,
      
      // Contacto de emergencia
      emergencyContact,
      emergencyPhone,
      emergencyRelationshipId,
      
      // Roles
      roles = [],
      
      // Configuración
      sendWelcomeEmail = true,
      requirePasswordChange = true,
    } = body;

    // Validaciones básicas
    if (!firstName || !lastName || !email) {
      return NextResponse.json(
        { success: false, error: 'Nombre, apellido y email son requeridos' },
        { status: 400 }
      );
    }

    if (roles.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Debe asignar al menos un rol al usuario' },
        { status: 400 }
      );
    }

    // Verificar si el email ya existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.email, email))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { success: false, error: 'Ya existe un usuario con este email' },
        { status: 400 }
      );
    }

    // Generar ID único para el usuario
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    try {
      // 1. Crear usuario en Clerk
      const client = await clerkClient();
      const clerkUser = await client.users.createUser({
        emailAddress: [email],
        firstName,
        lastName,
        publicMetadata: {
          onboardingCompleted: true,
          roles: roles.map((r: any) => r.role),
        },
        privateMetadata: {
          internalUserId: userId,
        },
      });

      // 2. Crear usuario en base de datos
      const newUser = await db.insert(user).values({
        id: userId,
        email,
        emailVerified: false,
        firstName,
        lastName,
        documentType,
        documentNumber,
        dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
        gender,
        phone,
        alternativePhone,
        address,
        countryId,
        departmentId,
        municipalityId,
        occupationId,
        emergencyContact,
        emergencyPhone,
        emergencyRelationshipId,
        overallStatus: 'active',
      }).returning();

      // 3. Crear roles
      const rolePromises = roles.map((roleData: any) => {
        const roleId = `role_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        return db.insert(userRoles).values({
          id: roleId,
          userId,
          role: roleData.role,
          status: 'active',
          consultoryId: roleData.consultoryId,
          specialtyId: roleData.specialtyId,
          medicalLicense: roleData.medicalLicense,
          roleData: roleData.additionalData,
        });
      });

      await Promise.all(rolePromises);

      // 4. Enviar email de bienvenida (opcional)
      if (sendWelcomeEmail) {
        // TODO: Implementar envío de email de bienvenida
        console.log(`Welcome email should be sent to ${email}`);
      }

      return NextResponse.json({
        success: true,
        data: {
          user: newUser[0],
          clerkId: clerkUser.id,
          message: 'Usuario creado exitosamente'
        }
      });

    } catch (clerkError) {
      console.error('Error creating user in Clerk:', clerkError);
      
      // Si falló la creación en Clerk, limpiar usuario de DB si se creó
      try {
        await db.delete(user).where(eq(user.id, userId));
      } catch (cleanupError) {
        console.error('Error cleaning up user:', cleanupError);
      }

      return NextResponse.json(
        { success: false, error: 'Error al crear usuario en el sistema de autenticación' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}