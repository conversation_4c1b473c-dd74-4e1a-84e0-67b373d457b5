import { db } from '../db/drizzle';
import { medicalServices, companies } from '../db/schema';
import { eq, isNull } from 'drizzle-orm';

/**
 * Script para asignar una compañía por defecto a todos los servicios médicos
 * que no tengan companyId asignado
 */
export async function assignDefaultCompanyToServices() {
  try {
    console.log('🏢 Assigning default company to medical services...');
    
    // Obtener la primera compañía activa como empresa por defecto
    const defaultCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.isActive, true))
      .limit(1);
    
    if (defaultCompany.length === 0) {
      console.log('⚠️ No active companies found. Please create a company first.');
      return;
    }
    
    const company = defaultCompany[0];
    console.log(`✅ Using default company: ${company.businessName} (NIT: ${company.nit})`);
    
    // Obtener servicios sin companyId asignado
    const servicesWithoutCompany = await db
      .select()
      .from(medicalServices)
      .where(isNull(medicalServices.companyId));
    
    console.log(`📊 Found ${servicesWithoutCompany.length} services without company assignment`);
    
    if (servicesWithoutCompany.length === 0) {
      console.log('✅ All services already have company assignments');
      return;
    }
    
    // Actualizar servicios sin companyId
    const result = await db
      .update(medicalServices)
      .set({ 
        companyId: company.id,
        updatedAt: new Date()
      })
      .where(isNull(medicalServices.companyId));
    
    console.log(`✅ Successfully assigned company "${company.businessName}" to ${servicesWithoutCompany.length} medical services`);
    
    // Verificar resultado
    const updatedServices = await db
      .select({
        id: medicalServices.id,
        name: medicalServices.name,
        companyId: medicalServices.companyId
      })
      .from(medicalServices)
      .where(eq(medicalServices.companyId, company.id));
    
    console.log(`📋 Services now assigned to ${company.businessName}:`);
    updatedServices.forEach(service => {
      console.log(`  - ${service.name}`);
    });
    
  } catch (error) {
    console.error('❌ Error assigning default company to services:', error);
    throw error;
  }
}

// Ejecutar si el archivo se ejecuta directamente
if (require.main === module) {
  assignDefaultCompanyToServices()
    .then(() => {
      console.log('✅ Company assignment completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Company assignment failed:', error);
      process.exit(1);
    });
}