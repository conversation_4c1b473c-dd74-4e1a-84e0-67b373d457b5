import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { activityTypes } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or } from 'drizzle-orm';

const categories = [
  { id: 'medical', name: '<PERSON><PERSON><PERSON><PERSON>', color: '#3B82F6' },
  { id: 'administrative', name: 'Administrativo', color: '#84CC16' },
  { id: 'personal', name: 'Personal', color: '#6B7280' }
];

// GET - Listar tipos de actividad
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const onlyActive = searchParams.get('active') === 'true';
    const requiresPatient = searchParams.get('requiresPatient');
    const orderBy = searchParams.get('orderBy') || 'name'; // 'name', 'category', 'duration', 'createdAt'
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(activityTypes);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(activityTypes.name, `%${search}%`),
          ilike(activityTypes.description, `%${search}%`),
          ilike(activityTypes.category, `%${search}%`)
        )
      );
    }

    if (category) {
      conditions.push(eq(activityTypes.category, category));
    }

    if (onlyActive) {
      conditions.push(eq(activityTypes.isActive, true));
    }

    if (requiresPatient !== null) {
      conditions.push(eq(activityTypes.requiresPatient, requiresPatient === 'true'));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'category' ? activityTypes.category : 
                       orderBy === 'duration' ? activityTypes.duration :
                       orderBy === 'createdAt' ? activityTypes.createdAt : 
                       activityTypes.name;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const activityTypesData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(activityTypes);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Obtener estadísticas por categoría
    const activeTypes = await db.select().from(activityTypes).where(eq(activityTypes.isActive, true));

    return NextResponse.json({
      data: activityTypesData,
      categories,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: activeTypes.length,
        byCategory: categories.map(cat => ({
          category: cat.id,
          name: cat.name,
          count: activeTypes.filter(t => t.category === cat.id).length
        }))
      }
    });

  } catch (error) {
    console.error('Error obteniendo tipos de actividad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo tipo de actividad
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    // Solo admin y asistentes pueden crear tipos de actividad
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Sin permisos para crear tipos de actividad' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      category, 
      color, 
      duration, 
      requiresPatient, 
      allowsRecurrence, 
      icon, 
      description, 
      isActive 
    } = body;

    // Validaciones
    if (!name || !category || !duration) {
      return NextResponse.json(
        { error: 'Nombre, categoría y duración son requeridos' },
        { status: 400 }
      );
    }

    if (!categories.find(c => c.id === category)) {
      return NextResponse.json(
        { error: 'Categoría inválida' },
        { status: 400 }
      );
    }

    if (duration < 5 || duration > 480) {
      return NextResponse.json(
        { error: 'La duración debe estar entre 5 y 480 minutos' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un tipo con el mismo ID
    const existingType = await db.select().from(activityTypes).where(eq(activityTypes.id, id)).limit(1);
    if (existingType.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un tipo de actividad con este nombre' },
        { status: 400 }
      );
    }

    const newActivityType = {
      id,
      name: name.trim(),
      category,
      color: color || categories.find(c => c.id === category)?.color || '#6B7280',
      duration: parseInt(duration),
      requiresPatient: Boolean(requiresPatient),
      allowsRecurrence: Boolean(allowsRecurrence),
      icon: icon || 'calendar',
      description: description?.trim() || '',
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedActivityType] = await db.insert(activityTypes).values(newActivityType).returning();

    return NextResponse.json({
      message: 'Tipo de actividad creado exitosamente',
      data: insertedActivityType
    });

  } catch (error) {
    console.error('Error creando tipo de actividad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar tipo de actividad
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    // Solo admin y asistentes pueden actualizar tipos de actividad
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Sin permisos para actualizar tipos de actividad' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, category, color, duration, requiresPatient, allowsRecurrence, icon, description, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de tipo de actividad requerido' },
        { status: 400 }
      );
    }

    // Buscar tipo existente
    const existingType = await db.select().from(activityTypes).where(eq(activityTypes.id, id)).limit(1);
    if (existingType.length === 0) {
      return NextResponse.json(
        { error: 'Tipo de actividad no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones
    if (category && !categories.find(c => c.id === category)) {
      return NextResponse.json(
        { error: 'Categoría inválida' },
        { status: 400 }
      );
    }

    if (duration && (duration < 5 || duration > 480)) {
      return NextResponse.json(
        { error: 'La duración debe estar entre 5 y 480 minutos' },
        { status: 400 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(category && { category }),
      ...(color && { color }),
      ...(duration && { duration: parseInt(duration) }),
      ...(requiresPatient !== undefined && { requiresPatient: Boolean(requiresPatient) }),
      ...(allowsRecurrence !== undefined && { allowsRecurrence: Boolean(allowsRecurrence) }),
      ...(icon && { icon }),
      ...(description !== undefined && { description: description.trim() }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedActivityType] = await db.update(activityTypes)
      .set(updateData)
      .where(eq(activityTypes.id, id))
      .returning();

    return NextResponse.json({
      message: 'Tipo de actividad actualizado exitosamente',
      data: updatedActivityType
    });

  } catch (error) {
    console.error('Error actualizando tipo de actividad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}