'use client';

import { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox'
import { DateInput } from '@/components/ui/date-input';;
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Heart,
  Shield,
  Save
} from 'lucide-react';
import { toast } from 'sonner';

interface UserFormData {
  // Datos personales
  firstName: string;
  lastName: string;
  email: string;
  documentType: string;
  documentNumber: string;
  dateOfBirth: string;
  gender: string;
  phone: string;
  alternativePhone: string;
  address: string;
  
  // Ubicación
  countryId: string;
  departmentId: string;
  municipalityId: string;
  occupationId: string;
  
  // Contacto de emergencia
  emergencyContact: string;
  emergencyPhone: string;
  emergencyRelationshipId: string;
  
  // Roles
  roles: string[];
  
  // Configuración
  sendWelcomeEmail: boolean;
  requirePasswordChange: boolean;
}

const roleOptions = [
  { value: 'admin', label: 'Administrador', description: 'Acceso completo al sistema' },
  { value: 'doctor', label: 'Doctor', description: 'Profesional médico' },
  { value: 'assistant', label: 'Asistente', description: 'Apoyo médico y administrativo' },
  { value: 'patient', label: 'Paciente', description: 'Persona que recibe atención' },
  { value: 'guardian', label: 'Guardian', description: 'Responsable de otros pacientes' },
  { value: 'provider', label: 'Proveedor', description: 'Proveedor de servicios médicos' },
];

export default function CreateUserPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  
  const [loading, setLoading] = useState(false);
  const [returnUrl, setReturnUrl] = useState<string | null>(null);

  useEffect(() => {
    const returnUrlParam = searchParams.get('returnUrl');
    setReturnUrl(returnUrlParam);
  }, [searchParams]);
  const [formData, setFormData] = useState<UserFormData>({
    // Datos personales
    firstName: '',
    lastName: '',
    email: '',
    documentType: '',
    documentNumber: '',
    dateOfBirth: '',
    gender: '',
    phone: '',
    alternativePhone: '',
    address: '',
    
    // Ubicación
    countryId: '',
    departmentId: '',
    municipalityId: '',
    occupationId: '',
    
    // Contacto de emergencia
    emergencyContact: '',
    emergencyPhone: '',
    emergencyRelationshipId: '',
    
    // Roles
    roles: [],
    
    // Configuración
    sendWelcomeEmail: true,
    requirePasswordChange: true,
  });

  const handleInputChange = (field: keyof UserFormData, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleRoleChange = (roleValue: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      roles: checked 
        ? [...prev.roles, roleValue]
        : prev.roles.filter(r => r !== roleValue)
    }));
  };

  const validateForm = (): string[] => {
    const errors: string[] = [];
    
    if (!formData.firstName.trim()) errors.push('El nombre es requerido');
    if (!formData.lastName.trim()) errors.push('El apellido es requerido');
    if (!formData.email.trim()) errors.push('El email es requerido');
    if (!formData.documentType) errors.push('El tipo de documento es requerido');
    if (!formData.documentNumber.trim()) errors.push('El número de documento es requerido');
    if (formData.roles.length === 0) errors.push('Debe seleccionar al menos un rol');
    
    // Validar email
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      errors.push('El email no tiene un formato válido');
    }
    
    return errors;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const errors = validateForm();
    if (errors.length > 0) {
      toast.error(errors.join(', '));
      return;
    }

    setLoading(true);

    try {
      // Preparar datos para envío
      const submitData = {
        ...formData,
        roles: formData.roles.map(role => ({
          role,
          status: 'active'
        })),
        dateOfBirth: formData.dateOfBirth || null,
        countryId: formData.countryId || null,
        departmentId: formData.departmentId || null,
        municipalityId: formData.municipalityId || null,
        occupationId: formData.occupationId || null,
        emergencyRelationshipId: formData.emergencyRelationshipId || null,
      };

      const response = await fetch('/api/admin/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(submitData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success(result.data.message);
        
        // Si hay un returnUrl, redirigir allí; sino, ir a la lista de usuarios
        if (returnUrl) {
          router.push(returnUrl);
        } else {
          router.push('/dashboard/admin/users');
        }
      } else {
        throw new Error(result.error || 'Error al crear usuario');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al crear usuario');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button 
          variant="ghost" 
          onClick={() => {
            if (returnUrl) {
              router.push(returnUrl);
            } else {
              router.back();
            }
          }}
          className="hover:bg-gray-100 transition-colors"
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Crear Usuario</h1>
          <p className="text-gray-600">Completa la información para crear un nuevo usuario</p>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Datos Personales */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              Datos Personales
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="firstName">Nombre *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Nombre"
                  required
                  autoFocus
                />
              </div>
              
              <div>
                <Label htmlFor="lastName">Apellido *</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Apellido"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
              <div className="md:col-span-2">
                <Label htmlFor="documentType">Tipo de Documento *</Label>
                <Select value={formData.documentType} onValueChange={(value) => handleInputChange('documentType', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona tipo" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="dpi">DPI</SelectItem>
                    <SelectItem value="passport">Pasaporte</SelectItem>
                    <SelectItem value="license">Licencia</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="md:col-span-3">
                <Label htmlFor="documentNumber">Número de Documento *</Label>
                <Input
                  id="documentNumber"
                  value={formData.documentNumber}
                  onChange={(e) => handleInputChange('documentNumber', e.target.value)}
                  placeholder="Número de documento"
                  required
                />
              </div>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dateOfBirth">Fecha de Nacimiento</Label>
                <DateInput
                  id="dateOfBirth"
                  value={formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined}
                  onChange={(date) => handleInputChange('dateOfBirth', date ? date.toISOString().split('T')[0] : '')}
                  maxDate={new Date()}
                />
              </div>
              
              <div>
                <Label htmlFor="gender">Género</Label>
                <Select value={formData.gender} onValueChange={(value) => handleInputChange('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona género" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Masculino</SelectItem>
                    <SelectItem value="female">Femenino</SelectItem>
                    <SelectItem value="other">Otro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Información de Contacto */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-emerald-600" />
              Información de Contacto
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="email">Email *</Label>
              <Input
                id="email"
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="phone">Teléfono</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="Número de teléfono"
                />
              </div>
              
              <div>
                <Label htmlFor="alternativePhone">Teléfono Alternativo</Label>
                <Input
                  id="alternativePhone"
                  value={formData.alternativePhone}
                  onChange={(e) => handleInputChange('alternativePhone', e.target.value)}
                  placeholder="Número alternativo"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="address">Dirección</Label>
              <Textarea
                id="address"
                value={formData.address}
                onChange={(e) => handleInputChange('address', e.target.value)}
                placeholder="Dirección completa"
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* Contacto de Emergencia */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-600" />
              Contacto de Emergencia
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="emergencyContact">Nombre del Contacto</Label>
                <Input
                  id="emergencyContact"
                  value={formData.emergencyContact}
                  onChange={(e) => handleInputChange('emergencyContact', e.target.value)}
                  placeholder="Nombre completo"
                />
              </div>
              
              <div>
                <Label htmlFor="emergencyPhone">Teléfono de Emergencia</Label>
                <Input
                  id="emergencyPhone"
                  value={formData.emergencyPhone}
                  onChange={(e) => handleInputChange('emergencyPhone', e.target.value)}
                  placeholder="Número de teléfono"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Roles del Usuario */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-600" />
              Roles del Usuario *
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {roleOptions.map((role) => (
                <div key={role.value} className="flex items-start space-x-3 p-3 rounded-lg border border-gray-200 hover:border-gray-300 transition-colors">
                  <Checkbox
                    id={role.value}
                    checked={formData.roles.includes(role.value)}
                    onCheckedChange={(checked) => handleRoleChange(role.value, checked as boolean)}
                  />
                  <div className="grid gap-1.5 leading-none">
                    <Label htmlFor={role.value} className="text-sm font-medium cursor-pointer">
                      {role.label}
                    </Label>
                    <p className="text-xs text-gray-500">
                      {role.description}
                    </p>
                  </div>
                </div>
              ))}
            </div>
            
            {formData.roles.length === 0 && (
              <p className="text-sm text-red-600 mt-2">
                Debe seleccionar al menos un rol para el usuario
              </p>
            )}
          </CardContent>
        </Card>

        {/* Configuración */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle>Configuración</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="sendWelcomeEmail"
                checked={formData.sendWelcomeEmail}
                onCheckedChange={(checked) => handleInputChange('sendWelcomeEmail', checked as boolean)}
              />
              <Label htmlFor="sendWelcomeEmail" className="text-sm font-medium">
                Enviar email de bienvenida
              </Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Checkbox
                id="requirePasswordChange"
                checked={formData.requirePasswordChange}
                onCheckedChange={(checked) => handleInputChange('requirePasswordChange', checked as boolean)}
              />
              <Label htmlFor="requirePasswordChange" className="text-sm font-medium">
                Requerir cambio de contraseña en primer acceso
              </Label>
            </div>
          </CardContent>
        </Card>

        {/* Botones de Acción */}
        <div className="flex gap-4 justify-end">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => {
              if (returnUrl) {
                router.push(returnUrl);
              } else {
                router.back();
              }
            }}
            disabled={loading}
            className="hover:bg-gray-50"
          >
            Cancelar
          </Button>
          <Button 
            type="submit" 
            disabled={loading || formData.roles.length === 0}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Creando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Crear Usuario
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}