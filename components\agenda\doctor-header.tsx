'use client';

import { useState } from 'react';

import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ChevronDown, Stethoscope, Calendar, Clock, CheckCircle } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DoctorHeaderProps {
  doctorInfo?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    clerkImageUrl?: string;
    specialty?: string;
  };
  availableDoctors?: Array<{
    id: string;
    firstName: string;
    lastName: string;
    imageUrl?: string;
    specialty?: string;
  }>;
  onDoctorChange?: (doctorId: string) => void;
  stats?: {
    todayCount?: number;
    weekCount?: number;
    confirmedCount?: number;
  };
  isSelectable?: boolean; // Solo true para asistentes/admin
  className?: string;
}

export function DoctorHeader({
  doctorInfo,
  availableDoctors = [],
  onDoctorChange,
  stats,
  isSelectable = false,
  className
}: DoctorHeaderProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Para agenda del asistente: solo iniciales por seguridad


  if (!doctorInfo && !isSelectable) {
    return null;
  }

  const displayName = doctorInfo 
    ? `Dr. ${doctorInfo.firstName} ${doctorInfo.lastName}`
    : 'Seleccionar médico';

  const initials = doctorInfo
    ? `${doctorInfo.firstName.charAt(0)}${doctorInfo.lastName.charAt(0)}`
    : '?';

  // Si hay médicos disponibles y es seleccionable, mostrar avatares múltiples (removemos límite de 3)
  const showMultipleAvatars = isSelectable && availableDoctors.length > 0;
  
  

  return (
    <div className={cn(
      "bg-white border rounded-lg shadow-sm p-4",
      className
    )}>
      <div className="flex items-center justify-between">
        {/* Información del médico */}
        <div className="flex items-center gap-4">
          {showMultipleAvatars ? (
            // Mostrar múltiples avatares para 2-3 médicos
            <div className="flex items-center gap-6">
              {/* Opción "Todos los médicos" */}
              <div 
                className="flex flex-col items-center cursor-pointer group"
                onClick={() => onDoctorChange?.('all')}
              >
                <div className={cn(
                  "h-12 w-12 rounded-full flex items-center justify-center transition-all",
                  "ring-2 ring-offset-2",
                  !doctorInfo ? "ring-blue-500 bg-blue-100" : "ring-transparent hover:ring-blue-300 bg-gray-100"
                )}>
                  <Calendar className={cn(
                    "h-6 w-6",
                    !doctorInfo ? "text-blue-600" : "text-gray-600"
                  )} />
                </div>
                <span className={cn(
                  "text-xs mt-1 font-medium",
                  !doctorInfo ? "text-blue-600" : "text-gray-600 group-hover:text-blue-600"
                )}>
                  Todos
                </span>
              </div>

              {/* Avatares de médicos */}
              {availableDoctors.map((doctor) => {
                const isSelected = doctor.id === doctorInfo?.id;
                const doctorInitials = `${doctor.firstName.charAt(0)}${doctor.lastName.charAt(0)}`;
                
                // Usar la imagen de doctorInfo si está seleccionado, sino usar la del doctor
                const imageUrl = isSelected && doctorInfo 
                  ? (doctorInfo.clerkImageUrl || doctorInfo.imageUrl)
                  : doctor.imageUrl;
                
                // Avatar elegante seleccionado
                
                return (
                  <div 
                    key={doctor.id}
                    className="flex flex-col items-center cursor-pointer group"
                    onClick={() => onDoctorChange?.(doctor.id)}
                  >
                    <div className={cn(
                      "h-12 w-12 transition-all cursor-pointer relative",
                      "ring-2 ring-offset-2 rounded-full overflow-hidden",
                      isSelected ? "ring-blue-500" : "ring-transparent hover:ring-blue-300"
                    )}>
                      {/* Mostrar avatar elegante con iniciales */}
                      <div 
                        className={cn(
                          "absolute inset-0 flex items-center justify-center font-bold text-white z-0 text-sm",
                          isSelected 
                            ? "bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 shadow-lg" 
                            : "bg-gradient-to-br from-slate-500 via-slate-600 to-slate-700 group-hover:from-blue-400 group-hover:via-blue-500 group-hover:to-blue-600 group-hover:shadow-md"
                        )}
                      >
                        {doctorInitials}
                      </div>
                    </div>
                    <span className={cn(
                      "text-xs mt-1 font-medium text-center",
                      isSelected ? "text-blue-600" : "text-gray-600 group-hover:text-blue-600"
                    )}>
                      Dr. {doctor.firstName}
                    </span>
                  </div>
                );
              })}
            </div>
          ) : isSelectable && availableDoctors.length > 6 ? (
            // Dropdown para más de 3 médicos
            <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
              <DropdownMenuTrigger asChild>
                <Button 
                  variant="ghost" 
                  className="p-0 h-auto hover:bg-transparent focus:ring-2 focus:ring-blue-500 rounded-lg"
                >
                  <div className="flex items-center gap-3">
                    <div className="h-12 w-12 cursor-pointer ring-2 ring-transparent hover:ring-blue-300 transition-all rounded-full overflow-hidden relative">
                      {/* Avatar elegante con iniciales */}
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white font-bold flex items-center justify-center z-0 text-sm shadow-lg">
                        {initials}
                      </div>
                    </div>
                    <div className="text-left">
                      <div className="flex items-center gap-2">
                        <h3 className="font-semibold text-gray-900">
                          {displayName}
                        </h3>
                        <ChevronDown className={cn(
                          "h-4 w-4 text-gray-500 transition-transform",
                          isOpen && "rotate-180"
                        )} />
                      </div>
                      {doctorInfo?.specialty && (
                        <p className="text-sm text-blue-600 font-medium flex items-center gap-1">
                          <Stethoscope className="h-3 w-3" />
                          {doctorInfo.specialty}
                        </p>
                      )}
                    </div>
                  </div>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start" className="w-64">
                <DropdownMenuLabel>Cambiar médico</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => onDoctorChange?.('all')}
                  className="cursor-pointer"
                >
                  <div className="flex items-center gap-3">
                    <div className="h-8 w-8 rounded-full bg-gray-200 flex items-center justify-center">
                      <Calendar className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium">Todos los médicos</div>
                      <div className="text-xs text-gray-500">Ver agenda completa</div>
                    </div>
                  </div>
                </DropdownMenuItem>
                {availableDoctors.map((doctor) => (
                  <DropdownMenuItem
                    key={doctor.id}
                    onClick={() => onDoctorChange?.(doctor.id)}
                    className={cn(
                      "cursor-pointer",
                      doctor.id === doctorInfo?.id && "bg-blue-50"
                    )}
                  >
                    <div className="flex items-center gap-3">
                      <div className="h-8 w-8 rounded-full overflow-hidden relative">
                        {/* Avatar elegante con iniciales */}
                        <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-blue-500 to-blue-600 text-white text-xs font-bold flex items-center justify-center z-0 shadow-md">
                          {doctor.firstName.charAt(0)}{doctor.lastName.charAt(0)}
                        </div>
                      </div>
                      <div>
                        <div className="font-medium">
                          Dr. {doctor.firstName} {doctor.lastName}
                        </div>
                        {doctor.specialty && (
                          <div className="text-xs text-gray-500">{doctor.specialty}</div>
                        )}
                      </div>
                    </div>
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          ) : (
            // Vista estática para doctores
            <div className="flex items-center gap-3">
              <div className="h-12 w-12 rounded-full overflow-hidden relative">
                {/* Avatar elegante con iniciales */}
                <div className="absolute inset-0 bg-gradient-to-br from-blue-500 via-blue-600 to-blue-700 text-white font-bold flex items-center justify-center z-0 text-sm shadow-lg">
                  {initials}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">
                  {displayName}
                </h3>
                {doctorInfo?.specialty && (
                  <p className="text-sm text-blue-600 font-medium flex items-center gap-1">
                    <Stethoscope className="h-3 w-3" />
                    {doctorInfo.specialty}
                  </p>
                )}
              </div>
            </div>
          )}
          
          {/* Mostrar información del médico seleccionado cuando hay múltiples avatares */}
          {showMultipleAvatars && doctorInfo && (
            <div className="border-l pl-6 ml-2">
              <h3 className="font-semibold text-gray-900">
                {displayName}
              </h3>
              {doctorInfo.specialty && (
                <p className="text-sm text-blue-600 font-medium flex items-center gap-1">
                  <Stethoscope className="h-3 w-3" />
                  {doctorInfo.specialty}
                </p>
              )}
            </div>
          )}
        </div>

      </div>
    </div>
  );
}