-- Agregar campo para almacenar toda la información del pre-checkin
ALTER TABLE appointments 
ADD COLUMN IF NOT EXISTS "preCheckinData" JSONB;

-- Agregar índice para consultas eficientes
CREATE INDEX IF NOT EXISTS appointments_precheckin_data_idx ON appointments USING GIN("preCheckinData");

-- Comentario para documentar el campo
COMMENT ON COLUMN appointments."preCheckinData" IS 'Datos completos del formulario de pre-checkin en formato JSON';