#!/usr/bin/env tsx

import { db } from '../db/drizzle';
import { eq, isNotNull } from 'drizzle-orm';
import { appointments } from '../db/schema';

async function debugPreCheckinData() {
  try {
    console.log('🔍 Buscando citas con datos de pre-checkin...\n');
    
    // Buscar todas las citas que tengan datos de pre-checkin
    const appointmentsWithPreCheckin = await db.select().from(appointments)
      .where(isNotNull(appointments.preCheckinData))
      .limit(10);
    
    console.log(`Encontradas ${appointmentsWithPreCheckin.length} citas con pre-checkin\n`);
    
    for (const appointment of appointmentsWithPreCheckin) {
      console.log(`=== CITA ID: ${appointment.id} ===`);
      console.log(`Paciente: ${appointment.patientId}`);
      console.log(`Doctor: ${appointment.doctorId}`);
      console.log(`Fecha: ${appointment.scheduledDate}`);
      console.log(`Pre-checkin completado: ${appointment.preCheckinCompleted}`);
      console.log(`Pre-checkin data: ${appointment.preCheckinData}`);
      
      // Parsear el JSON si existe
      if (appointment.preCheckinData) {
        try {
          const parsedData = JSON.parse(appointment.preCheckinData as string);
          console.log('📋 Datos del pre-checkin:');
          console.log('  - Síntomas:', parsedData.symptoms || 'No hay');
          console.log('  - Alergias:', parsedData.allergies || 'No hay');
          console.log('  - Medicamentos:', parsedData.medications || 'No hay');
          console.log('  - Documentos:', parsedData.documents?.length || 0, 'archivos');
          
          if (parsedData.documents && parsedData.documents.length > 0) {
            console.log('📄 Lista de documentos:');
            parsedData.documents.forEach((doc: any, index: number) => {
              console.log(`    ${index + 1}. ${doc.filename} (${doc.type})`);
              console.log(`       URL: ${doc.url}`);
              console.log(`       Descripción: ${doc.description || 'Sin descripción'}`);
            });
          }
          
        } catch (error) {
          console.log('❌ Error al parsear JSON:', error);
        }
      }
      
      console.log('\n-------------------\n');
    }
    
    // También buscar por el campo nuevo si existe
    console.log('🔍 Buscando citas con preCheckinData como texto...\n');
    
    const allAppointments = await db.select().from(appointments)
      .limit(5);
      
    console.log('Estructura de las citas:');
    allAppointments.forEach((apt, index) => {
      console.log(`Cita ${index + 1}:`, {
        id: apt.id,
        preCheckinCompleted: apt.preCheckinCompleted,
        preCheckinData: typeof apt.preCheckinData,
        hasPreCheckinData: !!apt.preCheckinData
      });
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

debugPreCheckinData().then(() => {
  console.log('✅ Debug completado');
  process.exit(0);
}).catch((error) => {
  console.error('❌ Error en debug:', error);
  process.exit(1);
});