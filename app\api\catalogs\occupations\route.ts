import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { occupations } from '@/db/schema';
import { and, eq, ilike, or, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');
    const category = url.searchParams.get('category');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(occupations.name, `%${search}%`),
          ilike(occupations.category, `%${search}%`)
        )
      );
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(occupations.isActive, active === 'true'));
    }

    if (category) {
      conditions.push(ilike(occupations.category, `%${category}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(occupations)
      .where(whereClause);

    // Get paginated data
    const occupationsData = await db
      .select({
        id: occupations.id,
        name: occupations.name,
        category: occupations.category,
        isActive: occupations.isActive,
        createdAt: occupations.createdAt,
        updatedAt: occupations.updatedAt
      })
      .from(occupations)
      .where(whereClause)
      .orderBy(occupations.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: occupationsData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching occupations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch occupations' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, category, isActive = true } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(occupations);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newOccupation = await db.insert(occupations).values({
      id: nextId,
      name,
      category: category || null,
      isActive
    }).returning();

    return NextResponse.json({ 
      success: true, 
      data: newOccupation[0],
      message: 'Ocupación creada exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating occupation:', error);
    return NextResponse.json(
      { error: 'Failed to create occupation' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, category, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedOccupation = await db
      .update(occupations)
      .set({
        name,
        category,
        isActive,
        updatedAt: new Date()
      })
      .where(eq(occupations.id, parseInt(id)))
      .returning();

    if (updatedOccupation.length === 0) {
      return NextResponse.json(
        { error: 'Occupation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedOccupation[0]);
  } catch (error) {
    console.error('Error updating occupation:', error);
    return NextResponse.json(
      { error: 'Failed to update occupation' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedOccupation = await db
      .update(occupations)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(occupations.id, parseInt(id)))
      .returning();

    if (deletedOccupation.length === 0) {
      return NextResponse.json(
        { error: 'Occupation not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Occupation deleted successfully' });
  } catch (error) {
    console.error('Error deleting occupation:', error);
    return NextResponse.json(
      { error: 'Failed to delete occupation' },
      { status: 500 }
    );
  }
} 