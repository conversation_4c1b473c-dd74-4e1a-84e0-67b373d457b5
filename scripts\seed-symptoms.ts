import { nanoid } from 'nanoid';
import { db } from '../db/drizzle';
import { symptoms } from '../db/schema';

const SYMPTOMS_DATA = [
  // Síntomas del Sistema Cardiovascular
  {
    id: nanoid(),
    name: '<PERSON><PERSON>',
    category: 'Cardiovascular',
    subcategory: 'Dolor',
    icdCode: 'R06.02',
    isSymptom: true,
    description: 'Dolor o molestia en el pecho que puede irradiar a brazos, cuello o mandíbula',
    commonCauses: ['Infarto agudo del miocardio', 'Angina de pecho', 'Pericarditis', 'Reflujo gastroesofágico'],
    severity: 'high',
    bodySystem: 'Cardiovascular',
    order: 1,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Palpitaciones',
    category: 'Cardiovascular',
    subcategory: 'Ritmo cardíaco',
    icdCode: 'R00.2',
    isSymptom: true,
    description: 'Sensación consciente de los latidos del corazón',
    commonCauses: ['Arritmias', 'Ansiedad', 'Hipertiroidismo', 'Cafeína', 'Medicamentos'],
    severity: 'medium',
    bodySystem: 'Cardiovascular',
    order: 2,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Edema',
    category: 'Cardiovascular',
    icdCode: 'R60.9',
    isSymptom: false, // Es un signo
    description: 'Acumulación anormal de líquido en tejidos',
    commonCauses: ['Insuficiencia cardíaca', 'Insuficiencia renal', 'Insuficiencia venosa', 'Hipoalbuminemia'],
    severity: 'medium',
    bodySystem: 'Cardiovascular',
    order: 3,
    isActive: true,
  },

  // Síntomas del Sistema Respiratorio
  {
    id: nanoid(),
    name: 'Disnea',
    category: 'Respiratorio',
    subcategory: 'Dificultad respiratoria',
    icdCode: 'R06.00',
    isSymptom: true,
    description: 'Sensación subjetiva de dificultad para respirar',
    commonCauses: ['Asma', 'EPOC', 'Insuficiencia cardíaca', 'Anemia', 'Ansiedad'],
    severity: 'high',
    bodySystem: 'Respiratorio',
    order: 4,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Tos',
    category: 'Respiratorio',
    subcategory: 'Reflejo respiratorio',
    icdCode: 'R05',
    isSymptom: true,
    description: 'Expulsión súbita y audible del aire de los pulmones',
    commonCauses: ['Infección respiratoria', 'Asma', 'EPOC', 'Reflujo gastroesofágico', 'Medicamentos'],
    severity: 'low',
    bodySystem: 'Respiratorio',
    order: 5,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Hemoptisis',
    category: 'Respiratorio',
    subcategory: 'Sangrado',
    icdCode: 'R04.2',
    isSymptom: true,
    description: 'Expectoración de sangre procedente del árbol respiratorio',
    commonCauses: ['Tuberculosis', 'Cáncer de pulmón', 'Bronquiectasias', 'Embolia pulmonar'],
    severity: 'high',
    bodySystem: 'Respiratorio',
    order: 6,
    isActive: true,
  },

  // Síntomas del Sistema Gastrointestinal
  {
    id: nanoid(),
    name: 'Dolor abdominal',
    category: 'Gastrointestinal',
    subcategory: 'Dolor',
    icdCode: 'R10.9',
    isSymptom: true,
    description: 'Dolor localizado en el abdomen',
    commonCauses: ['Gastritis', 'Apendicitis', 'Cólico biliar', 'Obstrucción intestinal', 'Gastroenteritis'],
    severity: 'medium',
    bodySystem: 'Gastrointestinal',
    order: 7,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Náuseas',
    category: 'Gastrointestinal',
    subcategory: 'Malestar digestivo',
    icdCode: 'R11.0',
    isSymptom: true,
    description: 'Sensación de malestar gástrico con impulso de vomitar',
    commonCauses: ['Gastroenteritis', 'Embarazo', 'Medicamentos', 'Migraña', 'Cinetosis'],
    severity: 'low',
    bodySystem: 'Gastrointestinal',
    order: 8,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Diarrea',
    category: 'Gastrointestinal',
    subcategory: 'Alteración intestinal',
    icdCode: 'K59.1',
    isSymptom: true,
    description: 'Evacuaciones líquidas frecuentes',
    commonCauses: ['Gastroenteritis infecciosa', 'Síndrome intestino irritable', 'Medicamentos', 'Intolerancias alimentarias'],
    severity: 'medium',
    bodySystem: 'Gastrointestinal',
    order: 9,
    isActive: true,
  },

  // Síntomas del Sistema Neurológico
  {
    id: nanoid(),
    name: 'Cefalea',
    category: 'Neurológico',
    subcategory: 'Dolor',
    icdCode: 'R51',
    isSymptom: true,
    description: 'Dolor de cabeza de cualquier intensidad',
    commonCauses: ['Tensión', 'Migraña', 'Hipertensión arterial', 'Sinusitis', 'Meningitis'],
    severity: 'medium',
    bodySystem: 'Neurológico',
    order: 10,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Mareos',
    category: 'Neurológico',
    subcategory: 'Equilibrio',
    icdCode: 'R42',
    isSymptom: true,
    description: 'Sensación de inestabilidad o desequilibrio',
    commonCauses: ['Hipotensión ortostática', 'Vértigo', 'Anemia', 'Medicamentos', 'Deshidratación'],
    severity: 'medium',
    bodySystem: 'Neurológico',
    order: 11,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Convulsiones',
    category: 'Neurológico',
    subcategory: 'Crisis neurológica',
    icdCode: 'R56.9',
    isSymptom: false, // Es un signo
    description: 'Actividad anormal del cerebro que causa movimientos o comportamientos anormales',
    commonCauses: ['Epilepsia', 'Fiebre', 'Traumatismo craneal', 'Tóxicos', 'Trastornos metabólicos'],
    severity: 'high',
    bodySystem: 'Neurológico',
    order: 12,
    isActive: true,
  },

  // Síntomas del Sistema Musculoesquelético
  {
    id: nanoid(),
    name: 'Dolor articular',
    category: 'Musculoesquelético',
    subcategory: 'Dolor articular',
    icdCode: 'M25.50',
    isSymptom: true,
    description: 'Dolor en una o más articulaciones',
    commonCauses: ['Artritis', 'Artrosis', 'Gota', 'Lupus', 'Infección articular'],
    severity: 'medium',
    bodySystem: 'Musculoesquelético',
    order: 13,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Dolor muscular',
    category: 'Musculoesquelético',
    subcategory: 'Dolor muscular',
    icdCode: 'M79.1',
    isSymptom: true,
    description: 'Dolor en uno o más músculos',
    commonCauses: ['Sobreesfuerzo', 'Fibromialgia', 'Infección viral', 'Medicamentos', 'Deshidratación'],
    severity: 'low',
    bodySystem: 'Musculoesquelético',
    order: 14,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Rigidez articular',
    category: 'Musculoesquelético',
    subcategory: 'Movilidad',
    icdCode: 'M25.60',
    isSymptom: true,
    description: 'Dificultad para mover las articulaciones',
    commonCauses: ['Artritis reumatoide', 'Artrosis', 'Espondilitis anquilosante', 'Inmovilización'],
    severity: 'medium',
    bodySystem: 'Musculoesquelético',
    order: 15,
    isActive: true,
  },

  // Síntomas del Sistema Genitourinario
  {
    id: nanoid(),
    name: 'Disuria',
    category: 'Genitourinario',
    subcategory: 'Micción',
    icdCode: 'R30.0',
    isSymptom: true,
    description: 'Dolor o molestia al orinar',
    commonCauses: ['Infección urinaria', 'Cistitis', 'Uretritis', 'Cálculos renales', 'Prostatitis'],
    severity: 'medium',
    bodySystem: 'Genitourinario',
    order: 16,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Poliuria',
    category: 'Genitourinario',
    subcategory: 'Micción',
    icdCode: 'R35.8',
    isSymptom: true,
    description: 'Producción excesiva de orina',
    commonCauses: ['Diabetes mellitus', 'Diabetes insípida', 'Diuréticos', 'Hipercalcemia', 'Polidipsia psicógena'],
    severity: 'medium',
    bodySystem: 'Genitourinario',
    order: 17,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Hematuria',
    category: 'Genitourinario',
    subcategory: 'Sangrado',
    icdCode: 'R31.9',
    isSymptom: false, // Es un signo
    description: 'Presencia de sangre en la orina',
    commonCauses: ['Infección urinaria', 'Cálculos renales', 'Cáncer urológico', 'Glomerulonefritis', 'Traumatismo'],
    severity: 'high',
    bodySystem: 'Genitourinario',
    order: 18,
    isActive: true,
  },

  // Síntomas Generales
  {
    id: nanoid(),
    name: 'Fiebre',
    category: 'General',
    subcategory: 'Temperatura',
    icdCode: 'R50.9',
    isSymptom: false, // Es un signo
    description: 'Elevación de la temperatura corporal por encima de lo normal',
    commonCauses: ['Infecciones bacterianas', 'Infecciones virales', 'Reacciones medicamentosas', 'Neoplasias', 'Enfermedades autoinmunes'],
    severity: 'medium',
    bodySystem: 'General',
    order: 19,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Fatiga',
    category: 'General',
    subcategory: 'Energía',
    icdCode: 'R53',
    isSymptom: true,
    description: 'Sensación de cansancio o falta de energía',
    commonCauses: ['Anemia', 'Hipotiroidismo', 'Depresión', 'Síndrome fatiga crónica', 'Medicamentos'],
    severity: 'low',
    bodySystem: 'General',
    order: 20,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Pérdida de peso',
    category: 'General',
    subcategory: 'Peso corporal',
    icdCode: 'R63.4',
    isSymptom: true,
    description: 'Disminución involuntaria del peso corporal',
    commonCauses: ['Cáncer', 'Hipertiroidismo', 'Diabetes mellitus', 'Enfermedad inflamatoria intestinal', 'Depresión'],
    severity: 'high',
    bodySystem: 'General',
    order: 21,
    isActive: true,
  },

  // Síntomas Dermatológicos
  {
    id: nanoid(),
    name: 'Prurito',
    category: 'Dermatológico',
    subcategory: 'Sensación cutánea',
    icdCode: 'L29.9',
    isSymptom: true,
    description: 'Sensación de picazón en la piel',
    commonCauses: ['Dermatitis', 'Urticaria', 'Xerosis', 'Medicamentos', 'Enfermedades sistémicas'],
    severity: 'low',
    bodySystem: 'Tegumentario',
    order: 22,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Exantema',
    category: 'Dermatológico',
    subcategory: 'Erupción cutánea',
    icdCode: 'R21',
    isSymptom: false, // Es un signo
    description: 'Erupción cutánea generalizada',
    commonCauses: ['Infecciones virales', 'Reacciones alérgicas', 'Medicamentos', 'Enfermedades autoinmunes'],
    severity: 'medium',
    bodySystem: 'Tegumentario',
    order: 23,
    isActive: true,
  },

  // Síntomas Oftalmológicos
  {
    id: nanoid(),
    name: 'Visión borrosa',
    category: 'Oftalmológico',
    subcategory: 'Agudeza visual',
    icdCode: 'H53.8',
    isSymptom: true,
    description: 'Pérdida de nitidez de la visión',
    commonCauses: ['Errores refractivos', 'Cataratas', 'Diabetes mellitus', 'Glaucoma', 'Medicamentos'],
    severity: 'medium',
    bodySystem: 'Oftalmológico',
    order: 24,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Ojo rojo',
    category: 'Oftalmológico',
    subcategory: 'Inflamación ocular',
    icdCode: 'H57.8',
    isSymptom: false, // Es un signo
    description: 'Enrojecimiento del ojo',
    commonCauses: ['Conjuntivitis', 'Uveítis', 'Glaucoma agudo', 'Traumatismo', 'Sequedad ocular'],
    severity: 'medium',
    bodySystem: 'Oftalmológico',
    order: 25,
    isActive: true,
  }
];

export async function seedSymptoms() {
  try {
    console.log('🌱 Seeding symptoms...');
    
    // Insertar síntomas
    for (const symptom of SYMPTOMS_DATA) {
      await db.insert(symptoms).values(symptom);
    }
    
    console.log(`✅ Successfully seeded ${SYMPTOMS_DATA.length} symptoms`);
  } catch (error) {
    console.error('❌ Error seeding symptoms:', error);
    throw error;
  }
}

// Ejecutar si el archivo se ejecuta directamente
if (require.main === module) {
  seedSymptoms()
    .then(() => {
      console.log('✅ Symptom seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Symptom seeding failed:', error);
      process.exit(1);
    });
}