import { db } from '../db/drizzle';
import { user, userRoles, registrationRequests } from '../db/schema';
import { eq } from 'drizzle-orm';

const EMAIL = '<EMAIL>';

async function resetUserOnboarding() {
  console.log(`🔄 Reseteando onboarding para usuario: ${EMAIL}`);
  
  try {
    // 1. Buscar usuario en base de datos
    const userInfo = await db
      .select()
      .from(user)
      .where(eq(user.email, EMAIL))
      .limit(1);
    
    if (!userInfo.length) {
      console.log('❌ Usuario no encontrado en base de datos');
      return;
    }
    
    const dbUser = userInfo[0];
    console.log('👤 Usuario encontrado:', { id: dbUser.id, email: dbUser.email, status: dbUser.overallStatus });
    
    // 2. Eliminar roles si existen
    const existingRoles = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, dbUser.id));
    
    if (existingRoles.length > 0) {
      console.log(`🗑️ Eliminando ${existingRoles.length} roles existentes...`);
      await db.delete(userRoles).where(eq(userRoles.userId, dbUser.id));
    }
    
    // 3. Eliminar solicitudes de registro si existen
    const existingRequests = await db
      .select()
      .from(registrationRequests)
      .where(eq(registrationRequests.userId, dbUser.id));
    
    if (existingRequests.length > 0) {
      console.log(`🗑️ Eliminando ${existingRequests.length} solicitudes existentes...`);
      await db.delete(registrationRequests).where(eq(registrationRequests.userId, dbUser.id));
    }
    
    // 4. Actualizar status del usuario a pending
    await db
      .update(user)
      .set({ 
        overallStatus: 'pending',
        updatedAt: new Date()
      })
      .where(eq(user.id, dbUser.id));
    
    console.log('✅ Usuario reseteado exitosamente en base de datos');
    console.log('📝 Ahora cierra sesión y vuelve a iniciar sesión para limpiar metadatos de Clerk');
    console.log('🔗 Los metadatos de Clerk se actualizarán automáticamente en el próximo login');
    
  } catch (error) {
    console.error('❌ Error reseteando usuario:', error);
  }
}

resetUserOnboarding();