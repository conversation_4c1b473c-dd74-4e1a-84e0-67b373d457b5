import { 
  pgTable, 
  text, 
  timestamp, 
  boolean, 
  integer,
  jsonb,
  uniqueIndex,
  index,
  numeric,
  real,
  primaryKey
} from 'drizzle-orm/pg-core';
import { relations, eq, sql } from 'drizzle-orm';

// ============================================
// ESTRUCTURA v3: BASE DE DATOS OPTIMIZADA
// ============================================

// 1. USUARIOS BASE - Solo datos personales
export const user = pgTable("user", {
  // Identificación
  id: text("id").primaryKey(),
  email: text("email").unique().notNull(),
  emailVerified: boolean("emailVerified").default(false),
  name: text("name"),
  image: text("image"),
  
  // Datos personales
  firstName: text("firstName"),
  lastName: text("lastName"),
  documentType: text("documentType"),
  documentNumber: text("documentNumber"),
  dateOfBirth: timestamp("dateOfBirth"),
  gender: text("gender"),
  phone: text("phone"),
  alternativePhone: text("alternativePhone"),
  address: text("address"),
  
  // Referencias geográficas
  countryId: integer("countryId").references(() => countries.id),
  departmentId: integer("departmentId").references(() => departments.id),
  municipalityId: integer("municipalityId").references(() => municipalities.id),
  occupationId: integer("occupationId").references(() => occupations.id),
  
  // Contacto de emergencia
  emergencyContact: text("emergencyContact"),
  emergencyPhone: text("emergencyPhone"),
  emergencyRelationshipId: integer("emergencyRelationshipId").references(() => relationships.id),
  
  // Estado general del usuario
  overallStatus: text("overallStatus").default("pending"), // pending, active, inactive, suspended
  
  // Timestamps
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    emailIdx: uniqueIndex("email_idx").on(table.email),
    statusIdx: index("overall_status_idx").on(table.overallStatus),
    documentIdx: uniqueIndex("document_idx").on(table.documentType, table.documentNumber),
  };
});

// 2. ROLES MÚLTIPLES POR USUARIO
export const userRoles = pgTable("user_roles", {
  // Identificación
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => user.id, { onDelete: "cascade" }),
  role: text("role").notNull(), // admin, doctor, patient, assistant, guardian, provider
  
  // Estado específico del rol
  status: text("status").default("pending"), // pending, active, inactive, suspended
  
  // Referencias específicas por rol
  consultoryId: text("consultoryId").references(() => consultories.id),
  specialtyId: integer("specialtyId").references(() => medicalSpecialties.id),
  preferredDoctorId: text("preferredDoctorId").references(() => user.id),
  
  // Datos específicos del rol
  medicalLicense: text("medicalLicense"),
  roleData: jsonb("roleData"), // datos flexibles por rol
  
  // Auditoría del rol
  approvedBy: text("approvedBy").references(() => user.id),
  approvedAt: timestamp("approvedAt"),
  rejectedBy: text("rejectedBy").references(() => user.id),
  rejectedAt: timestamp("rejectedAt"),
  rejectionReason: text("rejectionReason"),
  
  // Timestamps
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    userRoleIdx: uniqueIndex("user_role_idx").on(table.userId, table.role),
    userIdx: index("user_roles_user_idx").on(table.userId),
    roleIdx: index("user_roles_role_idx").on(table.role),
    statusIdx: index("user_roles_status_idx").on(table.status),
  };
});

// 3. SOLICITUDES DE REGISTRO
export const registrationRequests = pgTable("registrationRequests", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => user.id),
  role: text("role").notNull(),
  
  status: text("status").default("pending"), // pending, approved, rejected, reviewing
  
  generalData: jsonb("generalData"),
  specificData: jsonb("specificData"),
  
  reviewedBy: text("reviewedBy").references(() => user.id),
  reviewedAt: timestamp("reviewedAt"),
  reviewNotes: text("reviewNotes"),
  rejectionReason: text("rejectionReason"),
  
  submittedAt: timestamp("submittedAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    userIdx: index("registration_requests_user_idx").on(table.userId),
    statusIdx: index("registration_requests_status_idx").on(table.status),
    submittedIdx: index("registration_requests_submitted_idx").on(table.submittedAt),
  };
});

// ============================================
// CATÁLOGOS ESENCIALES
// ============================================

// 4. CONSULTORIOS (AMPLIADO - Sistema completo de consultorios médicos)
export const consultories = pgTable("consultories", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Consultorio Pediatría Principal"
  code: text("code").unique(), // "PED-001", "CAR-001"
  type: text("type"), // "pediatric", "specialty", "emergency", "procedure", "telemedicine"
  specialty: text("specialty"), // "Pediatría General", "Cardiología Pediátrica"
  capacity: integer("capacity").default(1), // Número de pacientes simultáneos
  floor: integer("floor"),
  building: text("building"), // "Edificio Principal", "Edificio Especialidades"
  address: text("address"),
  phone: text("phone"),
  email: text("email"),
  logoUrl: text("logoUrl"), // URL del logo del consultorio para documentos oficiales
  logoPublicId: text("logoPublicId"), // ID público de Cloudinary para gestión
  
  // Paleta de colores dinámica basada en el logo
  colorPalette: jsonb("color_palette").$type<{
    primary?: string;        // Color principal extraído del logo
    secondary?: string;      // Color secundario extraído del logo
    accent?: string;         // Color de acento extraído del logo
    background?: string;     // Color de fondo sugerido
    text?: string;          // Color de texto sugerido
    success?: string;       // Color para estados exitosos
    warning?: string;       // Color para advertencias
    error?: string;         // Color para errores
    info?: string;          // Color para información
    neutral?: string;       // Color neutral/gris
    gradientStart?: string; // Inicio de gradiente
    gradientEnd?: string;   // Final de gradiente
    extractedColors?: string[]; // Array de todos los colores extraídos del logo
    isAutoGenerated?: boolean;  // Si fue generada automáticamente o editada manualmente
    lastUpdated?: string;   // Fecha de última actualización
  }>(),
  
  // Referencias geográficas
  countryId: integer("countryId").references(() => countries.id),
  departmentId: integer("departmentId").references(() => departments.id),
  municipalityId: integer("municipalityId").references(() => municipalities.id),
  
  businessHours: jsonb("businessHours"), // Horarios detallados por día
  equipment: jsonb("equipment"), // Array de equipos médicos disponibles
  services: jsonb("services"), // Array de servicios que se ofrecen
  
  // Configuración regional del consultorio
  regionalSettings: jsonb("regional_settings").$type<{
    dateFormat?: string;
    dateTimeFormat?: string;
    timeFormat?: string;
    currency?: string;
    currencySymbol?: string;
    currencyPosition?: 'before' | 'after';
    locale?: string;
    timezone?: string;
    decimalSeparator?: string;
    thousandsSeparator?: string;
    weekStartsOn?: 0 | 1 | 2 | 3 | 4 | 5 | 6;
    phoneFormat?: string;
    phoneDigits?: number;
    phoneExample?: string;
  }>(),
  
  isEmergencyCapable: boolean("isEmergencyCapable").default(false),
  hasAirConditioning: boolean("hasAirConditioning").default(true),
  hasWaitingRoom: boolean("hasWaitingRoom").default(true),
  accessibility: jsonb("accessibility"), // Configuración de accesibilidad
  // Campo para identificar consultorio principal
  isPrimary: boolean("isPrimary").default(false),
  
  // Campos legacy mantenidos
  active: boolean("active").default(true), // Renombrado a isActive en nuevas APIs
  isActive: boolean("isActive").default(true), // Campo nuevo estándar
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    activeIdx: index("consultories_active_idx").on(table.active),
    isActiveIdx: index("consultories_is_active_idx").on(table.isActive),
    codeIdx: uniqueIndex("consultories_code_idx").on(table.code),
    typeIdx: index("consultories_type_idx").on(table.type),
    buildingIdx: index("consultories_building_idx").on(table.building),
    emergencyIdx: index("consultories_emergency_idx").on(table.isEmergencyCapable),
  };
});

// 5. ESPECIALIDADES MÉDICAS
export const medicalSpecialties = pgTable("medical_specialties", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  category: text("category"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});

// 6-10. CATÁLOGOS GEOGRÁFICOS Y DE CLASIFICACIÓN
export const countries = pgTable("countries", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  code: text("code").unique(),
  phoneCode: text("phoneCode"),
  currency: text("currency"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
});

export const departments = pgTable("departments", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  countryId: integer("countryId").notNull().references(() => countries.id),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});

export const municipalities = pgTable("municipalities", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  departmentId: integer("departmentId").notNull().references(() => departments.id),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});

export const occupations = pgTable("occupations", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  category: text("category"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});

export const relationships = pgTable("relationships", {
  id: integer("id").primaryKey(),
  name: text("name").notNull(),
  category: text("category"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow().notNull(),
  updatedAt: timestamp("updatedAt").defaultNow().notNull(),
});

// ============================================
// FUNCIONALIDADES ESPECÍFICAS
// ============================================

// 11. RELACIONES ASISTENTE-DOCTOR
export const assistantDoctorRelations = pgTable("assistant_doctor_relations", {
  id: text("id").primaryKey(),
  assistantId: text("assistantId").notNull().references(() => user.id),
  doctorId: text("doctorId").notNull().references(() => user.id),
  consultoryId: text("consultoryId").references(() => consultories.id),
  permissions: jsonb("permissions"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    assistantDoctorIdx: uniqueIndex("assistant_doctor_idx").on(table.assistantId, table.doctorId),
    assistantIdx: index("assistant_relations_idx").on(table.assistantId),
    doctorIdx: index("doctor_relations_idx").on(table.doctorId),
  };
});

// 12. CÓDIGOS DE ASOCIACIÓN
export const associationCodes = pgTable("association_codes", {
  id: text("id").primaryKey(),
  code: text("code").unique().notNull(),
  patientId: text("patientId").notNull().references(() => user.id),
  expiresAt: timestamp("expiresAt").notNull(),
  usedBy: text("usedBy").references(() => user.id),
  usedAt: timestamp("usedAt"),
  createdAt: timestamp("createdAt").defaultNow(),
}, (table) => {
  return {
    codeIdx: uniqueIndex("association_code_idx").on(table.code),
    patientIdx: index("association_patient_idx").on(table.patientId),
    expiresIdx: index("association_expires_idx").on(table.expiresAt),
  };
});

// 13. RELACIONES GUARDIAN-PACIENTE
export const guardianPatientRelations = pgTable("guardian_patient_relations", {
  id: text("id").primaryKey(),
  guardianId: text("guardianId").notNull().references(() => user.id),
  patientId: text("patientId").notNull().references(() => user.id),
  relationship: text("relationship"),
  isPrimary: boolean("isPrimary").default(false),
  canMakeDecisions: boolean("canMakeDecisions").default(true),
  validUntil: timestamp("validUntil"),
  createdAt: timestamp("createdAt").defaultNow(),
}, (table) => {
  return {
    guardianPatientIdx: uniqueIndex("guardian_patient_idx").on(table.guardianId, table.patientId),
    guardianIdx: index("guardian_relations_idx").on(table.guardianId),
    patientIdx: index("patient_relations_idx").on(table.patientId),
  };
});

// 14. NOTIFICACIONES
export const notifications = pgTable("notifications", {
  id: text("id").primaryKey(),
  userId: text("userId").notNull().references(() => user.id),
  type: text("type").notNull(),
  title: text("title").notNull(),
  message: text("message").notNull(),
  read: boolean("read").default(false),
  data: jsonb("data"),
  createdAt: timestamp("createdAt").defaultNow(),
}, (table) => {
  return {
    userIdx: index("notifications_user_idx").on(table.userId),
    readIdx: index("notifications_read_idx").on(table.read),
    typeIdx: index("notifications_type_idx").on(table.type),
  };
});

// ============================================
// RELACIONES DRIZZLE
// ============================================

export const userRelations = relations(user, ({ many, one }) => ({
  // Múltiples roles por usuario
  roles: many(userRoles),
  registrationRequests: many(registrationRequests),
  notifications: many(notifications),
  
  // Como asistente o doctor
  assistantRelations: many(assistantDoctorRelations, { relationName: "assistantRelations" }),
  doctorRelations: many(assistantDoctorRelations, { relationName: "doctorRelations" }),
  
  // Como guardian o paciente
  guardianRelations: many(guardianPatientRelations, { relationName: "guardianRelations" }),
  patientRelations: many(guardianPatientRelations, { relationName: "patientRelations" }),
  
  // Códigos generados (como paciente)
  associationCodes: many(associationCodes),
  
  // Referencias geográficas
  country: one(countries, { fields: [user.countryId], references: [countries.id] }),
  department: one(departments, { fields: [user.departmentId], references: [departments.id] }),
  municipality: one(municipalities, { fields: [user.municipalityId], references: [municipalities.id] }),
  occupation: one(occupations, { fields: [user.occupationId], references: [occupations.id] }),
  emergencyRelationship: one(relationships, { fields: [user.emergencyRelationshipId], references: [relationships.id] }),
  
  // Precios personalizados (como doctor)
  servicePrices: many(doctorServicePrices),
}));

export const userRolesRelations = relations(userRoles, ({ one }) => ({
  user: one(user, { fields: [userRoles.userId], references: [user.id] }),
  consultory: one(consultories, { fields: [userRoles.consultoryId], references: [consultories.id] }),
  specialty: one(medicalSpecialties, { fields: [userRoles.specialtyId], references: [medicalSpecialties.id] }),
  preferredDoctor: one(user, { fields: [userRoles.preferredDoctorId], references: [user.id] }),
  approvedBy: one(user, { fields: [userRoles.approvedBy], references: [user.id] }),
  rejectedBy: one(user, { fields: [userRoles.rejectedBy], references: [user.id] }),
}));

export const registrationRequestsRelations = relations(registrationRequests, ({ one }) => ({
  user: one(user, { fields: [registrationRequests.userId], references: [user.id] }),
  reviewedBy: one(user, { fields: [registrationRequests.reviewedBy], references: [user.id] }),
}));

export const assistantDoctorRelationsRelations = relations(assistantDoctorRelations, ({ one }) => ({
  assistant: one(user, { fields: [assistantDoctorRelations.assistantId], references: [user.id], relationName: "assistantRelations" }),
  doctor: one(user, { fields: [assistantDoctorRelations.doctorId], references: [user.id], relationName: "doctorRelations" }),
  consultory: one(consultories, { fields: [assistantDoctorRelations.consultoryId], references: [consultories.id] }),
}));

export const guardianPatientRelationsRelations = relations(guardianPatientRelations, ({ one }) => ({
  guardian: one(user, { fields: [guardianPatientRelations.guardianId], references: [user.id], relationName: "guardianRelations" }),
  patient: one(user, { fields: [guardianPatientRelations.patientId], references: [user.id], relationName: "patientRelations" }),
}));

export const associationCodesRelations = relations(associationCodes, ({ one }) => ({
  patient: one(user, { fields: [associationCodes.patientId], references: [user.id] }),
  usedBy: one(user, { fields: [associationCodes.usedBy], references: [user.id] }),
}));

export const notificationsRelations = relations(notifications, ({ one }) => ({
  user: one(user, { fields: [notifications.userId], references: [user.id] }),
}));

// Relaciones de catálogos
export const consultoriesRelations = relations(consultories, ({ one, many }) => ({
  userRoles: many(userRoles),
  assistantDoctorRelations: many(assistantDoctorRelations),
  country: one(countries, { fields: [consultories.countryId], references: [countries.id] }),
  department: one(departments, { fields: [consultories.departmentId], references: [departments.id] }),
  municipality: one(municipalities, { fields: [consultories.municipalityId], references: [municipalities.id] }),
}));

export const medicalSpecialtiesRelations = relations(medicalSpecialties, ({ many }) => ({
  userRoles: many(userRoles),
}));

export const countriesRelations = relations(countries, ({ many }) => ({
  departments: many(departments),
  users: many(user),
  consultories: many(consultories),
}));

export const departmentsRelations = relations(departments, ({ one, many }) => ({
  country: one(countries, { fields: [departments.countryId], references: [countries.id] }),
  municipalities: many(municipalities),
  users: many(user),
  consultories: many(consultories),
}));

export const municipalitiesRelations = relations(municipalities, ({ one, many }) => ({
  department: one(departments, { fields: [municipalities.departmentId], references: [departments.id] }),
  users: many(user),
  consultories: many(consultories),
}));

export const occupationsRelations = relations(occupations, ({ many }) => ({
  users: many(user),
}));

export const relationshipsRelations = relations(relationships, ({ many }) => ({
  users: many(user),
}));

// ============================================
// NUEVOS CATÁLOGOS - FASE 1 COMPLETADA
// ============================================

// 15. MONEDAS
export const currencies = pgTable("currencies", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Quetzal Guatemalteco", "Dólar Estadounidense"
  code: text("code").unique().notNull(), // "GTQ", "USD", "EUR"
  symbol: text("symbol").notNull(), // "Q", "$", "€"
  exchangeRate: real("exchangeRate").notNull().default(1.0), // Tasa de cambio vs moneda base
  isDefault: boolean("isDefault").default(false), // Solo una puede ser predeterminada
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    codeIdx: uniqueIndex("currencies_code_idx").on(table.code),
    activeIdx: index("currencies_active_idx").on(table.isActive),
    defaultIdx: index("currencies_default_idx").on(table.isDefault),
  };
});

// 16. TIPOS DE ACTIVIDAD (Para calendario y citas)
export const activityTypes = pgTable("activity_types", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Consulta", "Cirugía", "Vacaciones"
  category: text("category").notNull(), // "medical", "administrative", "personal"
  color: text("color").notNull(), // "#3B82F6" para UI
  duration: integer("duration").notNull(), // Duración típica en minutos
  requiresPatient: boolean("requiresPatient").default(true),
  allowsRecurrence: boolean("allowsRecurrence").default(false),
  respectsSchedule: boolean("respectsSchedule").default(true), // Si respeta horarios configurados
  icon: text("icon"), // Icono para UI
  order: integer("order"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    categoryIdx: index("activity_types_category_idx").on(table.category),
    activeIdx: index("activity_types_active_idx").on(table.isActive),
    orderIdx: index("activity_types_order_idx").on(table.order),
  };
});

// 17. MEDIOS/ORIGEN DE PACIENTES (Para análisis de marketing)
export const mediaSources = pgTable("media_sources", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Facebook", "Google", "Referido por médico"
  type: text("type").notNull(), // "digital", "traditional", "referral", "direct"
  category: text("category").notNull(), // "Redes Sociales", "Buscadores", "Referidos"
  trackingEnabled: boolean("trackingEnabled").default(false),
  cost: real("cost").default(0.0), // Costo mensual para ROI
  description: text("description"),
  icon: text("icon"),
  color: text("color"), // Color para gráficos
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    typeIdx: index("media_sources_type_idx").on(table.type),
    categoryIdx: index("media_sources_category_idx").on(table.category),
    activeIdx: index("media_sources_active_idx").on(table.isActive),
    trackingIdx: index("media_sources_tracking_idx").on(table.trackingEnabled),
  };
});

// 18. NIVELES DE ESCOLARIDAD
export const educationLevels = pgTable("education_levels", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Primaria completa", "Universidad", "Posgrado"
  order: integer("order").notNull(), // Orden jerárquico (0-9)
  description: text("description"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    orderIdx: uniqueIndex("education_levels_order_idx").on(table.order),
    activeIdx: index("education_levels_active_idx").on(table.isActive),
  };
});

// 19. TIPOS DE DOCUMENTO
export const documentTypes = pgTable("document_types", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "DPI", "Pasaporte Guatemalteco", "SSN"
  shortName: text("shortName").notNull(), // "DPI", "Passport", "SSN"
  countryId: integer("countryId").notNull().references(() => countries.id),
  countryName: text("countryName").notNull(), // Desnormalizado para performance
  format: text("format").notNull(), // Regex de validación
  maxLength: integer("maxLength").notNull(),
  minLength: integer("minLength").notNull(),
  isRequired: boolean("isRequired").default(false), // Obligatorio para el país
  description: text("description"),
  example: text("example"), // Ejemplo de formato válido
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    countryIdx: index("document_types_country_idx").on(table.countryId),
    activeIdx: index("document_types_active_idx").on(table.isActive),
    requiredIdx: index("document_types_required_idx").on(table.isRequired),
  };
});

// 20. ESTADOS CIVILES
export const maritalStatus = pgTable("marital_status", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Soltero(a)", "Casado(a)", "Divorciado(a)"
  allowsSpouse: boolean("allowsSpouse").default(false), // Permite registrar cónyuge
  legalImplications: text("legalImplications"), // Para consentimientos médicos
  description: text("description"),
  order: integer("order"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    orderIdx: uniqueIndex("marital_status_order_idx").on(table.order),
    activeIdx: index("marital_status_active_idx").on(table.isActive),
    spouseIdx: index("marital_status_spouse_idx").on(table.allowsSpouse),
  };
});

// 21. ANTECEDENTES PATOLÓGICOS
export const pathologicalHistory = pgTable("pathological_history", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Asma bronquial", "Diabetes tipo 1"
  category: text("category").notNull(), // "Respiratorio", "Endocrino", "Cardiovascular"
  icd11Code: text("icd11Code"), // Código CIE-11
  severity: text("severity").notNull(), // "low", "moderate", "high"
  isHereditary: boolean("isHereditary").default(false),
  requiresSpecialistFollow: boolean("requiresSpecialistFollow").default(false),
  commonInChildren: boolean("commonInChildren").default(false),
  riskLevel: text("riskLevel").notNull(), // "low", "medium", "high"
  description: text("description"),
  symptoms: jsonb("symptoms"), // Array de síntomas principales
  treatments: jsonb("treatments"), // Array de tratamientos comunes
  order: integer("order"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    categoryIdx: index("pathological_history_category_idx").on(table.category),
    severityIdx: index("pathological_history_severity_idx").on(table.severity),
    riskIdx: index("pathological_history_risk_idx").on(table.riskLevel),
    hereditaryIdx: index("pathological_history_hereditary_idx").on(table.isHereditary),
    childrenIdx: index("pathological_history_children_idx").on(table.commonInChildren),
    activeIdx: index("pathological_history_active_idx").on(table.isActive),
    icd11Idx: index("pathological_history_icd11_idx").on(table.icd11Code),
  };
});

// 22. ANTECEDENTES NO PATOLÓGICOS
export const nonPathologicalHistory = pgTable("non_pathological_history", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Lactancia materna", "Ejercicio regular"
  category: text("category").notNull(), // "Alimentación", "Actividad Física", "Sueño"
  subcategory: text("subcategory"), // "Lactancia", "Ejercicio", "Horarios"
  isPositive: boolean("isPositive").default(true), // true = beneficioso, false = factor de riesgo
  importance: text("importance").notNull(), // "low", "medium", "high"
  ageRelevant: text("ageRelevant").notNull(), // "infant", "child", "adolescent", "all"
  description: text("description"),
  benefits: jsonb("benefits"), // Array de beneficios (si es positivo)
  risks: jsonb("risks"), // Array de riesgos (si es negativo)
  duration: text("duration"), // Duración típica o recomendada
  order: integer("order"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    categoryIdx: index("non_pathological_history_category_idx").on(table.category),
    subcategoryIdx: index("non_pathological_history_subcategory_idx").on(table.subcategory),
    positiveIdx: index("non_pathological_history_positive_idx").on(table.isPositive),
    importanceIdx: index("non_pathological_history_importance_idx").on(table.importance),
    ageIdx: index("non_pathological_history_age_idx").on(table.ageRelevant),
    activeIdx: index("non_pathological_history_active_idx").on(table.isActive),
  };
});

// 23. ACTUALIZACIÓN DE CONSULTORIOS (Ampliado)
// Nota: El esquema existente de consultories se mantiene pero se amplían los campos

// ============================================
// NUEVOS CATÁLOGOS - FASE 2: SERVICIOS MÉDICOS Y SEGUROS
// ============================================

// 24. EMPRESAS/NITs (Para facturación legal en Guatemala)
export const companies = pgTable("companies", {
  id: text("id").primaryKey(),
  businessName: text("businessName").notNull(), // Razón social completa
  commercialName: text("commercialName").notNull(), // Nombre comercial/marca
  nit: text("nit").unique().notNull(), // NIT para facturación (formato: 12345678-9)
  address: text("address").notNull(), // Dirección fiscal
  phone: text("phone"), // Teléfono principal
  email: text("email"), // Email de contacto
  website: text("website"), // Sitio web (opcional)
  legalRepresentative: text("legalRepresentative").notNull(), // Representante legal
  activityType: text("activityType").notNull(), // Tipo de actividad económica
  taxRegime: text("taxRegime"), // Régimen tributario
  
  // Información adicional para facturación
  fiscalAddress: text("fiscalAddress"), // Dirección fiscal específica
  contactPerson: text("contactPerson"), // Persona de contacto
  contactPhone: text("contactPhone"), // Teléfono de contacto
  contactEmail: text("contactEmail"), // Email de contacto
  
  // Configuración
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    nitIdx: uniqueIndex("companies_nit_idx").on(table.nit),
    activeIdx: index("companies_active_idx").on(table.isActive),
    businessNameIdx: index("companies_business_name_idx").on(table.businessName),
    activityTypeIdx: index("companies_activity_type_idx").on(table.activityType),
  };
});

// 25. SERVICIOS MÉDICOS (Catálogo simple para administración)
export const medicalServices = pgTable("medical_services", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Consulta General", "Procedimiento Menor", "Examen Físico"
  description: text("description"), // Descripción detallada del servicio
  code: text("code").unique(), // Código interno del servicio (opcional)
  category: text("category").notNull(), // "Consulta", "Procedimiento", "Emergencia", "Preventivo", "Diagnóstico"
  basePrice: numeric("basePrice", { precision: 10, scale: 2 }), // Precio base sugerido
  currency: text("currency").default("GTQ"), // Moneda del precio
  duration: integer("duration"), // Duración estimada en minutos
  requiresEquipment: boolean("requiresEquipment").default(false), // Requiere equipo especial
  requiresSpecialist: boolean("requiresSpecialist").default(false), // Requiere especialista
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    codeIdx: uniqueIndex("medical_services_code_idx").on(table.code),
    categoryIdx: index("medical_services_category_idx").on(table.category),
    activeIdx: index("medical_services_active_idx").on(table.isActive),
    nameIdx: index("medical_services_name_idx").on(table.name),
    priceIdx: index("medical_services_price_idx").on(table.basePrice),
  };
});

// 26. PRECIOS POR MÉDICO - Sistema de precios personalizados
export const doctorServicePrices = pgTable("doctor_service_prices", {
  id: text("id").primaryKey(),
  doctorId: text("doctorId").notNull().references(() => user.id, { onDelete: "cascade" }),
  serviceId: text("serviceId").notNull().references(() => medicalServices.id, { onDelete: "cascade" }),
  customPrice: numeric("customPrice", { precision: 10, scale: 2 }).notNull(), // Precio personalizado
  currency: text("currency").default("GTQ"), // Moneda del precio
  isActive: boolean("isActive").default(true), // Si este precio está activo
  effectiveFrom: timestamp("effectiveFrom").defaultNow(), // Fecha desde cuando aplica
  effectiveUntil: timestamp("effectiveUntil"), // Fecha hasta cuando aplica (opcional)
  notes: text("notes"), // Notas adicionales sobre el precio
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    // Única combinación doctor-servicio activa
    doctorServiceIdx: uniqueIndex("doctor_service_active_idx")
      .on(table.doctorId, table.serviceId)
      .where(sql`${table.isActive} = true`),
    doctorIdx: index("doctor_service_prices_doctor_idx").on(table.doctorId),
    serviceIdx: index("doctor_service_prices_service_idx").on(table.serviceId),
    activeIdx: index("doctor_service_prices_active_idx").on(table.isActive),
    priceIdx: index("doctor_service_prices_price_idx").on(table.customPrice),
    effectiveDateIdx: index("doctor_service_prices_effective_idx").on(table.effectiveFrom, table.effectiveUntil),
  };
});

// 11. SERVICE TAGS - Sistema de etiquetas para servicios médicos
export const serviceTags = pgTable("service_tags", {
  id: text("id").primaryKey(),
  name: text("name").notNull().unique(), // "Requiere Equipo", "Pediatría", "Ambulatorio"
  description: text("description"), // Descripción del tag
  color: text("color").default("blue"), // Color para UI: "blue", "green", "red", "yellow", "purple"
  category: text("category").default("general"), // "requirement", "specialty", "type", "duration", "general"
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    nameIdx: uniqueIndex("service_tags_name_idx").on(table.name),
    categoryIdx: index("service_tags_category_idx").on(table.category),
    activeIdx: index("service_tags_active_idx").on(table.isActive),
  };
});

// 12. MEDICAL SERVICE TAGS - Relación muchos-a-muchos entre servicios y tags
export const medicalServiceTags = pgTable("medical_service_tags", {
  serviceId: text("serviceId").notNull().references(() => medicalServices.id, { onDelete: "cascade" }),
  tagId: text("tagId").notNull().references(() => serviceTags.id, { onDelete: "cascade" }),
  createdAt: timestamp("createdAt").defaultNow(),
}, (table) => {
  return {
    pk: primaryKey({ columns: [table.serviceId, table.tagId] }),
    serviceIdx: index("medical_service_tags_service_idx").on(table.serviceId),
    tagIdx: index("medical_service_tags_tag_idx").on(table.tagId),
  };
});

// ============================================
// RELACIONES PARA NUEVOS CATÁLOGOS
// ============================================

export const currenciesRelations = relations(currencies, ({ many }) => ({
  // Futuras relaciones con servicios médicos, facturas, etc.
}));

export const activityTypesRelations = relations(activityTypes, ({ many }) => ({
  // Futuras relaciones con citas, calendarios, etc.
}));

export const mediaSourcesRelations = relations(mediaSources, ({ many }) => ({
  // Futuras relaciones con pacientes, análisis de ROI, etc.
}));

export const educationLevelsRelations = relations(educationLevels, ({ many }) => ({
  // Futuras relaciones con usuarios/pacientes
}));

export const documentTypesRelations = relations(documentTypes, ({ one, many }) => ({
  country: one(countries, { fields: [documentTypes.countryId], references: [countries.id] }),
  // Futuras relaciones con usuarios/pacientes
}));

export const maritalStatusRelations = relations(maritalStatus, ({ many }) => ({
  // Futuras relaciones con usuarios/pacientes
}));

export const pathologicalHistoryRelations = relations(pathologicalHistory, ({ many }) => ({
  // Futuras relaciones con expedientes médicos
}));

export const nonPathologicalHistoryRelations = relations(nonPathologicalHistory, ({ many }) => ({
  // Futuras relaciones con expedientes médicos
}));

export const companiesRelations = relations(companies, ({ many }) => ({
  // Futuras relaciones con servicios médicos, facturas, etc.
}));

export const medicalServicesRelations = relations(medicalServices, ({ many }) => ({
  tags: many(medicalServiceTags),
  doctorPrices: many(doctorServicePrices),
  // Futuras relaciones con citas, facturas, etc.
}));

export const serviceTagsRelations = relations(serviceTags, ({ many }) => ({
  services: many(medicalServiceTags),
}));

export const medicalServiceTagsRelations = relations(medicalServiceTags, ({ one }) => ({
  service: one(medicalServices, { fields: [medicalServiceTags.serviceId], references: [medicalServices.id] }),
  tag: one(serviceTags, { fields: [medicalServiceTags.tagId], references: [serviceTags.id] }),
}));

export const doctorServicePricesRelations = relations(doctorServicePrices, ({ one }) => ({
  doctor: one(user, { fields: [doctorServicePrices.doctorId], references: [user.id] }),
  service: one(medicalServices, { fields: [doctorServicePrices.serviceId], references: [medicalServices.id] }),
}));


// ============================================
// HORARIOS DE TRABAJO MÉDICOS
// ============================================

// Tabla para gestionar los horarios de trabajo de los doctores
export const doctorSchedules = pgTable("doctor_schedules", {
  id: text("id").primaryKey(),
  doctorId: text("doctorId").notNull().references(() => user.id, { onDelete: "cascade" }),
  consultoryId: text("consultoryId").references(() => consultories.id),
  
  // Configuración del horario
  dayOfWeek: integer("dayOfWeek").notNull(), // 0=Domingo, 1=Lunes, ..., 6=Sábado
  startTime: text("startTime").notNull(), // Formato "HH:mm" (ej: "08:00")
  endTime: text("endTime").notNull(), // Formato "HH:mm" (ej: "17:00")
  
  // Descansos y configuración especial
  lunchBreakStart: text("lunchBreakStart"), // Formato "HH:mm" (ej: "12:00")
  lunchBreakEnd: text("lunchBreakEnd"), // Formato "HH:mm" (ej: "13:00")
  
  // Configuración de citas
  appointmentDuration: integer("appointmentDuration").default(30), // Duración por defecto en minutos
  maxAppointmentsPerHour: integer("maxAppointmentsPerHour").default(2), // Máximo de citas por hora
  
  // Estado y configuración
  isActive: boolean("isActive").default(true),
  allowEmergencies: boolean("allowEmergencies").default(true), // Permite citas de emergencia
  allowOnlineBooking: boolean("allowOnlineBooking").default(true), // Permite reserva online
  
  // Notas especiales
  notes: text("notes"), // Notas especiales para el día
  
  // Auditoría
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorIdx: index("doctor_schedules_doctor_idx").on(table.doctorId),
    consultoryIdx: index("doctor_schedules_consultory_idx").on(table.consultoryId),
    dayIdx: index("doctor_schedules_day_idx").on(table.dayOfWeek),
    activeIdx: index("doctor_schedules_active_idx").on(table.isActive),
    doctorDayIdx: uniqueIndex("doctor_schedules_doctor_day_idx").on(table.doctorId, table.dayOfWeek, table.consultoryId),
  };
});

// Excepciones de horario (días especiales, vacaciones, etc.)
export const doctorScheduleExceptions = pgTable("doctor_schedule_exceptions", {
  id: text("id").primaryKey(),
  doctorId: text("doctorId").notNull().references(() => user.id, { onDelete: "cascade" }),
  consultoryId: text("consultoryId").references(() => consultories.id),
  
  // Fecha específica de la excepción
  exceptionDate: timestamp("exceptionDate").notNull(),
  
  // Tipo de excepción
  type: text("type").notNull(), // "vacation", "sick_leave", "conference", "special_hours", "closed"
  title: text("title").notNull(), // Título descriptivo
  description: text("description"), // Descripción detallada
  
  // Horarios especiales (si aplica)
  startTime: text("startTime"), // Si es "special_hours"
  endTime: text("endTime"), // Si es "special_hours"
  
  // Estado
  isAllDay: boolean("isAllDay").default(false), // Todo el día afectado
  
  // Auditoría
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorIdx: index("doctor_schedule_exceptions_doctor_idx").on(table.doctorId),
    consultoryIdx: index("doctor_schedule_exceptions_consultory_idx").on(table.consultoryId),
    dateIdx: index("doctor_schedule_exceptions_date_idx").on(table.exceptionDate),
    typeIdx: index("doctor_schedule_exceptions_type_idx").on(table.type),
    doctorDateIdx: uniqueIndex("doctor_schedule_exceptions_doctor_date_idx").on(table.doctorId, table.exceptionDate),
  };
});

// ============================================
// TIPOS TYPESCRIPT
// ============================================

export type User = typeof user.$inferSelect;
export type NewUser = typeof user.$inferInsert;
export type UserRole = typeof userRoles.$inferSelect;
export type NewUserRole = typeof userRoles.$inferInsert;
export type RegistrationRequest = typeof registrationRequests.$inferSelect;
export type NewRegistrationRequest = typeof registrationRequests.$inferInsert;
export type Consultory = typeof consultories.$inferSelect;
export type MedicalSpecialty = typeof medicalSpecialties.$inferSelect;
export type AssistantDoctorRelation = typeof assistantDoctorRelations.$inferSelect;
export type GuardianPatientRelation = typeof guardianPatientRelations.$inferSelect;
export type AssociationCode = typeof associationCodes.$inferSelect;
export type Notification = typeof notifications.$inferSelect;
export type DoctorSchedule = typeof doctorSchedules.$inferSelect;
export type NewDoctorSchedule = typeof doctorSchedules.$inferInsert;
export type DoctorScheduleException = typeof doctorScheduleExceptions.$inferSelect;
export type NewDoctorScheduleException = typeof doctorScheduleExceptions.$inferInsert;

// Tipos para nuevos catálogos
export type Currency = typeof currencies.$inferSelect;
export type NewCurrency = typeof currencies.$inferInsert;
export type ActivityType = typeof activityTypes.$inferSelect;
export type NewActivityType = typeof activityTypes.$inferInsert;
export type MediaSource = typeof mediaSources.$inferSelect;
export type NewMediaSource = typeof mediaSources.$inferInsert;
export type EducationLevel = typeof educationLevels.$inferSelect;
export type NewEducationLevel = typeof educationLevels.$inferInsert;
export type DocumentType = typeof documentTypes.$inferSelect;
export type NewDocumentType = typeof documentTypes.$inferInsert;
export type MaritalStatus = typeof maritalStatus.$inferSelect;
export type NewMaritalStatus = typeof maritalStatus.$inferInsert;
export type PathologicalHistory = typeof pathologicalHistory.$inferSelect;
export type NewPathologicalHistory = typeof pathologicalHistory.$inferInsert;
export type NonPathologicalHistory = typeof nonPathologicalHistory.$inferSelect;
export type NewNonPathologicalHistory = typeof nonPathologicalHistory.$inferInsert;
export type Company = typeof companies.$inferSelect;
export type NewCompany = typeof companies.$inferInsert;
export type MedicalService = typeof medicalServices.$inferSelect;
export type NewMedicalService = typeof medicalServices.$inferInsert;
export type ServiceTag = typeof serviceTags.$inferSelect;
export type NewServiceTag = typeof serviceTags.$inferInsert;
export type MedicalServiceTag = typeof medicalServiceTags.$inferSelect;
export type NewMedicalServiceTag = typeof medicalServiceTags.$inferInsert;
export type DoctorServicePrice = typeof doctorServicePrices.$inferSelect;
export type NewDoctorServicePrice = typeof doctorServicePrices.$inferInsert;

// ============================================
// NUEVOS CATÁLOGOS: MEDICAMENTOS, SÍNTOMAS, RELIGIONES
// ============================================

// 1. RELIGIONES - Catálogo demográfico
export const religions = pgTable("religions", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Católica", "Evangélica", "Otra", "Ninguna"
  category: text("category").notNull(), // "Cristiana", "Otra", "No religiosa"
  description: text("description"), // Descripción opcional
  order: integer("order").default(0), // Orden de visualización
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    nameIdx: index("religions_name_idx").on(table.name),
    categoryIdx: index("religions_category_idx").on(table.category),
    activeIdx: index("religions_active_idx").on(table.isActive),
  };
});

// 2. SÍNTOMAS/PADECIMIENTOS - Catálogo médico básico
export const symptoms = pgTable("symptoms", {
  id: text("id").primaryKey(),
  name: text("name").notNull(), // "Dolor de cabeza", "Fiebre", "Náuseas"
  category: text("category").notNull(), // "Síntomas", "Signos", "Síndromes"
  subcategory: text("subcategory"), // Subcategoría específica
  icdCode: text("icdCode"), // Código CIE-11 si aplica
  isSymptom: boolean("isSymptom").default(true), // true = síntoma, false = signo
  description: text("description"), // Descripción médica
  commonCauses: jsonb("commonCauses").$type<string[]>().default([]), // Causas comunes
  severity: text("severity").default("low"), // "low", "medium", "high"
  bodySystem: text("bodySystem"), // Sistema corporal afectado
  order: integer("order").default(0),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    nameIdx: index("symptoms_name_idx").on(table.name),
    categoryIdx: index("symptoms_category_idx").on(table.category),
    icdIdx: index("symptoms_icd_idx").on(table.icdCode),
    activeIdx: index("symptoms_active_idx").on(table.isActive),
  };
});

// 3. MEDICAMENTOS - Catálogo farmacológico completo
export const medications = pgTable("medications", {
  id: text("id").primaryKey(),
  
  // Identificación del medicamento
  name: text("name").notNull(), // Nombre principal
  genericName: text("genericName").notNull(), // Nombre genérico
  brandName: text("brandName"), // Nombre comercial
  activeIngredient: text("activeIngredient").notNull(), // Principio activo
  
  // Forma farmacéutica y concentración
  dosageForm: text("dosageForm").notNull(), // "Tableta", "Jarabe", "Inyección", "Cápsula"
  strength: text("strength").notNull(), // "500mg", "250mg/5ml", "10mg/ml"
  concentration: text("concentration"), // Concentración específica
  
  // Clasificación médica
  therapeuticClass: text("therapeuticClass").notNull(), // Clase terapéutica
  pharmacologicalGroup: text("pharmacologicalGroup"), // Grupo farmacológico
  atcCode: text("atcCode"), // Código ATC (Anatomical Therapeutic Chemical)
  
  // Información médica
  indication: text("indication"), // Indicaciones principales
  contraindications: jsonb("contraindications").$type<string[]>().default([]), // Contraindicaciones
  sideEffects: jsonb("sideEffects").$type<string[]>().default([]), // Efectos secundarios
  dosageInstructions: text("dosageInstructions"), // Instrucciones de dosificación
  warnings: jsonb("warnings").$type<string[]>().default([]), // Advertencias especiales
  
  // Control y regulación
  requiresPrescription: boolean("requiresPrescription").default(false),
  isControlled: boolean("isControlled").default(false), // Medicamento controlado
  controlledCategory: text("controlledCategory"), // Categoría de control si aplica
  
  // Información comercial
  manufacturer: text("manufacturer"), // Laboratorio fabricante
  barcode: text("barcode"), // Código de barras
  ndc: text("ndc"), // National Drug Code (si aplica)
  
  // Presentación y almacenamiento
  presentation: text("presentation"), // "Caja x 20 tabletas", "Frasco x 100ml"
  storageConditions: text("storageConditions"), // Condiciones de almacenamiento
  shelfLife: text("shelfLife"), // Vida útil
  
  // Campos de catálogo estándar
  description: text("description"), // Descripción general
  notes: text("notes"), // Notas adicionales
  order: integer("order").default(0),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    nameIdx: index("medications_name_idx").on(table.name),
    genericIdx: index("medications_generic_idx").on(table.genericName),
    activeIngredientIdx: index("medications_active_ingredient_idx").on(table.activeIngredient),
    therapeuticIdx: index("medications_therapeutic_idx").on(table.therapeuticClass),
    controlledIdx: index("medications_controlled_idx").on(table.isControlled),
    activeIdx: index("medications_active_idx").on(table.isActive),
  };
});

// ============================================
// TIPOS TYPESCRIPT PARA NUEVOS CATÁLOGOS
// ============================================

export type Religion = typeof religions.$inferSelect;
export type NewReligion = typeof religions.$inferInsert;
export type Symptom = typeof symptoms.$inferSelect;
export type NewSymptom = typeof symptoms.$inferInsert;
export type Medication = typeof medications.$inferSelect;
export type NewMedication = typeof medications.$inferInsert;

// ============================================
// SISTEMA DE AGENDA MÉDICA - FASE 1
// ============================================

// 1. CITAS MÉDICAS - Tabla principal de citas
export const appointments = pgTable("appointments", {
  // Identificación
  id: text("id").primaryKey(),
  
  // Información básica de la cita
  title: text("title").notNull(), // "Consulta General - Juan Pérez"
  description: text("description"), // Notas adicionales
  
  // Participantes
  doctorId: text("doctorId").notNull().references(() => user.id), // Doctor asignado
  patientId: text("patientId").references(() => user.id), // Paciente (opcional para actividades sin paciente)
  consultoryId: text("consultoryId").notNull().references(() => consultories.id), // Consultorio
  
  // Información médica
  serviceId: text("serviceId").references(() => medicalServices.id), // Servicio médico
  activityTypeId: text("activityTypeId").references(() => activityTypes.id), // Tipo de actividad
  chiefComplaint: text("chiefComplaint"), // Motivo de consulta
  
  // Programación temporal
  scheduledDate: timestamp("scheduledDate").notNull(), // Fecha programada
  startTime: timestamp("startTime").notNull(), // Hora de inicio
  endTime: timestamp("endTime").notNull(), // Hora de fin
  duration: integer("duration").notNull(), // Duración en minutos
  
  // Estado de la cita
  status: text("status").notNull().default("scheduled"), // scheduled, confirmed, checked_in, in_progress, completed, cancelled, no_show
  confirmationStatus: text("confirmationStatus").default("pending"), // pending, confirmed, reminded
  
  // Información de asistencia
  checkedInAt: timestamp("checkedInAt"), // Hora de llegada
  checkedInBy: text("checkedInBy").references(() => user.id), // Quién registró la llegada
  startedAt: timestamp("startedAt"), // Hora de inicio real
  completedAt: timestamp("completedAt"), // Hora de finalización
  noShowReason: text("noShowReason"), // Motivo de no presentarse
  
  // Información financiera
  estimatedPrice: numeric("estimatedPrice", { precision: 10, scale: 2 }), // Precio estimado
  finalPrice: numeric("finalPrice", { precision: 10, scale: 2 }), // Precio final
  currency: text("currency").default("GTQ"),
  paymentStatus: text("paymentStatus").default("pending"), // pending, partial, paid, refunded
  
  // Notas y observaciones
  doctorNotes: text("doctorNotes"), // Notas del doctor
  adminNotes: text("adminNotes"), // Notas administrativas
  cancellationReason: text("cancellationReason"), // Razón de cancelación
  
  // Información de seguimiento
  isFollowUp: boolean("isFollowUp").default(false), // Es cita de seguimiento
  parentAppointmentId: text("parentAppointmentId").references(() => appointments.id), // Cita padre
  
  // Configuración
  isEmergency: boolean("isEmergency").default(false), // Cita de emergencia
  requiresReminder: boolean("requiresReminder").default(true), // Enviar recordatorios
  
  // Pre-checkin y notificaciones
  preCheckinSent: boolean("preCheckinSent").default(false), // Si se envió el link de pre-checkin
  preCheckinCompleted: boolean("preCheckinCompleted").default(false), // Si se completó el pre-checkin
  preCheckinToken: text("preCheckinToken"), // Token único para acceder al pre-checkin
  preCheckinCompletedAt: timestamp("preCheckinCompletedAt"), // Fecha y hora de completado
  preCheckinCompletedBy: text("preCheckinCompletedBy"), // ID del usuario que completó (puede ser guardián)
  preCheckinData: jsonb("preCheckinData"), // Datos completos del formulario de pre-checkin
  reminderSent48h: boolean("reminderSent48h").default(false), // Si se envió recordatorio 48h antes
  reminderSent24h: boolean("reminderSent24h").default(false), // Si se envió recordatorio 24h antes
  invitationSent: boolean("invitationSent").default(false), // Si se envió invitación de activación de cuenta
  
  // Sistema de códigos cortos para confirmación
  shortCode: text("shortCode").unique(), // Código corto para confirmación (ej: SGC1234567)
  emailCaptured: text("emailCaptured"), // Email capturado durante confirmación
  confirmedAt: timestamp("confirmedAt"), // Fecha y hora de confirmación
  confirmedVia: text("confirmedVia"), // Método de confirmación (web, phone, etc.)
  
  // Auditoría
  createdBy: text("createdBy").notNull().references(() => user.id), // Quien creó la cita
  updatedBy: text("updatedBy").references(() => user.id), // Quien modificó por última vez
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorIdx: index("appointments_doctor_idx").on(table.doctorId),
    patientIdx: index("appointments_patient_idx").on(table.patientId),
    consultoryIdx: index("appointments_consultory_idx").on(table.consultoryId),
    statusIdx: index("appointments_status_idx").on(table.status),
    dateIdx: index("appointments_date_idx").on(table.scheduledDate),
    timeRangeIdx: index("appointments_time_range_idx").on(table.startTime, table.endTime),
    emergencyIdx: index("appointments_emergency_idx").on(table.isEmergency),
    followUpIdx: index("appointments_follow_up_idx").on(table.isFollowUp),
    parentIdx: index("appointments_parent_idx").on(table.parentAppointmentId),
    shortCodeIdx: uniqueIndex("appointments_shortcode_idx").on(table.shortCode),
  };
});

// 2. HORARIOS DISPONIBLES - Slots de tiempo para citas
export const appointmentSlots = pgTable("appointment_slots", {
  // Identificación
  id: text("id").primaryKey(),
  
  // Asociación
  doctorId: text("doctorId").notNull().references(() => user.id), // Doctor
  consultoryId: text("consultoryId").notNull().references(() => consultories.id), // Consultorio
  
  // Información temporal
  date: timestamp("date").notNull(), // Fecha del slot
  startTime: timestamp("startTime").notNull(), // Hora de inicio
  endTime: timestamp("endTime").notNull(), // Hora de fin
  duration: integer("duration").notNull(), // Duración en minutos
  
  // Estado del slot
  isAvailable: boolean("isAvailable").default(true), // Disponible para agendar
  isBlocked: boolean("isBlocked").default(false), // Bloqueado por el doctor
  blockReason: text("blockReason"), // Razón del bloqueo
  
  // Configuración
  isRecurring: boolean("isRecurring").default(false), // Slot recurrente
  recurrencePattern: text("recurrencePattern"), // Patrón de recurrencia
  maxAppointments: integer("maxAppointments").default(1), // Máximo de citas simultáneas
  
  // Tipos de cita permitidos
  allowedServiceTypes: jsonb("allowedServiceTypes").$type<string[]>().default([]), // Tipos de servicio permitidos
  isEmergencyOnly: boolean("isEmergencyOnly").default(false), // Solo para emergencias
  
  // Auditoría
  createdBy: text("createdBy").notNull().references(() => user.id),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorDateIdx: index("appointment_slots_doctor_date_idx").on(table.doctorId, table.date),
    consultoryDateIdx: index("appointment_slots_consultory_date_idx").on(table.consultoryId, table.date),
    availableIdx: index("appointment_slots_available_idx").on(table.isAvailable),
    timeRangeIdx: index("appointment_slots_time_range_idx").on(table.startTime, table.endTime),
    emergencyIdx: index("appointment_slots_emergency_idx").on(table.isEmergencyOnly),
  };
});

// ============================================
// RELACIONES PARA AGENDA MÉDICA
// ============================================

export const appointmentsRelations = relations(appointments, ({ one, many }) => ({
  // Participantes
  doctor: one(user, { fields: [appointments.doctorId], references: [user.id], relationName: "doctorAppointments" }),
  patient: one(user, { fields: [appointments.patientId], references: [user.id], relationName: "patientAppointments" }),
  consultory: one(consultories, { fields: [appointments.consultoryId], references: [consultories.id] }),
  
  // Información médica
  service: one(medicalServices, { fields: [appointments.serviceId], references: [medicalServices.id] }),
  activityType: one(activityTypes, { fields: [appointments.activityTypeId], references: [activityTypes.id] }),
  
  // Seguimiento
  parentAppointment: one(appointments, { fields: [appointments.parentAppointmentId], references: [appointments.id], relationName: "followUpAppointments" }),
  followUpAppointments: many(appointments, { relationName: "followUpAppointments" }),
  
  // Auditoría
  createdBy: one(user, { fields: [appointments.createdBy], references: [user.id], relationName: "createdAppointments" }),
  updatedBy: one(user, { fields: [appointments.updatedBy], references: [user.id], relationName: "updatedAppointments" }),
}));

export const appointmentSlotsRelations = relations(appointmentSlots, ({ one }) => ({
  doctor: one(user, { fields: [appointmentSlots.doctorId], references: [user.id] }),
  consultory: one(consultories, { fields: [appointmentSlots.consultoryId], references: [consultories.id] }),
  createdBy: one(user, { fields: [appointmentSlots.createdBy], references: [user.id] }),
}));

// ============================================
// TIPOS TYPESCRIPT PARA AGENDA MÉDICA
// ============================================

export type Appointment = typeof appointments.$inferSelect;
export type NewAppointment = typeof appointments.$inferInsert;
export type AppointmentSlot = typeof appointmentSlots.$inferSelect;
export type NewAppointmentSlot = typeof appointmentSlots.$inferInsert;

// ============================================
// RELACIONES PARA HORARIOS MÉDICOS
// ============================================

export const doctorSchedulesRelations = relations(doctorSchedules, ({ one }) => ({
  doctor: one(user, { fields: [doctorSchedules.doctorId], references: [user.id] }),
  consultory: one(consultories, { fields: [doctorSchedules.consultoryId], references: [consultories.id] }),
}));

export const doctorScheduleExceptionsRelations = relations(doctorScheduleExceptions, ({ one }) => ({
  doctor: one(user, { fields: [doctorScheduleExceptions.doctorId], references: [user.id] }),
  consultory: one(consultories, { fields: [doctorScheduleExceptions.consultoryId], references: [consultories.id] }),
}));

// ============================================
// PLANTILLAS DE RECETAS Y MEDICAMENTOS FAVORITOS
// ============================================

// Tabla para plantillas de recetas del doctor
export const prescriptionTemplates = pgTable("prescription_templates", {
  id: text("id").primaryKey(),
  doctorId: text("doctorId").notNull().references(() => user.id, { onDelete: "cascade" }),
  name: text("name").notNull(),
  content: text("content").notNull(),
  category: text("category"),
  isActive: boolean("isActive").default(true),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorIdx: index("prescription_templates_doctor_idx").on(table.doctorId),
    activeIdx: index("prescription_templates_active_idx").on(table.isActive),
  };
});

// Tabla para medicamentos favoritos del doctor
export const doctorFavoriteMedications = pgTable("doctor_favorite_medications", {
  id: text("id").primaryKey(),
  doctorId: text("doctorId").notNull().references(() => user.id, { onDelete: "cascade" }),
  medicationId: text("medicationId").notNull().references(() => medications.id, { onDelete: "cascade" }),
  dosage: text("dosage"),
  frequency: text("frequency"),
  notes: text("notes"),
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    doctorMedicationIdx: uniqueIndex("doctor_medication_idx").on(table.doctorId, table.medicationId),
    doctorIdx: index("doctor_favorite_medications_doctor_idx").on(table.doctorId),
    medicationIdx: index("doctor_favorite_medications_medication_idx").on(table.medicationId),
  };
});

// ============================================
// RELACIONES PARA PLANTILLAS Y MEDICAMENTOS
// ============================================

export const prescriptionTemplatesRelations = relations(prescriptionTemplates, ({ one }) => ({
  doctor: one(user, { fields: [prescriptionTemplates.doctorId], references: [user.id] }),
}));

export const doctorFavoriteMedicationsRelations = relations(doctorFavoriteMedications, ({ one }) => ({
  doctor: one(user, { fields: [doctorFavoriteMedications.doctorId], references: [user.id] }),
  medication: one(medications, { fields: [doctorFavoriteMedications.medicationId], references: [medications.id] }),
}));

// ============================================
// TIPOS TYPESCRIPT PARA PLANTILLAS Y MEDICAMENTOS
// ============================================

export type PrescriptionTemplate = typeof prescriptionTemplates.$inferSelect;
export type NewPrescriptionTemplate = typeof prescriptionTemplates.$inferInsert;
export type DoctorFavoriteMedication = typeof doctorFavoriteMedications.$inferSelect;
export type NewDoctorFavoriteMedication = typeof doctorFavoriteMedications.$inferInsert;

// ============================================
// SISTEMA DE EXPEDIENTES CLÍNICOS PEDIÁTRICOS
// ============================================

// 1. EXPEDIENTES CLÍNICOS PRINCIPALES
export const medicalRecords = pgTable("medical_records", {
  id: text("id").primaryKey(),
  patientId: text("patientId").notNull().references(() => user.id, { onDelete: "cascade" }),
  consultoryId: text("consultoryId").notNull().references(() => consultories.id),
  primaryDoctorId: text("primaryDoctorId").notNull().references(() => user.id),
  
  // Información del expediente
  recordNumber: text("recordNumber").unique().notNull(),
  openDate: timestamp("openDate").defaultNow().notNull(),
  status: text("status").default("active"), // active, inactive, transferred, archived
  
  // Datos demográficos resumidos (cache del paciente)
  patientSummary: jsonb("patientSummary"), // fullName, dateOfBirth, age, gender, bloodType, allergies
  
  // Información demográfica del paciente
  demographics: jsonb("demographics").$type<{
    religion?: string;
    occupation?: string;
    maritalStatus?: string;
    educationLevel?: string;
    ethnicity?: string;
    nationality?: string;
    languagePreference?: string;
  }>().default({}),
  
  // Información administrativa
  administrative: jsonb("administrative").$type<{
    mediaSource?: string;
    company?: string;
    preferredConsultory?: string;
    insuranceProvider?: string;
    insuranceNumber?: string;
    referredBy?: string;
  }>().default({}),
  
  // Contacto de emergencia
  emergencyContact: jsonb("emergency_contact").$type<{
    name?: string;
    relationship?: string;
    phone?: string;
    email?: string;
    address?: string;
  }>().default({}),
  
  // Configuración
  isMinor: boolean("isMinor").default(false),
  guardianInfo: jsonb("guardianInfo"), // nombre, parentesco, teléfono, email
  
  // Información médica del paciente
  selectedSymptoms: jsonb("selectedSymptoms").$type<string[]>().default([]), // array de IDs de síntomas
  pathologicalHistory: jsonb("pathologicalHistory").$type<string[]>().default([]), // array de IDs de antecedentes patológicos
  nonPathologicalHistory: jsonb("nonPathologicalHistory").$type<string[]>().default([]), // array de IDs de antecedentes no patológicos
  
  // Metadata de acceso
  lastAccessDate: timestamp("lastAccessDate"),
  totalConsultations: integer("totalConsultations").default(0),
  
  // Auditoría
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
  createdBy: text("createdBy").notNull().references(() => user.id),
  updatedBy: text("updatedBy").notNull().references(() => user.id),
}, (table) => {
  return {
    patientIdx: index("medical_records_patient_idx").on(table.patientId),
    doctorIdx: index("medical_records_doctor_idx").on(table.primaryDoctorId),
    consultoryIdx: index("medical_records_consultory_idx").on(table.consultoryId),
    statusIdx: index("medical_records_status_idx").on(table.status),
    recordNumberIdx: uniqueIndex("medical_records_number_idx").on(table.recordNumber),
    createdIdx: index("medical_records_created_idx").on(table.createdAt),
  };
});

// 2. CONSULTAS MÉDICAS (HISTORIALES)
export const medicalConsultations = pgTable("medical_consultations", {
  id: text("id").primaryKey(),
  medicalRecordId: text("medicalRecordId").notNull().references(() => medicalRecords.id, { onDelete: "cascade" }),
  appointmentId: text("appointmentId").references(() => appointments.id), // referencia opcional a cita
  doctorId: text("doctorId").notNull().references(() => user.id),
  consultoryId: text("consultoryId").notNull().references(() => consultories.id),
  
  // Datos de la consulta
  consultationDate: timestamp("consultationDate").notNull(),
  services: jsonb("services").$type<ConsultationService[]>().default([]), // servicios múltiples realizados
  chiefComplaint: text("chiefComplaint").notNull(), // motivo principal
  currentIllness: text("currentIllness"), // enfermedad actual
  
  // Examen físico y signos vitales (JSON por flexibilidad)
  vitalSigns: jsonb("vitalSigns"), // peso, talla, temperatura, FC, FR, PA, satO2, IMC calculado
  physicalExam: jsonb("physicalExam"), // examen por sistemas
  
  // Diagnósticos y tratamiento
  diagnoses: jsonb("diagnoses"), // array de diagnósticos
  treatment: jsonb("treatment"), // plan de tratamiento
  prescriptions: jsonb("prescriptions"), // recetas/medicamentos
  
  // Seguimiento
  recommendations: text("recommendations"),
  nextAppointment: timestamp("nextAppointment"),
  followUpInstructions: text("followUpInstructions"),
  
  // Adjuntos
  attachments: jsonb("attachments"), // URLs de documentos/imágenes
  
  // Estado de la consulta
  status: text("status").default("completed"), // draft, completed, reviewed, billed
  
  // Auditoría
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
  createdBy: text("createdBy").notNull().references(() => user.id),
  updatedBy: text("updatedBy").notNull().references(() => user.id),
}, (table) => {
  return {
    recordIdx: index("medical_consultations_record_idx").on(table.medicalRecordId),
    doctorIdx: index("medical_consultations_doctor_idx").on(table.doctorId),
    appointmentIdx: index("medical_consultations_appointment_idx").on(table.appointmentId),
    dateIdx: index("medical_consultations_date_idx").on(table.consultationDate),
    statusIdx: index("medical_consultations_status_idx").on(table.status),
  };
});

// 3. ANTECEDENTES MÉDICOS (INTEGRADO CON CATÁLOGOS EXISTENTES)
export const patientMedicalHistory = pgTable("patient_medical_history", {
  id: text("id").primaryKey(),
  medicalRecordId: text("medicalRecordId").notNull().references(() => medicalRecords.id, { onDelete: "cascade" }),
  
  // Antecedentes patológicos (del catálogo existente)
  pathologicalHistory: jsonb("pathologicalHistory"), // [{historyId, historyName, diagnosedDate, severity, status, notes, treatingDoctor}]
  
  // Antecedentes no patológicos (del catálogo existente)  
  nonPathologicalHistory: jsonb("nonPathologicalHistory"), // [{historyId, historyName, value, startDate, endDate, notes}]
  
  // Antecedentes familiares
  familyHistory: jsonb("familyHistory"), // [{relationship, condition, age, notes}]
  
  // Alergias (usando catálogo medications + otros)
  allergies: jsonb("allergies"), // [{allergen, type, reaction, severity, diagnosedDate, notes}]
  
  // Hospitalizaciones previas
  hospitalizations: jsonb("hospitalizations"), // [{date, hospital, reason, duration, complications, notes}]
  
  // Cirugías previas
  surgeries: jsonb("surgeries"), // [{date, procedure, surgeon, hospital, complications, notes}]
  
  // Vacunación (usando datos específicos)
  vaccinations: jsonb("vaccinations"), // [{vaccine, date, doseNumber, lot, administrator, reactions, nextDueDate}]
  
  // Auditoría
  createdAt: timestamp("createdAt").defaultNow(),
  updatedAt: timestamp("updatedAt").defaultNow(),
  updatedBy: text("updatedBy").notNull().references(() => user.id),
}, (table) => {
  return {
    recordIdx: uniqueIndex("patient_medical_history_record_idx").on(table.medicalRecordId),
    updatedIdx: index("patient_medical_history_updated_idx").on(table.updatedAt),
  };
});

// 4. DOCUMENTOS MÉDICOS
export const medicalDocuments = pgTable("medical_documents", {
  id: text("id").primaryKey(),
  medicalRecordId: text("medicalRecordId").notNull().references(() => medicalRecords.id, { onDelete: "cascade" }),
  consultationId: text("consultationId").references(() => medicalConsultations.id), // si pertenece a consulta específica
  
  // Información del documento
  fileName: text("fileName").notNull(),
  originalName: text("originalName").notNull(),
  fileType: text("fileType").notNull(), // PDF, JPG, PNG, DICOM, etc.
  fileSize: integer("fileSize").notNull(), // bytes
  filePath: text("filePath").notNull(), // ruta en el storage
  
  // Metadata del documento
  documentType: text("documentType").notNull(), // lab_result, imaging, prescription, certificate, referral, consent, other
  title: text("title").notNull(),
  description: text("description"),
  documentDate: timestamp("documentDate").notNull(), // fecha del documento (no upload)
  
  // Información médica
  relatedDiagnosis: text("relatedDiagnosis"),
  laboratory: text("laboratory"), // lab que emitió
  physician: text("physician"), // médico que solicitó
  
  // Control de acceso
  visibility: text("visibility").default("doctor_only"), // doctor_only, medical_staff, patient_visible
  isConfidential: boolean("isConfidential").default(false),
  
  // Estado
  status: text("status").default("pending_review"), // pending_review, reviewed, filed, archived
  reviewedBy: text("reviewedBy").references(() => user.id),
  reviewedDate: timestamp("reviewedDate"),
  reviewNotes: text("reviewNotes"),
  
  // Auditoría
  uploadedAt: timestamp("uploadedAt").defaultNow(),
  uploadedBy: text("uploadedBy").notNull().references(() => user.id),
  updatedAt: timestamp("updatedAt").defaultNow(),
}, (table) => {
  return {
    recordIdx: index("medical_documents_record_idx").on(table.medicalRecordId),
    consultationIdx: index("medical_documents_consultation_idx").on(table.consultationId),
    typeIdx: index("medical_documents_type_idx").on(table.documentType),
    statusIdx: index("medical_documents_status_idx").on(table.status),
    uploadedIdx: index("medical_documents_uploaded_idx").on(table.uploadedAt),
    documentDateIdx: index("medical_documents_date_idx").on(table.documentDate),
  };
});

// ============================================
// RELACIONES PARA EXPEDIENTES CLÍNICOS
// ============================================

export const medicalRecordsRelations = relations(medicalRecords, ({ one, many }) => ({
  patient: one(user, { fields: [medicalRecords.patientId], references: [user.id], relationName: "patient" }),
  primaryDoctor: one(user, { fields: [medicalRecords.primaryDoctorId], references: [user.id], relationName: "primaryDoctor" }),
  consultory: one(consultories, { fields: [medicalRecords.consultoryId], references: [consultories.id] }),
  createdByUser: one(user, { fields: [medicalRecords.createdBy], references: [user.id], relationName: "createdBy" }),
  updatedByUser: one(user, { fields: [medicalRecords.updatedBy], references: [user.id], relationName: "updatedBy" }),
  consultations: many(medicalConsultations),
  medicalHistory: one(patientMedicalHistory),
  documents: many(medicalDocuments),
}));

export const medicalConsultationsRelations = relations(medicalConsultations, ({ one }) => ({
  medicalRecord: one(medicalRecords, { fields: [medicalConsultations.medicalRecordId], references: [medicalRecords.id] }),
  appointment: one(appointments, { fields: [medicalConsultations.appointmentId], references: [appointments.id] }),
  doctor: one(user, { fields: [medicalConsultations.doctorId], references: [user.id], relationName: "doctor" }),
  consultory: one(consultories, { fields: [medicalConsultations.consultoryId], references: [consultories.id] }),
  createdByUser: one(user, { fields: [medicalConsultations.createdBy], references: [user.id], relationName: "createdBy" }),
  updatedByUser: one(user, { fields: [medicalConsultations.updatedBy], references: [user.id], relationName: "updatedBy" }),
}));

export const patientMedicalHistoryRelations = relations(patientMedicalHistory, ({ one }) => ({
  medicalRecord: one(medicalRecords, { fields: [patientMedicalHistory.medicalRecordId], references: [medicalRecords.id] }),
  updatedByUser: one(user, { fields: [patientMedicalHistory.updatedBy], references: [user.id] }),
}));

export const medicalDocumentsRelations = relations(medicalDocuments, ({ one }) => ({
  medicalRecord: one(medicalRecords, { fields: [medicalDocuments.medicalRecordId], references: [medicalRecords.id] }),
  consultation: one(medicalConsultations, { fields: [medicalDocuments.consultationId], references: [medicalConsultations.id] }),
  uploadedByUser: one(user, { fields: [medicalDocuments.uploadedBy], references: [user.id], relationName: "uploadedBy" }),
  reviewedByUser: one(user, { fields: [medicalDocuments.reviewedBy], references: [user.id], relationName: "reviewedBy" }),
}));

// ============================================
// CONFIGURACIÓN REGIONAL DEL SISTEMA
// ============================================

// Sistema de configuración global del sistema
export const systemConfig = pgTable("system_config", {
  id: integer("id").primaryKey(),
  key: text("key").notNull().unique(),
  value: jsonb("value").notNull(),
  description: text("description"),
  category: text("category").default("general"),
  active: boolean("active").default(true),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
}, (table) => {
  return {
    keyIdx: uniqueIndex("system_config_key_idx").on(table.key),
    categoryIdx: index("system_config_category_idx").on(table.category),
    activeIdx: index("system_config_active_idx").on(table.active),
  };
});

// ============================================
// RELACIONES PARA CONFIGURACIÓN DEL SISTEMA
// ============================================

export const systemConfigRelations = relations(systemConfig, ({ many }) => ({
  // Futuras relaciones si es necesario
}));

// Tabla para invitaciones de pacientes
export const patientInvitations = pgTable("patient_invitations", {
  id: text("id").primaryKey(),
  patientUserId: text("patient_user_id").notNull().references(() => user.id, { onDelete: 'cascade' }),
  guardianEmail: text("guardian_email").notNull(),
  invitationToken: text("invitation_token").unique().notNull(),
  status: text("status").default("pending"), // 'pending', 'accepted', 'expired'
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  acceptedAt: timestamp("accepted_at"),
}, (table) => {
  return {
    tokenIdx: index("patient_invitations_token_idx").on(table.invitationToken),
    statusIdx: index("patient_invitations_status_idx").on(table.status),
    guardianEmailIdx: index("patient_invitations_guardian_email_idx").on(table.guardianEmail),
  };
});

export const patientInvitationsRelations = relations(patientInvitations, ({ one }) => ({
  patient: one(user, {
    fields: [patientInvitations.patientUserId],
    references: [user.id],
  }),
}));

// 15. LOG DE EVENTOS DE CITAS - Sistema de auditoría completo
export const appointmentLogs = pgTable("appointment_logs", {
  id: text("id").primaryKey(),
  appointmentId: text("appointment_id").notNull().references(() => appointments.id, { onDelete: 'cascade' }),
  
  // Información del evento
  eventType: text("event_type").notNull(), // 'created', 'confirmed', 'cancelled', 'completed', 'email_sent', 'status_changed', etc.
  eventCategory: text("event_category").notNull(), // 'system', 'user_action', 'email', 'schedule', 'payment', 'medical'
  
  // Detalles del evento
  title: text("title").notNull(), // "Cita creada", "Email de confirmación enviado", etc.
  description: text("description"), // Descripción detallada
  metadata: jsonb("metadata"), // Datos adicionales específicos del evento
  
  // Usuario que ejecutó la acción (si aplica)
  triggeredBy: text("triggered_by").references(() => user.id),
  triggeredByRole: text("triggered_by_role"), // 'patient', 'doctor', 'admin', 'system'
  
  // Estado antes y después (para cambios)
  previousState: jsonb("previous_state"),
  newState: jsonb("new_state"),
  
  // Información de emails (si aplica)
  emailType: text("email_type"), // 'appointment_created', 'reminder', 'cancellation', etc.
  emailRecipient: text("email_recipient"),
  emailStatus: text("email_status"), // 'sent', 'failed', 'delivered', 'opened'
  
  // Metadatos del sistema
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  
  // Timestamps
  createdAt: timestamp("created_at").defaultNow().notNull(),
}, (table) => {
  return {
    appointmentIdx: index("appointment_logs_appointment_idx").on(table.appointmentId),
    eventTypeIdx: index("appointment_logs_event_type_idx").on(table.eventType),
    eventCategoryIdx: index("appointment_logs_event_category_idx").on(table.eventCategory),
    createdAtIdx: index("appointment_logs_created_at_idx").on(table.createdAt),
    triggeredByIdx: index("appointment_logs_triggered_by_idx").on(table.triggeredBy),
  };
});

export const appointmentLogsRelations = relations(appointmentLogs, ({ one }) => ({
  appointment: one(appointments, {
    fields: [appointmentLogs.appointmentId],
    references: [appointments.id],
  }),
  triggeredByUser: one(user, {
    fields: [appointmentLogs.triggeredBy],
    references: [user.id],
  }),
}));

// ============================================
// TIPOS TYPESCRIPT PARA CONFIGURACIÓN DEL SISTEMA
// ============================================

export type SystemConfig = typeof systemConfig.$inferSelect;
export type NewSystemConfig = typeof systemConfig.$inferInsert;

// ============================================
// TIPOS TYPESCRIPT PARA EXPEDIENTES CLÍNICOS
// ============================================

export type MedicalRecord = typeof medicalRecords.$inferSelect;
export type NewMedicalRecord = typeof medicalRecords.$inferInsert;
export type MedicalConsultation = typeof medicalConsultations.$inferSelect;
export type NewMedicalConsultation = typeof medicalConsultations.$inferInsert;
export type PatientMedicalHistory = typeof patientMedicalHistory.$inferSelect;
export type NewPatientMedicalHistory = typeof patientMedicalHistory.$inferInsert;
export type MedicalDocument = typeof medicalDocuments.$inferSelect;
export type NewMedicalDocument = typeof medicalDocuments.$inferInsert;

// ============================================
// TIPOS ESPECÍFICOS PARA CONSULTAS
// ============================================

export interface ConsultationService {
  serviceId: string;
  serviceName?: string;
  category?: string;
  price?: number;
  duration?: number;
  performedBy?: string;
  addedAt: string;
  notes?: string;
}

// ============================================
// AUDITORÍA ADMINISTRATIVA
// ============================================

// Tabla para logs de auditoría de operaciones administrativas críticas
export const adminAuditLogs = pgTable("admin_audit_logs", {
  id: text("id").primaryKey(),
  action: text("action").notNull(), // DATABASE_RESET, USER_DELETION, BULK_OPERATION, etc.
  executedBy: text("executed_by").notNull().references(() => user.id),
  executedByName: text("executed_by_name").notNull(),
  timestamp: timestamp("timestamp").defaultNow().notNull(),
  details: jsonb("details"), // JSON con detalles específicos de la operación
  success: boolean("success").default(true).notNull(),
  errorMessage: text("error_message"),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => {
  return {
    actionIdx: index("admin_audit_logs_action_idx").on(table.action),
    executedByIdx: index("admin_audit_logs_executed_by_idx").on(table.executedBy),
    timestampIdx: index("admin_audit_logs_timestamp_idx").on(table.timestamp),
    successIdx: index("admin_audit_logs_success_idx").on(table.success),
  };
});

export const adminAuditLogsRelations = relations(adminAuditLogs, ({ one }) => ({
  executor: one(user, {
    fields: [adminAuditLogs.executedBy],
    references: [user.id],
  }),
}));

// ============================================
// TIPOS TYPESCRIPT PARA AUDITORÍA
// ============================================

export type AdminAuditLog = typeof adminAuditLogs.$inferSelect;
export type NewAdminAuditLog = typeof adminAuditLogs.$inferInsert;