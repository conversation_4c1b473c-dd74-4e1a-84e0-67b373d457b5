import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories, medicalServices, activityTypes } from '@/db/schema';
import { eq, and, or, sql } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Obtener cita específica
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;

    // Obtener información básica de la cita
    const result = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId));

    if (result.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = result[0];
    
    // Enriquecer con datos relacionados
    const enrichedAppointment = { ...appointment };
    
    // Obtener información del doctor
    if (appointment.doctorId) {
      const doctorData = await db
        .select({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
        })
        .from(user)
        .where(eq(user.id, appointment.doctorId))
        .limit(1);
      
      if (doctorData.length > 0) {
        enrichedAppointment.doctor = {
          id: appointment.doctorId,
          ...doctorData[0]
        };
      }
    }
    
    // Obtener información del paciente
    if (appointment.patientId) {
      const patientData = await db
        .select({
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
        })
        .from(user)
        .where(eq(user.id, appointment.patientId))
        .limit(1);
      
      if (patientData.length > 0) {
        enrichedAppointment.patient = {
          id: appointment.patientId,
          ...patientData[0]
        };
      }
    }
    
    // Obtener información del consultorio
    if (appointment.consultoryId) {
      const consultoryData = await db
        .select({
          name: consultories.name,
          type: consultories.type,
          floor: consultories.floor,
          building: consultories.building,
        })
        .from(consultories)
        .where(eq(consultories.id, appointment.consultoryId))
        .limit(1);
      
      if (consultoryData.length > 0) {
        enrichedAppointment.consultory = {
          id: appointment.consultoryId,
          ...consultoryData[0]
        };
      }
    }
    
    // Obtener información del servicio médico
    if (appointment.serviceId) {
      const serviceData = await db
        .select({
          name: medicalServices.name,
          category: medicalServices.category,
          basePrice: medicalServices.basePrice,
        })
        .from(medicalServices)
        .where(eq(medicalServices.id, appointment.serviceId))
        .limit(1);
      
      if (serviceData.length > 0) {
        enrichedAppointment.service = {
          id: appointment.serviceId,
          ...serviceData[0]
        };
      }
    }
    
    // Obtener información del tipo de actividad
    if (appointment.activityTypeId) {
      const activityData = await db
        .select({
          name: activityTypes.name,
          category: activityTypes.category,
          color: activityTypes.color,
          duration: activityTypes.duration,
          respectsSchedule: activityTypes.respectsSchedule,
          icon: activityTypes.icon,
        })
        .from(activityTypes)
        .where(eq(activityTypes.id, appointment.activityTypeId))
        .limit(1);
      
      if (activityData.length > 0) {
        enrichedAppointment.activityType = {
          id: appointment.activityTypeId,
          ...activityData[0]
        };
      }
    }

    // Obtener citas de seguimiento si es una cita padre
    const followUpAppointments = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        status: appointments.status,
      })
      .from(appointments)
      .where(eq(appointments.parentAppointmentId, appointmentId))
      .orderBy(appointments.scheduledDate);

    enrichedAppointment.followUpAppointments = followUpAppointments;

    return NextResponse.json({
      data: enrichedAppointment,
    });
  } catch (error) {
    console.error('Error fetching appointment:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// PUT - Actualizar cita
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;
    const body = await request.json();

    // Verificar que la cita existe
    const existingAppointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId));

    if (existingAppointment.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = existingAppointment[0];

    // Verificar permisos (solo el doctor, paciente o admin pueden modificar)
    const userRole = sessionClaims?.metadata?.role;
    const canModify = userRole === 'admin' || 
                     appointment.doctorId === userId || 
                     appointment.patientId === userId;

    if (!canModify) {
      return NextResponse.json({ error: 'No tienes permisos para modificar esta cita' }, { status: 403 });
    }

    // Validar cambios según el estado de la cita
    const currentStatus = appointment.status;
    const requestedChanges = Object.keys(body);
    
    // Campos que siempre se pueden modificar (metadatos)
    const alwaysAllowed = ['updatedBy', 'updatedAt'];
    
    // Definir campos permitidos por estado
    const allowedByStatus = {
      'scheduled': ['title', 'description', 'chiefComplaint', 'scheduledDate', 'startTime', 'endTime', 
                   'duration', 'doctorId', 'patientId', 'consultoryId', 'serviceId', 'activityTypeId',
                   'estimatedPrice', 'currency', 'isEmergency', 'requiresReminder', 'status', 'confirmationStatus'],
      
      'confirmed': ['title', 'description', 'chiefComplaint', 'doctorNotes', 'adminNotes', 
                   'estimatedPrice', 'currency', 'activityTypeId', 'status', 'cancellationReason'], // Permitir cancelación
      
      'in_progress': ['doctorNotes', 'duration', 'completedAt', 'status'], // Solo notas médicas y completar
      
      'completed': ['doctorNotes', 'adminNotes', 'finalPrice', 'paymentStatus'], // Solo información post-consulta
      
      'cancelled': ['cancellationReason', 'adminNotes'] // Solo motivo y notas administrativas
    };

    const allowedFields = [...alwaysAllowed, ...(allowedByStatus[currentStatus] || [])];
    
    // Verificar si se están haciendo cambios no permitidos
    const restrictedChanges = requestedChanges.filter(field => !allowedFields.includes(field));
    
    if (restrictedChanges.length > 0) {
      const statusMessages = {
        'confirmed': 'Una cita confirmada tiene cambios limitados. No se puede modificar horario ni participantes.',
        'in_progress': 'Una cita en progreso solo permite actualizar notas médicas y completarla.',
        'completed': 'Una cita completada solo permite actualizar información de facturación y notas.',
        'cancelled': 'Una cita cancelada solo permite actualizar el motivo de cancelación y notas administrativas.'
      };
      
      return NextResponse.json({ 
        error: `Cambios no permitidos para el estado '${currentStatus}': ${restrictedChanges.join(', ')}. ${statusMessages[currentStatus] || ''}` 
      }, { status: 400 });
    }

    // Preparar datos de actualización
    const updateData: any = {
      updatedBy: userId,
      updatedAt: new Date(),
    };

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        // Campos de fecha específicos
        if ((field === 'scheduledDate' || field === 'startTime' || field === 'endTime' || 
             field === 'checkedInAt' || field === 'startedAt' || field === 'completedAt') && body[field]) {
          try {
            updateData[field] = new Date(body[field]);
          } catch (error) {
            console.error(`Error parsing date for field ${field}:`, body[field], error);
            // Si no se puede parsear la fecha, mantener el valor original
            continue;
          }
        } else if (field === 'duration') {
          updateData[field] = parseInt(body[field]);
        } else if (field === 'estimatedPrice' || field === 'finalPrice') {
          updateData[field] = body[field] ? parseFloat(body[field]) : null;
        } else if (field === 'patientId' || field === 'serviceId' || field === 'activityTypeId') {
          updateData[field] = body[field] || null; // Convertir cadenas vacías a null
        } else {
          updateData[field] = body[field];
        }
      }
    }

    // Validar activityType y paciente si se están actualizando
    const finalActivityTypeId = updateData.activityTypeId || appointment.activityTypeId;
    if (finalActivityTypeId) {
      const activityTypeResult = await db
        .select()
        .from(activityTypes)
        .where(and(eq(activityTypes.id, finalActivityTypeId), eq(activityTypes.isActive, true)));

      if (activityTypeResult.length === 0) {
        return NextResponse.json({ error: 'Tipo de actividad no encontrado o inactivo' }, { status: 400 });
      }
      
      const activityType = activityTypeResult[0];
      
      // Validar paciente según el tipo de actividad
      const finalPatientId = updateData.patientId !== undefined ? updateData.patientId : appointment.patientId;
      
      if (activityType.requiresPatient && !finalPatientId) {
        return NextResponse.json({ 
          error: 'Esta actividad requiere seleccionar un paciente' 
        }, { status: 400 });
      }
      
      // Si el tipo de actividad no requiere paciente, limpiar campos médicos
      if (!activityType.requiresPatient) {
        updateData.patientId = null;
        updateData.serviceId = null;
        updateData.chiefComplaint = null;
        updateData.estimatedPrice = null;
      }
    }

    // Validaciones específicas
    if (updateData.startTime && updateData.endTime) {
      const startTime = new Date(updateData.startTime);
      const endTime = new Date(updateData.endTime);
      
      if (endTime <= startTime) {
        return NextResponse.json({ error: 'La hora de fin debe ser posterior a la hora de inicio' }, { status: 400 });
      }
    }

    // Si se está cambiando el horario, verificar conflictos
    if (updateData.startTime || updateData.endTime || updateData.scheduledDate) {
      const startTime = updateData.startTime ? new Date(updateData.startTime) : appointment.startTime;
      const endTime = updateData.endTime ? new Date(updateData.endTime) : appointment.endTime;
      const scheduledDate = updateData.scheduledDate ? new Date(updateData.scheduledDate) : appointment.scheduledDate;

      const conflictingAppointments = await db
        .select()
        .from(appointments)
        .where(
          and(
            eq(appointments.doctorId, appointment.doctorId),
            eq(appointments.scheduledDate, scheduledDate),
            // Excluir la cita actual
            sql`${appointments.id} != ${appointmentId}`,
            or(
              and(
                sql`${appointments.startTime} >= ${startTime}`,
                sql`${appointments.startTime} < ${endTime}`
              ),
              and(
                sql`${appointments.endTime} > ${startTime}`,
                sql`${appointments.endTime} <= ${endTime}`
              ),
              and(
                sql`${appointments.startTime} <= ${startTime}`,
                sql`${appointments.endTime} >= ${endTime}`
              )
            ),
            or(
              eq(appointments.status, 'scheduled'),
              eq(appointments.status, 'confirmed'),
              eq(appointments.status, 'in_progress')
            )
          )
        );

      if (conflictingAppointments.length > 0) {
        return NextResponse.json({ 
          error: 'El doctor ya tiene una cita programada en ese horario' 
        }, { status: 400 });
      }
    }

    // Actualizar la cita
    const result = await db
      .update(appointments)
      .set(updateData)
      .where(eq(appointments.id, appointmentId))
      .returning();

    return NextResponse.json({
      message: 'Cita actualizada exitosamente',
      data: result[0],
    });
  } catch (error) {
    console.error('Error updating appointment:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// DELETE - Cancelar/Eliminar cita
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId, sessionClaims } = await auth();
    
    console.log('🗑️ DELETE request received - User:', userId);
    
    if (!userId) {
      console.log('❌ DELETE: No user ID');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;
    const searchParams = request.nextUrl.searchParams;
    const reason = searchParams.get('reason') || 'Cancelada por el usuario';
    const hardDelete = searchParams.get('hardDelete') === 'true';
    
    console.log('🗑️ DELETE params:', { appointmentId, reason, hardDelete });

    // Verificar que la cita existe
    const existingAppointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId));

    if (existingAppointment.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = existingAppointment[0];

    // Verificar permisos - TEMPORAL: Más permisivo para debugging
    const userRole = sessionClaims?.metadata?.role;
    console.log('🔍 DELETE attempt - User:', userId, 'Role:', userRole, 'Appointment doctor:', appointment.doctorId);
    
    const canDelete = userRole === 'admin' || 
                     userRole === 'doctor' ||
                     userRole === 'assistant' ||
                     appointment.doctorId === userId || 
                     appointment.patientId === userId;

    if (!canDelete) {
      console.log('❌ DELETE permission denied - User role:', userRole);
      return NextResponse.json({ 
        error: 'No tienes permisos para eliminar esta cita',
        debug: { userRole, userId, appointmentDoctor: appointment.doctorId }
      }, { status: 403 });
    }

    if (hardDelete) {
      // Eliminación física - permitir a admin y al doctor propietario
      if (userRole === 'admin' || userRole === 'doctor' || appointment.doctorId === userId) {
        console.log('🗑️ Eliminando físicamente cita:', appointmentId);
        await db.delete(appointments).where(eq(appointments.id, appointmentId));
        
        return NextResponse.json({
          message: 'Cita eliminada permanentemente',
        });
      } else {
        return NextResponse.json({ 
          error: 'No tienes permisos para eliminar físicamente esta cita' 
        }, { status: 403 });
      }
    } else {
      // Eliminación lógica (cancelación)
      const result = await db
        .update(appointments)
        .set({
          status: 'cancelled',
          cancellationReason: reason,
          updatedBy: userId,
          updatedAt: new Date(),
        })
        .where(eq(appointments.id, appointmentId))
        .returning();

      return NextResponse.json({
        message: 'Cita cancelada exitosamente',
        data: result[0],
      });
    }
  } catch (error) {
    console.error('Error deleting appointment:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}