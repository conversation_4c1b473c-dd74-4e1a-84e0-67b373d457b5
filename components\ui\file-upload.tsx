'use client';

import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Progress } from '@/components/ui/progress';
import { X, Upload, File, Image } from 'lucide-react';
import { toast } from 'sonner';
import type { UploadPresetType } from '@/lib/cloudinary';

interface FileUploadProps {
  preset: UploadPresetType;
  onUploadComplete: (url: string, fileInfo: any) => void;
  onUploadError?: (error: string) => void;
  currentFile?: string | null;
  accept?: string;
  maxSize?: number; // en MB
  className?: string;
  label?: string;
}

export function FileUpload({
  preset,
  onUploadComplete,
  onUploadError,
  currentFile,
  accept = "image/*,application/pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv",
  maxSize = 10,
  className = "",
  label = "Subir archivo"
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (file: File) => {
    if (file.size > maxSize * 1024 * 1024) {
      const error = `El archivo es demasiado grande. Máximo ${maxSize}MB.`;
      toast.error(error);
      onUploadError?.(error);
      return;
    }

    setUploading(true);
    setProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('preset', preset);

      // Simular progreso de upload
      const progressInterval = setInterval(() => {
        setProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setProgress(100);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error subiendo archivo');
      }

      const result = await response.json();
      
      if (result.success) {
        toast.success('Archivo subido exitosamente');
        onUploadComplete(result.data.secure_url, result.data);
      } else {
        throw new Error(result.error || 'Error desconocido');
      }

    } catch (error) {
      console.error('Upload error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Error subiendo archivo';
      toast.error(errorMessage);
      onUploadError?.(errorMessage);
    } finally {
      setUploading(false);
      setProgress(0);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files[0]) {
      handleFileSelect(files[0]);
    }
  };

  const getFileIcon = (url: string) => {
    if (url.includes('.pdf')) return <File className="h-8 w-8 text-red-500" />;
    return <Image className="h-8 w-8 text-blue-500" />;
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label>{label}</Label>
      
      {/* Área de drop */}
      <div
        className={`border-2 border-dashed rounded-lg p-6 text-center transition-colors ${
          dragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'pointer-events-none opacity-50' : ''}`}
        onDrop={handleDrop}
        onDragOver={(e) => {
          e.preventDefault();
          setDragOver(true);
        }}
        onDragLeave={() => setDragOver(false)}
      >
        {currentFile && !uploading ? (
          <div className="space-y-2">
            <div className="flex items-center justify-center space-x-2">
              {getFileIcon(currentFile)}
              <span className="text-sm text-gray-600">Archivo actual</span>
            </div>
            {currentFile.match(/\.(jpg|jpeg|png|webp)$/i) ? (
              <img
                src={currentFile}
                alt="Preview"
                className="mx-auto max-h-32 max-w-32 rounded border object-cover"
              />
            ) : (
              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded border bg-gray-50">
                {getFileIcon(currentFile)}
              </div>
            )}
          </div>
        ) : uploading ? (
          <div className="space-y-2">
            <Upload className="mx-auto h-8 w-8 text-blue-500" />
            <p className="text-sm text-gray-600">Subiendo archivo...</p>
            <Progress value={progress} className="w-full" />
          </div>
        ) : (
          <div className="space-y-2">
            <Upload className="mx-auto h-8 w-8 text-gray-400" />
            <p className="text-sm text-gray-600">
              Arrastra un archivo aquí o haz clic para seleccionar
            </p>
            <p className="text-xs text-gray-500">
              Máximo {maxSize}MB
            </p>
          </div>
        )}
      </div>

      {/* Input oculto */}
      <input
        ref={fileInputRef}
        type="file"
        accept={accept}
        onChange={handleFileInputChange}
        className="hidden"
      />

      {/* Botones */}
      <div className="flex space-x-2">
        <Button
          type="button"
          variant="outline"
          onClick={() => fileInputRef.current?.click()}
          disabled={uploading}
          className="flex-1"
        >
          <Upload className="mr-2 h-4 w-4" />
          Seleccionar archivo
        </Button>
        
        {currentFile && (
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={() => onUploadComplete('', null)}
            disabled={uploading}
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  );
}