import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments } from '@/db/schema';
import { inArray, and, gte, ne } from 'drizzle-orm';

/**
 * GET /api/patients/appointment-status?patientIds=id1,id2,id3
 * POST /api/patients/appointment-status { patientIds: [...] }
 * 
 * Obtiene el estado de citas para múltiples pacientes
 * Devuelve información sobre citas pendientes, confirmadas y próximas
 */
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const patientIdsParam = searchParams.get('patientIds');

    if (!patientIdsParam) {
      return NextResponse.json({ error: 'patientIds es requerido' }, { status: 400 });
    }

    const patientIds = patientIdsParam.split(',').filter(id => id.trim()).slice(0, 50); // Máximo 50 pacientes
    
    if (patientIds.length === 0) {
      return NextResponse.json({ data: {} });
    }

    // Obtener citas activas de los pacientes (no canceladas)
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Solo fecha, sin hora
    
    const activeAppointments = await db
      .select({
        patientId: appointments.patientId,
        id: appointments.id,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        status: appointments.status,
        title: appointments.title
      })
      .from(appointments)
      .where(
        and(
          inArray(appointments.patientId, patientIds),
          ne(appointments.status, 'cancelled'),
          gte(appointments.scheduledDate, today) // Solo citas de hoy en adelante
        )
      )
      .orderBy(appointments.scheduledDate, appointments.startTime);

    // Agrupar citas por paciente y calcular estadísticas
    const patientAppointmentStatus: Record<string, {
      totalUpcoming: number;
      nextAppointment: Date | null;
      hasScheduled: boolean;
      hasConfirmed: boolean;
      statuses: string[];
      recentAppointments: Array<{
        id: string;
        date: Date;
        status: string;
        title: string;
      }>;
    }> = {};

    // Inicializar todos los pacientes
    patientIds.forEach(patientId => {
      patientAppointmentStatus[patientId] = {
        totalUpcoming: 0,
        nextAppointment: null,
        hasScheduled: false,
        hasPendingConfirmation: false,
        hasConfirmed: false,
        statuses: [],
        recentAppointments: []
      };
    });

    // Procesar citas
    activeAppointments.forEach(appointment => {
      const patientId = appointment.patientId;
      const status = patientAppointmentStatus[patientId];

      status.totalUpcoming++;
      
      // Establecer próxima cita si es la primera o es más cercana
      if (!status.nextAppointment || appointment.scheduledDate < status.nextAppointment) {
        status.nextAppointment = appointment.scheduledDate;
      }

      // Marcar tipos de estado
      if (appointment.status === 'scheduled') {
        status.hasScheduled = true;
      }
      if (appointment.status === 'pending_confirmation') {
        status.hasPendingConfirmation = true;
      }
      if (appointment.status === 'confirmed') {
        status.hasConfirmed = true;
      }

      // Agregar a lista de estados únicos
      if (!status.statuses.includes(appointment.status)) {
        status.statuses.push(appointment.status);
      }

      // Agregar a citas recientes (máximo 3)
      if (status.recentAppointments.length < 3) {
        status.recentAppointments.push({
          id: appointment.id,
          date: appointment.scheduledDate,
          status: appointment.status,
          title: appointment.title || 'Cita médica'
        });
      }
    });

    return NextResponse.json({
      success: true,
      data: patientAppointmentStatus
    });

  } catch (error) {
    console.error('Error obteniendo estado de citas de pacientes:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

/**
 * POST /api/patients/appointment-status
 * Mismo endpoint pero con POST para retrocompatibilidad
 */
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { patientIds } = await request.json();

    if (!patientIds || !Array.isArray(patientIds)) {
      return NextResponse.json({ error: 'patientIds es requerido y debe ser un array' }, { status: 400 });
    }

    const validPatientIds = patientIds.filter(id => id && typeof id === 'string').slice(0, 50); // Máximo 50 pacientes
    
    if (validPatientIds.length === 0) {
      return NextResponse.json({ success: true, data: {} });
    }

    // Obtener citas activas de los pacientes (no canceladas)
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate()); // Solo fecha, sin hora
    
    const activeAppointments = await db
      .select({
        patientId: appointments.patientId,
        id: appointments.id,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        status: appointments.status,
        title: appointments.title
      })
      .from(appointments)
      .where(
        and(
          inArray(appointments.patientId, validPatientIds),
          ne(appointments.status, 'cancelled'),
          gte(appointments.scheduledDate, today) // Solo citas de hoy en adelante
        )
      )
      .orderBy(appointments.scheduledDate, appointments.startTime);

    // Agrupar citas por paciente y calcular estadísticas
    const patientAppointmentStatus: Record<string, {
      totalUpcoming: number;
      nextAppointment: Date | null;
      hasScheduled: boolean;
      hasConfirmed: boolean;
      statuses: string[];
      recentAppointments: Array<{
        id: string;
        date: Date;
        status: string;
        title: string;
      }>;
    }> = {};

    // Inicializar todos los pacientes
    validPatientIds.forEach(patientId => {
      patientAppointmentStatus[patientId] = {
        totalUpcoming: 0,
        nextAppointment: null,
        hasScheduled: false,
        hasPendingConfirmation: false,
        hasConfirmed: false,
        statuses: [],
        recentAppointments: []
      };
    });

    // Procesar citas
    activeAppointments.forEach(appointment => {
      const patientId = appointment.patientId;
      const status = patientAppointmentStatus[patientId];

      status.totalUpcoming++;
      
      // Establecer próxima cita si es la primera o es más cercana
      if (!status.nextAppointment || appointment.scheduledDate < status.nextAppointment) {
        status.nextAppointment = appointment.scheduledDate;
      }

      // Marcar tipos de estado
      if (appointment.status === 'scheduled') {
        status.hasScheduled = true;
      }
      if (appointment.status === 'pending_confirmation') {
        status.hasPendingConfirmation = true;
      }
      if (appointment.status === 'confirmed') {
        status.hasConfirmed = true;
      }

      // Agregar a lista de estados únicos
      if (!status.statuses.includes(appointment.status)) {
        status.statuses.push(appointment.status);
      }

      // Agregar a citas recientes (máximo 3)
      if (status.recentAppointments.length < 3) {
        status.recentAppointments.push({
          id: appointment.id,
          date: appointment.scheduledDate,
          status: appointment.status,
          title: appointment.title || 'Cita médica'
        });
      }
    });

    return NextResponse.json({
      success: true,
      data: patientAppointmentStatus
    });

  } catch (error) {
    console.error('Error obteniendo estado de citas de pacientes:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}