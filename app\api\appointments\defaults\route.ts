import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { consultories, activityTypes } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener IDs por defecto para crear citas
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener consultorio activo
    const consultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);

    // Obtener tipo de actividad con order = 1
    const activityType = await db
      .select()
      .from(activityTypes)
      .where(eq(activityTypes.isActive, true))
      .limit(1);

    if (consultory.length === 0) {
      return NextResponse.json({ 
        error: 'No hay consultorios activos disponibles' 
      }, { status: 400 });
    }

    if (activityType.length === 0) {
      return NextResponse.json({ 
        error: 'No hay tipos de actividad activos disponibles' 
      }, { status: 400 });
    }

    return NextResponse.json({
      consultoryId: consultory[0].id,
      activityTypeId: activityType[0].id,
      consultoryName: consultory[0].name,
      activityTypeName: activityType[0].name
    });

  } catch (error) {
    console.error('Error obteniendo IDs por defecto:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}