import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { consultories, countries, departments, municipalities, userRoles } from '@/db/schema';
import { eq, ne, count } from 'drizzle-orm';

// GET - Obtener consultorio por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const consultoryResult = await db
      .select({
        consultory: consultories,
        country: countries,
        department: departments,
        municipality: municipalities
      })
      .from(consultories)
      .leftJoin(countries, eq(consultories.countryId, countries.id))
      .leftJoin(departments, eq(consultories.departmentId, departments.id))
      .leftJoin(municipalities, eq(consultories.municipalityId, municipalities.id))
      .where(eq(consultories.id, id))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado' }, { status: 404 });
    }

    const row = consultoryResult[0];
    const consultoryData = {
      ...row.consultory,
      country: row.country,
      department: row.department,
      municipality: row.municipality,
      // Solo agregar código de país si el teléfono no lo tiene ya
      phone: row.consultory.phone && row.country?.phoneCode && !row.consultory.phone.startsWith('+')
        ? `+${row.country.phoneCode} ${row.consultory.phone}`
        : row.consultory.phone
    };

    return NextResponse.json({ 
      success: true, 
      data: consultoryData 
    });

  } catch (error) {
    console.error('Error fetching consultory:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar consultorio
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar consultorios.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { 
      name, 
      code, 
      type, 
      specialty, 
      capacity, 
      floor, 
      building, 
      address, 
      phone, 
      email, 
      logoUrl,
      countryId,
      departmentId,
      municipalityId,
      isEmergencyCapable, 
      hasAirConditioning, 
      hasWaitingRoom, 
      isActive 
    } = body;

    // Validar datos requeridos
    if (!name || !code || !type || !specialty) {
      return NextResponse.json({ 
        error: 'Nombre, código, tipo y especialidad son requeridos' 
      }, { status: 400 });
    }

    // Verificar que el consultorio existe
    const existingConsultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, id))
      .limit(1);

    if (existingConsultory.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado' }, { status: 404 });
    }

    // Verificar si el código ha cambiado y si ya existe
    if (code !== existingConsultory[0].code) {
      const codeExists = await db
        .select()
        .from(consultories)
        .where(eq(consultories.code, code))
        .limit(1);

      if (codeExists.length > 0) {
        return NextResponse.json({ 
          error: 'Ya existe un consultorio con este código' 
        }, { status: 400 });
      }
    }

    // Validación para garantizar un solo consultorio activo
    const finalIsActive = isActive !== undefined ? isActive : true;
    const currentIsActive = existingConsultory[0].isActive;

    // Si se está activando y no estaba activo antes
    if (finalIsActive && !currentIsActive) {
      // Desactivar todos los demás consultorios
      await db
        .update(consultories)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(ne(consultories.id, id));
    } 
    // Si se está desactivando y estaba activo antes
    else if (!finalIsActive && currentIsActive) {
      // Verificar que no sea el único activo
      const [{ count: activeCount }] = await db
        .select({ count: count() })
        .from(consultories)
        .where(eq(consultories.isActive, true));

      if (activeCount <= 1) {
        return NextResponse.json({ 
          error: 'No se puede desactivar el único consultorio activo. Debe haber al menos un consultorio activo en el sistema.',
          code: 'LAST_ACTIVE_CONSULTORY'
        }, { status: 400 });
      }
    }

    // Actualizar consultorio
    const [updatedConsultory] = await db
      .update(consultories)
      .set({
        name,
        code,
        type,
        specialty,
        capacity: capacity ? parseInt(capacity) : 1,
        floor: floor ? parseInt(floor) : 1,
        building: building || '',
        address: address || '',
        phone: phone || null,
        email: email || null,
        logoUrl: logoUrl || null,
        countryId: countryId ? parseInt(countryId) : null,
        departmentId: departmentId ? parseInt(departmentId) : null,
        municipalityId: municipalityId ? parseInt(municipalityId) : null,
        isEmergencyCapable: isEmergencyCapable || false,
        hasAirConditioning: hasAirConditioning !== undefined ? hasAirConditioning : true,
        hasWaitingRoom: hasWaitingRoom !== undefined ? hasWaitingRoom : true,
        isActive: finalIsActive,
        updatedAt: new Date()
      })
      .where(eq(consultories.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedConsultory,
      message: 'Consultorio actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating consultory:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar consultorio
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar consultorios.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que el consultorio existe
    const existingConsultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, id))
      .limit(1);

    if (existingConsultory.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado' }, { status: 404 });
    }

    const consultory = existingConsultory[0];

    // Validación 1: No permitir eliminar consultorio activo
    if (consultory.isActive) {
      return NextResponse.json({ 
        error: 'No se puede eliminar un consultorio activo', 
        suggestion: 'Desactive el consultorio antes de eliminarlo',
        code: 'ACTIVE_CONSULTORY'
      }, { status: 400 });
    }

    // Validación 2: Verificar relaciones en tablas relacionadas
    const relatedUserRoles = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.consultoryId, id))
      .limit(1);

    if (relatedUserRoles.length > 0) {
      return NextResponse.json({ 
        error: 'No se puede eliminar el consultorio porque tiene usuarios asignados',
        suggestion: 'Reasigne los usuarios a otro consultorio antes de eliminar',
        code: 'HAS_RELATED_USERS'
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(consultories)
        .where(eq(consultories.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Consultorio eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedConsultory] = await db
        .update(consultories)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(consultories.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedConsultory,
        message: 'Consultorio desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting consultory:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}