import { nanoid } from 'nanoid';
import { db } from '../db/drizzle';
import { medications } from '../db/schema';

const MEDICATIONS_DATA = [
  // Analgésicos y Antiinflamatorios
  {
    id: nanoid(),
    name: 'Paracetamol 500mg',
    genericName: 'Paracetamol',
    brandName: 'Acetaminofén',
    activeIngredient: 'Paracetamol',
    dosageForm: 'Tableta',
    strength: '500mg',
    concentration: '500mg/tableta',
    therapeuticClass: 'Analgésico',
    pharmacologicalGroup: 'Analgésicos no opioides',
    atcCode: 'N02BE01',
    indication: 'Dolor leve a moderado, fiebre',
    contraindications: ['Hipersensibilidad al paracetamol', 'Insuficiencia hepática severa'],
    sideEffects: ['Náuseas', 'Vómitos', 'Reacciones alérgicas'],
    dosageInstructions: '1-2 tabletas cada 6-8 horas, máximo 4g/día',
    warnings: ['No exceder la dosis recomendada', 'Consultar médico si los síntomas persisten'],
    requiresPrescription: false,
    isControlled: false,
    manufacturer: 'Laboratorios Guatemala S.A.',
    presentation: 'Caja x 20 tabletas',
    storageConditions: 'Temperatura ambiente, lugar seco',
    shelfLife: '3 años',
    order: 1,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Ibuprofeno 400mg',
    genericName: 'Ibuprofeno',
    brandName: 'Advil',
    activeIngredient: 'Ibuprofeno',
    dosageForm: 'Tableta',
    strength: '400mg',
    concentration: '400mg/tableta',
    therapeuticClass: 'AINE',
    pharmacologicalGroup: 'Antiinflamatorios no esteroideos',
    atcCode: 'M01AE01',
    indication: 'Dolor, inflamación, fiebre',
    contraindications: ['Úlcera péptica activa', 'Hipersensibilidad a AINEs', 'Tercer trimestre embarazo'],
    sideEffects: ['Dolor epigástrico', 'Náuseas', 'Cefalea', 'Mareos'],
    dosageInstructions: '1 tableta cada 8 horas con alimentos',
    warnings: ['Tomar con alimentos', 'No usar en úlcera péptica'],
    requiresPrescription: false,
    isControlled: false,
    manufacturer: 'Farmacia Nacional',
    presentation: 'Blister x 10 tabletas',
    storageConditions: 'Temperatura ambiente',
    shelfLife: '2 años',
    order: 2,
    isActive: true,
  },

  // Antibióticos
  {
    id: nanoid(),
    name: 'Amoxicilina 500mg',
    genericName: 'Amoxicilina',
    brandName: 'Amoxil',
    activeIngredient: 'Amoxicilina trihidratada',
    dosageForm: 'Cápsula',
    strength: '500mg',
    concentration: '500mg/cápsula',
    therapeuticClass: 'Antibiótico',
    pharmacologicalGroup: 'Penicilinas',
    atcCode: 'J01CA04',
    indication: 'Infecciones bacterianas susceptibles',
    contraindications: ['Hipersensibilidad a penicilinas', 'Mononucleosis infecciosa'],
    sideEffects: ['Diarrea', 'Náuseas', 'Vómitos', 'Candidiasis'],
    dosageInstructions: '1 cápsula cada 8 horas por 7-10 días',
    warnings: ['Completar tratamiento aunque mejoren síntomas', 'Informar alergias a penicilina'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'Laboratorios Lancasco',
    presentation: 'Frasco x 21 cápsulas',
    storageConditions: 'Lugar fresco y seco',
    shelfLife: '2 años',
    order: 3,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Azitromicina 500mg',
    genericName: 'Azitromicina',
    brandName: 'Zitromax',
    activeIngredient: 'Azitromicina dihidratada',
    dosageForm: 'Tableta',
    strength: '500mg',
    concentration: '500mg/tableta',
    therapeuticClass: 'Antibiótico',
    pharmacologicalGroup: 'Macrólidos',
    atcCode: 'J01FA10',
    indication: 'Infecciones respiratorias, piel y tejidos blandos',
    contraindications: ['Hipersensibilidad a macrólidos', 'Insuficiencia hepática severa'],
    sideEffects: ['Dolor abdominal', 'Diarrea', 'Náuseas', 'Flatulencia'],
    dosageInstructions: '1 tableta diaria por 3 días',
    warnings: ['Tomar 1 hora antes o 2 horas después de alimentos'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'Pfizer Guatemala',
    presentation: 'Blister x 3 tabletas',
    storageConditions: 'Temperatura ambiente',
    shelfLife: '3 años',
    order: 4,
    isActive: true,
  },

  // Medicamentos cardiovasculares
  {
    id: nanoid(),
    name: 'Enalapril 10mg',
    genericName: 'Enalapril maleato',
    brandName: 'Renitec',
    activeIngredient: 'Enalapril maleato',
    dosageForm: 'Tableta',
    strength: '10mg',
    concentration: '10mg/tableta',
    therapeuticClass: 'Antihipertensivo',
    pharmacologicalGroup: 'Inhibidores ECA',
    atcCode: 'C09AA02',
    indication: 'Hipertensión arterial, insuficiencia cardíaca',
    contraindications: ['Embarazo', 'Angioedema previo', 'Estenosis renal bilateral'],
    sideEffects: ['Tos seca', 'Hipotensión', 'Mareos', 'Fatiga'],
    dosageInstructions: '1 tableta cada 12 horas',
    warnings: ['Controlar presión arterial regularmente', 'Evitar en embarazo'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'MSD Guatemala',
    presentation: 'Blister x 30 tabletas',
    storageConditions: 'Lugar seco, temperatura ambiente',
    shelfLife: '3 años',
    order: 5,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Atorvastatina 20mg',
    genericName: 'Atorvastatina cálcica',
    brandName: 'Lipitor',
    activeIngredient: 'Atorvastatina cálcica',
    dosageForm: 'Tableta',
    strength: '20mg',
    concentration: '20mg/tableta',
    therapeuticClass: 'Hipolipemiante',
    pharmacologicalGroup: 'Estatinas',
    atcCode: 'C10AA05',
    indication: 'Hipercolesterolemia, prevención cardiovascular',
    contraindications: ['Embarazo', 'Lactancia', 'Enfermedad hepática activa'],
    sideEffects: ['Mialgia', 'Cefalea', 'Dolor abdominal', 'Estreñimiento'],
    dosageInstructions: '1 tableta diaria en la noche',
    warnings: ['Monitorizar enzimas hepáticas', 'Reportar dolor muscular severo'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'Pfizer Guatemala',
    presentation: 'Caja x 30 tabletas',
    storageConditions: 'Temperatura ambiente',
    shelfLife: '2 años',
    order: 6,
    isActive: true,
  },

  // Medicamentos para diabetes
  {
    id: nanoid(),
    name: 'Metformina 850mg',
    genericName: 'Metformina clorhidrato',
    brandName: 'Glucophage',
    activeIngredient: 'Metformina clorhidrato',
    dosageForm: 'Tableta',
    strength: '850mg',
    concentration: '850mg/tableta',
    therapeuticClass: 'Antidiabético',
    pharmacologicalGroup: 'Biguanidas',
    atcCode: 'A10BA02',
    indication: 'Diabetes mellitus tipo 2',
    contraindications: ['Insuficiencia renal', 'Acidosis metabólica', 'Insuficiencia cardíaca severa'],
    sideEffects: ['Diarrea', 'Náuseas', 'Dolor abdominal', 'Sabor metálico'],
    dosageInstructions: '1 tableta cada 12 horas con alimentos',
    warnings: ['Suspender antes de cirugías', 'Monitorizar función renal'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'Laboratorios Rowe',
    presentation: 'Frasco x 60 tabletas',
    storageConditions: 'Lugar seco, temperatura ambiente',
    shelfLife: '3 años',
    order: 7,
    isActive: true,
  },

  // Medicamentos controlados
  {
    id: nanoid(),
    name: 'Tramadol 50mg',
    genericName: 'Tramadol clorhidrato',
    brandName: 'Tradol',
    activeIngredient: 'Tramadol clorhidrato',
    dosageForm: 'Cápsula',
    strength: '50mg',
    concentration: '50mg/cápsula',
    therapeuticClass: 'Analgésico opioide',
    pharmacologicalGroup: 'Analgésicos opioides',
    atcCode: 'N02AX02',
    indication: 'Dolor moderado a severo',
    contraindications: ['Hipersensibilidad al tramadol', 'Intoxicación por alcohol', 'Uso de IMAO'],
    sideEffects: ['Náuseas', 'Mareos', 'Somnolencia', 'Estreñimiento'],
    dosageInstructions: '1-2 cápsulas cada 6-8 horas según necesidad',
    warnings: ['Puede causar dependencia', 'No conducir vehículos', 'Evitar alcohol'],
    requiresPrescription: true,
    isControlled: true,
    controlledCategory: 'Lista III',
    manufacturer: 'Laboratorios Chinoin',
    presentation: 'Caja x 20 cápsulas',
    storageConditions: 'Lugar seguro, temperatura ambiente',
    shelfLife: '2 años',
    order: 8,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Diazepam 5mg',
    genericName: 'Diazepam',
    brandName: 'Valium',
    activeIngredient: 'Diazepam',
    dosageForm: 'Tableta',
    strength: '5mg',
    concentration: '5mg/tableta',
    therapeuticClass: 'Ansiolítico',
    pharmacologicalGroup: 'Benzodiazepinas',
    atcCode: 'N05BA01',
    indication: 'Ansiedad, espasmo muscular, convulsiones',
    contraindications: ['Miastenia gravis', 'Insuficiencia respiratoria severa', 'Apnea del sueño'],
    sideEffects: ['Somnolencia', 'Fatiga', 'Debilidad muscular', 'Ataxia'],
    dosageInstructions: '1 tableta 2-3 veces al día',
    warnings: ['Puede causar dependencia', 'No suspender bruscamente', 'Evitar alcohol'],
    requiresPrescription: true,
    isControlled: true,
    controlledCategory: 'Lista IV',
    manufacturer: 'Roche Guatemala',
    presentation: 'Blister x 30 tabletas',
    storageConditions: 'Lugar seguro y seco',
    shelfLife: '3 años',
    order: 9,
    isActive: true,
  },

  // Medicamentos gastrointestinales
  {
    id: nanoid(),
    name: 'Omeprazol 20mg',
    genericName: 'Omeprazol',
    brandName: 'Prilosec',
    activeIngredient: 'Omeprazol magnesio',
    dosageForm: 'Cápsula',
    strength: '20mg',
    concentration: '20mg/cápsula',
    therapeuticClass: 'Inhibidor bomba protones',
    pharmacologicalGroup: 'Antiulcerosos',
    atcCode: 'A02BC01',
    indication: 'Úlcera péptica, reflujo gastroesofágico, síndrome Zollinger-Ellison',
    contraindications: ['Hipersensibilidad al omeprazol'],
    sideEffects: ['Cefalea', 'Diarrea', 'Dolor abdominal', 'Náuseas'],
    dosageInstructions: '1 cápsula diaria antes del desayuno',
    warnings: ['Tragar entera, no masticar', 'Puede enmascarar síntomas de cáncer gástrico'],
    requiresPrescription: false,
    isControlled: false,
    manufacturer: 'Laboratorios Saval',
    presentation: 'Caja x 28 cápsulas',
    storageConditions: 'Lugar seco, proteger de luz',
    shelfLife: '2 años',
    order: 10,
    isActive: true,
  },

  // Medicamentos respiratorios
  {
    id: nanoid(),
    name: 'Salbutamol 100mcg/dosis',
    genericName: 'Salbutamol sulfato',
    brandName: 'Ventolin',
    activeIngredient: 'Salbutamol sulfato',
    dosageForm: 'Inhalador',
    strength: '100mcg/dosis',
    concentration: '100mcg por inhalación',
    therapeuticClass: 'Broncodilatador',
    pharmacologicalGroup: 'Beta-2 agonistas',
    atcCode: 'R03AC02',
    indication: 'Asma, EPOC, broncoespasmo',
    contraindications: ['Hipersensibilidad al salbutamol'],
    sideEffects: ['Temblor', 'Taquicardia', 'Nerviosismo', 'Cefalea'],
    dosageInstructions: '1-2 inhalaciones cada 4-6 horas según necesidad',
    warnings: ['Agitar antes de usar', 'Enjuagar boca después del uso'],
    requiresPrescription: true,
    isControlled: false,
    manufacturer: 'GlaxoSmithKline',
    presentation: 'Inhalador 200 dosis',
    storageConditions: 'Temperatura ambiente, no perforar',
    shelfLife: '2 años',
    order: 11,
    isActive: true,
  },

  // Vitaminas y suplementos
  {
    id: nanoid(),
    name: 'Complejo B',
    genericName: 'Vitaminas del complejo B',
    brandName: 'Bedoyecta',
    activeIngredient: 'Tiamina, Riboflavina, Niacina, B6, B12',
    dosageForm: 'Tableta',
    strength: 'Multivitamínico',
    concentration: 'Dosis estándar',
    therapeuticClass: 'Vitaminas',
    pharmacologicalGroup: 'Vitaminas hidrosolubles',
    indication: 'Deficiencia de vitaminas B, neuropatías',
    contraindications: ['Hipersensibilidad a los componentes'],
    sideEffects: ['Náuseas leves', 'Reacciones alérgicas raras'],
    dosageInstructions: '1 tableta diaria con alimentos',
    warnings: ['No exceder dosis recomendada'],
    requiresPrescription: false,
    isControlled: false,
    manufacturer: 'Laboratorios Leti',
    presentation: 'Frasco x 100 tabletas',
    storageConditions: 'Lugar fresco y seco',
    shelfLife: '3 años',
    order: 12,
    isActive: true,
  }
];

export async function seedMedications() {
  try {
    console.log('🌱 Seeding medications...');
    
    // Insertar medicamentos
    for (const medication of MEDICATIONS_DATA) {
      await db.insert(medications).values(medication);
    }
    
    console.log(`✅ Successfully seeded ${MEDICATIONS_DATA.length} medications`);
  } catch (error) {
    console.error('❌ Error seeding medications:', error);
    throw error;
  }
}

// Ejecutar si el archivo se ejecuta directamente
if (require.main === module) {
  seedMedications()
    .then(() => {
      console.log('✅ Medication seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Medication seeding failed:', error);
      process.exit(1);
    });
}