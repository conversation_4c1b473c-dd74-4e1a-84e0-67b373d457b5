import { db } from '../db/drizzle';
import { medicalConsultations } from '../db/schema';
import { sql } from 'drizzle-orm';

async function fixExistingConsultations() {
  try {
    console.log('Actualizando consultas existentes...');

    // Verificar consultas que no tienen services o tienen services null
    const consultationsWithoutServices = await db
      .select()
      .from(medicalConsultations)
      .where(sql`services IS NULL OR services::text = '[]'`);

    console.log(`Encontradas ${consultationsWithoutServices.length} consultas sin servicios`);

    for (const consultation of consultationsWithoutServices) {
      // Si la consulta tiene consultationType, migrar a services
      if (consultation.consultationType) {
        const services = [{
          serviceId: consultation.consultationType,
          addedAt: consultation.createdAt?.toISOString() || new Date().toISOString(),
          notes: 'Migrado desde consultationType'
        }];

        await db
          .update(medicalConsultations)
          .set({ services })
          .where(sql`id = ${consultation.id}`);

        console.log(`Actualizada consulta ${consultation.id}`);
      } else {
        // Si no tiene consultationType, crear un array vacío
        await db
          .update(medicalConsultations)
          .set({ services: [] })
          .where(sql`id = ${consultation.id}`);

        console.log(`Inicializada consulta ${consultation.id} con services vacío`);
      }
    }

    console.log('✅ Consultas actualizadas exitosamente');
  } catch (error) {
    console.error('❌ Error actualizando consultas:', error);
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  fixExistingConsultations()
    .then(() => process.exit(0))
    .catch((error) => {
      console.error(error);
      process.exit(1);
    });
}

export { fixExistingConsultations };