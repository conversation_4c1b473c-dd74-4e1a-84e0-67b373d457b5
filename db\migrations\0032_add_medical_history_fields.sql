-- Migración: Agregar campos para síntomas y antecedentes médicos a medical_records
-- Fecha: 2025-07-27

-- Agregar campos para información médica del paciente
ALTER TABLE "medical_records" ADD COLUMN "selectedSymptoms" jsonb DEFAULT '[]'::jsonb;
ALTER TABLE "medical_records" ADD COLUMN "pathologicalHistory" jsonb DEFAULT '[]'::jsonb;
ALTER TABLE "medical_records" ADD COLUMN "nonPathologicalHistory" jsonb DEFAULT '[]'::jsonb;

-- Agregar comentarios para documentar los campos
COMMENT ON COLUMN "medical_records"."selectedSymptoms" IS 'Array de IDs de síntomas seleccionados para el paciente';
COMMENT ON COLUMN "medical_records"."pathologicalHistory" IS 'Array de IDs de antecedentes patológicos del paciente';
COMMENT ON COLUMN "medical_records"."nonPathologicalHistory" IS 'Array de IDs de antecedentes no patológicos del paciente';