import { db } from "@/db";
import { appointments } from "@/db/schema";
import { eq, and, lt, or } from "drizzle-orm";

async function updatePastConfirmedAppointments() {
  console.log("🔍 Buscando citas confirmadas pasadas sin check-in...");
  
  try {
    // Obtener fecha y hora actual
    const now = new Date();
    
    // Buscar citas que:
    // 1. Estén en estado 'confirmed' o 'scheduled'
    // 2. Su hora de inicio (startTime) ya haya pasado
    // 3. No tengan checkedInAt (no se registró llegada)
    const pastConfirmedAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          or(
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'scheduled')
          ),
          lt(appointments.startTime, now)
        )
      );
    
    console.log(`📊 Encontradas ${pastConfirmedAppointments.length} citas pasadas sin atender`);
    
    // Mostrar detalles de cada cita
    for (const appointment of pastConfirmedAppointments) {
      console.log(`\n📅 Cita ID: ${appointment.id}`);
      console.log(`   - Título: ${appointment.title}`);
      console.log(`   - Fecha programada: ${appointment.scheduledDate}`);
      console.log(`   - Hora inicio: ${appointment.startTime}`);
      console.log(`   - Estado actual: ${appointment.status}`);
      console.log(`   - Check-in: ${appointment.checkedInAt ? 'Sí' : 'No'}`);
      
      // Verificar cuánto tiempo ha pasado
      const hoursAgo = Math.floor((now.getTime() - new Date(appointment.startTime).getTime()) / (1000 * 60 * 60));
      console.log(`   - Horas desde la cita: ${hoursAgo}`);
      
      // Si han pasado más de 2 horas, marcar como no-show
      if (hoursAgo >= 2 && !appointment.checkedInAt) {
        console.log(`   ⚠️  Marcando como NO SHOW (más de 2 horas sin check-in)`);
        
        await db
          .update(appointments)
          .set({
            status: 'no_show',
            noShowReason: 'Cita pasada sin registro de llegada (actualización automática)',
            updatedAt: new Date()
          })
          .where(eq(appointments.id, appointment.id));
        
        console.log(`   ✅ Actualizada a no-show`);
      } else if (hoursAgo >= 0 && hoursAgo < 2) {
        console.log(`   ⏰ Cita reciente, esperando posible llegada tardía`);
      }
    }
    
    console.log("\n✅ Proceso completado");
    
  } catch (error) {
    console.error("❌ Error:", error);
  }
}

// Ejecutar el script
updatePastConfirmedAppointments();