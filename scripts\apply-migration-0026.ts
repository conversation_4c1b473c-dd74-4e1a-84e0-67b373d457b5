import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function applyMigration() {
  try {
    console.log('🔄 Aplicando migración 0026: add_precheckin_data_field...');
    
    // Leer el archivo de migración
    const migrationPath = path.join(process.cwd(), 'db/migrations/0026_add_precheckin_data_field.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Ejecutar la migración
    await db.execute(sql.raw(migrationSQL));
    
    console.log('✅ Migración 0026 aplicada exitosamente');
    console.log('📋 Campo preCheckinData agregado a tabla appointments');
    
    // Verificar que la columna existe
    const columnExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'appointments'
        AND column_name = 'preCheckinData'
      );
    `);
    
    console.log('🔍 Verificación de columna:', columnExists.rows[0]?.exists ? 'EXISTE ✅' : 'NO EXISTE ❌');
    
  } catch (error) {
    console.error('❌ Error aplicando migración:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  applyMigration()
    .then(() => {
      console.log('🎉 Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { applyMigration };