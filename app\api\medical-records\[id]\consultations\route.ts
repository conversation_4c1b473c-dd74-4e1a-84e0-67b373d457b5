import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  medicalRecords,
  user, 
  consultories,
  appointments
} from '@/db/schema';
import { eq, and, desc, asc, count, gte, lte } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET - Obtener consultas de un expediente médico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const recordId = params.id;
    const searchParams = request.nextUrl.searchParams;
    const consultationType = searchParams.get('consultationType');
    const doctorId = searchParams.get('doctorId');
    const status = searchParams.get('status');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const orderBy = searchParams.get('orderBy') || 'consultationDate';
    const orderDirection = searchParams.get('orderDirection') || 'desc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 50);
    const offset = (page - 1) * limit;

    // Verificar que el expediente existe
    const recordExists = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (recordExists.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Query base
    let query = db
      .select()
      .from(medicalConsultations)
      .where(eq(medicalConsultations.medicalRecordId, recordId));

    // Aplicar filtros
    const conditions = [eq(medicalConsultations.medicalRecordId, recordId)];

    if (consultationType && consultationType !== 'all') {
      conditions.push(eq(medicalConsultations.consultationType, consultationType));
    }

    if (doctorId && doctorId !== 'all') {
      conditions.push(eq(medicalConsultations.doctorId, doctorId));
    }

    if (status && status !== 'all') {
      conditions.push(eq(medicalConsultations.status, status));
    }

    if (dateFrom) {
      const fromDate = new Date(dateFrom + 'T00:00:00.000Z');
      conditions.push(gte(medicalConsultations.consultationDate, fromDate));
    }

    if (dateTo) {
      const toDate = new Date(dateTo + 'T23:59:59.999Z');
      conditions.push(lte(medicalConsultations.consultationDate, toDate));
    }

    query = query.where(and(...conditions));

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'consultationType' ? medicalConsultations.consultationType :
                       orderBy === 'status' ? medicalConsultations.status :
                       orderBy === 'createdAt' ? medicalConsultations.createdAt :
                       medicalConsultations.consultationDate;

    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query
    const consultations = await query;

    // Enriquecer con información del médico y consultorio
    const enrichedConsultations = await Promise.all(
      consultations.map(async (consultation) => {
        const enrichedConsultation = { ...consultation };

        // Obtener información del médico
        try {
          const doctorData = await db
            .select({
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
            })
            .from(user)
            .where(eq(user.id, consultation.doctorId))
            .limit(1);

          if (doctorData.length > 0) {
            enrichedConsultation.doctor = doctorData[0];
          }
        } catch (error) {
          console.error('Error fetching doctor data:', error);
        }

        // Obtener información del consultorio
        try {
          const consultoryData = await db
            .select({
              name: consultories.name,
              type: consultories.type,
            })
            .from(consultories)
            .where(eq(consultories.id, consultation.consultoryId))
            .limit(1);

          if (consultoryData.length > 0) {
            enrichedConsultation.consultory = consultoryData[0];
          }
        } catch (error) {
          console.error('Error fetching consultory data:', error);
        }

        // Obtener información de la cita si existe
        if (consultation.appointmentId) {
          try {
            const appointmentData = await db
              .select({
                title: appointments.title,
                scheduledDate: appointments.scheduledDate,
                startTime: appointments.startTime,
                endTime: appointments.endTime,
              })
              .from(appointments)
              .where(eq(appointments.id, consultation.appointmentId))
              .limit(1);

            if (appointmentData.length > 0) {
              enrichedConsultation.appointment = appointmentData[0];
            }
          } catch (error) {
            console.error('Error fetching appointment data:', error);
          }
        }

        return enrichedConsultation;
      })
    );

    // Obtener conteo total
    let countQuery = db
      .select({ count: count() })
      .from(medicalConsultations)
      .where(and(...conditions));

    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Obtener estadísticas por tipo
    const typeStatsQuery = await db
      .select({
        consultationType: medicalConsultations.consultationType,
        count: count(),
      })
      .from(medicalConsultations)
      .where(eq(medicalConsultations.medicalRecordId, recordId))
      .groupBy(medicalConsultations.consultationType);

    const stats = {
      total,
      byType: typeStatsQuery.reduce((acc, stat) => {
        acc[stat.consultationType] = stat.count;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      data: enrichedConsultations,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching medical consultations:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Crear nueva consulta médica
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const recordId = params.id;
    const body = await request.json();

    const {
      appointmentId,
      doctorId,
      consultoryId,
      consultationDate,
      consultationType,
      chiefComplaint,
      currentIllness,
      vitalSigns,
      physicalExam,
      diagnoses,
      treatment,
      prescriptions,
      recommendations,
      nextAppointment,
      followUpInstructions,
      attachments,
      status = 'completed'
    } = body;

    // Validaciones básicas
    if (!doctorId || !consultoryId || !consultationDate || !consultationType || !chiefComplaint) {
      return NextResponse.json({ 
        error: 'Campos requeridos: doctorId, consultoryId, consultationDate, consultationType, chiefComplaint' 
      }, { status: 400 });
    }

    // Verificar que el expediente existe y está activo
    const recordResult = await db
      .select()
      .from(medicalRecords)
      .where(and(
        eq(medicalRecords.id, recordId),
        eq(medicalRecords.status, 'active')
      ))
      .limit(1);

    if (recordResult.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado o inactivo' }, { status: 400 });
    }

    // Verificar que el médico existe
    const doctorResult = await db
      .select()
      .from(user)
      .where(eq(user.id, doctorId))
      .limit(1);

    if (doctorResult.length === 0) {
      return NextResponse.json({ error: 'Médico no encontrado' }, { status: 400 });
    }

    // Verificar que el consultorio existe y está activo
    const consultoryResult = await db
      .select()
      .from(consultories)
      .where(and(eq(consultories.id, consultoryId), eq(consultories.isActive, true)))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado o inactivo' }, { status: 400 });
    }

    // Si se proporciona appointmentId, verificar que existe
    if (appointmentId) {
      const appointmentResult = await db
        .select()
        .from(appointments)
        .where(eq(appointments.id, appointmentId))
        .limit(1);

      if (appointmentResult.length === 0) {
        return NextResponse.json({ error: 'Cita no encontrada' }, { status: 400 });
      }
    }

    // Validar fecha de consulta
    const consultationDateTime = new Date(consultationDate);
    if (isNaN(consultationDateTime.getTime())) {
      return NextResponse.json({ error: 'Fecha de consulta inválida' }, { status: 400 });
    }

    // Crear la nueva consulta
    const newConsultation = {
      id: nanoid(),
      medicalRecordId: recordId,
      appointmentId: appointmentId || null,
      doctorId,
      consultoryId,
      consultationDate: consultationDateTime,
      consultationType,
      chiefComplaint,
      currentIllness: currentIllness || null,
      vitalSigns: vitalSigns || null,
      physicalExam: physicalExam || null,
      diagnoses: diagnoses || [],
      treatment: treatment || null,
      prescriptions: prescriptions || [],
      recommendations: recommendations || null,
      nextAppointment: nextAppointment ? new Date(nextAppointment) : null,
      followUpInstructions: followUpInstructions || null,
      attachments: attachments || [],
      status,
      createdBy: userId,
      updatedBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(medicalConsultations).values(newConsultation).returning();

    // Actualizar contador de consultas en el expediente
    await db
      .update(medicalRecords)
      .set({ 
        totalConsultations: sql`${medicalRecords.totalConsultations} + 1`,
        updatedAt: new Date(),
        updatedBy: userId
      })
      .where(eq(medicalRecords.id, recordId));

    return NextResponse.json({
      message: 'Consulta médica creada exitosamente',
      data: result[0],
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating medical consultation:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}