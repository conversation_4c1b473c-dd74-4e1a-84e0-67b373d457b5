import { cn } from '@/lib/utils';
import { LucideIcon } from 'lucide-react';

interface IconProps {
  icon: LucideIcon;
  variant?: 'user' | 'communication' | 'phone' | 'document' | 'time' | 'medical' | 'location' | 'muted' | 'success' | 'warning' | 'error' | 'info';
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

/**
 * Componente de icono consistente para toda la aplicación
 * Garantiza colores y tamaños uniformes
 */
export function Icon({ 
  icon: IconComponent, 
  variant = 'user', 
  size = 'md', 
  className 
}: IconProps) {
  return (
    <IconComponent 
      className={cn(
        // Aplicar variante de color
        {
          'icon-user': variant === 'user',
          'icon-communication': variant === 'communication',
          'icon-phone': variant === 'phone', 
          'icon-document': variant === 'document',
          'icon-time': variant === 'time',
          'icon-medical': variant === 'medical',
          'icon-location': variant === 'location',
          'icon-muted': variant === 'muted',
          'icon-success': variant === 'success',
          'icon-warning': variant === 'warning',
          'icon-error': variant === 'error',
          'icon-info': variant === 'info',
        },
        // Aplicar tamaño
        {
          'icon-xs': size === 'xs',
          'icon-sm': size === 'sm',
          'icon-md': size === 'md', 
          'icon-lg': size === 'lg',
          'icon-xl': size === 'xl',
        },
        className
      )}
    />
  );
}

/**
 * Hook para obtener clases de icono directamente
 * Útil cuando no se puede usar el componente Icon
 */
export function useIconClasses(
  variant: IconProps['variant'] = 'user',
  size: IconProps['size'] = 'md'
) {
  const variantClass = {
    user: 'icon-user',
    communication: 'icon-communication',
    phone: 'icon-phone',
    document: 'icon-document', 
    time: 'icon-time',
    medical: 'icon-medical',
    location: 'icon-location',
    muted: 'icon-muted',
    success: 'icon-success',
    warning: 'icon-warning',
    error: 'icon-error',
    info: 'icon-info',
  }[variant];

  const sizeClass = {
    xs: 'icon-xs',
    sm: 'icon-sm', 
    md: 'icon-md',
    lg: 'icon-lg',
    xl: 'icon-xl',
  }[size];

  return `${variantClass} ${sizeClass}`;
}

/**
 * Constantes de clases para uso directo
 */
export const ICON_VARIANTS = {
  user: 'icon-user',
  communication: 'icon-communication',
  phone: 'icon-phone',
  document: 'icon-document',
  time: 'icon-time',
  medical: 'icon-medical',
  location: 'icon-location',
  muted: 'icon-muted', 
  success: 'icon-success',
  warning: 'icon-warning',
  error: 'icon-error',
  info: 'icon-info',
} as const;

export const ICON_SIZES = {
  xs: 'icon-xs',
  sm: 'icon-sm',
  md: 'icon-md', 
  lg: 'icon-lg',
  xl: 'icon-xl',
} as const;