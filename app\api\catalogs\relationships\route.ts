import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { relationships } from '@/db/schema';
import { and, eq, ilike, or, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');
    const category = url.searchParams.get('category');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(ilike(relationships.name, `%${search}%`));
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(relationships.isActive, active === 'true'));
    }

    if (category) {
      conditions.push(ilike(relationships.category, `%${category}%`));
    }


    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(relationships)
      .where(whereClause);

    // Get paginated data
    const relationshipsData = await db
      .select({
        id: relationships.id,
        name: relationships.name,
        category: relationships.category,
        isActive: relationships.isActive,
        createdAt: relationships.createdAt,
        updatedAt: relationships.updatedAt
      })
      .from(relationships)
      .where(whereClause)
      .orderBy(relationships.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: relationshipsData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching relationships:', error);
    return NextResponse.json(
      { error: 'Failed to fetch relationships' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, category, isActive = true } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(relationships);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newRelationship = await db.insert(relationships).values({
      id: nextId,
      name,
      category: category || null,
      isActive
    }).returning();

    return NextResponse.json({ 
      success: true, 
      data: newRelationship[0],
      message: 'Parentesco creado exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating relationship:', error);
    return NextResponse.json(
      { error: 'Failed to create relationship' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, category, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedRelationship = await db
      .update(relationships)
      .set({
        name,
        category,
        isActive,
        updatedAt: new Date()
      })
      .where(eq(relationships.id, parseInt(id)))
      .returning();

    if (updatedRelationship.length === 0) {
      return NextResponse.json(
        { error: 'Relationship not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedRelationship[0]);
  } catch (error) {
    console.error('Error updating relationship:', error);
    return NextResponse.json(
      { error: 'Failed to update relationship' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedRelationship = await db
      .update(relationships)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(relationships.id, parseInt(id)))
      .returning();

    if (deletedRelationship.length === 0) {
      return NextResponse.json(
        { error: 'Relationship not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Relationship deleted successfully' });
  } catch (error) {
    console.error('Error deleting relationship:', error);
    return NextResponse.json(
      { error: 'Failed to delete relationship' },
      { status: 500 }
    );
  }
} 