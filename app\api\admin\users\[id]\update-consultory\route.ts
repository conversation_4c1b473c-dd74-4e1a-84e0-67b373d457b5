import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function PUT(
  request: NextRequest, 
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        error: 'No autorizado' 
      }, { status: 401 });
    }

    // Verificar que el usuario es admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ 
        success: false, 
        error: 'No tienes permisos para realizar esta acción' 
      }, { status: 403 });
    }

    const body = await request.json();
    const { roleId, consultoryId } = body;

    if (!roleId) {
      return NextResponse.json({ 
        success: false, 
        error: 'ID de rol requerido' 
      }, { status: 400 });
    }

    // Actualizar el consultorio del rol
    const result = await db
      .update(userRoles)
      .set({ 
        consultoryId: consultoryId || null,
        updatedAt: new Date()
      })
      .where(eq(userRoles.id, roleId))
      .returning();

    if (result.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'Rol no encontrado' 
      }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      message: 'Consultorio actualizado exitosamente',
      data: result[0]
    });

  } catch (error) {
    console.error('Error actualizando consultorio:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}