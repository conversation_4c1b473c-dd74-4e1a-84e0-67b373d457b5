/**
 * CORE DEL SISTEMA DE EMAILS
 * Motor principal que maneja toda la lógica de envío
 */

import { Resend } from 'resend';
import { nanoid } from 'nanoid';
import { emailTemplates } from './templates';
import { 
  EmailRequest, 
  EmailResponse, 
  EmailEventType, 
  EmailStatus,
  EmailLog 
} from './types';

const resend = new Resend(process.env.RESEND_API_KEY);

// Configuración del sistema
const EMAIL_CONFIG = {
  fromEmail: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
  fromName: 'Doctora Barbara',
  maxRetries: 3,
  retryDelayMs: 5000,
  enableLogs: true,
  testMode: false // Desactivar modo test para enviar emails realmente
};

// Storage para logs (en producción esto debería ser una base de datos)
const emailLogs: EmailLog[] = [];

/**
 * FUNCIÓN PRINCIPAL - Enviar Email
 * Esta es la función que usarán todos los módulos del sistema
 */
export async function sendSystemEmail(request: EmailRequest): Promise<EmailResponse> {
  const emailId = nanoid();
  
  try {
    // 1. Validar la petición
    const validationResult = validateEmailRequest(request);
    if (!validationResult.isValid) {
      return {
        success: false,
        status: 'failed',
        error: validationResult.error,
        retryable: false
      };
    }

    // 2. Obtener el template
    const template = emailTemplates[request.event];
    if (!template) {
      return {
        success: false,
        status: 'failed',
        error: `Template no encontrado para evento: ${request.event}`,
        retryable: false
      };
    }

    // 3. Generar contenido del email
    const subject = template.subject(request.params);
    const html = template.html(request.params);

    // 4. Crear log inicial
    if (EMAIL_CONFIG.enableLogs) {
      await createEmailLog({
        id: emailId,
        event: request.event,
        to: request.config.to,
        status: 'pending',
        retryCount: 0,
        context: request.context,
        createdAt: new Date(),
        updatedAt: new Date()
      });
    }

    // 5. Enviar email
    if (EMAIL_CONFIG.testMode) {
      console.log('📧 [EMAIL TEST MODE]');
      console.log(`To: ${request.config.to}`);
      console.log(`Subject: ${subject}`);
      console.log(`Event: ${request.event}`);
      console.log(`Context:`, request.context);
      
      // Simular éxito en modo test
      await updateEmailLog(emailId, 'sent');
      return {
        success: true,
        emailId,
        status: 'sent',
        message: 'Email enviado en modo test'
      };
    }

    // 6. Envío real con Resend
    // Asegurar formato correcto del email 'from'
    const fromEmail = EMAIL_CONFIG.fromEmail.includes('<') 
      ? EMAIL_CONFIG.fromEmail 
      : `${EMAIL_CONFIG.fromName} <${EMAIL_CONFIG.fromEmail}>`;
    
    const resendResponse = await resend.emails.send({
      from: fromEmail,
      to: request.config.to,
      cc: request.config.cc,
      bcc: request.config.bcc,
      subject,
      html,
      tags: [
        { name: 'event', value: request.event },
        { name: 'patient_id', value: request.context.patientId || 'unknown' },
        { name: 'priority', value: request.config.priority },
        ...template.tags.map((tag, index) => ({ name: `tag_${index}`, value: tag }))
      ]
    });

    // 7. Procesar respuesta
    if (resendResponse.error) {
      await updateEmailLog(emailId, 'failed', resendResponse.error.message);
      return {
        success: false,
        emailId,
        status: 'failed',
        error: resendResponse.error.message,
        retryable: true
      };
    }

    // 8. Éxito
    await updateEmailLog(emailId, 'sent');
    return {
      success: true,
      emailId,
      status: 'sent',
      message: 'Email enviado exitosamente'
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Error desconocido';
    
    if (EMAIL_CONFIG.enableLogs) {
      await updateEmailLog(emailId, 'failed', errorMessage);
    }
    
    console.error('❌ Error enviando email:', error);
    
    return {
      success: false,
      emailId,
      status: 'failed',
      error: errorMessage,
      retryable: true
    };
  }
}

/**
 * FUNCIÓN DE ALTO NIVEL - Para los flujos de pacientes
 * Esta es la que reemplazará toda la lógica actual
 */
export async function sendPatientEmail(
  event: EmailEventType,
  recipientEmail: string,
  params: any,
  context: {
    patientId?: string;
    doctorId?: string;
    appointmentId?: string;
    [key: string]: any;
  } = {}
): Promise<EmailResponse> {
  
  return sendSystemEmail({
    event,
    config: {
      to: recipientEmail,
      priority: getPriorityForEvent(event)
    },
    context,
    params
  });
}

/**
 * FUNCIÓN DE SCHEDULING - Para emails programados
 */
export async function scheduleEmail(
  request: EmailRequest,
  sendAt: Date
): Promise<{ success: boolean; scheduledId: string }> {
  // TODO: Implementar queue system (Redis, DB, etc.)
  // Por ahora, solo loggeamos
  console.log(`📅 Email programado para ${sendAt.toISOString()}:`, request.event);
  
  return {
    success: true,
    scheduledId: nanoid()
  };
}

/**
 * UTILIDADES INTERNAS
 */

function validateEmailRequest(request: EmailRequest): { isValid: boolean; error?: string } {
  if (!request.event) {
    return { isValid: false, error: 'Evento requerido' };
  }
  
  if (!request.config.to) {
    return { isValid: false, error: 'Email destinatario requerido' };
  }
  
  const template = emailTemplates[request.event];
  if (template) {
    const missingParams = template.requiredParams.filter(
      param => !(param in request.params)
    );
    
    if (missingParams.length > 0) {
      return { 
        isValid: false, 
        error: `Parámetros faltantes: ${missingParams.join(', ')}` 
      };
    }
  }
  
  return { isValid: true };
}

function getPriorityForEvent(event: EmailEventType): 'low' | 'normal' | 'high' | 'critical' {
  const priorityMap: Record<string, 'low' | 'normal' | 'high' | 'critical'> = {
    // Eventos críticos
    'appointment_reminder_2h': 'critical',
    'appointment_cancelled': 'high',
    'password_reset': 'high',
    
    // Eventos normales
    'appointment_created': 'normal',
    'appointment_reminder_24h': 'normal',
    'patient_created': 'normal',
    
    // Eventos de baja prioridad
    'consultation_completed': 'low',
    'feedback_request': 'low'
  };
  
  return priorityMap[event] || 'normal';
}

async function createEmailLog(log: EmailLog): Promise<void> {
  if (!EMAIL_CONFIG.enableLogs) return;
  
  // En producción, esto debería guardar en base de datos
  emailLogs.push(log);
  console.log(`📝 Email log creado: ${log.id} - ${log.event} -> ${log.to}`);
}

async function updateEmailLog(
  emailId: string, 
  status: EmailStatus, 
  error?: string
): Promise<void> {
  if (!EMAIL_CONFIG.enableLogs) return;
  
  const log = emailLogs.find(l => l.id === emailId);
  if (log) {
    log.status = status;
    log.updatedAt = new Date();
    
    if (status === 'sent') {
      log.sentAt = new Date();
    } else if (status === 'failed') {
      log.failedAt = new Date();
      log.error = error;
    }
    
    console.log(`📝 Email log actualizado: ${emailId} - ${status}`);
  }
}

/**
 * FUNCIONES DE ESTADÍSTICAS Y MONITOREO
 */
export function getEmailStats(): {
  total: number;
  byStatus: Record<EmailStatus, number>;
  byEvent: Record<EmailEventType, number>;
} {
  const stats = {
    total: emailLogs.length,
    byStatus: {} as Record<EmailStatus, number>,
    byEvent: {} as Record<EmailEventType, number>
  };
  
  emailLogs.forEach(log => {
    stats.byStatus[log.status] = (stats.byStatus[log.status] || 0) + 1;
    stats.byEvent[log.event] = (stats.byEvent[log.event] || 0) + 1;
  });
  
  return stats;
}

export function getRecentEmails(limit: number = 10): EmailLog[] {
  return emailLogs
    .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
    .slice(0, limit);
}