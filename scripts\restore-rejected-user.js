require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function restoreRejectedUser() {
  try {
    console.log('🔄 Restaurando usuario rechazado...');
    
    // 1. Insertar rol rechazado
    await pool.query(`
      INSERT INTO user_roles (id, "userId", role, status, "rejectionReason", "rejectedAt", "createdAt", "updatedAt")
      VALUES (gen_random_uuid(), 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg', 'assistant', 'rejected', 'Documentación incompleta - Se requiere completar certificaciones', NOW(), NOW(), NOW())
    `);
    console.log('✅ Rol rechazado insertado');
    
    // 2. Insertar solicitud rechazada
    await pool.query(`
      INSERT INTO "registrationRequests" (id, "userId", role, status, "rejectionReason", "reviewedAt", "submittedAt", "updatedAt")
      VALUES (gen_random_uuid(), 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg', 'assistant', 'rejected', 'Documentación incompleta - Se requiere completar certificaciones', NOW(), NOW(), NOW())
    `);
    console.log('✅ Solicitud rechazada insertada');

    // 3. Actualizar status del usuario
    await pool.query(`
      UPDATE "user" 
      SET "overallStatus" = 'rejected', "updatedAt" = NOW() 
      WHERE id = 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg'
    `);
    console.log('✅ Status del usuario actualizado a rejected');
    
    console.log('🎉 Usuario rechazado restaurado exitosamente');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

restoreRejectedUser();