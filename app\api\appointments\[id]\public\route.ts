import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Obtener información pública de cita (sin autenticación)
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;

    // Obtener información básica de la cita
    const result = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        status: appointments.status,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
        consultoryName: consultories.name,
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.patientId, user.id))
      .leftJoin(consultories, eq(appointments.consultoryId, consultories.id))
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = result[0];
    
    // Obtener información del doctor
    const doctorResult = await db
      .select({
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.doctorId, user.id))
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    const doctorInfo = doctorResult[0] || { firstName: '', lastName: '' };

    return NextResponse.json({
      data: {
        ...appointment,
        doctorFirstName: doctorInfo.firstName,
        doctorLastName: doctorInfo.lastName,
      }
    });
  } catch (error) {
    console.error('Error fetching public appointment data:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}