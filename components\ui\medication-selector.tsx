'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Pill, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Medication {
  id: string;
  name: string;
  description?: string;
  strength?: string;
  form?: string;
  manufacturer?: string;
  category?: string;
}

interface MedicationSelectorProps {
  medications: Medication[];
  value: string;
  onChange: (medicationId: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  autoOpen?: boolean; // Nueva prop para abrir automáticamente
}

export function MedicationSelector({ 
  medications, 
  value, 
  onChange, 
  disabled = false, 
  placeholder = "Seleccionar medicamento...",
  className,
  autoOpen = false
}: MedicationSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Auto-abrir cuando se agrega un nuevo medicamento
  useEffect(() => {
    if (autoOpen && !value && !disabled) {
      setIsOpen(true);
    }
  }, [autoOpen, value, disabled]);

  // Filtrar medicamentos basado en el término de búsqueda
  const filteredMedications = useMemo(() => {
    if (!searchTerm) return medications;
    
    const term = searchTerm.toLowerCase();
    return medications.filter(medication =>
      medication.name.toLowerCase().includes(term) ||
      medication.description?.toLowerCase().includes(term) ||
      medication.strength?.toLowerCase().includes(term) ||
      medication.form?.toLowerCase().includes(term) ||
      medication.manufacturer?.toLowerCase().includes(term) ||
      medication.category?.toLowerCase().includes(term)
    );
  }, [medications, searchTerm]);

  const selectedMedication = medications.find(m => m.id === value);

  const handleMedicationSelect = (medicationId: string) => {
    onChange(medicationId);
    setIsOpen(false);
    setSearchTerm('');
  };

  const clearSelection = () => {
    onChange('');
    setIsOpen(false);
  };

  if (disabled && selectedMedication) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md', className)}>
        <div className="text-sm font-medium text-gray-900">
          {selectedMedication.name}
        </div>
        {selectedMedication.strength && (
          <div className="text-xs text-gray-500 mt-1">
            {selectedMedication.strength}
          </div>
        )}
      </div>
    );
  }

  if (disabled) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        No especificado
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <Button
        type="button"
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-11 justify-start text-left font-normal"
      >
        <Pill className="mr-2 h-4 w-4 text-gray-400" />
        {selectedMedication ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col text-left">
              <span className="text-sm font-medium">{selectedMedication.name}</span>
              {selectedMedication.strength && (
                <span className="text-xs text-gray-500">
                  {selectedMedication.strength}
                </span>
              )}
            </div>
            <X 
              className="h-4 w-4 text-gray-400 hover:text-gray-600"
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
            />
          </div>
        ) : (
          <span className="text-gray-500">{placeholder}</span>
        )}
      </Button>
      
      {isOpen && (
        <>
          {/* Overlay para cerrar */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Panel de selección */}
          <Card className="absolute z-50 top-12 left-0 w-full max-w-lg shadow-lg border">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Campo de búsqueda */}
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar medicamentos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10"
                    autoFocus
                  />
                </div>
                
                {/* Lista de medicamentos */}
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {filteredMedications.length > 0 ? (
                    filteredMedications.map((medication) => (
                      <Button
                        key={medication.id}
                        type="button"
                        variant="ghost"
                        onClick={() => handleMedicationSelect(medication.id)}
                        className={cn(
                          "w-full p-3 h-auto text-left justify-start hover:bg-green-50",
                          value === medication.id && "bg-green-50"
                        )}
                      >
                        <div className="flex items-start justify-between w-full">
                          <div className="flex-1">
                            <div className="font-medium text-sm text-gray-900">
                              {medication.name}
                            </div>
                            {medication.description && (
                              <div className="text-xs text-gray-600 mt-1">
                                {medication.description}
                              </div>
                            )}
                            <div className="flex items-center gap-2 mt-2 flex-wrap">
                              {medication.strength && (
                                <Badge variant="outline" className="text-xs">
                                  {medication.strength}
                                </Badge>
                              )}
                              {medication.form && (
                                <Badge variant="secondary" className="text-xs">
                                  {medication.form}
                                </Badge>
                              )}
                              {medication.category && (
                                <Badge variant="outline" className="text-xs">
                                  {medication.category}
                                </Badge>
                              )}
                            </div>
                            {medication.manufacturer && (
                              <div className="text-xs text-gray-500 mt-1">
                                {medication.manufacturer}
                              </div>
                            )}
                          </div>
                        </div>
                      </Button>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Pill className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">No se encontraron medicamentos</p>
                      <p className="text-xs">Intenta con otro término de búsqueda</p>
                    </div>
                  )}
                </div>
                
                {/* Información adicional */}
                {searchTerm && filteredMedications.length > 0 && (
                  <div className="text-xs text-gray-500 border-t pt-2">
                    {filteredMedications.length} medicamento{filteredMedications.length !== 1 ? 's' : ''} encontrado{filteredMedications.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}