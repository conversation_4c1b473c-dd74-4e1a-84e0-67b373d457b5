import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 User Contexts API: Iniciando...');
    
    const { userId } = await auth();
    
    if (!userId) {
      console.log('❌ User Contexts API: Sin autenticación');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('✅ User Contexts API: Usuario autenticado:', userId);

    // Verificar si el usuario existe en la base de datos
    const userInfo = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    // Si el usuario no existe en DB, devolver respuesta de usuario nuevo
    if (!userInfo.length) {
      console.log('⚠️ User Contexts API: Usuario no encontrado en DB - usuario nuevo');
      return NextResponse.json({
        user: {
          id: userId,
          name: 'Usuario Nuevo',
          email: '<EMAIL>',
          firstName: 'Nuevo',
          lastName: 'Usuario',
        },
        contexts: {
          work: {},
          personal: {},
        },
        quickAccess: [],
        currentContext: 'pending',
        hasMultipleContexts: false,
        isPending: true,
        isNewUser: true
      });
    }

    const currentUser = userInfo[0];
    console.log('👤 User Contexts API: Usuario encontrado:', { 
      id: currentUser.id, 
      email: currentUser.email, 
      status: currentUser.overallStatus 
    });

    // Si el usuario está en estado pending, devolver respuesta simplificada
    if (currentUser.overallStatus === 'pending') {
      console.log('⏳ User Contexts API: Usuario pending');
      return NextResponse.json({
        user: {
          id: currentUser.id,
          name: currentUser.name || 'Usuario',
          email: currentUser.email,
          firstName: currentUser.firstName || '',
          lastName: currentUser.lastName || '',
        },
        contexts: {
          work: {},
          personal: {},
        },
        quickAccess: [],
        currentContext: 'pending',
        hasMultipleContexts: false,
        isPending: true
      });
    }

    // Para usuarios activos, verificar roles (simplificado)
    const roles = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.status, 'active')
        )
      );

    console.log('🔑 User Contexts API: Roles encontrados:', roles.length);

    return NextResponse.json({
      user: {
        id: currentUser.id,
        name: currentUser.name || 'Usuario',
        email: currentUser.email,
        firstName: currentUser.firstName || '',
        lastName: currentUser.lastName || '',
      },
      contexts: {
        work: {},
        personal: {},
      },
      quickAccess: [],
      currentContext: roles.length > 0 ? roles[0].role : 'pending',
      hasMultipleContexts: roles.length > 1,
      isPending: false
    });

  } catch (error) {
    console.error('❌ User Contexts API Error:', error);
    return NextResponse.json(
      { error: 'Error al obtener contextos del usuario' },
      { status: 500 }
    );
  }
}