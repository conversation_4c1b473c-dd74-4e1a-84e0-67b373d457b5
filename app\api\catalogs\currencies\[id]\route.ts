import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { currencies } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener moneda por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const currency = await db
      .select()
      .from(currencies)
      .where(eq(currencies.id, id))
      .limit(1);

    if (currency.length === 0) {
      return NextResponse.json({ error: 'Moneda no encontrada' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: currency[0] 
    });

  } catch (error) {
    console.error('Error fetching currency:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar moneda
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar monedas.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, code, symbol, exchangeRate, isDefault, isActive } = body;

    // Validar datos requeridos
    if (!name || !code || !symbol || exchangeRate === undefined) {
      return NextResponse.json({ 
        error: 'Nombre, código, símbolo y tasa de cambio son requeridos' 
      }, { status: 400 });
    }

    // Verificar que la moneda existe
    const existingCurrency = await db
      .select()
      .from(currencies)
      .where(eq(currencies.id, id))
      .limit(1);

    if (existingCurrency.length === 0) {
      return NextResponse.json({ error: 'Moneda no encontrada' }, { status: 404 });
    }

    // Si se está marcando como predeterminada, quitar el flag de todas las demás
    if (isDefault) {
      await db
        .update(currencies)
        .set({ isDefault: false, updatedAt: new Date() })
        .where(eq(currencies.isDefault, true));
    }

    // Verificar si el código ha cambiado y si ya existe
    if (code !== existingCurrency[0].code) {
      const codeExists = await db
        .select()
        .from(currencies)
        .where(eq(currencies.code, code))
        .limit(1);

      if (codeExists.length > 0) {
        return NextResponse.json({ 
          error: 'Ya existe una moneda con este código' 
        }, { status: 400 });
      }
    }

    // Actualizar moneda
    const [updatedCurrency] = await db
      .update(currencies)
      .set({
        name,
        code,
        symbol,
        exchangeRate: parseFloat(exchangeRate),
        isDefault: isDefault || false,
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(currencies.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedCurrency,
      message: 'Moneda actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error updating currency:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar moneda
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar monedas.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que la moneda existe
    const existingCurrency = await db
      .select()
      .from(currencies)
      .where(eq(currencies.id, id))
      .limit(1);

    if (existingCurrency.length === 0) {
      return NextResponse.json({ error: 'Moneda no encontrada' }, { status: 404 });
    }

    // No permitir eliminar la moneda predeterminada
    if (existingCurrency[0].isDefault) {
      return NextResponse.json({ 
        error: 'No se puede eliminar la moneda predeterminada. Primero cambia la moneda predeterminada.' 
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(currencies)
        .where(eq(currencies.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Moneda eliminada físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedCurrency] = await db
        .update(currencies)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(currencies.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedCurrency,
        message: 'Moneda desactivada exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting currency:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}