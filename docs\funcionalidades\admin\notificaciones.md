# Sistema de Notificaciones

## Descripción General

El sistema de notificaciones proporciona una forma elegante y no intrusiva de comunicar información importante a los usuarios, independientemente de su estado en la plataforma (activos, pendientes, rechazados).

## Objetivos Principales

1. **Informar el estado de solicitudes** de manera discreta
2. **Evitar redirecciones forzadas** que interrumpan la navegación
3. **Sentar las bases** para un sistema completo de notificaciones
4. **Mejorar la experiencia de usuario** manteniendo autonomía de navegación

## Funcionamiento Actual Propuesto

### Para Usuarios Pendientes
- **Icono de notificación** visible en la navbar
- **Contenido**: "Tu solicitud está siendo revisada. Solo puedes acceder a páginas públicas por ahora."
- **Acción**: Al hacer clic lleva a `/onboarding/pending`
- **Comportamiento**: Puede navegar libremente en páginas públicas, solo se redirige al intentar acceder a áreas protegidas

### Para Usuarios Rechazados
- **Icono de notificación** visible en la navbar
- **Contenido**: "Tu solicitud fue rechazada. Haz clic para ver detalles y volver a aplicar."
- **Acción**: Al hacer clic lleva a `/onboarding/rejected`
- **Comportamiento**: Puede navegar libremente en páginas públicas, solo se redirige al intentar acceder a áreas protegidas

### Para Usuarios Activos
- **Sistema base** para futuras notificaciones (citas, mensajes, recordatorios, etc.)

## Ventajas de Esta Aproximación

### Experiencia de Usuario
- ✅ **No intrusiva**: No fuerza redirecciones automáticas
- ✅ **Informativa**: Comunica claramente el estado del usuario
- ✅ **Opcional**: El usuario decide cuándo revisar su estado
- ✅ **Consistente**: Usa el mismo patrón visual para todos los tipos de notificaciones

### Técnicas
- ✅ **Escalable**: Fácil agregar nuevos tipos de notificaciones
- ✅ **Mantenible**: Un solo sistema para manejar todas las notificaciones
- ✅ **Flexible**: Puede mostrar notificaciones basadas en metadata de Clerk
- ✅ **Futuro-compatible**: Base sólida para funcionalidades avanzadas

## Flujo de Usuario

```
1. Usuario inicia sesión
   ↓
2. Ve icono de notificación (si tiene estado pending/rejected)
   ↓
3. Puede navegar libremente por páginas públicas
   ↓
4. Si hace clic en notificación → Va a página de estado específica
   ↓
5. Si intenta acceder a dashboard → Redirigido automáticamente por middleware
```

## Implementación Técnica

### Componentes Necesarios
- **NotificationIcon**: Componente en navbar que muestra el icono
- **NotificationDropdown**: Dropdown con el contenido de la notificación
- **useNotifications**: Hook para manejar el estado de notificaciones

### Lógica de Detección
```typescript
// Detectar si mostrar notificación basado en metadata de Clerk
const shouldShowNotification = 
  user?.publicMetadata?.status === 'pending' || 
  user?.publicMetadata?.status === 'rejected'
```

### Estados de Notificación
- `pending`: Solicitud en revisión
- `rejected`: Solicitud rechazada
- `active`: Usuario activo (futuras notificaciones)

## Futuras Expansiones

### Para Sistema Médico Completo
1. **Citas Médicas**
   - Recordatorios de próximas citas
   - Confirmaciones de citas
   - Cambios de horario

2. **Comunicación**
   - Mensajes de doctores
   - Resultados de exámenes
   - Recetas médicas

3. **Administrativas**
   - Documentos por vencer
   - Actualizaciones del sistema
   - Mantenimientos programados

4. **Recordatorios**
   - Toma de medicamentos
   - Chequeos regulares
   - Renovación de documentos

## Beneficios a Largo Plazo

- **Engagement**: Mantiene a los usuarios informados y comprometidos
- **Eficiencia**: Reduce llamadas de soporte sobre estados de solicitud
- **Profesionalismo**: Presenta una imagen más pulida y organizada
- **Escalabilidad**: Crece con las necesidades del sistema de salud

## Problemas Resueltos

### Antes (con redirecciones forzadas)
- ❌ Usuarios frustrados por redirecciones constantes
- ❌ Imposibilidad de navegar libremente
- ❌ Experiencia confusa y poco clara

### Después (con notificaciones)
- ✅ Navegación libre y natural
- ✅ Información clara y accesible
- ✅ Control del usuario sobre cuándo revisar su estado
- ✅ Base sólida para futuras funcionalidades