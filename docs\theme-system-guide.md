# Sistema de Temas Unificado

## 📋 Descripción

El sistema implementa un método unificado para cambiar la paleta de colores de toda la aplicación sin romper el diseño. Todos los componentes de shadcn/ui y el diseño personalizado se actualizan automáticamente.

## 🎨 Paletas Disponibles

### 1. Verde Mundo Pediatra (Actual)
- **Primary**: #059669 (Verde principal)
- **Secondary**: #14b8a6 (Verde aguamarina)  
- **Accent**: #10b981 (Verde esmeralda)

### 2. Colores Pantone
- **Primary**: #A1D6CA (Turquesa Pantone 565 C)
- **Secondary**: #3D4E80 (Azul marino Pantone 4142 C)
- **Accent**: #FCEEA8 (Amarillo claro Pantone 938 C)

### 3. Azul Moderno  
- **Primary**: #3b82f6 (Azul moderno)
- **Secondary**: #dbeafe (Azul claro)
- **Accent**: #60a5fa (Azul medio)

## 🔧 Cómo Cambiar Temas

### Método 1: Editar CSS (Más Simple)

En `/app/globals.css`, buscar la sección "CONFIGURACIÓN DE TEMA ACTIVO" y cambiar las variables:

```css
/* CAMBIAR ESTAS VARIABLES PARA APLICAR UN TEMA DIFERENTE */
/* Opciones: --verde-*, --pantone-*, --azul-* */

/* Para usar la paleta Pantone: */
--theme-primary: var(--pantone-primary);
--theme-primary-foreground: var(--pantone-primary-foreground);
--theme-secondary: var(--pantone-secondary);
--theme-secondary-foreground: var(--pantone-secondary-foreground);
--theme-accent: var(--pantone-accent);
--theme-accent-foreground: var(--pantone-accent-foreground);
--theme-ring: var(--pantone-ring);
```

### Método 2: Usar la Utilidad JavaScript

```typescript
import { applyTheme } from '@/lib/theme-switcher';

// Cambiar a paleta Pantone
applyTheme('pantone');

// Cambiar a paleta azul
applyTheme('azul');

// Volver al verde original
applyTheme('verde');
```

### Método 3: Componente React

```typescript
import { useTheme } from '@/lib/theme-switcher';

function ThemeSwitcher() {
  const { currentTheme, switchTheme, availableThemes, themes } = useTheme();
  
  return (
    <select 
      value={currentTheme || ''} 
      onChange={(e) => switchTheme(e.target.value as ThemeName)}
    >
      {availableThemes.map(theme => (
        <option key={theme} value={theme}>
          {themes[theme].name}
        </option>
      ))}
    </select>
  );
}
```

## ✅ Ventajas del Sistema

1. **Sin romper el diseño**: Usa formato HSL compatible con shadcn/ui
2. **Cambio instantáneo**: Solo cambiar unas pocas variables CSS
3. **Extensible**: Fácil agregar nuevas paletas
4. **Consistente**: Todos los componentes se actualizan automáticamente
5. **Programático**: Cambiar temas desde JavaScript/React

## 🔍 Componentes Afectados

Al cambiar el tema, se actualizan automáticamente:
- ✅ Botones primarios, secundarios y de acento
- ✅ Bordes y rings de focus
- ✅ Barras laterales (sidebar)
- ✅ Gráficos y charts
- ✅ Cards y popovers
- ✅ Todos los componentes shadcn/ui

## 🛠️ Agregar Nueva Paleta

1. **En globals.css**, agregar la nueva paleta:
```css
/* PALETA 4: Mi Nueva Paleta */
--nueva-primary: 200 50% 60%;
--nueva-primary-foreground: 0 0% 100%;
--nueva-secondary: 180 40% 70%;
--nueva-secondary-foreground: 0 0% 100%;
--nueva-accent: 220 60% 50%;
--nueva-accent-foreground: 0 0% 100%;
--nueva-ring: 200 50% 60%;
```

2. **En theme-switcher.ts**, agregar a la configuración:
```typescript
export const themes = {
  // ... otras paletas
  nueva: {
    name: 'Mi Nueva Paleta',
    description: 'Descripción de la nueva paleta',
    colors: {
      primary: '200 50% 60%',
      primaryForeground: '0 0% 100%',
      // ... resto de colores
    },
  },
};
```

## 🎯 Recomendaciones

- **Usar Método 1** para cambios permanentes
- **Usar Método 2/3** para cambios dinámicos o interfaces de usuario
- **Probar en diferentes pantallas** después de cambiar paletas
- **Mantener contraste adecuado** para accesibilidad

## ⚠️ Formato de Colores

- **HSL**: Para compatibilidad con shadcn/ui (formato: `hue saturation% lightness%`)
- **HEX**: Para uso directo en componentes personalizados (formato: `#RRGGBB`)

Ejemplo de conversión:
- HEX: `#A1D6CA` 
- HSL: `169 38% 73%`