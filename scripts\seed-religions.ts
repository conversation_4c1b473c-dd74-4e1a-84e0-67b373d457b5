import { nanoid } from 'nanoid';
import { db } from '../db/drizzle';
import { religions } from '../db/schema';

const RELIGIONS_DATA = [
  // Cristianismo
  {
    id: nanoid(),
    name: 'Católica Romana',
    category: 'Cristianismo',
    description: 'Iglesia Católica Romana, la denominación cristiana más grande del mundo',
    order: 1,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Evangé<PERSON>',
    category: 'Cristianismo',
    description: 'Iglesias evangélicas y protestantes',
    order: 2,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Pentecostal',
    category: 'Cristianismo',
    description: 'Iglesias pentecostales y carismáticas',
    order: 3,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Adventista del Séptimo Día',
    category: 'Cristianismo',
    description: 'Iglesia Adventista del Séptimo Día',
    order: 4,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Testigos de Jehová',
    category: 'Cristianismo',
    description: 'Organización religiosa de los Testigos de Jehová',
    order: 5,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Mormona (Santos de los Últimos Días)',
    category: 'Cristianismo',
    description: 'Iglesia de Jesucristo de los Santos de los Últimos Días',
    order: 6,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Ortodoxa',
    category: 'Cristianismo',
    description: 'Iglesias ortodoxas orientales',
    order: 7,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Bautista',
    category: 'Cristianismo',
    description: 'Iglesias bautistas',
    order: 8,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Metodista',
    category: 'Cristianismo',
    description: 'Iglesias metodistas',
    order: 9,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Presbiteriana',
    category: 'Cristianismo',
    description: 'Iglesias presbiterianas',
    order: 10,
    isActive: true,
  },

  // Islam
  {
    id: nanoid(),
    name: 'Islam Sunní',
    category: 'Islam',
    description: 'Rama mayoritaria del Islam',
    order: 11,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Islam Chiíta',
    category: 'Islam',
    description: 'Segunda rama más grande del Islam',
    order: 12,
    isActive: true,
  },

  // Judaísmo
  {
    id: nanoid(),
    name: 'Judaísmo Ortodoxo',
    category: 'Judaísmo',
    description: 'Judaísmo ortodoxo tradicional',
    order: 13,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Judaísmo Conservador',
    category: 'Judaísmo',
    description: 'Judaísmo conservador o masortí',
    order: 14,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Judaísmo Reformista',
    category: 'Judaísmo',
    description: 'Judaísmo reformista o progresivo',
    order: 15,
    isActive: true,
  },

  // Budismo
  {
    id: nanoid(),
    name: 'Budismo Theravada',
    category: 'Budismo',
    description: 'Escuela más antigua del budismo',
    order: 16,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Budismo Mahayana',
    category: 'Budismo',
    description: 'Gran vehículo del budismo',
    order: 17,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Budismo Tibetano',
    category: 'Budismo',
    description: 'Budismo tibetano o vajrayana',
    order: 18,
    isActive: true,
  },

  // Hinduismo
  {
    id: nanoid(),
    name: 'Hinduismo Vaishnavismo',
    category: 'Hinduismo',
    description: 'Tradición que venera a Vishnu como deidad suprema',
    order: 19,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Hinduismo Shaivismo',
    category: 'Hinduismo',
    description: 'Tradición que venera a Shiva como deidad suprema',
    order: 20,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Hinduismo Shaktismo',
    category: 'Hinduismo',
    description: 'Tradición que venera a la Diosa Madre',
    order: 21,
    isActive: true,
  },

  // Religiones Tradicionales Mayas (relevante para Guatemala)
  {
    id: nanoid(),
    name: 'Espiritualidad Maya K\'iche\'',
    category: 'Tradicional Maya',
    description: 'Tradiciones espirituales del pueblo K\'iche\'',
    order: 22,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Espiritualidad Maya Kaqchikel',
    category: 'Tradicional Maya',
    description: 'Tradiciones espirituales del pueblo Kaqchikel',
    order: 23,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Espiritualidad Maya Q\'eqchi\'',
    category: 'Tradicional Maya',
    description: 'Tradiciones espirituales del pueblo Q\'eqchi\'',
    order: 24,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Espiritualidad Maya Mam',
    category: 'Tradicional Maya',
    description: 'Tradiciones espirituales del pueblo Mam',
    order: 25,
    isActive: true,
  },

  // Otras religiones
  {
    id: nanoid(),
    name: 'Sijismo',
    category: 'Otras',
    description: 'Religión fundada por Guru Nanak en el siglo XV',
    order: 26,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Bahaísmo',
    category: 'Otras',
    description: 'Fe bahá\'í fundada por Bahá\'u\'lláh',
    order: 27,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Jainismo',
    category: 'Otras',
    description: 'Religión india basada en la no violencia',
    order: 28,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Sintoísmo',
    category: 'Otras',
    description: 'Religión tradicional de Japón',
    order: 29,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Taoísmo',
    category: 'Otras',
    description: 'Tradición filosófica y religiosa china',
    order: 30,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Confucianismo',
    category: 'Otras',
    description: 'Sistema ético y filosófico chino',
    order: 31,
    isActive: true,
  },

  // Sincretismo religioso (común en Guatemala)
  {
    id: nanoid(),
    name: 'Católica con elementos mayas',
    category: 'Sincretismo',
    description: 'Catolicismo mezclado con tradiciones espirituales mayas',
    order: 32,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Evangélica con elementos mayas',
    category: 'Sincretismo',
    description: 'Protestantismo evangélico con elementos de espiritualidad maya',
    order: 33,
    isActive: true,
  },

  // No religiosos
  {
    id: nanoid(),
    name: 'Ateo',
    category: 'No religioso',
    description: 'No cree en la existencia de deidades',
    order: 34,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Agnóstico',
    category: 'No religioso',
    description: 'Considera que la existencia de deidades es incognoscible',
    order: 35,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Sin religión',
    category: 'No religioso',
    description: 'No practica ninguna religión específica',
    order: 36,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Espiritual sin religión',
    category: 'No religioso',
    description: 'Se considera espiritual pero no religioso',
    order: 37,
    isActive: true,
  },

  // Nuevos movimientos religiosos
  {
    id: nanoid(),
    name: 'Nueva Era',
    category: 'Nuevos movimientos',
    description: 'Movimiento espiritual New Age',
    order: 38,
    isActive: true,
  },
  {
    id: nanoid(),
    name: 'Espiritualidad Universal',
    category: 'Nuevos movimientos',
    description: 'Enfoque espiritual que trasciende religiones específicas',
    order: 39,
    isActive: true,
  },

  // Especial - Prefiere no especificar
  {
    id: nanoid(),
    name: 'Prefiere no especificar',
    category: 'Especial',
    description: 'El paciente prefiere no especificar su religión',
    order: 40,
    isActive: true,
  }
];

export async function seedReligions() {
  try {
    console.log('🌱 Seeding religions...');
    
    // Insertar religiones
    for (const religion of RELIGIONS_DATA) {
      await db.insert(religions).values(religion);
    }
    
    console.log(`✅ Successfully seeded ${RELIGIONS_DATA.length} religions`);
  } catch (error) {
    console.error('❌ Error seeding religions:', error);
    throw error;
  }
}

// Ejecutar si el archivo se ejecuta directamente
if (require.main === module) {
  seedReligions()
    .then(() => {
      console.log('✅ Religion seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Religion seeding failed:', error);
      process.exit(1);
    });
}