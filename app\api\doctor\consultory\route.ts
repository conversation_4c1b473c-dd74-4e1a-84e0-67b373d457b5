import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, consultories, countries, departments, municipalities } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        error: 'No autorizado' 
      }, { status: 401 });
    }

    console.log('🔍 Doctor consultory API: Buscando consultorio para userId:', userId);

    // Buscar el rol de doctor activo del usuario actual
    const doctorRoleResult = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    console.log('👨‍⚕️ Rol de doctor encontrado:', doctorRoleResult);

    if (doctorRoleResult.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'No se encontró rol de doctor activo' 
      }, { status: 404 });
    }

    const doctorRole = doctorRoleResult[0];
    
    if (!doctorRole.consultoryId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Doctor no tiene consultorio asignado' 
      }, { status: 404 });
    }

    console.log('🏥 Consultorio ID encontrado:', doctorRole.consultoryId);

    // Obtener información completa del consultorio
    const consultoryResult = await db
      .select({
        consultory: consultories,
        country: countries,
        department: departments,
        municipality: municipalities
      })
      .from(consultories)
      .leftJoin(countries, eq(consultories.countryId, countries.id))
      .leftJoin(departments, eq(consultories.departmentId, departments.id))
      .leftJoin(municipalities, eq(consultories.municipalityId, municipalities.id))
      .where(eq(consultories.id, doctorRole.consultoryId))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'Consultorio no encontrado' 
      }, { status: 404 });
    }

    const row = consultoryResult[0];
    const consultoryData = {
      ...row.consultory,
      country: row.country,
      department: row.department,
      municipality: row.municipality,
      phone: row.consultory.phone && row.country?.phoneCode 
        ? `+${row.country.phoneCode} ${row.consultory.phone}`
        : row.consultory.phone
    };

    console.log('✅ Consultorio completo:', consultoryData);

    return NextResponse.json({ 
      success: true, 
      data: consultoryData 
    });

  } catch (error) {
    console.error('❌ Error en doctor consultory API:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}