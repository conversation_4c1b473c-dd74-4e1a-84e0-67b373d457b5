'use client';

import { useState, useEffect } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Check, ChevronDown, Stethoscope, Search } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';

interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  imageUrl?: string;
  specialty?: string;
  isAvailable?: boolean;
  appointmentsToday?: number;
}

interface DoctorSelectorProps {
  selectedDoctorId?: string;
  onDoctorChange: (doctorId: string) => void;
  currentUserRole?: string;
  currentUserId?: string;
}

export function DoctorSelector({ 
  selectedDoctorId, 
  onDoctorChange,
  currentUserRole,
  currentUserId
}: DoctorSelectorProps) {
  const [open, setOpen] = useState(false);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  const isAssistant = currentUserRole === 'assistant';
  const isDoctor = currentUserRole === 'doctor';

  useEffect(() => {
    fetchDoctors();
  }, []);

  const fetchDoctors = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/agenda/doctors');
      const data = await response.json();
      
      if (response.ok) {
        setDoctors(data.data || []);
        
        // Si es doctor y no hay doctor seleccionado, seleccionar el actual
        if (isDoctor && !selectedDoctorId && currentUserId) {
          const currentDoctor = data.data.find((d: Doctor) => d.id === currentUserId);
          if (currentDoctor) {
            onDoctorChange(currentUserId);
          }
        }
      }
    } catch (error) {
      console.error('Error fetching doctors:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredDoctors = doctors.filter(doctor => {
    const fullName = `${doctor.firstName} ${doctor.lastName}`.toLowerCase();
    return fullName.includes(searchTerm.toLowerCase()) || 
           doctor.specialty?.toLowerCase().includes(searchTerm.toLowerCase());
  });

  const selectedDoctor = doctors.find(d => d.id === selectedDoctorId);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  // Si es doctor, solo mostrar su propio avatar sin selector
  if (isDoctor && !isAssistant) {
    return (
      <div className="flex items-center gap-3">
        <Avatar className="h-10 w-10 border-2 border-blue-200">
          <AvatarImage src={selectedDoctor?.imageUrl} />
          <AvatarFallback className="bg-blue-100 text-blue-700 font-semibold">
            {selectedDoctor ? getInitials(selectedDoctor.firstName, selectedDoctor.lastName) : 'DR'}
          </AvatarFallback>
        </Avatar>
        <div className="hidden sm:block">
          <p className="text-sm font-semibold text-gray-900">
            Dr. {selectedDoctor?.firstName} {selectedDoctor?.lastName}
          </p>
          {selectedDoctor?.specialty && (
            <p className="text-xs text-gray-600">{selectedDoctor.specialty}</p>
          )}
        </div>
      </div>
    );
  }

  // Para asistentes, mostrar selector completo
  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="justify-between min-w-[200px] h-auto py-2"
        >
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8 border-2 border-blue-200">
              <AvatarImage src={selectedDoctor?.imageUrl} />
              <AvatarFallback className="bg-blue-100 text-blue-700 text-xs font-semibold">
                {selectedDoctor ? getInitials(selectedDoctor.firstName, selectedDoctor.lastName) : <Stethoscope className="h-4 w-4" />}
              </AvatarFallback>
            </Avatar>
            <div className="text-left">
              <p className="text-sm font-semibold">
                {selectedDoctor ? `Dr. ${selectedDoctor.firstName} ${selectedDoctor.lastName}` : 'Seleccionar doctor'}
              </p>
              {selectedDoctor?.specialty && (
                <p className="text-xs text-gray-600">{selectedDoctor.specialty}</p>
              )}
            </div>
          </div>
          <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[300px] p-0" align="start">
        <div className="p-2">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Buscar doctor..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8"
            />
          </div>
        </div>
        <ScrollArea className="h-[300px]">
          {loading ? (
            <div className="p-4 text-center text-sm text-gray-500">
              Cargando doctores...
            </div>
          ) : filteredDoctors.length === 0 ? (
            <div className="p-4 text-center text-sm text-gray-500">
              No se encontraron doctores
            </div>
          ) : (
            <div className="p-2">
              {filteredDoctors.map((doctor) => (
                <button
                  key={doctor.id}
                  onClick={() => {
                    onDoctorChange(doctor.id);
                    setOpen(false);
                  }}
                  className={cn(
                    "flex items-center gap-3 w-full rounded-md px-3 py-2 text-sm hover:bg-gray-100 transition-colors",
                    selectedDoctorId === doctor.id && "bg-blue-50"
                  )}
                >
                  <Avatar className="h-10 w-10 border-2 border-gray-200">
                    <AvatarImage src={doctor.imageUrl} />
                    <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold">
                      {getInitials(doctor.firstName, doctor.lastName)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="flex-1 text-left">
                    <p className="font-semibold">
                      Dr. {doctor.firstName} {doctor.lastName}
                    </p>
                    <div className="flex items-center gap-2">
                      {doctor.specialty && (
                        <span className="text-xs text-gray-600">{doctor.specialty}</span>
                      )}
                      {doctor.appointmentsToday !== undefined && (
                        <Badge variant="outline" className="text-xs">
                          {doctor.appointmentsToday} citas hoy
                        </Badge>
                      )}
                    </div>
                  </div>
                  {selectedDoctorId === doctor.id && (
                    <Check className="h-4 w-4 text-blue-600" />
                  )}
                  {doctor.isAvailable === false && (
                    <Badge variant="secondary" className="text-xs">
                      No disponible
                    </Badge>
                  )}
                </button>
              ))}
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}