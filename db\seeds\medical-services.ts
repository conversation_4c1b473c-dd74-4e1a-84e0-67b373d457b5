import { db } from '@/db/drizzle';
import { medicalServices } from '@/db/schema';
import { nanoid } from 'nanoid';

export async function seedMedicalServices() {
  console.log('🏥 Seeding medical services...');
  
  const services = [
    // CONSULTAS
    {
      id: nanoid(),
      name: 'Consulta General Pediátrica',
      description: 'Consulta médica general para niños y adolescentes',
      code: 'CONS-001',
      category: 'Consulta',
      basePrice: '250.00',
      currency: 'GTQ',
      duration: 30,
      requiresEquipment: false,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Consulta de Seguimiento',
      description: 'Consulta de seguimiento y control médico',
      code: 'CONS-002',
      category: 'Consulta',
      basePrice: '200.00',
      currency: 'GTQ',
      duration: 20,
      requiresEquipment: false,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Consulta de Emergencia',
      description: 'Atención médica de urgencia',
      code: 'EMER-001',
      category: 'Emergencia',
      basePrice: '350.00',
      currency: 'GTQ',
      duration: 45,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    
    // PROCEDIMIENTOS
    {
      id: nanoid(),
      name: 'Curaciones Menores',
      description: 'Curaciones y tratamiento de heridas menores',
      code: 'PROC-001',
      category: 'Procedimiento',
      basePrice: '150.00',
      currency: 'GTQ',
      duration: 15,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Nebulizaciones',
      description: 'Tratamiento con nebulizador para problemas respiratorios',
      code: 'PROC-002',
      category: 'Procedimiento',
      basePrice: '75.00',
      currency: 'GTQ',
      duration: 20,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Extracción de Cerumen',
      description: 'Limpieza y extracción de cerumen del oído',
      code: 'PROC-003',
      category: 'Procedimiento',
      basePrice: '100.00',
      currency: 'GTQ',
      duration: 15,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    
    // PREVENTIVOS
    {
      id: nanoid(),
      name: 'Control de Niño Sano',
      description: 'Examen médico preventivo para niños',
      code: 'PREV-001',
      category: 'Preventivo',
      basePrice: '200.00',
      currency: 'GTQ',
      duration: 30,
      requiresEquipment: false,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Vacunación',
      description: 'Administración de vacunas según esquema',
      code: 'PREV-002',
      category: 'Preventivo',
      basePrice: '50.00',
      currency: 'GTQ',
      duration: 10,
      requiresEquipment: false,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Evaluación Nutricional',
      description: 'Evaluación del estado nutricional del paciente',
      code: 'PREV-003',
      category: 'Preventivo',
      basePrice: '150.00',
      currency: 'GTQ',
      duration: 25,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    
    // DIAGNÓSTICOS
    {
      id: nanoid(),
      name: 'Prueba Rápida COVID-19',
      description: 'Prueba rápida para detección de COVID-19',
      code: 'DIAG-001',
      category: 'Diagnóstico',
      basePrice: '80.00',
      currency: 'GTQ',
      duration: 15,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Prueba de Strep A',
      description: 'Prueba rápida para detección de estreptococo',
      code: 'DIAG-002',
      category: 'Diagnóstico',
      basePrice: '60.00',
      currency: 'GTQ',
      duration: 10,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Examen Auditivo',
      description: 'Evaluación básica de la audición',
      code: 'DIAG-003',
      category: 'Diagnóstico',
      basePrice: '120.00',
      currency: 'GTQ',
      duration: 20,
      requiresEquipment: true,
      requiresSpecialist: false,
      isActive: true,
    },
    
    // SERVICIOS ESPECIALIZADOS
    {
      id: nanoid(),
      name: 'Consulta Cardiológica Pediátrica',
      description: 'Consulta especializada en cardiología pediátrica',
      code: 'SPEC-001',
      category: 'Consulta',
      basePrice: '400.00',
      currency: 'GTQ',
      duration: 45,
      requiresEquipment: true,
      requiresSpecialist: true,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Consulta Neurológica Pediátrica',
      description: 'Consulta especializada en neurología pediátrica',
      code: 'SPEC-002',
      category: 'Consulta',
      basePrice: '450.00',
      currency: 'GTQ',
      duration: 50,
      requiresEquipment: true,
      requiresSpecialist: true,
      isActive: true,
    },
    {
      id: nanoid(),
      name: 'Teleconsulta',
      description: 'Consulta médica a distancia por videollamada',
      code: 'TELE-001',
      category: 'Consulta',
      basePrice: '180.00',
      currency: 'GTQ',
      duration: 25,
      requiresEquipment: false,
      requiresSpecialist: false,
      isActive: true,
    },
  ];
  
  try {
    await db.insert(medicalServices).values(services);
    console.log(`✅ ${services.length} medical services seeded successfully`);
    
    // Log categories summary
    const categories = [...new Set(services.map(s => s.category))];
    console.log('📊 Categories seeded:', categories.join(', '));
    
    return services;
  } catch (error) {
    console.error('❌ Error seeding medical services:', error);
    throw error;
  }
}