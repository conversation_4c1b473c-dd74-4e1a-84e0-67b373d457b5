'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { toast } from 'sonner';
import { UnifiedPatientForm, PatientFormData } from '@/components/forms/unified-patient-form';

export default function CreatePatientPage() {
  const router = useRouter();
  const { user } = useUser();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (formData: PatientFormData) => {
    setLoading(true);
    
    try {
      // Preparar datos para el API
      const patientData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        documentType: formData.documentType,
        documentNumber: formData.documentNumber,
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        email: formData.email || undefined,
        phone: formData.phone,
        alternativePhone: formData.alternativePhone,
        address: formData.address,
        countryId: formData.countryId ? parseInt(formData.countryId) : undefined,
        departmentId: formData.departmentId ? parseInt(formData.departmentId) : undefined,
        municipalityId: formData.municipalityId ? parseInt(formData.municipalityId) : undefined,
        emergencyContact: formData.emergencyContact,
        emergencyEmail: formData.emergencyEmail,
        emergencyPhone: formData.emergencyPhone,
        emergencyRelationshipId: formData.emergencyRelationshipId ? parseInt(formData.emergencyRelationshipId) : undefined,
        occupationId: formData.occupationId ? parseInt(formData.occupationId) : undefined,
        religionId: formData.religionId ? parseInt(formData.religionId) : undefined,
        educationLevelId: formData.educationLevelId ? parseInt(formData.educationLevelId) : undefined,
        maritalStatusId: formData.maritalStatusId ? parseInt(formData.maritalStatusId) : undefined,
        createdBy: user?.id,
        sendInvitation: formData.sendInvitation,
        // Datos del guardián si es menor
        isMinor: formData.isMinor,
        guardianOption: formData.guardianOption,
        guardianData: formData.guardianData
      };

      const response = await fetch('/api/patients/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(patientData),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Paciente creado exitosamente');
        
        // Determinar a dónde navegar basado en el sendInvitation
        if (formData.sendInvitation && formData.email) {
          toast.info('Se ha enviado una invitación por email al paciente');
        }
        
        // Navegar de vuelta a la lista
        router.push('/dashboard/doctor/pacientes');
      } else {
        // Manejar errores específicos
        if (result.type === 'document_exists') {
          toast.error(`Ya existe un paciente con ese documento: ${result.existingUser?.name}`);
        } else if (result.type === 'email_exists') {
          toast.error(`El email ya está registrado: ${result.existingUser?.name}`);
        } else {
          toast.error(result.error || 'Error al crear paciente');
        }
        throw new Error(result.error);
      }
    } catch (error) {
      console.error('Error creating patient:', error);
      // El error ya se mostró en toast, solo necesitamos propagarlo
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/doctor/pacientes');
  };

  return (
    <div className="w-full max-w-6xl mx-auto px-4 py-4">
      {/* Header simple */}
      <div className="mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/dashboard/doctor/pacientes')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Volver a Pacientes
        </Button>
      </div>

      {/* Formulario Unificado */}
      <div className="pb-8">
        <UnifiedPatientForm
          mode="complete"
          context={{
            showEmailInvitation: true,
            doctorId: user?.id
          }}
          onSubmit={handleSubmit}
          loading={loading}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}