import { db } from '@/db/drizzle';
import { medicalServices } from '@/db/schema';

async function checkMedicalServices() {
  console.log('🔍 Servicios médicos disponibles en el catálogo:\n');

  try {
    const services = await db
      .select()
      .from(medicalServices)
      .where(eq => true); // Obtener todos

    console.log(`📋 Total de servicios: ${services.length}\n`);

    services.forEach((service, index) => {
      console.log(`${index + 1}. ${service.name}`);
      console.log(`   ID: ${service.id}`);
      console.log(`   Precio: Q${service.basePrice}`);
      console.log(`   Categoría: ${service.category}`);
      console.log(`   Duración: ${service.duration || 'N/A'} min`);
      console.log(`   Activo: ${service.isActive ? '✅' : '❌'}`);
      if (service.description) {
        console.log(`   Descripción: ${service.description}`);
      }
      console.log('   ─────────────────────────────────');
    });

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkMedicalServices();