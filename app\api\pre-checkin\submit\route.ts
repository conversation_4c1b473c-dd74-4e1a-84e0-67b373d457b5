import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, guardianPatientRelations } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { logPreCheckinCompleted, logStatusChange } from '@/lib/appointment-logger';

interface PreCheckinData {
  willAttend: 'yes' | 'no' | '';
  hasSymptoms: boolean;
  symptoms: string;
  takingMedications: boolean;
  medications: string;
  hasAllergies: boolean;
  allergies: string;
  phone: string;
  chiefComplaint: string;
  additionalNotes: string;
  
  // 📄 DOCUMENTOS - Campo faltante
  documents?: Array<{
    type: string;
    filename: string;
    url: string;
    description?: string;
  }>;
  
  // Gestión de acompañante
  companionType: 'registered_guardian' | 'different_person' | 'new_guardian' | '';
  selectedGuardianId: string;
  
  // Campos para acompañante diferente o nuevo encargado
  companionName: string;
  companionRelationship: string;
  companionPhone: string;
  companionEmail: string;
  
  // Campos de emergencia
  emergencyContact: string;
  emergencyPhone: string;
  
  // Opciones para crear nueva relación
  shouldCreateNewRelation: boolean;
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      appointmentId,
      formData,
      isDependent
    }: {
      appointmentId: string;
      formData: PreCheckinData;
      isDependent: boolean;
    } = body;

    if (!appointmentId || !formData) {
      return NextResponse.json(
        { error: 'Datos requeridos faltantes' },
        { status: 400 }
      );
    }

    // Verificar que la cita existe
    const appointment = await db
      .select({
        id: appointments.id,
        patientId: appointments.patientId,
        status: appointments.status,
        preCheckinCompleted: appointments.preCheckinCompleted,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { error: 'Cita no encontrada' },
        { status: 404 }
      );
    }

    const appointmentData = appointment[0];

    // Verificar que no se haya completado ya
    if (appointmentData.preCheckinCompleted) {
      return NextResponse.json(
        { error: 'El pre-checkin ya fue completado anteriormente' },
        { status: 400 }
      );
    }

    // Preparar datos para guardar
    const preCheckinInfo = {
      attendance: formData.willAttend,
      hasSymptoms: formData.hasSymptoms,
      symptoms: formData.symptoms,
      takingMedications: formData.takingMedications,
      medications: formData.medications,
      hasAllergies: formData.hasAllergies,
      allergies: formData.allergies,
      contactInfo: {
        phone: formData.phone,
        emergencyContact: formData.emergencyContact,
        emergencyPhone: formData.emergencyPhone,
      },
      companionInfo: isDependent ? {
        name: formData.companionName,
        relationship: formData.companionRelationship,
        phone: formData.companionPhone,
      } : null,
      chiefComplaint: formData.chiefComplaint,
      additionalNotes: formData.additionalNotes,
      // 📄 DOCUMENTOS - Agregar campo faltante
      documents: formData.documents || [],
      isDependent,
      completedAt: new Date().toISOString(),
    };
    
    console.log('🔍 DEBUG Submit: Documentos recibidos:', formData.documents?.length || 0);
    console.log('🔍 DEBUG Submit: Síntomas recibidos:', formData.symptoms);
    console.log('🔍 DEBUG Submit: hasSymptoms:', formData.hasSymptoms);

    // Actualizar la cita con información de pre-checkin
    const updateData: any = {
      preCheckinCompleted: true,
      preCheckinCompletedAt: new Date(),
      preCheckinData: preCheckinInfo, // ← NUEVO: Guardar todos los datos
      updatedAt: new Date(),
    };

    // Si confirma asistencia, cambiar status a 'confirmed'
    if (formData.willAttend === 'yes') {
      updateData.status = 'confirmed';
      updateData.confirmationStatus = 'confirmed';
    } else if (formData.willAttend === 'no') {
      // Si no va a asistir, marcar como cancelada con motivo
      updateData.status = 'cancelled';
      updateData.cancellationReason = `Cancelado por paciente: ${formData.additionalNotes}`;
    }

    // Actualizar motivo de consulta si se proporcionó
    if (formData.chiefComplaint) {
      updateData.chiefComplaint = formData.chiefComplaint;
    }

    // Manejar creación de nueva relación de guardián si es necesario
    let newGuardianId = null;
    if (formData.companionType === 'new_guardian' && formData.shouldCreateNewRelation && appointmentData.patientId) {
      try {
        console.log('🔗 Creando nueva relación de guardián...');
        
        // Buscar si ya existe un usuario con este email
        let guardianUser = null;
        if (formData.companionEmail) {
          const existingUser = await db
            .select()
            .from(user)
            .where(eq(user.email, formData.companionEmail))
            .limit(1);
          
          if (existingUser.length > 0) {
            guardianUser = existingUser[0];
            console.log('👤 Usuario encontrado:', guardianUser.id);
          }
        }
        
        // Si no existe usuario con ese email, crear uno temporal
        if (!guardianUser) {
          const guardianData = {
            id: nanoid(),
            firstName: formData.companionName.split(' ')[0] || formData.companionName,
            lastName: formData.companionName.split(' ').slice(1).join(' ') || null,
            email: formData.companionEmail || `guardian_${nanoid()}@temp.local`,
            phone: formData.companionPhone,
            overallStatus: 'pending' as const,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
          
          await db.insert(user).values(guardianData);
          guardianUser = guardianData;
          newGuardianId = guardianData.id;
          console.log('👤 Nuevo usuario guardián creado:', newGuardianId);
        }
        
        // Verificar si ya existe una relación
        const existingRelation = await db
          .select()
          .from(guardianPatientRelations)
          .where(
            and(
              eq(guardianPatientRelations.guardianId, guardianUser.id),
              eq(guardianPatientRelations.patientId, appointmentData.patientId)
            )
          )
          .limit(1);
        
        if (existingRelation.length === 0) {
          // Crear nueva relación de guardián
          const relationData = {
            id: nanoid(),
            guardianId: guardianUser.id,
            patientId: appointmentData.patientId,
            relationship: formData.companionRelationship,
            isPrimary: false, // Las nuevas relaciones no son primarias por defecto
            canMakeDecisions: false, // Se puede cambiar después por admin
            isActive: true,
            createdAt: new Date(),
            updatedAt: new Date(),
          };
          
          await db.insert(guardianPatientRelations).values(relationData);
          console.log('🔗 Nueva relación de guardián creada');
        } else {
          console.log('🔗 Relación de guardián ya existe');
        }
        
      } catch (error) {
        console.error('❌ Error creando relación de guardián:', error);
        // No fallar el pre-checkin por esto, solo loggear
      }
    }

    // Guardar en la base de datos
    await db
      .update(appointments)
      .set(updateData)
      .where(eq(appointments.id, appointmentId));

    // 📝 LOG: Pre-checkin completado
    await logPreCheckinCompleted(
      appointmentId,
      'system', // TODO: obtener usuario real del contexto
      isDependent,
      preCheckinInfo
    );

    // 📝 LOG: Cambio de estado si aplica
    if (updateData.status && updateData.status !== appointmentData.status) {
      await logStatusChange(
        appointmentId,
        'system', // TODO: obtener usuario real del contexto
        appointmentData.status,
        updateData.status,
        formData.willAttend === 'no' ? 'Cancelado por pre-checkin' : 'Confirmado por pre-checkin'
      );
    }

    // Si hay información de contacto, actualizar el perfil del paciente
    if (formData.phone && appointmentData.patientId) {
      const patientUpdates: any = {};
      
      if (formData.phone) {
        patientUpdates.phone = formData.phone;
      }
      
      if (Object.keys(patientUpdates).length > 0) {
        patientUpdates.updatedAt = new Date();
        
        await db
          .update(user)
          .set(patientUpdates)
          .where(eq(user.id, appointmentData.patientId));
      }
    }

    // Respuesta exitosa
    return NextResponse.json({
      success: true,
      message: 'Pre-checkin completado exitosamente',
      data: {
        appointmentId,
        status: updateData.status,
        attendance: formData.willAttend,
        completedAt: updateData.preCheckinCompletedAt,
      },
    });

  } catch (error) {
    console.error('Error processing pre-checkin:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error interno del servidor'
      },
      { status: 500 }
    );
  }
}

// Función auxiliar para generar reportes de pre-checkin (opcional)
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const appointmentId = searchParams.get('appointmentId');

    if (!appointmentId) {
      return NextResponse.json(
        { error: 'ID de cita requerido' },
        { status: 400 }
      );
    }

    // Obtener información del pre-checkin
    const appointment = await db
      .select({
        id: appointments.id,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinCompletedAt: appointments.preCheckinCompletedAt,
        preCheckinData: appointments.preCheckinData,
        preCheckinSent: appointments.preCheckinSent,
        status: appointments.status,
        confirmationStatus: appointments.confirmationStatus,
        chiefComplaint: appointments.chiefComplaint,
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.patientId, user.id))
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { error: 'Cita no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: appointment[0],
    });

  } catch (error) {
    console.error('Error getting pre-checkin info:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}