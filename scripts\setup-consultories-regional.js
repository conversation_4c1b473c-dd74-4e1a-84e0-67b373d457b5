// Script para agregar configuración regional solo a consultories
const { neon } = require('@neondatabase/serverless');
require('dotenv').config({ path: '.env.local' });

async function setupConsultoriesRegional() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🏥 Configurando regional_settings en consultories...');
  
  try {
    // 1. Verificar si la columna regional_settings existe
    const columns = await sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'consultories' AND column_name = 'regional_settings';
    `;
    
    if (columns.length === 0) {
      console.log('➕ Agregando columna regional_settings...');
      await sql`
        ALTER TABLE consultories 
        ADD COLUMN regional_settings jsonb DEFAULT '{
          "dateFormat": "dd/MM/yyyy",
          "dateTimeFormat": "dd/MM/yyyy HH:mm", 
          "timeFormat": "HH:mm",
          "currency": "GTQ",
          "currencySymbol": "Q",
          "currencyPosition": "before",
          "locale": "es-GT",
          "timezone": "America/Guatemala",
          "decimalSeparator": ".",
          "thousandsSeparator": ",",
          "weekStartsOn": 1
        }'::jsonb;
      `;
    }
    
    // 2. Actualizar consultories existentes que no tengan configuración
    const result = await sql`
      UPDATE consultories 
      SET regional_settings = '{
        "dateFormat": "dd/MM/yyyy",
        "dateTimeFormat": "dd/MM/yyyy HH:mm", 
        "timeFormat": "HH:mm",
        "currency": "GTQ",
        "currencySymbol": "Q",
        "currencyPosition": "before",
        "locale": "es-GT",
        "timezone": "America/Guatemala",
        "decimalSeparator": ".",
        "thousandsSeparator": ",",
        "weekStartsOn": 1
      }'::jsonb
      WHERE regional_settings IS NULL OR regional_settings = '{}'::jsonb;
    `;
    
    console.log(`✅ ${result.count || 0} consultories actualizados con configuración regional`);
    
    // 3. Mostrar consultories con su configuración
    const consultories = await sql`
      SELECT id, name, regional_settings 
      FROM consultories 
      WHERE active = true OR "isActive" = true
      LIMIT 5;
    `;
    
    console.log('\n🏥 Consultories configurados:');
    consultories.forEach(c => {
      console.log(`  - ${c.name}: ${c.regional_settings ? '✅ Configurado' : '❌ Sin configurar'}`);
    });
    
    console.log('\n🎉 ¡Configuración regional completada para consultories!');
    console.log('\n📝 Próximos pasos:');
    console.log('  1. El sistema ya puede usar configuración regional por consultorio');
    console.log('  2. Para configuración global, crear la tabla system_config más tarde');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.error(error);
  }
}

setupConsultoriesRegional();