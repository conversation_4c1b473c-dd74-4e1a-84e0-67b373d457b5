import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

async function applyPatientInvitationsMigration() {
  try {
    console.log('Aplicando migración para tabla patient_invitations...');

    // Crear tabla de invitaciones de pacientes
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS patient_invitations (
        id VARCHAR(255) PRIMARY KEY,
        patient_user_id VARCHAR(255) NOT NULL,
        guardian_email VARCHAR(255) NOT NULL,
        invitation_token VARCHAR(255) UNIQUE NOT NULL,
        status VARCHAR(20) DEFAULT 'pending',
        expires_at TIMESTAMP NOT NULL,
        created_at TIMESTAMP DEFAULT NOW(),
        accepted_at TIMESTAMP,
        
        FOREIGN KEY (patient_user_id) REFERENCES "user"(id) ON DELETE CASCADE
      )
    `);

    // Crear índices
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS patient_invitations_token_idx ON patient_invitations(invitation_token)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS patient_invitations_status_idx ON patient_invitations(status)
    `);

    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS patient_invitations_guardian_email_idx ON patient_invitations(guardian_email)
    `);

    console.log('✅ Migración aplicada exitosamente');

  } catch (error) {
    console.error('❌ Error aplicando migración:', error);
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  applyPatientInvitationsMigration()
    .then(() => {
      console.log('Migración completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Error:', error);
      process.exit(1);
    });
}

export { applyPatientInvitationsMigration };