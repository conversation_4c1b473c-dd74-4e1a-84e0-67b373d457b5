'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Checkbox } from '@/components/ui/checkbox';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Plus,
  Search,
  Filter,
  RefreshCw,
  Users,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  UserCheck,
  UserX,
  Shield,
  Download,
  ChevronDown,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  AlertCircle
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { formatDate, formatTempEmail } from '@/lib/utils';

interface UserRole {
  id: string;
  role: string;
  status: string;
  consultoryId?: string;
  specialtyId?: number;
  medicalLicense?: string;
  createdAt: string;
}

interface UserData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  image?: string;
  dateOfBirth?: string;
  documentType: string;
  documentNumber: string;
  gender?: string;
  bloodType?: string;
  address?: string;
  city?: string;
  countryId?: number;
  departmentId?: number;
  municipalityId?: number;
  occupationId?: number;
  emergencyContact?: string;
  emergencyPhone?: string;
  emergencyRelationshipId?: number;
  emailVerified: boolean;
  phoneVerified: boolean;
  overallStatus: string;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
  roles: UserRole[];
}

interface PaginationData {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
  from: number;
  to: number;
}

interface StatsData {
  total: number;
  active: number;
  inactive: number;
  pending: number;
  suspended: number;
}

interface UsersResponse {
  users: UserData[];
  pagination: PaginationData;
  stats: StatsData;
}

interface DeleteAnalysisResult {
  success: boolean;
  analysis?: any;
  summary?: {
    hasImpact: boolean;
    totalItems: number;
    itemsList: string[];
  };
  user?: {
    name: string;
    email: string;
  };
}

const roleLabels: { [key: string]: string } = {
  admin: 'Administrador',
  doctor: 'Doctor',
  assistant: 'Asistente',
  patient: 'Paciente',
  guardian: 'Guardian',
  provider: 'Proveedor'
};

const statusColors: { [key: string]: string } = {
  active: 'bg-emerald-100 text-emerald-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800',
  suspended: 'bg-red-100 text-red-800',
  deleted: 'bg-red-100 text-red-800'
};

const statusLabels: { [key: string]: string } = {
  active: 'Activo',
  inactive: 'Inactivo',
  pending: 'Pendiente',
  suspended: 'Suspendido',
  deleted: 'Eliminado'
};

export default function UsersPage() {
  const router = useRouter();
  
  // Estados principales
  const [data, setData] = useState<UsersResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');
  
  // Estados de filtros
  const [search, setSearch] = useState('');
  const [roleFilter, setRoleFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  
  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Estados de selección múltiple
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  
  // Estados para el diálogo de eliminación
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [userToDelete, setUserToDelete] = useState<string | null>(null);
  const [deleteAnalysis, setDeleteAnalysis] = useState<DeleteAnalysisResult | null>(null);
  const [deletingUser, setDeletingUser] = useState(false);
  
  // Estados para modales
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedUser, setSelectedUser] = useState<UserData | null>(null);
  const [editingUser, setEditingUser] = useState<UserData | null>(null);
  

  // Cargar usuarios
  const fetchUsers = async () => {
    try {
      setError('');
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        sortBy,
        sortOrder,
      });

      if (search) params.append('search', search);
      if (roleFilter !== 'all') params.append('role', roleFilter);
      if (statusFilter !== 'all') params.append('status', statusFilter);

      const response = await fetch(`/api/admin/users?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar usuarios');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.error || 'Error desconocido');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Error al cargar usuarios';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchUsers();
  }, [page, limit, sortBy, sortOrder, search, roleFilter, statusFilter]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchUsers();
  };

  const handleSearch = () => {
    setPage(1);
    fetchUsers();
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(data?.users.map(u => u.id) || []);
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: string, checked: boolean) => {
    if (checked) {
      setSelectedUsers([...selectedUsers, userId]);
    } else {
      setSelectedUsers(selectedUsers.filter(id => id !== userId));
    }
  };

  // Funciones para modales
  const openDetailsDialog = (user: UserData) => {
    setSelectedUser(user);
    setIsDetailsDialogOpen(true);
  };

  const openEditDialog = (user: UserData) => {
    setEditingUser(user);
    setIsEditDialogOpen(true);
  };

  const handleUserAction = async (action: string, userId: string) => {
    try {
      switch (action) {
        case 'view':
          router.push(`/dashboard/admin/users/${userId}`);
          break;
        case 'edit':
          router.push(`/dashboard/admin/users/${userId}?mode=edit`);
          break;
        case 'toggle-status':
          const statusUser = data?.users.find(u => u.id === userId);
          if (!statusUser) return;
          
          const newStatus = statusUser.overallStatus === 'active' ? 'inactive' : 'active';
          const response = await fetch(`/api/admin/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ newStatus }),
          });

          const result = await response.json();
          if (result.success) {
            toast.success(result.data.message);
            fetchUsers();
          } else {
            throw new Error(result.error);
          }
          break;
        case 'delete':
          // Mostrar diálogo de confirmación personalizado
          await showDeleteConfirmation(userId);
          break;
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al procesar acción');
    }
  };

  const showDeleteConfirmation = async (userId: string) => {
    try {
      const analysisResponse = await fetch(`/api/admin/users/${userId}/analyze-deletion`, {
        method: 'POST',
      });
      
      const analysisResult = await analysisResponse.json();
      
      if (analysisResult.success) {
        setDeleteAnalysis(analysisResult);
        setUserToDelete(userId);
        setShowDeleteDialog(true);
      } else {
        throw new Error(analysisResult.error);
      }
    } catch (error) {
      toast.error('Error al analizar impacto de eliminación: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    }
  };

  const executePhysicalDeletion = async () => {
    if (!userToDelete) return;
    
    setDeletingUser(true);
    try {
      const deleteResponse = await fetch(
        `/api/admin/users/${userToDelete}?deletePhysically=true&deleteFromClerk=true`, 
        {
          method: 'DELETE',
        }
      );

      const deleteResult = await deleteResponse.json();
      
      if (deleteResult.success) {
        toast.success('Usuario eliminado físicamente exitosamente');
        setShowDeleteDialog(false);
        setUserToDelete(null);
        setDeleteAnalysis(null);
        fetchUsers();
      } else {
        throw new Error(deleteResult.error);
      }
    } catch (error) {
      toast.error('Error al eliminar usuario: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    } finally {
      setDeletingUser(false);
    }
  };

  const executeLogicalDeletion = async () => {
    if (!userToDelete) return;
    
    setDeletingUser(true);
    try {
      const deleteResponse = await fetch(
        `/api/admin/users/${userToDelete}?deletePhysically=false&deleteFromClerk=false`, 
        {
          method: 'DELETE',
        }
      );

      const deleteResult = await deleteResponse.json();
      
      if (deleteResult.success) {
        toast.success('Usuario eliminado lógicamente exitosamente');
        setShowDeleteDialog(false);
        setUserToDelete(null);
        setDeleteAnalysis(null);
        fetchUsers();
      } else {
        throw new Error(deleteResult.error);
      }
    } catch (error) {
      toast.error('Error al eliminar usuario: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    } finally {
      setDeletingUser(false);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) return;

    try {
      const response = await fetch('/api/admin/users/bulk-actions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          userIds: selectedUsers,
        }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success(result.data.message);
        setSelectedUsers([]);
        fetchUsers();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error en acción masiva');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestión de Usuarios</h1>
          <p className="text-gray-600">
            Administra todos los usuarios del sistema
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={handleRefresh} 
            variant="outline"
            disabled={refreshing}
            className="hover:bg-gray-50 transition-colors"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button 
            onClick={() => router.push('/dashboard/admin/users/create')}
            className="bg-blue-600 hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2" />
            Crear Usuario
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      {data && (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-5">
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Total
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Users className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.stats.total}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Usuarios totales</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Activos
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
                <UserCheck className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.stats.active}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Usuarios activos</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Inactivos
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-gray-100 flex items-center justify-center">
                <UserX className="h-5 w-5 md:h-6 md:w-6 text-gray-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.stats.inactive}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Usuarios inactivos</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Pendientes
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Shield className="h-5 w-5 md:h-6 md:w-6 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.stats.pending}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Por aprobar</span>
              </div>
            </CardContent>
          </Card>

          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Suspendidos
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-red-100 flex items-center justify-center">
                <Shield className="h-5 w-5 md:h-6 md:w-6 text-red-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.stats.suspended}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Usuarios suspendidos</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, email o documento..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activo</SelectItem>
                  <SelectItem value="inactive">Inactivo</SelectItem>
                  <SelectItem value="pending">Pendiente</SelectItem>
                  <SelectItem value="suspended">Suspendido</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Todos los roles" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los roles</SelectItem>
                  <SelectItem value="admin">Administrador</SelectItem>
                  <SelectItem value="doctor">Doctor</SelectItem>
                  <SelectItem value="assistant">Asistente</SelectItem>
                  <SelectItem value="patient">Paciente</SelectItem>
                  <SelectItem value="guardian">Guardian</SelectItem>
                  <SelectItem value="provider">Proveedor</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Tabla de usuarios */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Lista de Usuarios
              </CardTitle>
              {data && (
                <Badge variant="outline" className="border-gray-300">
                  {data.pagination.total} usuarios
                </Badge>
              )}
            </div>
            
            <div className="flex gap-2">
              {selectedUsers.length > 0 && (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="hover:bg-gray-50">
                      Acciones ({selectedUsers.length})
                      <ChevronDown className="ml-2 h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => handleBulkAction('activate')} className="text-green-600">
                      <UserCheck className="mr-2 h-4 w-4" />
                      Activar seleccionados
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleBulkAction('deactivate')} className="text-orange-600">
                      <UserX className="mr-2 h-4 w-4" />
                      Inactivar seleccionados
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => handleBulkAction('delete')}
                      className="text-red-600 focus:text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Eliminar seleccionados
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          {error ? (
            <div className="p-6 text-center text-red-600">
              {error}
            </div>
          ) : !data?.users.length ? (
            <div className="p-6 text-center text-gray-500">
              No se encontraron usuarios
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block overflow-x-auto">
                <Table>
                <TableHeader>
                  <TableRow className="border-gray-200">
                    <TableHead className="w-12">
                      <Checkbox
                        className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                        checked={selectedUsers.length === data.users.length}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('firstName')}
                    >
                      <div className="flex items-center gap-2">
                        Usuario
                        <SortIcon column="firstName" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('email')}
                    >
                      <div className="flex items-center gap-2">
                        Email
                        <SortIcon column="email" />
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">Roles</TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('overallStatus')}
                    >
                      <div className="flex items-center gap-2">
                        Estado
                        <SortIcon column="overallStatus" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('createdAt')}
                    >
                      <div className="flex items-center gap-2">
                        Creado
                        <SortIcon column="createdAt" />
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700 text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                
                <TableBody>
                  {data.users.map((user) => (
                    <TableRow key={user.id} className="hover:bg-gray-50 transition-colors border-gray-200">
                      <TableCell>
                        <Checkbox
                          className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                        />
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <Avatar className="h-8 w-8">
                            <AvatarImage src={user.image} />
                            <AvatarFallback className="bg-blue-100 text-blue-600">
                              {user.firstName?.[0]}{user.lastName?.[0]}
                            </AvatarFallback>
                          </Avatar>
                          <div>
                            <p className="font-medium text-gray-900">
                              {user.firstName} {user.lastName}
                            </p>
                            <p className="text-sm text-gray-500">
                              {user.documentType}: {user.documentNumber}
                            </p>
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <span className="text-gray-900 truncate max-w-[200px]">{formatTempEmail(user.email)}</span>
                          {user.emailVerified && (
                            <Badge variant="secondary" className="text-xs">
                              Verificado
                            </Badge>
                          )}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <div className="flex flex-wrap gap-1">
                          {user.roles.map((role) => (
                            <Badge key={role.id} variant="outline" className="border-gray-300">
                              {roleLabels[role.role] || role.role}
                            </Badge>
                          ))}
                        </div>
                      </TableCell>
                      
                      <TableCell>
                        <Badge className={statusColors[user.overallStatus]}>
                          {statusLabels[user.overallStatus] || user.overallStatus}
                        </Badge>
                      </TableCell>
                      
                      <TableCell>
                        <div className="text-sm">
                          <div className="text-gray-900">
                            {format(new Date(user.createdAt), 'dd MMM yyyy', { locale: es })}
                          </div>
                          <div className="text-xs text-gray-500">
                            {format(new Date(user.createdAt), 'HH:mm')}
                          </div>
                        </div>
                      </TableCell>
                      
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0 hover:bg-gray-100">
                              <span className="sr-only">Abrir menú</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => handleUserAction('view', user.id)}>
                              <Eye className="mr-2 h-4 w-4" />
                              Ver detalles
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleUserAction('edit', user.id)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Editar
                            </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                              onClick={() => handleUserAction('toggle-status', user.id)}
                              className={user.overallStatus === 'active' ? "text-orange-600" : "text-green-600"}
                            >
                              {user.overallStatus === 'active' ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  Inactivar
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activar
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction('delete', user.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
              </div>

              {/* Mobile Cards View */}
              <div className="lg:hidden space-y-4">
                {/* Mobile Select All */}
                <div className="flex items-center gap-2 p-3 bg-gray-50 rounded-lg">
                  <Checkbox
                    className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    checked={selectedUsers.length === data.users.length}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-gray-600">
                    Seleccionar todos ({data.users.length})
                  </span>
                </div>

                {data.users.map((user) => (
                  <Card key={user.id} className="hover:shadow-md transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <Checkbox
                            className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                            checked={selectedUsers.includes(user.id)}
                            onCheckedChange={(checked) => handleSelectUser(user.id, checked as boolean)}
                          />
                          <Avatar className="h-10 w-10">
                            <AvatarImage src={user.image} />
                            <AvatarFallback>
                              {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                            </AvatarFallback>
                          </Avatar>
                          <div className="flex-1 min-w-0">
                            <p className="font-semibold text-gray-900 truncate">
                              {user.firstName} {user.lastName}
                            </p>
                            <p className="text-sm text-gray-500 truncate">
                              {user.email}
                            </p>
                          </div>
                        </div>
                        
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-48">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction('view', user.id)}
                              className="cursor-pointer"
                            >
                              <Eye className="mr-2 h-4 w-4" />
                              Ver detalles
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction('edit', user.id)}
                              className="cursor-pointer"
                            >
                              <Edit className="mr-2 h-4 w-4" />
                              Editar
                            </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                              onClick={() => handleUserAction('toggle-status', user.id)}
                              className={`cursor-pointer ${user.overallStatus === 'active' ? 'text-orange-600' : 'text-green-600'}`}
                            >
                              {user.overallStatus === 'active' ? (
                                <>
                                  <UserX className="mr-2 h-4 w-4" />
                                  Inactivar
                                </>
                              ) : (
                                <>
                                  <UserCheck className="mr-2 h-4 w-4" />
                                  Activar
                                </>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleUserAction('delete', user.id)}
                              className="cursor-pointer text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Estado:</span>
                          <Badge className={statusColors[user.overallStatus]}>
                            {statusLabels[user.overallStatus] || user.overallStatus}
                          </Badge>
                        </div>
                        
                        {user.emailVerified && (
                          <div className="flex items-center justify-between">
                            <span className="text-xs text-gray-500">Email:</span>
                            <Badge variant="outline" className="text-xs border-green-300 text-green-700">
                              Verificado
                            </Badge>
                          </div>
                        )}

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Documento:</span>
                          <span className="text-xs text-gray-700">{user.documentType}: {user.documentNumber}</span>
                        </div>

                        {user.roles.length > 0 && (
                          <div className="flex items-start justify-between">
                            <span className="text-xs text-gray-500">Roles:</span>
                            <div className="flex flex-wrap gap-1 max-w-[60%]">
                              {user.roles.map((role) => (
                                <Badge key={role.id} variant="outline" className="text-xs border-gray-300">
                                  {role.role}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        )}

                        <div className="flex items-center justify-between pt-1 border-t border-gray-100">
                          <span className="text-xs text-gray-500">Creado:</span>
                          <span className="text-xs text-gray-700">
                            {format(new Date(user.createdAt), 'dd MMM yyyy', { locale: es })}
                          </span>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Paginación */}
      {data && data.pagination.totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-500">
              Mostrando {data.pagination.from} a {data.pagination.to} de {data.pagination.total} resultados
            </span>
            <Select
              value={limit.toString()}
              onValueChange={(value) => {
                setLimit(parseInt(value));
                setPage(1);
              }}
            >
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            <div className="flex items-center gap-2">
              {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === data.pagination.totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === data.pagination.totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}

      {/* Diálogo de Confirmación de Eliminación */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Confirmar Eliminación de Usuario
            </DialogTitle>
          </DialogHeader>
          
          {deleteAnalysis && (
            <div className="space-y-4">
              {/* Información del Usuario */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Usuario a eliminar:</h4>
                <div className="text-sm text-gray-700 space-y-1">
                  <p><strong>Nombre:</strong> {deleteAnalysis.user?.name}</p>
                  <p><strong>Email:</strong> {deleteAnalysis.user?.email}</p>
                </div>
              </div>

              {/* Análisis de Impacto */}
              {deleteAnalysis.summary?.hasImpact ? (
                <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <h4 className="font-semibold text-amber-800">Impacto de la eliminación</h4>
                  </div>
                  <p className="text-sm text-amber-700 mb-3">
                    Se eliminarán <strong>{deleteAnalysis.summary.totalItems}</strong> registros adicionales:
                  </p>
                  <ul className="text-sm text-amber-700 space-y-1">
                    {deleteAnalysis.summary.itemsList.map((item, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-amber-600">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <h4 className="font-semibold text-green-800">Sin impacto adicional</h4>
                  </div>
                  <p className="text-sm text-green-700">
                    No hay registros adicionales que se vean afectados por esta eliminación.
                  </p>
                </div>
              )}

              {/* Opciones de Eliminación */}
              <div className="space-y-3">
                <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                  <h5 className="font-semibold text-red-800 mb-1">Eliminación Física</h5>
                  <p className="text-sm text-red-700">
                    Elimina completamente de la base de datos y Clerk. 
                    El email podrá ser reutilizado para nuevos registros.
                  </p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg">
                  <h5 className="font-semibold text-blue-800 mb-1">Eliminación Lógica</h5>
                  <p className="text-sm text-blue-700">
                    Solo marca como eliminado. Es reversible y mantiene 
                    la integridad de los datos históricos.
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deletingUser}
            >
              Cancelar
            </Button>
            <Button
              variant="secondary"
              onClick={executeLogicalDeletion}
              disabled={deletingUser}
            >
              {deletingUser ? 'Procesando...' : 'Eliminar Lógicamente'}
            </Button>
            <Button
              variant="destructive"
              onClick={executePhysicalDeletion}
              disabled={deletingUser}
            >
              {deletingUser ? 'Procesando...' : 'Eliminar Físicamente'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal Expandido de Detalles con Tabs */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Detalles del Usuario</DialogTitle>
            <DialogDescription>
              Información completa y detallada del usuario
            </DialogDescription>
          </DialogHeader>
          
          {selectedUser && (
            <Tabs defaultValue="general" className="flex-1 flex flex-col">
              {/* Tabs responsivos */}
              <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-4">
                <TabsTrigger value="general">General</TabsTrigger>
                <TabsTrigger value="contact">Contacto</TabsTrigger>
                <TabsTrigger value="roles">Roles</TabsTrigger>
                <TabsTrigger value="activity">Actividad</TabsTrigger>
              </TabsList>
              
              {/* Contenido scrolleable */}
              <div className="flex-1 overflow-y-auto px-1">
                <TabsContent value="general" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg flex items-center gap-3">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={selectedUser.image || undefined} />
                          <AvatarFallback className="bg-gray-100 text-gray-600 text-xl">
                            {selectedUser.firstName?.charAt(0)}{selectedUser.lastName?.charAt(0)}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="text-xl font-semibold">{selectedUser.firstName} {selectedUser.lastName}</div>
                          <div className="text-sm text-gray-500">{selectedUser.email}</div>
                          <div className="flex items-center gap-2 mt-2">
                            <Badge className={statusColors[selectedUser.overallStatus]}>
                              {statusLabels[selectedUser.overallStatus] || selectedUser.overallStatus}
                            </Badge>
                            {selectedUser.emailVerified && (
                              <Badge variant="secondary">Email Verificado</Badge>
                            )}
                          </div>
                        </div>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Tipo de Documento</Label>
                        <p className="text-sm text-gray-900">{selectedUser.documentType || 'No especificado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Número de Documento</Label>
                        <p className="text-sm text-gray-900">{selectedUser.documentNumber || 'No especificado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                        <p className="text-sm text-gray-900">
                          {selectedUser.dateOfBirth 
                            ? formatDate(selectedUser.dateOfBirth)
                            : 'No especificado'}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Género</Label>
                        <p className="text-sm text-gray-900">{selectedUser.gender || 'No especificado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">ID de Usuario</Label>
                        <p className="text-sm text-gray-900 font-mono">{selectedUser.id}</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="contact" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Información de Contacto</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Email</Label>
                        <p className="text-sm text-gray-900">{selectedUser.email}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                        <p className="text-sm text-gray-900">{selectedUser.phone || 'No registrado'}</p>
                      </div>
                      <div className="md:col-span-2">
                        <Label className="text-sm font-medium text-gray-700">Dirección</Label>
                        <p className="text-sm text-gray-900">{selectedUser.address || 'No registrado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono Alternativo</Label>
                        <p className="text-sm text-gray-900">{selectedUser.alternativePhone || 'No registrado'}</p>
                      </div>
                    </CardContent>
                  </Card>
                  
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Contacto de Emergencia</CardTitle>
                    </CardHeader>
                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                        <p className="text-sm text-gray-900">{selectedUser.emergencyContact || 'No registrado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                        <p className="text-sm text-gray-900">{selectedUser.emergencyPhone || 'No registrado'}</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="roles" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Roles y Permisos</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-4">
                        {selectedUser.roles.map((role) => (
                          <div key={role.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                            <div className="flex items-center gap-4">
                              <Shield className="h-6 w-6 text-gray-500" />
                              <div>
                                <p className="font-medium text-lg">{roleLabels[role.role] || role.role}</p>
                                <p className="text-sm text-gray-500">
                                  Creado el {formatDate(role.createdAt)}
                                </p>
                                {role.medicalLicense && (
                                  <p className="text-sm text-blue-600">
                                    Licencia: {role.medicalLicense}
                                  </p>
                                )}
                              </div>
                            </div>
                            <Badge className={statusColors[role.status]}>
                              {statusLabels[role.status] || role.status}
                            </Badge>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
                
                <TabsContent value="activity" className="space-y-4 mt-0">
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Información de Registro</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Fecha de Registro</Label>
                        <p className="text-sm text-gray-900">
                          {format(new Date(selectedUser.createdAt), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es })}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Última Actualización</Label>
                        <p className="text-sm text-gray-900">
                          {format(new Date(selectedUser.updatedAt), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es })}
                        </p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">ID de Usuario</Label>
                        <p className="text-sm text-gray-900 font-mono bg-gray-100 p-2 rounded">{selectedUser.id}</p>
                      </div>
                    </CardContent>
                  </Card>
                </TabsContent>
              </div>
            </Tabs>
          )}
          
          <DialogFooter className="border-t pt-4">
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              Cerrar
            </Button>
            <Button onClick={() => {
              setIsDetailsDialogOpen(false);
              if (selectedUser) openEditDialog(selectedUser);
            }}>
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}