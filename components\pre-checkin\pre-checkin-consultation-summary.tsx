'use client';

import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle,
  XCircle,
  FileText,
  ExternalLink,
  Clock
} from 'lucide-react';

interface PreCheckinConsultationSummaryProps {
  preCheckinData: any;
  appointmentInfo?: any;
}

const DOCUMENT_TYPES = {
  lab_results: 'Exámenes de Laboratorio',
  imaging: 'Estudios de Imagen',
  prescription: 'Recetas Médicas',
  insurance: 'Documentos de Seguro',
  other: 'Otros Documentos'
} as const;

export function PreCheckinConsultationSummary({
  preCheckinData,
  appointmentInfo
}: PreCheckinConsultationSummaryProps) {
  
  
  const openDocument = (url: string) => {
    window.open(url, '_blank');
  };

  if (!preCheckinData) {
    return (
      <Card className="border-[#FAC9D1]/60 bg-[#FAC9D1]/20">
        <CardContent className="text-center py-8">
          <XCircle className="h-12 w-12 text-[#ea6cb0] mx-auto mb-4" />
          <h3 className="text-lg font-medium text-[#3D4E80] mb-2">Pre-checkin no completado</h3>
          <p className="text-[#3D4E80]/70">
            El paciente no completó el pre-checkin antes de la consulta
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {/* Resumen completo en formato de texto estructurado */}
      <Card className="border-[#50bed2]/30 bg-[#50bed2]/10">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2 text-[#3D4E80]">
            <FileText className="h-5 w-5 text-[#50bed2]" />
            Resumen Completo del Pre-checkin
          </CardTitle>
          <CardDescription className="text-[#3D4E80]/70">
            Información proporcionada por el paciente antes de la consulta
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="bg-white rounded-lg p-4 border border-[#50bed2]/30 font-mono text-sm leading-relaxed">
            <div className="space-y-3 text-gray-700">
              {/* Estado de confirmación */}
              <div className="pb-2 border-b border-gray-100">
                <span className="font-semibold text-gray-900">CONFIRMACIÓN DE ASISTENCIA:</span>
                <br />
                {(preCheckinData.willAttend || preCheckinData.attendance) === 'yes' 
                  ? '✅ El paciente CONFIRMÓ su asistencia a la cita.' 
                  : preCheckinData.attendance === 'no'
                  ? '❌ El paciente NO podrá asistir a la cita.'
                  : '⚠️ Estado de confirmación no disponible.'}
              </div>

              {/* Información de contacto */}
              {(preCheckinData.phone || preCheckinData.contactInfo?.phone || 
                preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact) && (
                <div className="pb-2 border-b border-gray-100">
                  <span className="font-semibold text-gray-900">INFORMACIÓN DE CONTACTO:</span>
                  <br />
                  {(preCheckinData.phone || preCheckinData.contactInfo?.phone) && (
                    <>• Teléfono: {preCheckinData.phone || preCheckinData.contactInfo?.phone}<br /></>
                  )}
                  {(preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact) && (
                    <>• Contacto de emergencia: {preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact}
                    {(preCheckinData.emergencyPhone || preCheckinData.contactInfo?.emergencyPhone) && 
                      ` - Tel: ${preCheckinData.emergencyPhone || preCheckinData.contactInfo?.emergencyPhone}`}<br /></>
                  )}
                </div>
              )}

              {/* Síntomas */}
              <div className="pb-2 border-b border-gray-100">
                <span className="font-semibold text-gray-900">SÍNTOMAS ACTUALES:</span>
                <br />
                {preCheckinData.hasSymptoms || preCheckinData.symptoms ? (
                  <>⚠️ El paciente reporta los siguientes síntomas:<br />
                  <span className="ml-4 italic">"{preCheckinData.symptoms}"</span></>
                ) : (
                  '✅ El paciente NO presenta síntomas actualmente.'
                )}
              </div>

              {/* Medicamentos */}
              <div className="pb-2 border-b border-gray-100">
                <span className="font-semibold text-gray-900">MEDICAMENTOS:</span>
                <br />
                {preCheckinData.takingMedications || preCheckinData.medications ? (
                  <>💊 El paciente está tomando los siguientes medicamentos:<br />
                  <span className="ml-4 italic">"{preCheckinData.medications}"</span></>
                ) : (
                  '✅ El paciente NO está tomando medicamentos actualmente.'
                )}
              </div>

              {/* Alergias */}
              <div className="pb-2 border-b border-gray-100">
                <span className="font-semibold text-gray-900">ALERGIAS:</span>
                <br />
                {preCheckinData.hasAllergies || preCheckinData.allergies ? (
                  <>⚠️ IMPORTANTE - El paciente tiene las siguientes alergias:<br />
                  <span className="ml-4 italic text-orange-600 font-semibold">"{preCheckinData.allergies}"</span></>
                ) : (
                  '✅ El paciente NO reporta alergias conocidas.'
                )}
              </div>

              {/* Información del acompañante */}
              {preCheckinData.isDependent && preCheckinData.companionInfo && (
                <div className="pb-2 border-b border-gray-100">
                  <span className="font-semibold text-gray-900">ACOMPAÑANTE DEL PACIENTE:</span>
                  <br />
                  {preCheckinData.companionInfo.name && (
                    <>• Nombre: {preCheckinData.companionInfo.name}<br /></>
                  )}
                  {preCheckinData.companionInfo.relationship && (
                    <>• Relación: {preCheckinData.companionInfo.relationship}<br /></>
                  )}
                  {preCheckinData.companionInfo.phone && (
                    <>• Teléfono: {preCheckinData.companionInfo.phone}<br /></>
                  )}
                </div>
              )}

              {/* Motivo de consulta */}
              {preCheckinData.chiefComplaint && (
                <div className="pb-2 border-b border-gray-100">
                  <span className="font-semibold text-gray-900">MOTIVO DE CONSULTA:</span>
                  <br />
                  <span className="italic">"{preCheckinData.chiefComplaint}"</span>
                </div>
              )}

              {/* Documentos */}
              {preCheckinData.documents && preCheckinData.documents.length > 0 && (
                <div className="pb-2 border-b border-gray-100 bg-yellow-50 -mx-4 px-4 py-2 rounded">
                  <span className="font-semibold text-gray-900">📄 DOCUMENTOS ADJUNTOS:</span>
                  <br />
                  <span className="text-blue-700 font-medium">🔍 {preCheckinData.documents.length} documento(s) disponible(s) para revisión:</span>
                  {preCheckinData.documents.map((doc: any, index: number) => (
                    <div key={index} className="ml-4 mt-1">
                      • <span className="font-medium text-blue-800">{DOCUMENT_TYPES[doc.type as keyof typeof DOCUMENT_TYPES] || 'Documento'}</span>: {doc.filename}
                      {doc.description && <span className="text-gray-600"> - {doc.description}</span>}
                    </div>
                  ))}
                  <div className="mt-2 text-xs text-blue-600 font-medium">
                    ↓ Ver sección "Documentos del Paciente" abajo para acceder a los archivos
                  </div>
                </div>
              )}

              {/* Observaciones adicionales */}
              {preCheckinData.additionalNotes && (
                <div className="pb-2">
                  <span className="font-semibold text-gray-900">OBSERVACIONES ADICIONALES:</span>
                  <br />
                  <span className="italic">"{preCheckinData.additionalNotes}"</span>
                </div>
              )}

              {/* Fecha de completado */}
              {preCheckinData.completedAt && (
                <div className="pt-2 border-t border-gray-200 text-xs text-gray-500">
                  Pre-checkin completado el {new Date(preCheckinData.completedAt).toLocaleDateString('es-GT', {
                    day: 'numeric',
                    month: 'long',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documentos adjuntos con botones de descarga */}
      {preCheckinData.documents && preCheckinData.documents.length > 0 && (
        <Card className="border-[#50bed2]/30 bg-[#50bed2]/10 shadow-md">
          <CardHeader className="pb-3 bg-[#50bed2]/5">
            <CardTitle className="text-lg flex items-center gap-2 text-[#3D4E80]">
              <FileText className="h-5 w-5 text-[#50bed2]" />
              📄 Documentos del Paciente
              <Badge variant="secondary" className="ml-2 bg-[#FCEEA8]/50 text-[#3D4E80]">
                {preCheckinData.documents.length} archivo{preCheckinData.documents.length !== 1 ? 's' : ''}
              </Badge>
            </CardTitle>
            <CardDescription className="text-[#3D4E80]/70">
              Documentos adjuntados por el paciente durante el pre-checkin - Haga clic para abrir
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {preCheckinData.documents.map((doc: any, index: number) => (
                <Button
                  key={index}
                  variant="outline"
                  size="default"
                  onClick={() => openDocument(doc.url)}
                  className="justify-start text-left h-auto p-3 border-[#ADB6CA] text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2] hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start gap-3 w-full">
                    <ExternalLink className="h-4 w-4 mt-0.5 text-[#50bed2] flex-shrink-0" />
                    <div className="flex-1 text-left">
                      <div className="font-medium text-sm text-[#3D4E80]">
                        {DOCUMENT_TYPES[doc.type as keyof typeof DOCUMENT_TYPES] || 'Documento'}
                      </div>
                      <div className="text-xs text-gray-600 truncate">
                        {doc.filename}
                      </div>
                      {doc.description && (
                        <div className="text-xs text-gray-500 mt-1">
                          {doc.description}
                        </div>
                      )}
                    </div>
                  </div>
                </Button>
              ))}
            </div>
            <div className="mt-4 p-3 bg-[#FCEEA8]/30 rounded-lg border border-[#FCEEA8]/50">
              <p className="text-xs text-[#3D4E80]/80 flex items-center gap-2">
                <Clock className="h-3 w-3" />
                Documentos adjuntados durante el pre-checkin. Pueden incluir exámenes previos, recetas, o información relevante para la consulta.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}