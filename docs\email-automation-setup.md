# Configuración de Automatización de Emails

## Resumen del Sistema

El sistema ahora maneja el envío de emails de pre-checkin y recordatorios de forma automatizada:

### 1. **Al crear una cita**
- Solo se envía email de **activación de cuenta** (si es necesario)
- El pre-checkin NO se envía inmediatamente

### 2. **48 horas antes de la cita**
- Se envía el **email de pre-checkin** automáticamente
- Endpoint: `POST /api/cron/send-precheckin`

### 3. **24 horas antes de la cita**
- Se envía el **recordatorio final** automáticamente
- Endpoint: `POST /api/cron/send-reminders`

## Configuración de Cron Jobs

### Variables de Entorno

Añadir a tu archivo `.env.local`:

```bash
# Secret para autenticar cron jobs
CRON_SECRET=tu-secret-key-muy-segura

# Configuración de email (ya existente)
RESEND_API_KEY=tu-resend-api-key
RESEND_FROM_EMAIL=<EMAIL>
```

### Opción 1: Cron Jobs del Sistema (Linux/macOS)

Ejecutar `crontab -e` y añadir:

```bash
# Enviar pre-checkin emails cada hora
0 * * * * curl -X POST -H "Authorization: Bearer tu-secret-key-muy-segura" http://localhost:3000/api/cron/send-precheckin

# Enviar recordatorios 24h cada hora
0 * * * * curl -X POST -H "Authorization: Bearer tu-secret-key-muy-segura" http://localhost:3000/api/cron/send-reminders
```

### Opción 2: Vercel Cron Jobs (Recomendado para producción)

Crear archivo `vercel.json` en la raíz del proyecto:

```json
{
  "crons": [
    {
      "path": "/api/cron/send-precheckin",
      "schedule": "0 * * * *"
    },
    {
      "path": "/api/cron/send-reminders", 
      "schedule": "0 * * * *"
    }
  ]
}
```

### Opción 3: Servicios de Cron Externos

**Cron-job.org:**
1. Crear cuenta en cron-job.org
2. Configurar jobs:
   - URL: `https://tudominio.com/api/cron/send-precheckin`
   - Method: POST
   - Headers: `Authorization: Bearer tu-secret-key`
   - Schedule: Cada hora

**GitHub Actions (si usas GitHub):**
Crear `.github/workflows/email-cron.yml`:

```yaml
name: Email Cron Jobs
on:
  schedule:
    - cron: '0 * * * *'  # Cada hora
jobs:
  send-emails:
    runs-on: ubuntu-latest
    steps:
      - name: Send Pre-checkin Emails
        run: |
          curl -X POST -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
               ${{ secrets.APP_URL }}/api/cron/send-precheckin
      - name: Send Reminder Emails
        run: |
          curl -X POST -H "Authorization: Bearer ${{ secrets.CRON_SECRET }}" \
               ${{ secrets.APP_URL }}/api/cron/send-reminders
```

## Testing Manual

Para probar los endpoints manualmente:

```bash
# Test pre-checkin
curl -X POST \
  -H "Authorization: Bearer tu-secret-key" \
  http://localhost:3000/api/cron/send-precheckin

# Test recordatorios
curl -X POST \
  -H "Authorization: Bearer tu-secret-key" \
  http://localhost:3000/api/cron/send-reminders
```

## Logs y Monitoreo

Los endpoints generan logs detallados:
- `📧 Enviando pre-checkin para cita X...`
- `✅ Pre-checkin enviado exitosamente`
- `❌ Error enviando pre-checkin`

Revisa los logs de tu aplicación para monitorear el funcionamiento.

## Notas Importantes

1. **Frecuencia**: Los cron jobs se ejecutan cada hora para capturar todas las citas
2. **Duplicados**: El sistema previene envío duplicado verificando flags en la BD
3. **Fallos**: Si un email falla, se registra pero no bloquea otros envíos
4. **Seguridad**: Usa un CRON_SECRET fuerte y único
5. **Zona Horaria**: Los cálculos usan la hora del servidor

## Troubleshooting

**Emails no se envían:**
- Verificar RESEND_API_KEY
- Revisar logs del servidor
- Confirmar que el cron job se ejecuta

**Timing incorrecto:**
- Verificar zona horaria del servidor
- Ajustar cálculos de tiempo si es necesario

**Emails duplicados:**
- Verificar que los flags en BD se actualizan correctamente
- No ejecutar cron jobs con demasiada frecuencia