import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  user, 
  userRoles,
  registrationRequests,
  appointments,
  appointmentSlots,
  medicalRecords,
  medicalConsultations,
  patientMedicalHistory,
  medicalDocuments,
  guardianPatientRelations,
  patientInvitations,
  doctorSchedules,
  doctorScheduleExceptions,
  doctorServicePrices,
  doctorFavoriteMedications,
  prescriptionTemplates,
  assistantDoctorRelations,
  associationCodes,
  notifications,
  adminAuditLogs
} from '@/db/schema';
import { generateId } from '@/lib/utils';
import { eq, and, ne } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const adminUser = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: userRoles.role
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, userId),
          eq(userRoles.role, 'admin'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (adminUser.length === 0) {
      return NextResponse.json(
        { error: 'Solo administradores pueden ejecutar esta operación' },
        { status: 403 }
      );
    }

    const admin = adminUser[0];

    const { confirmationPhrase, confirmDelete } = await request.json();

    if (confirmationPhrase !== 'BORRAR TODA LA INFORMACION MEDICA') {
      return NextResponse.json(
        { error: 'Frase de confirmación incorrecta' },
        { status: 400 }
      );
    }

    if (!confirmDelete) {
      return NextResponse.json(
        { error: 'Debe confirmar la operación de borrado' },
        { status: 400 }
      );
    }

    console.log(`🚨 INICIO DE LIMPIEZA DE BASE DE DATOS - Ejecutado por: ${admin.firstName} ${admin.lastName} (${admin.id})`);

    // Contar registros antes del borrado
    const countsBefore = {
      registrationRequests: await db.select().from(registrationRequests).then(r => r.length),
      appointments: await db.select().from(appointments).then(r => r.length),
      appointmentSlots: await db.select().from(appointmentSlots).then(r => r.length),
      medicalRecords: await db.select().from(medicalRecords).then(r => r.length),
      medicalConsultations: await db.select().from(medicalConsultations).then(r => r.length),
      patientMedicalHistory: await db.select().from(patientMedicalHistory).then(r => r.length),
      medicalDocuments: await db.select().from(medicalDocuments).then(r => r.length),
      guardianRelations: await db.select().from(guardianPatientRelations).then(r => r.length),
      patientInvitations: await db.select().from(patientInvitations).then(r => r.length),
      doctorSchedules: await db.select().from(doctorSchedules).then(r => r.length),
      doctorScheduleExceptions: await db.select().from(doctorScheduleExceptions).then(r => r.length),
      doctorServicePrices: await db.select().from(doctorServicePrices).then(r => r.length),
      doctorFavoriteMedications: await db.select().from(doctorFavoriteMedications).then(r => r.length),
      prescriptionTemplates: await db.select().from(prescriptionTemplates).then(r => r.length),
      assistantDoctorRelations: await db.select().from(assistantDoctorRelations).then(r => r.length),
      associationCodes: await db.select().from(associationCodes).then(r => r.length),
      notifications: await db.select().from(notifications).then(r => r.length)
    };

    const usersToDelete = await db
      .select({
        userId: user.id,
        role: userRoles.role
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(ne(userRoles.role, 'admin'));

    const usersByRole = (usersToDelete || []).reduce((acc: Record<string, number>, user) => {
      acc[user.role] = (acc[user.role] || 0) + 1;
      return acc;
    }, {});

    console.log('📊 Registros a eliminar:', {
      ...countsBefore,
      usersByRole
    });

    // PASO 1: Eliminar documentos médicos
    console.log('🗑️ Eliminando documentos médicos...');
    await db.delete(medicalDocuments);

    // PASO 2: Eliminar historia médica de pacientes
    console.log('🗑️ Eliminando historia médica de pacientes...');
    await db.delete(patientMedicalHistory);

    // PASO 3: Eliminar consultas médicas
    console.log('🗑️ Eliminando consultas médicas...');
    await db.delete(medicalConsultations);

    // PASO 4: Eliminar expedientes médicos
    console.log('🗑️ Eliminando expedientes médicos...');
    await db.delete(medicalRecords);

    // PASO 5: Eliminar slots de citas
    console.log('🗑️ Eliminando slots de citas...');
    await db.delete(appointmentSlots);

    // PASO 6: Eliminar citas médicas
    console.log('🗑️ Eliminando citas médicas...');
    await db.delete(appointments);

    // PASO 7: Eliminar relaciones guardian-paciente
    console.log('🗑️ Eliminando relaciones guardian-paciente...');
    await db.delete(guardianPatientRelations);

    // PASO 8: Eliminar invitaciones de pacientes
    console.log('🗑️ Eliminando invitaciones de pacientes...');
    await db.delete(patientInvitations);

    // PASO 9: Eliminar horarios de doctores
    console.log('🗑️ Eliminando horarios de doctores...');
    await db.delete(doctorScheduleExceptions);
    await db.delete(doctorSchedules);

    // PASO 10: Eliminar precios de servicios de doctores
    console.log('🗑️ Eliminando precios de servicios...');
    await db.delete(doctorServicePrices);

    // PASO 11: Eliminar medicamentos favoritos
    console.log('🗑️ Eliminando medicamentos favoritos...');
    await db.delete(doctorFavoriteMedications);

    // PASO 12: Eliminar plantillas de prescripción
    console.log('🗑️ Eliminando plantillas de prescripción...');
    await db.delete(prescriptionTemplates);

    // PASO 13: Eliminar relaciones asistente-doctor
    console.log('🗑️ Eliminando relaciones asistente-doctor...');
    await db.delete(assistantDoctorRelations);

    // PASO 14: Eliminar códigos de asociación
    console.log('🗑️ Eliminando códigos de asociación...');
    await db.delete(associationCodes);

    // PASO 15: Eliminar notificaciones
    console.log('🗑️ Eliminando notificaciones...');
    await db.delete(notifications);

    // PASO 16: Eliminar solicitudes de registro
    console.log('🗑️ Eliminando solicitudes de registro...');
    await db.delete(registrationRequests);

    // PASO 17: Obtener usuarios no admin para eliminar de Clerk
    console.log('📋 Obteniendo usuarios no admin para eliminar de Clerk...');
    const usersToDeleteFromClerk = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        role: userRoles.role
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(ne(userRoles.role, 'admin'));

    console.log(`📊 ${usersToDeleteFromClerk.length} usuarios serán eliminados de Clerk y base de datos`);

    // PASO 18: Eliminar usuarios de Clerk
    console.log('🗑️ Eliminando usuarios de Clerk...');
    const clerkDeletionResults = [];
    
    for (const userToDelete of usersToDeleteFromClerk) {
      try {
        console.log(`🗑️ Eliminando de Clerk: ${userToDelete.firstName} ${userToDelete.lastName} (${userToDelete.id})`);
        await clerkClient.users.deleteUser(userToDelete.id);
        clerkDeletionResults.push({
          success: true,
          user: `${userToDelete.firstName} ${userToDelete.lastName}`,
          clerkUserId: userToDelete.id
        });
      } catch (error) {
        console.error(`❌ Error eliminando usuario de Clerk ${userToDelete.id}:`, error);
        clerkDeletionResults.push({
          success: false,
          user: `${userToDelete.firstName} ${userToDelete.lastName}`,
          clerkUserId: userToDelete.id,
          error: error instanceof Error ? error.message : 'Error desconocido'
        });
      }
    }

    // PASO 19: Eliminar roles de usuarios no admin
    console.log('🗑️ Eliminando roles de usuarios no admin...');
    await db.delete(userRoles).where(ne(userRoles.role, 'admin'));

    // PASO 20: Eliminar usuarios no admin de base de datos
    console.log('🗑️ Eliminando usuarios no admin de base de datos...');
    const adminIds = await db
      .select({ id: user.id })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(eq(userRoles.role, 'admin'));

    const adminUserIds = adminIds.map(admin => admin.id);
    
    for (const adminId of adminUserIds) {
      await db.delete(user).where(ne(user.id, adminId));
    }

    // Contar registros después del borrado
    const countsAfter = {
      registrationRequests: await db.select().from(registrationRequests).then(r => r.length),
      appointments: await db.select().from(appointments).then(r => r.length),
      appointmentSlots: await db.select().from(appointmentSlots).then(r => r.length),
      medicalRecords: await db.select().from(medicalRecords).then(r => r.length),
      medicalConsultations: await db.select().from(medicalConsultations).then(r => r.length),
      patientMedicalHistory: await db.select().from(patientMedicalHistory).then(r => r.length),
      medicalDocuments: await db.select().from(medicalDocuments).then(r => r.length),
      guardianRelations: await db.select().from(guardianPatientRelations).then(r => r.length),
      patientInvitations: await db.select().from(patientInvitations).then(r => r.length),
      doctorSchedules: await db.select().from(doctorSchedules).then(r => r.length),
      doctorScheduleExceptions: await db.select().from(doctorScheduleExceptions).then(r => r.length),
      doctorServicePrices: await db.select().from(doctorServicePrices).then(r => r.length),
      doctorFavoriteMedications: await db.select().from(doctorFavoriteMedications).then(r => r.length),
      prescriptionTemplates: await db.select().from(prescriptionTemplates).then(r => r.length),
      assistantDoctorRelations: await db.select().from(assistantDoctorRelations).then(r => r.length),
      associationCodes: await db.select().from(associationCodes).then(r => r.length),
      notifications: await db.select().from(notifications).then(r => r.length)
    };

    const remainingUsers = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: userRoles.role
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId));

    console.log('✅ LIMPIEZA COMPLETADA');
    console.log('📊 Registros después:', countsAfter);
    console.log('👤 Usuarios restantes:', remainingUsers);

    const clerkStats = {
      totalProcessed: clerkDeletionResults.length,
      successful: clerkDeletionResults.filter(r => r.success).length,
      failed: clerkDeletionResults.filter(r => !r.success).length,
      withoutClerkId: clerkDeletionResults.filter(r => r.note).length
    };

    const auditLog = {
      action: 'DATABASE_RESET',
      executedBy: {
        id: admin.id,
        name: `${admin.firstName} ${admin.lastName}`
      },
      timestamp: new Date().toISOString(),
      recordsDeleted: {
        before: countsBefore,
        after: countsAfter,
        usersByRole
      },
      clerkDeletion: {
        stats: clerkStats,
        results: clerkDeletionResults
      },
      remainingUsers: remainingUsers.length
    };

    console.log('📝 Audit Log:', JSON.stringify(auditLog, null, 2));

    try {
      await db.insert(adminAuditLogs).values({
        id: generateId(),
        action: 'DATABASE_RESET',
        executedBy: admin.id,
        executedByName: `${admin.firstName} ${admin.lastName}`,
        timestamp: new Date(),
        details: auditLog,
        success: true,
        ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
        userAgent: request.headers.get('user-agent') || 'unknown',
        createdAt: new Date(),
        updatedAt: new Date()
      });
      console.log('✅ Log de auditoría guardado en base de datos');
    } catch (logError) {
      console.error('⚠️ Error guardando log de auditoría:', logError);
    }

    return NextResponse.json({
      success: true,
      message: 'Base de datos y usuarios de Clerk limpiados exitosamente',
      summary: {
        recordsDeleted: countsBefore ? Object.entries(countsBefore).reduce((total, [key, count]) => total + count, 0) : 0,
        usersDeleted: usersByRole ? Object.values(usersByRole).reduce((total, count) => total + count, 0) : 0,
        clerkUsersDeleted: clerkStats.successful,
        clerkDeletionErrors: clerkStats.failed,
        remainingUsers: remainingUsers.length,
        executedBy: `${admin.firstName} ${admin.lastName}`,
        executedAt: new Date().toISOString()
      },
      details: {
        database: {
          before: countsBefore,
          after: countsAfter,
          usersByRole
        },
        clerk: {
          stats: clerkStats,
          results: clerkDeletionResults
        },
        remainingUsers
      }
    });

  } catch (error) {
    console.error('❌ Error en limpieza de base de datos:', error);
    
    try {
      const { userId } = await auth();
      const adminData = await db
        .select({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName
        })
        .from(user)
        .innerJoin(userRoles, eq(user.id, userRoles.userId))
        .where(
          and(
            eq(user.id, userId || ''),
            eq(userRoles.role, 'admin')
          )
        )
        .limit(1);

      if (adminData.length > 0) {
        const admin = adminData[0];
        await db.insert(adminAuditLogs).values({
          id: generateId(),
          action: 'DATABASE_RESET',
          executedBy: admin.id,
          executedByName: `${admin.firstName} ${admin.lastName}`,
          timestamp: new Date(),
          details: {
            error: error instanceof Error ? error.message : 'Error desconocido',
            stack: error instanceof Error ? error.stack : undefined
          },
          success: false,
          errorMessage: error instanceof Error ? error.message : 'Error desconocido',
          ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
          userAgent: request.headers.get('user-agent') || 'unknown',
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
    } catch (logError) {
      console.error('⚠️ Error guardando log de error:', logError);
    }

    return NextResponse.json(
      { 
        error: 'Error interno durante la limpieza de base de datos',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const adminCheck = await db
      .select()
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, userId),
          eq(userRoles.role, 'admin')
        )
      )
      .limit(1);

    if (adminCheck.length === 0) {
      return NextResponse.json(
        { error: 'Solo administradores pueden acceder a esta información' },
        { status: 403 }
      );
    }

    const currentCounts = {
      registrationRequests: await db.select().from(registrationRequests).then(r => r.length),
      appointments: await db.select().from(appointments).then(r => r.length),
      appointmentSlots: await db.select().from(appointmentSlots).then(r => r.length),
      medicalRecords: await db.select().from(medicalRecords).then(r => r.length),
      medicalConsultations: await db.select().from(medicalConsultations).then(r => r.length),
      patientMedicalHistory: await db.select().from(patientMedicalHistory).then(r => r.length),
      medicalDocuments: await db.select().from(medicalDocuments).then(r => r.length),
      guardianRelations: await db.select().from(guardianPatientRelations).then(r => r.length),
      patientInvitations: await db.select().from(patientInvitations).then(r => r.length),
      doctorSchedules: await db.select().from(doctorSchedules).then(r => r.length),
      doctorScheduleExceptions: await db.select().from(doctorScheduleExceptions).then(r => r.length),
      doctorServicePrices: await db.select().from(doctorServicePrices).then(r => r.length),
      doctorFavoriteMedications: await db.select().from(doctorFavoriteMedications).then(r => r.length),
      prescriptionTemplates: await db.select().from(prescriptionTemplates).then(r => r.length),
      assistantDoctorRelations: await db.select().from(assistantDoctorRelations).then(r => r.length),
      associationCodes: await db.select().from(associationCodes).then(r => r.length),
      notifications: await db.select().from(notifications).then(r => r.length)
    };

    const allUsers = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: userRoles.role,
        status: userRoles.status
      })
      .from(user)
      .leftJoin(userRoles, eq(user.id, userRoles.userId));

    const usersByRole = allUsers.reduce((acc: Record<string, number>, user) => {
      const role = user.role || 'sin_rol';
      acc[role] = (acc[role] || 0) + 1;
      return acc;
    }, {});

    const totalRecords = Object.values(currentCounts).reduce((total, count) => total + count, 0);
    const totalUsers = Object.values(usersByRole).reduce((total, count) => total + count, 0);
    const usersToDelete = totalUsers - (usersByRole.admin || 0);

    return NextResponse.json({
      success: true,
      preview: {
        totalRecords,
        totalUsers,
        usersToDelete,
        recordsToDelete: totalRecords,
        willRemain: {
          admins: usersByRole.admin || 0,
          catalogs: 'Todos los catálogos se mantendrán'
        }
      },
      details: {
        currentRecords: currentCounts,
        usersByRole
      }
    });

  } catch (error) {
    console.error('Error obteniendo preview:', error);
    return NextResponse.json(
      { error: 'Error obteniendo información' },
      { status: 500 }
    );
  }
}