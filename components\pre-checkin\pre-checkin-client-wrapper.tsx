'use client';

import { useUser } from '@clerk/nextjs';
import { PreCheckinForm } from '@/components/pre-checkin/pre-checkin-form';
import { PreCheckinStandalone } from '@/components/pre-checkin/pre-checkin-standalone';
import { PreCheckinAuthenticated } from '@/components/pre-checkin/pre-checkin-authenticated';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Shield, Loader2 } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface PreCheckinClientWrapperProps {
  appointmentData: {
    id: string;
    title: string | null;
    scheduledDate: Date;
    startTime: Date;
    endTime: Date;
    status: string;
    preCheckinCompleted: boolean | null;
    preCheckinCompletedAt: Date | null;
    patientId: string | null;
    patientFirstName: string | null;
    patientLastName: string | null;
    patientEmail: string | null;
    doctorFirstName: string | null;
    doctorLastName: string | null;
    isDependent: boolean;
    guardianInfo?: {
      guardianId: string;
      relationship: string;
      guardianFirstName: string | null;
      guardianLastName: string | null;
      guardianEmail: string | null;
    } | null;
  };
  appointmentId: string;
  token: string;
  forPatientId?: string;
}

export function PreCheckinClientWrapper({ 
  appointmentData, 
  appointmentId, 
  token, 
  forPatientId 
}: PreCheckinClientWrapperProps) {
  const { user, isLoaded } = useUser();

  // Mostrar loading mientras carga la información de autenticación
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="flex items-center gap-3">
          <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-gray-700">Cargando...</span>
        </div>
      </div>
    );
  }

  // Determinar si es un usuario autenticado con permisos
  const isAuthenticatedUser = user && (
    // Es el paciente mismo
    user.id === appointmentData.patientId ||
    // Es el guardián del paciente dependiente
    (appointmentData.isDependent && 
     appointmentData.guardianInfo && 
     user.id === appointmentData.guardianInfo.guardianId)
  );

  // Debug logs para troubleshooting
  if (user) {
    console.log('🔍 Pre-checkin Debug:', {
      userId: user.id,
      userEmail: user.emailAddresses?.[0]?.emailAddress,
      patientId: appointmentData.patientId,
      isDependent: appointmentData.isDependent,
      guardianId: appointmentData.guardianInfo?.guardianId,
      isAuthenticatedUser,
      willShowAuthenticatedView: isAuthenticatedUser
    });
  }

  // Formatear fecha y hora
  const appointmentDate = format(new Date(appointmentData.scheduledDate), 'EEEE, d MMMM yyyy', { locale: es });
  const appointmentTime = format(new Date(appointmentData.startTime), 'HH:mm', { locale: es });
  const appointmentEndTime = format(new Date(appointmentData.endTime), 'HH:mm', { locale: es });

  const commonProps = {
    appointmentData,
    appointmentDate,
    appointmentTime,
    appointmentEndTime,
    isAlreadyCompleted: appointmentData.preCheckinCompleted || false,
  };

  if (isAuthenticatedUser) {
    // Usuario autenticado con permisos - mostrar experiencia integrada
    return (
      <PreCheckinAuthenticated 
        {...commonProps}
        user={user}
      />
    );
  } else {
    // Usuario no autenticado o sin permisos - mostrar experiencia standalone
    return (
      <PreCheckinStandalone 
        {...commonProps}
      />
    );
  }
}