'use client';

import { useState, useEffect, useCallback } from 'react';
import { useUser } from '@clerk/nextjs';
import { Calendar, Clock, Plus, Search, Filter, ChevronLeft, ChevronRight, X, BarChart3, CheckCircle, Bot, FileText, AlertCircle, RefreshCw, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, subDays } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatDateInConsultoryTimezone, formatLocalDateFromUTC } from '@/lib/timezone-utils';
import { AppointmentsList } from '@/components/agenda/appointments-list';
import { useRouter } from 'next/navigation';
import { CalendarWeekView } from '@/components/agenda/calendar-week-view';
import { CalendarMonthView } from '@/components/agenda/calendar-month-view';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { RevertConfirmationDialog } from '@/components/ui/revert-confirmation-dialog';
import { NoShowConfirmationDialog } from '@/components/ui/no-show-confirmation-dialog';
import { CalendarDayView } from '@/components/agenda/calendar-day-view';
import { DoctorHeader } from '@/components/agenda/doctor-header';
import { AIAssistantChat } from '@/components/agenda/ai-assistant-chat';
import { toast } from 'sonner';
import { cn, formatDateTime } from '@/lib/utils';

export default function AssistantAgendaPage() {
  const { user } = useUser();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [consultories, setConsultories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month' | 'list'>('day');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [doctorFilter, setDoctorFilter] = useState('all');
  const [consultoryFilter, setConsultoryFilter] = useState('all');
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [stats, setStats] = useState({
    total: 0,
    scheduled: 0,
    confirmed: 0,
    completed: 0,
    cancelled: 0
  });
  const [selectedDoctorInfo, setSelectedDoctorInfo] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showKPIs, setShowKPIs] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [showCreateRecordModal, setShowCreateRecordModal] = useState(false);

  // Cargar doctores
  const fetchDoctors = useCallback(async () => {
    try {
      const response = await fetch('/api/catalogs/doctor-service-prices/doctors');
      const data = await response.json();
      if (response.ok) {
        const doctorsData = data.data || [];
        setDoctors(doctorsData);
        
        // Auto-seleccionar el primer doctor si no hay uno seleccionado
        if (doctorsData.length > 0 && doctorFilter === 'all') {
          setDoctorFilter(doctorsData[0].id);
        }
      }
    } catch (error) {
      console.error('Error al cargar doctores:', error);
    }
  }, [doctorFilter]);

  // Cargar consultorios
  const fetchConsultories = useCallback(async () => {
    try {
      const response = await fetch('/api/catalogs/consultories?isActive=true');
      const data = await response.json();
      if (response.ok) {
        setConsultories(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar consultorios:', error);
    }
  }, []);

  // Obtener información específica del médico seleccionado
  const fetchSelectedDoctorInfo = useCallback(async (doctorId: string) => {
    if (doctorId === 'all') {
      setSelectedDoctorInfo(null);
      return;
    }
    
    try {
      console.log('🔍 Obteniendo información del médico seleccionado:', doctorId);
      
      // Primero buscar en la lista de doctores disponibles
      const doctorFromList = doctors.find(d => d.id === doctorId);
      
      if (doctorFromList) {
        console.log('✅ Doctor encontrado en la lista:', doctorFromList);
        
        // Si es el usuario actual, usar su imagen de Clerk directamente
        const isCurrentUser = user?.id === doctorId;
        const imageUrl = isCurrentUser ? user.imageUrl : doctorFromList.imageUrl;
        
        setSelectedDoctorInfo({
          id: doctorFromList.id,
          firstName: doctorFromList.firstName,
          lastName: doctorFromList.lastName,
          email: doctorFromList.email,
          imageUrl: imageUrl,
          clerkImageUrl: imageUrl,
          specialty: doctorFromList.specialty || 'Medicina General'
        });
        
        console.log('✅ Información del médico seleccionado cargada desde lista');
        console.log('🖼️ Imagen utilizada:', imageUrl);
        console.log('🔍 Es usuario actual:', isCurrentUser);
        return;
      }
      
      // Si no está en la lista, obtener de la API como fallback
      const response = await fetch(`/api/auth/current-user?userId=${doctorId}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        const userInfo = data.data;
        
        setSelectedDoctorInfo({
          id: userInfo.id,
          firstName: userInfo.firstName || userInfo.name?.split(' ')[0] || 'Doctor',
          lastName: userInfo.lastName || userInfo.name?.split(' ')[1] || '',
          email: userInfo.email,
          imageUrl: userInfo.imageUrl,
          clerkImageUrl: userInfo.imageUrl,
          specialty: userInfo.medicalSpecialty || 'Medicina General'
        });
        
        console.log('✅ Información del médico seleccionado cargada desde API:', userInfo);
        console.log('🖼️ Imagen específica de userInfo.imageUrl:', userInfo.imageUrl);
      } else {
        console.error('❌ Error al obtener información del médico:', data);
        setSelectedDoctorInfo(null);
      }
    } catch (error) {
      console.error('Error al obtener información del médico seleccionado:', error);
      setSelectedDoctorInfo(null);
    }
  }, [doctors, user]);

  // Obtener citas
  const fetchAppointments = useCallback(async () => {
    try {
      setLoading(true);
      
      // Calcular rango de fechas según la vista
      let dateFrom, dateTo;
      if (viewMode === 'day') {
        dateFrom = format(currentDate, 'yyyy-MM-dd');
        dateTo = format(currentDate, 'yyyy-MM-dd');
      } else if (viewMode === 'week') {
        dateFrom = format(startOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
        dateTo = format(endOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
      } else if (viewMode === 'month') {
        dateFrom = format(startOfMonth(currentDate), 'yyyy-MM-dd');
        dateTo = format(endOfMonth(currentDate), 'yyyy-MM-dd');
      }

      const params = new URLSearchParams({
        ...(doctorFilter !== 'all' && { doctorId: doctorFilter }),
        ...(consultoryFilter !== 'all' && { consultoryId: consultoryFilter }),
        ...(dateFrom && { dateFrom }),
        ...(dateTo && { dateTo }),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        orderBy: 'scheduledDate',
        orderDirection: 'asc',
        limit: '100',
        _t: Date.now().toString() // Cache busting
      });

      const response = await fetch(`/api/appointments?${params}`);
      const data = await response.json();

      if (response.ok) {
        const appointmentsData = data.data || [];
        console.log('📊 Appointments fetched:', appointmentsData.length);
        
        // Debug: Log ALL appointments with their status
        console.log('🔍 All appointments status:');
        appointmentsData.forEach((apt: any, index: number) => {
          console.log(`${index + 1}. ID: ${apt.id} | Status: ${apt.status} | Patient: ${apt.patientName || `${apt.patientFirstName} ${apt.patientLastName}`} | Title: ${apt.title}`);
        });
        
        setAppointments(appointmentsData);
        if (data.stats?.byStatus) {
          setStats({
            total: data.stats.total || 0,
            scheduled: data.stats.byStatus.scheduled || 0,
            confirmed: data.stats.byStatus.confirmed || 0,
            completed: data.stats.byStatus.completed || 0,
            cancelled: data.stats.byStatus.cancelled || 0
          });
        }
      } else {
        toast.error('Error al cargar las citas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  }, [currentDate, viewMode, searchTerm, statusFilter, doctorFilter, consultoryFilter]);

  useEffect(() => {
    fetchDoctors();
    fetchConsultories();
  }, [fetchDoctors, fetchConsultories]);

  useEffect(() => {
    fetchAppointments();
  }, [fetchAppointments]);

  // Obtener información del médico cuando cambie el filtro
  useEffect(() => {
    if (doctorFilter && doctorFilter !== 'all') {
      fetchSelectedDoctorInfo(doctorFilter);
    } else {
      setSelectedDoctorInfo(null);
    }
  }, [doctorFilter, fetchSelectedDoctorInfo]);

  // Navegación de fechas
  const navigateDate = (direction: 'prev' | 'next') => {
    if (viewMode === 'day') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 1) : addDays(currentDate, 1));
    } else if (viewMode === 'week') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 7) : addDays(currentDate, 7));
    } else if (viewMode === 'month') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 30) : addDays(currentDate, 30));
    }
  };

  // Formato del período actual
  const formatCurrentPeriod = () => {
    if (viewMode === 'day') {
      return format(currentDate, "EEEE, d 'de' MMMM 'de' yyyy", { locale: es });
    } else if (viewMode === 'week') {
      const start = startOfWeek(currentDate, { locale: es });
      const end = endOfWeek(currentDate, { locale: es });
      return `${format(start, "d MMM", { locale: es })} - ${format(end, "d MMM yyyy", { locale: es })}`;
    } else if (viewMode === 'month') {
      return format(currentDate, "MMMM 'de' yyyy", { locale: es });
    }
    return '';
  };

  // Calcular estadísticas para el header
  const getDoctorStats = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const todayAppointments = appointments.filter(app =>
      formatLocalDateFromUTC(app.scheduledDate || app.startTime, 'yyyy-MM-dd') === today
    );
    
    const weekStart = startOfWeek(new Date(), { locale: es });
    const weekEnd = endOfWeek(new Date(), { locale: es });
    const weekAppointments = appointments.filter(app => {
      const appDate = new Date(app.scheduledDate);
      return appDate >= weekStart && appDate <= weekEnd;
    });
    
    const confirmedCount = appointments.filter(app => app.status === 'confirmed').length;
    
    return {
      todayCount: todayAppointments.length,
      weekCount: weekAppointments.length,
      confirmedCount
    };
  };

  // Handlers para las acciones de citas del asistente
  const handleAppointmentView = (appointment: any) => {
    router.push(`/dashboard/assistant/agenda/appointment/${appointment.id}`);
  };

  const handleAppointmentEdit = (appointment: any) => {
    router.push(`/dashboard/assistant/agenda/appointment/${appointment.id}?mode=edit`);
  };

  const handleAppointmentConfirm = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'confirmed' })
      });

      if (response.ok) {
        toast.success('Cita confirmada exitosamente');
        // Refrescar inmediatamente y luego con delay para asegurar sincronización
        fetchAppointments();
        setTimeout(() => {
          fetchAppointments();
        }, 500);
      } else {
        toast.error('Error al confirmar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentCancel = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status: 'cancelled',
          cancellationReason: 'Cancelada por el asistente médico'
        })
      });

      if (response.ok) {
        toast.success('Cita cancelada exitosamente');
        fetchAppointments();
      } else {
        toast.error('Error al cancelar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const [deleteDialog, setDeleteDialog] = useState<{ 
    open: boolean; 
    appointment: any | null; 
    loading: boolean; 
  }>({ open: false, appointment: null, loading: false });

  const [revertDialog, setRevertDialog] = useState<{ 
    open: boolean; 
    appointment: any | null; 
    loading: boolean; 
  }>({ open: false, appointment: null, loading: false });

  const handleAppointmentDelete = (appointment: any) => {
    setDeleteDialog({ open: true, appointment, loading: false });
  };

  const handleDelete = async (type: 'logical' | 'physical') => {
    if (!deleteDialog.appointment) return;
    
    setDeleteDialog(prev => ({ ...prev, loading: true }));
    
    try {
      const url = type === 'physical' 
        ? `/api/appointments/${deleteDialog.appointment.id}?hardDelete=true`
        : `/api/appointments/${deleteDialog.appointment.id}`;
        
      const response = await fetch(url, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const message = type === 'physical' 
          ? 'Cita eliminada permanentemente' 
          : 'Cita cancelada exitosamente';
        toast.success(message);
        fetchAppointments();
      } else {
        const error = await response.json();
        toast.error(error.error || `Error al ${type === 'physical' ? 'eliminar' : 'cancelar'} la cita`);
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    } finally {
      setDeleteDialog(prev => ({ ...prev, loading: false }));
    }
  };

  const handleAppointmentComplete = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      });

      if (response.ok) {
        toast.success('Cita marcada como completada');
        fetchAppointments();
      } else {
        toast.error('Error al completar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentCheckIn = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}/check-in`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        toast.success('Llegada del paciente registrada exitosamente');
        fetchAppointments();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al registrar llegada');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const [noShowDialog, setNoShowDialog] = useState<{ 
    open: boolean; 
    appointment: any | null; 
    loading: boolean; 
  }>({ open: false, appointment: null, loading: false });

  const handleAppointmentNoShow = (appointment: any) => {
    setNoShowDialog({ open: true, appointment, loading: false });
  };

  const handleConfirmNoShow = async () => {
    if (!noShowDialog.appointment) return;
    
    setNoShowDialog(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await fetch(`/api/appointments/${noShowDialog.appointment.id}/no-show`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          reason: 'Marcado como no show por asistente médico'
        })
      });

      if (response.ok) {
        toast.success('Cita marcada como no show');
        setNoShowDialog({ open: false, appointment: null, loading: false });
        // Refrescar inmediatamente y luego con delay para asegurar sincronización
        fetchAppointments();
        setTimeout(() => {
          fetchAppointments();
        }, 500);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al marcar no show');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    } finally {
      setNoShowDialog(prev => ({ ...prev, loading: false }));
    }
  };

  const handleStartConsultation = async (appointment: any) => {
    try {
      toast.info('Iniciando consulta médica...');
      
      // Llamar API para crear consulta médica
      const response = await fetch('/api/medical-consultations', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          appointmentId: appointment.id
        })
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Consulta médica iniciada exitosamente');
        await fetchAppointments(); // Refrescar para mostrar el nuevo estado
        
        // Redirigir al formulario de consulta médica del doctor correspondiente
        router.push(`/dashboard/doctor/consultations/${result.data.consultationId}?appointment=${appointment.id}`);
      } else {
        toast.error(result.error || 'Error al iniciar la consulta');
      }
    } catch (error) {
      console.error('Error starting consultation:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleRevertNoShow = (appointment: any) => {
    console.log('🔄 Attempting to revert appointment:', {
      id: appointment.id,
      status: appointment.status,
      title: appointment.title,
      patientName: appointment.patientName || `${appointment.patientFirstName} ${appointment.patientLastName}`
    });
    setRevertDialog({ open: true, appointment, loading: false });
  };

  const handleConfirmRevert = async () => {
    if (!revertDialog.appointment) return;
    
    setRevertDialog(prev => ({ ...prev, loading: true }));
    
    try {
      const response = await fetch(`/api/appointments/${revertDialog.appointment.id}/revert-no-show`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        toast.success('Cita revertida exitosamente a estado confirmado');
        setRevertDialog({ open: false, appointment: null, loading: false });
        // Refrescar inmediatamente y luego con delay para asegurar sincronización
        fetchAppointments();
        setTimeout(() => {
          fetchAppointments();
        }, 500);
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al revertir la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    } finally {
      setRevertDialog(prev => ({ ...prev, loading: false }));
    }
  };

  return (
    <div className="flex h-screen">
      {/* Panel principal */}
      <div className={cn(
        "flex-1 transition-all duration-300",
        showAIAssistant ? "mr-96" : "mr-0"
      )}>
        <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6 h-full overflow-y-auto">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div className="flex-1">
          <div className="flex items-center space-x-3 mb-2">
            <Calendar className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Agenda Médica</h1>
              <p className="text-sm lg:text-base text-gray-600">
                Gestión de citas para todos los médicos
              </p>
            </div>
          </div>

          {/* Controles secundarios */}
          <div className="flex gap-2 mt-4">
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowKPIs(!showKPIs)}
              className={cn(
                "shadow-sm transition-all",
                showKPIs 
                  ? "bg-purple-50 border-purple-300 text-purple-700" 
                  : "hover:bg-gray-50 hover:border-gray-300"
              )}
            >
              <BarChart3 className="h-4 w-4 mr-1.5" />
              Indicadores
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                "shadow-sm transition-all",
                showFilters 
                  ? "bg-blue-50 border-blue-300 text-blue-700" 
                  : "hover:bg-gray-50 hover:border-gray-300"
              )}
            >
              <Filter className="h-4 w-4 mr-1.5" />
              Filtros
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowAIAssistant(!showAIAssistant)}
              className={cn(
                "shadow-sm transition-all",
                showAIAssistant 
                  ? "bg-emerald-50 border-emerald-300 text-emerald-700" 
                  : "hover:bg-emerald-50 hover:border-emerald-300"
              )}
            >
              <Bot className="h-4 w-4 mr-1.5" />
              Asistente
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  const response = await fetch('/api/appointments/process-past', {
                    method: 'POST'
                  });
                  const data = await response.json();
                  if (data.success) {
                    toast.success(`${data.data.processed} citas actualizadas a no-show`);
                    fetchAppointments();
                  } else {
                    toast.error('Error al procesar citas pasadas');
                  }
                } catch (error) {
                  toast.error('Error al conectar con el servidor');
                }
              }}
              className="shadow-sm hover:bg-orange-50 hover:border-orange-300"
            >
              <AlertCircle className="h-4 w-4 mr-1.5" />
              Procesar No-Shows
            </Button>
          </div>
        </div>
        
        <Button 
          onClick={() => router.push('/dashboard/assistant/agenda/appointment/new')}
          className="bg-emerald-600 hover:bg-emerald-700 text-white shadow-md"
        >
          <Plus className="h-4 w-4 mr-2" />
          Nueva Cita
        </Button>
      </div>

      {/* KPI's - Solo se muestra si showKPIs es true */}
      {showKPIs && (
        <Card className="animate-in slide-in-from-top-4 duration-300 mb-6 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              Indicadores de Rendimiento
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-blue-900">
                    Total Citas
                  </CardTitle>
                  <div className="h-10 w-10 rounded-full bg-blue-200 flex items-center justify-center">
                    <Calendar className="h-5 w-5 text-blue-700" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-blue-900">{stats.total}</div>
                  <div className="text-xs text-blue-700">Todas las citas</div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-sky-50 to-sky-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-sky-900">
                    Programadas
                  </CardTitle>
                  <div className="h-10 w-10 rounded-full bg-sky-200 flex items-center justify-center">
                    <Clock className="h-5 w-5 text-sky-700" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-sky-900">{stats.scheduled}</div>
                  <div className="text-xs text-sky-700">Por confirmar</div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-green-900">
                    Confirmadas
                  </CardTitle>
                  <div className="h-10 w-10 rounded-full bg-green-200 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-green-700" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-green-900">{stats.confirmed}</div>
                  <div className="text-xs text-green-700">Listas para atender</div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-purple-900">
                    Completadas
                  </CardTitle>
                  <div className="h-10 w-10 rounded-full bg-purple-200 flex items-center justify-center">
                    <CheckCircle className="h-5 w-5 text-purple-700" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-purple-900">{stats.completed}</div>
                  <div className="text-xs text-purple-700">Finalizadas</div>
                </CardContent>
              </Card>

              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-red-50 to-red-100">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-semibold text-red-900">
                    Canceladas
                  </CardTitle>
                  <div className="h-10 w-10 rounded-full bg-red-200 flex items-center justify-center">
                    <X className="h-5 w-5 text-red-700" />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-red-900">{stats.cancelled}</div>
                  <div className="text-xs text-red-700">No realizadas</div>
                </CardContent>
              </Card>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Toolbar de navegación y filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
            {/* Navegación de fecha */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setCurrentDate(new Date())}
                >
                  Hoy
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="text-lg font-semibold text-gray-900">
                {formatCurrentPeriod()}
              </div>
            </div>
            
            {/* Selector de vista */}
            <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
              <TabsList>
                <TabsTrigger value="day">Día</TabsTrigger>
                <TabsTrigger value="week">Semana</TabsTrigger>
                <TabsTrigger value="month">Mes</TabsTrigger>
                <TabsTrigger value="list">Lista</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Filtros de búsqueda - Solo se muestra si showFilters es true */}
      {showFilters && (
        <Card className="animate-in slide-in-from-top-4 duration-300 mb-6 shadow-md">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-600" />
              Filtros de Búsqueda
            </CardTitle>
          </CardHeader>
          <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <Label>Buscar paciente</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Nombre del paciente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div>
              <Label>Médico</Label>
              <Select value={doctorFilter} onValueChange={setDoctorFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos los médicos" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los médicos</SelectItem>
                  {doctors.map((doctor: any) => (
                    <SelectItem key={doctor.id} value={doctor.id}>
                      Dr. {doctor.firstName} {doctor.lastName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Consultorio</Label>
              <Select value={consultoryFilter} onValueChange={setConsultoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos los consultorios" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los consultorios</SelectItem>
                  {consultories.map((consultory: any) => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      {consultory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Estado</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="scheduled">📅 Programadas</SelectItem>
                  <SelectItem value="confirmed">✅ Confirmadas</SelectItem>
                  <SelectItem value="in_progress">🟢 En consulta</SelectItem>
                  <SelectItem value="completed">✅ Completadas</SelectItem>
                  <SelectItem value="cancelled">❌ Canceladas</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        </Card>
      )}


      {/* Vista principal */}
      {viewMode === 'list' && (
        <>
          <DoctorHeader
            doctorInfo={selectedDoctorInfo}
            availableDoctors={doctors}
            onDoctorChange={(doctorId) => setDoctorFilter(doctorId)}
            stats={getDoctorStats()}
            isSelectable={true}
            className="mb-4"
          />
          <AppointmentsList 
            appointments={appointments}
            loading={loading}
            onView={handleAppointmentView}
            onEdit={handleAppointmentEdit}
            onConfirm={handleAppointmentConfirm}
            onCancel={handleAppointmentCancel}
            onDelete={handleAppointmentDelete}
            onCheckIn={handleAppointmentCheckIn}
            onNoShow={handleAppointmentNoShow}
            onComplete={handleAppointmentComplete}
            onStartConsultation={handleStartConsultation}
            onRevertNoShow={handleRevertNoShow}
            onRefresh={fetchAppointments}
            showDoctor={true}
            showAuditInfo={true}
            userRole="assistant"
          />
        </>
      )}

      {viewMode === 'day' && (
        <>
          <DoctorHeader
            doctorInfo={selectedDoctorInfo}
            availableDoctors={doctors}
            onDoctorChange={(doctorId) => setDoctorFilter(doctorId)}
            stats={getDoctorStats()}
            isSelectable={true}
            className="mb-4"
          />
          <CalendarDayView
            appointments={appointments}
            currentDate={currentDate}
            loading={loading}
            doctorInfo={selectedDoctorInfo}
            onTimeSlotClick={(date, time) => {
              const dateParam = format(date, 'yyyy-MM-dd');
              const doctorParam = selectedDoctorInfo?.id || doctorFilter;
              router.push(`/dashboard/assistant/agenda/appointment/new?date=${dateParam}&time=${time}&doctorId=${doctorParam}`);
            }}
            onAppointmentClick={(appointment) => {
              router.push(`/dashboard/assistant/agenda/appointment/${appointment.id}`);
            }}
            onAppointmentEdit={handleAppointmentEdit}
            onAppointmentConfirm={handleAppointmentConfirm}
            onAppointmentCancel={handleAppointmentCancel}
            onAppointmentDelete={handleAppointmentDelete}
            onAppointmentCheckIn={handleAppointmentCheckIn}
            onAppointmentNoShow={handleAppointmentNoShow}
            onAppointmentComplete={handleAppointmentComplete}
            onAppointmentStart={handleStartConsultation}
            onAppointmentRevertNoShow={handleRevertNoShow}
          />
        </>
      )}

      {viewMode === 'week' && (
        <>
          <DoctorHeader
            doctorInfo={selectedDoctorInfo}
            availableDoctors={doctors}
            onDoctorChange={(doctorId) => setDoctorFilter(doctorId)}
            stats={getDoctorStats()}
            isSelectable={true}
            className="mb-4"
          />
          <CalendarWeekView
            appointments={appointments}
            currentDate={currentDate}
            loading={loading}
            onTimeSlotClick={(date, time) => {
              const dateParam = format(date, 'yyyy-MM-dd');
              const doctorParam = selectedDoctorInfo?.id || doctorFilter;
              router.push(`/dashboard/assistant/agenda/appointment/new?date=${dateParam}&time=${time}&doctorId=${doctorParam}`);
            }}
            onAppointmentClick={(appointment) => {
              router.push(`/dashboard/assistant/agenda/appointment/${appointment.id}`);
            }}
            onAppointmentEdit={handleAppointmentEdit}
            onAppointmentConfirm={handleAppointmentConfirm}
            onAppointmentCancel={handleAppointmentCancel}
            onAppointmentDelete={handleAppointmentDelete}
            onAppointmentCheckIn={handleAppointmentCheckIn}
            onAppointmentNoShow={handleAppointmentNoShow}
            onAppointmentComplete={handleAppointmentComplete}
            onAppointmentStart={handleStartConsultation}
            onAppointmentRevertNoShow={handleRevertNoShow}
          />
        </>
      )}

      {viewMode === 'month' && (
        <>
          <DoctorHeader
            doctorInfo={selectedDoctorInfo}
            availableDoctors={doctors}
            onDoctorChange={(doctorId) => setDoctorFilter(doctorId)}
            stats={getDoctorStats()}
            isSelectable={true}
            className="mb-4"
          />
          <CalendarMonthView
            appointments={appointments}
            currentDate={currentDate}
            loading={loading}
            onDateClick={(date) => {
              setCurrentDate(date);
              setViewMode('day');
            }}
            onAppointmentClick={(appointment) => {
              router.push(`/dashboard/assistant/agenda/appointment/${appointment.id}`);
            }}
            onAppointmentEdit={handleAppointmentEdit}
            onAppointmentConfirm={handleAppointmentConfirm}
            onAppointmentCancel={handleAppointmentCancel}
            onAppointmentDelete={handleAppointmentDelete}
            onAppointmentCheckIn={handleAppointmentCheckIn}
            onAppointmentNoShow={handleAppointmentNoShow}
            onAppointmentComplete={handleAppointmentComplete}
            onAppointmentStart={handleStartConsultation}
            onAppointmentRevertNoShow={handleRevertNoShow}
          />
        </>
      )}

        </div>
      </div>

      {/* Panel lateral derecho - Chat IA */}
      {showAIAssistant && (
        <div className="fixed right-0 top-0 h-screen w-96 bg-white border-l border-gray-200 shadow-lg z-40 transition-transform duration-300">
          <div className="h-full flex flex-col">
            <AIAssistantChat
              isOpen={showAIAssistant}
              onClose={() => setShowAIAssistant(false)}
              doctorInfo={{
                id: user?.id || '',
                firstName: 'Asistente',
                lastName: 'Médico',
                specialty: 'Gestión de Agenda'
              }}
            />
          </div>
        </div>
      )}

      {/* Modal de confirmación de eliminación */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => {
          if (!deleteDialog.loading) {
            setDeleteDialog({ open, appointment: null, loading: false });
          }
        }}
        itemToDelete={deleteDialog.appointment}
        onDelete={handleDelete}
        isDeleting={deleteDialog.loading}
        showPhysicalDelete={true}
      />

      {/* Modal de confirmación para revertir no-show */}
      <RevertConfirmationDialog
        open={revertDialog.open}
        onOpenChange={(open) => {
          if (!revertDialog.loading) {
            setRevertDialog({ open, appointment: null, loading: false });
          }
        }}
        appointment={revertDialog.appointment}
        onConfirm={handleConfirmRevert}
        isProcessing={revertDialog.loading}
      />

      {/* Modal de confirmación para marcar no-show */}
      <NoShowConfirmationDialog
        open={noShowDialog.open}
        onOpenChange={(open) => {
          if (!noShowDialog.loading) {
            setNoShowDialog({ open, appointment: null, loading: false });
          }
        }}
        appointment={noShowDialog.appointment}
        onConfirm={handleConfirmNoShow}
        isProcessing={noShowDialog.loading}
      />
    </div>
  );
}