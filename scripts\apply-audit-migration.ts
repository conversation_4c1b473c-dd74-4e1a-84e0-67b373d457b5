import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import fs from 'fs';
import path from 'path';

// Load environment variables
config({ path: '.env.local' });

async function applyMigration() {
  try {
    const sql = neon(process.env.DATABASE_URL!);
    const db = drizzle(sql);

    // Read the migration file
    const migrationPath = path.join(process.cwd(), 'db/migrations/0011_add_missing_audit_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf-8');

    console.log('Applying audit fields migration...');
    
    // Execute the migration
    await sql(migrationSQL);
    
    console.log('✓ Audit fields migration applied successfully!');
    
    // Verify the columns exist
    const result = await sql(`
      SELECT table_name, column_name 
      FROM information_schema.columns 
      WHERE table_name IN ('occupations', 'departments', 'municipalities') 
      AND column_name IN ('createdAt', 'updatedAt')
      ORDER BY table_name, column_name;
    `);
    
    console.log('Verified audit fields:');
    result.forEach(row => {
      console.log(`- ${row.table_name}.${row.column_name}`);
    });
    
  } catch (error) {
    console.error('Error applying migration:', error);
    process.exit(1);
  }
}

applyMigration();