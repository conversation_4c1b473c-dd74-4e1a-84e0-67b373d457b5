-- Migración: Cambiar consultationType a servicios múltiples
-- Fecha: 2025-01-27
-- Descripción: Per<PERSON><PERSON> múltiples servicios médicos por consulta para facturación detallada

-- 1. <PERSON><PERSON>, crear la nueva columna services como jsonb
ALTER TABLE medical_consultations 
ADD COLUMN services jsonb DEFAULT '[]'::jsonb;

-- 2. <PERSON><PERSON>r datos existentes de consultationType a services
-- Solo si consultationType no es null y no es un estado
UPDATE medical_consultations 
SET services = jsonb_build_array(
  jsonb_build_object(
    'serviceId', consultation_type,
    'addedAt', created_at,
    'price', null,
    'notes', null
  )
)
WHERE consultation_type IS NOT NULL 
  AND consultation_type NOT IN ('scheduled', 'completed', 'cancelled', 'confirmed', 'draft');

-- 3. Para los casos donde consultationType es un estado, dejar services vacío
-- (estos se llenarán desde la cita o manualmente)

-- 4. Remover la columna consultation_type (comentado por seguridad)
-- ALTER TABLE medical_consultations DROP COLUMN consultation_type;

-- 5. Agregar comentario para documentar la estructura
COMMENT ON COLUMN medical_consultations.services IS 
'Array de servicios médicos realizados en la consulta. Estructura: 
[{
  "serviceId": "id_del_servicio",
  "serviceName": "nombre_del_servicio", 
  "category": "categoria",
  "price": numero,
  "duration": minutos,
  "performedBy": "id_usuario",
  "addedAt": "timestamp",
  "notes": "notas_opcionales"
}]';