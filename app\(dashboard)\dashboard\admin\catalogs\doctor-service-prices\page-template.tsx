'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { TagSelector } from '@/components/ui/tag-selector';
import { 
  Plus,
  Search,
  Filter,
  RefreshCw,
  Stethoscope,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Download,
  ArrowLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  ToggleLeft,
  Clock,
  DollarSign,
  Wrench,
  User
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface ServiceTag {
  id: string;
  name: string;
  color?: string;
  category?: string;
}

interface MedicalService {
  id: string;
  name: string;
  description?: string;
  code?: string;
  category: string;
  basePrice?: number | string;
  currency?: string;
  duration?: number;
  requiresEquipment: boolean;
  requiresSpecialist: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  tags?: ServiceTag[];
}

export default function MedicalServicesPage() {
  const router = useRouter();
  const [medicalServices, setMedicalServices] = useState<MedicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [equipmentFilter, setEquipmentFilter] = useState<string>('all');
  const [specialistFilter, setSpecialistFilter] = useState<string>('all');
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [selectedService, setSelectedService] = useState<MedicalService | null>(null);
  const [editingService, setEditingService] = useState<MedicalService | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [categories, setCategories] = useState<string[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [availableTags, setAvailableTags] = useState<ServiceTag[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    code: '',
    category: '',
    basePrice: '',
    currency: 'GTQ',
    duration: '',
    requiresEquipment: false,
    requiresSpecialist: false,
  });
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [serviceToDelete, setServiceToDelete] = useState<MedicalService | null>(null);
  
  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  const getBadgeColor = (color?: string) => {
    const colorMap: Record<string, string> = {
      red: 'bg-red-100 text-red-800 border-red-200',
      green: 'bg-green-100 text-green-800 border-green-200',
      blue: 'bg-blue-100 text-blue-800 border-blue-200',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200',
      pink: 'bg-pink-100 text-pink-800 border-pink-200',
      cyan: 'bg-cyan-100 text-cyan-800 border-cyan-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200',
    };
    return colorMap[color || 'blue'] || colorMap.blue;
  };

  const fetchServiceTags = async () => {
    try {
      const response = await fetch('/api/catalogs/service-tags?limit=100&active=true');
      if (!response.ok) throw new Error('Error fetching service tags');
      
      const data = await response.json();
      setAvailableTags(data.data || []);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast.error('Error al cargar los tags de servicios');
    }
  };

  const fetchMedicalServices = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(categoryFilter !== 'all' && { category: categoryFilter }),
        ...(equipmentFilter !== 'all' && { requiresEquipment: equipmentFilter }),
        ...(specialistFilter !== 'all' && { requiresSpecialist: specialistFilter }),
        ...(activeFilter !== 'all' && { active: activeFilter }),
        orderBy: sortBy,
        orderDirection: sortOrder
      });

      const response = await fetch(`/api/catalogs/medical-services?${params}`);
      if (!response.ok) throw new Error('Error fetching medical services');
      
      const data = await response.json();
      setMedicalServices(data.data || []);
      setCategories((data.categories || []).filter((cat: string) => cat && cat.trim() !== ''));
      setTotalPages(data.pagination?.totalPages || 1);
      setTotalCount(data.pagination?.total || 0);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar los servicios médicos');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchMedicalServices();
  };

  useEffect(() => {
    fetchMedicalServices();
  }, [page, limit, searchTerm, categoryFilter, equipmentFilter, specialistFilter, activeFilter, sortBy, sortOrder]);

  // Cargar tags al montar el componente
  useEffect(() => {
    fetchServiceTags();
  }, []);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [searchTerm, categoryFilter, equipmentFilter, specialistFilter, activeFilter]);

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      code: '',
      category: '',
      basePrice: '',
      currency: 'GTQ',
      duration: '',
      requiresEquipment: false,
      requiresSpecialist: false,
    });
    setSelectedTags([]);
  };

  // Handle create
  const handleCreate = async () => {
    try {
      const response = await fetch('/api/catalogs/medical-services', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          tags: selectedTags
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error creating medical service');
      }

      toast.success('Servicio médico creado exitosamente');
      setIsCreateDialogOpen(false);
      resetForm();
      fetchMedicalServices();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  // Handle edit
  const handleEdit = async () => {
    if (!editingService) return;

    try {
      const response = await fetch('/api/catalogs/medical-services', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingService.id,
          ...formData,
          tags: selectedTags
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error updating medical service');
      }

      toast.success('Servicio médico actualizado exitosamente');
      setIsEditDialogOpen(false);
      setEditingService(null);
      resetForm();
      fetchMedicalServices();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const openDetailsDialog = (service: MedicalService) => {
    setSelectedService(service);
    setIsDetailsDialogOpen(true);
  };

  const openEditDialog = (service: MedicalService) => {
    setEditingService(service);
    setFormData({
      name: service.name,
      description: service.description || '',
      code: service.code || '',
      category: service.category,
      basePrice: service.basePrice?.toString() || '',
      currency: service.currency || 'GTQ',
      duration: service.duration?.toString() || '',
      requiresEquipment: Boolean(service.requiresEquipment),
      requiresSpecialist: Boolean(service.requiresSpecialist),
    });
    setSelectedTags(service.tags?.map(tag => tag.id) || []);
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (service: MedicalService) => {
    setServiceToDelete(service);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async (deleteType: 'logical' | 'physical' = 'logical') => {
    if (!serviceToDelete) return;
    
    try {
      const response = await fetch(`/api/catalogs/medical-services/${serviceToDelete.id}?type=${deleteType}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error eliminando servicio médico');
      }

      const result = await response.json();
      toast.success(result.message || 'Servicio médico eliminado exitosamente');
      setIsDeleteDialogOpen(false);
      setServiceToDelete(null);
      fetchMedicalServices();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const toggleStatus = async (service: MedicalService) => {
    try {
      const response = await fetch(`/api/catalogs/medical-services/${service.id}/toggle-status`, {
        method: 'POST'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error cambiando estado');
      }

      toast.success(`Servicio médico ${service.isActive ? 'desactivado' : 'activado'} exitosamente`);
      fetchMedicalServices();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const formatPrice = (price?: number | string, currency?: string) => {
    if (!price) return 'N/A';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    if (isNaN(numPrice)) return 'N/A';
    const symbol = currency === 'USD' ? '$' : 'Q';
    return `${symbol}${numPrice.toFixed(2)}`;
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'Consulta': return <Stethoscope className="h-4 w-4" />;
      case 'Procedimiento': return <Wrench className="h-4 w-4" />;
      case 'Emergencia': return <Clock className="h-4 w-4" />;
      case 'Preventivo': return <User className="h-4 w-4" />;
      case 'Diagnóstico': return <Search className="h-4 w-4" />;
      default: return <Stethoscope className="h-4 w-4" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'Consulta': return 'bg-blue-100 text-blue-800';
      case 'Procedimiento': return 'bg-green-100 text-green-800';
      case 'Emergencia': return 'bg-red-100 text-red-800';
      case 'Preventivo': return 'bg-purple-100 text-purple-800';
      case 'Diagnóstico': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="hover:bg-green-100 hover:text-green-600 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestión de Servicios Médicos</h1>
              <p className="text-sm lg:text-base text-gray-600">Administra los servicios médicos del consultorio</p>
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            disabled={refreshing}
            className="hover:bg-gray-50 transition-colors w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button 
            className="w-full sm:w-auto bg-green-600 hover:bg-green-700"
            onClick={() => {
              resetForm();
              setIsCreateDialogOpen(true);
            }}
          >
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Servicio
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, descripción, código o categoría..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Todas las categorías" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las categorías</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category} value={category}>
                      {category}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={equipmentFilter} onValueChange={setEquipmentFilter}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Equipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todo equipo</SelectItem>
                  <SelectItem value="true">Con equipo</SelectItem>
                  <SelectItem value="false">Sin equipo</SelectItem>
                </SelectContent>
              </Select>

              <Select value={specialistFilter} onValueChange={setSpecialistFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Especialista" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas Características</SelectItem>
                  <SelectItem value="true">Requiere Especialista</SelectItem>
                  <SelectItem value="false">No requiere</SelectItem>
                </SelectContent>
              </Select>

              <Select value={activeFilter} onValueChange={setActiveFilter}>
                <SelectTrigger className="w-full sm:w-32">
                  <SelectValue placeholder="Estado" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="true">Activos</SelectItem>
                  <SelectItem value="false">Inactivos</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Stethoscope className="h-5 w-5 text-blue-600" />
                Lista de Servicios Médicos
              </CardTitle>
              <Badge variant="outline" className="border-gray-300">
                {totalCount} servicios
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block overflow-x-auto">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Servicio
                        <SortIcon column="name" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('category')}
                    >
                      <div className="flex items-center gap-2">
                        Categoría
                        <SortIcon column="category" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('basePrice')}
                    >
                      <div className="flex items-center gap-2">
                        Precio
                        <SortIcon column="basePrice" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('duration')}
                    >
                      <div className="flex items-center gap-2">
                        Duración
                        <SortIcon column="duration" />
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">Características</TableHead>
                    <TableHead className="font-semibold text-gray-700">Estado</TableHead>
                    <TableHead className="font-semibold text-gray-700 text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-gray-600">Cargando servicios...</p>
                      </TableCell>
                    </TableRow>
                  ) : medicalServices.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={8} className="text-center py-8">
                        <Stethoscope className="h-12 w-12 text-gray-400 mx-auto" />
                        <p className="mt-2 text-gray-600">No se encontraron servicios médicos</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    medicalServices.map((service) => (
                      <TableRow key={service.id}>
                        <TableCell>
                          <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            {getCategoryIcon(service.category)}
                            <div>
                              <span className="font-medium">{service.name}</span>
                              {service.code && (
                                <p className="text-sm text-gray-500">{service.code}</p>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge className={`text-xs ${getCategoryColor(service.category)}`}>
                            {service.category}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-green-700">{formatPrice(service.basePrice, service.currency)}</span>
                        </TableCell>
                        <TableCell>
                          {service.duration ? (
                            <div className="flex items-center gap-1">
                              <Clock className="h-4 w-4 text-blue-600" />
                              <span>{service.duration} min</span>
                            </div>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex flex-wrap gap-1">
                            {service.tags && service.tags.length > 0 ? (
                              service.tags.slice(0, 3).map(tag => (
                                <Badge 
                                  key={tag.id} 
                                  variant="outline" 
                                  className={cn('text-xs', getBadgeColor(tag.color))}
                                >
                                  {tag.name}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-xs text-gray-500">Sin tags</span>
                            )}
                            {service.tags && service.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs bg-gray-100">
                                +{service.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant={service.isActive ? "default" : "secondary"}>
                            {service.isActive ? "Activo" : "Inactivo"}
                          </Badge>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openDetailsDialog(service)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Ver detalles
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openEditDialog(service)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => toggleStatus(service)}
                                className={service.isActive ? "text-orange-600" : "text-green-600"}
                              >
                                <ToggleLeft className="h-4 w-4 mr-2" />
                                {service.isActive ? 'Desactivar' : 'Activar'}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600 focus:text-red-600" 
                                onClick={() => openDeleteDialog(service)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Eliminar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Cards View */}
          <div className="lg:hidden space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Cargando...</p>
              </div>
            ) : medicalServices.length === 0 ? (
              <div className="text-center py-8">
                <Stethoscope className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="mt-2 text-gray-600">No se encontraron servicios médicos</p>
              </div>
            ) : (
              medicalServices.map((service) => (
                <Card key={service.id} className="hover:shadow-md transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <Checkbox className="mt-1" />
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            {getCategoryIcon(service.category)}
                            <span className="font-medium">{service.name}</span>
                          </div>
                          <div className="flex flex-wrap gap-2">
                            <Badge className={`text-xs ${getCategoryColor(service.category)}`}>
                              {service.category}
                            </Badge>
                            {service.basePrice && (
                              <Badge variant="outline" className="text-xs text-green-700">
                                {formatPrice(service.basePrice, service.currency)}
                              </Badge>
                            )}
                            {service.duration && (
                              <Badge variant="outline" className="text-xs">
                                <Clock className="h-3 w-3 mr-1" />
                                {service.duration} min
                              </Badge>
                            )}
                          </div>
                          <div className="flex flex-wrap gap-1">
                            {service.tags && service.tags.length > 0 ? (
                              service.tags.slice(0, 3).map(tag => (
                                <Badge 
                                  key={tag.id} 
                                  variant="outline" 
                                  className={cn('text-xs', getBadgeColor(tag.color))}
                                >
                                  {tag.name}
                                </Badge>
                              ))
                            ) : (
                              <span className="text-xs text-gray-500">Sin tags</span>
                            )}
                            {service.tags && service.tags.length > 3 && (
                              <Badge variant="outline" className="text-xs bg-gray-100">
                                +{service.tags.length - 3}
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openDetailsDialog(service)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openEditDialog(service)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => toggleStatus(service)}
                            className={service.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            <ToggleLeft className="h-4 w-4 mr-2" />
                            {service.isActive ? 'Desactivar' : 'Activar'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600 focus:text-red-600" 
                                onClick={() => openDeleteDialog(service)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>

          {/* Pagination */}
          <div className="flex flex-col sm:flex-row items-center justify-between px-2 py-4 gap-4">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Mostrando {Math.min((page - 1) * limit + 1, totalCount)} a {Math.min(page * limit, totalCount)} de {totalCount} servicios médicos
              </div>
              <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 por página</SelectItem>
                  <SelectItem value="10">10 por página</SelectItem>
                  <SelectItem value="20">20 por página</SelectItem>
                  <SelectItem value="50">50 por página</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(prev => Math.max(1, prev - 1))}
                  disabled={page === 1}
                >
                  Anterior
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber;
                    if (totalPages <= 5) {
                      pageNumber = i + 1;
                    } else if (page <= 3) {
                      pageNumber = i + 1;
                    } else if (page >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNumber}
                        variant={page === pageNumber ? "default" : "outline"}
                        size="sm"
                        className="w-8 h-8 p-0"
                        onClick={() => setPage(pageNumber)}
                      >
                        {pageNumber}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={page === totalPages}
                >
                  Siguiente
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Stethoscope className="h-5 w-5 text-blue-600" />
              Detalles del Servicio Médico
            </DialogTitle>
            <DialogDescription>
              Información detallada del servicio médico
            </DialogDescription>
          </DialogHeader>
          {selectedService && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                  <p className="text-sm text-gray-900">{selectedService.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Código</Label>
                  <p className="text-sm text-gray-900">{selectedService.code || 'N/A'}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Categoría</Label>
                  <Badge className={`text-xs ${getCategoryColor(selectedService.category)}`}>
                    {selectedService.category}
                  </Badge>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Precio Base</Label>
                  <p className="text-sm text-gray-900">{formatPrice(selectedService.basePrice, selectedService.currency)}</p>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Duración</Label>
                  <p className="text-sm text-gray-900">{selectedService.duration ? `${selectedService.duration} minutos` : 'N/A'}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Estado</Label>
                  <Badge variant={selectedService.isActive ? "default" : "secondary"}>
                    {selectedService.isActive ? "Activo" : "Inactivo"}
                  </Badge>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Descripción</Label>
                <p className="text-sm text-gray-900">{selectedService.description || 'Sin descripción'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Tags</Label>
                <div className="flex flex-wrap gap-2 mt-1">
                  {selectedService.tags && selectedService.tags.length > 0 ? (
                    selectedService.tags.map(tag => (
                      <Badge 
                        key={tag.id} 
                        variant="outline" 
                        className={cn('text-xs', getBadgeColor(tag.color))}
                      >
                        {tag.name}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-gray-500">Sin tags asignados</span>
                  )}
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Servicio Médico</DialogTitle>
            <DialogDescription>
              Modifica la información del servicio médico
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nombre *</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="ej. Consulta General"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-code">Código</Label>
                <Input
                  id="edit-code"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value})}
                  placeholder="ej. CONS-001"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-category">Categoría *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona categoría" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Consulta">Consulta</SelectItem>
                    <SelectItem value="Procedimiento">Procedimiento</SelectItem>
                    <SelectItem value="Emergencia">Emergencia</SelectItem>
                    <SelectItem value="Preventivo">Preventivo</SelectItem>
                    <SelectItem value="Diagnóstico">Diagnóstico</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-duration">Duración (minutos)</Label>
                <Input
                  id="edit-duration"
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData({...formData, duration: e.target.value})}
                  placeholder="ej. 30"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-price">Precio Base</Label>
                <Input
                  id="edit-price"
                  type="number"
                  step="0.01"
                  value={formData.basePrice}
                  onChange={(e) => setFormData({...formData, basePrice: e.target.value})}
                  placeholder="ej. 250.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-currency">Moneda</Label>
                <Select value={formData.currency} onValueChange={(value) => setFormData({...formData, currency: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GTQ">GTQ (Quetzal)</SelectItem>
                    <SelectItem value="USD">USD (Dólar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="edit-description">Descripción</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Descripción del servicio médico..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <TagSelector
                availableTags={availableTags}
                selectedTags={selectedTags}
                onChange={setSelectedTags}
                placeholder="Seleccionar tags..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit} className="bg-green-600 hover:bg-green-700">
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Create Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Crear Nuevo Servicio Médico</DialogTitle>
            <DialogDescription>
              Completa la información para crear un nuevo servicio médico
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="create-name">Nombre *</Label>
                <Input
                  id="create-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  placeholder="ej. Consulta General"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-code">Código</Label>
                <Input
                  id="create-code"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value})}
                  placeholder="ej. CONS-001"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="create-category">Categoría *</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                  <SelectTrigger>
                    <SelectValue placeholder="Selecciona categoría" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Consulta">Consulta</SelectItem>
                    <SelectItem value="Procedimiento">Procedimiento</SelectItem>
                    <SelectItem value="Emergencia">Emergencia</SelectItem>
                    <SelectItem value="Preventivo">Preventivo</SelectItem>
                    <SelectItem value="Diagnóstico">Diagnóstico</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-duration">Duración (minutos)</Label>
                <Input
                  id="create-duration"
                  type="number"
                  value={formData.duration}
                  onChange={(e) => setFormData({...formData, duration: e.target.value})}
                  placeholder="ej. 30"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="create-price">Precio Base</Label>
                <Input
                  id="create-price"
                  type="number"
                  step="0.01"
                  value={formData.basePrice}
                  onChange={(e) => setFormData({...formData, basePrice: e.target.value})}
                  placeholder="ej. 250.00"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="create-currency">Moneda</Label>
                <Select value={formData.currency} onValueChange={(value) => setFormData({...formData, currency: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="GTQ">GTQ (Quetzal)</SelectItem>
                    <SelectItem value="USD">USD (Dólar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="create-description">Descripción</Label>
              <Textarea
                id="create-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                placeholder="Descripción del servicio médico..."
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <TagSelector
                availableTags={availableTags}
                selectedTags={selectedTags}
                onChange={setSelectedTags}
                placeholder="Seleccionar tags..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreate} className="bg-green-600 hover:bg-green-700">
              Crear Servicio
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Esta acción no se puede deshacer. Seleccione el tipo de eliminación que desea realizar.
            </DialogDescription>
          </DialogHeader>
          {serviceToDelete && (
            <div className="py-4">
              <div className="p-4 bg-gray-50 rounded-lg mb-4">
                <p className="text-sm font-medium text-gray-900 mb-2">
                  Servicio médico a eliminar:
                </p>
                <p className="text-lg font-semibold text-gray-800">
                  {serviceToDelete.name}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Categoría: {serviceToDelete.category}
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="p-3 border border-orange-200 bg-orange-50 rounded-lg">
                  <p className="text-sm font-medium text-orange-800 mb-1">
                    Eliminación Lógica (Recomendada)
                  </p>
                  <p className="text-xs text-orange-700">
                    El servicio médico se marcará como inactivo pero se mantendrá en la base de datos para referencia histórica.
                  </p>
                </div>
                
                <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
                  <p className="text-sm font-medium text-red-800 mb-1">
                    Eliminación Física (Permanente)
                  </p>
                  <p className="text-xs text-red-700">
                    El servicio médico se eliminará completamente de la base de datos. Esta acción es irreversible.
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button 
              variant="outline" 
              className="bg-orange-500 text-white hover:bg-orange-600"
              onClick={() => handleDelete('logical')}
            >
              Eliminar Lógicamente
            </Button>
            <Button 
              variant="destructive"
              onClick={() => handleDelete('physical')}
            >
              Eliminar Físicamente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}