"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useState } from "react";
import { Menu, X } from "lucide-react";
import { useAuth, useUser, UserButton } from "@clerk/nextjs";
import { useAuthModal } from "@/hooks/use-auth-modal";
import SignInModal from "@/components/auth/sign-in-modal";
import SignUpModal from "@/components/auth/sign-up-modal";
import { useConsultoryInfo } from "@/hooks/use-consultory-info";
import Image from "next/image";

export default function Navbar() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { isSignedIn } = useAuth();
  const { user } = useUser();
  const { consultory, isLoading } = useConsultoryInfo();
  
  // Determinar si el onboarding está completo usando metadata híbrida
  const isOnboardingCompleted = user?.publicMetadata?.onboardingCompleted || user?.unsafeMetadata?.onboardingCompleted;
  const { 
    isSignInOpen, 
    isSignUpOpen, 
    openSignIn, 
    openSignUp, 
    closeAll,
    switchToSignUp,
    switchToSignIn 
  } = useAuthModal();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <>
      <nav className="bg-white/70 backdrop-blur-xl border-b border-[#50bed2]/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6">
          <div className="flex justify-between items-center h-24">
            {/* Logo */}
            <Link href="/" className="flex items-center space-x-3">
              {consultory?.logoUrl && !isLoading ? (
                <div className="w-20 h-20 rounded-2xl overflow-hidden shadow-lg">
                  <Image
                    src={consultory.logoUrl}
                    alt={`${consultory.name} logo`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-20 h-20 bg-gradient-to-br from-[#ea6cb0] to-[#ea6cb0]/80 rounded-2xl flex items-center justify-center shadow-lg">
                  <span className="text-white font-bold text-2xl">
                    {isLoading ? '...' : (consultory?.name?.substring(0, 2).toUpperCase() || 'MP')}
                  </span>
                </div>
              )}
              <div className="hidden sm:block">
                <span className="text-xl font-bold text-[#ea6cb0]">
                  {isLoading ? 'Cargando...' : consultory?.name || 'Mundo Pediatra'}
                </span>
              </div>
            </Link>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-8">
              <Link 
                href="/blog" 
                className="text-gray-700 hover:text-green-600 transition-colors duration-200 font-medium"
              >
                Blog
              </Link>
              <div className="flex items-center space-x-3">
                {isSignedIn ? (
                  <>
                    <Button className="bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-200" asChild>
                      <Link href="/dashboard">Dashboard</Link>
                    </Button>
                    <UserButton afterSignOutUrl="/" />
                  </>
                ) : (
                  <>
                    <Button 
                      variant="ghost" 
                      className="text-gray-700 hover:text-[#50bed2] hover:bg-[#50bed2]/10"
                      onClick={openSignIn}
                    >
                      Iniciar Sesión
                    </Button>
                    <Button 
                      className="bg-gradient-to-r from-[#50bed2] to-[#50bed2]/90 hover:from-[#50bed2]/90 hover:to-[#50bed2] text-white shadow-lg hover:shadow-xl transition-all duration-200"
                      onClick={openSignUp}
                    >
                      Registrarse
                    </Button>
                  </>
                )}
              </div>
            </div>

            {/* Mobile menu button */}
            <div className="md:hidden">
              <Button variant="ghost" size="sm" onClick={toggleMenu} className="text-gray-700">
                {isMenuOpen ? <X size={20} /> : <Menu size={20} />}
              </Button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {isMenuOpen && (
            <div className="md:hidden border-t border-[#50bed2]/20 bg-white/95 backdrop-blur-xl">
              <div className="px-2 pt-2 pb-3 space-y-1">
                <Link
                  href="/blog"
                  className="block px-3 py-2 text-gray-700 hover:text-[#50bed2] hover:bg-[#50bed2]/10 rounded-xl transition-colors duration-200"
                  onClick={toggleMenu}
                >
                  Blog
                </Link>
                {isSignedIn ? (
                  <>
                    <Link
                      href="/dashboard"
                      className="block px-3 py-2 text-white bg-gradient-to-r from-green-600 to-teal-600 rounded-xl transition-colors duration-200 font-medium"
                      onClick={toggleMenu}
                    >
                      Dashboard
                    </Link>
                    <div className="px-3 py-2">
                      <UserButton afterSignOutUrl="/" />
                    </div>
                  </>
                ) : (
                  <>
                    <button
                      className="block w-full text-left px-3 py-2 text-gray-700 hover:text-[#50bed2] hover:bg-[#50bed2]/10 rounded-xl transition-colors duration-200"
                      onClick={() => {
                        openSignIn();
                        toggleMenu();
                      }}
                    >
                      Iniciar Sesión
                    </button>
                    <button
                      className="block w-full text-left px-3 py-2 text-white bg-gradient-to-r from-[#50bed2] to-[#50bed2]/90 rounded-xl transition-colors duration-200 font-medium"
                      onClick={() => {
                        openSignUp();
                        toggleMenu();
                      }}
                    >
                      Registrarse
                    </button>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </nav>

      {/* Auth Modals */}
      <SignInModal 
        isOpen={isSignInOpen} 
        onClose={closeAll} 
        onSwitchToSignUp={switchToSignUp}
      />
      <SignUpModal 
        isOpen={isSignUpOpen} 
        onClose={closeAll} 
        onSwitchToSignIn={switchToSignIn}
      />
    </>
  );
}