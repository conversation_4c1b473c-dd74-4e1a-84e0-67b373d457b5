'use client';

import { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Phone, 
  AlertTriangle, 
  Pill, 
  FileText,
  Loader2,
  Heart,
  Shield,
  Download,
  ExternalLink,
  Clock,
  Mail
} from 'lucide-react';
import { toast } from 'sonner';

interface PreCheckinQuickViewProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId?: string;
  patientName?: string;
}

interface DocumentFile {
  type: 'lab_results' | 'imaging' | 'prescription' | 'insurance' | 'other';
  url: string;
  filename: string;
  description?: string;
  uploadedAt: string;
}

interface PreCheckinData {
  // Datos básicos
  attendance?: 'yes' | 'no' | '';
  willAttend?: 'yes' | 'no' | '';
  
  // Información médica
  hasSymptoms?: boolean;
  symptoms?: string;
  takingMedications?: boolean;
  medications?: string;
  hasAllergies?: boolean;
  allergies?: string;
  
  // Información de contacto
  phone?: string;
  contactInfo?: {
    phone?: string;
    emergencyContact?: string;
    emergencyPhone?: string;
  };
  emergencyContact?: string;
  emergencyPhone?: string;
  
  // Información del acompañante (para dependientes)
  companionInfo?: {
    name?: string;
    relationship?: string;
    phone?: string;
  };
  
  // Información adicional
  chiefComplaint?: string;
  additionalNotes?: string;
  documents?: DocumentFile[];
  
  // Metadatos
  isDependent?: boolean;
  completedAt?: string;
  completedBy?: string;
}

const DOCUMENT_TYPES = {
  lab_results: 'Exámenes de Laboratorio',
  imaging: 'Estudios de Imagen',
  prescription: 'Recetas Médicas',
  insurance: 'Documentos de Seguro',
  other: 'Otros Documentos'
} as const;

export default function PreCheckinQuickView({ 
  isOpen, 
  onClose, 
  appointmentId,
  patientName 
}: PreCheckinQuickViewProps) {
  const [loading, setLoading] = useState(false);
  const [preCheckinData, setPreCheckinData] = useState<PreCheckinData | null>(null);
  const [appointmentInfo, setAppointmentInfo] = useState<any>(null);

  useEffect(() => {
    if (isOpen && appointmentId) {
      loadPreCheckinData();
    }
  }, [isOpen, appointmentId]);

  const loadPreCheckinData = async () => {
    if (!appointmentId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/pre-checkin/submit?appointmentId=${appointmentId}`);
      const result = await response.json();
      
      if (response.ok && result.data) {
        setAppointmentInfo(result.data);
        if (result.data.preCheckinData) {
          setPreCheckinData(result.data.preCheckinData);
        } else {
          setPreCheckinData(null);
        }
      } else {
        toast.error('Error al cargar datos del pre-checkin');
      }
    } catch (error) {
      console.error('Error loading pre-checkin data:', error);
      toast.error('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  const openDocument = (url: string) => {
    window.open(url, '_blank');
  };

  const getStatusInfo = () => {
    if (!appointmentInfo) return null;
    
    if (appointmentInfo.preCheckinCompleted) {
      return {
        status: 'completed',
        label: 'Completado',
        color: 'text-green-600 bg-green-50',
        icon: CheckCircle
      };
    } else if (appointmentInfo.preCheckinSent) {
      return {
        status: 'pending',
        label: 'Pendiente',
        color: 'text-orange-600 bg-orange-50',
        icon: Clock
      };
    } else {
      return {
        status: 'not-sent',
        label: 'No enviado',
        color: 'text-gray-600 bg-gray-50',
        icon: XCircle
      };
    }
  };

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Cargando Pre-checkin</DialogTitle>
            <DialogDescription>
              Obteniendo información del pre-checkin...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando información del pre-checkin...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  const statusInfo = getStatusInfo();

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Pre-checkin - {patientName || 'Paciente'}
          </DialogTitle>
          <DialogDescription>
            Información médica recopilada antes de la consulta
          </DialogDescription>
        </DialogHeader>

        {statusInfo && (
          <div className="flex items-center gap-2 mb-4">
            <Badge className={`${statusInfo.color} border-0`}>
              <statusInfo.icon className="h-3 w-3 mr-1" />
              {statusInfo.label}
            </Badge>
            {preCheckinData?.completedAt && (
              <span className="text-sm text-gray-500">
                Completado el {new Date(preCheckinData.completedAt).toLocaleDateString('es-GT', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </span>
            )}
          </div>
        )}

        {!preCheckinData ? (
          <Card>
            <CardContent className="text-center py-8">
              <XCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Pre-checkin no completado</h3>
              <p className="text-gray-600">
                {appointmentInfo?.preCheckinSent 
                  ? 'El paciente aún no ha completado su pre-checkin'
                  : 'El pre-checkin no ha sido enviado al paciente'
                }
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="space-y-4">
            {/* Resumen en formato de texto */}
            <Card className="bg-gray-50">
              <CardHeader>
                <CardTitle className="text-base flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Resumen del Pre-checkin
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="bg-white rounded-lg p-4 border border-gray-200 font-mono text-sm leading-relaxed">
                  <div className="space-y-3 text-gray-700">
                    {/* Estado de confirmación */}
                    <div className="pb-2 border-b border-gray-100">
                      <span className="font-semibold text-gray-900">CONFIRMACIÓN DE ASISTENCIA:</span>
                      <br />
                      {(preCheckinData.willAttend || preCheckinData.attendance) === 'yes' 
                        ? '✅ El paciente CONFIRMÓ su asistencia a la cita.' 
                        : '❌ El paciente NO podrá asistir a la cita.'}
                    </div>

                    {/* Información de contacto */}
                    {(preCheckinData.phone || preCheckinData.contactInfo?.phone || 
                      preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact) && (
                      <div className="pb-2 border-b border-gray-100">
                        <span className="font-semibold text-gray-900">INFORMACIÓN DE CONTACTO:</span>
                        <br />
                        {(preCheckinData.phone || preCheckinData.contactInfo?.phone) && (
                          <>• Teléfono: {preCheckinData.phone || preCheckinData.contactInfo?.phone}<br /></>
                        )}
                        {(preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact) && (
                          <>• Contacto de emergencia: {preCheckinData.emergencyContact || preCheckinData.contactInfo?.emergencyContact}
                          {(preCheckinData.emergencyPhone || preCheckinData.contactInfo?.emergencyPhone) && 
                            ` - Tel: ${preCheckinData.emergencyPhone || preCheckinData.contactInfo?.emergencyPhone}`}<br /></>
                        )}
                      </div>
                    )}

                    {/* Síntomas */}
                    <div className="pb-2 border-b border-gray-100">
                      <span className="font-semibold text-gray-900">SÍNTOMAS ACTUALES:</span>
                      <br />
                      {preCheckinData.hasSymptoms ? (
                        <>⚠️ El paciente reporta los siguientes síntomas:<br />
                        <span className="ml-4 italic">"{preCheckinData.symptoms}"</span></>
                      ) : (
                        '✅ El paciente NO presenta síntomas actualmente.'
                      )}
                    </div>

                    {/* Medicamentos */}
                    <div className="pb-2 border-b border-gray-100">
                      <span className="font-semibold text-gray-900">MEDICAMENTOS:</span>
                      <br />
                      {preCheckinData.takingMedications ? (
                        <>💊 El paciente está tomando los siguientes medicamentos:<br />
                        <span className="ml-4 italic">"{preCheckinData.medications}"</span></>
                      ) : (
                        '✅ El paciente NO está tomando medicamentos actualmente.'
                      )}
                    </div>

                    {/* Alergias */}
                    <div className="pb-2 border-b border-gray-100">
                      <span className="font-semibold text-gray-900">ALERGIAS:</span>
                      <br />
                      {preCheckinData.hasAllergies ? (
                        <>⚠️ IMPORTANTE - El paciente tiene las siguientes alergias:<br />
                        <span className="ml-4 italic text-orange-600 font-semibold">"{preCheckinData.allergies}"</span></>
                      ) : (
                        '✅ El paciente NO reporta alergias conocidas.'
                      )}
                    </div>

                    {/* Información del acompañante */}
                    {preCheckinData.isDependent && preCheckinData.companionInfo && (
                      <div className="pb-2 border-b border-gray-100">
                        <span className="font-semibold text-gray-900">ACOMPAÑANTE DEL PACIENTE:</span>
                        <br />
                        {preCheckinData.companionInfo.name && (
                          <>• Nombre: {preCheckinData.companionInfo.name}<br /></>
                        )}
                        {preCheckinData.companionInfo.relationship && (
                          <>• Relación: {preCheckinData.companionInfo.relationship}<br /></>
                        )}
                        {preCheckinData.companionInfo.phone && (
                          <>• Teléfono: {preCheckinData.companionInfo.phone}<br /></>
                        )}
                      </div>
                    )}

                    {/* Motivo de consulta */}
                    {preCheckinData.chiefComplaint && (
                      <div className="pb-2 border-b border-gray-100">
                        <span className="font-semibold text-gray-900">MOTIVO DE CONSULTA:</span>
                        <br />
                        <span className="italic">"{preCheckinData.chiefComplaint}"</span>
                      </div>
                    )}

                    {/* Documentos */}
                    {preCheckinData.documents && preCheckinData.documents.length > 0 && (
                      <div className="pb-2 border-b border-gray-100">
                        <span className="font-semibold text-gray-900">DOCUMENTOS ADJUNTOS:</span>
                        <br />
                        📎 El paciente adjuntó {preCheckinData.documents.length} documento(s):
                        {preCheckinData.documents.map((doc, index) => (
                          <div key={index} className="ml-4">
                            • {DOCUMENT_TYPES[doc.type]}: {doc.filename}
                            {doc.description && <span className="text-gray-600"> - {doc.description}</span>}
                          </div>
                        ))}
                      </div>
                    )}

                    {/* Observaciones adicionales */}
                    {preCheckinData.additionalNotes && (
                      <div className="pb-2">
                        <span className="font-semibold text-gray-900">OBSERVACIONES ADICIONALES:</span>
                        <br />
                        <span className="italic">"{preCheckinData.additionalNotes}"</span>
                      </div>
                    )}

                    {/* Fecha de completado */}
                    {preCheckinData.completedAt && (
                      <div className="pt-2 border-t border-gray-200 text-xs text-gray-500">
                        Pre-checkin completado el {new Date(preCheckinData.completedAt).toLocaleDateString('es-GT', {
                          day: 'numeric',
                          month: 'long',
                          year: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Documentos adjuntos con botones de descarga */}
            {preCheckinData.documents && preCheckinData.documents.length > 0 && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <FileText className="h-4 w-4 text-green-600" />
                    Acceso a Documentos
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {preCheckinData.documents.map((doc, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        onClick={() => openDocument(doc.url)}
                        className="justify-start text-left"
                      >
                        <ExternalLink className="h-4 w-4 mr-2" />
                        <span className="truncate">{doc.filename}</span>
                      </Button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        <div className="flex justify-end pt-4">
          <Button onClick={onClose}>
            Cerrar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}