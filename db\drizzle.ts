import { config } from "dotenv";
import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';
import * as schema from './schema';

config({ path: ".env.local" });

// Configuración mejorada del pool de conexiones
const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
  max: 25, // Aumentar el número máximo de conexiones
  idleTimeoutMillis: 60000, // 60 segundos antes de cerrar conexiones inactivas
  connectionTimeoutMillis: 10000, // 10 segundos para timeout de conexión
  ssl: {
    rejectUnauthorized: false // Para Neon en desarrollo
  }
});

// Manejo de errores del pool
pool.on('error', (err) => {
  console.error('Error inesperado en el pool de conexiones:', err);
});

// Verificar conexión al iniciar
pool.connect()
  .then(client => {
    console.log('Conexión a base de datos establecida');
    client.release();
  })
  .catch(err => {
    console.error('Error al conectar con la base de datos:', err);
  });

export const db = drizzle(pool, { schema });
