import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, appointments, consultories, guardianPatientRelations } from '@/db/schema';
import { eq, and, or, gte, lte } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { generateShortCode } from '@/lib/short-codes';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export async function POST(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { slotId, pacienteExistente, pacienteNuevo, guardian } = body;

    // Validaciones básicas
    if (!slotId) {
      return NextResponse.json(
        { success: false, error: 'Campo requerido: slotId' },
        { status: 400 }
      );
    }

    if (!pacienteExistente && !pacienteNuevo) {
      return NextResponse.json(
        { success: false, error: 'Debe proporcionar pacienteExistente o pacienteNuevo' },
        { status: 400 }
      );
    }

    // Extraer información del slotId (formato: doctorId-timestamp)
    const [doctorId, timestampStr] = slotId.split('-');
    
    if (!doctorId || !timestampStr) {
      return NextResponse.json(
        { success: false, error: 'Formato de slotId inválido. Debe ser: doctorId-timestamp' },
        { status: 400 }
      );
    }

    const slotTimestamp = parseInt(timestampStr);
    if (isNaN(slotTimestamp)) {
      return NextResponse.json(
        { success: false, error: 'Timestamp inválido en slotId' },
        { status: 400 }
      );
    }

    const startDateTime = new Date(slotTimestamp);
    const endDateTime = new Date(startDateTime.getTime() + (30 * 60 * 1000)); // 30 minutos
    const scheduledDate = new Date(startDateTime);
    scheduledDate.setHours(0, 0, 0, 0);

    console.log(`📅 Creando cita VAPI - Slot: ${format(startDateTime, 'yyyy-MM-dd HH:mm')}`);

    // 1. Verificar que el doctor existe y está activo
    const doctor = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
      })
      .from(user)
      .where(
        and(
          eq(user.id, doctorId),
          eq(user.role, 'doctor'),
          eq(user.isActive, true)
        )
      )
      .limit(1);

    if (doctor.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Doctor no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const doctorInfo = doctor[0];

    // 2. Obtener el consultorio por defecto (primer consultorio activo)
    // TODO: En el futuro, usar consultorioId del prompt VAPI
    const consultorio = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);
    
    if (consultorio.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No hay consultorios disponibles' },
        { status: 500 }
      );
    }

    const consultorioInfo = consultorio[0];

    // 3. Verificar que el slot aún está disponible (race condition)
    const conflictingAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          eq(appointments.scheduledDate, scheduledDate),
          or(
            and(
              gte(appointments.startTime, startDateTime),
              lte(appointments.startTime, endDateTime)
            ),
            and(
              gte(appointments.endTime, startDateTime),
              lte(appointments.endTime, endDateTime)
            ),
            and(
              lte(appointments.startTime, startDateTime),
              gte(appointments.endTime, endDateTime)
            )
          ),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'in_progress'),
            eq(appointments.status, 'pending_confirmation')
          )
        )
      );

    if (conflictingAppointments.length > 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'El horario ya no está disponible. Por favor seleccione otro.',
          code: 'SLOT_NOT_AVAILABLE'
        },
        { status: 409 }
      );
    }

    let finalPatientId: string;
    let patientName: string;
    let isNewPatient = false;

    // 4. Manejar paciente existente vs nuevo
    if (pacienteExistente) {
      // PACIENTE EXISTENTE
      console.log(`👤 Usando paciente existente: ${pacienteExistente.id}`);
      
      // Verificar que el paciente existe
      const existingPatient = await db
        .select({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          isActive: user.isActive,
        })
        .from(user)
        .where(
          and(
            eq(user.id, pacienteExistente.id),
            eq(user.role, 'patient'),
            eq(user.isActive, true)
          )
        )
        .limit(1);

      if (existingPatient.length === 0) {
        return NextResponse.json(
          { success: false, error: 'Paciente no encontrado o inactivo' },
          { status: 404 }
        );
      }

      finalPatientId = pacienteExistente.id;
      patientName = `${existingPatient[0].firstName} ${existingPatient[0].lastName}`;

    } else {
      // PACIENTE NUEVO
      console.log(`✨ Creando paciente nuevo: ${pacienteNuevo.nombres} ${pacienteNuevo.apellidos}`);
      isNewPatient = true;

      // Validar datos del paciente nuevo
      if (!pacienteNuevo.nombres || !pacienteNuevo.apellidos || !pacienteNuevo.telefono || pacienteNuevo.edad === undefined) {
        return NextResponse.json(
          { success: false, error: 'Paciente nuevo requiere: nombres, apellidos, telefono, edad' },
          { status: 400 }
        );
      }

      const isMinor = pacienteNuevo.edad < 18;

      // Si es menor, validar datos del guardián
      if (isMinor && (!guardian || !guardian.nombres || !guardian.apellidos || !guardian.telefono)) {
        return NextResponse.json(
          { success: false, error: 'Paciente menor requiere datos completos del guardián' },
          { status: 400 }
        );
      }

      // Crear paciente
      const patientId = `patient_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const birthYear = new Date().getFullYear() - pacienteNuevo.edad;
      const approximateBirthDate = new Date(birthYear, 0, 1); // 1 de enero del año calculado

      const newPatient = {
        id: patientId,
        firstName: pacienteNuevo.nombres.trim(),
        lastName: pacienteNuevo.apellidos.trim(),
        email: `patient_${Date.now()}@temp.local`, // Email temporal
        phone: pacienteNuevo.telefono,
        dateOfBirth: approximateBirthDate.toISOString().split('T')[0],
        role: 'patient' as const,
        isActive: true,
        createdBy: 'vapi-system',
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await db.insert(user).values(newPatient);
      console.log(`✅ Paciente creado con ID: ${patientId}`);

      // Si es menor, crear guardián
      if (isMinor && guardian) {
        const guardianId = `guardian_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        
        const newGuardian = {
          id: guardianId,
          firstName: guardian.nombres.trim(),
          lastName: guardian.apellidos.trim(),
          email: `guardian_${Date.now()}@temp.local`, // Email temporal
          phone: guardian.telefono,
          role: 'guardian' as const,
          isActive: true,
          createdBy: 'vapi-system',
          createdAt: new Date(),
          updatedAt: new Date()
        };

        await db.insert(user).values(newGuardian);
        console.log(`👨‍👩‍👧‍👦 Guardián creado con ID: ${guardianId}`);

        // Crear relación guardián-paciente
        const relationId = nanoid();
        await db.insert(guardianPatientRelations).values({
          id: relationId,
          guardianId: guardianId,
          patientId: patientId,
          relationship: guardian.relacion || 'familiar',
          isPrimary: true,
          canMakeDecisions: true,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date()
        });

        console.log(`🔗 Relación guardián-paciente creada`);
      }

      finalPatientId = patientId;
      patientName = `${pacienteNuevo.nombres} ${pacienteNuevo.apellidos}`;
    }

    // 5. Generar shortCode único
    const shortCode = generateShortCode();

    // 6. Crear la cita
    const appointmentId = nanoid();
    const motivoConsulta = pacienteExistente?.motivoConsulta || pacienteNuevo?.motivoConsulta || 'Consulta médica programada por teléfono';

    const newAppointment = {
      id: appointmentId,
      title: `${motivoConsulta} - ${patientName}`,
      description: `Cita agendada por VAPI para ${patientName}`,
      doctorId,
      patientId: finalPatientId,
      consultoryId: consultorioInfo.id,
      serviceId: null,
      activityTypeId: null,
      chiefComplaint: motivoConsulta,
      scheduledDate,
      startTime: startDateTime,
      endTime: endDateTime,
      duration: 30,
      status: 'pending_confirmation' as const,
      confirmationStatus: 'pending' as const,
      shortCode,
      estimatedPrice: null,
      currency: 'GTQ',
      paymentStatus: 'pending' as const,
      isFollowUp: false,
      isEmergency: false,
      requiresReminder: true,
      // Guardar datos temporales si es paciente nuevo (para crear paciente real después)
      tempPatientData: isNewPatient ? JSON.stringify({
        name: patientName,
        phone: pacienteNuevo?.telefono,
        edad: pacienteNuevo?.edad,
        source: 'vapi'
      }) : null,
      createdBy: 'vapi-system',
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(appointments).values(newAppointment).returning();
    const createdAppointment = result[0];

    // 7. Generar mensaje fonético para el código
    const phoneticCode = generatePhoneticCode(shortCode);

    // 8. Formatear fecha y hora para respuesta
    const appointmentDate = format(startDateTime, "EEEE, dd 'de' MMMM", { locale: es });
    const appointmentTime = format(startDateTime, 'h:mm a');

    console.log(`✅ Cita VAPI creada exitosamente - ID: ${appointmentId}, ShortCode: ${shortCode}`);

    return NextResponse.json({
      success: true,
      data: {
        citaId: createdAppointment.id,
        shortCode,
        codigoFonetico: phoneticCode,
        pacienteNombre: patientName,
        pacienteId: finalPatientId,
        doctorNombre: `${doctorInfo.firstName} ${doctorInfo.lastName}`,
        consultorioNombre: consultorioInfo.name,
        fecha: appointmentDate,
        hora: appointmentTime,
        motivoConsulta,
        linkConfirmacion: "www.doctorabarbara.com/confirmar",
        validoHasta: "24 horas",
        esPacienteNuevo: isNewPatient,
        mensaje: `Cita agendada para ${patientName}. Código de confirmación: ${phoneticCode}. Confirme en www.doctorabarbara.com/confirmar dentro de las próximas 24 horas.`
      }
    });

  } catch (error) {
    console.error('Error en crear-cita VAPI:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// Función para generar código fonético (reutilizada del archivo original)
function generatePhoneticCode(code: string): string {
  const phonetic: Record<string, string> = {
    'A': 'A de avión',
    'B': 'B de barco',
    'C': 'C de casa',
    'D': 'D de dado',
    'E': 'E de elefante',
    'F': 'F de fuego',
    'G': 'G de gato',
    'H': 'H de hotel',
    'J': 'J de jardín',
    'K': 'K de kilo',
    'L': 'L de luna',
    'M': 'M de mesa',
    'N': 'N de nube',
    'P': 'P de papel',
    'Q': 'Q de queso',
    'R': 'R de radio',
    'S': 'S de sol',
    'T': 'T de taxi',
    'U': 'U de uva',
    'V': 'V de vaso',
    'W': 'W de whisky',
    'X': 'X de xilófono',
    'Y': 'Y de yate',
    'Z': 'Z de zapato'
  };

  const parts = [];
  
  for (const char of code) {
    if (phonetic[char]) {
      parts.push(phonetic[char]);
    } else if (/\d/.test(char)) {
      parts.push(char);
    }
  }

  return parts.join(', ');
}