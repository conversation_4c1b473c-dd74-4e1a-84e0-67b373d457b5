import { neon } from '@neondatabase/serverless';
import { config } from 'dotenv';
import { nanoid } from 'nanoid';

// Cargar variables de entorno
config({ path: '.env.local' });

const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
  console.error('❌ DATABASE_URL no está configurado');
  process.exit(1);
}

const sql = neon(DATABASE_URL);

const companiesData = [
  {
    id: nanoid(),
    businessName: 'Clínica Pediátrica Guatemala S.A.',
    commercialName: 'Clínica Pediátrica Guatemala',
    nit: '12345678-9',
    address: '5ta Avenida 10-20 Zona 1, Ciudad de Guatemala',
    phone: '2234-5678',
    email: '<EMAIL>',
    website: 'www.clinicapediatrica.gt',
    legalRepresentative: 'Dr. <PERSON>',
    activityType: 'Servicios Médicos Especializados',
    taxRegime: 'Régimen General',
    fiscalAddress: '5ta Avenida 10-20 Zona 1, Ciudad de Guatemala',
    contactPerson: '<PERSON>',
    contactPhone: '5555-1234',
    contactEmail: '<EMAIL>',
    isActive: true,
  },
  {
    id: nanoid(),
    businessName: 'Centro Médico Infantil San José, S.A.',
    commercialName: 'Centro Médico San José',
    nit: '23456789-0',
    address: '12 Calle 8-45 Zona 10, Ciudad de Guatemala',
    phone: '2366-7890',
    email: '<EMAIL>',
    website: 'www.centrosanjose.com.gt',
    legalRepresentative: 'Dra. María Elena Rodríguez',
    activityType: 'Servicios de Salud Pediátrica',
    taxRegime: 'Régimen Sobre las Utilidades de Actividades Lucrativas',
    fiscalAddress: '12 Calle 8-45 Zona 10, Ciudad de Guatemala',
    contactPerson: 'Luis Fernando Morales',
    contactPhone: '4567-8901',
    contactEmail: '<EMAIL>',
    isActive: true,
  },
  {
    id: nanoid(),
    businessName: 'Servicios Médicos Integrados de Guatemala, Sociedad Anónima',
    commercialName: 'SMIG',
    nit: '34567890-1',
    address: 'Avenida Roosevelt 15-30 Zona 11, Ciudad de Guatemala',
    phone: '2422-3456',
    email: '<EMAIL>',
    website: 'www.smig.gt',
    legalRepresentative: 'Dr. Roberto Antonio Castillo',
    activityType: 'Servicios Médicos Integrales',
    taxRegime: 'Régimen General',
    fiscalAddress: null,
    contactPerson: 'Carmen Patricia López',
    contactPhone: '3456-7890',
    contactEmail: '<EMAIL>',
    isActive: true,
  },
  {
    id: nanoid(),
    businessName: 'Hospital Infantil de Especialidades, S.A.',
    commercialName: 'Hospital Infantil',
    nit: '45678901-2',
    address: 'Calzada Roosevelt 45-67 Zona 7, Ciudad de Guatemala',
    phone: '2471-2345',
    email: '<EMAIL>',
    website: 'www.hospitalinfantil.gt',
    legalRepresentative: 'Dr. Carlos Eduardo Méndez',
    activityType: 'Servicios Hospitalarios Pediátricos',
    taxRegime: 'Régimen General',
    fiscalAddress: 'Calzada Roosevelt 45-67 Zona 7, Ciudad de Guatemala',
    contactPerson: 'Sandra Elizabeth Hernández',
    contactPhone: '2345-6789',
    contactEmail: '<EMAIL>',
    isActive: true,
  },
  {
    id: nanoid(),
    businessName: 'Laboratorio Clínico Pediátrico GuateKids, S.A.',
    commercialName: 'GuateKids Lab',
    nit: '56789012-3',
    address: '7ma Avenida 12-34 Zona 9, Ciudad de Guatemala',
    phone: '2334-5678',
    email: '<EMAIL>',
    website: 'www.guatekidslab.com',
    legalRepresentative: 'Licda. Patricia Eugenia Morales',
    activityType: 'Servicios de Laboratorio Clínico',
    taxRegime: 'Régimen Sobre las Utilidades de Actividades Lucrativas',
    fiscalAddress: null,
    contactPerson: 'José Miguel Vásquez',
    contactPhone: '1234-5678',
    contactEmail: '<EMAIL>',
    isActive: true,
  }
];

async function seedCompanies() {
  try {
    console.log('🏢 Iniciando seed de empresas...');

    // Limpiar tabla existente (opcional)
    console.log('🧹 Limpiando empresas existentes...');
    await sql`DELETE FROM companies`;

    // Insertar empresas
    console.log('📝 Insertando empresas...');
    for (const company of companiesData) {
      await sql`
        INSERT INTO companies (
          id, "businessName", "commercialName", nit, address, phone, email, website,
          "legalRepresentative", "activityType", "taxRegime", "fiscalAddress",
          "contactPerson", "contactPhone", "contactEmail", "isActive", "createdAt", "updatedAt"
        ) VALUES (
          ${company.id}, ${company.businessName}, ${company.commercialName}, ${company.nit},
          ${company.address}, ${company.phone}, ${company.email}, ${company.website},
          ${company.legalRepresentative}, ${company.activityType}, ${company.taxRegime}, ${company.fiscalAddress},
          ${company.contactPerson}, ${company.contactPhone}, ${company.contactEmail}, ${company.isActive},
          NOW(), NOW()
        )
      `;
    }

    console.log(`✅ Seeded ${companiesData.length} empresas exitosamente`);

    // Verificar resultados
    const count = await sql`SELECT COUNT(*) as count FROM companies`;
    console.log(`📊 Total de empresas en la base de datos: ${count[0].count}`);

  } catch (error) {
    console.error('❌ Error durante el seed de empresas:', error);
    process.exit(1);
  }
}

// Ejecutar el seed
if (require.main === module) {
  seedCompanies()
    .then(() => {
      console.log('🎉 Seed de empresas completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error en el seed de empresas:', error);
      process.exit(1);
    });
}

export { seedCompanies };