import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { consultories } from '@/db/schema';
import { eq, ne, count } from 'drizzle-orm';

// POST - Activar/Desactivar consultorio
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de consultorios.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el consultorio existe
    const existingConsultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, id))
      .limit(1);

    if (existingConsultory.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado' }, { status: 404 });
    }

    // Cambiar el estado
    const newStatus = !existingConsultory[0].isActive;

    // Si se está activando un consultorio, verificar que no haya otros activos
    if (newStatus) {
      // Desactivar todos los demás consultorios antes de activar este
      await db
        .update(consultories)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(ne(consultories.id, id));
    } else {
      // Si se está desactivando, verificar que no sea el único activo
      const [{ count: activeCount }] = await db
        .select({ count: count() })
        .from(consultories)
        .where(eq(consultories.isActive, true));

      if (activeCount <= 1) {
        return NextResponse.json({ 
          error: 'No se puede desactivar el único consultorio activo. Debe haber al menos un consultorio activo en el sistema.',
          code: 'LAST_ACTIVE_CONSULTORY'
        }, { status: 400 });
      }
    }

    // Actualizar el consultorio objetivo
    const [updatedConsultory] = await db
      .update(consultories)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(consultories.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedConsultory,
      message: `Consultorio ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling consultory status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}