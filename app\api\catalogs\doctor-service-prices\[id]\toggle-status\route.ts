import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices } from '@/db/schema';
import { eq, and, ne } from 'drizzle-orm';

// POST /api/catalogs/doctor-service-prices/[id]/toggle-status - Activar/Desactivar precio
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Obtener el precio actual
    const currentPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(eq(doctorServicePrices.id, id))
      .limit(1);

    if (!currentPrice.length) {
      return NextResponse.json(
        { error: 'Precio personalizado no encontrado' },
        { status: 404 }
      );
    }

    const price = currentPrice[0];
    const newStatus = !price.isActive;

    // Si se está activando, verificar que no hay otro precio activo para la misma combinación
    if (newStatus === true) {
      const conflictingPrice = await db
        .select()
        .from(doctorServicePrices)
        .where(
          and(
            eq(doctorServicePrices.doctorId, price.doctorId),
            eq(doctorServicePrices.serviceId, price.serviceId),
            eq(doctorServicePrices.isActive, true),
            // Excluir el registro actual
            ne(doctorServicePrices.id, id)
          )
        )
        .limit(1);

      if (conflictingPrice.length) {
        return NextResponse.json(
          { error: 'Ya existe otro precio activo para este médico y servicio. Desactive el otro precio primero.' },
          { status: 400 }
        );
      }
    }

    // Actualizar estado
    await db
      .update(doctorServicePrices)
      .set({
        isActive: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(doctorServicePrices.id, id));

    return NextResponse.json({
      success: true,
      message: `Precio personalizado ${newStatus ? 'activado' : 'desactivado'} exitosamente`,
      data: {
        id,
        isActive: newStatus,
      },
    });

  } catch (error: any) {
    console.error('Error toggling doctor service price status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}