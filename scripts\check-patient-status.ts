import { db } from '../db/drizzle';
import { user, userRoles } from '../db/schema';
import { desc, eq, and } from 'drizzle-orm';

async function checkRecentPatients() {
  console.log('🔍 Buscando pacientes recientes...\n');
  
  try {
    // Buscar los últimos 5 usuarios con rol de paciente
    const recentPatients = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        overallStatus: user.overallStatus,
        role: userRoles.role,
        roleStatus: userRoles.status,
        createdAt: user.createdAt,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(eq(userRoles.role, 'patient'))
      .orderBy(desc(user.createdAt))
      .limit(5);

    console.log(`📊 Últimos ${recentPatients.length} pacientes:\n`);

    for (const patient of recentPatients) {
      console.log('─'.repeat(60));
      console.log(`ID: ${patient.id}`);
      console.log(`Nombre: ${patient.firstName} ${patient.lastName}`);
      console.log(`Email: ${patient.email}`);
      console.log(`Estado general: ${patient.overallStatus || 'NULL'}`);
      console.log(`Estado del rol: ${patient.roleStatus || 'NULL'}`);
      console.log(`Creado: ${patient.createdAt}`);
      
      // Verificar si aparecería en la API
      const passesFilters = patient.overallStatus === 'active' && patient.roleStatus === 'active';
      console.log(`¿Aparece en la API?: ${passesFilters ? '✅ SÍ' : '❌ NO'}`);
    }

    console.log('\n' + '─'.repeat(60));
    
    // Contar cuántos pacientes activos hay
    const activePatients = await db
      .select({ count: user.id })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active'),
          eq(user.overallStatus, 'active')
        )
      );

    console.log(`\n✅ Total de pacientes activos (visibles en la API): ${activePatients.length}`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkRecentPatients();