import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { departments } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Toggle status de departamento (activar/desactivar)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de departamentos.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el departamento existe
    const existingDepartment = await db
      .select()
      .from(departments)
      .where(eq(departments.id, id))
      .limit(1);

    if (existingDepartment.length === 0) {
      return NextResponse.json({ error: 'Departamento no encontrado' }, { status: 404 });
    }

    const currentDepartment = existingDepartment[0];
    const newStatus = !currentDepartment.isActive;

    // Actualizar el estado
    const [updatedDepartment] = await db
      .update(departments)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(departments.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedDepartment,
      message: `Departamento ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling department status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}