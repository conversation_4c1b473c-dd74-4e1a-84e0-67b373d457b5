CREATE TABLE "doctor_schedule_exceptions" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text,
	"exceptionDate" timestamp NOT NULL,
	"type" text NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"startTime" text,
	"endTime" text,
	"isAllDay" boolean DEFAULT false,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "doctor_schedules" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text,
	"dayOfWeek" integer NOT NULL,
	"startTime" text NOT NULL,
	"endTime" text NOT NULL,
	"lunchBreakStart" text,
	"lunchBreakEnd" text,
	"appointmentDuration" integer DEFAULT 30,
	"maxAppointmentsPerHour" integer DEFAULT 2,
	"isActive" boolean DEFAULT true,
	"allowEmergencies" boolean DEFAULT true,
	"allowOnlineBooking" boolean DEFAULT true,
	"notes" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "doctor_schedule_exceptions" ADD CONSTRAINT "doctor_schedule_exceptions_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_schedule_exceptions" ADD CONSTRAINT "doctor_schedule_exceptions_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_schedules" ADD CONSTRAINT "doctor_schedules_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_schedules" ADD CONSTRAINT "doctor_schedules_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "doctor_schedule_exceptions_doctor_idx" ON "doctor_schedule_exceptions" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "doctor_schedule_exceptions_consultory_idx" ON "doctor_schedule_exceptions" USING btree ("consultoryId");--> statement-breakpoint
CREATE INDEX "doctor_schedule_exceptions_date_idx" ON "doctor_schedule_exceptions" USING btree ("exceptionDate");--> statement-breakpoint
CREATE INDEX "doctor_schedule_exceptions_type_idx" ON "doctor_schedule_exceptions" USING btree ("type");--> statement-breakpoint
CREATE UNIQUE INDEX "doctor_schedule_exceptions_doctor_date_idx" ON "doctor_schedule_exceptions" USING btree ("doctorId","exceptionDate");--> statement-breakpoint
CREATE INDEX "doctor_schedules_doctor_idx" ON "doctor_schedules" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "doctor_schedules_consultory_idx" ON "doctor_schedules" USING btree ("consultoryId");--> statement-breakpoint
CREATE INDEX "doctor_schedules_day_idx" ON "doctor_schedules" USING btree ("dayOfWeek");--> statement-breakpoint
CREATE INDEX "doctor_schedules_active_idx" ON "doctor_schedules" USING btree ("isActive");--> statement-breakpoint
CREATE UNIQUE INDEX "doctor_schedules_doctor_day_idx" ON "doctor_schedules" USING btree ("doctorId","dayOfWeek","consultoryId");