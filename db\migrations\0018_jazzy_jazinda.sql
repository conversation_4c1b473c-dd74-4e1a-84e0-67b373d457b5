CREATE TABLE "appointment_slots" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"date" timestamp NOT NULL,
	"startTime" timestamp NOT NULL,
	"endTime" timestamp NOT NULL,
	"duration" integer NOT NULL,
	"isAvailable" boolean DEFAULT true,
	"isBlocked" boolean DEFAULT false,
	"blockReason" text,
	"isRecurring" boolean DEFAULT false,
	"recurrencePattern" text,
	"maxAppointments" integer DEFAULT 1,
	"allowedServiceTypes" jsonb DEFAULT '[]'::jsonb,
	"isEmergencyOnly" boolean DEFAULT false,
	"createdBy" text NOT NULL,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "appointments" (
	"id" text PRIMARY KEY NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"doctorId" text NOT NULL,
	"patientId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"serviceId" text,
	"chiefComplaint" text,
	"scheduledDate" timestamp NOT NULL,
	"startTime" timestamp NOT NULL,
	"endTime" timestamp NOT NULL,
	"duration" integer NOT NULL,
	"status" text DEFAULT 'scheduled' NOT NULL,
	"confirmationStatus" text DEFAULT 'pending',
	"checkedInAt" timestamp,
	"startedAt" timestamp,
	"completedAt" timestamp,
	"estimatedPrice" numeric(10, 2),
	"finalPrice" numeric(10, 2),
	"currency" text DEFAULT 'GTQ',
	"paymentStatus" text DEFAULT 'pending',
	"doctorNotes" text,
	"adminNotes" text,
	"cancellationReason" text,
	"isFollowUp" boolean DEFAULT false,
	"parentAppointmentId" text,
	"isEmergency" boolean DEFAULT false,
	"requiresReminder" boolean DEFAULT true,
	"createdBy" text NOT NULL,
	"updatedBy" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
DROP INDEX "doctor_service_active_idx";--> statement-breakpoint
ALTER TABLE "appointment_slots" ADD CONSTRAINT "appointment_slots_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointment_slots" ADD CONSTRAINT "appointment_slots_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointment_slots" ADD CONSTRAINT "appointment_slots_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_serviceId_medical_services_id_fk" FOREIGN KEY ("serviceId") REFERENCES "public"."medical_services"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_parentAppointmentId_appointments_id_fk" FOREIGN KEY ("parentAppointmentId") REFERENCES "public"."appointments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_updatedBy_user_id_fk" FOREIGN KEY ("updatedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "appointment_slots_doctor_date_idx" ON "appointment_slots" USING btree ("doctorId","date");--> statement-breakpoint
CREATE INDEX "appointment_slots_consultory_date_idx" ON "appointment_slots" USING btree ("consultoryId","date");--> statement-breakpoint
CREATE INDEX "appointment_slots_available_idx" ON "appointment_slots" USING btree ("isAvailable");--> statement-breakpoint
CREATE INDEX "appointment_slots_time_range_idx" ON "appointment_slots" USING btree ("startTime","endTime");--> statement-breakpoint
CREATE INDEX "appointment_slots_emergency_idx" ON "appointment_slots" USING btree ("isEmergencyOnly");--> statement-breakpoint
CREATE INDEX "appointments_doctor_idx" ON "appointments" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "appointments_patient_idx" ON "appointments" USING btree ("patientId");--> statement-breakpoint
CREATE INDEX "appointments_consultory_idx" ON "appointments" USING btree ("consultoryId");--> statement-breakpoint
CREATE INDEX "appointments_status_idx" ON "appointments" USING btree ("status");--> statement-breakpoint
CREATE INDEX "appointments_date_idx" ON "appointments" USING btree ("scheduledDate");--> statement-breakpoint
CREATE INDEX "appointments_time_range_idx" ON "appointments" USING btree ("startTime","endTime");--> statement-breakpoint
CREATE INDEX "appointments_emergency_idx" ON "appointments" USING btree ("isEmergency");--> statement-breakpoint
CREATE INDEX "appointments_follow_up_idx" ON "appointments" USING btree ("isFollowUp");--> statement-breakpoint
CREATE INDEX "appointments_parent_idx" ON "appointments" USING btree ("parentAppointmentId");--> statement-breakpoint
CREATE UNIQUE INDEX "doctor_service_active_idx" ON "doctor_service_prices" USING btree ("doctorId","serviceId") WHERE "doctor_service_prices"."isActive" = true;