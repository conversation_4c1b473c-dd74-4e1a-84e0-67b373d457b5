import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que es asistente o admin
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    if (!userRole.length || !['assistant', 'admin'].includes(userRole[0].role)) {
      return NextResponse.json({ 
        error: 'Solo asistentes y administradores pueden revertir no-shows' 
      }, { status: 403 });
    }

    const appointmentId = params.id;

    // Obtener información de la cita
    const appointment = await db
      .select({
        id: appointments.id,
        status: appointments.status,
        noShowReason: appointments.noShowReason,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (!appointment.length) {
      return NextResponse.json({ 
        error: 'Cita no encontrada' 
      }, { status: 404 });
    }

    const appointmentData = appointment[0];

    // Verificar que la cita esté en estado no_show
    if (appointmentData.status !== 'no_show') {
      return NextResponse.json({ 
        error: 'Solo se pueden revertir citas marcadas como no-show' 
      }, { status: 400 });
    }

    // Revertir a estado confirmed
    await db
      .update(appointments)
      .set({
        status: 'confirmed',
        noShowReason: null, // Limpiar la razón del no-show
        updatedAt: new Date(),
        updatedBy: userId
      })
      .where(eq(appointments.id, appointmentId));

    return NextResponse.json({
      success: true,
      message: 'Cita revertida exitosamente a estado confirmado',
      data: {
        appointmentId,
        previousStatus: 'no_show',
        newStatus: 'confirmed',
        revertedBy: userId,
        revertedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error reverting no-show:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}