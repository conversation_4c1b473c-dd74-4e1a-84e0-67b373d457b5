'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Settings,
  CheckCircle2,
  Clock,
  BarChart3,
  AlertCircle,
  DollarSign,
  Pill,
  User
} from 'lucide-react';

// Datos de las configuraciones organizadas por secciones lógicas
const configSections = {
  services: {
    title: "Servicios y Precios",
    description: "Gestiona tus precios personalizados y servicios médicos",
    icon: DollarSign,
    configs: [
      { 
        id: 'service-prices', 
        name: 'Precios de Servicios', 
        icon: DollarSign, 
        count: 0, 
        route: '/dashboard/doctor/catalogs/doctor-service-prices'
      }
    ]
  },
  medical: {
    title: "Recursos Médicos",
    description: "Medicamentos favoritos y recursos para consultas",
    icon: Pill,
    configs: [
      { 
        id: 'favorite-medications', 
        name: 'Medicamentos Favoritos', 
        icon: Pill, 
        count: 0, 
        route: '/dashboard/doctor/settings/favorite-medications'
      }
    ]
  }
};

export default function DoctorSettingsIndexPage() {
  const [configCounts, setConfigCounts] = useState<Record<string, number>>({});
  const [loadingCounts, setLoadingCounts] = useState(true);
  const router = useRouter();

  // Calcular estadísticas dinámicamente de todas las secciones
  const allConfigs = Object.values(configSections).flatMap(section => section.configs);
  const totalConfigs = allConfigs.length;
  const configsWithData = allConfigs.filter(config => (configCounts[config.id] || 0) > 0).length;
  const configsWithoutData = allConfigs.filter(config => (configCounts[config.id] || 0) === 0).length;

  const handleManageConfig = (config: any) => {
    router.push(config.route);
  };

  // Función para cargar conteos de cada configuración
  const loadConfigCounts = async () => {
    setLoadingCounts(true);
    const counts: Record<string, number> = {};
    
    try {
      // Cargar conteo de precios de servicios del doctor
      const pricesResponse = await fetch('/api/catalogs/doctor-service-prices?limit=1');
      if (pricesResponse.ok) {
        const pricesData = await pricesResponse.json();
        counts['service-prices'] = pricesData.pagination?.total || 0;
      } else {
        counts['service-prices'] = 0;
      }

      // Cargar conteo de medicamentos favoritos (será implementado)
      // Por ahora asumimos 0
      counts['favorite-medications'] = 0;

    } catch (error) {
      console.error('Error loading config counts:', error);
      counts['service-prices'] = 0;
      counts['favorite-medications'] = 0;
    }

    setConfigCounts(counts);
    setLoadingCounts(false);
  };

  useEffect(() => {
    loadConfigCounts();
  }, []);

  // Calcular estadísticas dinámicamente
  const overallStats = {
    total: totalConfigs,
    configured: configsWithData,
    pending: configsWithoutData,
    progress: Math.round((configsWithData / totalConfigs) * 100)
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Configuración del Doctor</h1>
              <p className="text-gray-600 text-sm lg:text-base">Administra tus configuraciones personales del sistema</p>
            </div>
          </div>
        </div>
      </div>

      {/* Estadísticas Generales */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Configuraciones</p>
                <p className="text-3xl font-bold text-gray-900">{overallStats.total}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Settings className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Configuradas</p>
                <p className="text-3xl font-bold text-green-600">{overallStats.configured}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Por Configurar</p>
                <p className="text-3xl font-bold text-orange-600">{overallStats.pending}</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completado</p>
                <p className="text-3xl font-bold text-blue-600">{overallStats.progress}%</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-3">
              <Progress value={overallStats.progress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configuraciones por Secciones */}
      <div className="space-y-6">
        {Object.entries(configSections).map(([sectionKey, section]) => {
          const sectionConfigs = section.configs.map(config => ({
            ...config,
            count: configCounts[config.id] || 0
          }));
          
          const configsWithData = sectionConfigs.filter(config => config.count > 0).length;
          const totalSectionConfigs = sectionConfigs.length;
          const sectionProgress = Math.round((configsWithData / totalSectionConfigs) * 100);
          
          return (
            <Card key={sectionKey}>
              <CardHeader>
                <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
                  <div>
                    <CardTitle className="text-lg lg:text-xl flex items-center gap-3">
                      <section.icon className="h-6 w-6 text-gray-600" />
                      {section.title}
                    </CardTitle>
                    <p className="text-gray-600 mt-1 text-sm lg:text-base">{section.description}</p>
                  </div>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
                    <Badge variant="outline">
                      {configsWithData}/{totalSectionConfigs} configuradas
                    </Badge>
                    <Badge 
                      className={
                        sectionProgress === 100 ? 'bg-green-100 text-green-800 border-green-200' :
                        sectionProgress >= 75 ? 'bg-blue-100 text-blue-800 border-blue-200' :
                        sectionProgress >= 50 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                        sectionProgress >= 25 ? 'bg-orange-100 text-orange-800 border-orange-200' :
                        'bg-red-100 text-red-800 border-red-200'
                      }
                    >
                      {sectionProgress}% completado
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {sectionConfigs.map((config) => {
                    const IconComponent = config.icon;
                    const hasData = config.count > 0;
                    
                    return (
                      <Button 
                        key={config.id}
                        className="h-auto p-4 flex flex-col items-center gap-2 relative"
                        variant="outline"
                        onClick={() => handleManageConfig(config)}
                      >
                        <IconComponent className="h-12 w-12" />
                        <span className="text-center">{config.name}</span>
                        {/* Indicador de estado en la esquina */}
                        {hasData && (
                          <div className="absolute -top-1 -right-1">
                            <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                              <span className="text-white text-xs font-bold">{config.count}</span>
                            </div>
                          </div>
                        )}
                      </Button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Información y Recomendaciones */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-blue-900 mb-2">Estado de tu Configuración Personal</h3>
              <p className="text-blue-800">
                <strong>Configuraciones del Doctor:</strong> Aquí puedes gestionar tus configuraciones personales 
                que afectan únicamente a tu práctica médica. Las configuraciones con datos aparecen en{' '}
                <span className="text-green-700 font-medium">verde</span>, las que necesitan configuración inicial en{' '}
                <span className="text-orange-700 font-medium">naranja</span>.
              </p>
              {overallStats.progress === 100 && (
                <div className="mt-3 p-3 bg-green-100 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">
                    🎉 ¡Excelente! Todas tus configuraciones personales están completas. 
                    Tu perfil médico está completamente configurado.
                  </p>
                </div>
              )}
              {overallStats.progress < 100 && (
                <div className="mt-3 p-3 bg-orange-100 border border-orange-200 rounded-lg">
                  <p className="text-orange-800">
                    📋 Tienes <strong>{overallStats.pending} configuraciones</strong> que necesitan atención. 
                    Haz clic en cada opción para personalizar tu configuración médica.
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}