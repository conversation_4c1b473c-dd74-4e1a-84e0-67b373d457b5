import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { companies } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar empresa
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = params;

    // Verificar que la empresa existe
    const existingCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.id, id))
      .limit(1);

    if (existingCompany.length === 0) {
      return NextResponse.json(
        { error: 'Empresa no encontrada' },
        { status: 404 }
      );
    }

    const currentStatus = existingCompany[0].isActive;
    const newStatus = !currentStatus;

    // Actualizar estado
    const result = await db
      .update(companies)
      .set({ 
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(companies.id, id))
      .returning();

    return NextResponse.json({
      success: true,
      message: `Empresa ${newStatus ? 'activada' : 'desactivada'} exitosamente`,
      data: result[0],
    });

  } catch (error) {
    console.error('Error toggling company status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}