'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Search, 
  Edit, 
  Eye, 
  Trash2, 
  MoreHorizontal,
  ChevronDown,
  Check,
  ChevronLeft,
  ChevronRight,
  AlertCircle,
  ToggleLeft,
  ArrowLeft,
  RefreshCw,
  Download,
  Activity
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface Symptom {
  id: string;
  name: string;
  category: string;
  subcategory?: string;
  icdCode?: string;
  isSymptom: boolean;
  description?: string;
  commonCauses: string[];
  severity: string;
  bodySystem?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FiltersData {
  categories: string[];
  bodySystems: string[];
}

const SEVERITY_OPTIONS = [
  { value: 'low', label: 'Baja', color: 'bg-green-100 text-green-800' },
  { value: 'medium', label: 'Media', color: 'bg-yellow-100 text-yellow-800' },
  { value: 'high', label: 'Alta', color: 'bg-red-100 text-red-800' },
];

const SYMPTOM_TYPE_OPTIONS = [
  { value: 'true', label: 'Síntoma' },
  { value: 'false', label: 'Signo' },
];

export default function SymptomsPage() {
  const router = useRouter();
  const [symptoms, setSymptoms] = useState<Symptom[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [bodySystemFilter, setBodySystemFilter] = useState('');
  const [severityFilter, setSeverityFilter] = useState('');
  const [typeFilter, setTypeFilter] = useState('');
  const [filtersData, setFiltersData] = useState<FiltersData>({ categories: [], bodySystems: [] });
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedSymptom, setSelectedSymptom] = useState<Symptom | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    subcategory: '',
    icdCode: '',
    isSymptom: true,
    description: '',
    commonCauses: [] as string[],
    severity: 'low',
    bodySystem: '',
    order: 0,
    isActive: true,
  });

  // Form helpers
  const [newCause, setNewCause] = useState('');
  const [categoryOpen, setCategoryOpen] = useState(false);
  const [bodySystemOpen, setBodySystemOpen] = useState(false);
  const [severityOpen, setSeverityOpen] = useState(false);
  const [typeOpen, setTypeOpen] = useState(false);

  // Filter helpers
  const [categoryFilterOpen, setCategoryFilterOpen] = useState(false);
  const [bodySystemFilterOpen, setBodySystemFilterOpen] = useState(false);
  const [severityFilterOpen, setSeverityFilterOpen] = useState(false);
  const [typeFilterOpen, setTypeFilterOpen] = useState(false);

  // Refresh and export handlers
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSymptoms();
    setRefreshing(false);
  };

  const handleExport = () => {
    toast.info('Funcionalidad de exportación en desarrollo');
  };

  const fetchSymptoms = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        search: searchTerm,
        status: statusFilter,
        ...(categoryFilter && { category: categoryFilter }),
        ...(bodySystemFilter && { bodySystem: bodySystemFilter }),
        ...(severityFilter && { severity: severityFilter }),
        ...(typeFilter && { isSymptom: typeFilter }),
      });

      const response = await fetch(`/api/catalogs/symptoms?${params}`);
      if (!response.ok) throw new Error('Error al cargar síntomas');

      const result = await response.json();
      setSymptoms(result.data || []);
      
      if (result.pagination) {
        setTotalPages(result.pagination.totalPages);
        setTotalItems(result.pagination.total);
      }

      if (result.filters) {
        setFiltersData(result.filters);
      }
    } catch (error) {
      console.error('Error fetching symptoms:', error);
      toast.error('Error al cargar los síntomas');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSymptoms();
  }, [currentPage, itemsPerPage, searchTerm, statusFilter, categoryFilter, bodySystemFilter, severityFilter, typeFilter]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      subcategory: '',
      icdCode: '',
      isSymptom: true,
      description: '',
      commonCauses: [],
      severity: 'low',
      bodySystem: '',
      order: 0,
      isActive: true,
    });
    setNewCause('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const method = selectedSymptom ? 'PUT' : 'POST';
      const url = '/api/catalogs/symptoms';
      
      const payload = selectedSymptom 
        ? { ...formData, id: selectedSymptom.id }
        : formData;

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error al guardar');
      }

      toast.success(selectedSymptom ? 'Síntoma actualizado exitosamente' : 'Síntoma creado exitosamente');
      
      setIsCreateModalOpen(false);
      setIsEditModalOpen(false);
      resetForm();
      setSelectedSymptom(null);
      fetchSymptoms();
    } catch (error: any) {
      toast.error(error.message || 'Error al guardar el síntoma');
    }
  };

  const handleEdit = (symptom: Symptom) => {
    setSelectedSymptom(symptom);
    setFormData({
      name: symptom.name,
      category: symptom.category,
      subcategory: symptom.subcategory || '',
      icdCode: symptom.icdCode || '',
      isSymptom: symptom.isSymptom,
      description: symptom.description || '',
      commonCauses: symptom.commonCauses || [],
      severity: symptom.severity,
      bodySystem: symptom.bodySystem || '',
      order: symptom.order,
      isActive: symptom.isActive,
    });
    setIsEditModalOpen(true);
  };

  const handleView = (symptom: Symptom) => {
    setSelectedSymptom(symptom);
    setIsViewModalOpen(true);
  };

  const handleToggleStatus = async (symptom: Symptom) => {
    try {
      const response = await fetch(`/api/catalogs/symptoms/${symptom.id}/toggle-status`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Error al cambiar estado');

      toast.success(`Síntoma ${symptom.isActive ? 'desactivado' : 'activado'} exitosamente`);
      fetchSymptoms();
    } catch (error) {
      toast.error('Error al cambiar el estado del síntoma');
    }
  };

  const handleDelete = async (symptom: Symptom) => {
    if (!confirm('¿Estás seguro de que deseas eliminar este síntoma?')) return;

    try {
      const response = await fetch(`/api/catalogs/symptoms/${symptom.id}?type=logical`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar');

      toast.success('Síntoma eliminado exitosamente');
      fetchSymptoms();
    } catch (error) {
      toast.error('Error al eliminar el síntoma');
    }
  };

  const addCause = () => {
    if (newCause.trim() && !formData.commonCauses.includes(newCause.trim())) {
      setFormData({
        ...formData,
        commonCauses: [...formData.commonCauses, newCause.trim()]
      });
      setNewCause('');
    }
  };

  const removeCause = (index: number) => {
    setFormData({
      ...formData,
      commonCauses: formData.commonCauses.filter((_, i) => i !== index)
    });
  };

  const getSeverityBadge = (severity: string) => {
    const option = SEVERITY_OPTIONS.find(opt => opt.value === severity);
    return option ? (
      <Badge className={option.color}>
        {option.label}
      </Badge>
    ) : severity;
  };

  const getTypeBadge = (isSymptom: boolean) => {
    return (
      <Badge variant={isSymptom ? "default" : "secondary"}>
        {isSymptom ? 'Síntoma' : 'Signo'}
      </Badge>
    );
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header con navegación y acciones principales */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            {/* Botón de regreso */}
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            {/* Título y descripción */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Gestión de Síntomas y Signos
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Administra síntomas y signos médicos del sistema
              </p>
            </div>
          </div>
        </div>
        
        {/* Botones de acción */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Síntoma
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Card de filtros de búsqueda */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Búsqueda */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar síntomas..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            {/* Estado */}
            <div>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="flex h-10 w-full rounded-md border-0 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>

            {/* Categoría */}
            <div>
              <Popover open={categoryFilterOpen} onOpenChange={setCategoryFilterOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={categoryFilterOpen}
                    className="w-full justify-between border-0"
                  >
                    {categoryFilter || "Categoría"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Buscar categoría..." />
                    <CommandEmpty>No se encontraron categorías.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        value=""
                        onSelect={() => {
                          setCategoryFilter('');
                          setCategoryFilterOpen(false);
                          setCurrentPage(1);
                        }}
                      >
                        Todas las categorías
                      </CommandItem>
                      {filtersData.categories.map((category) => (
                        <CommandItem
                          key={category}
                          value={category}
                          onSelect={() => {
                            setCategoryFilter(category);
                            setCategoryFilterOpen(false);
                            setCurrentPage(1);
                          }}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              categoryFilter === category ? "opacity-100" : "opacity-0"
                            }`}
                          />
                          {category}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Sistema Corporal */}
            <div>
              <Popover open={bodySystemFilterOpen} onOpenChange={setBodySystemFilterOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={bodySystemFilterOpen}
                    className="w-full justify-between border-0"
                  >
                    {bodySystemFilter || "Sistema"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Buscar sistema..." />
                    <CommandEmpty>No se encontraron sistemas.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        value=""
                        onSelect={() => {
                          setBodySystemFilter('');
                          setBodySystemFilterOpen(false);
                          setCurrentPage(1);
                        }}
                      >
                        Todos los sistemas
                      </CommandItem>
                      {filtersData.bodySystems.map((system) => (
                        <CommandItem
                          key={system}
                          value={system}
                          onSelect={() => {
                            setBodySystemFilter(system);
                            setBodySystemFilterOpen(false);
                            setCurrentPage(1);
                          }}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              bodySystemFilter === system ? "opacity-100" : "opacity-0"
                            }`}
                          />
                          {system}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>

            {/* Tipo */}
            <div>
              <Popover open={typeFilterOpen} onOpenChange={setTypeFilterOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={typeFilterOpen}
                    className="w-full justify-between border-0"
                  >
                    {typeFilter ? SYMPTOM_TYPE_OPTIONS.find(opt => opt.value === typeFilter)?.label : "Tipo"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandEmpty>No se encontraron tipos.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        value=""
                        onSelect={() => {
                          setTypeFilter('');
                          setTypeFilterOpen(false);
                          setCurrentPage(1);
                        }}
                      >
                        Todos los tipos
                      </CommandItem>
                      {SYMPTOM_TYPE_OPTIONS.map((option) => (
                        <CommandItem
                          key={option.value}
                          value={option.value}
                          onSelect={() => {
                            setTypeFilter(option.value);
                            setTypeFilterOpen(false);
                            setCurrentPage(1);
                          }}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              typeFilter === option.value ? "opacity-100" : "opacity-0"
                            }`}
                          />
                          {option.label}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card principal con grid de datos */}
      <div className="hidden md:block">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              Síntomas y Signos
            </CardTitle>
          </CardHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nombre</TableHead>
                <TableHead>Tipo</TableHead>
                <TableHead>Categoría</TableHead>
                <TableHead>Sistema</TableHead>
                <TableHead>Severidad</TableHead>
                <TableHead>Código CIE</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    Cargando síntomas...
                  </TableCell>
                </TableRow>
              ) : symptoms.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    No se encontraron síntomas
                  </TableCell>
                </TableRow>
              ) : (
                symptoms.map((symptom) => (
                  <TableRow key={symptom.id}>
                    <TableCell className="font-medium">{symptom.name}</TableCell>
                    <TableCell>{getTypeBadge(symptom.isSymptom)}</TableCell>
                    <TableCell>{symptom.category}</TableCell>
                    <TableCell>{symptom.bodySystem || '-'}</TableCell>
                    <TableCell>{getSeverityBadge(symptom.severity)}</TableCell>
                    <TableCell>{symptom.icdCode || '-'}</TableCell>
                    <TableCell>
                      <Badge variant={symptom.isActive ? "default" : "secondary"}>
                        {symptom.isActive ? "Activo" : "Inactivo"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleView(symptom)}>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver detalles
                          </DropdownMenuItem><DropdownMenuItem onClick={() => handleEdit(symptom)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(symptom)}
                            className={symptom.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            <ToggleLeft className="mr-2 h-4 w-4" />
                            {symptom.isActive ? 'Desactivar' : 'Activar'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(symptom)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Card>
      </div>

      {/* Cards para Mobile */}
      <div className="md:hidden space-y-4">
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              Síntomas y Signos
            </CardTitle>
          </CardHeader>
        </Card>
        {loading ? (
          <Card>
            <CardContent className="pt-6 text-center">
              Cargando síntomas...
            </CardContent>
          </Card>
        ) : symptoms.length === 0 ? (
          <Card>
            <CardContent className="pt-6 text-center">
              No se encontraron síntomas
            </CardContent>
          </Card>
        ) : (
          symptoms.map((symptom) => (
            <Card key={symptom.id}>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-2">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold">{symptom.name}</h3>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(symptom)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Ver detalles
                      </DropdownMenuItem><DropdownMenuItem onClick={() => handleEdit(symptom)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(symptom)}
                            className={symptom.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            <ToggleLeft className="mr-2 h-4 w-4" />
                            {symptom.isActive ? 'Desactivar' : 'Activar'}
                          </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDelete(symptom)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Eliminar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    {getTypeBadge(symptom.isSymptom)}
                    {getSeverityBadge(symptom.severity)}
                    <Badge variant={symptom.isActive ? "default" : "secondary"}>
                      {symptom.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    <p><strong>Categoría:</strong> {symptom.category}</p>
                    {symptom.bodySystem && <p><strong>Sistema:</strong> {symptom.bodySystem}</p>}
                    {symptom.icdCode && <p><strong>Código CIE:</strong> {symptom.icdCode}</p>}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Filas por página</p>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>

          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Página {currentPage} de {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage >= totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Crear/Editar */}
      <Dialog open={isCreateModalOpen || isEditModalOpen} onOpenChange={(open) => {
        if (!open) {
          setIsCreateModalOpen(false);
          setIsEditModalOpen(false);
          resetForm();
          setSelectedSymptom(null);
        }
      }}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedSymptom ? 'Editar Síntoma' : 'Agregar Síntoma'}
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Nombre */}
              <div className="md:col-span-2">
                <Label htmlFor="name">Nombre *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>

              {/* Tipo */}
              <div>
                <Label>Tipo *</Label>
                <Popover open={typeOpen} onOpenChange={setTypeOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={typeOpen}
                      className="w-full justify-between"
                    >
                      {SYMPTOM_TYPE_OPTIONS.find(opt => opt.value === formData.isSymptom.toString())?.label || "Seleccionar tipo"}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandEmpty>No se encontraron tipos.</CommandEmpty>
                      <CommandGroup>
                        {SYMPTOM_TYPE_OPTIONS.map((option) => (
                          <CommandItem
                            key={option.value}
                            value={option.value}
                            onSelect={() => {
                              setFormData({ ...formData, isSymptom: option.value === 'true' });
                              setTypeOpen(false);
                            }}
                          >
                            <Check
                              className={`mr-2 h-4 w-4 ${
                                formData.isSymptom.toString() === option.value ? "opacity-100" : "opacity-0"
                              }`}
                            />
                            {option.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Categoría */}
              <div>
                <Label htmlFor="category">Categoría *</Label>
                <Input
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  required
                />
              </div>

              {/* Subcategoría */}
              <div>
                <Label htmlFor="subcategory">Subcategoría</Label>
                <Input
                  id="subcategory"
                  value={formData.subcategory}
                  onChange={(e) => setFormData({ ...formData, subcategory: e.target.value })}
                />
              </div>

              {/* Código CIE */}
              <div>
                <Label htmlFor="icdCode">Código CIE-11</Label>
                <Input
                  id="icdCode"
                  value={formData.icdCode}
                  onChange={(e) => setFormData({ ...formData, icdCode: e.target.value })}
                  placeholder="ej: 8A80.Z"
                />
              </div>

              {/* Sistema Corporal */}
              <div>
                <Label htmlFor="bodySystem">Sistema Corporal</Label>
                <Input
                  id="bodySystem"
                  value={formData.bodySystem}
                  onChange={(e) => setFormData({ ...formData, bodySystem: e.target.value })}
                  placeholder="ej: Cardiovascular"
                />
              </div>

              {/* Severidad */}
              <div>
                <Label>Severidad</Label>
                <Popover open={severityOpen} onOpenChange={setSeverityOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      role="combobox"
                      aria-expanded={severityOpen}
                      className="w-full justify-between"
                    >
                      {SEVERITY_OPTIONS.find(opt => opt.value === formData.severity)?.label || "Seleccionar severidad"}
                      <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-0">
                    <Command>
                      <CommandEmpty>No se encontraron opciones.</CommandEmpty>
                      <CommandGroup>
                        {SEVERITY_OPTIONS.map((option) => (
                          <CommandItem
                            key={option.value}
                            value={option.value}
                            onSelect={() => {
                              setFormData({ ...formData, severity: option.value });
                              setSeverityOpen(false);
                            }}
                          >
                            <Check
                              className={`mr-2 h-4 w-4 ${
                                formData.severity === option.value ? "opacity-100" : "opacity-0"
                              }`}
                            />
                            {option.label}
                          </CommandItem>
                        ))}
                      </CommandGroup>
                    </Command>
                  </PopoverContent>
                </Popover>
              </div>

              {/* Orden */}
              <div>
                <Label htmlFor="order">Orden</Label>
                <Input
                  id="order"
                  type="number"
                  value={formData.order}
                  onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
                />
              </div>

              {/* Descripción */}
              <div className="md:col-span-2">
                <Label htmlFor="description">Descripción</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                />
              </div>

              {/* Causas Comunes */}
              <div className="md:col-span-2">
                <Label>Causas Comunes</Label>
                <div className="space-y-2">
                  <div className="flex gap-2">
                    <Input
                      value={newCause}
                      onChange={(e) => setNewCause(e.target.value)}
                      placeholder="Agregar causa común..."
                      onKeyPress={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          addCause();
                        }
                      }}
                    />
                    <Button type="button" onClick={addCause} variant="outline">
                      <Plus className="h-4 w-4" />
                    </Button>
                  </div>
                  <div className="flex flex-wrap gap-2">
                    {formData.commonCauses.map((cause, index) => (
                      <Badge key={index} variant="secondary" className="cursor-pointer" onClick={() => removeCause(index)}>
                        {cause} ×
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              {/* Estado */}
              <div className="md:col-span-2">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="isActive"
                    checked={formData.isActive}
                    onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                  />
                  <Label htmlFor="isActive">Activo</Label>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateModalOpen(false);
                  setIsEditModalOpen(false);
                  resetForm();
                  setSelectedSymptom(null);
                }}
              >
                Cancelar
              </Button>
              <Button type="submit">
                {selectedSymptom ? 'Actualizar' : 'Crear'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Modal Ver Detalles */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Detalles del Síntoma</DialogTitle>
          </DialogHeader>
          
          {selectedSymptom && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Nombre</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Activity className="h-4 w-4 text-blue-600" />
                    <p className="text-sm">{selectedSymptom.name}</p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Tipo</Label>
                  <div className="mt-1">{getTypeBadge(selectedSymptom.isSymptom)}</div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Categoría</Label>
                  <p className="text-sm">{selectedSymptom.category}</p>
                </div>

                {selectedSymptom.subcategory && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Subcategoría</Label>
                    <p className="text-sm">{selectedSymptom.subcategory}</p>
                  </div>
                )}

                {selectedSymptom.icdCode && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Código CIE-11</Label>
                    <p className="text-sm">{selectedSymptom.icdCode}</p>
                  </div>
                )}

                {selectedSymptom.bodySystem && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Sistema Corporal</Label>
                    <p className="text-sm">{selectedSymptom.bodySystem}</p>
                  </div>
                )}

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Severidad</Label>
                  <div className="mt-1">{getSeverityBadge(selectedSymptom.severity)}</div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Estado</Label>
                  <div className="mt-1">
                    <Badge variant={selectedSymptom.isActive ? "default" : "secondary"}>
                      {selectedSymptom.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                </div>

                {selectedSymptom.description && (
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-muted-foreground">Descripción</Label>
                    <p className="text-sm">{selectedSymptom.description}</p>
                  </div>
                )}

                {selectedSymptom.commonCauses && selectedSymptom.commonCauses.length > 0 && (
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-muted-foreground">Causas Comunes</Label>
                    <div className="flex flex-wrap gap-2 mt-1">
                      {selectedSymptom.commonCauses.map((cause, index) => (
                        <Badge key={index} variant="outline">{cause}</Badge>
                      ))}
                    </div>
                  </div>
                )}

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Creado</Label>
                  <p className="text-sm">{new Date(selectedSymptom.createdAt).toLocaleDateString()}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Actualizado</Label>
                  <p className="text-sm">{new Date(selectedSymptom.updatedAt).toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}