import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { associationCodes, user, userRoles } from '@/db/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and, gt } from 'drizzle-orm';
import { randomBytes } from 'crypto';

// Función para generar código de 6 dígitos
function generateAssociationCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Función para generar ID único
function generateId(): string {
  return randomBytes(16).toString('hex');
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar que el usuario es un paciente
    const patientRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (patientRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solo los pacientes pueden generar códigos de asociación' },
        { status: 403 }
      );
    }

    // Verificar si ya tiene un código activo (no expirado y no usado)
    const existingCode = await db
      .select()
      .from(associationCodes)
      .where(
        and(
          eq(associationCodes.patientId, userId),
          gt(associationCodes.expiresAt, new Date()) // No expirado
          // No verificamos usedBy porque un paciente podría querer múltiples guardians
        )
      )
      .limit(1);

    if (existingCode.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'Ya tienes un código activo',
        data: {
          code: existingCode[0].code,
          expiresAt: existingCode[0].expiresAt,
          createdAt: existingCode[0].createdAt
        }
      });
    }

    // Generar nuevo código único
    let code: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 10;

    do {
      code = generateAssociationCode();
      
      // Verificar que el código no exista o esté expirado
      const codeExists = await db
        .select()
        .from(associationCodes)
        .where(
          and(
            eq(associationCodes.code, code),
            gt(associationCodes.expiresAt, new Date()) // Solo códigos no expirados
          )
        )
        .limit(1);
      
      isUnique = codeExists.length === 0;
      attempts++;
    } while (!isUnique && attempts < maxAttempts);

    if (!isUnique) {
      return NextResponse.json(
        { success: false, message: 'Error generando código único. Intenta de nuevo.' },
        { status: 500 }
      );
    }

    // Crear código con expiración de 24 horas
    const expirationDate = new Date();
    expirationDate.setHours(expirationDate.getHours() + 24);

    const newCode = await db
      .insert(associationCodes)
      .values({
        id: generateId(),
        code: code!,
        patientId: userId,
        expiresAt: expirationDate,
        usedBy: null,
        usedAt: null,
        createdAt: new Date()
      })
      .returning({
        id: associationCodes.id,
        code: associationCodes.code,
        expiresAt: associationCodes.expiresAt,
        createdAt: associationCodes.createdAt
      });

    return NextResponse.json({
      success: true,
      message: 'Código de asociación generado exitosamente',
      data: {
        code: newCode[0].code,
        expiresAt: newCode[0].expiresAt,
        createdAt: newCode[0].createdAt,
        validFor: '24 horas'
      }
    });

  } catch (error) {
    console.error('Error generando código de asociación:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 