'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  User, 
  FileText, 
  History, 
  Stethoscope, 
  Pill, 
  BarChart3,
  ArrowLeft,
  Edit,
  Save,
  RefreshCw,
  Calendar,
  Shield,
  AlertCircle,
  Heart,
  Activity,
  Syringe,
  AlertTriangle,
  Plus,
  X,
  Search,
  Eye
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { cn, formatDate, formatDateTime } from '@/lib/utils';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface MedicalRecordDetail {
  record: any;
  patient: any;
  primaryDoctor: any;
  consultory: any;
  medicalHistory: any;
  recentConsultations: any[];
  documents: any[];
  stats: {
    totalConsultations: number;
    lastConsultationDate?: string;
    activeDiagnoses: number;
    activePrescriptions: number;
    pendingFollowUps: number;
  };
}

export default function ExpedienteDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const resolvedParams = use(params);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  
  // Estados principales
  const [expedienteData, setExpedienteData] = useState<MedicalRecordDetail | null>(null);
  const [loading, setLoading] = useState(true);
  const [isEditMode, setIsEditMode] = useState(false);
  const [activeTab, setActiveTab] = useState('general');
  const [processing, setProcessing] = useState(false);

  // Verificar si viene en modo edición
  useEffect(() => {
    const mode = searchParams.get('mode');
    if (mode === 'edit') {
      setIsEditMode(true);
    }
  }, [searchParams]);

  // Obtener datos del expediente
  const fetchExpedienteData = async () => {
    try {
      setLoading(true);
      
      const response = await fetch(`/api/medical-records/${resolvedParams.id}`);
      const data = await response.json();

      if (response.ok) {
        setExpedienteData(data.data);
      } else {
        toast.error('Error al cargar el expediente');
        router.push('/dashboard/doctor/expedientes');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
      router.push('/dashboard/doctor/expedientes');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (resolvedParams.id) {
      fetchExpedienteData();
    }
  }, [resolvedParams.id]);

  // Handlers
  const handleSave = async () => {
    setProcessing(true);
    // Aquí iría la lógica de guardado
    await new Promise(resolve => setTimeout(resolve, 1000)); // Simular guardado
    setProcessing(false);
    setIsEditMode(false);
    toast.success('Expediente actualizado exitosamente');
  };

  if (loading) {
    return (
      <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
        <div className="text-center py-12">
          <RefreshCw className="h-12 w-12 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-500">Cargando expediente...</p>
        </div>
      </div>
    );
  }

  if (!expedienteData) {
    return (
      <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
        <div className="text-center py-12">
          <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
          <p className="text-gray-500">Expediente no encontrado</p>
          <Button className="mt-4" onClick={() => router.push('/dashboard/doctor/expedientes')}>
            Volver a Expedientes
          </Button>
        </div>
      </div>
    );
  }

  const { record, patient, primaryDoctor, consultory, medicalHistory, recentConsultations, stats } = expedienteData;

  return (
    <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
      {/* Header responsivo estándar */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Expediente Clínico
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                {record.patientSummary?.fullName || 'Paciente'} • {record.recordNumber}
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          <Badge className={cn(
            "text-sm",
            record.status === 'active' ? 'bg-green-100 text-green-800 border-green-200' : 
            'bg-gray-100 text-gray-800 border-gray-200'
          )}>
            {record.status === 'active' ? 'Activo' : record.status}
          </Badge>
          {record.isMinor && (
            <Badge variant="outline" className="text-sm text-blue-600 border-blue-300">
              Menor de edad
            </Badge>
          )}
        </div>
      </div>

      {/* Sistema de tabs personalizado (NO shadcn/ui) */}
      <div className="space-y-4">
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('general')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'general'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <User className="h-4 w-4" />
                  <span>Información General</span>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('history')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'history'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <History className="h-4 w-4" />
                  <span>Antecedentes</span>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('consultations')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'consultations'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <Stethoscope className="h-4 w-4" />
                  <span>Consultas</span>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('prescriptions')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'prescriptions'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <Pill className="h-4 w-4" />
                  <span>Prescripciones</span>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('documents')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'documents'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <FileText className="h-4 w-4" />
                  <span>Documentos</span>
                </div>
              </div>
            </button>
            
            <button
              onClick={() => setActiveTab('reports')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'reports'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="flex items-center justify-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>Reportes</span>
                </div>
              </div>
            </button>
          </div>
        </div>
        
        {/* Contenido de los tabs con organización vertical */}
        {activeTab === 'general' && (
          <GeneralInfoTab 
            record={record}
            patient={patient}
            primaryDoctor={primaryDoctor}
            consultory={consultory}
            stats={stats}
            isEditMode={isEditMode}
          />
        )}
        {activeTab === 'history' && (
          <MedicalHistoryTab 
            medicalHistory={medicalHistory}
            isEditMode={isEditMode}
            recordId={record.id}
            onSave={handleSave}
          />
        )}
        {activeTab === 'consultations' && (
          <ConsultationsTab 
            consultations={recentConsultations}
            recordId={record.id}
          />
        )}
        {activeTab === 'prescriptions' && (
          <PrescriptionsTab 
            consultations={recentConsultations}
          />
        )}
        {activeTab === 'documents' && (
          <DocumentsTab 
            recordId={record.id}
          />
        )}
        {activeTab === 'reports' && (
          <ReportsTab 
            recordId={record.id}
            stats={stats}
          />
        )}
      </div>
      
      {/* Botones de acción en parte inferior derecha */}
      {isEditMode ? (
        <div className="flex justify-end gap-3 pt-4">
          <Button variant="outline" onClick={() => setIsEditMode(false)}>
            Cancelar
          </Button>
          <Button 
            onClick={handleSave} 
            className="bg-emerald-600 hover:bg-emerald-700 text-white"
            disabled={processing}
          >
            <Save className="h-4 w-4 mr-2" />
            {processing ? 'Guardando...' : 'Guardar Cambios'}
          </Button>
        </div>
      ) : (
        <div className="flex justify-end gap-3 pt-4">
          <Button 
            onClick={() => setIsEditMode(true)}
            className="bg-emerald-600 hover:bg-emerald-700 text-white"
          >
            <Edit className="h-4 w-4 mr-2" />
            Editar Expediente
          </Button>
        </div>
      )}
    </div>
  );
}

// Componente para Tab de Información General
function GeneralInfoTab({ record, patient, primaryDoctor, consultory, stats, isEditMode }: any) {
  return (
    <div className="space-y-4">
      {/* Información del paciente */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <User className="h-5 w-5 text-blue-600" />
            Información del Paciente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div>
              <Label className="text-sm font-medium text-gray-700">Nombre Completo</Label>
              <p className="text-sm text-gray-900">{record.patientSummary?.fullName || 'No registrado'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Edad</Label>
              <p className="text-sm text-gray-900">{record.patientSummary?.age || 'No registrado'} años</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Sexo</Label>
              <p className="text-sm text-gray-900">{record.patientSummary?.gender || 'No registrado'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Tipo de Sangre</Label>
              <p className="text-sm text-gray-900">{record.patientSummary?.bloodType || 'No registrado'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Email</Label>
              <p className="text-sm text-gray-900">{patient?.email || 'No registrado'}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
              <p className="text-sm text-gray-900">{patient?.phone || 'No registrado'}</p>
            </div>
          </div>
          
          {record.patientSummary?.allergies && record.patientSummary.allergies.length > 0 && (
            <div className="mt-4">
              <Label className="text-sm font-medium text-gray-700">Alergias Conocidas</Label>
              <div className="flex flex-wrap gap-1 mt-1">
                {record.patientSummary.allergies.map((allergy: string, index: number) => (
                  <Badge key={index} variant="outline" className="text-red-600 border-red-300">
                    {allergy}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Estado del expediente */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Estado del Expediente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div>
              <Label className="text-sm font-medium text-gray-700">Número de Expediente</Label>
              <p className="text-sm text-gray-900 font-mono">{record.recordNumber}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Fecha de Apertura</Label>
              <p className="text-sm text-gray-900">
                {formatDate(record.openDate)}
              </p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Estado</Label>
              <Badge className={cn(
                record.status === 'active' ? 'bg-green-100 text-green-800 border-green-200' : 
                'bg-gray-100 text-gray-800 border-gray-200'
              )}>
                {record.status === 'active' ? 'Activo' : record.status}
              </Badge>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Total Consultas</Label>
              <p className="text-sm text-gray-900">{stats.totalConsultations}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Médico primario y consultorio */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Stethoscope className="h-5 w-5 text-blue-600" />
            Equipo Médico
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <Label className="text-sm font-medium text-gray-700">Médico Primario</Label>
              <p className="text-sm text-gray-900">
                Dr. {primaryDoctor?.firstName} {primaryDoctor?.lastName}
              </p>
              <p className="text-xs text-gray-500">{primaryDoctor?.email}</p>
            </div>
            <div>
              <Label className="text-sm font-medium text-gray-700">Consultorio</Label>
              <p className="text-sm text-gray-900">{consultory?.name}</p>
              <p className="text-xs text-gray-500">{consultory?.type}</p>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Información del encargado (si es menor) */}
      {record.isMinor && record.guardianInfo && (
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Shield className="h-5 w-5 text-blue-600" />
              Información del Encargado
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <Label className="text-sm font-medium text-gray-700">Nombre del Encargado</Label>
                <p className="text-sm text-gray-900">{record.guardianInfo.name}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">Parentesco</Label>
                <p className="text-sm text-gray-900">{record.guardianInfo.relationship}</p>
              </div>
              <div>
                <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                <p className="text-sm text-gray-900">{record.guardianInfo.phone}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Tab de Antecedentes Médicos
function MedicalHistoryTab({ medicalHistory, isEditMode, recordId, onSave }: any) {
  const [isAddPathologicalOpen, setIsAddPathologicalOpen] = useState(false);
  const [isAddNonPathologicalOpen, setIsAddNonPathologicalOpen] = useState(false);
  const [isAddAllergyOpen, setIsAddAllergyOpen] = useState(false);
  const [isAddVaccinationOpen, setIsAddVaccinationOpen] = useState(false);
  const [history, setHistory] = useState(medicalHistory || {
    pathologicalHistory: [],
    nonPathologicalHistory: [],
    allergies: [],
    vaccinations: [],
    hospitalizations: [],
    surgeries: [],
    familyHistory: []
  });

  // Actualizar el estado cuando cambien los datos de medicalHistory (solo una vez al montar)
  useEffect(() => {
    if (medicalHistory && Object.keys(medicalHistory).length > 0) {
      setHistory(medicalHistory);
    }
  }, [medicalHistory?.id]); // Solo cambiar cuando el ID del historial cambie, no el contenido

  const removePathological = (index: number) => {
    const updated = { ...history };
    updated.pathologicalHistory.splice(index, 1);
    setHistory(updated);
  };

  const removeNonPathological = (index: number) => {
    const updated = { ...history };
    updated.nonPathologicalHistory.splice(index, 1);
    setHistory(updated);
  };

  const removeAllergy = (index: number) => {
    const updated = { ...history };
    updated.allergies.splice(index, 1);
    setHistory(updated);
  };

  const removeVaccination = (index: number) => {
    const updated = { ...history };
    updated.vaccinations.splice(index, 1);
    setHistory(updated);
  };

  const saveHistory = async () => {
    try {
      const response = await fetch(`/api/medical-records/${recordId}/history`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(history),
      });

      if (response.ok) {
        toast.success('Antecedentes médicos guardados exitosamente');
        if (onSave) {
          onSave();
        }
      } else {
        const error = await response.json();
        toast.error(error.message || 'Error al guardar antecedentes médicos');
      }
    } catch (error) {
      console.error('Error saving medical history:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  // Auto-save removido para evitar bucles infinitos

  return (
    <div className="space-y-4">
      {/* Antecedentes Patológicos */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            <span className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Antecedentes Patológicos
            </span>
            {isEditMode && (
              <Button size="sm" onClick={() => setIsAddPathologicalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {history.pathologicalHistory?.length > 0 ? (
            <div className="space-y-3">
              {history.pathologicalHistory.map((item: any, index: number) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{item.historyName || item.name}</div>
                      <div className="text-sm text-gray-600 mb-2">{item.category}</div>
                      <div className="flex items-center gap-2 mb-2">
                        <Badge variant={
                          item.severity === 'severe' ? 'destructive' :
                          item.severity === 'moderate' ? 'secondary' : 'default'
                        }>
                          {item.severity === 'mild' ? 'Leve' :
                           item.severity === 'moderate' ? 'Moderado' : 'Severo'}
                        </Badge>
                        <Badge variant={
                          item.status === 'active' ? 'destructive' :
                          item.status === 'chronic' ? 'secondary' : 'default'
                        }>
                          {item.status === 'active' ? 'Activo' :
                           item.status === 'chronic' ? 'Crónico' : 'Resuelto'}
                        </Badge>
                      </div>
                      {item.diagnosedDate && (
                        <div className="text-xs text-gray-500">
                          Diagnosticado: {formatDate(item.diagnosedDate)}
                        </div>
                      )}
                      {item.notes && (
                        <div className="text-sm text-gray-700 mt-2">{item.notes}</div>
                      )}
                    </div>
                    {isEditMode && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => removePathological(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No se han registrado antecedentes patológicos</p>
              {isEditMode && (
                <Button className="mt-4" onClick={() => setIsAddPathologicalOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Agregar Primer Antecedente
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Antecedentes No Patológicos */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-green-600" />
              Antecedentes No Patológicos
            </span>
            {isEditMode && (
              <Button size="sm" onClick={() => setIsAddNonPathologicalOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {history.nonPathologicalHistory?.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {history.nonPathologicalHistory.map((item: any, index: number) => (
                <div key={index} className="p-3 border rounded-lg">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 flex items-center gap-2">
                        {item.historyName || item.name}
                        <Badge variant={item.isPositive ? 'default' : 'secondary'}>
                          {item.isPositive ? 'Positivo' : 'Factor de Riesgo'}
                        </Badge>
                      </div>
                      <div className="text-sm text-gray-600">{item.category}</div>
                      <div className="text-sm text-gray-700 mt-1">{item.value}</div>
                      {item.notes && (
                        <div className="text-xs text-gray-500 mt-2">{item.notes}</div>
                      )}
                    </div>
                    {isEditMode && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => removeNonPathological(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Heart className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No se han registrado antecedentes no patológicos</p>
              {isEditMode && (
                <Button className="mt-4" onClick={() => setIsAddNonPathologicalOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Agregar Primer Antecedente
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Alergias */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            <span className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-orange-600" />
              Alergias
            </span>
            {isEditMode && (
              <Button size="sm" onClick={() => setIsAddAllergyOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {history.allergies?.length > 0 ? (
            <div className="space-y-3">
              {history.allergies.map((allergy: any, index: number) => (
                <div key={index} className="p-3 border rounded-lg bg-red-50">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="font-medium text-red-900 flex items-center gap-2">
                        {allergy.allergen}
                        <Badge variant="destructive">
                          {allergy.severity === 'anaphylaxis' ? 'Anafilaxia' :
                           allergy.severity === 'severe' ? 'Severa' :
                           allergy.severity === 'moderate' ? 'Moderada' : 'Leve'}
                        </Badge>
                      </div>
                      <div className="text-sm text-red-700">
                        Tipo: {allergy.type === 'medication' ? 'Medicamento' :
                              allergy.type === 'food' ? 'Alimento' :
                              allergy.type === 'environmental' ? 'Ambiental' : 'Otro'}
                      </div>
                      <div className="text-sm text-red-700">Reacción: {allergy.reaction}</div>
                      {allergy.notes && (
                        <div className="text-sm text-red-600 mt-2">{allergy.notes}</div>
                      )}
                    </div>
                    {isEditMode && (
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        onClick={() => removeAllergy(index)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No se han registrado alergias</p>
              {isEditMode && (
                <Button className="mt-4" onClick={() => setIsAddAllergyOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Agregar Primera Alergia
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Vacunación */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Syringe className="h-5 w-5 text-blue-600" />
              Historial de Vacunación
            </span>
            {isEditMode && (
              <Button size="sm" onClick={() => setIsAddVaccinationOpen(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Agregar
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {history.vaccinations?.length > 0 ? (
            <div className="overflow-x-auto">
              <table className="w-full text-sm">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">Vacuna</th>
                    <th className="text-left p-2">Fecha</th>
                    <th className="text-left p-2">Dosis</th>
                    <th className="text-left p-2">Lote</th>
                    <th className="text-left p-2">Próxima Dosis</th>
                    {isEditMode && <th className="text-left p-2">Acciones</th>}
                  </tr>
                </thead>
                <tbody>
                  {history.vaccinations.map((vaccination: any, index: number) => (
                    <tr key={index} className="border-b">
                      <td className="p-2 font-medium">{vaccination.vaccine}</td>
                      <td className="p-2">{formatDate(vaccination.date)}</td>
                      <td className="p-2">{vaccination.doseNumber}</td>
                      <td className="p-2 font-mono text-xs">{vaccination.lot}</td>
                      <td className="p-2">
                        {vaccination.nextDueDate ? 
                          formatDate(vaccination.nextDueDate) : 
                          'Esquema completo'
                        }
                      </td>
                      {isEditMode && (
                        <td className="p-2">
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            onClick={() => removeVaccination(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </td>
                      )}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              <Syringe className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p>No se han registrado vacunas</p>
              {isEditMode && (
                <Button className="mt-4" onClick={() => setIsAddVaccinationOpen(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Agregar Primera Vacuna
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modales para agregar antecedentes */}
      <AddPathologicalModal 
        isOpen={isAddPathologicalOpen}
        onClose={() => setIsAddPathologicalOpen(false)}
        onAdd={(item) => {
          setHistory({
            ...history,
            pathologicalHistory: [...(history.pathologicalHistory || []), item]
          });
          setIsAddPathologicalOpen(false);
        }}
      />

      <AddNonPathologicalModal 
        isOpen={isAddNonPathologicalOpen}
        onClose={() => setIsAddNonPathologicalOpen(false)}
        onAdd={(item) => {
          setHistory({
            ...history,
            nonPathologicalHistory: [...(history.nonPathologicalHistory || []), item]
          });
          setIsAddNonPathologicalOpen(false);
        }}
      />

      <AddAllergyModal 
        isOpen={isAddAllergyOpen}
        onClose={() => setIsAddAllergyOpen(false)}
        onAdd={(item) => {
          setHistory({
            ...history,
            allergies: [...(history.allergies || []), item]
          });
          setIsAddAllergyOpen(false);
        }}
      />

      <AddVaccinationModal 
        isOpen={isAddVaccinationOpen}
        onClose={() => setIsAddVaccinationOpen(false)}
        onAdd={(item) => {
          setHistory({
            ...history,
            vaccinations: [...(history.vaccinations || []), item]
          });
          setIsAddVaccinationOpen(false);
        }}
      />
    </div>
  );
}

function ConsultationsTab({ consultations, recordId }: any) {
  const router = useRouter();
  const [isAddConsultationOpen, setIsAddConsultationOpen] = useState(false);
  const [consultationsList, setConsultationsList] = useState(consultations || []);
  const [loading, setLoading] = useState(false);

  const fetchConsultations = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/medical-records/${recordId}/consultations`);
      const data = await response.json();
      
      if (response.ok) {
        setConsultationsList(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching consultations:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (recordId) {
      fetchConsultations();
    }
  }, [recordId]);

  const handleAddConsultation = (newConsultation: any) => {
    setConsultationsList([newConsultation, ...consultationsList]);
    setIsAddConsultationOpen(false);
    toast.success('Consulta médica registrada exitosamente');
  };

  return (
    <div className="space-y-4">
      {/* Header con botón agregar */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Stethoscope className="h-5 w-5 text-blue-600" />
              Consultas Médicas
            </CardTitle>
            <Button disabled onClick={() => toast.info('Funcionalidad en desarrollo')}>
              <Plus className="h-4 w-4 mr-2" />
              Nueva Consulta
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-gray-600">
            Total de consultas registradas: {consultationsList.length}
          </div>
        </CardContent>
      </Card>

      {/* Lista de consultas */}
      {loading ? (
        <div className="text-center py-8">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-500">Cargando consultas...</p>
        </div>
      ) : consultationsList.length > 0 ? (
        <div className="space-y-4">
          {consultationsList.map((consultation: any, index: number) => (
            <Card key={consultation.id || index} className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <CardTitle className="text-base">
                        {consultation.services && consultation.services.length > 0 
                          ? `${consultation.services[0].serviceName || 'Consulta'}${consultation.services.length > 1 ? ` (+${consultation.services.length - 1})` : ''}`
                          : 'Consulta'}
                      </CardTitle>
                      <Badge variant={
                        consultation.status === 'completed' ? 'default' :
                        consultation.status === 'draft' ? 'secondary' :
                        consultation.status === 'reviewed' ? 'outline' : 'destructive'
                      }>
                        {consultation.status === 'completed' ? 'Completada' :
                         consultation.status === 'draft' ? 'Borrador' :
                         consultation.status === 'reviewed' ? 'Revisada' : 'Pendiente'}
                      </Badge>
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatDateTime(consultation.consultationDate || consultation.createdAt)}
                    </div>
                  </div>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => router.push(`/dashboard/doctor/consultations/${consultation.id}`)}
                    title="Ver consulta completa"
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {/* Motivo de consulta */}
                  <div>
                    <Label className="text-sm font-medium text-gray-700">Motivo de Consulta</Label>
                    <p className="text-sm text-gray-900">{consultation.chiefComplaint || 'No especificado'}</p>
                  </div>

                  {/* Diagnósticos */}
                  {consultation.diagnoses && consultation.diagnoses.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Diagnósticos</Label>
                      <div className="flex flex-wrap gap-1 mt-1">
                        {consultation.diagnoses.map((diagnosis: any, idx: number) => (
                          <Badge key={idx} variant="outline" className="text-xs">
                            {diagnosis.description}
                          </Badge>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Signos vitales si existen */}
                  {consultation.vitalSigns && (
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Signos Vitales</Label>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2 mt-1 text-xs">
                        {consultation.vitalSigns.weight && (
                          <span>Peso: {consultation.vitalSigns.weight}kg</span>
                        )}
                        {consultation.vitalSigns.height && (
                          <span>Talla: {consultation.vitalSigns.height}cm</span>
                        )}
                        {consultation.vitalSigns.temperature && (
                          <span>Temp: {consultation.vitalSigns.temperature}°C</span>
                        )}
                        {consultation.vitalSigns.heartRate && (
                          <span>FC: {consultation.vitalSigns.heartRate}bpm</span>
                        )}
                      </div>
                    </div>
                  )}

                  {/* Prescripciones */}
                  {consultation.prescriptions && consultation.prescriptions.length > 0 && (
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Prescripciones</Label>
                      <div className="text-xs text-gray-600 mt-1">
                        {consultation.prescriptions.length} medicamento{consultation.prescriptions.length > 1 ? 's' : ''} prescrito{consultation.prescriptions.length > 1 ? 's' : ''}
                      </div>
                    </div>
                  )}

                  {/* Próxima cita */}
                  {consultation.nextAppointment && (
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Próxima Cita</Label>
                      <div className="text-sm text-blue-600">
                        {formatDate(consultation.nextAppointment)}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Stethoscope className="h-12 w-12 mx-auto mb-4 text-gray-300" />
          <p className="text-gray-500 mb-4">No se han registrado consultas médicas</p>
          <Button disabled onClick={() => toast.info('Funcionalidad en desarrollo')}>
            <Plus className="h-4 w-4 mr-2" />
            Registrar Primera Consulta
          </Button>
        </div>
      )}

      {/* Modal de nueva consulta - TODO: Implementar AddConsultationModal */}
      {/* <AddConsultationModal
        isOpen={isAddConsultationOpen}
        onClose={() => setIsAddConsultationOpen(false)}
        onAdd={handleAddConsultation}
        recordId={recordId}
      /> */}
    </div>
  );
}

function PrescriptionsTab({ consultations }: any) {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedPrescription, setSelectedPrescription] = useState<any>(null);
  const [showPrescriptionModal, setShowPrescriptionModal] = useState(false);

  // Debug logs removidos para limpiar consola

  // Extraer todas las prescripciones de todas las consultas (ahora viene enriquecido desde la API)
  const allPrescriptions = consultations.flatMap((consultation: any) => 
    (consultation.prescriptions || []).map((prescription: any) => {
      return {
        ...prescription,
        consultationId: consultation.id,
        consultationDate: consultation.consultationDate,
        services: consultation.services,
        doctorName: consultation.doctor?.firstName && consultation.doctor?.lastName 
          ? `Dr. ${consultation.doctor.firstName} ${consultation.doctor.lastName}`
          : 'Dr. No especificado',
        // medicationDisplayName ya viene enriquecido desde la API
        id: prescription.id || `${consultation.id}-${prescription.medicationDisplayName}`
      };
    })
  );

  // Debug logs removidos para limpiar consola

  // Filtrar prescripciones
  const filteredPrescriptions = allPrescriptions.filter((prescription: any) => {
    const medicationName = prescription.medicationDisplayName || '';
    const dosage = prescription.dosage || '';
    
    const matchesSearch = searchTerm === '' || 
                         medicationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         dosage.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || prescription.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // Agrupar por consulta
  const prescriptionsByConsultation = filteredPrescriptions.reduce((acc: any, prescription: any) => {
    const consultationId = prescription.consultationId;
    if (!acc[consultationId]) {
      acc[consultationId] = {
        consultation: consultations.find((c: any) => c.id === consultationId),
        prescriptions: []
      };
    }
    acc[consultationId].prescriptions.push(prescription);
    return acc;
  }, {});

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      active: { label: 'Activa', variant: 'default' as const, className: 'bg-green-100 text-green-800 border-green-200' },
      completed: { label: 'Completada', variant: 'secondary' as const, className: 'bg-blue-100 text-blue-800 border-blue-200' },
      suspended: { label: 'Suspendida', variant: 'destructive' as const, className: 'bg-red-100 text-red-800 border-red-200' },
      expired: { label: 'Vencida', variant: 'outline' as const, className: 'bg-gray-100 text-gray-800 border-gray-200' }
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.active;
    return <Badge variant={config.variant} className={config.className}>{config.label}</Badge>;
  };

  if (allPrescriptions.length === 0) {
    return (
      <div className="text-center py-12">
        <Pill className="h-12 w-12 mx-auto mb-4 text-gray-300" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No hay prescripciones registradas</h3>
        <p className="text-gray-500">Las prescripciones aparecerán aquí cuando se registren en las consultas médicas.</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con filtros */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">Historial de Prescripciones</h3>
          <p className="text-sm text-gray-600">
            {allPrescriptions.length} prescripción{allPrescriptions.length !== 1 ? 'es' : ''} registrada{allPrescriptions.length !== 1 ? 's' : ''}
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
          {/* Búsqueda */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Buscar por medicamento..."
              className="pl-10 w-full sm:w-64 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>

          {/* Filtro por estado */}
          <select
            className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
          >
            <option value="all">Todos los estados</option>
            <option value="active">Activos</option>
            <option value="completed">Completados</option>
            <option value="suspended">Suspendidos</option>
          </select>
        </div>
      </div>

      {/* Lista de prescripciones */}
      <div className="space-y-4">
        {filteredPrescriptions.map((prescription: any, index: number) => (
          <Card key={`${prescription.consultationId}-${index}`} className="border border-gray-200">
            <CardContent className="p-4">
              <div className="flex flex-col sm:flex-row sm:items-center justify-between mb-3">
                <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                    <Pill className="h-5 w-5 text-blue-600" />
                  </div>
                  <div>
                    <h4 className="font-semibold text-gray-900">{prescription.medicationDisplayName}</h4>
                    <p className="text-sm text-gray-600">
                      {prescription.dosage} - {prescription.frequency}
                    </p>
                  </div>
                </div>
                <div className="mt-2 sm:mt-0">
                  <Badge variant={
                    prescription.status === 'active' ? 'default' : 
                    prescription.status === 'completed' ? 'secondary' :
                    prescription.status === 'suspended' ? 'destructive' : 'outline'
                  }>
                    {prescription.status === 'active' ? 'Activo' : 
                     prescription.status === 'completed' ? 'Completado' : 
                     prescription.status === 'suspended' ? 'Suspendido' :
                     prescription.status === 'paused' ? 'Pausado' :
                     prescription.status === 'discontinued' ? 'Descontinuado' :
                     prescription.status || 'Estado no definido'}
                  </Badge>
                </div>
              </div>
              
              <div className="space-y-2 text-sm text-gray-600">
                <p><span className="font-medium">Duración:</span> {prescription.duration}</p>
                {prescription.instructions && (
                  <p><span className="font-medium">Instrucciones:</span> {prescription.instructions}</p>
                )}
                <p><span className="font-medium">Prescrito:</span> {format(new Date(prescription.consultationDate), 'dd/MM/yyyy', { locale: es })}</p>
              </div>

              <div className="mt-3 pt-3 border-t border-gray-100">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSelectedPrescription(prescription);
                    setShowPrescriptionModal(true);
                  }}
                >
                  <Eye className="h-4 w-4 mr-2" />
                  Ver detalles
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Modal de detalles de prescripción sin overlay */}
      {showPrescriptionModal && selectedPrescription && (
        <div className="fixed inset-0 flex items-center justify-center p-4 z-50 pointer-events-none">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl border pointer-events-auto">
            <div className="p-6 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-900">Detalles de Prescripción</h3>
                <Button variant="ghost" size="sm" onClick={() => setShowPrescriptionModal(false)}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </div>
            <div className="p-6 space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium text-gray-700">Medicamento</Label>
                <p className="text-sm text-gray-900 font-medium">
                  {selectedPrescription.medicationDisplayName}
                </p>
                {selectedPrescription.medicationInfo && (
                  <p className="text-xs text-gray-500 mt-1">
                    {selectedPrescription.medicationInfo.genericName} • {selectedPrescription.medicationInfo.brandName}
                  </p>
                )}
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Estado</Label>
                <Badge variant={
                  selectedPrescription.status === 'active' ? 'default' : 
                  selectedPrescription.status === 'completed' ? 'secondary' :
                  selectedPrescription.status === 'suspended' ? 'destructive' : 'outline'
                }>
                  {selectedPrescription.status === 'active' ? 'Activo' : 
                   selectedPrescription.status === 'completed' ? 'Completado' : 
                   selectedPrescription.status === 'suspended' ? 'Suspendido' :
                   selectedPrescription.status === 'paused' ? 'Pausado' :
                   selectedPrescription.status === 'discontinued' ? 'Descontinuado' :
                   selectedPrescription.status || 'Estado no definido'}
                </Badge>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Dosis</Label>
                <p className="text-sm text-gray-900">{selectedPrescription.dose || 'No especificada'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Frecuencia</Label>
                <p className="text-sm text-gray-900">{selectedPrescription.frequency || 'No especificada'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Duración</Label>
                <p className="text-sm text-gray-900">{selectedPrescription.duration || 'No especificada'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Vía de Administración</Label>
                <p className="text-sm text-gray-900">{selectedPrescription.route || 'Oral'}</p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Fecha de Prescripción</Label>
                <p className="text-sm text-gray-900">
                  {format(new Date(selectedPrescription.consultationDate), 'dd/MM/yyyy', { locale: es })}
                </p>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Médico</Label>
                <p className="text-sm text-gray-900">{selectedPrescription.doctorName}</p>
              </div>
            </div>

            {selectedPrescription.instructions && (
              <div className="mt-4">
                <Label className="text-sm font-medium text-gray-700">Instrucciones Especiales</Label>
                <p className="text-sm text-gray-900 mt-1 p-2 bg-gray-50 rounded border">
                  {selectedPrescription.instructions}
                </p>
              </div>
            )}

            {selectedPrescription.services && selectedPrescription.services.length > 0 && (
              <div className="mt-4">
                <Label className="text-sm font-medium text-gray-700">Consulta Relacionada</Label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {selectedPrescription.services.map((service: any, idx: number) => (
                    <Badge key={idx} variant="outline" className="text-xs">
                      {service.name || service.serviceName || 'Servicio'}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            </div>

            <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
              <Button variant="outline" onClick={() => setShowPrescriptionModal(false)}>
                Cerrar
              </Button>
            </div>
          </div>
        </div>
      )}

    </div>
  );
}

function AddPathologicalModal({ isOpen, onClose, onAdd }: {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [catalogItems, setCatalogItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    diagnosisDate: '',
    severity: 'leve',
    treatment: '',
    status: 'controlado',
    notes: ''
  });

  const searchCatalog = async (term: string) => {
    if (!term.trim()) {
      setCatalogItems([]);
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/catalogs/pathological-history?search=${encodeURIComponent(term)}&limit=10`);
      if (response.ok) {
        const data = await response.json();
        setCatalogItems(data.data || []);
      }
    } catch (error) {
      console.error('Error searching catalog:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    searchCatalog(term);
  };

  const handleAdd = () => {
    if (!selectedItem) return;
    
    const newItem = {
      id: selectedItem.id,
      name: selectedItem.name,
      diagnosisDate: formData.diagnosisDate,
      severity: formData.severity,
      treatment: formData.treatment,
      status: formData.status,
      notes: formData.notes,
      type: 'pathological'
    };
    
    onAdd(newItem);
    onClose();
    
    // Reset form
    setSearchTerm('');
    setSelectedItem(null);
    setCatalogItems([]);
    setFormData({
      diagnosisDate: '',
      severity: 'leve',
      treatment: '',
      status: 'controlado',
      notes: ''
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50 pointer-events-none">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl border pointer-events-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Agregar Antecedente Patológico</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          {/* Búsqueda de catálogo */}
          <div>
            <Label>Buscar antecedente patológico</Label>
            <div className="relative mt-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Buscar por nombre del antecedente..."
                value={searchTerm}
                onChange={(e) => handleSearch(e.target.value)}
              />
            </div>
          </div>

          {/* Resultados de búsqueda */}
          {catalogItems.length > 0 && (
            <div>
              <Label>Seleccionar antecedente</Label>
              <div className="mt-1 max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                {catalogItems.map((item) => (
                  <div
                    key={item.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${
                      selectedItem?.id === item.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                    onClick={() => setSelectedItem(item)}
                  >
                    <div className="font-medium text-gray-900">{item.name}</div>
                    {item.description && (
                      <div className="text-sm text-gray-600">{item.description}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedItem && (
            <>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-900">Antecedente seleccionado:</p>
                <p className="text-blue-800">{selectedItem.name}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Fecha de diagnóstico</Label>
                  <input
                    type="date"
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    value={formData.diagnosisDate}
                    onChange={(e) => setFormData({...formData, diagnosisDate: e.target.value})}
                  />
                </div>

                <div>
                  <Label>Severidad</Label>
                  <select
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    value={formData.severity}
                    onChange={(e) => setFormData({...formData, severity: e.target.value})}
                  >
                    <option value="leve">Leve</option>
                    <option value="moderado">Moderado</option>
                    <option value="severo">Severo</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Tratamiento</Label>
                  <input
                    type="text"
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Tratamiento recibido"
                    value={formData.treatment}
                    onChange={(e) => setFormData({...formData, treatment: e.target.value})}
                  />
                </div>

                <div>
                  <Label>Estado actual</Label>
                  <select
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    value={formData.status}
                    onChange={(e) => setFormData({...formData, status: e.target.value})}
                  >
                    <option value="controlado">Controlado</option>
                    <option value="en_tratamiento">En tratamiento</option>
                    <option value="curado">Curado</option>
                    <option value="cronico">Crónico</option>
                  </select>
                </div>
              </div>

              <div>
                <Label>Notas adicionales</Label>
                <textarea
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md resize-none"
                  rows={3}
                  placeholder="Observaciones, complicaciones, etc..."
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />
              </div>
            </>
          )}
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleAdd} disabled={!selectedItem}>
            Agregar Antecedente
          </Button>
        </div>
      </div>
    </div>
  );
}

function AddNonPathologicalModal({ isOpen, onClose, onAdd }: {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
}) {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const [catalogItems, setCatalogItems] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    value: '',
    isPositive: true,
    notes: ''
  });

  const searchCatalog = async (term: string) => {
    if (!term.trim()) {
      setCatalogItems([]);
      return;
    }

    try {
      setLoading(true);
      const response = await fetch(`/api/medical-records/search/catalogs?type=non_pathological&search=${encodeURIComponent(term)}&limit=20`);
      const data = await response.json();
      
      if (response.ok) {
        setCatalogItems(data.data || []);
      }
    } catch (error) {
      console.error('Error searching catalog:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const debounce = setTimeout(() => {
      searchCatalog(searchTerm);
    }, 300);

    return () => clearTimeout(debounce);
  }, [searchTerm]);

  const handleAdd = () => {
    if (!selectedItem || !formData.value.trim()) return;

    const newItem = {
      historyId: selectedItem.id,
      historyName: selectedItem.name,
      category: selectedItem.category,
      value: formData.value,
      isPositive: formData.isPositive,
      notes: formData.notes,
      ageRelevant: selectedItem.ageRelevant
    };

    onAdd(newItem);
    setSelectedItem(null);
    setSearchTerm('');
    setFormData({
      value: '',
      isPositive: true,
      notes: ''
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50 pointer-events-none">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl border pointer-events-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Agregar Antecedente No Patológico</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <div className="p-6 space-y-4">
          {/* Búsqueda en catálogo */}
          <div>
            <Label>Buscar antecedente</Label>
            <div className="relative mt-1">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <input
                type="text"
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Buscar por nombre del antecedente..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </div>

          {/* Resultados de búsqueda */}
          {catalogItems.length > 0 && (
            <div>
              <Label>Seleccionar antecedente</Label>
              <div className="mt-1 max-h-40 overflow-y-auto border border-gray-300 rounded-md">
                {catalogItems.map((item) => (
                  <div
                    key={item.id}
                    className={`p-3 cursor-pointer hover:bg-gray-50 ${
                      selectedItem?.id === item.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                    }`}
                    onClick={() => setSelectedItem(item)}
                  >
                    <div className="font-medium text-gray-900">{item.name}</div>
                    {item.description && (
                      <div className="text-sm text-gray-600">{item.description}</div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {selectedItem && (
            <>
              <div className="p-3 bg-blue-50 rounded-lg">
                <p className="text-sm font-medium text-blue-900">Antecedente seleccionado:</p>
                <p className="text-blue-800">{selectedItem.name}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label>Valor/Descripción</Label>
                  <input
                    type="text"
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    placeholder="Describe el antecedente..."
                    value={formData.value}
                    onChange={(e) => setFormData({...formData, value: e.target.value})}
                  />
                </div>

                <div>
                  <Label>Tipo</Label>
                  <select
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    value={formData.isPositive ? 'positive' : 'risk'}
                    onChange={(e) => setFormData({...formData, isPositive: e.target.value === 'positive'})}
                  >
                    <option value="positive">Positivo</option>
                    <option value="risk">Factor de Riesgo</option>
                  </select>
                </div>
              </div>

              <div>
                <Label>Notas adicionales</Label>
                <textarea
                  className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md resize-none"
                  rows={3}
                  placeholder="Observaciones adicionales..."
                  value={formData.notes}
                  onChange={(e) => setFormData({...formData, notes: e.target.value})}
                />
              </div>
            </>
          )}
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleAdd} disabled={!selectedItem || !formData.value.trim()}>
            Agregar Antecedente
          </Button>
        </div>
      </div>
    </div>
  );
}

function AddAllergyModal({ isOpen, onClose, onAdd }: {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
}) {
  const [formData, setFormData] = useState({
    allergen: '',
    type: 'medicamento',
    severity: 'leve',
    reaction: '',
    notes: ''
  });

  const handleAdd = () => {
    if (!formData.allergen.trim()) return;
    
    const newItem = {
      id: Date.now().toString(),
      allergen: formData.allergen,
      type: formData.type,
      severity: formData.severity,
      reaction: formData.reaction,
      notes: formData.notes,
      category: 'allergy'
    };
    
    onAdd(newItem);
    onClose();
    
    // Reset form
    setFormData({
      allergen: '',
      type: 'medicamento',
      severity: 'leve',
      reaction: '',
      notes: ''
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50 pointer-events-none">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl border pointer-events-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Agregar Alergia</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          <div>
            <Label>Alérgeno *</Label>
            <input
              type="text"
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nombre del alérgeno..."
              value={formData.allergen}
              onChange={(e) => setFormData({...formData, allergen: e.target.value})}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Tipo</Label>
              <select
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                value={formData.type}
                onChange={(e) => setFormData({...formData, type: e.target.value})}
              >
                <option value="medicamento">Medicamento</option>
                <option value="alimento">Alimento</option>
                <option value="ambiental">Ambiental</option>
                <option value="contacto">Contacto</option>
                <option value="otro">Otro</option>
              </select>
            </div>

            <div>
              <Label>Severidad</Label>
              <select
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                value={formData.severity}
                onChange={(e) => setFormData({...formData, severity: e.target.value})}
              >
                <option value="leve">Leve</option>
                <option value="moderada">Moderada</option>
                <option value="severa">Severa</option>
                <option value="anafilaxia">Anafilaxia</option>
              </select>
            </div>
          </div>

          <div>
            <Label>Reacción</Label>
            <input
              type="text"
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
              placeholder="Descripción de la reacción..."
              value={formData.reaction}
              onChange={(e) => setFormData({...formData, reaction: e.target.value})}
            />
          </div>

          <div>
            <Label>Notas adicionales</Label>
            <textarea
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md resize-none"
              rows={3}
              placeholder="Observaciones adicionales..."
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleAdd} disabled={!formData.allergen.trim()}>
            Agregar Alergia
          </Button>
        </div>
      </div>
    </div>
  );
}

function AddVaccinationModal({ isOpen, onClose, onAdd }: {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (item: any) => void;
}) {
  const [formData, setFormData] = useState({
    vaccine: '',
    date: '',
    dose: '1',
    lot: '',
    manufacturer: '',
    nextDose: '',
    notes: ''
  });

  const handleAdd = () => {
    if (!formData.vaccine.trim() || !formData.date) return;
    
    const newItem = {
      id: Date.now().toString(),
      vaccine: formData.vaccine,
      date: formData.date,
      dose: formData.dose,
      lot: formData.lot,
      manufacturer: formData.manufacturer,
      nextDose: formData.nextDose,
      notes: formData.notes,
      category: 'vaccination'
    };
    
    onAdd(newItem);
    onClose();
    
    // Reset form
    setFormData({
      vaccine: '',
      date: '',
      dose: '1',
      lot: '',
      manufacturer: '',
      nextDose: '',
      notes: ''
    });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 flex items-center justify-center p-4 z-50 pointer-events-none">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto shadow-xl border pointer-events-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Agregar Vacunación</h3>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <div className="p-6 space-y-4">
          <div>
            <Label>Vacuna *</Label>
            <input
              type="text"
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Nombre de la vacuna..."
              value={formData.vaccine}
              onChange={(e) => setFormData({...formData, vaccine: e.target.value})}
            />
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Fecha de aplicación *</Label>
              <input
                type="date"
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                value={formData.date}
                onChange={(e) => setFormData({...formData, date: e.target.value})}
              />
            </div>

            <div>
              <Label>Dosis</Label>
              <select
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                value={formData.dose}
                onChange={(e) => setFormData({...formData, dose: e.target.value})}
              >
                <option value="1">Primera dosis</option>
                <option value="2">Segunda dosis</option>
                <option value="3">Tercera dosis</option>
                <option value="refuerzo">Refuerzo</option>
                <option value="unica">Dosis única</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label>Lote</Label>
              <input
                type="text"
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Número de lote..."
                value={formData.lot}
                onChange={(e) => setFormData({...formData, lot: e.target.value})}
              />
            </div>

            <div>
              <Label>Fabricante</Label>
              <input
                type="text"
                className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                placeholder="Laboratorio fabricante..."
                value={formData.manufacturer}
                onChange={(e) => setFormData({...formData, manufacturer: e.target.value})}
              />
            </div>
          </div>

          <div>
            <Label>Próxima dosis</Label>
            <input
              type="date"
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
              value={formData.nextDose}
              onChange={(e) => setFormData({...formData, nextDose: e.target.value})}
            />
          </div>

          <div>
            <Label>Notas adicionales</Label>
            <textarea
              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md resize-none"
              rows={3}
              placeholder="Observaciones, reacciones, etc..."
              value={formData.notes}
              onChange={(e) => setFormData({...formData, notes: e.target.value})}
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 p-6 border-t border-gray-200">
          <Button variant="outline" onClick={onClose}>
            Cancelar
          </Button>
          <Button onClick={handleAdd} disabled={!formData.vaccine.trim() || !formData.date}>
            Agregar Vacunación
          </Button>
        </div>
      </div>
    </div>
  );
}

// Tab de Documentos
function DocumentsTab({ recordId }: any) {
  const [documents, setDocuments] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchDocuments();
  }, [recordId]);

  const fetchDocuments = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/medical-records/${recordId}/documents`);
      
      if (response.ok) {
        const data = await response.json();
        setDocuments(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="text-center py-8">
        <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
        <p className="text-gray-500">Cargando documentos...</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Documentos del Expediente
          </CardTitle>
        </CardHeader>
        <CardContent>
          {documents.length > 0 ? (
            <div className="space-y-3">
              {documents.map((document: any, index: number) => (
                <div key={document.id || index} className="p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{document.name}</h4>
                      <p className="text-sm text-gray-600">{document.type}</p>
                      <p className="text-xs text-gray-500">
                        Subido el {formatDate(document.uploadDate)}
                      </p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      Ver
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
              <p className="text-gray-500 mb-4">No hay documentos adjuntos</p>
              <Button disabled>
                <Plus className="h-4 w-4 mr-2" />
                Subir Documento
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Tab de Reportes
function ReportsTab({ recordId, stats }: any) {
  return (
    <div className="space-y-4">
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <BarChart3 className="h-5 w-5 text-blue-600" />
            Estadísticas del Expediente
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.totalConsultations}</div>
              <div className="text-sm text-gray-600">Consultas Totales</div>
            </div>
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{stats.activeDiagnoses}</div>
              <div className="text-sm text-gray-600">Diagnósticos Activos</div>
            </div>
            <div className="text-center p-4 bg-orange-50 rounded-lg">
              <div className="text-2xl font-bold text-orange-600">{stats.activePrescriptions}</div>
              <div className="text-sm text-gray-600">Prescripciones Activas</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.pendingFollowUps}</div>
              <div className="text-sm text-gray-600">Seguimientos Pendientes</div>
            </div>
          </div>
          
          {stats.lastConsultationDate && (
            <div className="mt-6 p-4 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-gray-700">Última Consulta</Label>
              <p className="text-gray-900">{formatDate(stats.lastConsultationDate)}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg">Funcionalidad en Desarrollo</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p className="text-gray-500 mb-4">Los reportes detallados estarán disponibles próximamente</p>
            <p className="text-sm text-gray-400">
              Incluirá gráficos de evolución, análisis de tendencias y reportes personalizados
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}