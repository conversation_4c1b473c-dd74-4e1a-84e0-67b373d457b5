import { db } from '../db/drizzle';
import { user, userRoles, medicalSpecialties } from '../db/schema';
import { eq, and } from 'drizzle-orm';

async function testDoctorsEndpoint() {
  try {
    console.log('🔍 Probando consulta de médicos...');
    
    // Simular la consulta del endpoint
    const conditions = [
      eq(userRoles.role, 'doctor'), // Solo usuarios con rol de doctor
      eq(userRoles.status, 'active') // Solo médicos activos
    ];

    const whereClause = and(...conditions);

    // Obtener médicos con sus especialidades
    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        medicalLicense: userRoles.medicalLicense,
        phone: user.phone,
        status: userRoles.status,
        specialtyId: userRoles.specialtyId,
        specialtyName: medicalSpecialties.name,
        consultoryId: userRoles.consultoryId,
      })
      .from(userRoles)
      .innerJoin(user, eq(userRoles.userId, user.id))
      .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
      .where(whereClause)
      .orderBy(user.firstName, user.lastName);

    console.log(`✅ Médicos encontrados: ${doctors.length}`);
    
    // Formatear datos para la respuesta
    const formattedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      name: `${doctor.firstName || ''} ${doctor.lastName || ''}`.trim(),
      firstName: doctor.firstName,
      lastName: doctor.lastName,
      email: doctor.email,
      medicalLicense: doctor.medicalLicense,
      phone: doctor.phone,
      isActive: doctor.status === 'active',
      specialty: doctor.specialtyName || null,
      specialtyId: doctor.specialtyId,
      consultoryId: doctor.consultoryId,
    }));

    console.log('👨‍⚕️ Médicos formateados:');
    formattedDoctors.forEach((doctor, index) => {
      console.log(`  ${index + 1}. ${doctor.name} (${doctor.email}) - Especialidad: ${doctor.specialty || 'Sin especialidad'}`);
    });
    
    return {
      success: true,
      data: formattedDoctors,
      total: formattedDoctors.length,
    };
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  testDoctorsEndpoint()
    .then((result) => {
      console.log('\n🎉 Prueba completada exitosamente');
      console.log(`📊 Total de médicos: ${result.total}`);
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { testDoctorsEndpoint };