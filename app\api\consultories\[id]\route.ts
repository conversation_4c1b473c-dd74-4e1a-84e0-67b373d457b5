import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const consultoryId = params.id;

    if (!consultoryId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de consultorio es requerido' 
        },
        { status: 400 }
      );
    }

    const consultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, consultoryId))
      .limit(1);

    if (!consultory || consultory.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Consultorio no encontrado' 
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: consultory[0]
    });

  } catch (error) {
    console.error('Error fetching consultory:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al obtener información del consultorio' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const consultoryId = params.id;
    const body = await request.json();

    if (!consultoryId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'ID de consultorio es requerido' 
        },
        { status: 400 }
      );
    }

    // Actualizar consultorio
    await db
      .update(consultories)
      .set({
        ...body,
        updatedAt: new Date()
      })
      .where(eq(consultories.id, consultoryId));

    return NextResponse.json({
      success: true,
      message: 'Consultorio actualizado correctamente'
    });

  } catch (error) {
    console.error('Error updating consultory:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al actualizar el consultorio' 
      },
      { status: 500 }
    );
  }
}