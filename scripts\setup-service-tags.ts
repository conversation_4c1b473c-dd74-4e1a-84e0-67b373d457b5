import { seedServiceTags } from '../db/seeds/service-tags';

async function main() {
  try {
    console.log('🚀 Setting up service tags system...');
    
    console.log('\n📦 Step 1: Seeding service tags...');
    await seedServiceTags();
    
    console.log('\n✅ Service tags system setup completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Update API endpoints to handle tags');
    console.log('   2. Update frontend to use tag selector');
    console.log('   3. Migrate existing services to use tags');
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error setting up service tags system:', error);
    process.exit(1);
  }
}

main();