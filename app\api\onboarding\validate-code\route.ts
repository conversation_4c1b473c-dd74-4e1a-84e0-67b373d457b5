import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { associationCodes, user, userRoles } from '@/db/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and, gt, isNull } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    const { code }: { code: string } = await request.json();

    if (!code || code.length !== 6) {
      return NextResponse.json(
        { success: false, message: 'Código inválido' },
        { status: 400 }
      );
    }

    // Verificar que el usuario es un guardian
    const guardianRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'guardian'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (guardianRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solo los guardians pueden validar códigos de asociación' },
        { status: 403 }
      );
    }

    // Buscar el código de asociación válido
    const associationCode = await db
      .select({
        id: associationCodes.id,
        patientId: associationCodes.patientId,
        expiresAt: associationCodes.expiresAt,
        usedBy: associationCodes.usedBy,
        usedAt: associationCodes.usedAt,
      })
      .from(associationCodes)
      .where(
        and(
          eq(associationCodes.code, code),
          gt(associationCodes.expiresAt, new Date()) // No expirado
          // No verificamos usedBy porque un código puede ser usado por múltiples guardians
        )
      )
      .limit(1);

    if (associationCode.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Código inválido o expirado' },
        { status: 404 }
      );
    }

    // Obtener información del paciente
    const patient = await db
      .select({
        id: user.id,
        name: user.name,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        dateOfBirth: user.dateOfBirth,
        documentNumber: user.documentNumber,
        documentType: user.documentType
      })
      .from(user)
      .where(eq(user.id, associationCode[0].patientId))
      .limit(1);

    if (patient.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    const patientData = patient[0];

    // Calcular edad del paciente
    let patientAge = null;
    if (patientData.dateOfBirth) {
      const birthDate = new Date(patientData.dateOfBirth);
      const today = new Date();
      patientAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        patientAge--;
      }
    }

    // Retornar información del paciente para confirmar la asociación
    return NextResponse.json({
      success: true,
      message: 'Código válido',
      data: {
        codeId: associationCode[0].id,
        expiresAt: associationCode[0].expiresAt,
        patient: {
          id: patientData.id,
          name: patientData.name,
          fullName: `${patientData.firstName || ''} ${patientData.lastName || ''}`.trim(),
          firstName: patientData.firstName,
          lastName: patientData.lastName,
          dateOfBirth: patientData.dateOfBirth,
          age: patientAge,
          documentType: patientData.documentType,
          documentNumber: patientData.documentNumber,
          email: patientData.email
        }
      }
    });

  } catch (error) {
    console.error('Error validando código:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 