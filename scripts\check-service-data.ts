import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq } from 'drizzle-orm';
import * as schema from '../db/schema';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

async function checkServiceData() {
  const serviceId = 'Xeu0lWkOlGulg-XBGcDO9';
  const appointmentId = 'i7EgTkGSq1RUJWsHmRZN8';
  
  try {
    console.log('🔍 Buscando información del servicio médico...');
    
    // Buscar el servicio
    const service = await db
      .select()
      .from(schema.medicalServices)
      .where(eq(schema.medicalServices.id, serviceId))
      .limit(1);
    
    if (service.length > 0) {
      console.log('✅ Servicio encontrado:', service[0]);
    } else {
      console.log('⚠️ No se encontró el servicio médico');
    }
    
    console.log('\n🔍 Buscando precio del servicio para el doctor...');
    
    // Buscar el precio del servicio para el doctor específico
    const doctorServicePrice = await db
      .select()
      .from(schema.doctorServicePrices)
      .where(eq(schema.doctorServicePrices.serviceId, serviceId))
      .limit(5);
    
    console.log('💰 Precios encontrados:', doctorServicePrice);
    
    // Actualizar la cita con la información completa del servicio
    if (service.length > 0) {
      console.log('\n🔄 Actualizando cita con información completa del servicio...');
      
      const defaultPrice = doctorServicePrice.find(p => p.doctorId === 'user_2zkcCb4KrwJcCMMyxHlRWnlywMY')?.price || service[0].basePrice;
      
      const result = await db
        .update(schema.appointments)
        .set({
          serviceName: service[0].name,
          servicePrice: defaultPrice,
          updatedAt: new Date()
        })
        .where(eq(schema.appointments.id, appointmentId))
        .returning();
      
      if (result.length > 0) {
        console.log('✅ Cita actualizada con información del servicio');
        console.log('📋 Datos actualizados:', {
          id: result[0].id,
          serviceId: result[0].serviceId,
          serviceName: result[0].serviceName,
          servicePrice: result[0].servicePrice
        });
      }
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

checkServiceData();