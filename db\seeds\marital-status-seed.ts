import { db } from '@/db/drizzle';
import { maritalStatus } from '@/db/schema';

export const maritalStatusData = [
  {
    id: 'single',
    name: '<PERSON><PERSON>(a)',
    allowsSpouse: false,
    legalImplications: 'No requiere consentimiento de cónyuge para decisiones médicas',
    description: 'Persona sin vínculo matrimonial ni unión de hecho',
    order: 1
  },
  {
    id: 'married',
    name: 'Casa<PERSON>(a)',
    allowsSpouse: true,
    legalImplications: 'Puede requerir consentimiento del cónyuge para procedimientos médicos',
    description: 'Persona unida en matrimonio civil o religioso',
    order: 2
  },
  {
    id: 'common-law',
    name: 'Unión de Hecho',
    allowsSpouse: true,
    legalImplications: 'Reconocida legalmente, puede requerir consentimiento de la pareja',
    description: 'Convivencia marital sin matrimonio formal pero con reconocimiento legal',
    order: 3
  },
  {
    id: 'divorced',
    name: '<PERSON><PERSON><PERSON><PERSON>(a)',
    allowsSpouse: false,
    legalImplications: 'No requiere consentimiento de ex-cónyuge para decisiones médicas',
    description: 'Persona que ha disuelto legalmente su matrimonio',
    order: 4
  },
  {
    id: 'widowed',
    name: 'Viudo(a)',
    allowsSpouse: false,
    legalImplications: 'No requiere consentimiento de cónyuge para decisiones médicas',
    description: 'Persona cuyo cónyuge ha fallecido',
    order: 5
  },
  {
    id: 'separated',
    name: 'Separado(a)',
    allowsSpouse: false,
    legalImplications: 'Separación legal puede afectar decisiones médicas según legislación local',
    description: 'Persona separada legalmente pero sin divorcio finalizado',
    order: 6
  },
  {
    id: 'engaged',
    name: 'Comprometido(a)',
    allowsSpouse: false,
    legalImplications: 'No tiene implicaciones legales para decisiones médicas',
    description: 'Persona con compromiso matrimonial pero aún soltera',
    order: 7
  },
  {
    id: 'other',
    name: 'Otro',
    allowsSpouse: false,
    legalImplications: 'Evaluar caso por caso según situación específica',
    description: 'Estado civil no especificado en las opciones anteriores',
    order: 8
  }
];

export async function seedMaritalStatus() {
  console.log('💍 Seeding marital status...');
  
  try {
    await db.insert(maritalStatus).values(maritalStatusData).onConflictDoNothing();
    console.log('✅ Marital status seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding marital status:', error);
    throw error;
  }
}