import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, consultories, appointments, doctorSchedules } from '@/db/schema';
import { eq, and, or, gte, lte, between } from 'drizzle-orm';
import { format, addDays, startOfDay, endOfDay, parseISO, addMinutes } from 'date-fns';
import { es } from 'date-fns/locale';

export async function GET(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    // Obtener parámetros de query
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get('doctorId');
    const consultorioId = searchParams.get('consultorioId');
    const fechaParam = searchParams.get('fecha'); // YYYY-MM-DD opcional

    // Validaciones básicas
    if (!doctorId || !consultorioId) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Parámetros requeridos: doctorId, consultorioId' 
        },
        { status: 400 }
      );
    }

    console.log(`🗓️ Consultando horarios - Doctor: ${doctorId}, Consultorio: ${consultorioId}`);

    // 1. Verificar que el doctor existe y está activo
    const doctor = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        isActive: user.isActive,
      })
      .from(user)
      .where(
        and(
          eq(user.id, doctorId),
          eq(user.role, 'doctor'),
          eq(user.isActive, true)
        )
      )
      .limit(1);

    if (doctor.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Doctor no encontrado o inactivo' },
        { status: 404 }
      );
    }

    // 2. Verificar que el consultorio existe y está activo
    const consultorio = await db
      .select({
        id: consultories.id,
        name: consultories.name,
        isActive: consultories.isActive,
      })
      .from(consultories)
      .where(
        and(
          eq(consultories.id, consultorioId),
          eq(consultories.isActive, true)
        )
      )
      .limit(1);

    if (consultorio.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Consultorio no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const doctorInfo = doctor[0];
    const consultorioInfo = consultorio[0];

    // 3. Determinar rango de fechas a consultar
    const fechaInicio = fechaParam ? parseISO(fechaParam) : new Date();
    const fechaFin = addDays(fechaInicio, 7); // Próximos 7 días

    console.log(`📅 Buscando horarios desde ${format(fechaInicio, 'yyyy-MM-dd')} hasta ${format(fechaFin, 'yyyy-MM-dd')}`);

    // 4. Obtener horarios del doctor (schedule base)
    // Por simplicidad, asumimos horario estándar: Lun-Vie 8:00-17:00
    // TODO: Implementar consulta real a doctorSchedules si existe
    const horariosBase = generarHorariosBase(fechaInicio, fechaFin);

    // 5. Obtener citas existentes para filtrar horarios ocupados
    const citasExistentes = await db
      .select({
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        scheduledDate: appointments.scheduledDate,
      })
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          eq(appointments.consultoryId, consultorioId),
          between(appointments.scheduledDate, startOfDay(fechaInicio), endOfDay(fechaFin)),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'pending_confirmation'),
            eq(appointments.status, 'checked_in'),
            eq(appointments.status, 'in_progress')
          )
        )
      );

    console.log(`⏰ Encontradas ${citasExistentes.length} citas existentes`);

    // 6. Filtrar horarios disponibles
    const horariosDisponibles = horariosBase.filter(horario => {
      return !citasExistentes.some(cita => {
        const inicioHorario = horario.startTime;
        const finHorario = horario.endTime;
        const inicioCita = new Date(cita.startTime);
        const finCita = new Date(cita.endTime);

        // Verificar si hay conflicto de horarios
        return (
          (inicioHorario >= inicioCita && inicioHorario < finCita) ||
          (finHorario > inicioCita && finHorario <= finCita) ||
          (inicioHorario <= inicioCita && finHorario >= finCita)
        );
      });
    });

    // 7. Tomar solo las primeras 3 opciones disponibles
    const tresPrimerasOpciones = horariosDisponibles.slice(0, 3);

    console.log(`✅ Encontrados ${tresPrimerasOpciones.length} horarios disponibles`);

    // 8. Formatear respuesta
    const horariosFormateados = tresPrimerasOpciones.map(horario => ({
      slotId: `${doctorId}-${horario.startTime.getTime()}`,// formato: doctorId-timestamp
      fecha: format(horario.startTime, 'yyyy-MM-dd'),
      fechaLegible: format(horario.startTime, "EEEE, dd 'de' MMMM", { locale: es }),
      hora: format(horario.startTime, 'HH:mm'),
      horaLegible: format(horario.startTime, 'h:mm a', { locale: es }),
      disponible: true,
      doctorNombre: `${doctorInfo.firstName} ${doctorInfo.lastName}`,
      consultorioNombre: consultorioInfo.name
    }));

    return NextResponse.json({
      success: true,
      doctor: {
        id: doctorInfo.id,
        nombre: `${doctorInfo.firstName} ${doctorInfo.lastName}`
      },
      consultorio: {
        id: consultorioInfo.id,
        nombre: consultorioInfo.name
      },
      horarios: horariosFormateados,
      mensaje: `Horarios disponibles con ${doctorInfo.firstName} ${doctorInfo.lastName} en ${consultorioInfo.name}`
    });

  } catch (error) {
    console.error('Error en horarios-disponibles:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

/**
 * Genera horarios base para un rango de fechas
 * Por simplicidad: Lun-Vie 8:00-22:00 (10 PM), slots de 30 minutos
 */
function generarHorariosBase(fechaInicio: Date, fechaFin: Date): Array<{startTime: Date, endTime: Date}> {
  const horarios: Array<{startTime: Date, endTime: Date}> = [];
  let fechaActual = new Date(fechaInicio);

  while (fechaActual <= fechaFin) {
    // Solo días laborales (Lun-Vie)
    const diaSemana = fechaActual.getDay();
    if (diaSemana >= 1 && diaSemana <= 5) {
      
      // Solo horarios futuros (no pasados)
      const ahora = new Date();
      if (fechaActual >= startOfDay(ahora)) {
        
        // Generar slots de 30 minutos de 8:00 a 22:00 (10 PM)
        for (let hora = 8; hora < 22; hora++) {
          for (let minuto = 0; minuto < 60; minuto += 30) {
            const inicioSlot = new Date(fechaActual);
            inicioSlot.setHours(hora, minuto, 0, 0);
            
            const finSlot = addMinutes(inicioSlot, 30);
            
            // Solo agregar si es horario futuro
            if (inicioSlot > ahora) {
              horarios.push({
                startTime: inicioSlot,
                endTime: finSlot
              });
            }
          }
        }
      }
    }
    
    fechaActual = addDays(fechaActual, 1);
  }

  return horarios;
}