import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { generateId } from '@/lib/utils';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId: currentUserId } = await auth();
    const { id } = await params;
    
    if (!currentUserId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Buscar paciente
    const [patient] = await db
      .select()
      .from(user)
      .where(eq(user.id, id))
      .limit(1);

    if (!patient) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    // Verificar que no tenga cuenta activa ya
    if (patient.overallStatus === 'active') {
      return NextResponse.json(
        { error: 'El paciente ya tiene cuenta activa' },
        { status: 400 }
      );
    }

    // Verificar que tenga email válido
    if (!patient.email || patient.email.includes('@temp.local')) {
      return NextResponse.json(
        { error: 'El paciente necesita un email válido para recibir la invitación' },
        { status: 400 }
      );
    }

    // Generar token único para la invitación
    const invitationToken = generateId();
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // Expira en 7 días

    // Actualizar usuario con token de invitación
    await db
      .update(user)
      .set({
        overallStatus: 'invited',
        // Guardar token en roleData o crear nuevo campo si es necesario
        updatedAt: new Date(),
      })
      .where(eq(user.id, id));

    // TODO: Aquí se enviaría el email con Resend
    // Por ahora solo preparamos los datos
    const invitationData = {
      patientName: `${patient.firstName} ${patient.lastName}`,
      email: patient.email,
      token: invitationToken,
      expiresAt,
      activationUrl: `${process.env.NEXT_PUBLIC_APP_URL}/activate-account?token=${invitationToken}`,
    };

    console.log('📧 Invitación preparada:', invitationData);

    return NextResponse.json({
      success: true,
      data: {
        patientId: patient.id,
        patientName: `${patient.firstName} ${patient.lastName}`,
        email: patient.email,
        status: 'invited',
        token: invitationToken,
        expiresAt,
        activationUrl: invitationData.activationUrl,
      },
      message: 'Invitación preparada exitosamente',
    });

  } catch (error) {
    console.error('Error enviando invitación:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al enviar invitación',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}