'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Calendar, Clock, Plus, Search, Filter, ChevronLeft, ChevronRight, X, BarChart3, CheckCircle, Bot, FileText, AlertCircle, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, subDays } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatInConsultoryTimezone, formatDateInConsultoryTimezone, formatTimeInConsultoryTimezone, formatLocalTimeFromUTC, formatLocalDateFromUTC } from '@/lib/timezone-utils';
import { AppointmentsList } from '@/components/agenda/appointments-list';
import { useRouter } from 'next/navigation';
import { CalendarWeekView } from '@/components/agenda/calendar-week-view';
import { CalendarMonthView } from '@/components/agenda/calendar-month-view';
import { DeleteConfirmationDialog } from '@/components/ui/delete-confirmation-dialog';
import { CalendarDayView } from '@/components/agenda/calendar-day-view';
import { DoctorHeader } from '@/components/agenda/doctor-header';
import { AIAssistantChat } from '@/components/agenda/ai-assistant-chat';
import PreCheckinQuickView from '@/components/pre-checkin/pre-checkin-quick-view';
import { toast } from 'sonner';
import { cn, formatDateTime } from '@/lib/utils';

export default function DoctorAgendaPage() {
  const { user } = useUser();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month' | 'list'>('day');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [stats, setStats] = useState({
    total: 0,
    scheduled: 0,
    confirmed: 0,
    completed: 0,
    cancelled: 0
  });
  const [doctorConsultory, setDoctorConsultory] = useState(null);
  const [showFilters, setShowFilters] = useState(false);
  const [showKPIs, setShowKPIs] = useState(false);
  const [showAIAssistant, setShowAIAssistant] = useState(false);
  const [doctorInfo, setDoctorInfo] = useState(null);
  const [showCreateRecordModal, setShowCreateRecordModal] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [showCompleteConfirmDialog, setShowCompleteConfirmDialog] = useState(false);
  const [appointmentToComplete, setAppointmentToComplete] = useState(null);
  const [showPreCheckinModal, setShowPreCheckinModal] = useState(false);
  const [selectedPreCheckinAppointment, setSelectedPreCheckinAppointment] = useState(null);

  // Obtener información del médico
  const fetchDoctorInfo = async () => {
    try {
      if (!user?.id) return;
      
      console.log('🔍 Obteniendo información del médico desde BD:', user.id);
      
      const response = await fetch(`/api/auth/current-user`);
      const data = await response.json();
      
      console.log('📋 Respuesta del API:', data);
      
      if (response.ok && data.success) {
        const userInfo = data.data;
        
        setDoctorInfo({
          id: userInfo.id,
          firstName: userInfo.firstName || userInfo.name?.split(' ')[0] || 'Doctor',
          lastName: userInfo.lastName || userInfo.name?.split(' ')[1] || '',
          email: userInfo.email,
          imageUrl: userInfo.imageUrl,
          clerkImageUrl: user?.imageUrl,
          specialty: userInfo.medicalSpecialty || 'Medicina General'
        });
        
        console.log('✅ Información del médico cargada desde BD:', {
          firstName: userInfo.firstName,
          lastName: userInfo.lastName,
          name: userInfo.name,
          specialty: userInfo.medicalSpecialty,
          imageUrl: userInfo.imageUrl
        });
      } else {
        console.error('❌ Error en respuesta del API:', data);
        // Si falla, mostrar mensaje de error pero no usar fallback de Clerk
        setDoctorInfo({
          id: user.id,
          firstName: 'Usuario',
          lastName: 'No encontrado',
          email: '',
          imageUrl: user.imageUrl,
          clerkImageUrl: user?.imageUrl,
          specialty: 'Verificar datos'
        });
      }
    } catch (error) {
      console.error('Error al obtener información del médico:', error);
      // En caso de error de red, también mostrar mensaje indicativo
      setDoctorInfo({
        id: user?.id || '',
        firstName: 'Error',
        lastName: 'de conexión',
        email: '',
        imageUrl: user?.imageUrl,
        clerkImageUrl: user?.imageUrl,
        specialty: 'Revisar configuración'
      });
    }
  };

  // Obtener consultorio asignado del doctor
  const fetchDoctorConsultory = async () => {
    try {
      const doctorId = user?.id;
      if (!doctorId) return;
      
      console.log('🔍 Buscando consultorio para doctor:', doctorId);
      
      // Crear un endpoint específico para obtener consultorio del doctor actual
      const response = await fetch(`/api/doctor/consultory?doctorId=${doctorId}`);
      const data = await response.json();
      
      console.log('📋 Respuesta del consultorio:', data);
      
      if (response.ok && data.success && data.data) {
        console.log('✅ Consultorio cargado:', data.data);
        setDoctorConsultory(data.data);
      } else {
        console.log('❌ No se encontró consultorio asignado');
      }
    } catch (error) {
      console.error('Error al cargar consultorio del doctor:', error);
    }
  };

  // Obtener citas del doctor
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      
      // Usar el usuario actual como doctor
      const doctorId = user?.id;
      if (!doctorId) {
        setAppointments([]);
        setLoading(false);
        return;
      }
      
      // Calcular rango de fechas según la vista
      let dateFrom, dateTo;
      if (viewMode === 'day') {
        dateFrom = format(currentDate, 'yyyy-MM-dd');
        dateTo = format(currentDate, 'yyyy-MM-dd');
        console.log('🔍 Vista día - Buscando citas para:', { 
          dateFrom, 
          dateTo, 
          currentDateStr: format(currentDate, 'yyyy-MM-dd HH:mm:ss'), 
          viewMode,
          doctorId 
        });
      } else if (viewMode === 'week') {
        dateFrom = format(startOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
        dateTo = format(endOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
      } else if (viewMode === 'month') {
        dateFrom = format(startOfMonth(currentDate), 'yyyy-MM-dd');
        dateTo = format(endOfMonth(currentDate), 'yyyy-MM-dd');
      }

      const params = new URLSearchParams({
        doctorId: doctorId,
        ...(dateFrom && { dateFrom }),
        ...(dateTo && { dateTo }),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        orderBy: 'scheduledDate',
        orderDirection: 'asc',
        limit: '100'
      });

      const apiUrl = `/api/appointments?${params}`;
      console.log('🌐 URL del API:', apiUrl);
      const response = await fetch(apiUrl);
      const data = await response.json();

      if (response.ok) {
        const appointmentsData = data.data || [];
        setAppointments(appointmentsData);
        if (data.stats?.byStatus) {
          setStats({
            total: data.stats.total || 0,
            scheduled: data.stats.byStatus.scheduled || 0,
            confirmed: data.stats.byStatus.confirmed || 0,
            completed: data.stats.byStatus.completed || 0,
            cancelled: data.stats.byStatus.cancelled || 0
          });
        }
      } else {
        toast.error('Error al cargar las citas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  // Handlers para las acciones de citas
  const handleAppointmentEdit = (appointment: any) => {
    router.push(`/dashboard/doctor/agenda/appointment/${appointment.id}?mode=edit`);
  };

  const handleAppointmentConfirm = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'confirmed' })
      });

      if (response.ok) {
        toast.success('Cita confirmada exitosamente');
        fetchAppointments();
      } else {
        toast.error('Error al confirmar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentCancel = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          status: 'cancelled',
          cancellationReason: 'Cancelada por el doctor'
        })
      });

      if (response.ok) {
        toast.success('Cita cancelada exitosamente');
        fetchAppointments();
      } else {
        toast.error('Error al cancelar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const [deleteDialog, setDeleteDialog] = useState<{ 
    open: boolean; 
    appointment: any | null; 
    loading: boolean; 
  }>({ open: false, appointment: null, loading: false });

  const handleAppointmentDelete = (appointment: any) => {
    setDeleteDialog({ open: true, appointment, loading: false });
  };

  const handleDelete = async (type: 'logical' | 'physical') => {
    if (!deleteDialog.appointment) return;
    
    setDeleteDialog(prev => ({ ...prev, loading: true }));
    
    try {
      const url = type === 'physical' 
        ? `/api/appointments/${deleteDialog.appointment.id}?hardDelete=true`
        : `/api/appointments/${deleteDialog.appointment.id}`;
        
      const response = await fetch(url, {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        const message = type === 'physical' 
          ? 'Cita eliminada permanentemente' 
          : 'Cita cancelada exitosamente';
        toast.success(message);
        fetchAppointments();
      } else {
        const error = await response.json();
        toast.error(error.error || `Error al ${type === 'physical' ? 'eliminar' : 'cancelar'} la cita`);
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    } finally {
      setDeleteDialog(prev => ({ ...prev, loading: false }));
    }
  };

  const handleAppointmentComplete = async (appointment: any) => {
    // Primero verificar si hay una consulta médica asociada
    try {
      const consultationCheck = await fetch(`/api/medical-consultations?appointmentId=${appointment.id}`);
      const consultationData = await consultationCheck.json();
      
      if (consultationData.data && consultationData.data.length > 0) {
        const consultation = consultationData.data[0];
        
        // Si la consulta está en progreso o borrador, mostrar advertencia
        if (consultation.status === 'in_progress' || consultation.status === 'draft') {
          toast.warning('Hay una consulta médica en progreso. Debe finalizarla desde el módulo de consultas.');
          // Redirigir a la consulta
          router.push(`/dashboard/doctor/consultations/${consultation.id}?mode=edit`);
          return;
        }
      }
      
      // Si no hay consulta o ya está completada, mostrar confirmación
      setAppointmentToComplete(appointment);
      setShowCompleteConfirmDialog(true);
    } catch (error) {
      console.error('Error checking consultation:', error);
      toast.error('Error al verificar el estado de la consulta');
    }
  };

  const confirmCompleteAppointment = async () => {
    if (!appointmentToComplete) return;
    
    try {
      // Marcar cita como completada
      const response = await fetch(`/api/appointments/${appointmentToComplete.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'completed' })
      });

      if (response.ok) {
        toast.success('Cita marcada como completada');
        setShowCompleteConfirmDialog(false);
        setAppointmentToComplete(null);
        
        // Verificar si el paciente ya tiene expediente
        const recordCheck = await fetch(`/api/medical-records?patientId=${appointmentToComplete.patientId}&limit=1`);
        const recordData = await recordCheck.json();
        
        if (recordData.data && recordData.data.length > 0) {
          // Paciente ya tiene expediente, redirigir para agregar consulta
          const existingRecord = recordData.data[0];
          router.push(`/dashboard/doctor/expedientes/${existingRecord.id}?tab=consultations&from=appointment&appointmentId=${appointmentToComplete.id}`);
        } else {
          // No tiene expediente, mostrar modal para crear uno
          setSelectedAppointment(appointmentToComplete);
          setShowCreateRecordModal(true);
        }
        
        fetchAppointments();
      } else {
        toast.error('Error al completar la cita');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentCheckIn = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}/check-in`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (response.ok) {
        toast.success('Llegada del paciente registrada exitosamente');
        fetchAppointments();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al registrar llegada');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentNoShow = async (appointment: any) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}/no-show`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          reason: 'Paciente no se presentó a la cita'
        })
      });

      if (response.ok) {
        toast.success('Cita marcada como no show');
        fetchAppointments();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al marcar no show');
      }
    } catch (error) {
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleStartConsultation = async (appointment: any) => {
    try {
      toast.info('Iniciando consulta médica...');
      
      // Verificar que la cita esté en estado adecuado
      if (!['confirmed', 'checked_in'].includes(appointment.status)) {
        toast.error('La cita debe estar confirmada o el paciente debe haber llegado');
        return;
      }

      // Redirigir directamente a la nueva interfaz de consulta
      router.push(`/dashboard/doctor/agenda/consultation/${appointment.id}`);
      
    } catch (error) {
      console.error('Error starting consultation:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleViewConsultation = async (appointment: any) => {
    try {
      // Verificar que la cita esté en progreso
      if (appointment.status !== 'in_progress') {
        toast.error('Solo se pueden ver consultas en progreso');
        return;
      }

      // Buscar la consulta médica asociada a esta cita
      const response = await fetch(`/api/medical-consultations?appointmentId=${appointment.id}`);
      const result = await response.json();
      
      if (!response.ok) {
        toast.error('Error al buscar la consulta médica');
        return;
      }
      
      if (!result.data || result.data.length === 0) {
        toast.error('No se encontró una consulta médica activa para esta cita');
        return;
      }
      
      const consultation = result.data[0];
      // Navegar a la interfaz de consulta existente que funciona correctamente
      router.push(`/dashboard/doctor/consultations/${consultation.id}`);
      
    } catch (error) {
      console.error('Error viewing consultation:', error);
      toast.error('Error al acceder a la consulta');
    }
  };

  const handleAppointmentRevertCompleted = async (appointment: any) => {
    try {
      toast.info('Revirtiendo cita completada...');
      
      const response = await fetch(`/api/appointments/${appointment.id}/revert-completed`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Cita revertida exitosamente');
        await fetchAppointments(); // Refrescar para mostrar el nuevo estado
      } else {
        toast.error(result.error || 'Error al revertir la cita');
      }
    } catch (error) {
      console.error('Error reverting completed appointment:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleAppointmentRevertNoShow = async (appointment: any) => {
    try {
      toast.info('Revirtiendo estado de inasistencia...');
      
      const response = await fetch(`/api/appointments/${appointment.id}/revert-no-show`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success(result.message || 'Estado de inasistencia revertido exitosamente');
        await fetchAppointments(); // Refrescar para mostrar el nuevo estado
      } else {
        toast.error(result.error || 'Error al revertir inasistencia');
      }
    } catch (error) {
      console.error('Error reverting no-show appointment:', error);
      toast.error('Error al conectar con el servidor');
    }
  };

  const handleViewPreCheckin = (appointment: any) => {
    console.log('🔍 Abriendo pre-checkin para cita:', appointment.id);
    setSelectedPreCheckinAppointment(appointment);
    setShowPreCheckinModal(true);
  };

  // Effect para cargar información del doctor
  useEffect(() => {
    if (user?.id) {
      fetchDoctorInfo();
    }
  }, [user?.id]);

  // Effect para cargar consultorio del doctor
  useEffect(() => {
    if (user?.id) {
      fetchDoctorConsultory();
    }
  }, [user?.id]);

  // Effect para cargar citas (cuando cambien los filtros)
  useEffect(() => {
    console.log('🔄 Effect triggered:', { currentDate, viewMode, searchTerm, statusFilter });
    if (user?.id) {
      fetchAppointments();
    }
  }, [user?.id, currentDate, viewMode, searchTerm, statusFilter]);

  // Navegación de fechas
  const navigateDate = (direction: 'prev' | 'next') => {
    if (viewMode === 'day') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 1) : addDays(currentDate, 1));
    } else if (viewMode === 'week') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 7) : addDays(currentDate, 7));
    } else if (viewMode === 'month') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 30) : addDays(currentDate, 30));
    }
  };

  // Formato del período actual
  const formatCurrentPeriod = () => {
    if (viewMode === 'day') {
      return format(currentDate, "EEEE, d 'de' MMMM 'de' yyyy", { locale: es });
    } else if (viewMode === 'week') {
      const start = startOfWeek(currentDate, { locale: es });
      const end = endOfWeek(currentDate, { locale: es });
      return `${format(start, "d MMM", { locale: es })} - ${format(end, "d MMM yyyy", { locale: es })}`;
    } else if (viewMode === 'month') {
      return format(currentDate, "MMMM 'de' yyyy", { locale: es });
    }
    return '';
  };

  // Calcular estadísticas para el header
  const getDoctorStats = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const todayAppointments = appointments.filter(app =>
      formatLocalDateFromUTC(app.scheduledDate || app.startTime, 'yyyy-MM-dd') === today
    );
    
    const weekStart = startOfWeek(new Date(), { locale: es });
    const weekEnd = endOfWeek(new Date(), { locale: es });
    const weekAppointments = appointments.filter(app => {
      const appDate = new Date(app.scheduledDate);
      return appDate >= weekStart && appDate <= weekEnd;
    });
    
    const confirmedCount = appointments.filter(app => app.status === 'confirmed').length;
    
    return {
      todayCount: todayAppointments.length,
      weekCount: weekAppointments.length,
      confirmedCount
    };
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-full mx-auto px-2 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8">
        {/* Header mejorado */}
        <div className="mb-6 lg:mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            {/* Título y fecha */}
            <div className="flex flex-col sm:flex-row sm:items-start gap-4">
              <div>
                <div className="flex items-center gap-3 mb-2">
                  <Calendar className="h-6 w-6 lg:h-8 lg:w-8 text-blue-600" />
                  <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">
                    Mi Agenda Médica
                  </h1>
                </div>
                <div className="flex items-center gap-4 text-sm lg:text-base text-gray-600">
                  <span className="font-medium">{formatCurrentPeriod()}</span>
                  <span className="hidden sm:inline text-gray-400">•</span>
                  <span className="hidden sm:inline">{appointments.length} citas programadas</span>
                </div>
              </div>
              
            </div>

            {/* Controles principales */}
            <div className="flex flex-col sm:flex-row gap-3">
              {/* Navegación de fecha */}
              <div className="flex items-center gap-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('prev')}
                  className="hover:bg-green-50 hover:border-green-300"
                >
                  <ChevronLeft className="h-4 w-4 text-green-600" />
                </Button>
                <Button 
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentDate(new Date())}
                  className="hover:bg-green-50 hover:border-green-300 px-4"
                >
                  Hoy
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('next')}
                  className="hover:bg-green-50 hover:border-green-300"
                >
                  <ChevronRight className="h-4 w-4 text-green-600" />
                </Button>
              </div>

              {/* Selector de vista */}
              <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
                <TabsList className="bg-white border border-gray-200 shadow-sm">
                  <TabsTrigger value="day" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white">
                    Día
                  </TabsTrigger>
                  <TabsTrigger value="week" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white">
                    Semana
                  </TabsTrigger>
                  <TabsTrigger value="month" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white">
                    Mes
                  </TabsTrigger>
                  <TabsTrigger value="list" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white">
                    Lista
                  </TabsTrigger>
                </TabsList>
              </Tabs>

              {/* Botón nueva cita */}
              <Button 
                onClick={() => router.push('/dashboard/doctor/agenda/appointment/new')}
                className="bg-emerald-600 hover:bg-emerald-700 text-white shadow-md"
              >
                <Plus className="h-4 w-4 mr-2" />
                Nueva Cita
              </Button>
            </div>
          </div>

          {/* Controles secundarios */}
          <div className="flex gap-2 mt-4">
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowKPIs(!showKPIs)}
              className={cn(
                "shadow-sm transition-all",
                showKPIs 
                  ? "bg-purple-50 border-purple-300 text-purple-700" 
                  : "hover:bg-gray-50 hover:border-gray-300"
              )}
            >
              <BarChart3 className="h-4 w-4 mr-1.5" />
              Indicadores
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              className={cn(
                "shadow-sm transition-all",
                showFilters 
                  ? "bg-blue-50 border-blue-300 text-blue-700" 
                  : "hover:bg-gray-50 hover:border-gray-300"
              )}
            >
              <Filter className="h-4 w-4 mr-1.5" />
              Filtros
            </Button>
            
            <Button 
              variant="outline"
              size="sm"
              onClick={() => setShowAIAssistant(!showAIAssistant)}
              className={cn(
                "shadow-sm transition-all",
                showAIAssistant 
                  ? "bg-emerald-50 border-emerald-300 text-emerald-700" 
                  : "hover:bg-emerald-50 hover:border-emerald-300"
              )}
            >
              <Bot className="h-4 w-4 mr-1.5" />
              Asistente
            </Button>
          </div>
        </div>

        {/* KPI's - Solo se muestra si showKPIs es true */}
        {showKPIs && (
          <Card className="animate-in slide-in-from-top-4 duration-300 mb-6 shadow-md">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-purple-600" />
                Indicadores de Rendimiento
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-blue-50 to-blue-100">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold text-blue-900">
                      Total Citas
                    </CardTitle>
                    <div className="h-10 w-10 rounded-full bg-blue-200 flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-blue-700" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-blue-900">{stats.total}</div>
                    <div className="text-xs text-blue-700">Todas las citas</div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-sky-50 to-sky-100">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold text-sky-900">
                      Programadas
                    </CardTitle>
                    <div className="h-10 w-10 rounded-full bg-sky-200 flex items-center justify-center">
                      <Clock className="h-5 w-5 text-sky-700" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-sky-900">{stats.scheduled}</div>
                    <div className="text-xs text-sky-700">Por confirmar</div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-green-50 to-green-100">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold text-green-900">
                      Confirmadas
                    </CardTitle>
                    <div className="h-10 w-10 rounded-full bg-green-200 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-green-700" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-green-900">{stats.confirmed}</div>
                    <div className="text-xs text-green-700">Listas para atender</div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-purple-50 to-purple-100">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold text-purple-900">
                      Completadas
                    </CardTitle>
                    <div className="h-10 w-10 rounded-full bg-purple-200 flex items-center justify-center">
                      <CheckCircle className="h-5 w-5 text-purple-700" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-purple-900">{stats.completed}</div>
                    <div className="text-xs text-purple-700">Finalizadas</div>
                  </CardContent>
                </Card>

                <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm bg-gradient-to-br from-red-50 to-red-100">
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-semibold text-red-900">
                      Canceladas
                    </CardTitle>
                    <div className="h-10 w-10 rounded-full bg-red-200 flex items-center justify-center">
                      <X className="h-5 w-5 text-red-700" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-red-900">{stats.cancelled}</div>
                    <div className="text-xs text-red-700">No realizadas</div>
                  </CardContent>
                </Card>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filtros de búsqueda - Solo se muestra si showFilters es true */}
        {showFilters && (
          <Card className="animate-in slide-in-from-top-4 duration-300 mb-6 shadow-md">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Search className="h-5 w-5 text-blue-600" />
                Filtros de Búsqueda
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Buscar paciente</Label>
                  <div className="relative mt-1">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Nombre, apellido o email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Estado de la cita</Label>
                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="mt-1">
                      <SelectValue placeholder="Todos los estados" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos los estados</SelectItem>
                      <SelectItem value="scheduled">📅 Programadas</SelectItem>
                      <SelectItem value="confirmed">✅ Confirmadas</SelectItem>
                      <SelectItem value="in_progress">🟢 En consulta</SelectItem>
                      <SelectItem value="completed">✔️ Completadas</SelectItem>
                      <SelectItem value="cancelled">❌ Canceladas</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="flex items-end">
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setSearchTerm('');
                      setStatusFilter('all');
                    }}
                    className="w-full"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Limpiar Filtros
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Asistente IA - Solo se muestra si showAIAssistant es true */}
        <AIAssistantChat
          isOpen={showAIAssistant}
          onClose={() => setShowAIAssistant(false)}
          doctorInfo={doctorInfo}
        />

        {/* Vista principal */}
        <div className="bg-white rounded-lg shadow-md">
          {viewMode === 'list' && (
            <>
              <DoctorHeader
                doctorInfo={doctorInfo}
                stats={getDoctorStats()}
                isSelectable={false}
                className="mb-4"
              />
              <AppointmentsList 
                appointments={appointments}
                loading={loading}
                onView={(appointment) => {
                  router.push(`/dashboard/doctor/agenda/appointment/${appointment.id}`);
                }}
                onEdit={handleAppointmentEdit}
                onConfirm={handleAppointmentConfirm}
                onCancel={handleAppointmentCancel}
                onDelete={handleAppointmentDelete}
                onCheckIn={handleAppointmentCheckIn}
                onNoShow={handleAppointmentNoShow}
                onComplete={handleAppointmentComplete}
                onStartConsultation={handleStartConsultation}
                onViewConsultation={handleViewConsultation}
                onViewPreCheckin={handleViewPreCheckin}
                onRefresh={fetchAppointments}
                userRole="doctor"
              />
            </>
          )}

          {viewMode === 'day' && (
            <>
              <DoctorHeader
                doctorInfo={doctorInfo}
                stats={getDoctorStats()}
                isSelectable={false}
                className="mb-4"
              />
              <CalendarDayView
                appointments={appointments}
                currentDate={currentDate}
                loading={loading}
                doctorInfo={doctorInfo}
                userRole="doctor"
                onTimeSlotClick={(date, time) => {
                  // Navegar a nueva cita con fecha preseleccionada
                  const dateParam = format(currentDate, 'yyyy-MM-dd');
                  router.push(`/dashboard/doctor/agenda/appointment/new?date=${dateParam}&time=${time}`);
                }}
                onAppointmentClick={(appointment) => {
                  router.push(`/dashboard/doctor/agenda/appointment/${appointment.id}`);
                }}
                onAppointmentEdit={handleAppointmentEdit}
                onAppointmentConfirm={handleAppointmentConfirm}
                onAppointmentCancel={handleAppointmentCancel}
                onAppointmentDelete={handleAppointmentDelete}
                onAppointmentCheckIn={handleAppointmentCheckIn}
                onAppointmentNoShow={handleAppointmentNoShow}
                onAppointmentComplete={handleAppointmentComplete}
                onAppointmentStart={handleStartConsultation}
                onAppointmentRevertCompleted={handleAppointmentRevertCompleted}
                onAppointmentRevertNoShow={handleAppointmentRevertNoShow}
                onViewPreCheckin={handleViewPreCheckin}
                onViewConsultation={handleViewConsultation}
              />
            </>
          )}

          {viewMode === 'week' && (
            <>
              <DoctorHeader
                doctorInfo={doctorInfo}
                stats={getDoctorStats()}
                isSelectable={false}
                className="mb-4"
              />
              <CalendarWeekView
                appointments={appointments}
                currentDate={currentDate}
                loading={loading}
                userRole="doctor"
                onTimeSlotClick={(date, time) => {
                  // Navegar a nueva cita con fecha preseleccionada
                  const dateParam = format(currentDate, 'yyyy-MM-dd');
                  router.push(`/dashboard/doctor/agenda/appointment/new?date=${dateParam}&time=${time}`);
                }}
                onAppointmentClick={(appointment) => {
                  router.push(`/dashboard/doctor/agenda/appointment/${appointment.id}`);
                }}
                onAppointmentEdit={handleAppointmentEdit}
                onAppointmentConfirm={handleAppointmentConfirm}
                onAppointmentCancel={handleAppointmentCancel}
                onAppointmentDelete={handleAppointmentDelete}
                onAppointmentCheckIn={handleAppointmentCheckIn}
                onAppointmentNoShow={handleAppointmentNoShow}
                onAppointmentComplete={handleAppointmentComplete}
                onAppointmentStart={handleStartConsultation}
                onAppointmentRevertCompleted={handleAppointmentRevertCompleted}
                onAppointmentRevertNoShow={handleAppointmentRevertNoShow}
                onViewPreCheckin={handleViewPreCheckin}
                onViewConsultation={handleViewConsultation}
              />
            </>
          )}

          {viewMode === 'month' && (
            <>
              <DoctorHeader
                doctorInfo={doctorInfo}
                stats={getDoctorStats()}
                isSelectable={false}
                className="mb-4"
              />
              <CalendarMonthView
                appointments={appointments}
                currentDate={currentDate}
                loading={loading}
                userRole="doctor"
                onDateClick={(date) => {
                  setCurrentDate(date);
                  setViewMode('day');
                }}
                onAppointmentClick={(appointment) => {
                  router.push(`/dashboard/doctor/agenda/appointment/${appointment.id}`);
                }}
                onAppointmentEdit={handleAppointmentEdit}
                onAppointmentConfirm={handleAppointmentConfirm}
                onAppointmentCancel={handleAppointmentCancel}
                onAppointmentDelete={handleAppointmentDelete}
                onAppointmentCheckIn={handleAppointmentCheckIn}
                onAppointmentNoShow={handleAppointmentNoShow}
                onAppointmentComplete={handleAppointmentComplete}
                onAppointmentStart={handleStartConsultation}
                onAppointmentRevertCompleted={handleAppointmentRevertCompleted}
                onAppointmentRevertNoShow={handleAppointmentRevertNoShow}
                onViewConsultation={handleViewConsultation}
              />
            </>
          )}
        </div>
      </div>

      {/* Modal para crear expediente desde cita */}
      <CreateRecordFromAppointmentModal
        appointment={selectedAppointment}
        isOpen={showCreateRecordModal}
        onClose={() => {
          setShowCreateRecordModal(false);
          setSelectedAppointment(null);
        }}
        doctorConsultory={doctorConsultory}
      />

      {/* Modal de confirmación de eliminación */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => {
          if (!deleteDialog.loading) {
            setDeleteDialog({ open, appointment: null, loading: false });
          }
        }}
        itemToDelete={deleteDialog.appointment}
        onDelete={handleDelete}
        isDeleting={deleteDialog.loading}
        showPhysicalDelete={true}
      />

      {/* Modal de confirmación para completar cita */}
      <Dialog open={showCompleteConfirmDialog} onOpenChange={setShowCompleteConfirmDialog}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
              Confirmar Completar Cita
            </DialogTitle>
            <DialogDescription>
              ¿Está seguro de que desea marcar esta cita como completada?
            </DialogDescription>
          </DialogHeader>
          
          {appointmentToComplete && (
            <div className="py-4">
              <div className="p-4 bg-gray-50 rounded-lg space-y-2">
                <p className="text-sm font-medium text-gray-900">
                  Paciente: {appointmentToComplete.patientFirstName} {appointmentToComplete.patientLastName}
                </p>
                <p className="text-sm text-gray-600">
                  Fecha: {formatLocalDateFromUTC(appointmentToComplete.scheduledDate || appointmentToComplete.startTime, 'dd/MM/yyyy')}
                </p>
                <p className="text-sm text-gray-600">
                  Hora: {formatLocalTimeFromUTC(appointmentToComplete.startTime)} - {formatLocalTimeFromUTC(appointmentToComplete.endTime)}
                </p>
              </div>
              
              <div className="mt-4 p-4 bg-yellow-50 rounded-lg">
                <div className="flex items-start gap-2">
                  <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                  <div className="text-sm text-yellow-800">
                    <p className="font-medium mb-1">Importante:</p>
                    <ul className="list-disc list-inside space-y-1">
                      <li>Esta acción marcará la cita como completada</li>
                      <li>Si hay una consulta médica asociada, asegúrese de haberla finalizado primero</li>
                      <li>Puede revertir esta acción más tarde si es necesario</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setShowCompleteConfirmDialog(false);
              setAppointmentToComplete(null);
            }}>
              Cancelar
            </Button>
            <Button onClick={confirmCompleteAppointment} className="bg-green-600 hover:bg-green-700">
              <CheckCircle className="h-4 w-4 mr-2" />
              Sí, Completar Cita
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Pre-checkin */}
      <PreCheckinQuickView
        isOpen={showPreCheckinModal}
        onClose={() => setShowPreCheckinModal(false)}
        appointmentId={selectedPreCheckinAppointment?.id}
        patientName={`${selectedPreCheckinAppointment?.patientFirstName || ''} ${selectedPreCheckinAppointment?.patientLastName || ''}`}
      />
    </div>
  );
}

// Modal para crear expediente desde cita completada
function CreateRecordFromAppointmentModal({ 
  appointment, 
  isOpen, 
  onClose, 
  doctorConsultory 
}: any) {
  const router = useRouter();
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [includeVitalSigns, setIncludeVitalSigns] = useState(true);
  const [requestHistory, setRequestHistory] = useState(true);
  const [vitalSigns, setVitalSigns] = useState({
    weight: '',
    height: '',
    temperature: '',
    heartRate: '',
    bloodPressure: '',
    respiratoryRate: '',
    oxygenSaturation: ''
  });

  const handleCreateRecord = async () => {
    if (!appointment || !user?.id || !doctorConsultory?.id) {
      toast.error('Faltan datos necesarios para crear el expediente');
      return;
    }

    setLoading(true);
    try {
      // Crear expediente
      const recordData = {
        patientId: appointment.patientId,
        consultoryId: doctorConsultory.id,
        primaryDoctorId: user.id,
        patientSummary: {
          fullName: appointment.patient?.firstName 
            ? `${appointment.patient.firstName} ${appointment.patient.lastName}` 
            : appointment.patientName || 'Paciente',
          age: appointment.patient?.age || 0,
          gender: appointment.patient?.gender || 'No especificado',
          bloodType: appointment.patient?.bloodType || null,
          allergies: []
        },
        isMinor: (appointment.patient?.age || 0) < 18,
        guardianInfo: appointment.patient?.guardianInfo || null,
        status: 'active'
      };

      const recordResponse = await fetch('/api/medical-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(recordData)
      });

      const recordResult = await recordResponse.json();

      if (!recordResponse.ok) {
        throw new Error(recordResult.message || 'Error al crear expediente');
      }

      // Si incluir signos vitales, crear primera consulta
      if (includeVitalSigns) {
        const consultationData = {
          appointmentId: appointment.id,
          reason: appointment.reason || 'Consulta médica',
          vitalSigns: Object.entries(vitalSigns).reduce((acc, [key, value]) => {
            if (value.trim()) {
              acc[key] = parseFloat(value) || value;
            }
            return acc;
          }, {}),
          physicalExam: {},
          diagnoses: [],
          treatment: {
            medications: [],
            instructions: '',
            followUp: ''
          },
          status: 'draft'
        };

        await fetch(`/api/medical-records/${recordResult.data.id}/consultations`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(consultationData)
        });
      }

      toast.success('Expediente creado exitosamente');
      
      // Redirigir al expediente recién creado
      const targetTab = includeVitalSigns ? 'consultations' : (requestHistory ? 'history' : 'general');
      router.push(`/dashboard/doctor/expedientes/${recordResult.data.id}?tab=${targetTab}&from=appointment`);
      
      onClose();
    } catch (error) {
      console.error('Error:', error);
      toast.error(error.message || 'Error al crear expediente');
    } finally {
      setLoading(false);
    }
  };

  if (!appointment) return null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-blue-600" />
            Crear Expediente Clínico
          </DialogTitle>
          <DialogDescription>
            Crear nuevo expediente para <strong>{appointment.patientName}</strong> desde la cita completada.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6">
          {/* Información de la cita */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-semibold text-blue-900 mb-2">Información de la Cita</h4>
            <div className="text-sm text-blue-800">
              <p><strong>Paciente:</strong> {appointment.patientName}</p>
              <p><strong>Motivo:</strong> {appointment.reason || 'Consulta médica'}</p>
              <p><strong>Fecha:</strong> {formatDateTime(appointment.scheduledDate)}</p>
            </div>
          </div>

          {/* Opciones */}
          <div className="space-y-4">
            <div className="flex items-start space-x-3">
              <Checkbox 
                id="include-vitals" 
                checked={includeVitalSigns}
                onCheckedChange={setIncludeVitalSigns}
              />
              <div className="space-y-1">
                <Label htmlFor="include-vitals" className="text-sm font-medium">
                  Incluir signos vitales de la consulta
                </Label>
                <p className="text-xs text-gray-500">
                  Se creará automáticamente la primera consulta con los signos vitales.
                </p>
              </div>
            </div>

            {includeVitalSigns && (
              <div className="ml-6 space-y-3 border-l-2 border-blue-200 pl-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs text-gray-600">Peso (kg)</Label>
                    <Input 
                      placeholder="70.5"
                      value={vitalSigns.weight}
                      onChange={(e) => setVitalSigns({...vitalSigns, weight: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Altura (cm)</Label>
                    <Input 
                      placeholder="165"
                      value={vitalSigns.height}
                      onChange={(e) => setVitalSigns({...vitalSigns, height: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Temperatura (°C)</Label>
                    <Input 
                      placeholder="36.5"
                      value={vitalSigns.temperature}
                      onChange={(e) => setVitalSigns({...vitalSigns, temperature: e.target.value})}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">Frecuencia cardíaca</Label>
                    <Input 
                      placeholder="80"
                      value={vitalSigns.heartRate}
                      onChange={(e) => setVitalSigns({...vitalSigns, heartRate: e.target.value})}
                    />
                  </div>
                </div>
              </div>
            )}

            <div className="flex items-start space-x-3">
              <Checkbox 
                id="request-history" 
                checked={requestHistory}
                onCheckedChange={setRequestHistory}
              />
              <div className="space-y-1">
                <Label htmlFor="request-history" className="text-sm font-medium">
                  Solicitar antecedentes médicos
                </Label>
                <p className="text-xs text-gray-500">
                  Ir directo a la sección de antecedentes para completar el historial.
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-50 p-3 rounded-lg flex items-start gap-2">
            <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium">Importante:</p>
              <p>El expediente se creará inmediatamente. Podrás completar la información médica en las siguientes pantallas.</p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancelar
          </Button>
          <Button onClick={handleCreateRecord} disabled={loading}>
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Creando...
              </>
            ) : (
              <>
                <FileText className="h-4 w-4 mr-2" />
                Crear Expediente
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}