/**
 * SISTEMA UNIFICADO DE EMAILS PARA CITAS
 * Maneja todos los canales: VAPI, Backend, Manual, WhatsApp
 */

import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { sendPatientFlowEmails } from './patient-flows';
import { sendEmail } from './core';
import { generateShortCode } from '../short-codes';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface EmailTriggerContext {
  triggerType: 'creation' | 'vapi_confirmation' | 'manual_creation';
  capturedEmail?: string;
  createdBy?: string;
}

/**
 * Función principal para enviar emails relacionados con citas
 */
export async function sendAppointmentEmailsAsync(
  appointmentId: string, 
  patientId: string | null,
  context: EmailTriggerContext
) {
  console.log(`📧 Iniciando envío de emails para cita ${appointmentId}, trigger: ${context.triggerType}`);
  
  try {
    // 1. Obtener datos completos de la cita
    const appointmentData = await getAppointmentDetails(appointmentId);
    if (!appointmentData) {
      throw new Error('Cita no encontrada');
    }

    // 2. Determinar el paciente (existente o crear desde tempData)
    let finalPatientId = patientId;
    
    if (!finalPatientId && appointmentData.tempPatientData) {
      // Crear paciente desde datos temporales (caso VAPI)
      finalPatientId = await createPatientFromTempData(
        appointmentData.tempPatientData,
        context.capturedEmail
      );
      
      // Actualizar la cita con el patientId real
      await db.update(appointments)
        .set({ 
          patientId: finalPatientId,
          tempPatientData: null, // Limpiar datos temporales
          updatedAt: new Date()
        })
        .where(eq(appointments.id, appointmentId));
        
      console.log(`👤 Paciente creado desde datos temporales: ${finalPatientId}`);
    }

    if (!finalPatientId) {
      throw new Error('No se pudo determinar el paciente para la cita');
    }

    // 3. Enviar emails según el tipo de trigger
    await handleEmailsByTrigger(appointmentId, finalPatientId, appointmentData, context);
    
    console.log(`✅ Emails enviados exitosamente para cita ${appointmentId}`);

  } catch (error) {
    console.error('❌ Error en sendAppointmentEmailsAsync:', error);
    throw error;
  }
}

/**
 * Obtiene detalles completos de la cita
 */
async function getAppointmentDetails(appointmentId: string) {
  const result = await db
    .select({
      id: appointments.id,
      title: appointments.title,
      scheduledDate: appointments.scheduledDate,
      startTime: appointments.startTime,
      endTime: appointments.endTime,
      shortCode: appointments.shortCode,
      tempPatientData: appointments.tempPatientData,
      doctorId: appointments.doctorId,
      doctorFirstName: user.firstName,
      doctorLastName: user.lastName,
      consultoryName: consultories.name,
    })
    .from(appointments)
    .leftJoin(user, eq(appointments.doctorId, user.id))
    .leftJoin(consultories, eq(appointments.consultoryId, consultories.id))
    .where(eq(appointments.id, appointmentId))
    .limit(1);

  return result[0] || null;
}

/**
 * Crea un paciente desde datos temporales (caso VAPI)
 */
async function createPatientFromTempData(tempDataJson: string, email?: string) {
  const tempData = JSON.parse(tempDataJson);
  
  // Separar nombre en firstName y lastName
  const nameParts = tempData.name.trim().split(' ');
  const firstName = nameParts[0];
  const lastName = nameParts.slice(1).join(' ') || 'Sin Apellido';
  
  // Crear usuario con email temporal si no se proporcionó uno
  const patientEmail = email || `patient_${Date.now()}@temp.local`;
  
  const newPatient = {
    id: `patient_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    firstName,
    lastName,
    email: patientEmail,
    phone: tempData.phone || null,
    role: 'patient',
    isActive: true,
    createdBy: 'vapi-system',
    createdAt: new Date(),
    updatedAt: new Date()
  };

  const result = await db.insert(user).values(newPatient).returning();
  return result[0].id;
}

/**
 * Maneja el envío de emails según el tipo de trigger
 */
async function handleEmailsByTrigger(
  appointmentId: string,
  patientId: string,
  appointmentData: any,
  context: EmailTriggerContext
) {
  const delayBetweenEmails = 90000; // 90 segundos entre emails

  switch (context.triggerType) {
    case 'creation':
      // Cita creada desde backend/manual con email desde el inicio
      console.log('📧 Flujo: Creación normal con email');
      
      // Email combinado: Confirmación + Pre-checkin + Cancelar
      await sendCombinedAppointmentEmail(appointmentId, patientId, appointmentData, {
        triggerType: 'creation',
        status: 'confirmed' // Las citas creadas normalmente están confirmadas
      });
      break;

    case 'vapi_confirmation':
      // Cita confirmada via VAPI - enviar confirmación + activación
      console.log('📧 Flujo: Confirmación VAPI');
      
      // Email 1: Activación de cuenta
      await sendActivationEmail(patientId, appointmentData, context.capturedEmail);
      
      // Email 2: Confirmación combinada (con delay)
      setTimeout(async () => {
        await sendCombinedAppointmentEmail(appointmentId, patientId, appointmentData, {
          triggerType: 'vapi_confirmation',
          status: 'confirmed'
        });
      }, delayBetweenEmails);
      
      break;

    case 'manual_creation':
      // Cita creada manualmente por doctor/asistente
      console.log('📧 Flujo: Creación manual');
      
      // Email 1: Activación de cuenta (si no tiene email real)
      await sendActivationEmailIfNeeded(patientId, appointmentData);
      
      // Email 2: Confirmación combinada (con delay)
      setTimeout(async () => {
        await sendCombinedAppointmentEmail(appointmentId, patientId, appointmentData, {
          triggerType: 'manual_creation',
          status: 'confirmed' // Las citas manuales están confirmadas
        });
      }, delayBetweenEmails);
      
      break;

    default:
      console.warn(`⚠️ Trigger type no reconocido: ${context.triggerType}`);
  }
}

/**
 * Envía email combinado con confirmación, pre-checkin y cancelación
 */
async function sendCombinedAppointmentEmail(
  appointmentId: string, 
  patientId: string, 
  appointmentData: any, 
  options: { triggerType: string; status: string }
) {
  console.log('📧 Enviando email combinado de cita');
  
  // Obtener datos del paciente
  const patientResult = await db
    .select({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      dateOfBirth: user.dateOfBirth,
    })
    .from(user)
    .where(eq(user.id, patientId))
    .limit(1);

  if (patientResult.length === 0) {
    throw new Error('Paciente no encontrado');
  }

  const patient = patientResult[0];
  const patientName = `${patient.firstName} ${patient.lastName}`;
  const doctorName = `${appointmentData.doctorFirstName} ${appointmentData.doctorLastName}`;
  
  // Verificar si tiene email real
  const hasRealEmail = patient.email && !patient.email.includes('@temp.local');
  if (!hasRealEmail) {
    console.log('⚠️ Paciente sin email real, no se enviará email combinado');
    return;
  }

  // Determinar si es el mismo día
  const appointmentDate = new Date(appointmentData.startTime);
  const today = new Date();
  const isSameDay = appointmentDate.toDateString() === today.toDateString();

  // Calcular edad del paciente
  let isMinor = false;
  if (patient.dateOfBirth) {
    const birthDate = new Date(patient.dateOfBirth);
    const age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    isMinor = age < 18;
  }

  // Generar enlaces
  const preCheckinLink = `https://doctorabarbara.com/pre-checkin/${appointmentId}`;
  const cancelLink = `https://doctorabarbara.com/appointments/${appointmentId}/cancel`;

  // Formatear fecha y hora
  const formattedDate = format(appointmentDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: es });
  const formattedTime = format(appointmentDate, 'h:mm a');

  try {
    await sendEmail({
      templateName: 'appointment_combined_notification',
      to: patient.email,
      params: {
        patientName,
        doctorName,
        appointmentDate: formattedDate,
        appointmentTime: formattedTime,
        confirmationCode: appointmentData.shortCode,
        consultoryName: appointmentData.consultoryName,
        chiefComplaint: appointmentData.title,
        isSameDay,
        isDependent: isMinor,
        relationship: 'responsable', // TODO: Obtener relación real si es menor
        preCheckinLink,
        cancelLink,
        preCheckinCompleted: false // TODO: Verificar si ya completó pre-checkin
      }
    });

    console.log(`✅ Email combinado enviado a ${patient.email}`);
  } catch (error) {
    console.error('❌ Error enviando email combinado:', error);
    throw error;
  }
}

/**
 * Envía email de activación de cuenta
 */
async function sendActivationEmail(patientId: string, appointmentData: any, email?: string) {
  console.log('📧 Enviando email de activación de cuenta');
  
  // Obtener datos del paciente
  const patientResult = await db
    .select({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
    })
    .from(user)
    .where(eq(user.id, patientId))
    .limit(1);

  if (patientResult.length === 0) {
    throw new Error('Paciente no encontrado para activación');
  }

  const patient = patientResult[0];
  const patientName = `${patient.firstName} ${patient.lastName}`;
  const targetEmail = email || patient.email;

  // Solo enviar si tiene email real o se proporcionó uno
  const hasRealEmail = targetEmail && !targetEmail.includes('@temp.local');
  if (!hasRealEmail) {
    console.log('⚠️ No se puede enviar activación sin email real');
    return;
  }

  const activationLink = `https://doctorabarbara.com/activate/${patientId}`;
  const appointmentDate = format(appointmentData.startTime, "dd 'de' MMMM", { locale: es });

  try {
    await sendEmail({
      templateName: 'patient_created',
      to: targetEmail,
      params: {
        patientName,
        activationLink,
        hasAppointment: true,
        appointmentDate,
        isDependent: false // TODO: Detectar si es dependiente
      }
    });

    console.log(`✅ Email de activación enviado a ${targetEmail}`);
  } catch (error) {
    console.error('❌ Error enviando email de activación:', error);
    throw error;
  }
}

/**
 * Envía email de activación solo si es necesario
 */
async function sendActivationEmailIfNeeded(patientId: string, appointmentData: any) {
  // Obtener datos del paciente
  const patientResult = await db
    .select({
      email: user.email,
    })
    .from(user)
    .where(eq(user.id, patientId))
    .limit(1);

  if (patientResult.length === 0) {
    return;
  }

  const patient = patientResult[0];
  const hasRealEmail = patient.email && !patient.email.includes('@temp.local');
  
  if (!hasRealEmail) {
    console.log('⚠️ Paciente sin email real, no se enviará activación');
    return;
  }

  await sendActivationEmail(patientId, appointmentData);
}