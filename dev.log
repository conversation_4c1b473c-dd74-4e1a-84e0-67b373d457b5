
> apple-pass@0.1.0 dev
> next dev --turbopack

   ▲ Next.js 15.3.1 (Turbopack)
   - Local:        http://localhost:3000
   - Network:      http://**************:3000
   - Environments: .env.local

 ✓ Starting...
 ○ Compiling middleware ...
 ✓ Compiled middleware in 2.9s
 ✓ Ready in 22.6s
 ○ Compiling /dashboard/doctor ...
 ✓ Compiled /dashboard/doctor in 11.3s
 ○ Compiling /_error ...
 ✓ Compiled /_error in 5s
 GET /dashboard/doctor 500 in 327ms
 ○ Compiling /favicon.ico ...
 ✓ Compiled /favicon.ico in 2.3s
 GET /favicon.ico 200 in 3736ms
 GET /dashboard/doctor 500 in 484ms
 GET /dashboard/doctor 500 in 341ms
 GET /dashboard/doctor 500 in 564ms
 GET /favicon.ico 200 in 747ms
 GET /favicon.ico 200 in 881ms
 GET /dashboard/doctor 500 in 313ms
 ✓ Compiled /_not-found/page in 411ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483779 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /.well-known/appspecific/com.chrome.devtools.json 500 in 2373ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483784 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483787 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /_next/internal/helpers.ts 500 in 294ms
 GET /_next/static/runtime.ts 500 in 282ms
 GET /dashboard/doctor 500 in 416ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483798 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /.well-known/appspecific/com.chrome.devtools.json 500 in 654ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483823 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483826 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /_next/internal/helpers.ts 500 in 546ms
 GET /favicon.ico 200 in 1445ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483833 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /_next/static/runtime.ts 500 in 586ms
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483836 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 GET /_next/internal/helpers.ts 500 in 391ms
 GET /_next/static/runtime.ts 500 in 325ms
 GET /dashboard/doctor 500 in 381ms
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483864 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483869 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483874 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483880 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'
}
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483887 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483891 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'
}
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)

Debug info:
- Execution of TaskId { id: 2147483898 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- failed to receive message
- reading packet data
- Connection reset by peer (os error 104)]
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- creating new process
- node process exited before we could connect to it with exit status: 1

Debug info:
- Execution of TaskId { id: 2147483902 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- creating new process
- node process exited before we could connect to it with exit status: 1
  Process output:
  
  Process error output:
  node:internal/modules/cjs/loader:1143
    throw err;
    ^
  
  Error: Cannot find module '/mnt/c/Proyectos/sgc/.next/transform.js'
      at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
      at Module._load (node:internal/modules/cjs/loader:981:27)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
      at node:internal/main/run_main_module:28:49 {
    code: 'MODULE_NOT_FOUND',
  }
  
  Node.js v18.20.8]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'
}
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- creating new process
- node process exited before we could connect to it with exit status: 1

Debug info:
- Execution of TaskId { id: 2147483913 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- creating new process
- node process exited before we could connect to it with exit status: 1
  Process output:
  
  Process error output:
  node:internal/modules/cjs/loader:1143
    throw err;
    ^
  
  Error: Cannot find module '/mnt/c/Proyectos/sgc/.next/transform.js'
      at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
      at Module._load (node:internal/modules/cjs/loader:981:27)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
      at node:internal/main/run_main_module:28:49 {
    code: 'MODULE_NOT_FOUND',
  }
  
  Node.js v18.20.8]
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error [TurbopackInternalError]: Failed to write app endpoint /(dashboard)/dashboard/doctor/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- creating new process
- node process exited before we could connect to it with exit status: 1

Debug info:
- Execution of TaskId { id: 2147483921 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /(dashboard)/dashboard/doctor/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- creating new process
- node process exited before we could connect to it with exit status: 1
  Process output:
  
  Process error output:
  node:internal/modules/cjs/loader:1143
    throw err;
    ^
  
  Error: Cannot find module '/mnt/c/Proyectos/sgc/.next/transform.js'
      at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
      at Module._load (node:internal/modules/cjs/loader:981:27)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
      at node:internal/main/run_main_module:28:49 {
    code: 'MODULE_NOT_FOUND',
  }
  
  Node.js v18.20.8]
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/app/favicon.ico/[__metadata_id__]/route/app-paths-manifest.json'
}
 ⨯ [Error [TurbopackInternalError]: Failed to write app endpoint /_not-found/page

Caused by:
- [project]/app/globals.css [app-client] (css)
- creating new process
- node process exited before we could connect to it with exit status: 1

Debug info:
- Execution of TaskId { id: 2147483932 } transient failed
- Execution of get_written_endpoint_with_issues_operation failed
- Execution of endpoint_write_to_disk failed
- Execution of <AppEndpoint as Endpoint>::output failed
- Failed to write app endpoint /_not-found/page
- Execution of AppEndpoint::output failed
- Execution of AppProject::app_module_graphs failed
- Execution of SingleModuleGraph::new_with_entries_visited_intern failed
- [project]/app/globals.css [app-client] (css)
- Execution of primary_chunkable_referenced_modules failed
- Execution of <CssModuleAsset as Module>::references failed
- Execution of parse_css failed
- Execution of <PostCssTransformedAsset as Asset>::content failed
- Execution of PostCssTransformedAsset::process failed
- creating new process
- node process exited before we could connect to it with exit status: 1
  Process output:
  
  Process error output:
  node:internal/modules/cjs/loader:1143
    throw err;
    ^
  
  Error: Cannot find module '/mnt/c/Proyectos/sgc/.next/transform.js'
      at Module._resolveFilename (node:internal/modules/cjs/loader:1140:15)
      at Module._load (node:internal/modules/cjs/loader:981:27)
      at Function.executeUserEntryPoint [as runMain] (node:internal/modules/run_main:128:12)
      at node:internal/main/run_main_module:28:49 {
    code: 'MODULE_NOT_FOUND',
  }
  
  Node.js v18.20.8]
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
 ⨯ [Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
[Error: ENOENT: no such file or directory, open '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'open',
  path: '/mnt/c/Proyectos/sgc/.next/server/pages/_app/build-manifest.json'
}
