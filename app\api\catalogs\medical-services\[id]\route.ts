import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalServices } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener servicio médico por ID
export async function GET(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const serviceId = params.id;
    
    const service = await db.select().from(medicalServices).where(eq(medicalServices.id, serviceId));
    
    if (service.length === 0) {
      return NextResponse.json({ error: 'Servicio médico no encontrado' }, { status: 404 });
    }

    return NextResponse.json({
      data: service[0],
    });
  } catch (error) {
    console.error('Error fetching medical service:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// DELETE - Eliminar servicio médico
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario tenga permisos de admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'No tienes permisos para eliminar servicios médicos' }, { status: 403 });
    }

    const serviceId = params.id;
    const searchParams = request.nextUrl.searchParams;
    const deleteType = searchParams.get('type') || 'logical';
    
    // Verificar que el servicio existe
    const service = await db.select().from(medicalServices).where(eq(medicalServices.id, serviceId));
    if (service.length === 0) {
      return NextResponse.json({ error: 'Servicio médico no encontrado' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física
      await db.delete(medicalServices).where(eq(medicalServices.id, serviceId));
      return NextResponse.json({ 
        message: 'Servicio médico eliminado permanentemente' 
      });
    } else {
      // Eliminación lógica (desactivar)
      await db.update(medicalServices)
        .set({ 
          isActive: false, 
          updatedAt: new Date() 
        })
        .where(eq(medicalServices.id, serviceId));
      
      return NextResponse.json({ 
        message: 'Servicio médico desactivado exitosamente' 
      });
    }
  } catch (error) {
    console.error('Error deleting medical service:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}