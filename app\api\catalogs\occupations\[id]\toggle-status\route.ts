import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { occupations } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar ocupación
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de ocupaciones.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que la ocupación existe
    const existingOccupation = await db
      .select()
      .from(occupations)
      .where(eq(occupations.id, parseInt(id)))
      .limit(1);

    if (existingOccupation.length === 0) {
      return NextResponse.json({ error: 'Ocupación no encontrada' }, { status: 404 });
    }

    // Cambiar el estado
    const newStatus = !existingOccupation[0].isActive;
    const [updatedOccupation] = await db
      .update(occupations)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(occupations.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedOccupation,
      message: `Ocupación ${newStatus ? 'activada' : 'desactivada'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling occupation status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}