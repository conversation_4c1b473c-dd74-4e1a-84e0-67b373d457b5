import { Toaster } from "@/components/ui/sonner";
import type { Metadata } from "next";
import { ThemeProvider } from "../components/provider";
import { ClerkProvider } from "@clerk/nextjs";
import "./globals.css";
import { Analytics } from "@vercel/analytics/next";
import { LayoutProvider } from "@/components/layout-provider";
import { RegionalConfigProvider } from "@/hooks/use-regional-config";
import { UserContextsProvider } from "@/hooks/use-user-contexts";
import { AuthCleanupProvider } from "@/hooks/use-auth-cleanup";
import DynamicMetadata from "@/components/dynamic-metadata";

export const metadata: Metadata = {
  title: "Mundo Pediatra - Sistema de Gestión Clínica Pediátrica",
  description:
    "Sistema integral de gestión clínica pediátrica. Expedientes médicos, control de citas, seguimiento de crecimiento y desarrollo, y más.",
  openGraph: {
    title: "Mundo Pediatra - Sistema de Gestión Clínica",
    description:
      "Sistema integral de gestión clínica pediátrica. Expedientes médicos, control de citas, seguimiento de crecimiento y desarrollo.",
    url: "https://mundopediatra.com",
    siteName: "Mundo Pediatra",
    images: [
      {
        url: "/logo.png",
        width: 1200,
        height: 630,
        alt: "Mundo Pediatra",
      },
    ],
    locale: "es-ES",
    type: "website",
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider>
      <html lang="es" suppressHydrationWarning>
        <body className="antialiased">
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            forcedTheme="light"
            disableTransitionOnChange
          >
            <AuthCleanupProvider>
              <RegionalConfigProvider>
                <UserContextsProvider>
                  <LayoutProvider>
                    <DynamicMetadata />
                    {children}
                  </LayoutProvider>
                </UserContextsProvider>
              </RegionalConfigProvider>
            </AuthCleanupProvider>
            <Toaster />
            <Analytics />
          </ThemeProvider>
        </body>
      </html>
    </ClerkProvider>
  );
}
