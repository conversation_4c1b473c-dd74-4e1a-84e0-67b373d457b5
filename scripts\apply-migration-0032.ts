import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function applyMigration() {
  try {
    console.log('🔄 Aplicando migración 0032: add_medical_history_fields...');
    
    // Leer el archivo de migración
    const migrationPath = path.join(process.cwd(), 'db/migrations/0032_add_medical_history_fields.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Ejecutar la migración
    await db.execute(sql.raw(migrationSQL));
    
    console.log('✅ Migración 0032 aplicada exitosamente');
    console.log('📋 Campos selectedSymptoms, pathologicalHistory y nonPathologicalHistory agregados a tabla medical_records');
    
    // Verificar que las columnas existen
    const columnsExist = await db.execute(sql`
      SELECT column_name
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'medical_records'
      AND column_name IN ('selectedSymptoms', 'pathologicalHistory', 'nonPathologicalHistory')
      ORDER BY column_name;
    `);
    
    console.log('🔍 Columnas encontradas:');
    columnsExist.rows.forEach((row: any) => {
      console.log(`  - ${row.column_name} ✅`);
    });
    
    if (columnsExist.rows.length === 3) {
      console.log('🎯 Todas las columnas fueron creadas correctamente');
    } else {
      console.log(`⚠️  Solo se encontraron ${columnsExist.rows.length} de 3 columnas esperadas`);
    }
    
  } catch (error) {
    console.error('❌ Error aplicando migración:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  applyMigration()
    .then(() => {
      console.log('🎉 Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { applyMigration };