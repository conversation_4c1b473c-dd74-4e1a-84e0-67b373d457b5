import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { consultories, user, userRoles } from '../db/schema';
import { nanoid } from 'nanoid';

config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

async function setupInitialData() {
  try {
    console.log('🏥 Creando consultorio por defecto...');
    
    // Crear consultorio por defecto
    const consultoryId = 'default_clinic';
    await db.insert(consultories).values({
      id: consultoryId,
      name: 'Consultorio Médico Principal',
      address: 'Dirección por configurar',
      phone: '+502 0000-0000',
      email: '<EMAIL>',
      active: true,
      businessHours: {
        monday: { open: '08:00', close: '17:00', closed: false },
        tuesday: { open: '08:00', close: '17:00', closed: false },
        wednesday: { open: '08:00', close: '17:00', closed: false },
        thursday: { open: '08:00', close: '17:00', closed: false },
        friday: { open: '08:00', close: '17:00', closed: false },
        saturday: { open: '08:00', close: '12:00', closed: false },
        sunday: { closed: true }
      },
    }).onConflictDoNothing();
    
    console.log('✅ Consultorio creado:', consultoryId);
    
    console.log('👑 Creando usuario administrador...');
    
    // Crear usuario admin (será sincronizado con Clerk cuando se logee)
    const adminUserId = 'admin_ralvarado'; // Se actualizará cuando se logee por primera vez
    const adminEmail = '<EMAIL>';
    
    await db.insert(user).values({
      id: adminUserId,
      email: adminEmail,
      emailVerified: true,
      name: 'Rodrigo Alvarado',
      firstName: 'Rodrigo',
      lastName: 'Alvarado',
      documentType: 'dpi',
      documentNumber: 'ADMIN-001',
      overallStatus: 'active',
    }).onConflictDoNothing();
    
    // Crear rol admin
    await db.insert(userRoles).values({
      id: nanoid(),
      userId: adminUserId,
      role: 'admin',
      status: 'active',
      consultoryId: consultoryId,
      approvedBy: adminUserId,
      approvedAt: new Date(),
    }).onConflictDoNothing();
    
    console.log('✅ Usuario admin creado:', adminUserId);
    console.log('📧 Email del admin:', adminEmail);
    
    console.log('\n🎉 ¡Setup inicial completado!');
    console.log('📝 Próximos pasos:');
    console.log(`  1. Inicia sesión en tu app con: ${adminEmail}`);
    console.log('  2. Al hacer login, Clerk sincronizará automáticamente el usuario');
    console.log('  3. El usuario admin ya tiene permisos completos');
    console.log('  4. Puedes probar el onboarding con otros usuarios');
    
  } catch (error) {
    console.error('❌ Error durante setup inicial:', error);
    throw error;
  }
}

setupInitialData()
  .then(() => {
    console.log('🏁 Setup completado exitosamente');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Error fatal:', error);
    process.exit(1);
  }); 