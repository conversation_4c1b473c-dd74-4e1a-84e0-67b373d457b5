'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, AlertCircle, User, Stethoscope, UserCheck, Heart, Shield, Building, Upload } from 'lucide-react';
import { UserRole, OnboardingFormData, GeneralProfileData } from '@/lib/types/onboarding';
import { toast } from 'sonner';
import { uploadDocuments, DOCUMENT_PRESETS, getDocumentUrls } from '@/lib/upload-helpers';

// Importar formularios específicos
import { UnifiedPatientForm } from '@/components/forms/unified-patient-form';
import { GeneralInfoForm } from './general-info-form';
import dynamic from 'next/dynamic';

// Lazy load de formularios específicos para evitar llamadas API prematuras
const DoctorSpecificForm = dynamic(() => import('./doctor-specific-form').then(mod => ({ default: mod.DoctorSpecificForm })), {
  ssr: false,
  loading: () => <div className="text-center py-8">Cargando formulario...</div>
});

const AssistantSpecificForm = dynamic(() => import('./assistant-specific-form').then(mod => ({ default: mod.AssistantSpecificForm })), {
  ssr: false,
  loading: () => <div className="text-center py-8">Cargando formulario...</div>
});

// PatientSpecificForm removido - pacientes solo usan información general

const GuardianSpecificForm = dynamic(() => import('./guardian-specific-form').then(mod => ({ default: mod.GuardianSpecificForm })), {
  ssr: false,
  loading: () => <div className="text-center py-8">Cargando formulario...</div>
});

const ProviderSpecificForm = dynamic(() => import('./provider-specific-form').then(mod => ({ default: mod.ProviderSpecificForm })), {
  ssr: false,
  loading: () => <div className="text-center py-8">Cargando formulario...</div>
});

interface OnboardingFlowProps {
  selectedRole: UserRole;
  onComplete: (data: OnboardingFormData) => void;
  onBackToSelection?: () => void;
}

const ROLE_CONFIG = {
  [UserRole.DOCTOR]: {
    title: 'Doctor',
    icon: Stethoscope,
    color: 'text-blue-600',
    bgColor: 'bg-blue-50',
    borderColor: 'border-blue-200',
    steps: 2
  },
  [UserRole.ASSISTANT]: {
    title: 'Asistente Médico',
    icon: UserCheck,
    color: 'text-purple-600',
    bgColor: 'bg-purple-50',
    borderColor: 'border-purple-200',
    steps: 2
  },
  [UserRole.PATIENT]: {
    title: 'Paciente',
    icon: Heart,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    borderColor: 'border-green-200',
    steps: 1
  },
  [UserRole.GUARDIAN]: {
    title: 'Tutor Legal',
    icon: Shield,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    borderColor: 'border-orange-200',
    steps: 2
  },
  [UserRole.PROVIDER]: {
    title: 'Proveedor',
    icon: Building,
    color: 'text-indigo-600',
    bgColor: 'bg-indigo-50',
    borderColor: 'border-indigo-200',
    steps: 2
  }
};

export function OnboardingFlow({ selectedRole, onComplete, onBackToSelection }: OnboardingFlowProps) {
  const router = useRouter();
  const [currentStep, setCurrentStep] = useState(1);
  const [formData, setFormData] = useState<OnboardingFormData>({
    step: 1,
    role: selectedRole,
    generalData: {},
    specificData: {},
    documents: {},
    completed: false
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const config = ROLE_CONFIG[selectedRole];
  const totalSteps = config.steps;
  const progress = (currentStep / totalSteps) * 100;

  const handleGeneralDataChange = (data: Partial<GeneralProfileData>) => {
    setFormData(prev => ({
      ...prev,
      generalData: { ...prev.generalData, ...data }
    }));
  };

  const handleSpecificDataChange = (data: any) => {
    setFormData(prev => ({
      ...prev,
      specificData: { ...prev.specificData, ...data }
    }));
  };

  const handleNextStep = () => {
    // Para pacientes, ir directo a submit después del paso 1 (sin información médica)
    if (selectedRole === UserRole.PATIENT && currentStep === 1) {
      handleSubmit();
      return;
    }
    
    if (currentStep < totalSteps) {
      setCurrentStep(prev => prev + 1);
      setFormData(prev => ({ ...prev, step: prev.step + 1 }));
    }
  };

  const handlePreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
      setFormData(prev => ({ ...prev, step: prev.step - 1 }));
    }
  };

  const handleSubmit = async () => {
    setLoading(true);
    setError(null);

    try {
      // Step 1: Upload documents if any
      let documentUrls: { [key: string]: string } = {};
      
      if (formData.documents && Object.keys(formData.documents).length > 0) {
        toast.info('Subiendo documentos...', { id: 'upload-progress' });
        
        // Upload documents with their respective presets
        const uploadResults = await uploadDocuments(formData.documents, DOCUMENT_PRESETS);
        
        // Check for upload failures
        const failedUploads = Object.entries(uploadResults).filter(([_, result]) => !result.success);
        if (failedUploads.length > 0) {
          const errors = failedUploads.map(([key, result]) => `${key}: ${result.error}`).join(', ');
          throw new Error(`Error subiendo documentos: ${errors}`);
        }
        
        // Get URLs from successful uploads
        documentUrls = getDocumentUrls(uploadResults);
        toast.success('Documentos subidos exitosamente', { id: 'upload-progress' });
      }

      // Step 2: Submit onboarding data with document URLs
      toast.info('Enviando solicitud...', { id: 'submit-progress' });
      
      const response = await fetch('/api/onboarding/submit', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          role: selectedRole,
          generalData: formData.generalData,
          specificData: {
            ...formData.specificData,
            documentUrls // Include document URLs in specificData
          }
        })
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Solicitud enviada exitosamente', { id: 'submit-progress' });
        const completedData = {
          ...formData,
          completed: true
        };
        onComplete(completedData);
      } else {
        const errorMessage = result.message || 'Error al enviar la solicitud';
        setError(errorMessage);
        toast.error(errorMessage, { id: 'submit-progress' });
      }
    } catch (error) {
      const errorMessage = 'Error de conexión. Intente nuevamente.';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        // Para pacientes usar UnifiedPatientForm, para otros roles usar GeneralInfoForm
        if (selectedRole === UserRole.PATIENT) {
          return (
            <div className="max-w-4xl mx-auto">
              <UnifiedPatientForm
                mode="complete"
                context={{
                  theme: "public",
                  showEmailInvitation: false,
                  simplifiedUI: true
                }}
                onSubmit={async (patientData) => {
                  // Convertir datos del paciente al formato de onboarding
                  const generalData = {
                    firstName: patientData.firstName,
                    lastName: patientData.lastName,
                    dateOfBirth: patientData.dateOfBirth,
                    phone: patientData.phone,
                    email: patientData.email,
                    countryId: patientData.countryId ? parseInt(patientData.countryId) : undefined,
                    departmentId: patientData.departmentId ? parseInt(patientData.departmentId) : undefined,
                    municipalityId: patientData.municipalityId ? parseInt(patientData.municipalityId) : undefined,
                    occupationId: patientData.occupationId ? parseInt(patientData.occupationId) : undefined,
                    emergencyContact: patientData.emergencyContact,
                    emergencyPhone: patientData.emergencyPhone,
                    emergencyRelationshipId: patientData.emergencyRelationshipId ? parseInt(patientData.emergencyRelationshipId) : undefined,
                    address: patientData.address,
                    // Datos de guardián si es menor
                    isMinor: patientData.isMinor,
                    guardianOption: patientData.guardianOption,
                    guardianData: patientData.guardianData
                  };
                  
                  handleGeneralDataChange(generalData);
                  handleNextStep();
                }}
                onCancel={onBackToSelection ? onBackToSelection : () => router.push('/onboarding')}
              />
            </div>
          );
        } else {
          return (
            <GeneralInfoForm
              data={formData.generalData || {}}
              onDataChange={handleGeneralDataChange}
              onNext={handleNextStep}
              onBack={onBackToSelection ? onBackToSelection : () => router.push('/onboarding')}
            />
          );
        }

      case 2:
        switch (selectedRole) {
          case UserRole.DOCTOR:
            return (
              <DoctorSpecificForm
                data={formData.specificData}
                onChange={handleSpecificDataChange}
                onNext={handleSubmit}
                onBack={handlePreviousStep}
              />
            );
          case UserRole.ASSISTANT:
            return (
              <AssistantSpecificForm
                data={formData.specificData}
                onChange={handleSpecificDataChange}
                onNext={handleSubmit}
                onBack={handlePreviousStep}
              />
            );
          case UserRole.PATIENT:
            // Los pacientes no tienen paso 2 - van directo a submit después del paso 1
            return null;
          case UserRole.GUARDIAN:
            return (
              <GuardianSpecificForm
                data={formData.specificData}
                onChange={handleSpecificDataChange}
                onNext={handleSubmit}
                onBack={handlePreviousStep}
              />
            );
          case UserRole.PROVIDER:
            return (
              <ProviderSpecificForm
                data={formData.specificData}
                onChange={handleSpecificDataChange}
                onNext={handleSubmit}
                onBack={handlePreviousStep}
              />
            );
          default:
            return null;
        }

      default:
        return null;
    }
  };

  const getStepTitle = (step: number) => {
    switch (step) {
      case 1:
        return 'Información General';
      case 2:
        return `Información de ${config.title}`;
      default:
        return `Paso ${step}`;
    }
  };

  const getStepIcon = (step: number) => {
    switch (step) {
      case 1:
        return User;
      case 2:
        return config.icon;
      default:
        return User;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center space-x-2 mb-4">
            <config.icon className={`h-8 w-8 ${config.color}`} />
            <h1 className="text-3xl font-bold text-gray-900">
              Registro como {config.title}
            </h1>
          </div>
          <p className="text-gray-600">
            Complete los siguientes pasos para enviar su solicitud de registro
          </p>
        </div>

        {/* Progress */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">
              Paso {currentStep} de {totalSteps}
            </span>
            <span className="text-sm text-gray-500">
              {Math.round(progress)}% completado
            </span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>

        {/* Steps indicator */}
        <div className="max-w-2xl mx-auto mb-8">
          <div className="flex items-center justify-center space-x-8">
            {Array.from({ length: totalSteps }).map((_, index) => {
              const step = index + 1;
              const StepIcon = getStepIcon(step);
              const isCompleted = step < currentStep;
              const isCurrent = step === currentStep;
              
              return (
                <div key={step} className="flex items-center space-x-2">
                  <div
                    className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                      isCompleted
                        ? 'bg-green-100 border-green-500 text-green-600'
                        : isCurrent
                        ? `${config.bgColor} ${config.borderColor} ${config.color}`
                        : 'bg-gray-100 border-gray-300 text-gray-400'
                    }`}
                  >
                    {isCompleted ? (
                      <CheckCircle className="h-5 w-5" />
                    ) : (
                      <StepIcon className="h-5 w-5" />
                    )}
                  </div>
                  <div className="text-sm">
                    <div className={`font-medium ${isCurrent ? config.color : 'text-gray-500'}`}>
                      {getStepTitle(step)}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Error message */}
        {error && (
          <div className="max-w-2xl mx-auto mb-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
                <div className="text-red-800">
                  <div className="font-medium">Error</div>
                  <div className="text-sm">{error}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Content */}
        <div className="relative">
          {loading && (
            <div className="absolute inset-0 bg-white/80 backdrop-blur-sm z-10 flex items-center justify-center">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Enviando solicitud...</p>
              </div>
            </div>
          )}
          
          {renderStepContent()}
        </div>

        {/* Footer info */}
        <div className="max-w-2xl mx-auto mt-8">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="pt-6">
              <div className="flex items-start space-x-3">
                <Clock className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
                <div className="text-sm text-blue-800">
                  <div className="font-medium mb-1">Proceso de Revisión</div>
                  <div>
                    Una vez enviada su solicitud, será revisada por nuestro equipo administrativo. 
                    Recibirá una notificación por email sobre el estado de su solicitud en un plazo de 
                    2-3 días hábiles.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 