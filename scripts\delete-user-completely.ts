import { db } from '../db/drizzle';
import { user, userRoles, registrationRequests, guardianPatientRelations, appointments } from '../db/schema';
import { eq, or } from 'drizzle-orm';

const EMAIL = '<EMAIL>';

async function deleteUserCompletely() {
  console.log(`🗑️ Eliminando completamente usuario: ${EMAIL}`);
  
  try {
    // 1. Buscar usuario en base de datos
    const userInfo = await db
      .select()
      .from(user)
      .where(eq(user.email, EMAIL))
      .limit(1);
    
    if (!userInfo.length) {
      console.log('❌ Usuario no encontrado en base de datos');
      return;
    }
    
    const dbUser = userInfo[0];
    console.log('👤 Usuario encontrado:', { id: dbUser.id, email: dbUser.email, status: dbUser.overallStatus });
    
    // 2. Eliminar citas como paciente o como doctor
    const userAppointments = await db
      .select()
      .from(appointments)
      .where(
        or(
          eq(appointments.patientId, dbUser.id),
          eq(appointments.doctorId, dbUser.id)
        )
      );
    
    if (userAppointments.length > 0) {
      console.log(`🗑️ Eliminando ${userAppointments.length} citas...`);
      await db.delete(appointments).where(
        or(
          eq(appointments.patientId, dbUser.id),
          eq(appointments.doctorId, dbUser.id)
        )
      );
    }
    
    // 3. Eliminar relaciones de guardián
    const guardianRelations = await db
      .select()
      .from(guardianPatientRelations)
      .where(
        or(
          eq(guardianPatientRelations.guardianId, dbUser.id),
          eq(guardianPatientRelations.patientId, dbUser.id)
        )
      );
    
    if (guardianRelations.length > 0) {
      console.log(`🗑️ Eliminando ${guardianRelations.length} relaciones de guardián...`);
      await db.delete(guardianPatientRelations).where(
        or(
          eq(guardianPatientRelations.guardianId, dbUser.id),
          eq(guardianPatientRelations.patientId, dbUser.id)
        )
      );
    }
    
    // 4. Eliminar solicitudes de registro
    const existingRequests = await db
      .select()
      .from(registrationRequests)
      .where(eq(registrationRequests.userId, dbUser.id));
    
    if (existingRequests.length > 0) {
      console.log(`🗑️ Eliminando ${existingRequests.length} solicitudes de registro...`);
      await db.delete(registrationRequests).where(eq(registrationRequests.userId, dbUser.id));
    }
    
    // 5. Eliminar roles (esto debería eliminar automáticamente por CASCADE, pero lo hacemos explícito)
    const existingRoles = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, dbUser.id));
    
    if (existingRoles.length > 0) {
      console.log(`🗑️ Eliminando ${existingRoles.length} roles...`);
      await db.delete(userRoles).where(eq(userRoles.userId, dbUser.id));
    }
    
    // 6. Finalmente eliminar el usuario
    console.log('🗑️ Eliminando usuario...');
    await db.delete(user).where(eq(user.id, dbUser.id));
    
    console.log('✅ Usuario eliminado completamente de la base de datos');
    console.log('📝 Ahora puedes crear una cuenta nueva desde cero');
    console.log('🔗 Ve a la página principal y haz "Sign Up" con tu email');
    
  } catch (error) {
    console.error('❌ Error eliminando usuario:', error);
  }
}

deleteUserCompletely();