import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices, medicalServices } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener precios de servicios del doctor actual con información relacionada
    const servicePrices = await db
      .select({
        id: doctorServicePrices.id,
        price: doctorServicePrices.customPrice,
        currency: doctorServicePrices.currency,
        isActive: doctorServicePrices.isActive,
        createdAt: doctorServicePrices.createdAt,
        updatedAt: doctorServicePrices.updatedAt,
        serviceName: medicalServices.name,
        serviceId: doctorServicePrices.serviceId,
        notes: doctorServicePrices.notes,
        effectiveFrom: doctorServicePrices.effectiveFrom,
        effectiveUntil: doctorServicePrices.effectiveUntil
      })
      .from(doctorServicePrices)
      .leftJoin(medicalServices, eq(doctorServicePrices.serviceId, medicalServices.id))
      .where(eq(doctorServicePrices.doctorId, userId))
      .orderBy(doctorServicePrices.createdAt);

    return NextResponse.json({
      success: true,
      data: servicePrices
    });
  } catch (error) {
    console.error('Error al obtener precios de servicios:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { serviceId, price, currency, isActive, notes } = body;

    // Validaciones
    if (!serviceId || !price) {
      return NextResponse.json(
        { error: 'El servicio y precio son requeridos' },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      return NextResponse.json(
        { error: 'El precio debe ser un número mayor a 0' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un precio activo para este servicio del doctor
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(
        and(
          eq(doctorServicePrices.doctorId, userId),
          eq(doctorServicePrices.serviceId, serviceId),
          eq(doctorServicePrices.isActive, true)
        )
      )
      .limit(1);

    if (existingPrice.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un precio activo para este servicio' },
        { status: 400 }
      );
    }

    // Crear el precio del servicio
    const newServicePrice = await db
      .insert(doctorServicePrices)
      .values({
        id: crypto.randomUUID(),
        doctorId: userId,
        serviceId,
        customPrice: parseFloat(price),
        currency: currency || 'GTQ',
        isActive: isActive ?? true,
        notes: notes?.trim() || null,
        effectiveFrom: new Date(),
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Precio de servicio creado exitosamente',
      data: newServicePrice[0]
    });
  } catch (error) {
    console.error('Error al crear precio de servicio:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}