import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { serviceTags } from '@/db/schema';
import { eq, asc, desc, ilike, or, and, count } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET - Listar tags de servicios
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const onlyActive = searchParams.get('active') !== 'false';

    const offset = (page - 1) * limit;

    // Construir query base
    let query = db.select().from(serviceTags);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(serviceTags.name, `%${search}%`),
          ilike(serviceTags.description, `%${search}%`)
        )
      );
    }

    if (category && category !== 'all') {
      conditions.push(eq(serviceTags.category, category));
    }

    if (onlyActive) {
      conditions.push(eq(serviceTags.isActive, true));
    }

    // Aplicar condiciones si existen
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    query = query.orderBy(asc(serviceTags.category), asc(serviceTags.name));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query
    const result = await query;

    // Obtener conteo total
    let countQuery = db.select({ count: count() }).from(serviceTags);
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Obtener categorías únicas
    const categoriesResult = await db.select({ category: serviceTags.category })
      .from(serviceTags)
      .where(eq(serviceTags.isActive, true))
      .groupBy(serviceTags.category);
    
    const categories = categoriesResult.map(r => r.category).filter(Boolean);

    return NextResponse.json({
      data: result,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      categories,
    });

  } catch (error) {
    console.error('Error fetching service tags:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Crear nuevo tag de servicio
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario tenga permisos de admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'No tienes permisos para crear tags' }, { status: 403 });
    }

    const body = await request.json();
    const { name, description, color, category } = body;

    // Validaciones
    if (!name) {
      return NextResponse.json({ error: 'Nombre es requerido' }, { status: 400 });
    }

    // Verificar que el nombre no exista
    const existingTag = await db.select().from(serviceTags).where(eq(serviceTags.name, name));
    if (existingTag.length > 0) {
      return NextResponse.json({ error: 'Ya existe un tag con ese nombre' }, { status: 400 });
    }

    // Crear el nuevo tag
    const newTag = {
      id: nanoid(),
      name,
      description: description || null,
      color: color || 'blue',
      category: category || 'general',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const result = await db.insert(serviceTags).values(newTag).returning();

    return NextResponse.json({
      message: 'Tag creado exitosamente',
      data: result[0],
    }, { status: 201 });

  } catch (error) {
    console.error('Error creating service tag:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}