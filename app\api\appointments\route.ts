import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories, activityTypes, medicalServices, guardianPatientRelations } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or, gte, lte, sql, ne } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { format } from 'date-fns';

// Funciones auxiliares simplificadas
const generateShortCode = () => {
  const letters = 'ABCDEFGHJKLMNPQRTUVWXY';
  const numbers = '**********';
  let code = '';
  for (let i = 0; i < 3; i++) {
    code += letters[Math.floor(Math.random() * letters.length)];
  }
  for (let i = 0; i < 3; i++) {
    code += numbers[Math.floor(Math.random() * numbers.length)];
  }
  return code;
};

const createConsultoryDate = (dateStr: string, timeStr: string) => {
  return new Date(`${dateStr}T${timeStr}:00.000Z`);
};

const formatInConsultoryTimezone = (date: Date | string, formatStr: string) => {
  // Importar la función real de timezone-utils
  const { formatInTimeZone } = require('date-fns-tz');
  const { es } = require('date-fns/locale');

  const timezone = 'America/Guatemala';
  const dateObj = typeof date === 'string' ? new Date(date) : date;

  return formatInTimeZone(dateObj, timezone, formatStr, { locale: es });
};

// Funciones de logging temporales
const logAppointmentCreated = async (appointmentId: string, userId: string, appointmentData: any) => {
  console.log('📝 Cita creada:', appointmentId, 'por usuario:', userId);
};

// GET - Listar citas médicas
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const doctorId = searchParams.get('doctorId');
    const patientId = searchParams.get('patientId');
    const consultoryId = searchParams.get('consultoryId');
    const activityTypeId = searchParams.get('activityTypeId');
    const status = searchParams.get('status');
    const dateFrom = searchParams.get('dateFrom');
    const dateTo = searchParams.get('dateTo');
    const today = searchParams.get('today');
    const isEmergency = searchParams.get('isEmergency');
    const type = searchParams.get('type');
    const orderBy = searchParams.get('orderBy') || 'scheduledDate';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
    const offset = (page - 1) * limit;

    // Query base con información básica
    let query = db
      .select({
        id: appointments.id,
        title: appointments.title,
        description: appointments.description,
        chiefComplaint: appointments.chiefComplaint,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        duration: appointments.duration,
        status: appointments.status,
        confirmationStatus: appointments.confirmationStatus,
        checkedInAt: appointments.checkedInAt,
        checkedInBy: appointments.checkedInBy,
        startedAt: appointments.startedAt,
        completedAt: appointments.completedAt,
        estimatedPrice: appointments.estimatedPrice,
        finalPrice: appointments.finalPrice,
        currency: appointments.currency,
        paymentStatus: appointments.paymentStatus,
        doctorNotes: appointments.doctorNotes,
        adminNotes: appointments.adminNotes,
        cancellationReason: appointments.cancellationReason,
        isFollowUp: appointments.isFollowUp,
        parentAppointmentId: appointments.parentAppointmentId,
        isEmergency: appointments.isEmergency,
        requiresReminder: appointments.requiresReminder,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinCompletedAt: appointments.preCheckinCompletedAt,
        preCheckinCompletedBy: appointments.preCheckinCompletedBy,
        shortCode: appointments.shortCode,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        consultoryId: appointments.consultoryId,
        serviceId: appointments.serviceId,
        activityTypeId: appointments.activityTypeId,
        activityTypeName: activityTypes.name,
        activityTypeCategory: activityTypes.category,
        activityTypeColor: activityTypes.color,
        activityTypeDuration: activityTypes.duration,
        activityTypeRespectsSchedule: activityTypes.respectsSchedule,
        activityTypeIcon: activityTypes.icon,
        serviceName: medicalServices.name,
        createdAt: appointments.createdAt,
        updatedAt: appointments.updatedAt,
      })
      .from(appointments)
      .leftJoin(activityTypes, eq(appointments.activityTypeId, activityTypes.id))
      .leftJoin(medicalServices, eq(appointments.serviceId, medicalServices.id));

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(appointments.title, `%${search}%`),
          ilike(appointments.description, `%${search}%`),
          ilike(appointments.chiefComplaint, `%${search}%`)
        )
      );
    }

    if (doctorId && doctorId !== 'all') {
      conditions.push(eq(appointments.doctorId, doctorId));
    }

    if (patientId && patientId !== 'all') {
      conditions.push(eq(appointments.patientId, patientId));
    }

    if (consultoryId && consultoryId !== 'all') {
      conditions.push(eq(appointments.consultoryId, consultoryId));
    }

    if (activityTypeId && activityTypeId !== 'all') {
      conditions.push(eq(appointments.activityTypeId, activityTypeId));
    }

    if (status && status !== 'all') {
      conditions.push(eq(appointments.status, status));
    }

    if (dateFrom) {
      const fromDate = createConsultoryDate(dateFrom, '00:00');
      conditions.push(gte(appointments.scheduledDate, fromDate));
    }

    if (dateTo) {
      const toDate = createConsultoryDate(dateTo, '23:59');
      conditions.push(lte(appointments.scheduledDate, toDate));
    }

    if (today === 'true') {
      const todayStr = format(new Date(), 'yyyy-MM-dd');
      const todayStart = createConsultoryDate(todayStr, '00:00');
      const todayEnd = createConsultoryDate(todayStr, '23:59');
      
      conditions.push(
        and(
          gte(appointments.scheduledDate, todayStart),
          lte(appointments.scheduledDate, todayEnd)
        )
      );
    }

    if (isEmergency === 'true') {
      conditions.push(eq(appointments.isEmergency, true));
    } else if (isEmergency === 'false') {
      conditions.push(eq(appointments.isEmergency, false));
    }

    // Filtro especial para citas de dependientes
    if (type === 'dependents') {
      const dependentsData = await db
        .select({
          patientId: guardianPatientRelations.patientId
        })
        .from(guardianPatientRelations)
        .where(eq(guardianPatientRelations.guardianId, userId));
      
      const dependentIds = dependentsData.map(dep => dep.patientId);
      
      if (dependentIds.length > 0) {
        conditions.push(
          or(...dependentIds.map(id => eq(appointments.patientId, id)))
        );
      } else {
        conditions.push(eq(appointments.id, 'no-dependents'));
      }
    }

    // Aplicar condiciones si existen
    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'scheduledDate' ? appointments.scheduledDate :
                       orderBy === 'startTime' ? appointments.startTime :
                       orderBy === 'status' ? appointments.status :
                       orderBy === 'createdAt' ? appointments.createdAt :
                       appointments.scheduledDate;

    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query
    const result = await query;

    // Enriquecer con datos de usuarios (pacientes y doctores)
    const enrichedResults = await Promise.all(
      result.map(async (appointment) => {
        const enrichedAppointment: any = { ...appointment };
        
        // Obtener información del doctor
        if (appointment.doctorId) {
          try {
            const doctorData = await db
              .select({
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
              })
              .from(user)
              .where(eq(user.id, appointment.doctorId))
              .limit(1);
            
            if (doctorData.length > 0) {
              enrichedAppointment.doctorFirstName = doctorData[0].firstName;
              enrichedAppointment.doctorLastName = doctorData[0].lastName;
              enrichedAppointment.doctorEmail = doctorData[0].email;
            }
          } catch (error) {
            console.error('Error fetching doctor data:', error);
          }
        }
        
        // Obtener información del paciente
        if (appointment.patientId) {
          try {
            const patientData = await db
              .select({
                firstName: user.firstName,
                lastName: user.lastName,
                email: user.email,
                phone: user.phone,
              })
              .from(user)
              .where(eq(user.id, appointment.patientId))
              .limit(1);
            
            if (patientData.length > 0) {
              enrichedAppointment.patientFirstName = patientData[0].firstName;
              enrichedAppointment.patientLastName = patientData[0].lastName;
              enrichedAppointment.patientEmail = patientData[0].email;
              enrichedAppointment.patientPhone = patientData[0].phone;
            }
          } catch (error) {
            console.error('Error fetching patient data:', error);
          }
        }
        
        // Obtener información del consultorio
        if (appointment.consultoryId) {
          try {
            const consultoryData = await db
              .select({
                name: consultories.name,
                type: consultories.type,
                floor: consultories.floor,
                building: consultories.building,
              })
              .from(consultories)
              .where(eq(consultories.id, appointment.consultoryId))
              .limit(1);
            
            if (consultoryData.length > 0) {
              enrichedAppointment.consultoryName = consultoryData[0].name;
              enrichedAppointment.consultoryType = consultoryData[0].type;
              enrichedAppointment.consultoryFloor = consultoryData[0].floor;
              enrichedAppointment.consultoryBuilding = consultoryData[0].building;
            }
          } catch (error) {
            console.error('Error fetching consultory data:', error);
          }
        }
        
        return enrichedAppointment;
      })
    );

    // Obtener conteo total
    let countQuery = db.select({ count: count() }).from(appointments);
    if (conditions.length > 0) {
      countQuery = countQuery.where(and(...conditions));
    }
    const totalResult = await countQuery;
    const total = totalResult[0]?.count || 0;

    // Obtener estadísticas adicionales
    const statsQuery = await db
      .select({
        status: appointments.status,
        count: count(),
      })
      .from(appointments)
      .groupBy(appointments.status);

    const stats = {
      total,
      byStatus: statsQuery.reduce((acc, stat) => {
        acc[stat.status] = stat.count;
        return acc;
      }, {} as Record<string, number>),
    };

    return NextResponse.json({
      data: enrichedResults,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
      stats,
    });
  } catch (error) {
    console.error('Error fetching appointments:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Crear nueva cita médica
export async function POST(request: NextRequest) {
  console.log('🚀 POST /api/appointments - Iniciando creación de cita');

  try {
    const { userId, sessionClaims } = await auth();

    console.log('👤 Usuario autenticado:', userId);

    if (!userId) {
      console.log('❌ Usuario no autorizado');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    console.log('📥 Datos recibidos:', JSON.stringify(body, null, 2));
    console.log('🚨 FECHAS RECIBIDAS DEL FRONTEND:', {
      scheduledDate: body.scheduledDate,
      startTime: body.startTime,
      endTime: body.endTime
    });

    const {
      title,
      description,
      doctorId,
      patientId,
      consultoryId,
      serviceId,
      activityTypeId,
      chiefComplaint,
      scheduledDate,
      startTime,
      endTime,
      duration,
      isEmergency,
      requiresReminder,
      estimatedPrice,
      currency,
    } = body;

    // Validaciones básicas
    if (!title || !doctorId || !consultoryId || !activityTypeId || !scheduledDate || !startTime || !endTime || !duration) {
      return NextResponse.json({
        error: 'Campos requeridos: title, doctorId, consultoryId, activityTypeId, scheduledDate, startTime, endTime, duration'
      }, { status: 400 });
    }

    // Validar activityType si se proporciona y verificar patientId
    let activityTypeData = null;
    if (activityTypeId) {
      const activityTypeResult = await db
        .select()
        .from(activityTypes)
        .where(and(eq(activityTypes.id, activityTypeId), eq(activityTypes.isActive, true)));

      if (activityTypeResult.length === 0) {
        return NextResponse.json({ error: 'Tipo de actividad no encontrado o inactivo' }, { status: 400 });
      }

      activityTypeData = activityTypeResult[0];

      // Validar que se proporcione paciente si la actividad lo requiere
      if (activityTypeData.requiresPatient && !patientId) {
        return NextResponse.json({
          error: 'Esta actividad requiere un paciente asignado'
        }, { status: 400 });
      }
    }

    // 🔍 FIX DE FECHAS - PARTE PRINCIPAL
    console.log('🔍 [API] Datos recibidos:', {
      scheduledDate,
      startTime,
      endTime,
      duration
    });

    // Las fechas vienen del frontend como strings de hora local de Guatemala
    // Necesitamos parsearlas como hora local, no como UTC

    // Para startTime y endTime: agregar 'Z' para que se interpreten como UTC
    // pero representan hora local de Guatemala
    const startDateTime = new Date(startTime + (startTime.includes('Z') ? '' : 'Z'));
    const endDateTime = new Date(endTime + (endTime.includes('Z') ? '' : 'Z'));

    // Para scheduledDate, si es solo fecha (YYYY-MM-DD), agregamos mediodía
    const scheduledDateTime = typeof scheduledDate === 'string' && scheduledDate.length === 10
      ? new Date(scheduledDate + 'T12:00:00.000Z')
      : new Date(scheduledDate);

    console.log('🔍 [API] Fechas parseadas CORRECTAMENTE:', {
      startDateTime: startDateTime.toISOString(),
      endDateTime: endDateTime.toISOString(),
      scheduledDateTime: scheduledDateTime.toISOString(),
      horaGuatemala: formatInConsultoryTimezone(startDateTime, 'dd/MM/yyyy HH:mm')
    });

    if (isNaN(startDateTime.getTime()) || isNaN(endDateTime.getTime()) || isNaN(scheduledDateTime.getTime())) {
      return NextResponse.json({ error: 'Fechas inválidas' }, { status: 400 });
    }

    if (endDateTime <= startDateTime) {
      return NextResponse.json({ error: 'La hora de fin debe ser posterior a la hora de inicio' }, { status: 400 });
    }

    // Verificar que el doctor existe y está activo
    const doctorResult = await db
      .select()
      .from(user)
      .where(and(eq(user.id, doctorId), eq(user.overallStatus, 'active')));

    if (doctorResult.length === 0) {
      return NextResponse.json({ error: 'Doctor no encontrado o inactivo' }, { status: 400 });
    }

    // Verificar que el paciente existe (solo si se proporciona)
    if (patientId) {
      const patientResult = await db
        .select()
        .from(user)
        .where(eq(user.id, patientId));

      if (patientResult.length === 0) {
        return NextResponse.json({ error: 'Paciente no encontrado' }, { status: 400 });
      }
    } else if (activityTypeData?.requiresPatient) {
      return NextResponse.json({
        error: 'Esta actividad requiere seleccionar un paciente'
      }, { status: 400 });
    }

    // Verificar que el consultorio existe y está activo
    const consultoryResult = await db
      .select()
      .from(consultories)
      .where(and(eq(consultories.id, consultoryId), eq(consultories.isActive, true)));

    if (consultoryResult.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado o inactivo' }, { status: 400 });
    }

    // Verificar conflictos de horario del doctor
    const conflictingAppointments = await db
      .select()
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          eq(appointments.scheduledDate, scheduledDateTime),
          or(
            and(
              gte(appointments.startTime, startDateTime),
              lte(appointments.startTime, endDateTime)
            ),
            and(
              gte(appointments.endTime, startDateTime),
              lte(appointments.endTime, endDateTime)
            ),
            and(
              lte(appointments.startTime, startDateTime),
              gte(appointments.endTime, endDateTime)
            )
          ),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed'),
            eq(appointments.status, 'in_progress')
          )
        )
      );

    if (conflictingAppointments.length > 0) {
      return NextResponse.json({
        error: 'El doctor ya tiene una cita programada en ese horario'
      }, { status: 400 });
    }

    // Generar código corto único
    const shortCode = generateShortCode();

    // Crear la nueva cita con fechas corregidas
    const newAppointment = {
      id: nanoid(),
      title,
      description: description || null,
      doctorId,
      patientId: patientId || null,
      consultoryId,
      serviceId: serviceId || null,
      activityTypeId: activityTypeId || null,
      chiefComplaint: chiefComplaint || null,
      scheduledDate: scheduledDateTime,
      startTime: startDateTime,  // ✅ Usar fechas corregidas
      endTime: endDateTime,      // ✅ Usar fechas corregidas
      duration: parseInt(duration),
      status: 'confirmed' as const,
      confirmationStatus: 'pending' as const,
      shortCode,
      estimatedPrice: estimatedPrice ? estimatedPrice.toString() : null,
      currency: currency || 'GTQ',
      paymentStatus: 'pending' as const,
      isFollowUp: false,
      isEmergency: isEmergency || false,
      requiresReminder: requiresReminder !== false,
      createdBy: userId,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    console.log('🔍 [API] Cita a guardar en BD (FINAL):', {
      scheduledDate: newAppointment.scheduledDate,
      startTime: newAppointment.startTime,
      endTime: newAppointment.endTime,
      horaGuatemala: formatInConsultoryTimezone(newAppointment.startTime, 'dd/MM/yyyy HH:mm')
    });

    const result = await db.insert(appointments).values(newAppointment).returning();
    const createdAppointment = result[0];

    // 📝 LOG: Cita creada
    await logAppointmentCreated(createdAppointment.id, userId, newAppointment);

    console.log('✅ Cita creada exitosamente con fechas corregidas');

    return NextResponse.json({
      message: 'Cita creada exitosamente',
      data: {
        ...result[0],
        shortCode: createdAppointment.shortCode
      },
      shortCode: createdAppointment.shortCode,
      confirmationMessage: patientId
        ? `Cita creada. Código de confirmación: ${createdAppointment.shortCode}`
        : 'Cita creada exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating appointment:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}
