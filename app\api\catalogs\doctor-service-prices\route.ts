import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices, medicalServices, user, userRoles } from '@/db/schema';
import { eq, and, desc, ilike, count, or } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET /api/catalogs/doctor-service-prices - Listar precios por médico con filtros
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all'; // all, active, inactive
    const doctorId = searchParams.get('doctorId') || ''; // Filtro por médico específico
    const serviceId = searchParams.get('serviceId') || ''; // Filtro por servicio específico
    const sortBy = searchParams.get('sortBy') || 'createdAt';
    const sortOrder = searchParams.get('sortOrder') || 'desc';

    const offset = (page - 1) * limit;

    // Construir condiciones WHERE
    const conditions = [];
    
    if (status === 'active') {
      conditions.push(eq(doctorServicePrices.isActive, true));
    } else if (status === 'inactive') {
      conditions.push(eq(doctorServicePrices.isActive, false));
    }

    if (doctorId) {
      conditions.push(eq(doctorServicePrices.doctorId, doctorId));
    }

    if (serviceId) {
      conditions.push(eq(doctorServicePrices.serviceId, serviceId));
    }

    if (search) {
      // Buscar en nombre del doctor o servicio
      conditions.push(
        or(
          ilike(user.firstName, `%${search}%`),
          ilike(user.lastName, `%${search}%`),
          ilike(medicalServices.name, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Obtener total de registros
    const totalResult = await db
      .select({ count: count() })
      .from(doctorServicePrices)
      .leftJoin(user, eq(doctorServicePrices.doctorId, user.id))
      .leftJoin(medicalServices, eq(doctorServicePrices.serviceId, medicalServices.id))
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Obtener datos con paginación
    const orderColumn = sortBy === 'doctor' ? user.firstName : 
                       sortBy === 'service' ? medicalServices.name :
                       sortBy === 'price' ? doctorServicePrices.customPrice :
                       doctorServicePrices.createdAt;

    const data = await db
      .select({
        id: doctorServicePrices.id,
        doctorId: doctorServicePrices.doctorId,
        serviceId: doctorServicePrices.serviceId,
        customPrice: doctorServicePrices.customPrice,
        currency: doctorServicePrices.currency,
        isActive: doctorServicePrices.isActive,
        effectiveFrom: doctorServicePrices.effectiveFrom,
        effectiveUntil: doctorServicePrices.effectiveUntil,
        notes: doctorServicePrices.notes,
        createdAt: doctorServicePrices.createdAt,
        updatedAt: doctorServicePrices.updatedAt,
        // Datos del doctor
        doctorName: user.firstName,
        doctorLastName: user.lastName,
        doctorEmail: user.email,
        // Datos del servicio
        serviceName: medicalServices.name,
        serviceCategory: medicalServices.category,
        serviceBasePrice: medicalServices.basePrice,
      })
      .from(doctorServicePrices)
      .leftJoin(user, eq(doctorServicePrices.doctorId, user.id))
      .leftJoin(medicalServices, eq(doctorServicePrices.serviceId, medicalServices.id))
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(orderColumn) : orderColumn)
      .limit(limit)
      .offset(offset);

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    });

  } catch (error: any) {
    console.error('Error fetching doctor service prices:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/catalogs/doctor-service-prices - Crear nuevo precio personalizado
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validaciones
    if (!body.doctorId || !body.doctorId.trim()) {
      return NextResponse.json(
        { error: 'El ID del médico es requerido' },
        { status: 400 }
      );
    }

    if (!body.serviceId || !body.serviceId.trim()) {
      return NextResponse.json(
        { error: 'El ID del servicio es requerido' },
        { status: 400 }
      );
    }

    if (!body.customPrice || body.customPrice <= 0) {
      return NextResponse.json(
        { error: 'El precio personalizado debe ser mayor a 0' },
        { status: 400 }
      );
    }

    // Verificar que el doctor existe y tiene rol de doctor
    const doctorExists = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, body.doctorId),
          eq(userRoles.role, 'doctor')
        )
      )
      .limit(1);

    if (!doctorExists.length) {
      return NextResponse.json(
        { error: 'El usuario especificado no es un médico válido' },
        { status: 400 }
      );
    }

    // Verificar que el servicio existe
    const serviceExists = await db
      .select()
      .from(medicalServices)
      .where(eq(medicalServices.id, body.serviceId))
      .limit(1);

    if (!serviceExists.length) {
      return NextResponse.json(
        { error: 'El servicio médico no existe' },
        { status: 400 }
      );
    }

    // Verificar que no existe ya un precio activo para esta combinación
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(
        and(
          eq(doctorServicePrices.doctorId, body.doctorId),
          eq(doctorServicePrices.serviceId, body.serviceId),
          eq(doctorServicePrices.isActive, true)
        )
      )
      .limit(1);

    if (existingPrice.length) {
      return NextResponse.json(
        { error: 'Ya existe un precio activo para este médico y servicio' },
        { status: 400 }
      );
    }

    // Crear nuevo precio personalizado
    const newPrice = {
      id: nanoid(),
      doctorId: body.doctorId,
      serviceId: body.serviceId,
      customPrice: body.customPrice.toString(),
      currency: body.currency || 'GTQ',
      isActive: body.isActive !== undefined ? body.isActive : true,
      effectiveFrom: body.effectiveFrom ? new Date(body.effectiveFrom) : new Date(),
      effectiveUntil: body.effectiveUntil ? new Date(body.effectiveUntil) : null,
      notes: body.notes || null,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await db.insert(doctorServicePrices).values(newPrice);

    return NextResponse.json({
      success: true,
      message: 'Precio personalizado creado exitosamente',
      data: newPrice,
    });

  } catch (error: any) {
    console.error('Error creating doctor service price:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/catalogs/doctor-service-prices - Actualizar precio personalizado
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { error: 'El ID es requerido para actualizar' },
        { status: 400 }
      );
    }

    // Verificar que el precio existe
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(eq(doctorServicePrices.id, body.id))
      .limit(1);

    if (!existingPrice.length) {
      return NextResponse.json(
        { error: 'Precio personalizado no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones
    if (body.customPrice && body.customPrice <= 0) {
      return NextResponse.json(
        { error: 'El precio personalizado debe ser mayor a 0' },
        { status: 400 }
      );
    }

    // Si se está activando, verificar que no hay otro precio activo para la misma combinación
    if (body.isActive === true) {
      const conflictingPrice = await db
        .select()
        .from(doctorServicePrices)
        .where(
          and(
            eq(doctorServicePrices.doctorId, existingPrice[0].doctorId),
            eq(doctorServicePrices.serviceId, existingPrice[0].serviceId),
            eq(doctorServicePrices.isActive, true),
            // Excluir el registro actual
            eq(doctorServicePrices.id, body.id) === false
          )
        )
        .limit(1);

      if (conflictingPrice.length) {
        return NextResponse.json(
          { error: 'Ya existe otro precio activo para este médico y servicio' },
          { status: 400 }
        );
      }
    }

    // Preparar datos para actualizar
    const updateData: any = {
      updatedAt: new Date(),
    };

    if (body.customPrice !== undefined) updateData.customPrice = body.customPrice.toString();
    if (body.currency !== undefined) updateData.currency = body.currency;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;
    if (body.effectiveFrom !== undefined) updateData.effectiveFrom = body.effectiveFrom ? new Date(body.effectiveFrom) : null;
    if (body.effectiveUntil !== undefined) updateData.effectiveUntil = body.effectiveUntil ? new Date(body.effectiveUntil) : null;
    if (body.notes !== undefined) updateData.notes = body.notes;

    // Actualizar precio
    await db
      .update(doctorServicePrices)
      .set(updateData)
      .where(eq(doctorServicePrices.id, body.id));

    return NextResponse.json({
      success: true,
      message: 'Precio personalizado actualizado exitosamente',
    });

  } catch (error: any) {
    console.error('Error updating doctor service price:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}