-- Migration: Add doctor service prices system
-- Date: 2025-01-18
-- Description: Adds the doctor_service_prices table for custom pricing per doctor

CREATE TABLE IF NOT EXISTS "doctor_service_prices" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"serviceId" text NOT NULL,
	"customPrice" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ',
	"isActive" boolean DEFAULT true,
	"effectiveFrom" timestamp DEFAULT now(),
	"effectiveUntil" timestamp,
	"notes" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);

-- Add foreign key constraints
ALTER TABLE "doctor_service_prices" ADD CONSTRAINT "doctor_service_prices_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "user"("id") ON DELETE cascade ON UPDATE no action;
ALTER TABLE "doctor_service_prices" ADD CONSTRAINT "doctor_service_prices_serviceId_medical_services_id_fk" FOREIGN KEY ("serviceId") REFERENCES "medical_services"("id") ON DELETE cascade ON UPDATE no action;

-- Create indexes
CREATE INDEX IF NOT EXISTS "doctor_service_prices_doctor_idx" ON "doctor_service_prices" ("doctorId");
CREATE INDEX IF NOT EXISTS "doctor_service_prices_service_idx" ON "doctor_service_prices" ("serviceId");
CREATE INDEX IF NOT EXISTS "doctor_service_prices_active_idx" ON "doctor_service_prices" ("isActive");
CREATE INDEX IF NOT EXISTS "doctor_service_prices_price_idx" ON "doctor_service_prices" ("customPrice");
CREATE INDEX IF NOT EXISTS "doctor_service_prices_effective_idx" ON "doctor_service_prices" ("effectiveFrom", "effectiveUntil");

-- Create unique constraint for active doctor-service combinations
CREATE UNIQUE INDEX IF NOT EXISTS "doctor_service_active_idx" ON "doctor_service_prices" ("doctorId", "serviceId") WHERE "isActive" = true;