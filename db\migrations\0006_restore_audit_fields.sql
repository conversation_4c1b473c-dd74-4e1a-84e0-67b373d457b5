-- Restaurar campos de auditoría a las tablas de catálogos
-- Estos campos son críticos para seguridad, trazabilidad y cumplimiento

-- 1. COUNTRIES - Agregar campos de auditoría
ALTER TABLE "countries" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "countries" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 2. DEPARTMENTS - Agregar campos de auditoría  
ALTER TABLE "departments" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "departments" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 3. MEDICAL_SPECIALTIES - Agregar campos de auditoría
ALTER TABLE "medical_specialties" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "medical_specialties" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 4. MUNICIPALITIES - Agregar campos de auditoría
ALTER TABLE "municipalities" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "municipalities" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 5. OCCUPATIONS - Agregar campos de auditoría
ALTER TABLE "occupations" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "occupations" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 6. RELATIONSHIPS - Agregar campos de auditoría
ALTER TABLE "relationships" ADD COLUMN "createdAt" timestamp DEFAULT now();
ALTER TABLE "relationships" ADD COLUMN "updatedAt" timestamp DEFAULT now();

-- 7. USER_ROLES ya tiene createdAt, solo agregar updatedAt si no existe
-- Verificamos primero si existe para evitar errores
DO $$ 
BEGIN 
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'user_roles' AND column_name = 'updatedAt') THEN
        ALTER TABLE "user_roles" ADD COLUMN "updatedAt" timestamp DEFAULT now();
    END IF;
END $$;

-- Crear índices para mejorar performance en consultas por fecha
CREATE INDEX IF NOT EXISTS "countries_created_at_idx" ON "countries" ("createdAt");
CREATE INDEX IF NOT EXISTS "countries_updated_at_idx" ON "countries" ("updatedAt");

CREATE INDEX IF NOT EXISTS "departments_created_at_idx" ON "departments" ("createdAt");
CREATE INDEX IF NOT EXISTS "departments_updated_at_idx" ON "departments" ("updatedAt");

CREATE INDEX IF NOT EXISTS "medical_specialties_created_at_idx" ON "medical_specialties" ("createdAt");
CREATE INDEX IF NOT EXISTS "medical_specialties_updated_at_idx" ON "medical_specialties" ("updatedAt");

CREATE INDEX IF NOT EXISTS "municipalities_created_at_idx" ON "municipalities" ("createdAt");
CREATE INDEX IF NOT EXISTS "municipalities_updated_at_idx" ON "municipalities" ("updatedAt");

CREATE INDEX IF NOT EXISTS "occupations_created_at_idx" ON "occupations" ("createdAt");
CREATE INDEX IF NOT EXISTS "occupations_updated_at_idx" ON "occupations" ("updatedAt");

CREATE INDEX IF NOT EXISTS "relationships_created_at_idx" ON "relationships" ("createdAt");
CREATE INDEX IF NOT EXISTS "relationships_updated_at_idx" ON "relationships" ("updatedAt");

CREATE INDEX IF NOT EXISTS "user_roles_updated_at_idx" ON "user_roles" ("updatedAt");