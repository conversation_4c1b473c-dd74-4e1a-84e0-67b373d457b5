-- Update medical_specialties table: replace description with category and add audit fields

-- Add category column (if it doesn't exist)
DO $$ 
BEGIN
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'medical_specialties' AND column_name = 'category') THEN
    ALTER TABLE "medical_specialties" ADD COLUMN "category" text;
  END IF;
END $$;

-- Drop description column (if it exists)
DO $$ 
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.columns 
             WHERE table_name = 'medical_specialties' AND column_name = 'description') THEN
    ALTER TABLE "medical_specialties" DROP COLUMN "description";
  END IF;
END $$;

-- Add audit fields
DO $$ 
BEGIN
  -- Check if createdAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'medical_specialties' AND column_name = 'createdAt') THEN
    ALTER TABLE "medical_specialties" ADD COLUMN "createdAt" timestamp DEFAULT now() NOT NULL;
  END IF;
  
  -- Check if updatedAt column exists
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'medical_specialties' AND column_name = 'updatedAt') THEN
    ALTER TABLE "medical_specialties" ADD COLUMN "updatedAt" timestamp DEFAULT now() NOT NULL;
  END IF;
END $$;