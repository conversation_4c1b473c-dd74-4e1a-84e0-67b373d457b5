import { db } from '@/db/drizzle';
import { activityTypes } from '@/db/schema';

export const activityTypesData = [
  {
    id: 'consultation',
    name: 'Consulta médica',
    category: 'medical',
    color: '#3B82F6',
    duration: 30,
    requiresPatient: true,
    allowsRecurrence: true,
    respectsSchedule: true, // ✅ Respeta horarios configurados
    icon: 'stethoscope',
    order: 1
  },
  {
    id: 'procedure',
    name: 'Procedimiento menor',
    category: 'medical',
    color: '#8B5CF6',
    duration: 45,
    requiresPatient: true,
    allowsRecurrence: false,
    respectsSchedule: true, // ✅ Respeta horarios configurados
    icon: 'activity',
    order: 2
  },
  {
    id: 'surgery',
    name: 'Cirugía',
    category: 'medical',
    color: '#EF4444',
    duration: 120,
    requiresPatient: true,
    allowsRecurrence: false,
    respectsSchedule: false, // 🆓 Horario libre (se programa aparte)
    icon: 'scissors',
    order: 3
  },
  {
    id: 'emergency',
    name: 'Emergencia',
    category: 'medical',
    color: '#DC2626',
    duration: 60,
    requiresPatient: true,
    allowsRecurrence: false,
    respectsSchedule: false, // 🆓 Horario libre (emergencias)
    icon: 'alert-circle',
    order: 4
  },
  {
    id: 'vaccination',
    name: 'Vacunación',
    category: 'medical',
    color: '#10B981',
    duration: 15,
    requiresPatient: true,
    allowsRecurrence: true,
    respectsSchedule: true, // ✅ Respeta horarios configurados
    icon: 'shield',
    order: 5
  },
  {
    id: 'checkup',
    name: 'Control de crecimiento',
    category: 'medical',
    color: '#059669',
    duration: 25,
    requiresPatient: true,
    allowsRecurrence: true,
    respectsSchedule: true, // ✅ Respeta horarios configurados
    icon: 'trending-up',
    order: 6
  },
  {
    id: 'teleconsultation',
    name: 'Teleconsulta',
    category: 'medical',
    color: '#6366F1',
    duration: 20,
    requiresPatient: true,
    allowsRecurrence: true,
    respectsSchedule: true, // ✅ Respeta horarios configurados
    icon: 'video',
    order: 7
  },
  {
    id: 'meeting',
    name: 'Reunión administrativa',
    category: 'administrative',
    color: '#F59E0B',
    duration: 60,
    requiresPatient: false,
    allowsRecurrence: true,
    respectsSchedule: false, // 🆓 Horario flexible
    icon: 'users',
    order: 8
  },
  {
    id: 'training',
    name: 'Capacitación',
    category: 'administrative',
    color: '#F97316',
    duration: 90,
    requiresPatient: false,
    allowsRecurrence: false,
    respectsSchedule: false, // 🆓 Horario flexible
    icon: 'book-open',
    order: 9
  },
  {
    id: 'vacation',
    name: 'Vacaciones',
    category: 'personal',
    color: '#84CC16',
    duration: 480,
    requiresPatient: false,
    allowsRecurrence: false,
    respectsSchedule: false, // 🆓 Bloquea horarios completos
    icon: 'calendar-x',
    order: 10
  }
];

export async function seedActivityTypes() {
  console.log('📅 Seeding activity types...');
  
  try {
    await db.insert(activityTypes).values(activityTypesData).onConflictDoNothing();
    console.log('✅ Activity types seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding activity types:', error);
    throw error;
  }
}