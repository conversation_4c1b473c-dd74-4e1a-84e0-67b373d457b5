'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { CheckCircle, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface RevertConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  appointment: any;
  onConfirm: () => Promise<void>;
  isProcessing?: boolean;
}

export function RevertConfirmationDialog({
  open,
  onOpenChange,
  appointment,
  onConfirm,
  isProcessing = false
}: RevertConfirmationDialogProps) {
  const [reverting, setReverting] = useState(false);

  const handleConfirm = async () => {
    setReverting(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      // Error handled by parent
    } finally {
      setReverting(false);
    }
  };

  const isLoading = reverting || isProcessing;

  return (
    <Dialog open={open} onOpenChange={!isLoading ? onOpenChange : undefined}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Revertir a Confirmada
          </DialogTitle>
          <DialogDescription className="text-base">
            ¿Estás seguro de que quieres revertir esta cita a estado "Confirmada"?
          </DialogDescription>
        </DialogHeader>
        
        {appointment && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="flex items-center gap-2">
              <AlertCircle className="h-4 w-4 text-orange-500" />
              <span className="font-medium text-sm text-orange-700">
                Estado actual: No se presentó
              </span>
            </div>
            
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium text-gray-700">Paciente:</span>
                <span className="ml-2 text-gray-900">
                  {appointment.patientName || appointment.title || 'Sin nombre'}
                </span>
              </div>
              
              {appointment.startTime && (
                <div>
                  <span className="font-medium text-gray-700">Hora programada:</span>
                  <span className="ml-2 text-gray-900">
                    {format(new Date(appointment.startTime), 'HH:mm', { locale: es })}
                  </span>
                </div>
              )}
              
              {appointment.scheduledDate && (
                <div>
                  <span className="font-medium text-gray-700">Fecha:</span>
                  <span className="ml-2 text-gray-900">
                    {format(new Date(appointment.scheduledDate), 'dd/MM/yyyy', { locale: es })}
                  </span>
                </div>
              )}
            </div>
            
            <div className="mt-3 p-3 bg-green-50 rounded border border-green-200">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span className="text-sm font-medium text-green-800">
                  La cita cambiará a estado "Confirmada"
                </span>
              </div>
              <p className="text-xs text-green-700 mt-1">
                Después podrás registrar la llegada del paciente cuando venga.
              </p>
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading}
            className="bg-green-600 hover:bg-green-700 text-white"
          >
            {reverting ? (
              <>
                <CheckCircle className="mr-2 h-4 w-4 animate-spin" />
                Revirtiendo...
              </>
            ) : (
              <>
                <CheckCircle className="mr-2 h-4 w-4" />
                Revertir a Confirmada
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}