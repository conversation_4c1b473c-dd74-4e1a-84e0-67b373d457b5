'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { MapPin, Phone, Mail, Clock, MessageSquare } from "lucide-react";
import { useConsultoryInfo } from "@/hooks/use-consultory-info";

export default function ContactSection() {
  const { consultory, isLoading } = useConsultoryInfo();

  return (
    <section className="py-20 bg-gradient-to-b from-white to-green-50/30">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Contacto
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Estamos aquí para ayudarte. Contáctanos para agendar una cita o resolver tus dudas
          </p>
        </div>

        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2">
          {/* Información de contacto */}
          <div className="space-y-8">
            <div>
              <h3 className="text-xl font-semibold text-foreground mb-6">
                Información de Contacto
              </h3>
              <div className="space-y-6">
                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <MapPin className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">Dirección</h4>
                    <p className="text-muted-foreground">
                      {isLoading ? 'Cargando...' : consultory?.address || 'Av. Principal 123, Zona 10'}<br />
                      Ciudad de Guatemala, Guatemala
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Phone className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">Teléfono</h4>
                    <p className="text-muted-foreground">
                      {isLoading ? 'Cargando...' : consultory?.phone || '+502 2234-5678'}<br />
                      +502 5678-9012 (WhatsApp)
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Mail className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">Email</h4>
                    <p className="text-muted-foreground">
                      {isLoading ? 'Cargando...' : consultory?.email || '<EMAIL>'}<br />
                      citas@{isLoading ? 'cargando.com' : consultory?.email?.split('@')[1] || 'mundopediatra.com'}
                    </p>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="flex-shrink-0 w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                    <Clock className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h4 className="font-medium text-foreground">Horarios</h4>
                    <div className="text-muted-foreground space-y-1">
                      <p>Lunes a Viernes: 8:00 AM - 6:00 PM</p>
                      <p>Sábados: 8:00 AM - 1:00 PM</p>
                      <p>Domingos: Solo emergencias</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <Card className="border-0 bg-primary/5">
              <CardContent className="p-6">
                <div className="flex items-start gap-3">
                  <MessageSquare className="h-5 w-5 text-primary mt-1" />
                  <div>
                    <h4 className="font-medium text-foreground mb-2">
                      ¿Necesitas una consulta urgente?
                    </h4>
                    <p className="text-sm text-muted-foreground mb-3">
                      Para emergencias pediátricas fuera de horario, contáctanos por WhatsApp o llama a nuestro número de emergencias.
                    </p>
                    <Button size="sm" className="bg-green-600 hover:bg-green-700">
                      WhatsApp: +502 5678-9012
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Formulario de contacto */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="text-xl">Envíanos un mensaje</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="firstName">Nombre</Label>
                  <Input id="firstName" placeholder="Tu nombre" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="lastName">Apellido</Label>
                  <Input id="lastName" placeholder="Tu apellido" />
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">Email</Label>
                <Input id="email" type="email" placeholder="<EMAIL>" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Teléfono</Label>
                <Input id="phone" type="tel" placeholder="+502 1234-5678" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="subject">Asunto</Label>
                <Input id="subject" placeholder="¿En qué podemos ayudarte?" />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">Mensaje</Label>
                <Textarea 
                  id="message" 
                  placeholder="Describe tu consulta o solicitud..."
                  className="min-h-[100px]"
                />
              </div>

              <Button className="w-full" size="lg">
                Enviar Mensaje
              </Button>

              <p className="text-xs text-muted-foreground text-center">
                Nos pondremos en contacto contigo dentro de las próximas 24 horas.
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
} 