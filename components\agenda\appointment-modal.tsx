'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
// import { Calendar } from '@/components/ui/calendar';
// import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { cn } from '@/lib/utils';
import { format, setHours, setMinutes, addMinutes } from 'date-fns';
import { es } from 'date-fns/locale';
import { createConsultoryDate, createConsultoryDateString, formatDateInConsultoryTimezone, splitConsultoryDateTime } from '@/lib/timezone-utils';
import { CalendarIcon, Clock, User, Stethoscope, Home, DollarSign, RefreshCw } from 'lucide-react';
import { toast } from 'sonner';

interface AppointmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointment?: any;
  doctorId?: string;
  preselectedConsultoryId?: string;
  onSuccess?: () => void;
}

export function AppointmentModal({
  isOpen,
  onClose,
  appointment,
  doctorId,
  preselectedConsultoryId,
  onSuccess,
}: AppointmentModalProps) {
  const { user } = useUser();
  const [loading, setLoading] = useState(false);
  const [doctors, setDoctors] = useState([]);
  const [patients, setPatients] = useState([]);
  const [consultories, setConsultories] = useState([]);
  const [services, setServices] = useState([]);
  const [activityTypes, setActivityTypes] = useState([]);
  
  // Crear fecha inicial a mediodía para evitar problemas de timezone
  const getInitialDate = () => {
    const now = new Date();
    return new Date(now.getFullYear(), now.getMonth(), now.getDate(), 12, 0, 0);
  };
  
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    doctorId: doctorId || '',
    patientId: '',
    consultoryId: preselectedConsultoryId || '',
    serviceId: '',
    activityTypeId: '',
    scheduledDate: getInitialDate(),
    startTime: '09:00',
    duration: '30',
    estimatedPrice: '',
    currency: 'GTQ',
    chiefComplaint: '',
    isEmergency: false,
  });

  // Cargar datos iniciales
  useEffect(() => {
    if (isOpen) {
      fetchDoctors();
      fetchPatients();
      fetchConsultories();
      fetchServices();
      fetchActivityTypes();
      
      if (appointment) {
        // Cargar datos de la cita existente usando timezone del consultorio
        const { dateString, timeString } = splitConsultoryDateTime(appointment.startTime);
        
        setFormData({
          title: appointment.title || '',
          description: appointment.description || '',
          doctorId: appointment.doctorId || '',
          patientId: appointment.patientId || '',
          consultoryId: appointment.consultoryId || '',
          serviceId: appointment.serviceId || '',
          activityTypeId: appointment.activityTypeId || '',
          scheduledDate: new Date(dateString + 'T12:00:00'),
          startTime: timeString,
          duration: appointment.duration?.toString() || '30',
          estimatedPrice: appointment.estimatedPrice?.toString() || '',
          currency: appointment.currency || 'GTQ',
          chiefComplaint: appointment.chiefComplaint || '',
          isEmergency: appointment.isEmergency || false,
        });
      }
    }
  }, [isOpen, appointment]);

  const fetchDoctors = async () => {
    try {
      const response = await fetch('/api/agenda/doctors');
      const data = await response.json();
      if (response.ok) {
        setDoctors(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar médicos:', error);
    }
  };

  const fetchPatients = async () => {
    try {
      const response = await fetch('/api/catalogs/patients');
      const data = await response.json();
      if (response.ok) {
        setPatients(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar pacientes:', error);
    }
  };

  const fetchConsultories = async () => {
    try {
      const response = await fetch('/api/consultories?isActive=true');
      const data = await response.json();
      if (response.ok) {
        setConsultories(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar consultorios:', error);
    }
  };

  const fetchServices = async () => {
    try {
      const response = await fetch('/api/catalogs/medical-services?isActive=true');
      const data = await response.json();
      if (response.ok) {
        setServices(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar servicios:', error);
    }
  };

  const fetchActivityTypes = async () => {
    try {
      const response = await fetch('/api/catalogs/activity-types?isActive=true');
      const data = await response.json();
      if (response.ok) {
        setActivityTypes(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar tipos de actividad:', error);
    }
  };

  // Manejar cambio de tipo de actividad para auto-rellenar duración
  const handleActivityTypeChange = (activityTypeId: string) => {
    const selectedActivityType = activityTypes.find(at => at.id === activityTypeId);
    
    setFormData(prev => ({
      ...prev,
      activityTypeId,
      duration: selectedActivityType?.duration?.toString() || prev.duration,
      // Limpiar campos de paciente y servicio si no son requeridos
      patientId: selectedActivityType?.requiresPatient ? prev.patientId : '',
      serviceId: (selectedActivityType?.requiresPatient && selectedActivityType?.category === 'medical') ? prev.serviceId : '',
      estimatedPrice: (selectedActivityType?.requiresPatient && selectedActivityType?.category === 'medical') ? prev.estimatedPrice : '',
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validación adicional para tipo de actividad
    if (!formData.activityTypeId) {
      toast.error('Por favor selecciona un tipo de actividad');
      return;
    }
    
    try {
      setLoading(true);
      
      // Debug: Ver qué datos tenemos
      console.log('🔍 [FRONTEND] Datos del formulario:', {
        scheduledDate: formData.scheduledDate,
        scheduledDateType: typeof formData.scheduledDate,
        startTime: formData.startTime,
        duration: formData.duration
      });
      
      // Preparar las fechas y horas sin conversión UTC
      const scheduledDateStr = format(formData.scheduledDate, 'yyyy-MM-dd');
      
      console.log('🔍 [FRONTEND] Fecha formateada:', scheduledDateStr);
      
      // Crear strings ISO manteniendo hora local (sin conversión UTC)
      const startTimeISO = createConsultoryDateString(scheduledDateStr, formData.startTime);
      
      // Calcular hora fin
      const [hours, minutes] = formData.startTime.split(':').map(Number);
      const endHours = Math.floor((minutes + parseInt(formData.duration)) / 60) + hours;
      const endMinutes = (minutes + parseInt(formData.duration)) % 60;
      const endTimeStr = `${String(endHours).padStart(2, '0')}:${String(endMinutes).padStart(2, '0')}`;
      const endTimeISO = createConsultoryDateString(scheduledDateStr, endTimeStr);
      
      console.log('🔍 [FRONTEND] Fechas ISO a enviar:', {
        startTimeISO,
        endTimeISO
      });
      
      const payload = {
        ...formData,
        scheduledDate: scheduledDateStr,
        startTime: startTimeISO,
        endTime: endTimeISO,
        duration: parseInt(formData.duration),
        estimatedPrice: formData.estimatedPrice ? parseFloat(formData.estimatedPrice) : null,
      };
      
      console.log('🔍 [FRONTEND] Payload completo:', payload);
      
      const url = appointment
        ? `/api/appointments/${appointment.id}`
        : '/api/appointments';
      
      const method = appointment ? 'PUT' : 'POST';
      
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
      
      if (response.ok) {
        toast.success(appointment ? 'Cita actualizada correctamente' : 'Cita creada correctamente');
        onSuccess?.();
        onClose();
      } else {
        const error = await response.json();
        toast.error(error.error || 'Error al guardar la cita');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  const timeSlots = [];
  for (let hour = 7; hour <= 22; hour++) { // Extendido hasta 10 PM
    for (let minute = 0; minute < 60; minute += 30) {
      const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
      timeSlots.push(time);
    }
  }

  // Determinar el tipo de actividad seleccionado
  const selectedActivityType = activityTypes.find(at => at.id === formData.activityTypeId);
  const showMedicalService = selectedActivityType?.requiresPatient && selectedActivityType?.category === 'medical';
  const requiresPatient = selectedActivityType?.requiresPatient;
  const showFinancialInfo = selectedActivityType?.requiresPatient && selectedActivityType?.category === 'medical';

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-4xl max-h-[90vh] overflow-y-auto sm:w-full">
        <DialogHeader>
          <DialogTitle>
            {appointment ? 'Editar Cita' : 'Nueva Cita Médica'}
          </DialogTitle>
          <DialogDescription>
            Complete la información necesaria para {appointment ? 'actualizar' : 'programar'} la cita médica
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Información básica */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Información básica
            </h3>
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="title" className="text-sm font-medium">
                  Título de la cita
                </Label>
                <Input
                  id="title"
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Ej: Consulta general, Control mensual"
                  className="h-11"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="activityType" className="text-sm font-medium">
                  Tipo de actividad *
                </Label>
                <Select
                  value={formData.activityTypeId}
                  onValueChange={handleActivityTypeChange}
                  required
                >
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Seleccionar tipo de actividad" />
                  </SelectTrigger>
                  <SelectContent>
                    {activityTypes.map((type: any) => (
                      <SelectItem key={type.id} value={type.id}>
                        <span className="flex items-center gap-2">
                          <span className="text-lg">{type.icon}</span>
                          <span>{type.name}</span>
                          <span className="text-xs text-gray-500">({type.duration} min)</span>
                        </span>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Servicio médico - Solo para actividades médicas que requieren paciente */}
              {showMedicalService ? (
                <div className="space-y-2">
                  <Label htmlFor="service" className="text-sm font-medium">
                    <DollarSign className="h-4 w-4 inline mr-1" />
                    Servicio médico
                  </Label>
                  <Select
                    value={formData.serviceId}
                    onValueChange={(value) => {
                      const service = services.find((s: any) => s.id === value);
                      setFormData({ 
                        ...formData, 
                        serviceId: value,
                        estimatedPrice: service?.basePrice?.toString() || ''
                      });
                    }}
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Seleccionar servicio médico" />
                    </SelectTrigger>
                    <SelectContent>
                      {services.map((service: any) => (
                        <SelectItem key={service.id} value={service.id}>
                          {service.name} - {service.currency} {service.basePrice}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <p className="text-xs text-gray-600">
                    El servicio determina el precio y se trasladará al expediente médico
                  </p>
                </div>
              ) : (
                <div className="p-3 bg-gray-50 border border-gray-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-4 w-4 text-gray-500" />
                    <span className="text-sm text-gray-700 font-medium">
                      No requiere servicio médico
                    </span>
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    Esta actividad no genera facturación ni se registra en expediente médico
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Participantes */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Participantes
            </h3>
            
            <div className="grid grid-cols-1 gap-4">
              <div className="space-y-2">
                <Label htmlFor="doctor" className="text-sm font-medium">
                  <Stethoscope className="h-4 w-4 inline mr-1" />
                  Médico
                </Label>
                <Select
                  value={formData.doctorId}
                  onValueChange={(value) => setFormData({ ...formData, doctorId: value })}
                  required
                >
                  <SelectTrigger className="h-11">
                    <SelectValue placeholder="Seleccionar médico" />
                  </SelectTrigger>
                  <SelectContent>
                    {doctors.map((doctor: any) => (
                      <SelectItem key={doctor.id} value={doctor.id}>
                        Dr. {doctor.firstName} {doctor.lastName}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Campo de paciente solo si la actividad lo requiere */}
              {requiresPatient ? (
                <div className="space-y-2">
                  <Label htmlFor="patient" className="text-sm font-medium">
                    <User className="h-4 w-4 inline mr-1" />
                    Paciente *
                  </Label>
                  <Select
                    value={formData.patientId}
                    onValueChange={(value) => setFormData({ ...formData, patientId: value })}
                    required
                  >
                    <SelectTrigger className="h-11">
                      <SelectValue placeholder="Seleccionar paciente" />
                    </SelectTrigger>
                    <SelectContent>
                      {patients.map((patient: any) => (
                        <SelectItem key={patient.id} value={patient.id}>
                          {patient.firstName} {patient.lastName}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ) : (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-blue-600" />
                    <span className="text-sm text-blue-800 font-medium">
                      Esta actividad no requiere paciente
                    </span>
                  </div>
                  <p className="text-xs text-blue-600 mt-1">
                    {selectedActivityType?.category === 'administrative' 
                      ? 'Actividad administrativa interna del consultorio'
                      : selectedActivityType?.category === 'personal'
                      ? 'Actividad personal (vacaciones, tiempo libre)'
                      : 'Actividad que no involucra atención médica directa'
                    }
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Ubicación */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Ubicación
            </h3>
            <div className="space-y-2">
              <Label htmlFor="consultory" className="text-sm font-medium">
                <Home className="h-4 w-4 inline mr-1" />
                Consultorio
              </Label>
              <Select
                value={formData.consultoryId}
                onValueChange={(value) => setFormData({ ...formData, consultoryId: value })}
                required
              >
                <SelectTrigger className="h-11">
                  <SelectValue placeholder="Seleccionar consultorio" />
                </SelectTrigger>
                <SelectContent>
                  {consultories.map((consultory: any) => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      {consultory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Fecha y hora */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Fecha y hora
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <CalendarIcon className="h-4 w-4 inline mr-1" />
                  Fecha
                </Label>
                <Input
                  type="date"
                  value={formData.scheduledDate ? format(formData.scheduledDate, 'yyyy-MM-dd') : ''}
                  onChange={(e) => {
                    // Crear fecha a mediodía hora local para evitar problemas de timezone
                    const dateValue = e.target.value;
                    if (dateValue) {
                      // Crear fecha con hora local mediodía (12:00) para evitar cambios de día
                      const [year, month, day] = dateValue.split('-').map(Number);
                      const date = new Date(year, month - 1, day, 12, 0, 0);
                      console.log('🔍 [FRONTEND] Fecha seleccionada:', {
                        input: dateValue,
                        dateCreated: date,
                        formatted: format(date, 'yyyy-MM-dd')
                      });
                      setFormData({ ...formData, scheduledDate: date });
                    } else {
                      setFormData({ ...formData, scheduledDate: new Date() });
                    }
                  }}
                  className="w-full"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  <Clock className="h-4 w-4 inline mr-1" />
                  Hora de inicio
                </Label>
                <Select
                  value={formData.startTime}
                  onValueChange={(value) => setFormData({ ...formData, startTime: value })}
                >
                  <SelectTrigger className="h-11">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {timeSlots.map((time) => (
                      <SelectItem key={time} value={time}>
                        {time}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-sm font-medium">
                  Duración
                </Label>
                <Select
                  value={formData.duration}
                  onValueChange={(value) => setFormData({ ...formData, duration: value })}
                >
                  <SelectTrigger className="h-11">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="15">15 minutos</SelectItem>
                    <SelectItem value="30">30 minutos</SelectItem>
                    <SelectItem value="45">45 minutos</SelectItem>
                    <SelectItem value="60">1 hora</SelectItem>
                    <SelectItem value="90">1.5 horas</SelectItem>
                    <SelectItem value="120">2 horas</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Información financiera - Solo para actividades médicas que requieren paciente */}
          {showFinancialInfo && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
                Información financiera
              </h3>
              <div className="space-y-2">
                <Label htmlFor="price" className="text-sm font-medium">
                  <DollarSign className="h-4 w-4 inline mr-1" />
                  Precio estimado
                </Label>
                <div className="flex gap-2">
                  <Select
                    value={formData.currency}
                    onValueChange={(value) => setFormData({ ...formData, currency: value })}
                  >
                    <SelectTrigger className="w-24 h-11">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="GTQ">GTQ</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.estimatedPrice}
                    onChange={(e) => setFormData({ ...formData, estimatedPrice: e.target.value })}
                    placeholder="0.00"
                    className="h-11 flex-1"
                  />
                </div>
                <p className="text-xs text-gray-600">
                  Este es el precio del servicio médico seleccionado
                </p>
              </div>
            </div>
          )}

          {/* Información adicional */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900 border-b pb-2">
              Información adicional
            </h3>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="chiefComplaint" className="text-sm font-medium">
                  Motivo de consulta
                </Label>
                <Textarea
                  id="chiefComplaint"
                  value={formData.chiefComplaint}
                  onChange={(e) => setFormData({ ...formData, chiefComplaint: e.target.value })}
                  placeholder="Breve descripción del motivo de la consulta"
                  rows={2}
                  className="resize-none"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="description" className="text-sm font-medium">
                  Notas adicionales
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  placeholder="Información adicional relevante para la cita"
                  rows={2}
                  className="resize-none"
                />
              </div>
            </div>
          </div>
        </form>

        <DialogFooter className="flex flex-col-reverse sm:flex-row gap-2 sm:gap-0">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            className="w-full sm:w-auto h-11"
          >
            Cancelar
          </Button>
          <Button 
            type="submit" 
            disabled={loading}
            className="w-full sm:w-auto h-11"
            onClick={handleSubmit}
          >
            {loading ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Guardando...
              </>
            ) : (
              <>
                {appointment ? 'Actualizar' : 'Crear'} Cita
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}