import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { user, userRoles, guardianPatientRelations, appointments } from '../db/schema.js';
import { eq, and, desc, isNull } from 'drizzle-orm';

config({ path: '.env.local' });

const sql = postgres(process.env.DATABASE_URL);
const db = drizzle(sql);

async function checkDuplicatePatients() {
  console.log('🔍 Buscando pacientes recientes con posibles duplicados...\n');
  
  try {
    // Buscar pacientes creados en las últimas 24 horas
    const recentDate = new Date();
    recentDate.setDate(recentDate.getDate() - 1);
    
    const recentPatients = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        documentNumber: user.documentNumber,
        documentType: user.documentType,
        createdAt: user.createdAt,
        clerkUserId: user.clerkUserId,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          // Solo pacientes sin clerkUserId (creados desde el formulario)
          isNull(user.clerkUserId)
        )
      )
      .orderBy(desc(user.createdAt))
      .limit(20);

    console.log(`📊 Encontrados ${recentPatients.length} pacientes recientes sin cuenta Clerk:\n`);

    for (const patient of recentPatients) {
      console.log('─'.repeat(60));
      console.log(`👤 ${patient.firstName} ${patient.lastName}`);
      console.log(`   ID: ${patient.id}`);
      console.log(`   Email: ${patient.email}`);
      console.log(`   Documento: ${patient.documentType} - ${patient.documentNumber}`);
      console.log(`   Creado: ${patient.createdAt}`);
      
      // Verificar si tiene relaciones de guardián
      const guardianRelations = await db
        .select({
          guardianId: guardianPatientRelations.guardianId,
          relationship: guardianPatientRelations.relationship,
        })
        .from(guardianPatientRelations)
        .where(eq(guardianPatientRelations.patientId, patient.id));
        
      if (guardianRelations.length > 0) {
        console.log(`   🔗 Relaciones de guardián: ${guardianRelations.length}`);
        for (const rel of guardianRelations) {
          console.log(`      - ${rel.relationship} (Guardian ID: ${rel.guardianId})`);
        }
      }
      
      // Verificar si tiene citas
      const appointmentCount = await db
        .select({ count: appointments.id })
        .from(appointments)
        .where(eq(appointments.patientId, patient.id));
        
      console.log(`   📅 Citas: ${appointmentCount.length}`);
    }
    
    console.log('\n' + '─'.repeat(60));
    console.log('\n💡 Para eliminar un paciente específico, usa:');
    console.log('   node scripts/delete-patient.js <patient-id>\n');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await sql.end();
  }
}

checkDuplicatePatients();