-- Agregar campos de pre-checkin a la tabla appointments
ALTER TABLE appointments 
ADD COLUMN preCheckinSent BOOLEAN DEFAULT FALSE,
ADD COLUMN preCheckinCompleted BOOLEAN DEFAULT FALSE,
ADD COLUMN preCheckinToken TEXT,
ADD COLUMN preCheckinCompletedAt TIMESTAMP,
ADD COLUMN preCheckinCompletedBy TEXT,
ADD COLUMN reminderSent48h BOOLEAN DEFAULT FALSE,
ADD COLUMN reminderSent24h BOOLEAN DEFAULT FALSE,
ADD COLUMN invitationSent BOOLEAN DEFAULT FALSE;

-- Crear índices para optimizar consultas
CREATE INDEX appointments_precheckin_token_idx ON appointments(preCheckinToken);
CREATE INDEX appointments_precheckin_sent_idx ON appointments(preCheckinSent);
CREATE INDEX appointments_reminder_idx ON appointments(reminderSent48h, reminderSent24h);

-- Comentarios para documentar los campos
COMMENT ON COLUMN appointments.preCheckinSent IS 'Si se envió el link de pre-checkin al paciente/guardián';
COMMENT ON COLUMN appointments.preCheckinCompleted IS 'Si el paciente/guardián completó el pre-checkin';
COMMENT ON COLUMN appointments.preCheckinToken IS 'Token único para acceder al pre-checkin';
COMMENT ON COLUMN appointments.preCheckinCompletedAt IS 'Fecha y hora cuando se completó el pre-checkin';
COMMENT ON COLUMN appointments.preCheckinCompletedBy IS 'ID del usuario que completó el pre-checkin (puede ser guardián)';
COMMENT ON COLUMN appointments.reminderSent48h IS 'Si se envió recordatorio 48h antes';
COMMENT ON COLUMN appointments.reminderSent24h IS 'Si se envió recordatorio 24h antes';
COMMENT ON COLUMN appointments.invitationSent IS 'Si se envió invitación de activación de cuenta al crear paciente';