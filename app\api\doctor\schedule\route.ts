import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { doctorSchedules, doctorScheduleExceptions } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { randomBytes } from 'crypto';

function generateId(): string {
  return randomBytes(16).toString('hex');
}

// GET - Obtener horarios del doctor
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const includeExceptions = searchParams.get('includeExceptions') === 'true';

    // Obtener horarios regulares
    const schedules = await db.select()
      .from(doctorSchedules)
      .where(eq(doctorSchedules.doctorId, userId))
      .orderBy(doctorSchedules.dayOfWeek);

    let exceptions = [];
    if (includeExceptions) {
      // Obtener excepciones (últimos 30 días y próximos 90 días)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const ninetyDaysFromNow = new Date();
      ninetyDaysFromNow.setDate(ninetyDaysFromNow.getDate() + 90);

      exceptions = await db.select()
        .from(doctorScheduleExceptions)
        .where(
          and(
            eq(doctorScheduleExceptions.doctorId, userId),
            // TODO: Agregar filtro de fechas cuando se implemente
          )
        )
        .orderBy(doctorScheduleExceptions.exceptionDate);
    }

    return NextResponse.json({
      success: true,
      data: {
        schedules,
        exceptions: includeExceptions ? exceptions : undefined,
      },
    });

  } catch (error) {
    console.error('Error fetching doctor schedule:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear o actualizar horario
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      dayOfWeek,
      startTime,
      endTime,
      lunchBreakStart,
      lunchBreakEnd,
      appointmentDuration,
      maxAppointmentsPerHour,
      allowEmergencies,
      allowOnlineBooking,
      notes,
      consultoryId,
    } = body;

    // Validaciones básicas
    if (typeof dayOfWeek !== 'number' || dayOfWeek < 0 || dayOfWeek > 6) {
      return NextResponse.json(
        { error: 'Día de la semana inválido' },
        { status: 400 }
      );
    }

    if (!startTime || !endTime) {
      return NextResponse.json(
        { error: 'Hora de inicio y fin son requeridas' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un horario para este día
    const existingSchedule = await db.select()
      .from(doctorSchedules)
      .where(
        and(
          eq(doctorSchedules.doctorId, userId),
          eq(doctorSchedules.dayOfWeek, dayOfWeek),
          consultoryId ? eq(doctorSchedules.consultoryId, consultoryId) : undefined
        )
      )
      .limit(1);

    if (existingSchedule.length > 0) {
      // Actualizar horario existente
      const updated = await db.update(doctorSchedules)
        .set({
          startTime,
          endTime,
          lunchBreakStart: lunchBreakStart || null,
          lunchBreakEnd: lunchBreakEnd || null,
          appointmentDuration: appointmentDuration || 30,
          maxAppointmentsPerHour: maxAppointmentsPerHour || 2,
          allowEmergencies: allowEmergencies !== false,
          allowOnlineBooking: allowOnlineBooking !== false,
          notes: notes || null,
          updatedAt: new Date(),
        })
        .where(eq(doctorSchedules.id, existingSchedule[0].id))
        .returning();

      return NextResponse.json({
        success: true,
        data: updated[0],
        message: 'Horario actualizado correctamente',
      });
    } else {
      // Crear nuevo horario
      const newSchedule = await db.insert(doctorSchedules)
        .values({
          id: generateId(),
          doctorId: userId,
          consultoryId: consultoryId || null,
          dayOfWeek,
          startTime,
          endTime,
          lunchBreakStart: lunchBreakStart || null,
          lunchBreakEnd: lunchBreakEnd || null,
          appointmentDuration: appointmentDuration || 30,
          maxAppointmentsPerHour: maxAppointmentsPerHour || 2,
          allowEmergencies: allowEmergencies !== false,
          allowOnlineBooking: allowOnlineBooking !== false,
          notes: notes || null,
          isActive: true,
        })
        .returning();

      return NextResponse.json({
        success: true,
        data: newSchedule[0],
        message: 'Horario creado correctamente',
      });
    }

  } catch (error) {
    console.error('Error saving doctor schedule:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar múltiples horarios
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { schedules } = body;

    if (!Array.isArray(schedules)) {
      return NextResponse.json(
        { error: 'Formato de datos inválido' },
        { status: 400 }
      );
    }

    const results = [];

    // Procesar cada horario
    for (const schedule of schedules) {
      const {
        dayOfWeek,
        startTime,
        endTime,
        lunchBreakStart,
        lunchBreakEnd,
        appointmentDuration,
        maxAppointmentsPerHour,
        allowEmergencies,
        allowOnlineBooking,
        notes,
        consultoryId,
        isActive,
      } = schedule;

      // Verificar si existe
      const existing = await db.select()
        .from(doctorSchedules)
        .where(
          and(
            eq(doctorSchedules.doctorId, userId),
            eq(doctorSchedules.dayOfWeek, dayOfWeek)
          )
        )
        .limit(1);

      if (existing.length > 0) {
        // Actualizar
        const updated = await db.update(doctorSchedules)
          .set({
            startTime,
            endTime,
            lunchBreakStart: lunchBreakStart || null,
            lunchBreakEnd: lunchBreakEnd || null,
            appointmentDuration: appointmentDuration || 30,
            maxAppointmentsPerHour: maxAppointmentsPerHour || 2,
            allowEmergencies: allowEmergencies !== false,
            allowOnlineBooking: allowOnlineBooking !== false,
            notes: notes || null,
            isActive: isActive !== false,
            updatedAt: new Date(),
          })
          .where(eq(doctorSchedules.id, existing[0].id))
          .returning();

        results.push(updated[0]);
      } else if (isActive !== false && startTime && endTime) {
        // Crear solo si está activo y tiene horarios
        const newSchedule = await db.insert(doctorSchedules)
          .values({
            id: generateId(),
            doctorId: userId,
            consultoryId: consultoryId || null,
            dayOfWeek,
            startTime,
            endTime,
            lunchBreakStart: lunchBreakStart || null,
            lunchBreakEnd: lunchBreakEnd || null,
            appointmentDuration: appointmentDuration || 30,
            maxAppointmentsPerHour: maxAppointmentsPerHour || 2,
            allowEmergencies: allowEmergencies !== false,
            allowOnlineBooking: allowOnlineBooking !== false,
            notes: notes || null,
            isActive: true,
          })
          .returning();

        results.push(newSchedule[0]);
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Horarios actualizados correctamente',
    });

  } catch (error) {
    console.error('Error updating doctor schedules:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}