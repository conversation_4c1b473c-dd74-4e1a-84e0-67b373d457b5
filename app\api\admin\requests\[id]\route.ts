import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { registrationRequests, user, userRoles, medicalSpecialties, consultories } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    const resolvedParams = await params;

    // Verificar que el usuario es admin
    const adminRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'admin'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (adminRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Acceso denegado. Solo administradores.' },
        { status: 403 }
      );
    }

    // Obtener la solicitud con información completa
    const result = await db
      .select({
        request: registrationRequests,
        user: user,
        userRole: userRoles,
        specialty: medicalSpecialties,
        consultory: consultories
      })
      .from(registrationRequests)
      .innerJoin(user, eq(registrationRequests.userId, user.id))
      .leftJoin(
        userRoles, 
        and(
          eq(userRoles.userId, registrationRequests.userId),
          eq(userRoles.role, registrationRequests.role)
        )
      )
      .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
      .leftJoin(consultories, eq(userRoles.consultoryId, consultories.id))
      .where(eq(registrationRequests.id, resolvedParams.id))
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    const requestData = result[0];

    // Obtener información adicional según el rol
    let additionalInfo = {};

    if (requestData.request.role === 'guardian') {
      // Verificar si hay relaciones guardian-paciente pendientes
      const guardianRelations = await db
        .select()
        .from(user)
        .where(eq(user.id, requestData.request.userId));
      
      additionalInfo = { guardianRelations };
    } else if (requestData.request.role === 'assistant') {
      // Obtener información detallada de los doctores seleccionados
      const assignedDoctorIds = requestData.request.specificData?.assignedDoctors || [];
      
      if (assignedDoctorIds.length > 0) {
        // Obtener información de doctores uno por uno (compatible con SQLite)
        const doctorDetails = [];
        for (const doctorId of assignedDoctorIds) {
          const doctorInfo = await db
            .select({
              id: user.id,
              firstName: user.firstName,
              lastName: user.lastName,
              email: user.email,
              specialty: medicalSpecialties.name,
              specialtyId: userRoles.specialtyId,
              consultory: consultories.name,
              consultoryId: userRoles.consultoryId,
              medicalLicense: userRoles.medicalLicense,
              status: userRoles.status
            })
            .from(user)
            .innerJoin(userRoles, eq(user.id, userRoles.userId))
            .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
            .leftJoin(consultories, eq(userRoles.consultoryId, consultories.id))
            .where(
              and(
                eq(user.id, doctorId),
                eq(userRoles.role, 'doctor'),
                eq(userRoles.status, 'active')
              )
            )
            .limit(1);
          
          
          if (doctorInfo.length > 0) {
            doctorDetails.push(doctorInfo[0]);
          }
        }
        
        additionalInfo = { 
          assignedDoctors: assignedDoctorIds,
          doctorDetails: doctorDetails 
        };
      } else {
        additionalInfo = { 
          assignedDoctors: [],
          doctorDetails: [] 
        };
      }
    }

    // Formatear respuesta
    const response = {
      id: requestData.request.id,
      userId: requestData.request.userId,
      role: requestData.request.role,
      status: requestData.request.status,
      submittedAt: requestData.request.submittedAt,
      reviewedAt: requestData.request.reviewedAt,
      reviewedBy: requestData.request.reviewedBy,
      reviewNotes: requestData.request.reviewNotes,
      rejectionReason: requestData.request.rejectionReason,
      user: {
        id: requestData.user.id,
        email: requestData.user.email,
        firstName: requestData.user.firstName,
        lastName: requestData.user.lastName,
        documentType: requestData.user.documentType,
        documentNumber: requestData.user.documentNumber,
        dateOfBirth: requestData.user.dateOfBirth,
        gender: requestData.user.gender,
        phone: requestData.user.phone,
        alternativePhone: requestData.user.alternativePhone,
        address: requestData.user.address,
        emergencyContact: requestData.user.emergencyContact,
        emergencyPhone: requestData.user.emergencyPhone,
        overallStatus: requestData.user.overallStatus
      },
      roleData: requestData.userRole ? {
        status: requestData.userRole.status,
        medicalLicense: requestData.userRole.medicalLicense,
        roleData: requestData.userRole.roleData,
        specialty: requestData.specialty,
        consultory: requestData.consultory
      } : null,
      generalData: requestData.request.generalData,
      specificData: requestData.request.specificData,
      additionalInfo
    };

    return NextResponse.json({
      success: true,
      data: response
    });

  } catch (error) {
    console.error('Error fetching registration request:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}