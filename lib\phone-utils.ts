import { useRegionalConfig } from '@/hooks/use-regional-config';

/**
 * Utilidades para manejo estandarizado de teléfonos
 * Usa configuración regional del consultorio
 */

export interface PhoneFormatConfig {
  countryCode: string;    // "+502"
  format: string;         // "####-####"
  digits: number;         // 8
  example: string;        // "5555-5555"
}

/**
 * Obtiene configuración de teléfonos desde la configuración regional
 */
export function getPhoneConfig(): PhoneFormatConfig {
  // Default para Guatemala (fallback)
  return {
    countryCode: '+502',
    format: '####-####',
    digits: 8,
    example: '5555-5555'
  };
}

/**
 * Normaliza un número de teléfono removiendo caracteres especiales
 * "502 399-293 392" → "50239929392"
 */
export function normalizePhone(phone: string): string {
  if (!phone) return '';
  return phone.replace(/[\s\-\(\)\+]/g, '');
}

/**
 * Extrae solo los dígitos del teléfono sin código de país
 * "+502 3992-9339" → "39929339"
 * "502 399 293 392" → "39929392" 
 * "57324963" → "57324963"
 */
export function extractPhoneDigits(phone: string, countryCode: string = '502'): string {
  if (!phone) return '';
  
  const normalized = normalizePhone(phone);
  
  // Solo remover código de país si viene con + o si es demasiado largo
  // Ejemplo: "+50255665555" o "50255665555" (más de 8 dígitos) → remover "502"
  // Pero "50255665" (8 dígitos exactos) → mantener completo
  const config = getPhoneConfig();
  
  if (normalized.startsWith(countryCode) && normalized.length > config.digits) {
    return normalized.substring(countryCode.length);
  }
  
  return normalized;
}

/**
 * Aplica formato visual a los dígitos del teléfono
 * "39929339" → "3992-9339" (formato Guatemala)
 */
export function applyPhoneFormat(digits: string, format: string = '####-####'): string {
  if (!digits) return '';
  
  // Solo dígitos
  const cleanDigits = digits.replace(/\D/g, '');
  
  let formatted = '';
  let digitIndex = 0;
  
  for (const char of format) {
    if (char === '#' && digitIndex < cleanDigits.length) {
      formatted += cleanDigits[digitIndex];
      digitIndex++;
    } else if (char !== '#') {
      formatted += char;
    }
  }
  
  return formatted;
}

/**
 * Formatea un teléfono para almacenamiento estandarizado
 * "57324963" → "+502 5732-4963"
 * "502 399 293 392" → "+502 3992-9339"
 */
export function formatPhoneForStorage(phone: string): string {
  if (!phone) return '';
  
  const config = getPhoneConfig();
  const digits = extractPhoneDigits(phone, config.countryCode.replace('+', ''));
  
  // Si no tiene los dígitos correctos, retornar como está
  if (digits.length !== config.digits) {
    return phone; // No alterar teléfonos inválidos
  }
  
  const formatted = applyPhoneFormat(digits, config.format);
  return `${config.countryCode} ${formatted}`;
}

/**
 * Formatea un teléfono para mostrar al usuario
 * Mismo que storage por ahora, pero separado por si necesitamos diferente formato
 */
export function formatPhoneForDisplay(phone: string): string {
  return formatPhoneForStorage(phone);
}

/**
 * Valida si un teléfono tiene el formato correcto
 */
export function validatePhone(phone: string): { isValid: boolean; error?: string } {
  if (!phone) {
    return { isValid: false, error: 'Teléfono es requerido' };
  }
  
  const config = getPhoneConfig();
  const digits = extractPhoneDigits(phone, config.countryCode.replace('+', ''));
  
  if (digits.length !== config.digits) {
    if (digits.length < config.digits) {
      return { 
        isValid: false, 
        error: `Número incompleto - faltan ${config.digits - digits.length} dígitos` 
      };
    } else {
      return { 
        isValid: false, 
        error: `Número muy largo - máximo ${config.digits} dígitos` 
      };
    }
  }
  
  // Verificar que solo contenga dígitos
  if (!/^\d+$/.test(digits)) {
    return { isValid: false, error: 'Solo debe contener números' };
  }
  
  return { isValid: true };
}

/**
 * Genera placeholder para input de teléfono
 */
export function getPhonePlaceholder(): string {
  const config = getPhoneConfig();
  return config.example; // Solo mostrar el ejemplo sin el código de país
}

/**
 * Formatea mientras el usuario escribe (para inputs)
 * "5732" → "5732"
 * "57324" → "5732-4"
 * "57324963" → "5732-4963"
 */
export function formatPhoneAsYouType(value: string): string {
  const config = getPhoneConfig();
  const digits = value.replace(/\D/g, '');
  
  // Limitar a la cantidad de dígitos permitidos
  const limitedDigits = digits.substring(0, config.digits);
  
  return applyPhoneFormat(limitedDigits, config.format);
}