import { db } from '@/db/drizzle';
import { mediaSources } from '@/db/schema';

export const mediaSourcesData = [
  // Redes Sociales
  {
    id: 'facebook',
    name: 'Facebook',
    type: 'digital',
    category: 'Redes Sociales',
    trackingEnabled: true,
    cost: 50.0,
    description: 'Pacientes que llegaron a través de Facebook',
    icon: 'facebook',
    color: '#1877F2'
  },
  {
    id: 'instagram',
    name: 'Instagram',
    type: 'digital',
    category: 'Redes Sociales',
    trackingEnabled: true,
    cost: 40.0,
    description: 'Pacientes que llegaron a través de Instagram',
    icon: 'instagram',
    color: '#E4405F'
  },
  {
    id: 'tiktok',
    name: 'TikTok',
    type: 'digital',
    category: 'Redes Sociales',
    trackingEnabled: true,
    cost: 30.0,
    description: 'Pacientes que llegaron a través de TikTok',
    icon: 'music',
    color: '#000000'
  },
  {
    id: 'whatsapp',
    name: 'WhatsApp',
    type: 'digital',
    category: 'Redes Sociales',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes que contactaron por WhatsApp',
    icon: 'message-circle',
    color: '#25D366'
  },

  // Buscadores
  {
    id: 'google-search',
    name: 'Google Búsqueda',
    type: 'digital',
    category: 'Buscadores',
    trackingEnabled: true,
    cost: 75.0,
    description: 'Pacientes que llegaron por búsquedas en Google',
    icon: 'search',
    color: '#4285F4'
  },
  {
    id: 'google-maps',
    name: 'Google Maps',
    type: 'digital',
    category: 'Buscadores',
    trackingEnabled: true,
    cost: 25.0,
    description: 'Pacientes que encontraron el consultorio en Google Maps',
    icon: 'map-pin',
    color: '#34A853'
  },

  // Referidos
  {
    id: 'referral-doctor',
    name: 'Referido por Médico',
    type: 'referral',
    category: 'Referidos',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes referidos por otro médico',
    icon: 'user-check',
    color: '#10B981'
  },
  {
    id: 'referral-patient',
    name: 'Referido por Paciente',
    type: 'referral',
    category: 'Referidos',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes referidos por otros pacientes',
    icon: 'users',
    color: '#10B981'
  },
  {
    id: 'referral-family',
    name: 'Referido por Familiar',
    type: 'referral',
    category: 'Referidos',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes referidos por familiares',
    icon: 'heart',
    color: '#10B981'
  },

  // Publicidad Tradicional
  {
    id: 'flyer',
    name: 'Volante/Flyer',
    type: 'traditional',
    category: 'Publicidad',
    trackingEnabled: false,
    cost: 15.0,
    description: 'Pacientes que llegaron por volantes publicitarios',
    icon: 'file-text',
    color: '#F59E0B'
  },
  {
    id: 'radio',
    name: 'Radio',
    type: 'traditional',
    category: 'Publicidad',
    trackingEnabled: false,
    cost: 100.0,
    description: 'Pacientes que llegaron por publicidad en radio',
    icon: 'radio',
    color: '#F59E0B'
  },
  {
    id: 'newspaper',
    name: 'Periódico',
    type: 'traditional',
    category: 'Publicidad',
    trackingEnabled: false,
    cost: 80.0,
    description: 'Pacientes que llegaron por anuncios en periódico',
    icon: 'newspaper',
    color: '#F59E0B'
  },

  // Directo
  {
    id: 'walk-in',
    name: 'Pasaba por Aquí',
    type: 'direct',
    category: 'Directo',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes que llegaron caminando sin referencia específica',
    icon: 'map',
    color: '#6B7280'
  },
  {
    id: 'emergency',
    name: 'Emergencia',
    type: 'direct',
    category: 'Directo',
    trackingEnabled: false,
    cost: 0.0,
    description: 'Pacientes que llegaron por emergencia médica',
    icon: 'alert-circle',
    color: '#EF4444'
  },
  {
    id: 'website',
    name: 'Sitio Web',
    type: 'digital',
    category: 'Web',
    trackingEnabled: true,
    cost: 20.0,
    description: 'Pacientes que llegaron a través del sitio web',
    icon: 'globe',
    color: '#3B82F6'
  }
];

export async function seedMediaSources() {
  console.log('📢 Seeding media sources...');
  
  try {
    await db.insert(mediaSources).values(mediaSourcesData).onConflictDoNothing();
    console.log('✅ Media sources seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding media sources:', error);
    throw error;
  }
}