import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { mediaSources } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or } from 'drizzle-orm';

const mediaTypes = [
  { id: 'digital', name: 'Digital', color: '#3B82F6' },
  { id: 'traditional', name: 'Tradicional', color: '#F59E0B' },
  { id: 'referral', name: '<PERSON><PERSON><PERSON>', color: '#10B981' },
  { id: 'direct', name: '<PERSON>o', color: '#6B7280' }
];

const categories = [
  { id: 'Redes Sociales', name: 'Redes Sociales', type: 'digital' },
  { id: 'Buscadores', name: 'Buscadores', type: 'digital' },
  { id: 'Web', name: 'Web', type: 'digital' },
  { id: '<PERSON>fer<PERSON><PERSON>', name: '<PERSON><PERSON><PERSON><PERSON>', type: 'referral' },
  { id: 'Publicidad', name: 'Publicidad', type: 'traditional' },
  { id: 'Directo', name: 'Directo', type: 'direct' }
];

// GET - Listar medios/origen de pacientes
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const category = searchParams.get('category');
    const onlyActive = searchParams.get('active') === 'true';
    const onlyTrackable = searchParams.get('trackable') === 'true';
    const analytics = searchParams.get('analytics') === 'true';
    const orderBy = searchParams.get('orderBy') || 'name'; // 'name', 'type', 'category', 'cost', 'createdAt'
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(mediaSources);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(mediaSources.name, `%${search}%`),
          ilike(mediaSources.description, `%${search}%`),
          ilike(mediaSources.category, `%${search}%`)
        )
      );
    }

    if (type) {
      conditions.push(eq(mediaSources.type, type));
    }

    if (category) {
      conditions.push(eq(mediaSources.category, category));
    }

    if (onlyActive) {
      conditions.push(eq(mediaSources.isActive, true));
    }

    if (onlyTrackable) {
      conditions.push(eq(mediaSources.trackingEnabled, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'type' ? mediaSources.type : 
                       orderBy === 'category' ? mediaSources.category :
                       orderBy === 'cost' ? mediaSources.cost :
                       orderBy === 'createdAt' ? mediaSources.createdAt : 
                       mediaSources.name;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const mediaSourcesData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(mediaSources);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Analíticas básicas
    let analyticsData = null;
    if (analytics) {
      const activeSources = await db.select().from(mediaSources).where(eq(mediaSources.isActive, true));
      const totalCost = activeSources.reduce((sum, s) => sum + s.cost, 0);
      
      analyticsData = {
        summary: {
          total: activeSources.length,
          trackable: activeSources.filter(s => s.trackingEnabled).length,
          totalMonthlyCost: totalCost,
          averageCost: activeSources.length > 0 ? totalCost / activeSources.length : 0
        },
        byType: mediaTypes.map(type => ({
          type: type.id,
          name: type.name,
          count: activeSources.filter(s => s.type === type.id).length,
          cost: activeSources.filter(s => s.type === type.id).reduce((sum, s) => sum + s.cost, 0)
        })),
        byCategory: categories.map(cat => ({
          category: cat.id,
          name: cat.name,
          count: activeSources.filter(s => s.category === cat.id).length,
          cost: activeSources.filter(s => s.category === cat.id).reduce((sum, s) => sum + s.cost, 0)
        })),
        costAnalysis: {
          free: activeSources.filter(s => s.cost === 0).length,
          lowCost: activeSources.filter(s => s.cost > 0 && s.cost <= 30).length,
          mediumCost: activeSources.filter(s => s.cost > 30 && s.cost <= 70).length,
          highCost: activeSources.filter(s => s.cost > 70).length
        }
      };
    }

    return NextResponse.json({
      data: mediaSourcesData,
      types: mediaTypes,
      categories,
      analytics: analyticsData,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error obteniendo medios/origen:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo medio/origen
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    // Solo admin y asistentes pueden crear medios/origen
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Sin permisos para crear medios/origen' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      type, 
      category, 
      trackingEnabled, 
      cost, 
      description, 
      icon, 
      color, 
      isActive 
    } = body;

    // Validaciones
    if (!name || !type || !category) {
      return NextResponse.json(
        { error: 'Nombre, tipo y categoría son requeridos' },
        { status: 400 }
      );
    }

    if (!mediaTypes.find(t => t.id === type)) {
      return NextResponse.json(
        { error: 'Tipo inválido' },
        { status: 400 }
      );
    }

    if (!categories.find(c => c.id === category)) {
      return NextResponse.json(
        { error: 'Categoría inválida' },
        { status: 400 }
      );
    }

    if (cost && (cost < 0 || cost > 10000)) {
      return NextResponse.json(
        { error: 'El costo debe estar entre 0 y 10,000' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un medio con el mismo ID
    const existingSource = await db.select().from(mediaSources).where(eq(mediaSources.id, id)).limit(1);
    if (existingSource.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un medio/origen con este nombre' },
        { status: 400 }
      );
    }

    const newMediaSource = {
      id,
      name: name.trim(),
      type,
      category,
      trackingEnabled: Boolean(trackingEnabled),
      cost: cost ? parseFloat(cost) : 0.0,
      description: description?.trim() || '',
      icon: icon || 'circle',
      color: color || mediaTypes.find(t => t.id === type)?.color || '#6B7280',
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedMediaSource] = await db.insert(mediaSources).values(newMediaSource).returning();

    return NextResponse.json({
      message: 'Medio/origen creado exitosamente',
      data: insertedMediaSource
    });

  } catch (error) {
    console.error('Error creando medio/origen:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar medio/origen
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    // Solo admin y asistentes pueden actualizar medios/origen
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Sin permisos para actualizar medios/origen' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, type, category, trackingEnabled, cost, description, icon, color, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de medio/origen requerido' },
        { status: 400 }
      );
    }

    // Buscar medio existente
    const existingSource = await db.select().from(mediaSources).where(eq(mediaSources.id, id)).limit(1);
    if (existingSource.length === 0) {
      return NextResponse.json(
        { error: 'Medio/origen no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones
    if (type && !mediaTypes.find(t => t.id === type)) {
      return NextResponse.json(
        { error: 'Tipo inválido' },
        { status: 400 }
      );
    }

    if (category && !categories.find(c => c.id === category)) {
      return NextResponse.json(
        { error: 'Categoría inválida' },
        { status: 400 }
      );
    }

    if (cost && (cost < 0 || cost > 10000)) {
      return NextResponse.json(
        { error: 'El costo debe estar entre 0 y 10,000' },
        { status: 400 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(type && { type }),
      ...(category && { category }),
      ...(trackingEnabled !== undefined && { trackingEnabled: Boolean(trackingEnabled) }),
      ...(cost !== undefined && { cost: parseFloat(cost) }),
      ...(description !== undefined && { description: description.trim() }),
      ...(icon && { icon }),
      ...(color && { color }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedMediaSource] = await db.update(mediaSources)
      .set(updateData)
      .where(eq(mediaSources.id, id))
      .returning();

    return NextResponse.json({
      message: 'Medio/origen actualizado exitosamente',
      data: updatedMediaSource
    });

  } catch (error) {
    console.error('Error actualizando medio/origen:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}