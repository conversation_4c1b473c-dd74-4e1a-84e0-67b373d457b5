'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  User, 
  FileText, 
  ArrowLeft,
  Save,
  RefreshCw,
  Calendar,
  Shield,
  AlertCircle,
  Building,
  Stethoscope
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { toast } from 'sonner';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
}

interface Consultory {
  id: string;
  name: string;
  type: string;
}

export default function NewExpedientePage() {
  const router = useRouter();
  const { user } = useUser();
  
  // Estados
  const [loading, setLoading] = useState(false);
  const [patients, setPatients] = useState<Patient[]>([]);
  const [consultories, setConsultories] = useState<Consultory[]>([]);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [formData, setFormData] = useState({
    patientId: '',
    consultoryId: '',
    isMinor: false,
    guardianInfo: {
      name: '',
      relationship: '',
      phone: '',
      email: ''
    }
  });

  // Cargar datos iniciales
  useEffect(() => {
    fetchPatients();
    fetchConsultories();
  }, []);

  const fetchPatients = async () => {
    try {
      const response = await fetch('/api/catalogs/patients');
      const data = await response.json();
      if (response.ok) {
        setPatients(data.data || []);
      }
    } catch (error) {
      console.error('Error loading patients:', error);
    }
  };

  const fetchConsultories = async () => {
    try {
      const response = await fetch('/api/catalogs/consultories');
      const data = await response.json();
      if (response.ok) {
        setConsultories(data.data || []);
      }
    } catch (error) {
      console.error('Error loading consultories:', error);
    }
  };

  const calculateAge = (dateOfBirth: string): number => {
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const handlePatientSelect = (patientId: string) => {
    const patient = patients.find(p => p.id === patientId);
    const age = patient?.dateOfBirth ? calculateAge(patient.dateOfBirth) : 0;
    
    setSelectedPatient(patient || null);
    setFormData(prev => ({
      ...prev,
      patientId,
      isMinor: age < 18
    }));
  };

  const handleCreateExpediente = async () => {
    if (!formData.patientId || !formData.consultoryId) {
      toast.error('Por favor completa todos los campos requeridos');
      return;
    }

    if (!user?.id) {
      toast.error('Error de autenticación');
      return;
    }

    setLoading(true);
    try {
      const recordData = {
        patientId: formData.patientId,
        consultoryId: formData.consultoryId,
        primaryDoctorId: user.id,
        patientSummary: {
          fullName: selectedPatient 
            ? `${selectedPatient.firstName} ${selectedPatient.lastName}`
            : 'Paciente',
          age: selectedPatient?.dateOfBirth ? calculateAge(selectedPatient.dateOfBirth) : 0,
          gender: selectedPatient?.gender || 'No especificado',
          bloodType: null,
          allergies: []
        },
        isMinor: formData.isMinor,
        guardianInfo: formData.isMinor ? formData.guardianInfo : null,
        status: 'active'
      };

      const response = await fetch('/api/medical-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(recordData)
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Expediente creado exitosamente');
        router.push(`/dashboard/doctor/expedientes/${result.data.id}?tab=general`);
      } else {
        throw new Error(result.message || 'Error al crear expediente');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error(error instanceof Error ? error.message : 'Error al crear expediente');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="ghost" size="sm" onClick={() => router.back()}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Crear Nuevo Expediente
          </h1>
          <p className="text-gray-600">
            Crear un nuevo expediente clínico para un paciente
          </p>
        </div>
      </div>

      <div className="max-w-2xl">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Información del Expediente
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Seleccionar Paciente */}
            <div className="space-y-2">
              <Label>Paciente *</Label>
              <Select
                value={formData.patientId}
                onValueChange={handlePatientSelect}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar paciente..." />
                </SelectTrigger>
                <SelectContent>
                  {patients.map((patient) => (
                    <SelectItem key={patient.id} value={patient.id}>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4" />
                        {patient.firstName} {patient.lastName}
                        {patient.dateOfBirth && (
                          <span className="text-sm text-gray-500">
                            ({calculateAge(patient.dateOfBirth)} años)
                          </span>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Información del paciente seleccionado */}
            {selectedPatient && (
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-semibold text-blue-900 mb-2">Datos del Paciente</h4>
                <div className="grid grid-cols-2 gap-2 text-sm text-blue-800">
                  <div>Email: {selectedPatient.email}</div>
                  <div>Teléfono: {selectedPatient.phone || 'No disponible'}</div>
                  <div>Edad: {selectedPatient.dateOfBirth ? calculateAge(selectedPatient.dateOfBirth) : 'No disponible'} años</div>
                  <div>Género: {selectedPatient.gender || 'No especificado'}</div>
                </div>
              </div>
            )}

            {/* Consultorio */}
            <div className="space-y-2">
              <Label>Consultorio *</Label>
              <Select
                value={formData.consultoryId}
                onValueChange={(value) => setFormData(prev => ({ ...prev, consultoryId: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar consultorio..." />
                </SelectTrigger>
                <SelectContent>
                  {consultories.map((consultory) => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4" />
                        {consultory.name}
                        <span className="text-sm text-gray-500">({consultory.type})</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Menor de edad */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="is-minor"
                checked={formData.isMinor}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, isMinor: checked as boolean }))
                }
              />
              <Label htmlFor="is-minor">Paciente menor de edad</Label>
            </div>

            {/* Información del tutor (solo si es menor) */}
            {formData.isMinor && (
              <div className="space-y-4 border-l-4 border-yellow-400 pl-4">
                <h4 className="font-semibold text-yellow-800 flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Información del Tutor/Encargado
                </h4>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label>Nombre del tutor</Label>
                    <Input
                      value={formData.guardianInfo.name}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        guardianInfo: { ...prev.guardianInfo, name: e.target.value }
                      }))}
                      placeholder="Nombre completo"
                    />
                  </div>
                  <div>
                    <Label>Relación</Label>
                    <Select
                      value={formData.guardianInfo.relationship}
                      onValueChange={(value) => setFormData(prev => ({
                        ...prev,
                        guardianInfo: { ...prev.guardianInfo, relationship: value }
                      }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar..." />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="padre">Padre</SelectItem>
                        <SelectItem value="madre">Madre</SelectItem>
                        <SelectItem value="tutor">Tutor Legal</SelectItem>
                        <SelectItem value="abuelo">Abuelo/a</SelectItem>
                        <SelectItem value="tio">Tío/a</SelectItem>
                        <SelectItem value="otro">Otro</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label>Teléfono</Label>
                    <Input
                      value={formData.guardianInfo.phone}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        guardianInfo: { ...prev.guardianInfo, phone: e.target.value }
                      }))}
                      placeholder="Teléfono de contacto"
                    />
                  </div>
                  <div>
                    <Label>Email</Label>
                    <Input
                      type="email"
                      value={formData.guardianInfo.email}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        guardianInfo: { ...prev.guardianInfo, email: e.target.value }
                      }))}
                      placeholder="Email del tutor"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Botones */}
            <div className="flex gap-3 pt-4">
              <Button
                variant="outline"
                onClick={() => router.back()}
                disabled={loading}
              >
                Cancelar
              </Button>
              <Button
                onClick={handleCreateExpediente}
                disabled={loading || !formData.patientId || !formData.consultoryId}
              >
                {loading ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creando...
                  </>
                ) : (
                  <>
                    <Save className="h-4 w-4 mr-2" />
                    Crear Expediente
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}