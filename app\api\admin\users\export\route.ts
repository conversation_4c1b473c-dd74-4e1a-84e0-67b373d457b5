import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  // Obtener rol de los session claims (igual que en el middleware)
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// POST - Exportar usuarios a PDF/Excel
export async function POST(request: NextRequest) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const body = await request.json();
    const { format, users, columns } = body;

    // Validaciones
    if (!format || !['pdf', 'excel'].includes(format)) {
      return NextResponse.json(
        { success: false, error: 'Formato no válido. Use "pdf" o "excel"' },
        { status: 400 }
      );
    }

    if (!users || !Array.isArray(users) || users.length === 0) {
      return NextResponse.json(
        { success: false, error: 'No se proporcionaron usuarios para exportar' },
        { status: 400 }
      );
    }

    // Preparar datos para exportar
    const exportData = users.map(user => ({
      ID: user.id,
      Nombre: user.name || `${user.firstName || ''} ${user.lastName || ''}`.trim(),
      Email: user.email,
      'Tipo Documento': user.documentType?.toUpperCase() || '',
      'Número Documento': user.documentNumber || '',
      Teléfono: user.phone || '',
      Roles: user.roles || '',
      Estado: user.status || '',
      'Fecha Creación': user.createdAt ? new Date(user.createdAt).toLocaleDateString('es-ES') : '',
    }));

    if (format === 'excel') {
      // Para Excel, necesitaremos una librería como xlsx
      // Por ahora, retornamos los datos en formato JSON para que el frontend pueda procesarlos
      return NextResponse.json({
        success: true,
        data: {
          filename: `usuarios-${new Date().toISOString().split('T')[0]}.xlsx`,
          content: exportData,
          format: 'excel'
        }
      });
    } else if (format === 'pdf') {
      // Para PDF, necesitaremos una librería como jsPDF o PDFKit
      // Por ahora, retornamos los datos en formato JSON para que el frontend pueda procesarlos
      return NextResponse.json({
        success: true,
        data: {
          filename: `usuarios-${new Date().toISOString().split('T')[0]}.pdf`,
          content: exportData,
          format: 'pdf'
        }
      });
    }

    return NextResponse.json(
      { success: false, error: 'Formato no implementado' },
      { status: 400 }
    );

  } catch (error) {
    console.error('Error exporting users:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// GET - Exportar usuarios con parámetros de URL
export async function GET(request: NextRequest) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const { searchParams } = new URL(request.url);
    const format = searchParams.get('format') || 'excel';
    const userIds = searchParams.get('userIds')?.split(',') || [];

    // Si no se especifican IDs, exportar todos los usuarios activos
    if (userIds.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Debe especificar los IDs de usuarios a exportar' },
        { status: 400 }
      );
    }

    // TODO: Implementar la lógica para obtener usuarios por IDs y exportar
    // Por ahora, retornamos un mensaje de éxito
    return NextResponse.json({
      success: true,
      message: `Exportación de ${userIds.length} usuarios en formato ${format} preparada`
    });

  } catch (error) {
    console.error('Error in GET export:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}