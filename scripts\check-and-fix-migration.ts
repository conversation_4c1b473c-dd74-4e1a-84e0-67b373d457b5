import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';

async function checkAndFixMigration() {
  try {
    console.log('🔍 Verificando estado de la tabla doctor_service_prices...');
    
    // Verificar si la tabla existe
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'doctor_service_prices'
      );
    `);
    
    console.log('📋 Tabla existe:', tableExists.rows[0]?.exists ? 'SÍ ✅' : 'NO ❌');
    
    if (tableExists.rows[0]?.exists) {
      console.log('✅ La tabla ya existe. Verificando estructura...');
      
      // Verificar columnas
      const columns = await db.execute(sql`
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'doctor_service_prices'
        ORDER BY ordinal_position;
      `);
      
      console.log('📊 Columnas encontradas:');
      columns.rows.forEach((col: any) => {
        console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
      });
      
      // Verificar constraints
      const constraints = await db.execute(sql`
        SELECT constraint_name, constraint_type
        FROM information_schema.table_constraints 
        WHERE table_schema = 'public' 
        AND table_name = 'doctor_service_prices';
      `);
      
      console.log('🔗 Constraints encontrados:');
      constraints.rows.forEach((constraint: any) => {
        console.log(`  - ${constraint.constraint_name}: ${constraint.constraint_type}`);
      });
      
      // Verificar índices
      const indexes = await db.execute(sql`
        SELECT indexname, indexdef
        FROM pg_indexes 
        WHERE tablename = 'doctor_service_prices'
        AND schemaname = 'public';
      `);
      
      console.log('📑 Índices encontrados:');
      indexes.rows.forEach((index: any) => {
        console.log(`  - ${index.indexname}`);
      });
      
      // Verificar datos
      const count = await db.execute(sql`
        SELECT COUNT(*) as total FROM doctor_service_prices;
      `);
      
      console.log(`📊 Registros en tabla: ${count.rows[0]?.total || 0}`);
      
      console.log('🎉 La tabla doctor_service_prices está lista para usar!');
      
    } else {
      console.log('❌ La tabla no existe. Creando tabla...');
      
      // Crear solo la tabla sin constraints que ya existen
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS "doctor_service_prices" (
          "id" text PRIMARY KEY NOT NULL,
          "doctorId" text NOT NULL,
          "serviceId" text NOT NULL,
          "customPrice" numeric(10, 2) NOT NULL,
          "currency" text DEFAULT 'GTQ',
          "isActive" boolean DEFAULT true,
          "effectiveFrom" timestamp DEFAULT now(),
          "effectiveUntil" timestamp,
          "notes" text,
          "createdAt" timestamp DEFAULT now(),
          "updatedAt" timestamp DEFAULT now()
        );
      `);
      
      console.log('✅ Tabla creada exitosamente');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  checkAndFixMigration()
    .then(() => {
      console.log('🎉 Verificación completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { checkAndFixMigration };