import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { prescriptionTemplates } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener plantillas de recetas del doctor actual
    const templates = await db
      .select()
      .from(prescriptionTemplates)
      .where(eq(prescriptionTemplates.doctorId, userId))
      .orderBy(prescriptionTemplates.createdAt);

    return NextResponse.json({
      success: true,
      data: templates
    });
  } catch (error) {
    console.error('Error al obtener plantillas de recetas:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, content, category, isActive } = body;

    // Validaciones
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    if (!content || !content.trim()) {
      return NextResponse.json(
        { error: 'El contenido es requerido' },
        { status: 400 }
      );
    }

    if (name.length < 2) {
      return NextResponse.json(
        { error: 'El nombre debe tener al menos 2 caracteres' },
        { status: 400 }
      );
    }

    // Crear la plantilla de receta
    const newTemplate = await db
      .insert(prescriptionTemplates)
      .values({
        doctorId: userId,
        name: name.trim(),
        content: content.trim(),
        category: category?.trim() || null,
        isActive: isActive ?? true,
        createdAt: new Date(),
        updatedAt: new Date()
      })
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Plantilla de receta creada exitosamente',
      data: newTemplate[0]
    });
  } catch (error) {
    console.error('Error al crear plantilla de receta:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}