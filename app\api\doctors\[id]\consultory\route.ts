import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, consultories } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ 
        success: false, 
        error: 'No autorizado' 
      }, { status: 401 });
    }

    const doctorId = params.id;

    // Buscar el rol de doctor activo para el usuario especificado
    const doctorRoleResult = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, doctorId),
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (doctorRoleResult.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'No se encontró rol de doctor activo' 
      }, { status: 404 });
    }

    const doctorRole = doctorRoleResult[0];
    
    if (!doctorRole.consultoryId) {
      return NextResponse.json({ 
        success: false, 
        error: 'Doctor no tiene consultorio asignado' 
      }, { status: 404 });
    }

    // Obtener información del consultorio
    const consultoryResult = await db
      .select()
      .from(consultories)
      .where(eq(consultories.id, doctorRole.consultoryId))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ 
        success: false, 
        error: 'Consultorio no encontrado' 
      }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: consultoryResult[0] 
    });

  } catch (error) {
    console.error('Error en doctor consultory API:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}