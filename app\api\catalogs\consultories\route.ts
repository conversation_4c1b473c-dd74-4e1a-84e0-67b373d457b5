import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { consultories, countries, departments, municipalities } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or, ne } from 'drizzle-orm';

// GET - Listar consultorios
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const type = searchParams.get('type');
    const specialty = searchParams.get('specialty');
    const building = searchParams.get('building');
    const floor = searchParams.get('floor');
    const activeFilter = searchParams.get('active');
    const emergencyFilter = searchParams.get('emergency');
    const onlyAccessible = searchParams.get('accessible') === 'true';
    const orderBy = searchParams.get('orderBy') || 'name';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle incluyendo relaciones geográficas
    let query = db.select({
      consultory: consultories,
      country: countries,
      department: departments,
      municipality: municipalities
    }).from(consultories)
    .leftJoin(countries, eq(consultories.countryId, countries.id))
    .leftJoin(departments, eq(consultories.departmentId, departments.id))
    .leftJoin(municipalities, eq(consultories.municipalityId, municipalities.id));

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(consultories.name, `%${search}%`),
          ilike(consultories.code, `%${search}%`),
          ilike(consultories.specialty, `%${search}%`),
          ilike(consultories.building, `%${search}%`),
          ilike(countries.name, `%${search}%`),
          ilike(departments.name, `%${search}%`),
          ilike(municipalities.name, `%${search}%`)
        )
      );
    }

    if (type && type !== 'all') {
      conditions.push(eq(consultories.type, type));
    }

    if (specialty && specialty !== 'all') {
      conditions.push(ilike(consultories.specialty, `%${specialty}%`));
    }

    if (building && building !== 'all') {
      conditions.push(ilike(consultories.building, `%${building}%`));
    }

    if (floor && floor !== 'all' && !isNaN(parseInt(floor))) {
      conditions.push(eq(consultories.floor, parseInt(floor)));
    }

    if (activeFilter && activeFilter !== 'all') {
      if (activeFilter === 'active') {
        conditions.push(eq(consultories.isActive, true));
      } else if (activeFilter === 'inactive') {
        conditions.push(eq(consultories.isActive, false));
      }
    }

    if (emergencyFilter && emergencyFilter !== 'all') {
      if (emergencyFilter === 'emergency') {
        conditions.push(eq(consultories.isEmergencyCapable, true));
      } else if (emergencyFilter === 'non-emergency') {
        conditions.push(eq(consultories.isEmergencyCapable, false));
      }
    }


    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'code' ? consultories.code :
                       orderBy === 'type' ? consultories.type :
                       orderBy === 'specialty' ? consultories.specialty :
                       orderBy === 'building' ? consultories.building :
                       orderBy === 'floor' ? consultories.floor :
                       orderBy === 'capacity' ? consultories.capacity :
                       orderBy === 'createdAt' ? consultories.createdAt :
                       consultories.name;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const consultoriesData = await query;
    
    // Transformar datos para incluir información geográfica
    const transformedData = consultoriesData.map(row => ({
      ...row.consultory,
      country: row.country,
      department: row.department,
      municipality: row.municipality,
      // Concatenar código de país al teléfono si existe
      phone: row.consultory.phone && row.country?.phoneCode && !row.consultory.phone.startsWith('+')
        ? `+${row.country.phoneCode} ${row.consultory.phone}`
        : row.consultory.phone
    }));

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(consultories);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Obtener filtros únicos para dropdown
    const uniqueTypes = await db.select({
      type: consultories.type
    }).from(consultories).groupBy(consultories.type);

    const uniqueSpecialties = await db.select({
      specialty: consultories.specialty
    }).from(consultories).groupBy(consultories.specialty);

    const uniqueBuildings = await db.select({
      building: consultories.building
    }).from(consultories).groupBy(consultories.building);

    return NextResponse.json({
      data: transformedData,
      filters: {
        types: uniqueTypes.map(t => t.type).filter(Boolean),
        specialties: uniqueSpecialties.map(s => s.specialty).filter(Boolean),
        buildings: uniqueBuildings.map(b => b.building).filter(Boolean)
      },
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: transformedData.filter(c => c.isActive).length,
        emergencyCapable: transformedData.filter(c => c.isEmergencyCapable).length,
        totalCapacity: transformedData.reduce((sum, c) => sum + c.capacity, 0)
      }
    });

  } catch (error) {
    console.error('Error obteniendo consultorios:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo consultorio (solo admin)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden crear consultorios' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      code, 
      type, 
      specialty, 
      capacity, 
      floor, 
      building, 
      address, 
      phone, 
      email, 
      logoUrl,
      countryId,
      departmentId,
      municipalityId,
      businessHours, 
      equipment, 
      services, 
      isEmergencyCapable, 
      hasAirConditioning, 
      hasWaitingRoom, 
      isActive 
    } = body;

    // Validaciones
    if (!name || !code || !type || !specialty) {
      return NextResponse.json(
        { error: 'Nombre, código, tipo y especialidad son requeridos' },
        { status: 400 }
      );
    }

    if (capacity && (capacity < 1 || capacity > 20)) {
      return NextResponse.json(
        { error: 'La capacidad debe estar entre 1 y 20' },
        { status: 400 }
      );
    }

    // Verificar que no exista un consultorio con el mismo código
    const existingCode = await db.select().from(consultories).where(eq(consultories.code, code.toUpperCase())).limit(1);
    if (existingCode.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un consultorio con este código' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = code.toLowerCase().replace(/[^a-z0-9\-]/g, '');

    const finalIsActive = isActive !== undefined ? Boolean(isActive) : true;

    // Si se está creando un consultorio activo, desactivar todos los demás
    if (finalIsActive) {
      await db
        .update(consultories)
        .set({
          isActive: false,
          updatedAt: new Date()
        });
    }

    const newConsultory = {
      id,
      name: name.trim(),
      code: code.toUpperCase(),
      type,
      specialty: specialty.trim(),
      capacity: capacity ? parseInt(capacity) : 1,
      floor: floor ? parseInt(floor) : 1,
      building: building?.trim() || '',
      address: address?.trim() || '',
      phone: phone?.trim() || null,
      email: email?.trim() || null,
      logoUrl: logoUrl?.trim() || null,
      countryId: countryId ? parseInt(countryId) : null,
      departmentId: departmentId ? parseInt(departmentId) : null,
      municipalityId: municipalityId ? parseInt(municipalityId) : null,
      businessHours: businessHours || {},
      equipment: equipment || [],
      services: services || [],
      isEmergencyCapable: Boolean(isEmergencyCapable),
      hasAirConditioning: Boolean(hasAirConditioning),
      hasWaitingRoom: Boolean(hasWaitingRoom),
      isActive: finalIsActive
    };

    // Insertar en base de datos
    const [insertedConsultory] = await db.insert(consultories).values(newConsultory).returning();

    return NextResponse.json({
      message: 'Consultorio creado exitosamente',
      data: insertedConsultory
    });

  } catch (error) {
    console.error('Error creando consultorio:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar consultorio (solo admin)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden actualizar consultorios' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      id, 
      name, 
      type, 
      specialty, 
      capacity, 
      floor, 
      building, 
      address, 
      phone, 
      email, 
      businessHours, 
      equipment, 
      services, 
      isEmergencyCapable, 
      hasAirConditioning, 
      hasWaitingRoom, 
      isActive 
    } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de consultorio requerido' },
        { status: 400 }
      );
    }

    // Buscar consultorio existente
    const existingConsultory = await db.select().from(consultories).where(eq(consultories.id, id)).limit(1);
    if (existingConsultory.length === 0) {
      return NextResponse.json(
        { error: 'Consultorio no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones
    if (capacity && (capacity < 1 || capacity > 20)) {
      return NextResponse.json(
        { error: 'La capacidad debe estar entre 1 y 20' },
        { status: 400 }
      );
    }

    // Validación para garantizar un solo consultorio activo
    const finalIsActive = isActive !== undefined ? Boolean(isActive) : existingConsultory[0].isActive;
    const currentIsActive = existingConsultory[0].isActive;

    // Si se está activando y no estaba activo antes
    if (finalIsActive && !currentIsActive) {
      // Desactivar todos los demás consultorios
      await db
        .update(consultories)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(ne(consultories.id, id));
    } 
    // Si se está desactivando y estaba activo antes
    else if (!finalIsActive && currentIsActive) {
      // Verificar que no sea el único activo
      const [{ count: activeCount }] = await db
        .select({ count: count() })
        .from(consultories)
        .where(eq(consultories.isActive, true));

      if (activeCount <= 1) {
        return NextResponse.json({ 
          error: 'No se puede desactivar el único consultorio activo. Debe haber al menos un consultorio activo en el sistema.',
          code: 'LAST_ACTIVE_CONSULTORY'
        }, { status: 400 });
      }
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(type && { type }),
      ...(specialty && { specialty: specialty.trim() }),
      ...(capacity !== undefined && { capacity: parseInt(capacity) }),
      ...(floor !== undefined && { floor: parseInt(floor) }),
      ...(building !== undefined && { building: building.trim() }),
      ...(address !== undefined && { address: address.trim() }),
      ...(phone !== undefined && { phone: phone?.trim() || null }),
      ...(email !== undefined && { email: email?.trim() || null }),
      ...(businessHours && { businessHours }),
      ...(equipment && { equipment }),
      ...(services && { services }),
      ...(isEmergencyCapable !== undefined && { isEmergencyCapable: Boolean(isEmergencyCapable) }),
      ...(hasAirConditioning !== undefined && { hasAirConditioning: Boolean(hasAirConditioning) }),
      ...(hasWaitingRoom !== undefined && { hasWaitingRoom: Boolean(hasWaitingRoom) }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedConsultory] = await db.update(consultories)
      .set(updateData)
      .where(eq(consultories.id, id))
      .returning();

    return NextResponse.json({
      message: 'Consultorio actualizado exitosamente',
      data: updatedConsultory
    });

  } catch (error) {
    console.error('Error actualizando consultorio:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}