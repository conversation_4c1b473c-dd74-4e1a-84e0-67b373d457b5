import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { db } from '@/db/drizzle';
import { appointments, user, guardianPatientRelations } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { PreCheckinClientWrapper } from '@/components/pre-checkin/pre-checkin-client-wrapper';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface PreCheckinPageProps {
  params: {
    appointmentId: string;
    token: string;
  };
  searchParams: {
    for?: string; // ID del paciente si es dependiente
  };
}

// Función para validar y obtener datos de la cita
async function getAppointmentData(appointmentId: string, token: string, patientId?: string) {
  try {
    // Verificar que la cita existe y el token es válido
    const appointment = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        status: appointments.status,
        preCheckinToken: appointments.preCheckinToken,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinCompletedAt: appointments.preCheckinCompletedAt,
        preCheckinCompletedBy: appointments.preCheckinCompletedBy,
        // Datos del paciente
        patientId: appointments.patientId,
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
        patientEmail: user.email,
        // Datos del doctor
        doctorFirstName: user.firstName,
        doctorLastName: user.lastName,
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.patientId, user.id))
      .where(
        and(
          eq(appointments.id, appointmentId),
          eq(appointments.preCheckinToken, token)
        )
      )
      .limit(1);

    if (appointment.length === 0) {
      return null;
    }

    const appointmentData = appointment[0];

    // Si es para un dependiente, verificar la relación guardián-paciente
    let isDependent = false;
    let guardianInfo = null;

    if (patientId && patientId !== appointmentData.patientId) {
      // Verificar que existe la relación guardián-paciente
      const guardianRelation = await db
        .select({
          guardianId: guardianPatientRelations.guardianId,
          relationship: guardianPatientRelations.relationship,
          guardianFirstName: user.firstName,
          guardianLastName: user.lastName,
          guardianEmail: user.email,
        })
        .from(guardianPatientRelations)
        .leftJoin(user, eq(guardianPatientRelations.guardianId, user.id))
        .where(
          and(
            eq(guardianPatientRelations.patientId, appointmentData.patientId!),
            eq(guardianPatientRelations.guardianId, patientId)
          )
        )
        .limit(1);

      if (guardianRelation.length === 0) {
        return null; // No tiene permisos para este paciente
      }

      isDependent = true;
      guardianInfo = guardianRelation[0];
    }

    return {
      ...appointmentData,
      isDependent,
      guardianInfo,
    };
  } catch (error) {
    console.error('Error fetching appointment data:', error);
    return null;
  }
}

export default async function PreCheckinPage({ params, searchParams }: PreCheckinPageProps) {
  const { appointmentId, token } = params;
  const { for: forPatientId } = searchParams;

  const appointmentData = await getAppointmentData(appointmentId, token, forPatientId);

  if (!appointmentData) {
    notFound();
  }

  return (
    <Suspense fallback={<div>Cargando...</div>}>
      <PreCheckinClientWrapper 
        appointmentData={appointmentData}
        appointmentId={appointmentId}
        token={token}
        forPatientId={forPatientId}
      />
    </Suspense>
  );
}