# Plan de Implementación: Catálogo de Servicios Médicos

## Análisis Inicial

### Estado Actual
- ❌ **No existe schema de servicios médicos** en la base de datos
- ❌ **No hay APIs implementadas** para servicios médicos
- ❌ **No hay interface CRUD** para servicios médicos
- 📋 **Documentado en Phase 2** del plan de catálogos (docs/funcionalidades/admin/catalogos.md)
- 🏥 **Consultorios** tienen campo `services` como array JSON (no es un catálogo propio)

### Patrón a Seguir
Según `docs/cruds.md`, debo implementar un **CRUD Simple** siguiendo el patrón de catálogos básicos como:
- Monedas (`currencies`)
- Tipos de Actividad (`activity-types`)
- Medios de Comunicación (`media-sources`)

## Plan de Implementación

### ✅ Tareas Pendientes

#### 1. Diseño del Schema
- [ ] Definir interface `MedicalService` siguiendo el patrón de catálogos simples
- [ ] Crear schema Drizzle en `db/schema.ts`
- [ ] Definir campos requeridos según documentación Phase 2
- [ ] Crear migration para la nueva tabla

#### 2. Implementación de APIs
- [ ] Crear `/api/catalogs/medical-services/route.ts` (GET, POST, PUT)
- [ ] Crear `/api/catalogs/medical-services/[id]/route.ts` (GET individual, DELETE)
- [ ] Crear `/api/catalogs/medical-services/[id]/toggle-status/route.ts` (activar/desactivar)
- [ ] Implementar validaciones de backend

#### 3. Implementación del CRUD Frontend
- [ ] Crear `/app/(dashboard)/dashboard/admin/catalogs/medical-services/page.tsx`
- [ ] Implementar grid de datos (desktop + mobile)
- [ ] Implementar filtros de búsqueda (nombre, categoría, estado)
- [ ] Implementar modales CRUD (crear, editar, ver detalles, eliminar)
- [ ] Aplicar colores estándar para acciones (verde=activar, naranja=desactivar, rojo=eliminar)

#### 4. Seeding y Datos Iniciales
- [ ] Crear datos iniciales en `db/seed-fixed.ts`
- [ ] Definir servicios médicos básicos para pediatría
- [ ] Ejecutar migration y seeding

#### 5. Testing y Validación
- [ ] Probar todas las operaciones CRUD
- [ ] Verificar responsive design
- [ ] Validar colores y estándares UI
- [ ] Probar filtros y paginación

## Diseño Propuesto

### Schema de Servicios Médicos
```typescript
interface MedicalService {
  id: string;
  name: string;          // "Consulta General", "Vacunación", "Procedimiento Menor"
  description: string;   // Descripción del servicio
  code: string;          // Código interno único
  category: string;      // "Consulta", "Procedimiento", "Emergencia"
  basePrice: number;     // Precio base sugerido
  currency: string;      // "GTQ" (referencia a currencies)
  duration: number;      // Duración estimada en minutos
  requiresEquipment: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

### Categorías Propuestas
- **Consulta**: Consulta general, consulta especializada, teleconsulta
- **Procedimiento**: Procedimiento menor, procedimiento mayor, biopsia
- **Emergencia**: Atención de urgencia, estabilización, reanimación
- **Preventivo**: Vacunación, control de niño sano, chequeo general
- **Diagnóstico**: Estudios de laboratorio, radiografías, ultrasonidos

### API Endpoints
- `GET /api/catalogs/medical-services` - Listar con filtros y paginación
- `POST /api/catalogs/medical-services` - Crear nuevo servicio
- `PUT /api/catalogs/medical-services` - Actualizar servicio existente
- `GET /api/catalogs/medical-services/[id]` - Obtener servicio específico
- `DELETE /api/catalogs/medical-services/[id]` - Eliminar servicio
- `POST /api/catalogs/medical-services/[id]/toggle-status` - Activar/desactivar

## Estimación de Tiempo
- **Schema y Migration**: 1 hora
- **APIs Backend**: 2 horas
- **Frontend CRUD**: 3 horas
- **Seeding y Testing**: 1 hora
- **Total**: ~7 horas

## Criterios de Completitud
- [ ] Schema creado y migrado exitosamente
- [ ] APIs funcionando con validaciones
- [ ] CRUD frontend completo y responsivo
- [ ] Datos iniciales cargados
- [ ] Estándares UI/UX aplicados (colores, iconos, layout)
- [ ] Filtros y paginación funcionando
- [ ] Todas las pruebas pasando