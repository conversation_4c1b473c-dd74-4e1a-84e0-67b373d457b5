import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { doctorScheduleExceptions } from '@/db/schema';
import { eq, and, gte, lte } from 'drizzle-orm';
import { randomBytes } from 'crypto';

function generateId(): string {
  return randomBytes(16).toString('hex');
}

// GET - Obtener excepciones del doctor
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    let query = db.select()
      .from(doctorScheduleExceptions)
      .where(eq(doctorScheduleExceptions.doctorId, userId));

    // Aplicar filtros de fecha si se proporcionan
    if (startDate) {
      query = query.where(
        and(
          eq(doctorScheduleExceptions.doctorId, userId),
          gte(doctorScheduleExceptions.exceptionDate, new Date(startDate))
        )
      );
    }

    if (endDate) {
      query = query.where(
        and(
          eq(doctorScheduleExceptions.doctorId, userId),
          lte(doctorScheduleExceptions.exceptionDate, new Date(endDate))
        )
      );
    }

    const exceptions = await query.orderBy(doctorScheduleExceptions.exceptionDate);

    return NextResponse.json({
      success: true,
      data: exceptions,
    });

  } catch (error) {
    console.error('Error fetching schedule exceptions:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nueva excepción
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      exceptionDate,
      type,
      title,
      description,
      startTime,
      endTime,
      isAllDay,
      consultoryId,
    } = body;

    // Validaciones básicas
    if (!exceptionDate || !type || !title) {
      return NextResponse.json(
        { error: 'Fecha, tipo y título son requeridos' },
        { status: 400 }
      );
    }

    const validTypes = ['vacation', 'sick_leave', 'conference', 'special_hours', 'closed'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Tipo de excepción inválido' },
        { status: 400 }
      );
    }

    // Si es special_hours, requiere horarios
    if (type === 'special_hours' && (!startTime || !endTime)) {
      return NextResponse.json(
        { error: 'Horarios especiales requieren hora de inicio y fin' },
        { status: 400 }
      );
    }

    // Verificar si ya existe una excepción para esta fecha
    const existingException = await db.select()
      .from(doctorScheduleExceptions)
      .where(
        and(
          eq(doctorScheduleExceptions.doctorId, userId),
          eq(doctorScheduleExceptions.exceptionDate, new Date(exceptionDate))
        )
      )
      .limit(1);

    if (existingException.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe una excepción para esta fecha' },
        { status: 400 }
      );
    }

    // Crear nueva excepción
    const newException = await db.insert(doctorScheduleExceptions)
      .values({
        id: generateId(),
        doctorId: userId,
        consultoryId: consultoryId || null,
        exceptionDate: new Date(exceptionDate),
        type,
        title,
        description: description || null,
        startTime: startTime || null,
        endTime: endTime || null,
        isAllDay: isAllDay || false,
      })
      .returning();

    return NextResponse.json({
      success: true,
      data: newException[0],
      message: 'Excepción creada correctamente',
    });

  } catch (error) {
    console.error('Error creating schedule exception:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}