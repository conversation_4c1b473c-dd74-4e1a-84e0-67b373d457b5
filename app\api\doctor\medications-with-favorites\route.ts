import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medications, doctorFavoriteMedications } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener medicamentos con información de si son favoritos del doctor actual
    const medicationsWithFavorites = await db
      .select({
        id: medications.id,
        name: medications.name,
        activeIngredient: medications.activeIngredient,
        presentation: medications.presentation,
        description: medications.description,
        isActive: medications.isActive,
        createdAt: medications.createdAt,
        updatedAt: medications.updatedAt,
        // Determinar si es favorito usando CASE WHEN
        isFavorite: sql<boolean>`CASE WHEN ${doctorFavoriteMedications.id} IS NOT NULL THEN true ELSE false END`.as('isFavorite')
      })
      .from(medications)
      .leftJoin(
        doctorFavoriteMedications, 
        sql`${medications.id} = ${doctorFavoriteMedications.medicationId} AND ${doctorFavoriteMedications.doctorId} = ${userId}`
      )
      .where(eq(medications.isActive, true)) // Solo medicamentos activos
      .orderBy(medications.name);

    return NextResponse.json({
      success: true,
      data: medicationsWithFavorites
    });
  } catch (error) {
    console.error('Error al obtener medicamentos con favoritos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}