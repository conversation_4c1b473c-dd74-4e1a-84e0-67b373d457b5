import { Package, FileText, Clock, Truck } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function ProviderDashboard() {
  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dashboard Proveedor
        </h1>
        <p className="text-gray-600">
          Gestiona tus productos, órdenes y ventas desde aquí
        </p>
      </div>
      
      {/* Métricas principales */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              <PERSON>rden<PERSON>
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-orange-100 flex items-center justify-center">
              <Clock className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              --
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Requieren procesamiento</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Productos Disponibles
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Package className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              --
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">En catálogo activo</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Entregas del Mes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
              <Truck className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              --
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Entregas completadas</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Facturas Pendientes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-purple-100 flex items-center justify-center">
              <FileText className="h-5 w-5 md:h-6 md:w-6 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              --
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Por facturar</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Área para catálogo */}
      <Card>
        <CardHeader>
          <CardTitle>Mi Catálogo</CardTitle>
          <p className="text-sm text-muted-foreground">
            Gestiona tus productos y servicios disponibles
          </p>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 mb-4">
              Aquí se mostrarán tus productos y servicios.
            </p>
            <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
              Agregar Producto
            </button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}