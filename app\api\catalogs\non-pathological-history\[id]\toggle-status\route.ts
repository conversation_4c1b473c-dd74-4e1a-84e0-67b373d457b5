import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { nonPathologicalHistory } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Cambiar estado activo/inactivo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para cambiar estado de antecedentes no patológicos' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el antecedente no patológico existe
    const existingHistory = await db.select()
      .from(nonPathologicalHistory)
      .where(eq(nonPathologicalHistory.id, id))
      .limit(1);

    if (existingHistory.length === 0) {
      return NextResponse.json({ error: 'Antecedente no patológico no encontrado' }, { status: 404 });
    }

    const nonPathologicalHistoryData = existingHistory[0];
    const newStatus = !nonPathologicalHistoryData.isActive;

    // Actualizar estado
    const [updatedHistory] = await db.update(nonPathologicalHistory)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(nonPathologicalHistory.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedHistory,
      message: `Antecedente no patológico ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error cambiando estado del antecedente no patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}