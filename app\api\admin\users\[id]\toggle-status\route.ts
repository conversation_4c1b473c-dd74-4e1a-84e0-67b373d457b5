import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { clerkClient } from '@clerk/nextjs/server';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  // Obtener rol de los session claims (igual que en el middleware)
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// POST - Cambiar estado de usuario (activar/inactivar)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;
    const body = await request.json();
    const { newStatus } = body;

    // Validar el nuevo estado
    const validStatuses = ['active', 'inactive', 'suspended'];
    if (!validStatuses.includes(newStatus)) {
      return NextResponse.json(
        { success: false, error: 'Estado no válido' },
        { status: 400 }
      );
    }

    // Verificar que el usuario existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const currentUser = existingUser[0];

    let updatedUser;
    let updatedRoles;
    
    try {
      // 1. Actualizar en base de datos usando transacción
      await db.transaction(async (tx) => {
        // Actualizar usuario
        const userUpdateResult = await tx
          .update(user)
          .set({
            overallStatus: newStatus,
            updatedAt: new Date(),
          })
          .where(eq(user.id, userId))
          .returning();
        
        updatedUser = userUpdateResult[0];

        // Actualizar estado de todos los roles del usuario
        const roleStatus = newStatus === 'active' ? 'active' : 'inactive';
        await tx
          .update(userRoles)
          .set({
            status: roleStatus,
            updatedAt: new Date(),
          })
          .where(eq(userRoles.userId, userId));

        // Obtener roles actualizados
        updatedRoles = await tx
          .select()
          .from(userRoles)
          .where(eq(userRoles.userId, userId));
      });

      // 4. Intentar actualizar en Clerk (pero no fallar si hay error)
      try {
        const clerk = await clerkClient();
        const clerkUsers = await clerk.users.getUserList({
          limit: 500
        });
        
        const clerkUser = clerkUsers.find(
          u => u.privateMetadata?.internalUserId === userId
        );

        if (clerkUser) {
          await clerk.users.updateUser(clerkUser.id, {
            publicMetadata: {
              ...clerkUser.publicMetadata,
              status: newStatus,
              lastStatusChange: new Date().toISOString(),
            },
          });

          // Banear/desbanear usuario en Clerk según el estado
          if (newStatus === 'suspended' || newStatus === 'inactive') {
            if (!clerkUser.banned) {
              await clerk.users.banUser(clerkUser.id);
            }
          } else if (newStatus === 'active' && clerkUser.banned) {
            await clerk.users.unbanUser(clerkUser.id);
          }
        }
      } catch (clerkError) {
        console.error('Warning: Could not update Clerk user status:', clerkError);
        // No fallar la operación completa por errores de Clerk
      }

      const statusMessages = {
        active: 'Usuario activado exitosamente',
        inactive: 'Usuario inactivado exitosamente',
        suspended: 'Usuario suspendido exitosamente'
      };

      return NextResponse.json({
        success: true,
        data: {
          user: {
            ...updatedUser,
            roles: updatedRoles
          },
          message: statusMessages[newStatus as keyof typeof statusMessages]
        }
      });

    } catch (dbError) {
      console.error('Error updating user status in database:', dbError);
      return NextResponse.json(
        { success: false, error: 'Error al actualizar estado del usuario' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error toggling user status:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}