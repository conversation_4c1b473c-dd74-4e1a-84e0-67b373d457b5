'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format, startOfMonth, endOfMonth, eachDayOfInterval, isSameDay, addDays, startOfWeek, endOfWeek } from 'date-fns';
import { es } from 'date-fns/locale';
import { Calendar, Plus, Clock, Stethoscope } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AppointmentActionsMenu } from './appointment-actions-menu';
import { AppointmentTooltip } from './appointment-tooltip';

interface CalendarMonthViewProps {
  appointments: any[];
  currentDate: Date;
  onDateClick?: (date: Date) => void;
  onAppointmentClick?: (appointment: any) => void;
  onAppointmentEdit?: (appointment: any) => void;
  onAppointmentConfirm?: (appointment: any) => void;
  onAppointmentCancel?: (appointment: any) => void;
  onAppointmentDelete?: (appointment: any) => void;
  onAppointmentCheckIn?: (appointment: any) => void;
  onAppointmentNoShow?: (appointment: any) => void;
  onAppointmentStart?: (appointment: any) => void;
  onAppointmentComplete?: (appointment: any) => void;
  onAppointmentRevertNoShow?: (appointment: any) => void;
  onAppointmentRevertCompleted?: (appointment: any) => void;
  onViewPreCheckin?: (appointment: any) => void;
  onViewConsultation?: (appointment: any) => void;
  loading?: boolean;
  userRole?: 'doctor' | 'assistant' | 'admin';
}

const statusColors = {
  scheduled: 'bg-blue-500',
  pending_confirmation: 'bg-orange-500',
  confirmed: 'bg-[#50bed2]',
  checked_in: 'bg-teal-500',
  in_progress: 'bg-purple-500',
  completed: 'bg-gray-500',
  cancelled: 'bg-red-500',
  no_show: 'bg-pink-500',
  urgent: 'bg-red-600'
};

export function CalendarMonthView({ 
  appointments, 
  currentDate, 
  onDateClick, 
  onAppointmentClick,
  onAppointmentEdit,
  onAppointmentConfirm,
  onAppointmentCancel,
  onAppointmentDelete,
  onAppointmentCheckIn,
  onAppointmentNoShow,
  onAppointmentStart,
  onAppointmentComplete,
  onAppointmentRevertNoShow,
  onAppointmentRevertCompleted,
  onViewPreCheckin,
  onViewConsultation,
  loading = false,
  userRole = 'assistant'
}: CalendarMonthViewProps) {
  const [hoveredDate, setHoveredDate] = useState<Date | null>(null);

  // Obtener el rango completo del calendario (incluyendo días de meses anteriores/siguientes)
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const calendarStart = startOfWeek(monthStart, { locale: es });
  const calendarEnd = endOfWeek(monthEnd, { locale: es });
  const allDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  // Agrupar citas por fecha
  const appointmentsByDate = appointments.reduce((acc, appointment) => {
    const date = format(new Date(appointment.scheduledDate), 'yyyy-MM-dd');
    if (!acc[date]) {
      acc[date] = [];
    }
    acc[date].push(appointment);
    return acc;
  }, {} as Record<string, any[]>);

  // Obtener citas para una fecha específica
  const getAppointmentsForDate = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return appointmentsByDate[dateStr] || [];
  };

  // Obtener estadísticas por estado para una fecha
  const getStatusStats = (date: Date) => {
    const dayAppointments = getAppointmentsForDate(date);
    const stats = dayAppointments.reduce((acc, appointment) => {
      const status = appointment.status;
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    return stats;
  };

  const handleDateClick = (date: Date) => {
    if (format(date, 'M') === format(currentDate, 'M')) {
      onDateClick?.(date);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Clock className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Cargando calendario...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        {/* Header del mes */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b py-3 px-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Calendar className="h-6 w-6 text-blue-600" />
              <h3 className="text-xl font-bold text-gray-900">
                {format(currentDate, "MMMM 'de' yyyy", { locale: es })}
              </h3>
            </div>
            <div className="text-sm text-gray-600">
              {appointments.length} citas este mes
            </div>
          </div>
        </div>

        {/* Header de días de la semana */}
        <div className="grid grid-cols-7 border-b bg-gray-50">
          {['Dom', 'Lun', 'Mar', 'Mié', 'Jue', 'Vie', 'Sáb'].map((day) => (
            <div key={day} className="py-2 px-3 text-center text-sm font-medium text-gray-600 border-r last:border-r-0">
              {day}
            </div>
          ))}
        </div>

        {/* Grid de días */}
        <div className="grid grid-cols-7">
          {allDays.map((day) => {
            const isCurrentMonth = format(day, 'M') === format(currentDate, 'M');
            const isToday = isSameDay(day, new Date());
            const dayAppointments = getAppointmentsForDate(day);
            const hasAppointments = dayAppointments.length > 0;
            const statusStats = getStatusStats(day);
            const isHovered = hoveredDate && isSameDay(hoveredDate, day);

            return (
              <div
                key={day.toString()}
                className={cn(
                  "border-r border-b last:border-r-0 min-h-[120px] p-2 cursor-pointer transition-all duration-200",
                  isCurrentMonth 
                    ? "bg-white hover:bg-blue-50" 
                    : "bg-gray-50 text-gray-400 hover:bg-gray-100",
                  isHovered && "bg-blue-100 shadow-inner",
                  isToday && "bg-blue-50 ring-2 ring-blue-200"
                )}
                onClick={() => handleDateClick(day)}
                onMouseEnter={() => setHoveredDate(day)}
                onMouseLeave={() => setHoveredDate(null)}
              >
                {/* Número del día */}
                <div className="flex items-center justify-between mb-2">
                  <span className={cn(
                    "text-sm font-medium",
                    isToday && isCurrentMonth && "bg-blue-600 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs",
                    !isCurrentMonth && "text-gray-400"
                  )}>
                    {format(day, 'd')}
                  </span>
                  
                  {/* Botón agregar cita */}
                  {isCurrentMonth && isHovered && !hasAppointments && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 bg-blue-500 hover:bg-blue-600 text-white"
                      onClick={(e) => {
                        e.stopPropagation();
                        onDateClick?.(day);
                      }}
                    >
                      <Plus className="h-3 w-3" />
                    </Button>
                  )}
                </div>

                {/* Indicadores de citas */}
                {hasAppointments && (
                  <div className="space-y-1">
                    {/* Mostrar primeras 2-3 citas */}
                    {dayAppointments.slice(0, 3).map((appointment, index) => (
                      <AppointmentTooltip 
                        key={appointment.id} 
                        appointment={appointment}
                        side="top"
                      >
                        <div
                          className={cn(
                            "group text-xs p-1 rounded text-white font-medium truncate cursor-pointer hover:opacity-80 transition-opacity relative",
                            statusColors[appointment.status] || statusColors.scheduled
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            onAppointmentClick?.(appointment);
                          }}
                        >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 min-w-0 flex-1">
                            <Clock className="h-2 w-2 flex-shrink-0" />
                            <span className="flex-shrink-0">{format(new Date(appointment.startTime), 'HH:mm')}</span>
                            <span className="truncate">
                              {appointment.patientFirstName || appointment.activityTypeName || 'Sin paciente'}
                            </span>
                          </div>
                          
                          <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                            <AppointmentActionsMenu
                              appointment={appointment}
                              onView={onAppointmentClick}
                              onEdit={onAppointmentEdit}
                              onConfirm={onAppointmentConfirm}
                              onCancel={onAppointmentCancel}
                              onDelete={onAppointmentDelete}
                              onCheckIn={onAppointmentCheckIn}
                              onNoShow={onAppointmentNoShow}
                              onStart={onAppointmentStart}
                              onComplete={onAppointmentComplete}
                              onRevertNoShow={onAppointmentRevertNoShow}
                              onRevertCompleted={onAppointmentRevertCompleted}
                              onViewPreCheckin={onViewPreCheckin}
                              onViewConsultation={onViewConsultation}
                              userRole={userRole}
                              className="ml-1 flex-shrink-0 bg-white/20 hover:bg-white/30"
                            />
                          </div>
                        </div>
                        </div>
                      </AppointmentTooltip>
                    ))}
                    
                    {/* Mostrar contador si hay más citas */}
                    {dayAppointments.length > 3 && (
                      <div className="text-xs text-center text-gray-600 bg-gray-100 rounded p-1">
                        +{dayAppointments.length - 3} más
                      </div>
                    )}
                  </div>
                )}

                {/* Resumen de estados (para días con muchas citas) */}
                {hasAppointments && dayAppointments.length > 1 && (
                  <div className="flex gap-1 mt-2 flex-wrap">
                    {Object.entries(statusStats).map(([status, count]) => (
                      <div
                        key={status}
                        className={cn(
                          "w-2 h-2 rounded-full",
                          statusColors[status] || statusColors.scheduled
                        )}
                        title={`${count} citas ${status}`}
                      />
                    ))}
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Leyenda de colores */}
        <div className="bg-gray-50 border-t p-4">
          <div className="flex flex-wrap gap-4 text-xs">
            <div className="flex items-center gap-2">
              <span className="text-gray-600 font-medium">Estados:</span>
            </div>
            {Object.entries(statusColors).map(([status, color]) => (
              <div key={status} className="flex items-center gap-1">
                <div className={cn("w-3 h-3 rounded-full", color)} />
                <span className="text-gray-600 capitalize">
                  {status === 'scheduled' && 'Programada'}
                  {status === 'pending_confirmation' && 'Pendiente de Confirmación'}
                  {status === 'confirmed' && 'Confirmada'}
                  {status === 'checked_in' && 'Paciente llegó'}
                  {status === 'in_progress' && 'En consulta'}
                  {status === 'completed' && 'Completada'}
                  {status === 'cancelled' && 'Cancelada'}
                  {status === 'no_show' && 'No asistió'}
                  {status === 'urgent' && 'Urgente'}
                </span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}