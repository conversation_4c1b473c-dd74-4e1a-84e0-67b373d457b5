import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments } from '@/db/schema';
import { eq, and, gte, lte, count, or } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const now = new Date();
    
    // Calcular fechas para diferentes períodos
    const in48Hours = new Date(now.getTime() + (48 * 60 * 60 * 1000));
    const in24Hours = new Date(now.getTime() + (24 * 60 * 60 * 1000));
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today.getTime() + (24 * 60 * 60 * 1000));
    
    // 1. Citas que necesitan pre-checkin (48h antes y no enviado)
    const needsPreCheckin = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        eq(appointments.status, 'scheduled'),
        gte(appointments.scheduledDate, today),
        lte(appointments.scheduledDate, in48Hours),
        eq(appointments.preCheckinSent, false)
      ));

    // 2. Citas que necesitan recordatorio 24h (24h antes y no enviado)
    const needsReminder = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        eq(appointments.status, 'scheduled'),
        gte(appointments.scheduledDate, today),
        lte(appointments.scheduledDate, in24Hours),
        eq(appointments.reminderSent24h, false)
      ));

    // 3. Citas programadas pero no confirmadas (hoy y mañana)
    const needsConfirmation = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        eq(appointments.status, 'scheduled'),
        gte(appointments.scheduledDate, today),
        lte(appointments.scheduledDate, tomorrow)
      ));

    // 4. Pre-checkin enviados pero no completados (últimos 7 días)
    const sevenDaysAgo = new Date(now.getTime() - (7 * 24 * 60 * 60 * 1000));
    const preCheckinNotCompleted = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        eq(appointments.preCheckinSent, true),
        eq(appointments.preCheckinCompleted, false),
        gte(appointments.scheduledDate, sevenDaysAgo),
        gte(appointments.scheduledDate, today) // Solo citas futuras
      ));

    // 5. Estadísticas adicionales para el dashboard
    const todayAppointments = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        gte(appointments.scheduledDate, today),
        lte(appointments.scheduledDate, tomorrow)
      ));

    const waitingPatients = await db
      .select({ count: count() })
      .from(appointments)
      .where(and(
        eq(appointments.status, 'checked_in'),
        gte(appointments.scheduledDate, today),
        lte(appointments.scheduledDate, tomorrow)
      ));

    // Calcular total de tareas pendientes
    const totalPendingTasks = (
      (needsPreCheckin[0]?.count || 0) +
      (needsReminder[0]?.count || 0) +
      (needsConfirmation[0]?.count || 0) +
      (preCheckinNotCompleted[0]?.count || 0)
    );

    return NextResponse.json({
      data: {
        pendingTasks: {
          total: totalPendingTasks,
          breakdown: {
            needsPreCheckin: needsPreCheckin[0]?.count || 0,
            needsReminder: needsReminder[0]?.count || 0,
            needsConfirmation: needsConfirmation[0]?.count || 0,
            preCheckinNotCompleted: preCheckinNotCompleted[0]?.count || 0
          }
        },
        todayStats: {
          totalAppointments: todayAppointments[0]?.count || 0,
          waitingPatients: waitingPatients[0]?.count || 0,
          pendingConfirmations: needsConfirmation[0]?.count || 0
        }
      }
    });

  } catch (error) {
    console.error('Error fetching assistant stats:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}