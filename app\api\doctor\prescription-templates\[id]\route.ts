import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { prescriptionTemplates } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, content, category, isActive } = body;
    const templateId = params.id;

    // Validaciones
    if (!name || !name.trim()) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    if (!content || !content.trim()) {
      return NextResponse.json(
        { error: 'El contenido es requerido' },
        { status: 400 }
      );
    }

    if (name.length < 2) {
      return NextResponse.json(
        { error: 'El nombre debe tener al menos 2 caracteres' },
        { status: 400 }
      );
    }

    // Verificar que la plantilla pertenece al doctor actual
    const existingTemplate = await db
      .select()
      .from(prescriptionTemplates)
      .where(
        and(
          eq(prescriptionTemplates.id, templateId),
          eq(prescriptionTemplates.doctorId, userId)
        )
      )
      .limit(1);

    if (existingTemplate.length === 0) {
      return NextResponse.json(
        { error: 'Plantilla de receta no encontrada' },
        { status: 404 }
      );
    }

    // Actualizar la plantilla de receta
    const updatedTemplate = await db
      .update(prescriptionTemplates)
      .set({
        name: name.trim(),
        content: content.trim(),
        category: category?.trim() || null,
        isActive: isActive ?? true,
        updatedAt: new Date()
      })
      .where(eq(prescriptionTemplates.id, templateId))
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Plantilla de receta actualizada exitosamente',
      data: updatedTemplate[0]
    });
  } catch (error) {
    console.error('Error al actualizar plantilla de receta:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const templateId = params.id;

    // Verificar que la plantilla pertenece al doctor actual
    const existingTemplate = await db
      .select()
      .from(prescriptionTemplates)
      .where(
        and(
          eq(prescriptionTemplates.id, templateId),
          eq(prescriptionTemplates.doctorId, userId)
        )
      )
      .limit(1);

    if (existingTemplate.length === 0) {
      return NextResponse.json(
        { error: 'Plantilla de receta no encontrada' },
        { status: 404 }
      );
    }

    // Eliminar la plantilla de receta
    await db
      .delete(prescriptionTemplates)
      .where(eq(prescriptionTemplates.id, templateId));

    return NextResponse.json({
      success: true,
      message: 'Plantilla de receta eliminada exitosamente'
    });
  } catch (error) {
    console.error('Error al eliminar plantilla de receta:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}