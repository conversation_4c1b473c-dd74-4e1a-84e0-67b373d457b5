'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { FileText, Image, Download, Eye, X } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';

interface Document {
  name: string;
  url: string;
  type: 'image' | 'pdf';
}

interface DocumentViewerProps {
  documents: { [key: string]: string };
  role: string;
}

const DOCUMENT_LABELS: { [key: string]: string } = {
  licensePhoto: 'Licencia Médica',
  diplomaPhoto: 'Diploma',
  cvPdf: 'Currículum Vitae',
  certificatePhoto: 'Certificado',
  nitDocument: 'Documento NIT',
  commercialLicense: 'Licencia Comercial',
  catalog: 'Catálogo',
  insuranceCard: 'Tar<PERSON><PERSON> de Seguro',
  legalDocuments: 'Documentos Legales',
};

export function DocumentViewer({ documents, role }: DocumentViewerProps) {
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  if (!documents || Object.keys(documents).length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5" />
            <span>Documentos</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500 text-center py-4">No se han subido documentos</p>
        </CardContent>
      </Card>
    );
  }

  const renderDocument = (key: string, url: string) => {
    const isPdf = url.toLowerCase().includes('.pdf') || key.includes('pdf');
    const label = DOCUMENT_LABELS[key] || key;

    return (
      <div key={key} className="border rounded-lg p-4 space-y-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isPdf ? (
              <FileText className="h-5 w-5 text-red-500" />
            ) : (
              <Image className="h-5 w-5 text-blue-500" />
            )}
            <span className="font-medium">{label}</span>
          </div>
          <Badge variant={isPdf ? "destructive" : "secondary"}>
            {isPdf ? 'PDF' : 'Imagen'}
          </Badge>
        </div>

        <div className="flex space-x-2">
          {isPdf ? (
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.open(url, '_blank')}
              className="flex items-center space-x-1"
            >
              <Eye className="h-4 w-4" />
              <span>Ver PDF</span>
            </Button>
          ) : (
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center space-x-1"
                >
                  <Eye className="h-4 w-4" />
                  <span>Ver Imagen</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
                <DialogHeader>
                  <DialogTitle>{label}</DialogTitle>
                </DialogHeader>
                <div className="mt-4">
                  <img
                    src={url}
                    alt={label}
                    className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
                  />
                </div>
              </DialogContent>
            </Dialog>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              const link = document.createElement('a');
              link.href = url;
              link.download = `${label}.${isPdf ? 'pdf' : 'jpg'}`;
              link.click();
            }}
            className="flex items-center space-x-1"
          >
            <Download className="h-4 w-4" />
            <span>Descargar</span>
          </Button>
        </div>

        {!isPdf && (
          <div className="mt-2">
            <img
              src={url}
              alt={label}
              className="w-full h-32 object-cover rounded-md cursor-pointer hover:opacity-80 transition-opacity"
              onClick={() => setSelectedImage(url)}
            />
          </div>
        )}
      </div>
    );
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center space-x-2">
          <FileText className="h-5 w-5" />
          <span>Documentos Subidos</span>
          <Badge variant="outline">{Object.keys(documents).length} archivos</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Object.entries(documents).map(([key, url]) => renderDocument(key, url))}
        </div>
      </CardContent>

      {/* Image preview modal */}
      {selectedImage && (
        <Dialog open={!!selectedImage} onOpenChange={() => setSelectedImage(null)}>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
            <DialogHeader>
              <DialogTitle className="flex items-center justify-between">
                <span>Vista Previa</span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedImage(null)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </DialogTitle>
            </DialogHeader>
            <div className="mt-4">
              <img
                src={selectedImage}
                alt="Preview"
                className="w-full h-auto max-h-[70vh] object-contain rounded-lg"
              />
            </div>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}