import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { companies } from '@/db/schema';
import { eq, ilike, and, or, desc, asc, count, sql } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// Función para validar NIT guatemalteco
function validateGuatemalanNIT(nit: string): boolean {
  // Formato: 8 dígitos + guión + 1 dígito verificador (ej: 12345678-9)
  const nitRegex = /^\d{8}-\d{1}$/;
  if (!nitRegex.test(nit)) return false;

  // Validar dígito verificador
  const [digits, checkDigit] = nit.split('-');
  const factors = [2, 3, 4, 5, 6, 7, 8, 9];
  let sum = 0;
  
  for (let i = 0; i < 8; i++) {
    sum += parseInt(digits[i]) * factors[i];
  }
  
  const remainder = sum % 11;
  const calculatedCheckDigit = remainder < 2 ? remainder : 11 - remainder;
  
  return calculatedCheckDigit === parseInt(checkDigit);
}

// GET - Listar empresas con paginación y filtros
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all';
    const activityType = searchParams.get('activityType') || 'all';
    const sortBy = searchParams.get('sortBy') || 'businessName';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const offset = (page - 1) * limit;

    // Construir condiciones de filtro
    const conditions = [];

    if (search) {
      conditions.push(
        or(
          ilike(companies.businessName, `%${search}%`),
          ilike(companies.commercialName, `%${search}%`),
          ilike(companies.nit, `%${search}%`),
          ilike(companies.legalRepresentative, `%${search}%`)
        )
      );
    }

    if (status !== 'all') {
      conditions.push(eq(companies.isActive, status === 'active'));
    }

    if (activityType !== 'all') {
      conditions.push(ilike(companies.activityType, `%${activityType}%`));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Obtener total de registros
    const totalResult = await db
      .select({ count: count() })
      .from(companies)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Obtener registros con paginación
    const orderColumn = companies[sortBy as keyof typeof companies] || companies.businessName;
    const orderDirection = sortOrder === 'desc' ? desc(orderColumn) : asc(orderColumn);

    const results = await db
      .select()
      .from(companies)
      .where(whereClause)
      .orderBy(orderDirection)
      .limit(limit)
      .offset(offset);

    // Obtener tipos de actividad únicos para filtros
    const activityTypesResult = await db
      .selectDistinct({
        activityType: companies.activityType
      })
      .from(companies)
      .where(eq(companies.isActive, true));

    const activityTypes = activityTypesResult
      .map(item => item.activityType)
      .filter(type => type && type.trim() !== '')
      .sort();

    return NextResponse.json({
      data: results,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      activityTypes,
    });

  } catch (error) {
    console.error('Error fetching companies:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nueva empresa
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    
    // Validaciones requeridas
    if (!body.businessName?.trim()) {
      return NextResponse.json(
        { error: 'La razón social es requerida' },
        { status: 400 }
      );
    }

    if (!body.commercialName?.trim()) {
      return NextResponse.json(
        { error: 'El nombre comercial es requerido' },
        { status: 400 }
      );
    }

    if (!body.nit?.trim()) {
      return NextResponse.json(
        { error: 'El NIT es requerido' },
        { status: 400 }
      );
    }

    if (!body.address?.trim()) {
      return NextResponse.json(
        { error: 'La dirección es requerida' },
        { status: 400 }
      );
    }

    if (!body.legalRepresentative?.trim()) {
      return NextResponse.json(
        { error: 'El representante legal es requerido' },
        { status: 400 }
      );
    }

    if (!body.activityType?.trim()) {
      return NextResponse.json(
        { error: 'El tipo de actividad es requerido' },
        { status: 400 }
      );
    }

    // Validar formato de NIT guatemalteco
    if (!validateGuatemalanNIT(body.nit)) {
      return NextResponse.json(
        { error: 'El formato del NIT es inválido. Debe ser: 12345678-9' },
        { status: 400 }
      );
    }

    // Verificar que el NIT no exista
    const existingCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.nit, body.nit))
      .limit(1);

    if (existingCompany.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe una empresa con este NIT' },
        { status: 400 }
      );
    }

    // Crear empresa
    const newCompany = {
      id: nanoid(),
      businessName: body.businessName.trim(),
      commercialName: body.commercialName.trim(),
      nit: body.nit.trim(),
      address: body.address.trim(),
      phone: body.phone?.trim() || null,
      email: body.email?.trim() || null,
      website: body.website?.trim() || null,
      legalRepresentative: body.legalRepresentative.trim(),
      activityType: body.activityType.trim(),
      taxRegime: body.taxRegime?.trim() || null,
      fiscalAddress: body.fiscalAddress?.trim() || null,
      contactPerson: body.contactPerson?.trim() || null,
      contactPhone: body.contactPhone?.trim() || null,
      contactEmail: body.contactEmail?.trim() || null,
      isActive: body.isActive ?? true,
    };

    const result = await db.insert(companies).values(newCompany).returning();

    return NextResponse.json({
      success: true,
      message: 'Empresa creada exitosamente',
      data: result[0],
    });

  } catch (error) {
    console.error('Error creating company:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar empresa
export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    
    if (!body.id) {
      return NextResponse.json(
        { error: 'ID de empresa requerido' },
        { status: 400 }
      );
    }

    // Validaciones requeridas
    if (!body.businessName?.trim()) {
      return NextResponse.json(
        { error: 'La razón social es requerida' },
        { status: 400 }
      );
    }

    if (!body.commercialName?.trim()) {
      return NextResponse.json(
        { error: 'El nombre comercial es requerido' },
        { status: 400 }
      );
    }

    if (!body.nit?.trim()) {
      return NextResponse.json(
        { error: 'El NIT es requerido' },
        { status: 400 }
      );
    }

    if (!body.address?.trim()) {
      return NextResponse.json(
        { error: 'La dirección es requerida' },
        { status: 400 }
      );
    }

    if (!body.legalRepresentative?.trim()) {
      return NextResponse.json(
        { error: 'El representante legal es requerido' },
        { status: 400 }
      );
    }

    if (!body.activityType?.trim()) {
      return NextResponse.json(
        { error: 'El tipo de actividad es requerido' },
        { status: 400 }
      );
    }

    // Validar formato de NIT guatemalteco
    if (!validateGuatemalanNIT(body.nit)) {
      return NextResponse.json(
        { error: 'El formato del NIT es inválido. Debe ser: 12345678-9' },
        { status: 400 }
      );
    }

    // Verificar que la empresa existe
    const existingCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.id, body.id))
      .limit(1);

    if (existingCompany.length === 0) {
      return NextResponse.json(
        { error: 'Empresa no encontrada' },
        { status: 404 }
      );
    }

    // Verificar que el NIT no exista en otra empresa
    const duplicateNIT = await db
      .select()
      .from(companies)
      .where(and(
        eq(companies.nit, body.nit),
        sql`${companies.id} != ${body.id}`
      ))
      .limit(1);

    if (duplicateNIT.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe otra empresa con este NIT' },
        { status: 400 }
      );
    }

    // Actualizar empresa
    const updatedData = {
      businessName: body.businessName.trim(),
      commercialName: body.commercialName.trim(),
      nit: body.nit.trim(),
      address: body.address.trim(),
      phone: body.phone?.trim() || null,
      email: body.email?.trim() || null,
      website: body.website?.trim() || null,
      legalRepresentative: body.legalRepresentative.trim(),
      activityType: body.activityType.trim(),
      taxRegime: body.taxRegime?.trim() || null,
      fiscalAddress: body.fiscalAddress?.trim() || null,
      contactPerson: body.contactPerson?.trim() || null,
      contactPhone: body.contactPhone?.trim() || null,
      contactEmail: body.contactEmail?.trim() || null,
      isActive: body.isActive ?? true,
      updatedAt: new Date(),
    };

    const result = await db
      .update(companies)
      .set(updatedData)
      .where(eq(companies.id, body.id))
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Empresa actualizada exitosamente',
      data: result[0],
    });

  } catch (error) {
    console.error('Error updating company:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}