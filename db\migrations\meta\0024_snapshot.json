{"id": "55951217-72aa-42e2-9173-0ca4e003c46e", "prevId": "8850864b-1c82-44d6-83ee-6a9a414bd2f8", "version": "7", "dialect": "postgresql", "tables": {"public.activity_types": {"name": "activity_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "requiresPatient": {"name": "requiresPatient", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "allowsRecurrence": {"name": "allowsRecurrence", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "respectsSchedule": {"name": "respectsSchedule", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"activity_types_category_idx": {"name": "activity_types_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_types_active_idx": {"name": "activity_types_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_types_order_idx": {"name": "activity_types_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.admin_audit_logs": {"name": "admin_audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "action": {"name": "action", "type": "text", "primaryKey": false, "notNull": true}, "executed_by": {"name": "executed_by", "type": "text", "primaryKey": false, "notNull": true}, "executed_by_name": {"name": "executed_by_name", "type": "text", "primaryKey": false, "notNull": true}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "success": {"name": "success", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"admin_audit_logs_action_idx": {"name": "admin_audit_logs_action_idx", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_audit_logs_executed_by_idx": {"name": "admin_audit_logs_executed_by_idx", "columns": [{"expression": "executed_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_audit_logs_timestamp_idx": {"name": "admin_audit_logs_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "admin_audit_logs_success_idx": {"name": "admin_audit_logs_success_idx", "columns": [{"expression": "success", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"admin_audit_logs_executed_by_user_id_fk": {"name": "admin_audit_logs_executed_by_user_id_fk", "tableFrom": "admin_audit_logs", "tableTo": "user", "columnsFrom": ["executed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointment_logs": {"name": "appointment_logs", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "appointment_id": {"name": "appointment_id", "type": "text", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "text", "primaryKey": false, "notNull": true}, "event_category": {"name": "event_category", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "triggered_by": {"name": "triggered_by", "type": "text", "primaryKey": false, "notNull": false}, "triggered_by_role": {"name": "triggered_by_role", "type": "text", "primaryKey": false, "notNull": false}, "previous_state": {"name": "previous_state", "type": "jsonb", "primaryKey": false, "notNull": false}, "new_state": {"name": "new_state", "type": "jsonb", "primaryKey": false, "notNull": false}, "email_type": {"name": "email_type", "type": "text", "primaryKey": false, "notNull": false}, "email_recipient": {"name": "email_recipient", "type": "text", "primaryKey": false, "notNull": false}, "email_status": {"name": "email_status", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "text", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"appointment_logs_appointment_idx": {"name": "appointment_logs_appointment_idx", "columns": [{"expression": "appointment_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_logs_event_type_idx": {"name": "appointment_logs_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_logs_event_category_idx": {"name": "appointment_logs_event_category_idx", "columns": [{"expression": "event_category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_logs_created_at_idx": {"name": "appointment_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_logs_triggered_by_idx": {"name": "appointment_logs_triggered_by_idx", "columns": [{"expression": "triggered_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"appointment_logs_appointment_id_appointments_id_fk": {"name": "appointment_logs_appointment_id_appointments_id_fk", "tableFrom": "appointment_logs", "tableTo": "appointments", "columnsFrom": ["appointment_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "appointment_logs_triggered_by_user_id_fk": {"name": "appointment_logs_triggered_by_user_id_fk", "tableFrom": "appointment_logs", "tableTo": "user", "columnsFrom": ["triggered_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointment_slots": {"name": "appointment_slots", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp", "primaryKey": false, "notNull": true}, "startTime": {"name": "startTime", "type": "timestamp", "primaryKey": false, "notNull": true}, "endTime": {"name": "endTime", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "isAvailable": {"name": "isAvailable", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "isBlocked": {"name": "isBlocked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "blockReason": {"name": "blockReason", "type": "text", "primaryKey": false, "notNull": false}, "isRecurring": {"name": "isRecurring", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "recurrencePattern": {"name": "recurrencePattern", "type": "text", "primaryKey": false, "notNull": false}, "maxAppointments": {"name": "maxAppointments", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "allowedServiceTypes": {"name": "allowedServiceTypes", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "isEmergencyOnly": {"name": "isEmergencyOnly", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"appointment_slots_doctor_date_idx": {"name": "appointment_slots_doctor_date_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_slots_consultory_date_idx": {"name": "appointment_slots_consultory_date_idx", "columns": [{"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_slots_available_idx": {"name": "appointment_slots_available_idx", "columns": [{"expression": "isAvailable", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_slots_time_range_idx": {"name": "appointment_slots_time_range_idx", "columns": [{"expression": "startTime", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "endTime", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointment_slots_emergency_idx": {"name": "appointment_slots_emergency_idx", "columns": [{"expression": "isEmergencyOnly", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"appointment_slots_doctorId_user_id_fk": {"name": "appointment_slots_doctorId_user_id_fk", "tableFrom": "appointment_slots", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointment_slots_consultoryId_consultories_id_fk": {"name": "appointment_slots_consultoryId_consultories_id_fk", "tableFrom": "appointment_slots", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointment_slots_createdBy_user_id_fk": {"name": "appointment_slots_createdBy_user_id_fk", "tableFrom": "appointment_slots", "tableTo": "user", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.appointments": {"name": "appointments", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": false}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": true}, "serviceId": {"name": "serviceId", "type": "text", "primaryKey": false, "notNull": false}, "activityTypeId": {"name": "activityTypeId", "type": "text", "primaryKey": false, "notNull": false}, "chiefComplaint": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "scheduledDate": {"name": "scheduledDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "startTime": {"name": "startTime", "type": "timestamp", "primaryKey": false, "notNull": true}, "endTime": {"name": "endTime", "type": "timestamp", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "confirmationStatus": {"name": "confirmation<PERSON>tatus", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "checkedInAt": {"name": "checkedInAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "checkedInBy": {"name": "checkedInBy", "type": "text", "primaryKey": false, "notNull": false}, "startedAt": {"name": "startedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "completedAt": {"name": "completedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "noShowReason": {"name": "noShowReason", "type": "text", "primaryKey": false, "notNull": false}, "estimatedPrice": {"name": "estimatedPrice", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "finalPrice": {"name": "finalPrice", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'GTQ'"}, "paymentStatus": {"name": "paymentStatus", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "doctorNotes": {"name": "doctor<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "adminNotes": {"name": "adminNotes", "type": "text", "primaryKey": false, "notNull": false}, "cancellationReason": {"name": "cancellationReason", "type": "text", "primaryKey": false, "notNull": false}, "isFollowUp": {"name": "isFollowUp", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "parentAppointmentId": {"name": "parentAppointmentId", "type": "text", "primaryKey": false, "notNull": false}, "isEmergency": {"name": "isEmergency", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requiresReminder": {"name": "requiresReminder", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "preCheckinSent": {"name": "preCheckinSent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "preCheckinCompleted": {"name": "preCheckinCompleted", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "preCheckinToken": {"name": "preCheckinToken", "type": "text", "primaryKey": false, "notNull": false}, "preCheckinCompletedAt": {"name": "preCheckinCompletedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "preCheckinCompletedBy": {"name": "preCheckinCompletedBy", "type": "text", "primaryKey": false, "notNull": false}, "preCheckinData": {"name": "preCheckinData", "type": "jsonb", "primaryKey": false, "notNull": false}, "reminderSent48h": {"name": "reminderSent48h", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "reminderSent24h": {"name": "reminderSent24h", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "invitationSent": {"name": "invitationSent", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "shortCode": {"name": "shortCode", "type": "text", "primaryKey": false, "notNull": false}, "emailCaptured": {"name": "emailCaptured", "type": "text", "primaryKey": false, "notNull": false}, "confirmedAt": {"name": "confirmedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "confirmedVia": {"name": "confirmedVia", "type": "text", "primaryKey": false, "notNull": false}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "updatedBy": {"name": "updatedBy", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"appointments_doctor_idx": {"name": "appointments_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_patient_idx": {"name": "appointments_patient_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_consultory_idx": {"name": "appointments_consultory_idx", "columns": [{"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_status_idx": {"name": "appointments_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_date_idx": {"name": "appointments_date_idx", "columns": [{"expression": "scheduledDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_time_range_idx": {"name": "appointments_time_range_idx", "columns": [{"expression": "startTime", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "endTime", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_emergency_idx": {"name": "appointments_emergency_idx", "columns": [{"expression": "isEmergency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_follow_up_idx": {"name": "appointments_follow_up_idx", "columns": [{"expression": "isFollowUp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_parent_idx": {"name": "appointments_parent_idx", "columns": [{"expression": "parentAppointmentId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "appointments_shortcode_idx": {"name": "appointments_shortcode_idx", "columns": [{"expression": "shortCode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"appointments_doctorId_user_id_fk": {"name": "appointments_doctorId_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_patientId_user_id_fk": {"name": "appointments_patientId_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_consultoryId_consultories_id_fk": {"name": "appointments_consultoryId_consultories_id_fk", "tableFrom": "appointments", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_serviceId_medical_services_id_fk": {"name": "appointments_serviceId_medical_services_id_fk", "tableFrom": "appointments", "tableTo": "medical_services", "columnsFrom": ["serviceId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_activityTypeId_activity_types_id_fk": {"name": "appointments_activityTypeId_activity_types_id_fk", "tableFrom": "appointments", "tableTo": "activity_types", "columnsFrom": ["activityTypeId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_checkedInBy_user_id_fk": {"name": "appointments_checkedInBy_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["checkedInBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_parentAppointmentId_appointments_id_fk": {"name": "appointments_parentAppointmentId_appointments_id_fk", "tableFrom": "appointments", "tableTo": "appointments", "columnsFrom": ["parentAppointmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_createdBy_user_id_fk": {"name": "appointments_createdBy_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "appointments_updatedBy_user_id_fk": {"name": "appointments_updatedBy_user_id_fk", "tableFrom": "appointments", "tableTo": "user", "columnsFrom": ["updatedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"appointments_shortCode_unique": {"name": "appointments_shortCode_unique", "nullsNotDistinct": false, "columns": ["shortCode"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assistant_doctor_relations": {"name": "assistant_doctor_relations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "assistantId": {"name": "assistantId", "type": "text", "primaryKey": false, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"assistant_doctor_idx": {"name": "assistant_doctor_idx", "columns": [{"expression": "assistantId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "assistant_relations_idx": {"name": "assistant_relations_idx", "columns": [{"expression": "assistantId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_relations_idx": {"name": "doctor_relations_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assistant_doctor_relations_assistantId_user_id_fk": {"name": "assistant_doctor_relations_assistantId_user_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "user", "columnsFrom": ["assistantId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assistant_doctor_relations_doctorId_user_id_fk": {"name": "assistant_doctor_relations_doctorId_user_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assistant_doctor_relations_consultoryId_consultories_id_fk": {"name": "assistant_doctor_relations_consultoryId_consultories_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.association_codes": {"name": "association_codes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "usedBy": {"name": "usedBy", "type": "text", "primaryKey": false, "notNull": false}, "usedAt": {"name": "usedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"association_code_idx": {"name": "association_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "association_patient_idx": {"name": "association_patient_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "association_expires_idx": {"name": "association_expires_idx", "columns": [{"expression": "expiresAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"association_codes_patientId_user_id_fk": {"name": "association_codes_patientId_user_id_fk", "tableFrom": "association_codes", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "association_codes_usedBy_user_id_fk": {"name": "association_codes_usedBy_user_id_fk", "tableFrom": "association_codes", "tableTo": "user", "columnsFrom": ["usedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"association_codes_code_unique": {"name": "association_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.companies": {"name": "companies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "businessName": {"name": "businessName", "type": "text", "primaryKey": false, "notNull": true}, "commercialName": {"name": "commercialName", "type": "text", "primaryKey": false, "notNull": true}, "nit": {"name": "nit", "type": "text", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "text", "primaryKey": false, "notNull": false}, "legalRepresentative": {"name": "legalRepresentative", "type": "text", "primaryKey": false, "notNull": true}, "activityType": {"name": "activityType", "type": "text", "primaryKey": false, "notNull": true}, "taxRegime": {"name": "taxRegime", "type": "text", "primaryKey": false, "notNull": false}, "fiscalAddress": {"name": "fiscalAddress", "type": "text", "primaryKey": false, "notNull": false}, "contactPerson": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "contactPhone": {"name": "contactPhone", "type": "text", "primaryKey": false, "notNull": false}, "contactEmail": {"name": "contactEmail", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"companies_nit_idx": {"name": "companies_nit_idx", "columns": [{"expression": "nit", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "companies_active_idx": {"name": "companies_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "companies_business_name_idx": {"name": "companies_business_name_idx", "columns": [{"expression": "businessName", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "companies_activity_type_idx": {"name": "companies_activity_type_idx", "columns": [{"expression": "activityType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"companies_nit_unique": {"name": "companies_nit_unique", "nullsNotDistinct": false, "columns": ["nit"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.consultories": {"name": "consultories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "specialty": {"name": "specialty", "type": "text", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "floor": {"name": "floor", "type": "integer", "primaryKey": false, "notNull": false}, "building": {"name": "building", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "logoUrl": {"name": "logoUrl", "type": "text", "primaryKey": false, "notNull": false}, "logoPublicId": {"name": "logoPublicId", "type": "text", "primaryKey": false, "notNull": false}, "color_palette": {"name": "color_palette", "type": "jsonb", "primaryKey": false, "notNull": false}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": false}, "departmentId": {"name": "departmentId", "type": "integer", "primaryKey": false, "notNull": false}, "municipalityId": {"name": "municipalityId", "type": "integer", "primaryKey": false, "notNull": false}, "businessHours": {"name": "businessHours", "type": "jsonb", "primaryKey": false, "notNull": false}, "equipment": {"name": "equipment", "type": "jsonb", "primaryKey": false, "notNull": false}, "services": {"name": "services", "type": "jsonb", "primaryKey": false, "notNull": false}, "regional_settings": {"name": "regional_settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "isEmergencyCapable": {"name": "isEmergencyCapable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hasAirConditioning": {"name": "hasAirConditioning", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "hasWaitingRoom": {"name": "hasWaitingRoom", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "accessibility": {"name": "accessibility", "type": "jsonb", "primaryKey": false, "notNull": false}, "isPrimary": {"name": "isPrimary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"consultories_active_idx": {"name": "consultories_active_idx", "columns": [{"expression": "active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_is_active_idx": {"name": "consultories_is_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_code_idx": {"name": "consultories_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "consultories_type_idx": {"name": "consultories_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_building_idx": {"name": "consultories_building_idx", "columns": [{"expression": "building", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_emergency_idx": {"name": "consultories_emergency_idx", "columns": [{"expression": "isEmergencyCapable", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"consultories_countryId_countries_id_fk": {"name": "consultories_countryId_countries_id_fk", "tableFrom": "consultories", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consultories_departmentId_departments_id_fk": {"name": "consultories_departmentId_departments_id_fk", "tableFrom": "consultories", "tableTo": "departments", "columnsFrom": ["departmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "consultories_municipalityId_municipalities_id_fk": {"name": "consultories_municipalityId_municipalities_id_fk", "tableFrom": "consultories", "tableTo": "municipalities", "columnsFrom": ["municipalityId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"consultories_code_unique": {"name": "consultories_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "phoneCode": {"name": "phoneCode", "type": "text", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"countries_code_unique": {"name": "countries_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currencies": {"name": "currencies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true}, "exchangeRate": {"name": "exchangeRate", "type": "real", "primaryKey": false, "notNull": true, "default": 1}, "isDefault": {"name": "isDefault", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currencies_code_idx": {"name": "currencies_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "currencies_active_idx": {"name": "currencies_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currencies_default_idx": {"name": "currencies_default_idx", "columns": [{"expression": "isDefault", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"currencies_code_unique": {"name": "currencies_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.departments": {"name": "departments", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"departments_countryId_countries_id_fk": {"name": "departments_countryId_countries_id_fk", "tableFrom": "departments", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.doctor_favorite_medications": {"name": "doctor_favorite_medications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "medicationId": {"name": "medicationId", "type": "text", "primaryKey": false, "notNull": true}, "dosage": {"name": "dosage", "type": "text", "primaryKey": false, "notNull": false}, "frequency": {"name": "frequency", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"doctor_medication_idx": {"name": "doctor_medication_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "medicationId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "doctor_favorite_medications_doctor_idx": {"name": "doctor_favorite_medications_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_favorite_medications_medication_idx": {"name": "doctor_favorite_medications_medication_idx", "columns": [{"expression": "medicationId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"doctor_favorite_medications_doctorId_user_id_fk": {"name": "doctor_favorite_medications_doctorId_user_id_fk", "tableFrom": "doctor_favorite_medications", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doctor_favorite_medications_medicationId_medications_id_fk": {"name": "doctor_favorite_medications_medicationId_medications_id_fk", "tableFrom": "doctor_favorite_medications", "tableTo": "medications", "columnsFrom": ["medicationId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.doctor_schedule_exceptions": {"name": "doctor_schedule_exceptions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "exceptionDate": {"name": "exceptionDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "startTime": {"name": "startTime", "type": "text", "primaryKey": false, "notNull": false}, "endTime": {"name": "endTime", "type": "text", "primaryKey": false, "notNull": false}, "isAllDay": {"name": "isAllDay", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"doctor_schedule_exceptions_doctor_idx": {"name": "doctor_schedule_exceptions_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedule_exceptions_consultory_idx": {"name": "doctor_schedule_exceptions_consultory_idx", "columns": [{"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedule_exceptions_date_idx": {"name": "doctor_schedule_exceptions_date_idx", "columns": [{"expression": "exceptionDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedule_exceptions_type_idx": {"name": "doctor_schedule_exceptions_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedule_exceptions_doctor_date_idx": {"name": "doctor_schedule_exceptions_doctor_date_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "exceptionDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"doctor_schedule_exceptions_doctorId_user_id_fk": {"name": "doctor_schedule_exceptions_doctorId_user_id_fk", "tableFrom": "doctor_schedule_exceptions", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doctor_schedule_exceptions_consultoryId_consultories_id_fk": {"name": "doctor_schedule_exceptions_consultoryId_consultories_id_fk", "tableFrom": "doctor_schedule_exceptions", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.doctor_schedules": {"name": "doctor_schedules", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "dayOfWeek": {"name": "dayOfWeek", "type": "integer", "primaryKey": false, "notNull": true}, "startTime": {"name": "startTime", "type": "text", "primaryKey": false, "notNull": true}, "endTime": {"name": "endTime", "type": "text", "primaryKey": false, "notNull": true}, "lunchBreakStart": {"name": "lunchBreakStart", "type": "text", "primaryKey": false, "notNull": false}, "lunchBreakEnd": {"name": "lunchBreakEnd", "type": "text", "primaryKey": false, "notNull": false}, "appointmentDuration": {"name": "appointmentDuration", "type": "integer", "primaryKey": false, "notNull": false, "default": 30}, "maxAppointmentsPerHour": {"name": "maxAppointmentsPerHour", "type": "integer", "primaryKey": false, "notNull": false, "default": 2}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "allowEmergencies": {"name": "allowEmergencies", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "allowOnlineBooking": {"name": "allowOnlineBooking", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"doctor_schedules_doctor_idx": {"name": "doctor_schedules_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedules_consultory_idx": {"name": "doctor_schedules_consultory_idx", "columns": [{"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedules_day_idx": {"name": "doctor_schedules_day_idx", "columns": [{"expression": "dayOfWeek", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedules_active_idx": {"name": "doctor_schedules_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_schedules_doctor_day_idx": {"name": "doctor_schedules_doctor_day_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "dayOfWeek", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"doctor_schedules_doctorId_user_id_fk": {"name": "doctor_schedules_doctorId_user_id_fk", "tableFrom": "doctor_schedules", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doctor_schedules_consultoryId_consultories_id_fk": {"name": "doctor_schedules_consultoryId_consultories_id_fk", "tableFrom": "doctor_schedules", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.doctor_service_prices": {"name": "doctor_service_prices", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "serviceId": {"name": "serviceId", "type": "text", "primaryKey": false, "notNull": true}, "customPrice": {"name": "customPrice", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'GTQ'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "effectiveFrom": {"name": "effectiveFrom", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "effectiveUntil": {"name": "effectiveUntil", "type": "timestamp", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"doctor_service_active_idx": {"name": "doctor_service_active_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "serviceId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"doctor_service_prices\".\"isActive\" = true", "concurrently": false, "method": "btree", "with": {}}, "doctor_service_prices_doctor_idx": {"name": "doctor_service_prices_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_service_prices_service_idx": {"name": "doctor_service_prices_service_idx", "columns": [{"expression": "serviceId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_service_prices_active_idx": {"name": "doctor_service_prices_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_service_prices_price_idx": {"name": "doctor_service_prices_price_idx", "columns": [{"expression": "customPrice", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_service_prices_effective_idx": {"name": "doctor_service_prices_effective_idx", "columns": [{"expression": "effectiveFrom", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "effectiveUntil", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"doctor_service_prices_doctorId_user_id_fk": {"name": "doctor_service_prices_doctorId_user_id_fk", "tableFrom": "doctor_service_prices", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "doctor_service_prices_serviceId_medical_services_id_fk": {"name": "doctor_service_prices_serviceId_medical_services_id_fk", "tableFrom": "doctor_service_prices", "tableTo": "medical_services", "columnsFrom": ["serviceId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_types": {"name": "document_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "shortName": {"name": "shortName", "type": "text", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": true}, "countryName": {"name": "countryName", "type": "text", "primaryKey": false, "notNull": true}, "format": {"name": "format", "type": "text", "primaryKey": false, "notNull": true}, "maxLength": {"name": "max<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true}, "minLength": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true}, "isRequired": {"name": "isRequired", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "example": {"name": "example", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"document_types_country_idx": {"name": "document_types_country_idx", "columns": [{"expression": "countryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_types_active_idx": {"name": "document_types_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_types_required_idx": {"name": "document_types_required_idx", "columns": [{"expression": "isRequired", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_types_countryId_countries_id_fk": {"name": "document_types_countryId_countries_id_fk", "tableFrom": "document_types", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.education_levels": {"name": "education_levels", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"education_levels_order_idx": {"name": "education_levels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "education_levels_active_idx": {"name": "education_levels_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.guardian_patient_relations": {"name": "guardian_patient_relations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "guardianId": {"name": "guardianId", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "text", "primaryKey": false, "notNull": false}, "isPrimary": {"name": "isPrimary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "canMakeDecisions": {"name": "canMakeDecisions", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "validUntil": {"name": "validUntil", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"guardian_patient_idx": {"name": "guardian_patient_idx", "columns": [{"expression": "guardianId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "guardian_relations_idx": {"name": "guardian_relations_idx", "columns": [{"expression": "guardianId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "patient_relations_idx": {"name": "patient_relations_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"guardian_patient_relations_guardianId_user_id_fk": {"name": "guardian_patient_relations_guardianId_user_id_fk", "tableFrom": "guardian_patient_relations", "tableTo": "user", "columnsFrom": ["guardianId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "guardian_patient_relations_patientId_user_id_fk": {"name": "guardian_patient_relations_patientId_user_id_fk", "tableFrom": "guardian_patient_relations", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.marital_status": {"name": "marital_status", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "allowsSpouse": {"name": "allowsSpouse", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "legalImplications": {"name": "legalImplications", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"marital_status_order_idx": {"name": "marital_status_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "marital_status_active_idx": {"name": "marital_status_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marital_status_spouse_idx": {"name": "marital_status_spouse_idx", "columns": [{"expression": "allowsSpouse", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_sources": {"name": "media_sources", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "trackingEnabled": {"name": "trackingEnabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": false, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"media_sources_type_idx": {"name": "media_sources_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_category_idx": {"name": "media_sources_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_active_idx": {"name": "media_sources_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_tracking_idx": {"name": "media_sources_tracking_idx", "columns": [{"expression": "trackingEnabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_consultations": {"name": "medical_consultations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "medicalRecordId": {"name": "medicalRecordId", "type": "text", "primaryKey": false, "notNull": true}, "appointmentId": {"name": "appointmentId", "type": "text", "primaryKey": false, "notNull": false}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": true}, "consultationDate": {"name": "consultationDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "services": {"name": "services", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "chiefComplaint": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "currentIllness": {"name": "currentIllness", "type": "text", "primaryKey": false, "notNull": false}, "vitalSigns": {"name": "vitalSigns", "type": "jsonb", "primaryKey": false, "notNull": false}, "physicalExam": {"name": "physicalExam", "type": "jsonb", "primaryKey": false, "notNull": false}, "diagnoses": {"name": "diagnoses", "type": "jsonb", "primaryKey": false, "notNull": false}, "treatment": {"name": "treatment", "type": "jsonb", "primaryKey": false, "notNull": false}, "prescriptions": {"name": "prescriptions", "type": "jsonb", "primaryKey": false, "notNull": false}, "recommendations": {"name": "recommendations", "type": "text", "primaryKey": false, "notNull": false}, "followUpInstructions": {"name": "followUpInstructions", "type": "text", "primaryKey": false, "notNull": false}, "nextAppointment": {"name": "nextAppointment", "type": "timestamp", "primaryKey": false, "notNull": false}, "nextAppointmentValue": {"name": "nextAppointmentV<PERSON>ue", "type": "integer", "primaryKey": false, "notNull": false}, "nextAppointmentUnit": {"name": "nextAppointmentUnit", "type": "text", "primaryKey": false, "notNull": false}, "nextAppointmentType": {"name": "nextAppointmentType", "type": "text", "primaryKey": false, "notNull": false}, "nextAppointmentNotes": {"name": "nextAppointmentNotes", "type": "text", "primaryKey": false, "notNull": false}, "nextAppointmentCalculatedDate": {"name": "nextAppointmentCalculatedDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'completed'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "updatedBy": {"name": "updatedBy", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"medical_consultations_record_idx": {"name": "medical_consultations_record_idx", "columns": [{"expression": "medicalRecordId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_consultations_doctor_idx": {"name": "medical_consultations_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_consultations_appointment_idx": {"name": "medical_consultations_appointment_idx", "columns": [{"expression": "appointmentId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_consultations_date_idx": {"name": "medical_consultations_date_idx", "columns": [{"expression": "consultationDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_consultations_status_idx": {"name": "medical_consultations_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"medical_consultations_medicalRecordId_medical_records_id_fk": {"name": "medical_consultations_medicalRecordId_medical_records_id_fk", "tableFrom": "medical_consultations", "tableTo": "medical_records", "columnsFrom": ["medicalRecordId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_consultations_appointmentId_appointments_id_fk": {"name": "medical_consultations_appointmentId_appointments_id_fk", "tableFrom": "medical_consultations", "tableTo": "appointments", "columnsFrom": ["appointmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_consultations_doctorId_user_id_fk": {"name": "medical_consultations_doctorId_user_id_fk", "tableFrom": "medical_consultations", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_consultations_consultoryId_consultories_id_fk": {"name": "medical_consultations_consultoryId_consultories_id_fk", "tableFrom": "medical_consultations", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_consultations_createdBy_user_id_fk": {"name": "medical_consultations_createdBy_user_id_fk", "tableFrom": "medical_consultations", "tableTo": "user", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_consultations_updatedBy_user_id_fk": {"name": "medical_consultations_updatedBy_user_id_fk", "tableFrom": "medical_consultations", "tableTo": "user", "columnsFrom": ["updatedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_documents": {"name": "medical_documents", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "medicalRecordId": {"name": "medicalRecordId", "type": "text", "primaryKey": false, "notNull": true}, "consultationId": {"name": "consultationId", "type": "text", "primaryKey": false, "notNull": false}, "fileName": {"name": "fileName", "type": "text", "primaryKey": false, "notNull": true}, "originalName": {"name": "originalName", "type": "text", "primaryKey": false, "notNull": true}, "fileType": {"name": "fileType", "type": "text", "primaryKey": false, "notNull": true}, "fileSize": {"name": "fileSize", "type": "integer", "primaryKey": false, "notNull": true}, "filePath": {"name": "filePath", "type": "text", "primaryKey": false, "notNull": true}, "documentType": {"name": "documentType", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "documentDate": {"name": "documentDate", "type": "timestamp", "primaryKey": false, "notNull": true}, "relatedDiagnosis": {"name": "relatedDiagnosis", "type": "text", "primaryKey": false, "notNull": false}, "laboratory": {"name": "laboratory", "type": "text", "primaryKey": false, "notNull": false}, "physician": {"name": "physician", "type": "text", "primaryKey": false, "notNull": false}, "visibility": {"name": "visibility", "type": "text", "primaryKey": false, "notNull": false, "default": "'doctor_only'"}, "isConfidential": {"name": "isConfidential", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending_review'"}, "reviewedBy": {"name": "reviewedBy", "type": "text", "primaryKey": false, "notNull": false}, "reviewedDate": {"name": "reviewedDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewNotes": {"name": "reviewNotes", "type": "text", "primaryKey": false, "notNull": false}, "uploadedAt": {"name": "uploadedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "uploadedBy": {"name": "uploadedBy", "type": "text", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"medical_documents_record_idx": {"name": "medical_documents_record_idx", "columns": [{"expression": "medicalRecordId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_documents_consultation_idx": {"name": "medical_documents_consultation_idx", "columns": [{"expression": "consultationId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_documents_type_idx": {"name": "medical_documents_type_idx", "columns": [{"expression": "documentType", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_documents_status_idx": {"name": "medical_documents_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_documents_uploaded_idx": {"name": "medical_documents_uploaded_idx", "columns": [{"expression": "uploadedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_documents_date_idx": {"name": "medical_documents_date_idx", "columns": [{"expression": "documentDate", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"medical_documents_medicalRecordId_medical_records_id_fk": {"name": "medical_documents_medicalRecordId_medical_records_id_fk", "tableFrom": "medical_documents", "tableTo": "medical_records", "columnsFrom": ["medicalRecordId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_documents_consultationId_medical_consultations_id_fk": {"name": "medical_documents_consultationId_medical_consultations_id_fk", "tableFrom": "medical_documents", "tableTo": "medical_consultations", "columnsFrom": ["consultationId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_documents_reviewedBy_user_id_fk": {"name": "medical_documents_reviewedBy_user_id_fk", "tableFrom": "medical_documents", "tableTo": "user", "columnsFrom": ["reviewedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_documents_uploadedBy_user_id_fk": {"name": "medical_documents_uploadedBy_user_id_fk", "tableFrom": "medical_documents", "tableTo": "user", "columnsFrom": ["uploadedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_records": {"name": "medical_records", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": true}, "primaryDoctorId": {"name": "primaryDoctorId", "type": "text", "primaryKey": false, "notNull": true}, "recordNumber": {"name": "recordNumber", "type": "text", "primaryKey": false, "notNull": true}, "openDate": {"name": "openDate", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'active'"}, "patientSummary": {"name": "patient<PERSON><PERSON><PERSON><PERSON>", "type": "jsonb", "primaryKey": false, "notNull": false}, "demographics": {"name": "demographics", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "administrative": {"name": "administrative", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "emergency_contact": {"name": "emergency_contact", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'{}'::jsonb"}, "isMinor": {"name": "isMinor", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "guardianInfo": {"name": "guardianInfo", "type": "jsonb", "primaryKey": false, "notNull": false}, "selectedSymptoms": {"name": "selectedSymptoms", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "pathologicalHistory": {"name": "pathologicalHistory", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "nonPathologicalHistory": {"name": "nonPathologicalHistory", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "lastAccessDate": {"name": "lastAccessDate", "type": "timestamp", "primaryKey": false, "notNull": false}, "totalConsultations": {"name": "totalConsultations", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "createdBy": {"name": "created<PERSON>y", "type": "text", "primaryKey": false, "notNull": true}, "updatedBy": {"name": "updatedBy", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"medical_records_patient_idx": {"name": "medical_records_patient_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_records_doctor_idx": {"name": "medical_records_doctor_idx", "columns": [{"expression": "primaryDoctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_records_consultory_idx": {"name": "medical_records_consultory_idx", "columns": [{"expression": "consultoryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_records_status_idx": {"name": "medical_records_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_records_number_idx": {"name": "medical_records_number_idx", "columns": [{"expression": "recordNumber", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "medical_records_created_idx": {"name": "medical_records_created_idx", "columns": [{"expression": "createdAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"medical_records_patientId_user_id_fk": {"name": "medical_records_patientId_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_records_consultoryId_consultories_id_fk": {"name": "medical_records_consultoryId_consultories_id_fk", "tableFrom": "medical_records", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_records_primaryDoctorId_user_id_fk": {"name": "medical_records_primaryDoctorId_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["primaryDoctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_records_createdBy_user_id_fk": {"name": "medical_records_createdBy_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["created<PERSON>y"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "medical_records_updatedBy_user_id_fk": {"name": "medical_records_updatedBy_user_id_fk", "tableFrom": "medical_records", "tableTo": "user", "columnsFrom": ["updatedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"medical_records_recordNumber_unique": {"name": "medical_records_recordNumber_unique", "nullsNotDistinct": false, "columns": ["recordNumber"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_service_tags": {"name": "medical_service_tags", "schema": "", "columns": {"serviceId": {"name": "serviceId", "type": "text", "primaryKey": false, "notNull": true}, "tagId": {"name": "tagId", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"medical_service_tags_service_idx": {"name": "medical_service_tags_service_idx", "columns": [{"expression": "serviceId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_service_tags_tag_idx": {"name": "medical_service_tags_tag_idx", "columns": [{"expression": "tagId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"medical_service_tags_serviceId_medical_services_id_fk": {"name": "medical_service_tags_serviceId_medical_services_id_fk", "tableFrom": "medical_service_tags", "tableTo": "medical_services", "columnsFrom": ["serviceId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "medical_service_tags_tagId_service_tags_id_fk": {"name": "medical_service_tags_tagId_service_tags_id_fk", "tableFrom": "medical_service_tags", "tableTo": "service_tags", "columnsFrom": ["tagId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"medical_service_tags_serviceId_tagId_pk": {"name": "medical_service_tags_serviceId_tagId_pk", "columns": ["serviceId", "tagId"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_services": {"name": "medical_services", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "basePrice": {"name": "basePrice", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "text", "primaryKey": false, "notNull": false, "default": "'GTQ'"}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "requiresEquipment": {"name": "requiresEquipment", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requiresSpecialist": {"name": "requiresSpecialist", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"medical_services_code_idx": {"name": "medical_services_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "medical_services_category_idx": {"name": "medical_services_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_services_active_idx": {"name": "medical_services_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_services_name_idx": {"name": "medical_services_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medical_services_price_idx": {"name": "medical_services_price_idx", "columns": [{"expression": "basePrice", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"medical_services_code_unique": {"name": "medical_services_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_specialties": {"name": "medical_specialties", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medications": {"name": "medications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "genericName": {"name": "genericName", "type": "text", "primaryKey": false, "notNull": true}, "brandName": {"name": "brandName", "type": "text", "primaryKey": false, "notNull": false}, "activeIngredient": {"name": "activeIngredient", "type": "text", "primaryKey": false, "notNull": true}, "dosageForm": {"name": "dosageForm", "type": "text", "primaryKey": false, "notNull": true}, "strength": {"name": "strength", "type": "text", "primaryKey": false, "notNull": true}, "concentration": {"name": "concentration", "type": "text", "primaryKey": false, "notNull": false}, "therapeuticClass": {"name": "therapeuticClass", "type": "text", "primaryKey": false, "notNull": true}, "pharmacologicalGroup": {"name": "pharmacologicalGroup", "type": "text", "primaryKey": false, "notNull": false}, "atcCode": {"name": "atcCode", "type": "text", "primaryKey": false, "notNull": false}, "indication": {"name": "indication", "type": "text", "primaryKey": false, "notNull": false}, "contraindications": {"name": "contraindications", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "sideEffects": {"name": "sideEffects", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "dosageInstructions": {"name": "dosageInstructions", "type": "text", "primaryKey": false, "notNull": false}, "warnings": {"name": "warnings", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "requiresPrescription": {"name": "requiresPrescription", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "isControlled": {"name": "isControlled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "controlledCategory": {"name": "controlledCategory", "type": "text", "primaryKey": false, "notNull": false}, "manufacturer": {"name": "manufacturer", "type": "text", "primaryKey": false, "notNull": false}, "barcode": {"name": "barcode", "type": "text", "primaryKey": false, "notNull": false}, "ndc": {"name": "ndc", "type": "text", "primaryKey": false, "notNull": false}, "presentation": {"name": "presentation", "type": "text", "primaryKey": false, "notNull": false}, "storageConditions": {"name": "storageConditions", "type": "text", "primaryKey": false, "notNull": false}, "shelfLife": {"name": "shelfLife", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"medications_name_idx": {"name": "medications_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medications_generic_idx": {"name": "medications_generic_idx", "columns": [{"expression": "genericName", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medications_active_ingredient_idx": {"name": "medications_active_ingredient_idx", "columns": [{"expression": "activeIngredient", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medications_therapeutic_idx": {"name": "medications_therapeutic_idx", "columns": [{"expression": "therapeuticClass", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medications_controlled_idx": {"name": "medications_controlled_idx", "columns": [{"expression": "isControlled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "medications_active_idx": {"name": "medications_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.municipalities": {"name": "municipalities", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "departmentId": {"name": "departmentId", "type": "integer", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"municipalities_departmentId_departments_id_fk": {"name": "municipalities_departmentId_departments_id_fk", "tableFrom": "municipalities", "tableTo": "departments", "columnsFrom": ["departmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.non_pathological_history": {"name": "non_pathological_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "subcategory": {"name": "subcategory", "type": "text", "primaryKey": false, "notNull": false}, "isPositive": {"name": "isPositive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "importance": {"name": "importance", "type": "text", "primaryKey": false, "notNull": true}, "ageRelevant": {"name": "ageR<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "benefits": {"name": "benefits", "type": "jsonb", "primaryKey": false, "notNull": false}, "risks": {"name": "risks", "type": "jsonb", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"non_pathological_history_category_idx": {"name": "non_pathological_history_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_subcategory_idx": {"name": "non_pathological_history_subcategory_idx", "columns": [{"expression": "subcategory", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_positive_idx": {"name": "non_pathological_history_positive_idx", "columns": [{"expression": "isPositive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_importance_idx": {"name": "non_pathological_history_importance_idx", "columns": [{"expression": "importance", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_age_idx": {"name": "non_pathological_history_age_idx", "columns": [{"expression": "ageR<PERSON><PERSON>", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_active_idx": {"name": "non_pathological_history_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"notifications_user_idx": {"name": "notifications_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_read_idx": {"name": "notifications_read_idx", "columns": [{"expression": "read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_type_idx": {"name": "notifications_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_userId_user_id_fk": {"name": "notifications_userId_user_id_fk", "tableFrom": "notifications", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.occupations": {"name": "occupations", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pathological_history": {"name": "pathological_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "icd11Code": {"name": "icd11Code", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "isHereditary": {"name": "isHereditary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requiresSpecialistFollow": {"name": "requiresSpecialist<PERSON><PERSON><PERSON>", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "commonInChildren": {"name": "commonInChildren", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "riskLevel": {"name": "riskLevel", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "symptoms": {"name": "symptoms", "type": "jsonb", "primaryKey": false, "notNull": false}, "treatments": {"name": "treatments", "type": "jsonb", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"pathological_history_category_idx": {"name": "pathological_history_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_severity_idx": {"name": "pathological_history_severity_idx", "columns": [{"expression": "severity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_risk_idx": {"name": "pathological_history_risk_idx", "columns": [{"expression": "riskLevel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_hereditary_idx": {"name": "pathological_history_hereditary_idx", "columns": [{"expression": "isHereditary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_children_idx": {"name": "pathological_history_children_idx", "columns": [{"expression": "commonInChildren", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_active_idx": {"name": "pathological_history_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_icd11_idx": {"name": "pathological_history_icd11_idx", "columns": [{"expression": "icd11Code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patient_invitations": {"name": "patient_invitations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "patient_user_id": {"name": "patient_user_id", "type": "text", "primaryKey": false, "notNull": true}, "guardian_email": {"name": "guardian_email", "type": "text", "primaryKey": false, "notNull": true}, "invitation_token": {"name": "invitation_token", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "accepted_at": {"name": "accepted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"patient_invitations_token_idx": {"name": "patient_invitations_token_idx", "columns": [{"expression": "invitation_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "patient_invitations_status_idx": {"name": "patient_invitations_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "patient_invitations_guardian_email_idx": {"name": "patient_invitations_guardian_email_idx", "columns": [{"expression": "guardian_email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"patient_invitations_patient_user_id_user_id_fk": {"name": "patient_invitations_patient_user_id_user_id_fk", "tableFrom": "patient_invitations", "tableTo": "user", "columnsFrom": ["patient_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"patient_invitations_invitation_token_unique": {"name": "patient_invitations_invitation_token_unique", "nullsNotDistinct": false, "columns": ["invitation_token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.patient_medical_history": {"name": "patient_medical_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "medicalRecordId": {"name": "medicalRecordId", "type": "text", "primaryKey": false, "notNull": true}, "pathologicalHistory": {"name": "pathologicalHistory", "type": "jsonb", "primaryKey": false, "notNull": false}, "nonPathologicalHistory": {"name": "nonPathologicalHistory", "type": "jsonb", "primaryKey": false, "notNull": false}, "familyHistory": {"name": "familyHistory", "type": "jsonb", "primaryKey": false, "notNull": false}, "allergies": {"name": "allergies", "type": "jsonb", "primaryKey": false, "notNull": false}, "hospitalizations": {"name": "hospitalizations", "type": "jsonb", "primaryKey": false, "notNull": false}, "surgeries": {"name": "surgeries", "type": "jsonb", "primaryKey": false, "notNull": false}, "vaccinations": {"name": "vaccinations", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedBy": {"name": "updatedBy", "type": "text", "primaryKey": false, "notNull": true}}, "indexes": {"patient_medical_history_record_idx": {"name": "patient_medical_history_record_idx", "columns": [{"expression": "medicalRecordId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "patient_medical_history_updated_idx": {"name": "patient_medical_history_updated_idx", "columns": [{"expression": "updatedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"patient_medical_history_medicalRecordId_medical_records_id_fk": {"name": "patient_medical_history_medicalRecordId_medical_records_id_fk", "tableFrom": "patient_medical_history", "tableTo": "medical_records", "columnsFrom": ["medicalRecordId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "patient_medical_history_updatedBy_user_id_fk": {"name": "patient_medical_history_updatedBy_user_id_fk", "tableFrom": "patient_medical_history", "tableTo": "user", "columnsFrom": ["updatedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.prescription_templates": {"name": "prescription_templates", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"prescription_templates_doctor_idx": {"name": "prescription_templates_doctor_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "prescription_templates_active_idx": {"name": "prescription_templates_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"prescription_templates_doctorId_user_id_fk": {"name": "prescription_templates_doctorId_user_id_fk", "tableFrom": "prescription_templates", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.registrationRequests": {"name": "registrationRequests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "generalData": {"name": "generalData", "type": "jsonb", "primaryKey": false, "notNull": false}, "specificData": {"name": "specificData", "type": "jsonb", "primaryKey": false, "notNull": false}, "reviewedBy": {"name": "reviewedBy", "type": "text", "primaryKey": false, "notNull": false}, "reviewedAt": {"name": "reviewedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewNotes": {"name": "reviewNotes", "type": "text", "primaryKey": false, "notNull": false}, "rejectionReason": {"name": "rejectionReason", "type": "text", "primaryKey": false, "notNull": false}, "submittedAt": {"name": "submittedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"registration_requests_user_idx": {"name": "registration_requests_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "registration_requests_status_idx": {"name": "registration_requests_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "registration_requests_submitted_idx": {"name": "registration_requests_submitted_idx", "columns": [{"expression": "submittedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"registrationRequests_userId_user_id_fk": {"name": "registrationRequests_userId_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "registrationRequests_reviewedBy_user_id_fk": {"name": "registrationRequests_reviewedBy_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["reviewedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.relationships": {"name": "relationships", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.religions": {"name": "religions", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"religions_name_idx": {"name": "religions_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "religions_category_idx": {"name": "religions_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "religions_active_idx": {"name": "religions_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.service_tags": {"name": "service_tags", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false, "default": "'blue'"}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"service_tags_name_idx": {"name": "service_tags_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "service_tags_category_idx": {"name": "service_tags_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "service_tags_active_idx": {"name": "service_tags_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"service_tags_name_unique": {"name": "service_tags_name_unique", "nullsNotDistinct": false, "columns": ["name"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.symptoms": {"name": "symptoms", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "subcategory": {"name": "subcategory", "type": "text", "primaryKey": false, "notNull": false}, "icdCode": {"name": "icdCode", "type": "text", "primaryKey": false, "notNull": false}, "isSymptom": {"name": "isSymptom", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "commonCauses": {"name": "commonC<PERSON><PERSON>", "type": "jsonb", "primaryKey": false, "notNull": false, "default": "'[]'::jsonb"}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": false, "default": "'low'"}, "bodySystem": {"name": "bodySystem", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"symptoms_name_idx": {"name": "symptoms_name_idx", "columns": [{"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "symptoms_category_idx": {"name": "symptoms_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "symptoms_icd_idx": {"name": "symptoms_icd_idx", "columns": [{"expression": "icdCode", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "symptoms_active_idx": {"name": "symptoms_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_config": {"name": "system_config", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false, "default": "'general'"}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"system_config_key_idx": {"name": "system_config_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "system_config_category_idx": {"name": "system_config_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_config_active_idx": {"name": "system_config_active_idx", "columns": [{"expression": "active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"system_config_key_unique": {"name": "system_config_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "documentType": {"name": "documentType", "type": "text", "primaryKey": false, "notNull": false}, "documentNumber": {"name": "documentNumber", "type": "text", "primaryKey": false, "notNull": false}, "dateOfBirth": {"name": "dateOfBirth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "alternativePhone": {"name": "alternativePhone", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": false}, "departmentId": {"name": "departmentId", "type": "integer", "primaryKey": false, "notNull": false}, "municipalityId": {"name": "municipalityId", "type": "integer", "primaryKey": false, "notNull": false}, "occupationId": {"name": "occupationId", "type": "integer", "primaryKey": false, "notNull": false}, "emergencyContact": {"name": "emergencyContact", "type": "text", "primaryKey": false, "notNull": false}, "emergencyPhone": {"name": "emergencyPhone", "type": "text", "primaryKey": false, "notNull": false}, "emergencyRelationshipId": {"name": "emergencyRelationshipId", "type": "integer", "primaryKey": false, "notNull": false}, "overallStatus": {"name": "overallStatus", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"email_idx": {"name": "email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "overall_status_idx": {"name": "overall_status_idx", "columns": [{"expression": "overallStatus", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_idx": {"name": "document_idx", "columns": [{"expression": "documentType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "documentNumber", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_countryId_countries_id_fk": {"name": "user_countryId_countries_id_fk", "tableFrom": "user", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_departmentId_departments_id_fk": {"name": "user_departmentId_departments_id_fk", "tableFrom": "user", "tableTo": "departments", "columnsFrom": ["departmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_municipalityId_municipalities_id_fk": {"name": "user_municipalityId_municipalities_id_fk", "tableFrom": "user", "tableTo": "municipalities", "columnsFrom": ["municipalityId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_occupationId_occupations_id_fk": {"name": "user_occupationId_occupations_id_fk", "tableFrom": "user", "tableTo": "occupations", "columnsFrom": ["occupationId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_emergencyRelationshipId_relationships_id_fk": {"name": "user_emergencyRelationshipId_relationships_id_fk", "tableFrom": "user", "tableTo": "relationships", "columnsFrom": ["emergencyRelationshipId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "specialtyId": {"name": "specialtyId", "type": "integer", "primaryKey": false, "notNull": false}, "preferredDoctorId": {"name": "preferredDoctorId", "type": "text", "primaryKey": false, "notNull": false}, "medicalLicense": {"name": "medicalLicense", "type": "text", "primaryKey": false, "notNull": false}, "roleData": {"name": "roleData", "type": "jsonb", "primaryKey": false, "notNull": false}, "approvedBy": {"name": "approvedBy", "type": "text", "primaryKey": false, "notNull": false}, "approvedAt": {"name": "approvedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejectedBy": {"name": "rejectedBy", "type": "text", "primaryKey": false, "notNull": false}, "rejectedAt": {"name": "rejectedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejectionReason": {"name": "rejectionReason", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_role_idx": {"name": "user_role_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_roles_user_idx": {"name": "user_roles_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_roles_role_idx": {"name": "user_roles_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_roles_status_idx": {"name": "user_roles_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_roles_userId_user_id_fk": {"name": "user_roles_userId_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_consultoryId_consultories_id_fk": {"name": "user_roles_consultoryId_consultories_id_fk", "tableFrom": "user_roles", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_specialtyId_medical_specialties_id_fk": {"name": "user_roles_specialtyId_medical_specialties_id_fk", "tableFrom": "user_roles", "tableTo": "medical_specialties", "columnsFrom": ["specialtyId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_preferredDoctorId_user_id_fk": {"name": "user_roles_preferredDoctorId_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["preferredDoctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_approvedBy_user_id_fk": {"name": "user_roles_approvedBy_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["approvedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_rejectedBy_user_id_fk": {"name": "user_roles_rejectedBy_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["rejectedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}