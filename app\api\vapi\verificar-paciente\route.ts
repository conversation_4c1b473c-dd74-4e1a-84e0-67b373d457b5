import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, appointments } from '@/db/schema';
import { eq, or, and, desc, sql } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { telefono, nombres, apellidos } = body;

    // Validaciones básicas
    if (!telefono || !nombres || !apellidos) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Campos requeridos: telefono, nombres, apellidos' 
        },
        { status: 400 }
      );
    }

    // Limpiar y normalizar datos
    const telefonoLimpio = telefono.replace(/\D/g, ''); // Solo números
    const nombresNormalizados = nombres.trim().toLowerCase();
    const apellidosNormalizados = apellidos.trim().toLowerCase();

    console.log(`🔍 Buscando paciente: ${nombres} ${apellidos}, Tel: ${telefono}`);

    // 1. Buscar por teléfono (principal)
    const pacientesPorTelefono = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        dateOfBirth: user.dateOfBirth,
        isActive: user.isActive,
        createdAt: user.createdAt,
      })
      .from(user)
      .where(
        and(
          eq(user.role, 'patient'),
          eq(user.isActive, true),
          or(
            sql`REPLACE(${user.phone}, ' ', '') LIKE ${'%' + telefonoLimpio + '%'}`,
            sql`REPLACE(REPLACE(${user.phone}, ' ', ''), '-', '') LIKE ${'%' + telefonoLimpio + '%'}`
          )
        )
      );

    // 2. Si no encuentra por teléfono, buscar por nombre completo
    let pacientesPorNombre: any[] = [];
    if (pacientesPorTelefono.length === 0) {
      pacientesPorNombre = await db
        .select({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          dateOfBirth: user.dateOfBirth,
          isActive: user.isActive,
          createdAt: user.createdAt,
        })
        .from(user)
        .where(
          and(
            eq(user.role, 'patient'),
            eq(user.isActive, true),
            sql`LOWER(${user.firstName}) LIKE ${'%' + nombresNormalizados + '%'}`,
            sql`LOWER(${user.lastName}) LIKE ${'%' + apellidosNormalizados + '%'}`
          )
        );
    }

    const pacientesEncontrados = pacientesPorTelefono.length > 0 ? pacientesPorTelefono : pacientesPorNombre;

    // 3. Si no encuentra ninguno = paciente nuevo
    if (pacientesEncontrados.length === 0) {
      console.log('✨ Paciente nuevo detectado');
      return NextResponse.json({
        success: true,
        esNuevo: true,
        mensaje: "Paciente nuevo. Procederemos a crear su perfil."
      });
    }

    // 4. Si encuentra exactamente uno = paciente existente
    if (pacientesEncontrados.length === 1) {
      const paciente = pacientesEncontrados[0];
      
      // Calcular edad
      let edad = null;
      if (paciente.dateOfBirth) {
        const birthDate = new Date(paciente.dateOfBirth);
        const today = new Date();
        edad = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          edad--;
        }
      }

      // Obtener última cita
      const ultimaCitaResult = await db
        .select({
          scheduledDate: appointments.scheduledDate,
          title: appointments.title,
        })
        .from(appointments)
        .where(eq(appointments.patientId, paciente.id))
        .orderBy(desc(appointments.scheduledDate))
        .limit(1);

      // Contar total de citas
      const totalCitasResult = await db
        .select({ count: sql<number>`count(*)` })
        .from(appointments)
        .where(eq(appointments.patientId, paciente.id));

      const ultimaCita = ultimaCitaResult[0]?.scheduledDate;
      const totalCitas = totalCitasResult[0]?.count || 0;

      console.log(`✅ Paciente existente encontrado: ${paciente.firstName} ${paciente.lastName}`);

      return NextResponse.json({
        success: true,
        esNuevo: false,
        paciente: {
          id: paciente.id,
          nombres: paciente.firstName,
          apellidos: paciente.lastName,
          telefono: paciente.phone,
          email: paciente.email,
          edad: edad,
          tieneEmail: !!(paciente.email && !paciente.email.includes('@temp.local')),
          esMenor: edad ? edad < 18 : false,
          ultimaCita: ultimaCita ? ultimaCita.toISOString().split('T')[0] : null,
          totalCitas: totalCitas
        },
        mensaje: `Paciente encontrado: ${paciente.firstName} ${paciente.lastName}.${ultimaCita ? ` Última cita: ${ultimaCita.toLocaleDateString('es-ES', { day: 'numeric', month: 'long' })}.` : ' Primera vez.'}`
      });
    }

    // 5. Si encuentra múltiples = desambiguación necesaria
    console.log(`🤔 Múltiples pacientes encontrados: ${pacientesEncontrados.length}`);
    
    // Obtener última cita para cada paciente (para ayudar en desambiguación)
    const pacientesConInfo = await Promise.all(
      pacientesEncontrados.map(async (paciente) => {
        const ultimaCitaResult = await db
          .select({ scheduledDate: appointments.scheduledDate })
          .from(appointments)
          .where(eq(appointments.patientId, paciente.id))
          .orderBy(desc(appointments.scheduledDate))
          .limit(1);

        return {
          id: paciente.id,
          nombres: paciente.firstName,
          apellidos: paciente.lastName,
          telefono: paciente.phone,
          ultimaCita: ultimaCitaResult[0]?.scheduledDate?.toISOString().split('T')[0] || null
        };
      })
    );

    return NextResponse.json({
      success: true,
      esNuevo: false,
      multipleCoincidencias: true,
      pacientes: pacientesConInfo,
      mensaje: `Encontramos ${pacientesEncontrados.length} pacientes con datos similares. ¿Cuál es correcto?`
    });

  } catch (error) {
    console.error('Error en verificar-paciente:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}