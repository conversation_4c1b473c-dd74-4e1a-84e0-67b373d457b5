import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  user, 
  userRoles, 
  notifications,
  associationCodes,
  guardianPatientRelations,
  assistantDoctorRelations,
  registrationRequests
} from '@/db/schema';
import { eq, sql } from 'drizzle-orm';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  // Obtener rol de los session claims (igual que en el middleware)
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// GET - Obtener detalle de usuario
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;

    // Obtener usuario
    const userData = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (userData.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    // Obtener roles del usuario
    const userRolesData = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, userId));

    // Combinar datos
    const userWithRoles = {
      ...userData[0],
      roles: userRolesData
    };

    return NextResponse.json({
      success: true,
      data: userWithRoles
    });

  } catch (error) {
    console.error('Error fetching user:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar usuario
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;
    const body = await request.json();

    const {
      firstName,
      lastName,
      email,
      documentType,
      documentNumber,
      dateOfBirth,
      gender,
      phone,
      alternativePhone,
      address,
      countryId,
      departmentId,
      municipalityId,
      occupationId,
      emergencyContact,
      emergencyPhone,
      emergencyRelationshipId,
      overallStatus,
      roles = []
    } = body;

    // Verificar que el usuario existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    try {
      // 1. Buscar usuario en Clerk por metadata
      const clerk = await clerkClient();
      const clerkUsers = await clerk.users.getUserList({
        limit: 100
      });
      
      const clerkUser = clerkUsers.find(
        (u: any) => u.privateMetadata?.internalUserId === userId
      );

      // 2. Actualizar en Clerk si existe
      if (clerkUser) {
        await clerk.users.updateUser(clerkUser.id, {
          firstName,
          lastName,
          publicMetadata: {
            ...clerkUser.publicMetadata,
            roles: roles.map((r: any) => r.role),
            lastUpdated: new Date().toISOString(),
          },
        });
      }

      // 3. Actualizar en base de datos
      const updatedUser = await db
        .update(user)
        .set({
          firstName,
          lastName,
          email,
          documentType,
          documentNumber,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          gender,
          phone,
          alternativePhone,
          address,
          countryId,
          departmentId,
          municipalityId,
          occupationId,
          emergencyContact,
          emergencyPhone,
          emergencyRelationshipId,
          overallStatus,
          updatedAt: new Date(),
        })
        .where(eq(user.id, userId))
        .returning();

      // 4. Actualizar roles
      // Eliminar roles existentes
      await db.delete(userRoles).where(eq(userRoles.userId, userId));

      // Crear nuevos roles
      if (roles.length > 0) {
        const rolePromises = roles.map((roleData: any) => {
          const roleId = `role_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
          return db.insert(userRoles).values({
            id: roleId,
            userId,
            role: roleData.role,
            status: roleData.status || 'active',
            consultoryId: roleData.consultoryId,
            specialtyId: roleData.specialtyId,
            medicalLicense: roleData.medicalLicense,
            roleData: roleData.additionalData,
          });
        });

        await Promise.all(rolePromises);
      }

      return NextResponse.json({
        success: true,
        data: {
          user: updatedUser[0],
          message: 'Usuario actualizado exitosamente'
        }
      });

    } catch (clerkError) {
      console.error('Error updating user in Clerk:', clerkError);
      return NextResponse.json(
        { success: false, error: 'Error al actualizar usuario en el sistema de autenticación' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar usuario
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;
    
    const { searchParams } = new URL(request.url);
    const deleteFromClerk = searchParams.get('deleteFromClerk') === 'true';
    const deletePhysically = searchParams.get('deletePhysically') === 'true';

    // Verificar que el usuario existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const currentUser = existingUser[0];

    // Información de usuario para Clerk (antes de eliminar)
    let clerkUser = null;
    if (deleteFromClerk && deletePhysically) {
      try {
        const clerk = await clerkClient();
        
        // Buscar por metadata interno first
        const clerkUsers = await clerk.users.getUserList({
          limit: 500
        });
        
        clerkUser = clerkUsers.data.find(
          (u: any) => u.privateMetadata?.internalUserId === userId
        );
        
        // Si no se encuentra por metadata, buscar por email
        if (!clerkUser) {
          const clerkUsersByEmail = await clerk.users.getUserList({
            emailAddress: [currentUser.email]
          });
          
          if (clerkUsersByEmail.data.length > 0) {
            clerkUser = clerkUsersByEmail.data[0];
          }
        }
      } catch (clerkError) {
        console.error('Error buscando usuario en Clerk:', clerkError);
      }
    }

    // Usar transacciones para garantizar consistencia
    const deletionSteps = [];
    let analysisResult;
    
    try {
      // 1. ANÁLISIS PREVIO - Verificar qué se va a eliminar
      analysisResult = await analyzeUserDeletionImpact(userId);
      
      if (deletePhysically) {
        // 2. ELIMINACIÓN FÍSICA COMPLETA CON TRANSACCIÓN
        await db.transaction(async (tx) => {
          // Paso 1: Eliminar notificaciones
          if (analysisResult.notifications.length > 0) {
            await tx.delete(notifications).where(eq(notifications.userId, userId));
            deletionSteps.push(`✓ ${analysisResult.notifications.length} notificaciones eliminadas`);
          }

          // Paso 2: Eliminar códigos de asociación
          if (analysisResult.associationCodes.length > 0) {
            await tx.delete(associationCodes).where(eq(associationCodes.patientId, userId));
            await tx.delete(associationCodes).where(eq(associationCodes.usedBy, userId));
            deletionSteps.push(`✓ ${analysisResult.associationCodes.length} códigos de asociación eliminados`);
          }

          // Paso 3: Eliminar relaciones guardian-paciente
          if (analysisResult.guardianPatientRelations.length > 0) {
            await tx.delete(guardianPatientRelations).where(eq(guardianPatientRelations.guardianId, userId));
            await tx.delete(guardianPatientRelations).where(eq(guardianPatientRelations.patientId, userId));
            deletionSteps.push(`✓ ${analysisResult.guardianPatientRelations.length} relaciones guardian-paciente eliminadas`);
          }

          // Paso 4: Eliminar relaciones asistente-doctor
          if (analysisResult.assistantDoctorRelations.length > 0) {
            await tx.delete(assistantDoctorRelations).where(eq(assistantDoctorRelations.assistantId, userId));
            await tx.delete(assistantDoctorRelations).where(eq(assistantDoctorRelations.doctorId, userId));
            deletionSteps.push(`✓ ${analysisResult.assistantDoctorRelations.length} relaciones asistente-doctor eliminadas`);
          }

          // Paso 5: Eliminar solicitudes de registro
          if (analysisResult.registrationRequests.length > 0) {
            await tx.delete(registrationRequests).where(eq(registrationRequests.userId, userId));
            deletionSteps.push(`✓ ${analysisResult.registrationRequests.length} solicitudes de registro eliminadas`);
          }

          // Paso 6: Actualizar referencias en userRoles (SET NULL para evitar FK violations)
          if (analysisResult.roleReferences.length > 0) {
            await tx.update(userRoles).set({ 
              preferredDoctorId: null 
            }).where(eq(userRoles.preferredDoctorId, userId));
            
            await tx.update(userRoles).set({ 
              approvedBy: null 
            }).where(eq(userRoles.approvedBy, userId));
            
            await tx.update(userRoles).set({ 
              rejectedBy: null 
            }).where(eq(userRoles.rejectedBy, userId));
            
            deletionSteps.push(`✓ ${analysisResult.roleReferences.length} referencias en roles actualizadas a NULL`);
          }

          // Paso 7: Eliminar roles del usuario
          if (analysisResult.userRoles.length > 0) {
            await tx.delete(userRoles).where(eq(userRoles.userId, userId));
            deletionSteps.push(`✓ ${analysisResult.userRoles.length} roles de usuario eliminados`);
          }

          // Paso 8: Eliminar usuario principal (al final)
          await tx.delete(user).where(eq(user.id, userId));
          deletionSteps.push(`✓ Usuario principal eliminado de la base de datos`);
        });

        // Paso 9: Eliminar de Clerk (fuera de transacción)
        if (deleteFromClerk) {
          try {
            const clerk = await clerkClient();
            
            // Si ya tenemos el usuario de Clerk, lo eliminamos directamente
            if (clerkUser) {
              await clerk.users.deleteUser(clerkUser.id);
              deletionSteps.push(`✓ Usuario eliminado de Clerk (${clerkUser.id})`);
            } else {
              // Si no lo encontramos antes, intentamos buscarlo de nuevo por email
              const currentUser = existingUser[0];
              const clerkUsersByEmail = await clerk.users.getUserList({
                emailAddress: [currentUser.email]
              });
              
              if (clerkUsersByEmail.data.length > 0) {
                await clerk.users.deleteUser(clerkUsersByEmail.data[0].id);
                deletionSteps.push(`✓ Usuario eliminado de Clerk por email (${clerkUsersByEmail.data[0].id})`);
              } else {
                deletionSteps.push(`⚠ Usuario no encontrado en Clerk para eliminar`);
              }
            }
          } catch (clerkError) {
            console.error('Error eliminando usuario de Clerk:', clerkError);
            // Si falla Clerk, la BD ya está limpia, no se puede hacer rollback
            deletionSteps.push(`⚠ Error al eliminar de Clerk: ${clerkError instanceof Error ? clerkError.message : 'Error desconocido'}`);
          }
        }

      } else {
        // ELIMINACIÓN LÓGICA - marcar como eliminado
        await db.update(user).set({
          overallStatus: 'deleted',
          updatedAt: new Date(),
        }).where(eq(user.id, userId));
        
        // Inactivar roles también
        await db.update(userRoles).set({
          status: 'inactive',
          updatedAt: new Date(),
        }).where(eq(userRoles.userId, userId));
        
        deletionSteps.push(`✓ Usuario marcado como eliminado (eliminación lógica)`);
        deletionSteps.push(`✓ Roles inactivados`);
      }

      const deletionResult = {
        success: true,
        steps: deletionSteps,
        analysis: analysisResult,
        user: {
          id: currentUser.id,
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email
        }
      };

      return NextResponse.json({
        success: true,
        message: deletePhysically ? 
          'Usuario eliminado físicamente de forma completa' : 
          'Usuario marcado como eliminado',
        details: {
          stepsCompleted: deletionResult.steps,
          itemsAffected: deletionResult.analysis,
          user: deletionResult.user
        }
      });

    } catch (deletionError) {
      console.error('Error en proceso de eliminación:', deletionError);
      return NextResponse.json(
        { 
          success: false, 
          error: 'Error en el proceso de eliminación. Operación cancelada.',
          details: deletionError instanceof Error ? deletionError.message : 'Error desconocido'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error eliminando usuario:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al eliminar usuario. Operación cancelada.',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

// Función para analizar el impacto de eliminación de un usuario
async function analyzeUserDeletionImpact(userId: string) {
  const [
    userNotifications,
    userAssociationCodes,
    userGuardianRelations, 
    userAssistantRelations,
    userRegistrationRequests,
    userRolesData,
    roleReferences
  ] = await Promise.all([
    // Notificaciones del usuario
    db.select().from(notifications).where(eq(notifications.userId, userId)),
    
    // Códigos de asociación donde el usuario aparece
    db.select().from(associationCodes).where(
      sql`${associationCodes.patientId} = ${userId} OR ${associationCodes.usedBy} = ${userId}`
    ),
    
    // Relaciones guardian-paciente donde el usuario aparece
    db.select().from(guardianPatientRelations).where(
      sql`${guardianPatientRelations.guardianId} = ${userId} OR ${guardianPatientRelations.patientId} = ${userId}`
    ),
    
    // Relaciones asistente-doctor donde el usuario aparece
    db.select().from(assistantDoctorRelations).where(
      sql`${assistantDoctorRelations.assistantId} = ${userId} OR ${assistantDoctorRelations.doctorId} = ${userId}`
    ),
    
    // Solicitudes de registro del usuario
    db.select().from(registrationRequests).where(eq(registrationRequests.userId, userId)),
    
    // Roles del usuario
    db.select().from(userRoles).where(eq(userRoles.userId, userId)),
    
    // Referencias a este usuario en otros roles
    db.select().from(userRoles).where(
      sql`${userRoles.preferredDoctorId} = ${userId} OR ${userRoles.approvedBy} = ${userId} OR ${userRoles.rejectedBy} = ${userId}`
    )
  ]);

  return {
    notifications: userNotifications,
    associationCodes: userAssociationCodes,
    guardianPatientRelations: userGuardianRelations,
    assistantDoctorRelations: userAssistantRelations,
    registrationRequests: userRegistrationRequests,
    userRoles: userRolesData,
    roleReferences: roleReferences,
    totalRecords: userNotifications.length + userAssociationCodes.length + 
                 userGuardianRelations.length + userAssistantRelations.length + 
                 userRegistrationRequests.length + userRolesData.length + roleReferences.length
  };
}