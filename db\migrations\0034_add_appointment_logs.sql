-- Migración: Agregar tabla de logs para citas médicas
-- Fecha: 2025-08-01

CREATE TABLE IF NOT EXISTS "appointment_logs" (
	"id" text PRIMARY KEY NOT NULL,
	"appointment_id" text NOT NULL,
	"event_type" text NOT NULL,
	"event_category" text NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"metadata" jsonb,
	"triggered_by" text,
	"triggered_by_role" text,
	"previous_state" jsonb,
	"new_state" jsonb,
	"email_type" text,
	"email_recipient" text,
	"email_status" text,
	"ip_address" text,
	"user_agent" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);

-- Índices para optimizar consultas
CREATE INDEX IF NOT EXISTS "appointment_logs_appointment_idx" ON "appointment_logs" ("appointment_id");
CREATE INDEX IF NOT EXISTS "appointment_logs_event_type_idx" ON "appointment_logs" ("event_type");
CREATE INDEX IF NOT EXISTS "appointment_logs_event_category_idx" ON "appointment_logs" ("event_category");
CREATE INDEX IF NOT EXISTS "appointment_logs_created_at_idx" ON "appointment_logs" ("created_at");
CREATE INDEX IF NOT EXISTS "appointment_logs_triggered_by_idx" ON "appointment_logs" ("triggered_by");

-- Foreign Keys
ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "appointments"("id") ON DELETE cascade;
ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_triggered_by_user_id_fk" FOREIGN KEY ("triggered_by") REFERENCES "user"("id");