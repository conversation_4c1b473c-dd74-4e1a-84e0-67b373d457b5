/**
 * Utilidad para extraer paleta de colores de imágenes (logos)
 * Utiliza Canvas API para analizar píxeles y generar paleta de colores dominantes
 */

export interface ColorPalette {
  primary: string;
  secondary: string;
  accent: string;
  background: string;
  text: string;
  success: string;
  warning: string;
  error: string;
  info: string;
  neutral: string;
  gradientStart: string;
  gradientEnd: string;
  extractedColors: string[];
  isAutoGenerated: boolean;
  lastUpdated: string;
}

export interface ExtractedColor {
  hex: string;
  rgb: [number, number, number];
  frequency: number;
  brightness: number;
  saturation: number;
}

/**
 * Extrae los colores dominantes de una imagen
 */
export async function extractColorsFromImage(imageUrl: string): Promise<ExtractedColor[]> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'anonymous';
    
    img.onload = () => {
      try {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('No se pudo crear contexto de canvas'));
          return;
        }

        // Redimensionar para optimizar procesamiento
        const maxSize = 100;
        const scale = Math.min(maxSize / img.width, maxSize / img.height);
        canvas.width = img.width * scale;
        canvas.height = img.height * scale;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
        
        const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
        const colors = analyzeImageColors(imageData);
        
        resolve(colors);
      } catch (error) {
        reject(error);
      }
    };

    img.onerror = () => reject(new Error('Error al cargar la imagen'));
    img.src = imageUrl;
  });
}

/**
 * Analiza los píxeles de la imagen y extrae colores dominantes
 */
function analyzeImageColors(imageData: ImageData): ExtractedColor[] {
  const data = imageData.data;
  const colorMap = new Map<string, number>();
  
  // Muestrear píxeles (cada 4 píxeles para optimizar)
  for (let i = 0; i < data.length; i += 16) {
    const r = data[i];
    const g = data[i + 1];
    const b = data[i + 2];
    const a = data[i + 3];
    
    // Ignorar píxeles transparentes o muy claros/oscuros
    if (a < 128 || (r > 240 && g > 240 && b > 240) || (r < 15 && g < 15 && b < 15)) {
      continue;
    }
    
    // Reducir precisión para agrupar colores similares
    const reducedR = Math.round(r / 16) * 16;
    const reducedG = Math.round(g / 16) * 16;
    const reducedB = Math.round(b / 16) * 16;
    
    const colorKey = `${reducedR},${reducedG},${reducedB}`;
    colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
  }
  
  // Convertir a array y ordenar por frecuencia
  const colors: ExtractedColor[] = Array.from(colorMap.entries())
    .map(([color, frequency]) => {
      const [r, g, b] = color.split(',').map(Number);
      return {
        hex: rgbToHex(r, g, b),
        rgb: [r, g, b] as [number, number, number],
        frequency,
        brightness: getBrightness(r, g, b),
        saturation: getSaturation(r, g, b)
      };
    })
    .sort((a, b) => b.frequency - a.frequency)
    .slice(0, 10); // Top 10 colores
  
  return colors;
}

/**
 * Genera una paleta de colores completa basada en los colores extraídos
 */
export function generateColorPalette(extractedColors: ExtractedColor[]): ColorPalette {
  if (extractedColors.length === 0) {
    return getDefaultPalette();
  }

  // Filtrar colores por características
  const vibrantColors = extractedColors.filter(c => c.saturation > 0.3 && c.brightness > 0.2 && c.brightness < 0.8);
  const darkColors = extractedColors.filter(c => c.brightness < 0.4);
  const lightColors = extractedColors.filter(c => c.brightness > 0.7);
  
  // Asignar colores principales
  const primary = vibrantColors[0]?.hex || extractedColors[0]?.hex || '#059669';
  const secondary = vibrantColors[1]?.hex || extractedColors[1]?.hex || '#14b8a6';
  const accent = vibrantColors[2]?.hex || extractedColors[2]?.hex || '#10b981';
  
  // Generar colores derivados
  const primaryRgb = hexToRgb(primary);
  const background = lightColors[0]?.hex || '#ffffff';
  const text = darkColors[0]?.hex || '#1e293b';
  
  return {
    primary,
    secondary,
    accent,
    background,
    text,
    success: adjustColor(primary, { saturation: 0.7, brightness: 0.5, hue: 120 }) || '#10b981',
    warning: adjustColor(primary, { saturation: 0.8, brightness: 0.6, hue: 45 }) || '#f59e0b',
    error: adjustColor(primary, { saturation: 0.7, brightness: 0.5, hue: 0 }) || '#ef4444',
    info: adjustColor(primary, { saturation: 0.6, brightness: 0.6, hue: 200 }) || '#3b82f6',
    neutral: '#64748b',
    gradientStart: primary,
    gradientEnd: secondary,
    extractedColors: extractedColors.map(c => c.hex),
    isAutoGenerated: true,
    lastUpdated: new Date().toISOString()
  };
}

/**
 * Paleta por defecto si no se puede extraer del logo
 */
function getDefaultPalette(): ColorPalette {
  return {
    primary: '#059669',
    secondary: '#14b8a6', 
    accent: '#10b981',
    background: '#ffffff',
    text: '#1e293b',
    success: '#10b981',
    warning: '#f59e0b',
    error: '#ef4444',
    info: '#3b82f6',
    neutral: '#64748b',
    gradientStart: '#059669',
    gradientEnd: '#14b8a6',
    extractedColors: ['#059669', '#14b8a6', '#10b981'],
    isAutoGenerated: true,
    lastUpdated: new Date().toISOString()
  };
}

// Utilidades de color
function rgbToHex(r: number, g: number, b: number): string {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1);
}

function hexToRgb(hex: string): [number, number, number] {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  return result ? [
    parseInt(result[1], 16),
    parseInt(result[2], 16),
    parseInt(result[3], 16)
  ] : [0, 0, 0];
}

function getBrightness(r: number, g: number, b: number): number {
  return (r * 299 + g * 587 + b * 114) / 1000 / 255;
}

function getSaturation(r: number, g: number, b: number): number {
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  return max === 0 ? 0 : diff / max;
}

function adjustColor(hex: string, adjustments: { 
  saturation?: number; 
  brightness?: number; 
  hue?: number 
}): string | null {
  try {
    const [r, g, b] = hexToRgb(hex);
    // Convertir a HSB y ajustar
    const hsb = rgbToHsb(r, g, b);
    
    if (adjustments.hue !== undefined) hsb[0] = adjustments.hue;
    if (adjustments.saturation !== undefined) hsb[1] = adjustments.saturation;
    if (adjustments.brightness !== undefined) hsb[2] = adjustments.brightness;
    
    const [newR, newG, newB] = hsbToRgb(hsb[0], hsb[1], hsb[2]);
    return rgbToHex(Math.round(newR), Math.round(newG), Math.round(newB));
  } catch {
    return null;
  }
}

function rgbToHsb(r: number, g: number, b: number): [number, number, number] {
  r /= 255;
  g /= 255;
  b /= 255;
  
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);
  const diff = max - min;
  
  let h = 0;
  if (diff !== 0) {
    if (max === r) h = (g - b) / diff;
    else if (max === g) h = 2 + (b - r) / diff;
    else h = 4 + (r - g) / diff;
  }
  h = (h * 60 + 360) % 360;
  
  const s = max === 0 ? 0 : diff / max;
  const brightness = max;
  
  return [h, s, brightness];
}

function hsbToRgb(h: number, s: number, b: number): [number, number, number] {
  const c = b * s;
  const x = c * (1 - Math.abs(((h / 60) % 2) - 1));
  const m = b - c;
  
  let r = 0, g = 0, blue = 0;
  
  if (h >= 0 && h < 60) {
    r = c; g = x; blue = 0;
  } else if (h >= 60 && h < 120) {
    r = x; g = c; blue = 0;
  } else if (h >= 120 && h < 180) {
    r = 0; g = c; blue = x;
  } else if (h >= 180 && h < 240) {
    r = 0; g = x; blue = c;
  } else if (h >= 240 && h < 300) {
    r = x; g = 0; blue = c;
  } else if (h >= 300 && h < 360) {
    r = c; g = 0; blue = x;
  }
  
  return [(r + m) * 255, (g + m) * 255, (blue + m) * 255];
}