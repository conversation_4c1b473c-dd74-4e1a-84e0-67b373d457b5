import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { pathologicalHistory } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or } from 'drizzle-orm';

// GET - Listar antecedentes patológicos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const severity = searchParams.get('severity');
    const isHereditary = searchParams.get('hereditary');
    const commonInChildren = searchParams.get('children') === 'true';
    const orderBy = searchParams.get('orderBy') || 'order';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(pathologicalHistory);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(pathologicalHistory.name, `%${search}%`),
          ilike(pathologicalHistory.description, `%${search}%`),
          ilike(pathologicalHistory.category, `%${search}%`)
        )
      );
    }

    if (category) {
      conditions.push(eq(pathologicalHistory.category, category));
    }

    if (severity) {
      conditions.push(eq(pathologicalHistory.severity, severity));
    }

    if (isHereditary !== null) {
      conditions.push(eq(pathologicalHistory.isHereditary, isHereditary === 'true'));
    }

    if (commonInChildren) {
      conditions.push(eq(pathologicalHistory.commonInChildren, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'name' ? pathologicalHistory.name :
                       orderBy === 'category' ? pathologicalHistory.category :
                       orderBy === 'severity' ? pathologicalHistory.severity :
                       orderBy === 'createdAt' ? pathologicalHistory.createdAt :
                       pathologicalHistory.order;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const pathologicalHistoryData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(pathologicalHistory);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Obtener categorías únicas
    const categories = await db.select({
      category: pathologicalHistory.category
    }).from(pathologicalHistory).groupBy(pathologicalHistory.category);

    return NextResponse.json({
      data: pathologicalHistoryData,
      categories: categories.map(c => c.category),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        hereditary: pathologicalHistoryData.filter(item => item.isHereditary).length,
        commonInChildren: pathologicalHistoryData.filter(item => item.commonInChildren).length,
        bySeverity: {
          low: pathologicalHistoryData.filter(item => item.severity === 'low').length,
          moderate: pathologicalHistoryData.filter(item => item.severity === 'moderate').length,
          high: pathologicalHistoryData.filter(item => item.severity === 'high').length
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo antecedentes patológicos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo antecedente patológico (solo admin y médicos)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Solo administradores y médicos pueden crear antecedentes patológicos' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      category, 
      icd11Code, 
      severity, 
      isHereditary, 
      requiresSpecialistFollow, 
      commonInChildren, 
      riskLevel, 
      description, 
      symptoms, 
      treatments, 
      order 
    } = body;

    // Validaciones
    if (!name || !category || !severity) {
      return NextResponse.json(
        { error: 'Nombre, categoría y severidad son requeridos' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un antecedente con el mismo ID
    const existingHistory = await db.select().from(pathologicalHistory).where(eq(pathologicalHistory.id, id)).limit(1);
    if (existingHistory.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un antecedente patológico con este nombre' },
        { status: 400 }
      );
    }

    const newPathologicalHistory = {
      id,
      name: name.trim(),
      category: category.trim(),
      icd11Code: icd11Code?.trim() || null,
      severity,
      isHereditary: Boolean(isHereditary),
      requiresSpecialistFollow: Boolean(requiresSpecialistFollow),
      commonInChildren: Boolean(commonInChildren),
      riskLevel: riskLevel || 'medium',
      description: description?.trim() || '',
      symptoms: symptoms || [],
      treatments: treatments || [],
      order: order ? parseInt(order) : 999
    };

    // Insertar en base de datos
    const [insertedPathologicalHistory] = await db.insert(pathologicalHistory).values(newPathologicalHistory).returning();

    return NextResponse.json({
      message: 'Antecedente patológico creado exitosamente',
      data: insertedPathologicalHistory
    });

  } catch (error) {
    console.error('Error creando antecedente patológico:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar antecedente patológico (solo admin y médicos)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Solo administradores y médicos pueden actualizar antecedentes patológicos' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, category, icd11Code, severity, isHereditary, requiresSpecialistFollow, commonInChildren, riskLevel, description, symptoms, treatments, order } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de antecedente patológico requerido' },
        { status: 400 }
      );
    }

    // Buscar antecedente existente
    const existingHistory = await db.select().from(pathologicalHistory).where(eq(pathologicalHistory.id, id)).limit(1);
    if (existingHistory.length === 0) {
      return NextResponse.json(
        { error: 'Antecedente patológico no encontrado' },
        { status: 404 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(category && { category: category.trim() }),
      ...(icd11Code !== undefined && { icd11Code: icd11Code?.trim() || null }),
      ...(severity && { severity }),
      ...(isHereditary !== undefined && { isHereditary: Boolean(isHereditary) }),
      ...(requiresSpecialistFollow !== undefined && { requiresSpecialistFollow: Boolean(requiresSpecialistFollow) }),
      ...(commonInChildren !== undefined && { commonInChildren: Boolean(commonInChildren) }),
      ...(riskLevel && { riskLevel }),
      ...(description !== undefined && { description: description.trim() }),
      ...(symptoms && { symptoms }),
      ...(treatments && { treatments }),
      ...(order !== undefined && { order: parseInt(order) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedPathologicalHistory] = await db.update(pathologicalHistory)
      .set(updateData)
      .where(eq(pathologicalHistory.id, id))
      .returning();

    return NextResponse.json({
      message: 'Antecedente patológico actualizado exitosamente',
      data: updatedPathologicalHistory
    });

  } catch (error) {
    console.error('Error actualizando antecedente patológico:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}