import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { 
  user, 
  userRoles,
  registrationRequests, 
  notifications,
  consultories,
  medicalSpecialties
} from '@/db/schema';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { eq, and, desc } from 'drizzle-orm';
import { randomBytes } from 'crypto';
import { sendWelcomeEmail } from '@/lib/email-platform';

// Función para generar IDs únicos
function generateId(): string {
  return randomBytes(16).toString('hex');
}
import { 
  OnboardingFormData, 
  GeneralProfileData, 
  UserRole,
  OnboardingResponse 
} from '@/lib/types/onboarding';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { 
      role, 
      generalData, 
      specificData, 
      documents 
    }: {
      role: UserRole;
      generalData: GeneralProfileData;
      specificData: any;
      documents?: { [key: string]: string }; // URLs de documentos subidos
    } = body;

    // Validar datos requeridos
    if (!role || !generalData) {
      return NextResponse.json(
        { success: false, message: 'Datos incompletos' },
        { status: 400 }
      );
    }

    // Verificar que el usuario no tenga ya una solicitud pendiente para este rol
    const existingRequest = await db
      .select()
      .from(registrationRequests)
      .where(
        and(
          eq(registrationRequests.userId, userId),
          eq(registrationRequests.role, role),
          eq(registrationRequests.status, 'pending')
        )
      )
      .limit(1);

    if (existingRequest.length > 0) {
      return NextResponse.json(
        { success: false, message: `Ya tienes una solicitud pendiente para el rol de ${role}` },
        { status: 400 }
      );
    }

    // Verificar si hay una solicitud rechazada anterior (para informar al admin)
    const rejectedRequest = await db
      .select()
      .from(registrationRequests)
      .where(
        and(
          eq(registrationRequests.userId, userId),
          eq(registrationRequests.role, role),
          eq(registrationRequests.status, 'rejected')
        )
      )
      .orderBy(desc(registrationRequests.reviewedAt))
      .limit(1);

    const hasBeenRejectedBefore = rejectedRequest.length > 0;

    // Crear la solicitud de registro - versión temporal simplificada
    const requestId = generateId();
    console.log('Creando solicitud:', { requestId, userId, role });
    
    // Obtener datos del usuario de Clerk
    const clerk = await clerkClient();
    const clerkUser = await clerk.users.getUser(userId);

    console.log('Datos a guardar:', { generalData, specificData, documents });

    // Obtener consultorio activo (para roles que lo necesiten)
    let activeConsultory = null;
    if (['doctor', 'assistant', 'provider'].includes(role)) {
      const consultoryResult = await db
        .select()
        .from(consultories)
        .where(eq(consultories.isActive, true))
        .limit(1);
      
      activeConsultory = consultoryResult[0];
      if (!activeConsultory) {
        return NextResponse.json(
          { success: false, message: 'No hay consultorio activo configurado' },
          { status: 400 }
        );
      }
    }

    // Verificar si el usuario ya existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    // Verificar si ya existe otro usuario con el mismo documento
    if (generalData.documentType && generalData.documentNumber) {
      const existingDocument = await db
        .select()
        .from(user)
        .where(
          and(
            eq(user.documentType, generalData.documentType),
            eq(user.documentNumber, generalData.documentNumber)
          )
        )
        .limit(1);

      if (existingDocument.length > 0 && existingDocument[0].id !== userId) {
        return NextResponse.json(
          { 
            success: false, 
            message: `Ya existe un usuario registrado con el documento ${generalData.documentType}: ${generalData.documentNumber}` 
          },
          { status: 400 }
        );
      }
    }

    // Crear o actualizar usuario base (solo datos personales)
    if (existingUser.length > 0) {
      // Actualizar usuario existente
      await db
        .update(user)
        .set({
          name: clerkUser.fullName || `${generalData.firstName} ${generalData.lastName}`,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified',
          image: clerkUser.imageUrl,
          firstName: generalData.firstName || '',
          lastName: generalData.lastName || '',
          documentType: generalData.documentType || '',
          documentNumber: generalData.documentNumber || '',
          dateOfBirth: generalData.dateOfBirth ? new Date(generalData.dateOfBirth) : null,
          gender: generalData.gender || null,
          phone: generalData.phone || null,
          alternativePhone: generalData.alternativePhone || null,
          address: generalData.address || null,
          countryId: generalData.countryId || null,
          departmentId: generalData.departmentId || null,
          municipalityId: generalData.municipalityId || null,
          occupationId: generalData.occupationId || null,
          emergencyContact: generalData.emergencyContact || null,
          emergencyPhone: generalData.emergencyPhone || null,
          emergencyRelationshipId: generalData.emergencyRelationshipId || null,
          updatedAt: new Date(),
        })
        .where(eq(user.id, userId));
    } else {
      // Crear nuevo usuario
      await db
        .insert(user)
        .values({
          id: userId,
          name: clerkUser.fullName || `${generalData.firstName} ${generalData.lastName}`,
          email: clerkUser.emailAddresses[0]?.emailAddress || '',
          emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified',
          image: clerkUser.imageUrl,
          overallStatus: 'pending',
          firstName: generalData.firstName || '',
          lastName: generalData.lastName || '',
          documentType: generalData.documentType || '',
          documentNumber: generalData.documentNumber || '',
          dateOfBirth: generalData.dateOfBirth ? new Date(generalData.dateOfBirth) : null,
          gender: generalData.gender || null,
          phone: generalData.phone || null,
          alternativePhone: generalData.alternativePhone || null,
          address: generalData.address || null,
          countryId: generalData.countryId || null,
          departmentId: generalData.departmentId || null,
          municipalityId: generalData.municipalityId || null,
          occupationId: generalData.occupationId || null,
          emergencyContact: generalData.emergencyContact || null,
          emergencyPhone: generalData.emergencyPhone || null,
          emergencyRelationshipId: generalData.emergencyRelationshipId || null,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
    }

    // Crear o actualizar rol específico
    const roleValues: any = {
      id: generateId(),
      userId: userId,
      role: role,
      status: 'pending',
      roleData: specificData || {},
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Agregar datos específicos según el rol
    if (role === 'doctor') {
      // Usar el consultorio seleccionado por el doctor, no solo el activo
      roleValues.consultoryId = specificData?.consultoryId || activeConsultory?.id;
      roleValues.medicalLicense = specificData?.medicalLicense;
      
      // Asignar especialidad si se proporcionó
      if (specificData?.specialtyId) {
        // Verificar que la especialidad existe y está activa
        const specialtyResult = await db
          .select()
          .from(medicalSpecialties)
          .where(
            and(
              eq(medicalSpecialties.id, parseInt(specificData.specialtyId)),
              eq(medicalSpecialties.isActive, true)
            )
          )
          .limit(1);
        
        if (specialtyResult.length > 0) {
          roleValues.specialtyId = parseInt(specificData.specialtyId);
        }
      }
    } else if (role === 'assistant') {
      roleValues.consultoryId = activeConsultory?.id;
    } else if (role === 'provider') {
      roleValues.consultoryId = activeConsultory?.id;
    } else if (role === 'patient') {
      // IMPORTANTE: Los pacientes también necesitan estar asociados a un consultorio
      roleValues.consultoryId = activeConsultory?.id;
      
      // Asignar doctor preferido si se proporcionó
      if (specificData?.preferredDoctorId) {
        // Verificar que el doctor existe y tiene rol de doctor activo
        const doctorResult = await db
          .select()
          .from(userRoles)
          .where(
            and(
              eq(userRoles.userId, specificData.preferredDoctorId),
              eq(userRoles.role, 'doctor'),
              eq(userRoles.status, 'active')
            )
          )
          .limit(1);
        
        if (doctorResult.length > 0) {
          roleValues.preferredDoctorId = specificData.preferredDoctorId;
        }
      }
    }

    await db
      .insert(userRoles)
      .values(roleValues)
      .onConflictDoUpdate({
        target: [userRoles.userId, userRoles.role],
        set: {
          status: 'pending',
          consultoryId: roleValues.consultoryId,
          specialtyId: roleValues.specialtyId,
          preferredDoctorId: roleValues.preferredDoctorId,
          medicalLicense: roleValues.medicalLicense,
          roleData: roleValues.roleData,
          updatedAt: new Date(),
        },
      });

    // Crear registro de solicitud para el proceso de aprobación
    const requestData: any = {
      id: requestId,
      userId: userId,
      role: role,
      status: 'pending',
      generalData: generalData,
      specificData: specificData,
      submittedAt: new Date(),
      updatedAt: new Date(),
    };

    // Agregar información de rechazo anterior si existe
    if (hasBeenRejectedBefore) {
      requestData.reviewNotes = `NOTA: Este usuario fue rechazado anteriormente para este rol el ${rejectedRequest[0].reviewedAt}. Razón anterior: ${rejectedRequest[0].rejectionReason || 'No especificada'}`;
    }

    await db.insert(registrationRequests).values(requestData);

    // Crear notificación para el usuario
    await db.insert(notifications).values({
      id: generateId(),
      userId: userId,
      type: 'onboarding',
      title: 'Solicitud Enviada',
      message: 'Tu solicitud ha sido enviada. Te notificaremos cuando sea revisada.',
      read: false,
      data: { requestId, role },
      createdAt: new Date(),
    });

    // Actualizar metadata de Clerk para que el middleware funcione
    await clerk.users.updateUserMetadata(userId, {
      publicMetadata: {
        onboardingCompleted: true,
        role: role,
        status: 'pending'
      }
    });

    // *** ENVIAR EMAIL DE BIENVENIDA CON NUEVO SISTEMA ***
    try {
      const userEmail = clerkUser.emailAddresses[0]?.emailAddress;
      const userName = clerkUser.fullName || `${generalData.firstName} ${generalData.lastName}`;
      
      if (userEmail) {
        await sendWelcomeEmail(
          userId,
          userName,
          userEmail,
          role,
          requestId
        );
        console.log('✅ Email de bienvenida enviado via nuevo sistema');
      }
    } catch (error) {
      console.error('❌ Error enviando email de bienvenida:', error);
      // No fallar la operación por error de email
    }

    // TODO: Crear notificación para administradores

    const response: OnboardingResponse = {
      success: true,
      message: 'Solicitud enviada exitosamente',
      data: { requestId, status: 'pending' }
    };

    return NextResponse.json(response);

  } catch (error) {
    console.error('Error en onboarding submit:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 