'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Trash2, Refresh<PERSON><PERSON>, User } from 'lucide-react';

interface PatientDeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  itemToDelete: {
    name: string;
    firstName: string;
    lastName: string;
    email: string;
  } | null;
  onDelete: (type: 'logical' | 'physical') => Promise<void>;
  isDeleting?: boolean;
  showPhysicalDelete?: boolean;
}

export function PatientDeleteConfirmationDialog({
  open,
  onOpenChange,
  itemToDelete,
  onDelete,
  isDeleting = false,
  showPhysicalDelete = false
}: PatientDeleteConfirmationDialogProps) {
  const [deletingType, setDeletingType] = useState<'logical' | 'physical' | null>(null);

  const handleDelete = async (type: 'logical' | 'physical') => {
    setDeletingType(type);
    try {
      await onDelete(type);
      onOpenChange(false);
    } catch (error) {
      // Error handled by parent
    } finally {
      setDeletingType(null);
    }
  };

  const isProcessing = deletingType !== null || isDeleting;

  return (
    <Dialog open={open} onOpenChange={!isProcessing ? onOpenChange : undefined}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-500" />
            Confirmar Eliminación de Paciente
          </DialogTitle>
          <DialogDescription>
            Esta acción afectará los registros médicos del paciente. Seleccione el tipo de eliminación.
          </DialogDescription>
        </DialogHeader>
        
        {itemToDelete && (
          <div className="py-4">
            {/* Información del paciente a eliminar */}
            <div className="p-4 bg-gray-50 rounded-lg mb-4">
              <p className="text-sm font-medium text-gray-900 mb-2">
                Paciente a eliminar:
              </p>
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                  {itemToDelete.firstName.charAt(0)}{itemToDelete.lastName.charAt(0)}
                </div>
                <div>
                  <p className="text-lg font-semibold text-gray-800">
                    {itemToDelete.name}
                  </p>
                  <p className="text-sm text-gray-600">
                    {itemToDelete.email}
                  </p>
                </div>
              </div>
            </div>
            
            {/* Opciones de eliminación */}
            <div className="space-y-3">
              <div className="p-3 border border-orange-200 bg-orange-50 rounded-lg">
                <p className="text-sm font-medium text-orange-800 mb-1">
                  Desactivación (Recomendada)
                </p>
                <p className="text-xs text-orange-700">
                  El paciente será marcado como inactivo pero se mantendrán todos sus registros médicos para referencia histórica. Esta es la opción más segura en un sistema médico.
                </p>
              </div>
              
              {showPhysicalDelete && (
                <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
                  <p className="text-sm font-medium text-red-800 mb-1">
                    Eliminación Física (Permanente)
                  </p>
                  <p className="text-xs text-red-700">
                    ⚠️ Se eliminará completamente junto con todos sus expedientes médicos, consultas y datos relacionados. Esta acción es irreversible y puede tener implicaciones legales.
                  </p>
                </div>
              )}
            </div>
          </div>
        )}
        
        <DialogFooter className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            Cancelar
          </Button>
          <Button 
            variant="outline" 
            className="bg-orange-500 text-white hover:bg-orange-600"
            onClick={() => handleDelete('logical')}
            disabled={isProcessing}
          >
            {deletingType === 'logical' ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Procesando...
              </>
            ) : (
              'Desactivar Paciente'
            )}
          </Button>
          {showPhysicalDelete && (
            <Button 
              variant="destructive"
              onClick={() => handleDelete('physical')}
              disabled={isProcessing}
            >
              {deletingType === 'physical' ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Procesando...
                </>
              ) : (
                'Eliminar Permanentemente'
              )}
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}