# 📋 Estándares Técnicos para CRUDs - Sistema Médico Pediátrico

## 📖 Índice

1. [Resumen Ejecutivo](#resumen-ejecutivo)
2. [Arquitectura General](#arquitectura-general)
3. [Estándares de UI/UX](#estándares-de-uiux)
4. [Componentes del Dashboard](#componentes-del-dashboard)
5. [Sistema de Búsqueda y Filtros](#sistema-de-búsqueda-y-filtros)
6. [<PERSON><PERSON> <PERSON> Datos](#grid-de-datos)
7. [Sistema de Acciones](#sistema-de-acciones)
8. [Modales y Formularios](#modales-y-formularios)
9. [Paginación](#paginación)
10. [Responsive Design](#responsive-design)
11. [Manejo de Estados](#manejo-de-estados)
12. [Validaciones](#validaciones)
13. [APIs y Backend](#apis-y-backend)
14. [<PERSON><PERSON> y Temas](#colores-y-temas)
15. [Accesibilidad](#accesibilidad)
16. [Patrones de Código](#patrones-de-código)

---

## 🎯 Resumen Ejecutivo

Este documento establece los estándares técnicos unificados para todas las implementaciones CRUD en el sistema médico pediátrico. Garantiza consistencia, mantenibilidad y experiencia de usuario óptima.

### 🏆 Principios Fundamentales

- **Consistencia**: Todos los CRUDs siguen los mismos patrones
- **Simplicidad**: Interfaces intuitivas y fáciles de usar
- **Rendimiento**: Optimización en carga de datos y navegación
- **Responsividad**: Funcional en desktop y mobile
- **Accesibilidad**: Cumple estándares WCAG 2.1
- **Mantenibilidad**: Código limpio y reutilizable

---

## 🏗️ Arquitectura General

### Stack Tecnológico
- **Frontend**: Next.js 14 + App Router
- **Componentes**: Shadcn/ui + Tailwind CSS
- **Estado**: React hooks (useState, useEffect)
- **Backend**: Next.js API Routes
- **Base de datos**: PostgreSQL + Drizzle ORM
- **Autenticación**: Clerk
- **Notificaciones**: Sonner (toast)

### Estructura de Archivos
```
app/(dashboard)/dashboard/admin/catalogs/[catalog-name]/
├── page.tsx                    # Componente principal del CRUD
└── loading.tsx                 # Loading state (opcional)

app/api/catalogs/[catalog-name]/
├── route.ts                    # GET, POST, PUT endpoints principales
├── [id]/
│   ├── route.ts               # GET individual, DELETE endpoints
│   └── toggle-status/
│       └── route.ts           # POST para activar/desactivar
```

---

## 🎨 Estándares de UI/UX

### Layout Principal
```typescript
// Estructura base para todos los CRUDs
<div className="space-y-6 p-6">
  {/* Header con navegación y acciones principales */}
  <Header />
  
  {/* Card de filtros de búsqueda */}
  <FiltersCard />
  
  {/* Card principal con grid de datos */}
  <DataGridCard />
  
  {/* Modales para acciones CRUD */}
  <CRUDModals />
</div>
```

### Header Section
```typescript
// Componente de header estándar
<div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
  <div>
    <div className="flex items-center space-x-3 mb-2">
      {/* Botón de regreso */}
      <Button variant="ghost" size="sm" onClick={() => router.back()}>
        <ArrowLeft className="h-4 w-4" />
      </Button>
      
      {/* Título y descripción */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Gestión de [Nombre del Catálogo]
        </h1>
        <p className="text-sm lg:text-base text-gray-600">
          Administra [descripción del catálogo] del sistema
        </p>
      </div>
    </div>
  </div>
  
  {/* Botones de acción */}
  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
    <Button onClick={handleRefresh} variant="outline" size="sm">
      <RefreshCw className="h-4 w-4 mr-2" />
      Actualizar
    </Button>
    <Button variant="outline" size="sm">
      <Download className="h-4 w-4 mr-2" />
      Exportar
    </Button>
    <Button onClick={() => setIsCreateDialogOpen(true)}>
      <Plus className="h-4 w-4 mr-2" />
      Nuevo [Elemento]
    </Button>
  </div>
</div>
```

---

## 🔍 Sistema de Búsqueda y Filtros

### Card de Filtros
```typescript
<Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
  <CardHeader>
    <CardTitle className="text-lg flex items-center gap-2">
      <Search className="h-5 w-5 text-blue-600" />
      Filtros de Búsqueda
    </CardTitle>
  </CardHeader>
  <CardContent>
    <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
      {/* Campo de búsqueda principal */}
      <div className="flex-1">
        <div className="relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Buscar por nombre..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
      </div>
      
      {/* Filtros adicionales */}
      <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
        {/* Filtro por estado (OBLIGATORIO) */}
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <SelectValue placeholder="Todos los estados" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Todos los estados</SelectItem>
            <SelectItem value="active">Activos</SelectItem>
            <SelectItem value="inactive">Inactivos</SelectItem>
          </SelectContent>
        </Select>
        
        {/* Filtros específicos por catálogo */}
        {categoryFilter && (
          <Select value={categoryFilter} onValueChange={setCategoryFilter}>
            <SelectTrigger className="w-full sm:w-48">
              <SelectValue placeholder="Todas las categorías" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Todas las categorías</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )}
      </div>
    </div>
  </CardContent>
</Card>
```

### Filtros Requeridos
1. **Búsqueda por texto**: Siempre incluir búsqueda principal
2. **Estado activo/inactivo**: OBLIGATORIO en todos los catálogos
3. **Categorización**: Si aplica (país, departamento, especialidad, etc.)
4. **Filtros específicos**: Según las necesidades del catálogo

### Estilo para Selectores en Filtros

**IMPORTANTE**: Los selectores de filtros y búsquedas desplegables deben usar el estilo **SIN BORDE** para consistencia visual:

```typescript
// ✅ CORRECTO: Select sin borde visible
<Select value={statusFilter} onValueChange={setStatusFilter}>
  <SelectTrigger className="w-full sm:w-48">
    <SelectValue placeholder="Todos los estados" />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="all">Todos los estados</SelectItem>
    <SelectItem value="active">Activos</SelectItem>
    <SelectItem value="inactive">Inactivos</SelectItem>
  </SelectContent>
</Select>

// ✅ CORRECTO: Select para categorías sin borde personalizado
<Select value={categoryFilter} onValueChange={setCategoryFilter}>
  <SelectTrigger className="w-full sm:w-48">
    <SelectValue placeholder="Todas las categorías" />
  </SelectTrigger>
  <SelectContent>
    {/* Opciones de categorías */}
  </SelectContent>
</Select>
```

**NO agregar clases de borde personalizadas** a menos que sea específicamente requerido para formularios de edición/creación.

---

## 📊 Grid de Datos

### Desktop Table View
```typescript
<div className="hidden lg:block overflow-x-auto">
  <div className="rounded-md border">
    <Table>
      <TableHeader>
        <TableRow>
          {/* Checkbox de selección con COLOR VERDE ESTÁNDAR */}
          <TableHead className="w-12">
            <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
          </TableHead>
          
          {/* Columnas ordenables */}
          <TableHead 
            className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('name')}
          >
            <div className="flex items-center gap-2">
              Nombre
              <SortIcon column="name" />
            </div>
          </TableHead>
          
          {/* Estado (OBLIGATORIO) */}
          <TableHead 
            className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
            onClick={() => handleSort('isActive')}
          >
            <div className="flex items-center gap-2">
              Estado
              <SortIcon column="isActive" />
            </div>
          </TableHead>
          
          {/* Acciones */}
          <TableHead className="font-semibold text-gray-700 text-right">
            Acciones
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {/* Contenido de filas */}
      </TableBody>
    </Table>
  </div>
</div>
```

### Mobile Cards View
```typescript
<div className="lg:hidden space-y-4">
  {items.map((item) => (
    <Card key={item.id} className="hover:shadow-md transition-all duration-200">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start space-x-3">
            <Checkbox className="mt-1 border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
            <div className="space-y-1">
              <div className="flex items-center space-x-2">
                <Icon className="h-4 w-4 text-blue-400" />
                <span className="font-medium">{item.name}</span>
              </div>
              {/* Información adicional */}
            </div>
          </div>
          {/* Dropdown de acciones */}
          <ActionsDropdown item={item} />
        </div>
      </CardContent>
    </Card>
  ))}
</div>
```

### Selectores de Registros (Checkboxes)

**IMPORTANTE**: Todos los checkboxes deben usar el estilo verde estándar:

```typescript
// Estilo OBLIGATORIO para todos los checkboxes
<Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
```

#### Implementación de Selección Múltiple
```typescript
// Estados para selección múltiple
const [selectedItems, setSelectedItems] = useState<string[]>([]);

// Seleccionar/deseleccionar todos
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    setSelectedItems(items.map(item => item.id));
  } else {
    setSelectedItems([]);
  }
};

// Seleccionar individual
const handleSelectItem = (itemId: string, checked: boolean) => {
  if (checked) {
    setSelectedItems(prev => [...prev, itemId]);
  } else {
    setSelectedItems(prev => prev.filter(id => id !== itemId));
  }
};
```

#### Checkbox del Header (Seleccionar todos)
```typescript
<Checkbox
  className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
  checked={selectedItems.length === items.length && items.length > 0}
  onCheckedChange={handleSelectAll}
/>
```

#### Checkbox de Filas (Selección individual)
```typescript
<Checkbox
  className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
  checked={selectedItems.includes(item.id)}
  onCheckedChange={(checked) => handleSelectItem(item.id, checked as boolean)}
/>
```

### Iconografía Estándar

#### Iconos por Funcionalidad
- **👁️ Ver Detalles**: `<Eye className="h-4 w-4" />` - Color blue-600
- **✏️ Editar**: `<Edit className="h-4 w-4" />` - Color blue-600  
- **🗑️ Eliminar**: `<Trash2 className="h-4 w-4" />` - Color red-600
- **✅ Activar**: `<UserCheck className="h-4 w-4" />` o `<ToggleRight className="h-4 w-4" />` - Color green-600
- **❌ Desactivar**: `<UserX className="h-4 w-4" />` o `<ToggleLeft className="h-4 w-4" />` - Color orange-600
- **🔄 Actualizar**: `<RefreshCw className="h-4 w-4" />` - Con animación `animate-spin` cuando está cargando
- **📥 Exportar**: `<Download className="h-4 w-4" />`
- **➕ Crear/Nuevo**: `<Plus className="h-4 w-4" />`
- **🔍 Buscar**: `<Search className="h-4 w-4" />`
- **🔽 Más acciones**: `<MoreHorizontal className="h-4 w-4" />`
- **⬅️ Regresar**: `<ArrowLeft className="h-4 w-4" />`

#### Iconos de Ordenamiento
- **📶 Sin orden**: `<ArrowUpDown className="h-4 w-4" />`
- **⬆️ Ascendente**: `<ArrowUp className="h-4 w-4" />`
- **⬇️ Descendente**: `<ArrowDown className="h-4 w-4" />`

#### Iconos de Estado
- **✅ Activo**: Badge verde con `bg-green-100 text-green-800`
- **⚪ Inactivo**: Badge gris con `bg-gray-100 text-gray-800`
- **⚠️ Pendiente**: Badge amarillo con `bg-yellow-100 text-yellow-800`
- **❌ Suspendido**: Badge rojo con `bg-red-100 text-red-800`

### Ordenamiento
```typescript
// Componente de icono de ordenamiento
const SortIcon = ({ column }: { column: string }) => {
  if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
  return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
};

// Handler de ordenamiento
const handleSort = (column: string) => {
  if (sortBy === column) {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  } else {
    setSortBy(column);
    setSortOrder('asc');
  }
};
```

---

## ⚡ Sistema de Acciones

### Dropdown de Acciones (ESTÁNDAR)
```typescript
<DropdownMenu>
  <DropdownMenuTrigger asChild>
    <Button variant="ghost" className="h-8 w-8 p-0">
      <MoreHorizontal className="h-4 w-4" />
    </Button>
  </DropdownMenuTrigger>
  <DropdownMenuContent align="end">
    <DropdownMenuLabel>Acciones</DropdownMenuLabel>
    
    {/* Ver detalles */}
    <DropdownMenuItem onClick={() => openDetailsDialog(item)}>
      <Eye className="h-4 w-4 mr-2" />
      Ver detalles
    </DropdownMenuItem>
    
    {/* Editar */}
    <DropdownMenuItem onClick={() => openEditDialog(item)}>
      <Edit className="h-4 w-4 mr-2" />
      Editar
    </DropdownMenuItem>
    
    {/* Activar/Desactivar con COLORES ESTÁNDAR */}
    <DropdownMenuItem 
      onClick={() => toggleStatus(item)}
      className={item.isActive ? "text-orange-600" : "text-green-600"}
    >
      <ToggleLeft className="h-4 w-4 mr-2" />
      {item.isActive ? 'Desactivar' : 'Activar'}
    </DropdownMenuItem>
    
    {/* Eliminar con COLOR ESTÁNDAR */}
    <DropdownMenuItem 
      className="text-red-600" 
      onClick={() => openDeleteDialog(item)}
    >
      <Trash2 className="h-4 w-4 mr-2" />
      Eliminar
    </DropdownMenuItem>
  </DropdownMenuContent>
</DropdownMenu>
```

### Colores Estándar para Acciones
- **🟠 Naranja**: Desactivar elementos (`text-orange-600`)
- **🟢 Verde**: Activar elementos (`text-green-600`)
- **🔴 Rojo**: Eliminar elementos (`text-red-600`)

### Toggle Status Function
```typescript
const toggleStatus = async (item: CatalogItem) => {
  try {
    const response = await fetch(`/api/catalogs/[catalog-name]/${item.id}/toggle-status`, {
      method: 'POST'
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Error cambiando estado');
    }

    toast.success(`${catalogName} ${item.isActive ? 'desactivado' : 'activado'} exitosamente`);
    fetchData(); // Recargar datos
  } catch (error: any) {
    toast.error(error.message);
  }
};
```

---

## 📝 Modales y Formularios

### Modal de Creación
```typescript
<Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
  <DialogContent className="sm:max-w-[600px]">
    <DialogHeader>
      <DialogTitle>Crear Nuevo [Elemento]</DialogTitle>
      <DialogDescription>
        Completa la información para crear un nuevo [elemento]
      </DialogDescription>
    </DialogHeader>
    
    <div className="grid gap-4 py-4">
      {/* Campos del formulario */}
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="create-name">Nombre *</Label>
          <Input
            id="create-name"
            value={formData.name}
            onChange={(e) => setFormData({...formData, name: e.target.value})}
            placeholder="Nombre del elemento"
          />
        </div>
        
        {/* Campo de estado OBLIGATORIO */}
        <div className="space-y-2">
          <Label htmlFor="create-active">Estado *</Label>
          <Select 
            value={formData.isActive.toString()} 
            onValueChange={(value) => setFormData({...formData, isActive: value === 'true'})}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Activo</SelectItem>
              <SelectItem value="false">Inactivo</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
    
    <DialogFooter>
      <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
        Cancelar
      </Button>
      <Button onClick={handleCreate} disabled={isCreating}>
        {isCreating ? (
          <>
            <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            Creando...
          </>
        ) : (
          'Crear [Elemento]'
        )}
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Modal de Edición
```typescript
// Similar al de creación, pero pre-poblado con datos existentes
// SIEMPRE incluir campo isActive para edición
```

### Modal de Detalles

#### Modal Simple (Catálogos básicos)
```typescript
<Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
  <DialogContent className="sm:max-w-[600px]">
    <DialogHeader>
      <DialogTitle>Detalles del [Elemento]</DialogTitle>
      <DialogDescription>
        Información detallada del [elemento]
      </DialogDescription>
    </DialogHeader>
    
    {selectedItem && (
      <div className="space-y-4">
        {/* Grid de información */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label className="text-sm font-medium text-gray-700">Nombre</Label>
            <p className="text-sm text-gray-900">{selectedItem.name}</p>
          </div>
          {/* Más campos... */}
        </div>
        
        {/* Estado con badge */}
        <div>
          <Label className="text-sm font-medium text-gray-700">Estado</Label>
          <Badge variant={selectedItem.isActive ? "default" : "secondary"}>
            {selectedItem.isActive ? "Activo" : "Inactivo"}
          </Badge>
        </div>
      </div>
    )}
    
    <DialogFooter>
      <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
        Cerrar
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

#### Modal Expandido con Tabs (Entidades complejas: Usuarios, Pacientes, Expedientes)
```typescript
<Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
  <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
    <DialogHeader>
      <DialogTitle className="text-xl font-semibold">Detalles del [Usuario/Paciente/Expediente]</DialogTitle>
      <DialogDescription>
        Información completa y detallada
      </DialogDescription>
    </DialogHeader>
    
    {selectedItem && (
      <Tabs defaultValue="general" className="flex-1 flex flex-col">
        {/* Tabs responsivos - scroll horizontal en mobile */}
        <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="contact">Contacto</TabsTrigger>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="activity">Actividad</TabsTrigger>
        </TabsList>
        
        {/* Contenido scrolleable */}
        <div className="flex-1 overflow-y-auto px-1">
          <TabsContent value="general" className="space-y-4 mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Información Personal</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                  <p className="text-sm text-gray-900">{selectedItem.firstName} {selectedItem.lastName}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Documento</Label>
                  <p className="text-sm text-gray-900">{selectedItem.documentType} - {selectedItem.documentNumber}</p>
                </div>
                {/* Más campos... */}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="contact" className="space-y-4 mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Información de Contacto</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Email</Label>
                  <p className="text-sm text-gray-900">{selectedItem.email}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                  <p className="text-sm text-gray-900">{selectedItem.phone || 'No registrado'}</p>
                </div>
                {/* Más campos... */}
              </CardContent>
            </Card>
            
            {/* Card de contacto de emergencia */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Contacto de Emergencia</CardTitle>
              </CardHeader>
              <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Campos de emergencia... */}
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="roles" className="space-y-4 mt-0">
            {/* Contenido de roles y permisos */}
          </TabsContent>
          
          <TabsContent value="activity" className="space-y-4 mt-0">
            {/* Historial de actividad */}
          </TabsContent>
        </div>
      </Tabs>
    )}
    
    <DialogFooter className="border-t pt-4">
      <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
        Cerrar
      </Button>
      <Button onClick={() => handleEdit(selectedItem)}>
        <Edit className="h-4 w-4 mr-2" />
        Editar
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Modal de Eliminación
```typescript
<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
  <DialogContent className="sm:max-w-[500px]">
    <DialogHeader>
      <DialogTitle className="flex items-center gap-2">
        <Trash2 className="h-5 w-5 text-red-500" />
        Confirmar Eliminación
      </DialogTitle>
      <DialogDescription>
        Esta acción no se puede deshacer. Seleccione el tipo de eliminación.
      </DialogDescription>
    </DialogHeader>
    
    {itemToDelete && (
      <div className="py-4">
        {/* Información del elemento a eliminar */}
        <div className="p-4 bg-gray-50 rounded-lg mb-4">
          <p className="text-sm font-medium text-gray-900 mb-2">
            [Elemento] a eliminar:
          </p>
          <p className="text-lg font-semibold text-gray-800">
            {itemToDelete.name}
          </p>
        </div>
        
        {/* Opciones de eliminación */}
        <div className="space-y-3">
          <div className="p-3 border border-orange-200 bg-orange-50 rounded-lg">
            <p className="text-sm font-medium text-orange-800 mb-1">
              Eliminación Lógica (Recomendada)
            </p>
            <p className="text-xs text-orange-700">
              Se marcará como inactivo pero se mantendrá para referencia histórica.
            </p>
          </div>
          
          <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
            <p className="text-sm font-medium text-red-800 mb-1">
              Eliminación Física (Permanente)
            </p>
            <p className="text-xs text-red-700">
              Se eliminará completamente. Esta acción es irreversible.
            </p>
          </div>
        </div>
      </div>
    )}
    
    <DialogFooter className="flex gap-2">
      <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
        Cancelar
      </Button>
      <Button 
        variant="outline" 
        className="bg-orange-500 text-white hover:bg-orange-600"
        onClick={() => handleDelete('logical')}
      >
        Eliminar Lógicamente
      </Button>
      <Button 
        variant="destructive"
        onClick={() => handleDelete('physical')}
      >
        Eliminar Físicamente
      </Button>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

### Estándar para Formularios Extendidos (Edición/Creación)

Para entidades complejas que requieren múltiples secciones de información, usar el siguiente patrón:

#### Estructura Base
```typescript
<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
  <DialogContent className="max-w-5xl max-h-[95vh] overflow-hidden flex flex-col p-0">
    {/* Header fijo */}
    <DialogHeader className="p-6 pb-0">
      <DialogTitle className="text-xl font-semibold">
        {isEditing ? 'Editar' : 'Crear'} [Entidad]
      </DialogTitle>
      <DialogDescription>
        Complete toda la información requerida en cada sección
      </DialogDescription>
    </DialogHeader>
    
    {/* Progress indicator para formularios largos */}
    <div className="px-6 py-2">
      <Progress value={calculateProgress()} className="h-2" />
      <p className="text-xs text-gray-500 mt-1">
        {completedSections}/{totalSections} secciones completadas
      </p>
    </div>
    
    {/* Tabs con contenido scrolleable */}
    <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col">
      {/* Tabs responsivos con indicadores de estado */}
      <TabsList className="px-6 grid w-full grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2">
        <TabsTrigger value="general" className="relative">
          <span>General</span>
          {hasErrors.general && (
            <AlertCircle className="h-3 w-3 text-red-500 absolute -top-1 -right-1" />
          )}
          {isCompleted.general && !hasErrors.general && (
            <CheckCircle className="h-3 w-3 text-green-500 absolute -top-1 -right-1" />
          )}
        </TabsTrigger>
        {/* Más tabs con indicadores... */}
      </TabsList>
      
      {/* Contenido del formulario */}
      <div className="flex-1 overflow-y-auto">
        <div className="p-6">
          <TabsContent value="general" className="space-y-4 mt-0">
            <Card>
              <CardHeader>
                <CardTitle className="text-base">Información General</CardTitle>
                <CardDescription>
                  Datos básicos de identificación
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form className="space-y-4">
                  {/* Grid responsivo para campos */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="firstName">
                        Nombre(s) <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="firstName"
                        value={formData.firstName}
                        onChange={(e) => updateFormData('firstName', e.target.value)}
                        className={errors.firstName ? 'border-red-500' : ''}
                      />
                      {errors.firstName && (
                        <p className="text-xs text-red-500">{errors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="lastName">
                        Apellido(s) <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="lastName"
                        value={formData.lastName}
                        onChange={(e) => updateFormData('lastName', e.target.value)}
                        className={errors.lastName ? 'border-red-500' : ''}
                      />
                      {errors.lastName && (
                        <p className="text-xs text-red-500">{errors.lastName}</p>
                      )}
                    </div>
                  </div>
                  
                  {/* Campos de selección */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="documentType">
                        Tipo de Documento <span className="text-red-500">*</span>
                      </Label>
                      <Select
                        value={formData.documentType}
                        onValueChange={(value) => updateFormData('documentType', value)}
                      >
                        <SelectTrigger className={errors.documentType ? 'border-red-500' : ''}>
                          <SelectValue placeholder="Seleccione..." />
                        </SelectTrigger>
                        <SelectContent>
                          {documentTypes.map((type) => (
                            <SelectItem key={type.id} value={type.id}>
                              {type.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    {/* Más campos... */}
                  </div>
                </form>
              </CardContent>
            </Card>
          </TabsContent>
          
          {/* Más tabs con contenido similar... */}
        </div>
      </div>
    </Tabs>
    
    {/* Footer fijo con acciones */}
    <DialogFooter className="border-t p-6 gap-2">
      <div className="flex items-center justify-between w-full">
        <div className="flex gap-2">
          <Button
            variant="ghost"
            onClick={handlePreviousTab}
            disabled={isFirstTab}
          >
            <ChevronLeft className="h-4 w-4 mr-1" />
            Anterior
          </Button>
          <Button
            variant="ghost"
            onClick={handleNextTab}
            disabled={isLastTab}
          >
            Siguiente
            <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </div>
        
        <div className="flex gap-2">
          <Button variant="outline" onClick={handleCancel}>
            Cancelar
          </Button>
          <Button
            onClick={handleSave}
            disabled={!isFormValid || isSaving}
          >
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Guardando...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Guardar
              </>
            )}
          </Button>
        </div>
      </div>
    </DialogFooter>
  </DialogContent>
</Dialog>
```

#### Características Clave para Formularios Extendidos

1. **Navegación por Tabs**
   - Tabs responsivos (2 columnas mobile, 4+ desktop)
   - Indicadores visuales de completitud y errores
   - Navegación con botones Anterior/Siguiente

2. **Validación en Tiempo Real**
   - Validación por campo al perder foco
   - Indicadores de campos requeridos (*)
   - Mensajes de error inline
   - Resumen de errores por sección

3. **Gestión de Estado**
   ```typescript
   // Estado del formulario
   const [formData, setFormData] = useState<FormData>(initialData);
   const [errors, setErrors] = useState<FormErrors>({});
   const [touched, setTouched] = useState<Set<string>>(new Set());
   const [activeTab, setActiveTab] = useState('general');
   
   // Helpers
   const updateFormData = (field: string, value: any) => {
     setFormData(prev => ({ ...prev, [field]: value }));
     setTouched(prev => new Set(prev).add(field));
     validateField(field, value);
   };
   ```

4. **Responsividad**
   - Grids adaptativos (1 columna mobile, 2-3 desktop)
   - Tabs con scroll horizontal en mobile
   - Footer fijo con acciones siempre visibles

5. **UX Mejorada**
   - Progress bar para formularios largos
   - Auto-guardado (opcional)
   - Confirmación al salir con cambios sin guardar
   - Tooltips para campos complejos

#### Casos de Uso

- **Usuarios**: Info personal, contacto, roles, permisos, preferencias
- **Pacientes**: Datos personales, historial médico, alergias, contactos
- **Expedientes**: Consultas, diagnósticos, tratamientos, evolución
- **Citas**: Paciente, médico, fecha/hora, motivo, observaciones
- **Inventario**: Producto, categorías, proveedores, precios, stock

---

## 📄 Paginación

### Reglas de Visualización de Paginación

**IMPORTANTE**: La paginación debe seguir estas reglas estrictas:

#### Regla Principal
- **SOLO mostrar paginación cuando `totalPages > 1`**
- **TODO el bloque de paginación** se envuelve en este condicional
- No mostrar controles de paginación para una sola página de resultados

### Componente de Paginación Estándar
```typescript
{/* Paginación - SOLO visible cuando hay múltiples páginas */}
{totalPages > 1 && (
  <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
    {/* Información de registros y selector - SIEMPRE incluidos cuando hay paginación */}
    <div className="flex items-center space-x-4">
      <div className="text-sm text-gray-600">
        Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, totalCount)} de {totalCount} [elementos]
      </div>
      
      {/* Selector de cantidad por página */}
      <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
        <SelectTrigger className="w-40">
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="10">10 por página</SelectItem>
          <SelectItem value="25">25 por página</SelectItem>
          <SelectItem value="50">50 por página</SelectItem>
          <SelectItem value="100">100 por página</SelectItem>
        </SelectContent>
      </Select>
    </div>
    
    {/* Controles de navegación - números de página y botones */}
    <div className="flex gap-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setPage(page - 1)}
        disabled={page === 1}
        className="hover:bg-gray-50"
      >
        Anterior
      </Button>
      
      {/* Números de página con puntos suspensivos */}
      <div className="flex items-center gap-2">
        {Array.from({ length: totalPages }, (_, i) => i + 1)
          .filter(p => p === 1 || p === totalPages || (p >= page - 2 && p <= page + 2))
          .map((p, idx, arr) => (
            <div key={p} className="flex items-center gap-2">
              {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
              <Button
                variant={p === page ? "default" : "outline"}
                size="sm"
                onClick={() => setPage(p)}
                className={p === page ? "" : "hover:bg-gray-50"}
              >
                {p}
              </Button>
            </div>
          ))}
      </div>
      
      <Button
        variant="outline"
        size="sm"
        onClick={() => setPage(page + 1)}
        disabled={page === totalPages}
        className="hover:bg-gray-50"
      >
        Siguiente
      </Button>
    </div>
  </div>
)}
```

### ❌ ERROR COMÚN: Card Wrapper Innecesario

**NO hacer esto** - No envolver la paginación en un Card:
```typescript
// ❌ INCORRECTO - No usar Card para paginación
{prices.length > 0 && (
  <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
    <CardContent className="p-4">
      {/* Paginación aquí */}
    </CardContent>
  </Card>
)}
```

**✅ CORRECTO** - Paginación directa sin Card wrapper:
```typescript
// ✅ CORRECTO - Paginación sin Card adicional
{totalPages > 1 && (
  <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
    {/* Contenido de paginación */}
  </div>
)}
```

### Configuración de Opciones por Página

**Opciones estándar recomendadas**:
- 10 por página (para catálogos pequeños)
- 25 por página 
- 50 por página
- 100 por página (para catálogos grandes)

**Evitar opciones como**: 5, 20 (usar las estándar arriba)

---

## 📱 Responsive Design

### Breakpoints Estándar
- **Desktop**: `lg:` (1024px+) - Vista de tabla completa
- **Tablet**: `md:` (768px-1023px) - Vista de tabla adaptada
- **Mobile**: `<768px` - Vista de cards

### Pattern de Responsive
```typescript
{/* Vista Desktop */}
<div className="hidden lg:block">
  <TableView />
</div>

{/* Vista Mobile */}
<div className="lg:hidden">
  <CardsView />
</div>
```

---

## 🔄 Manejo de Estados

### Estados Requeridos
```typescript
// Estados de datos
const [items, setItems] = useState<CatalogItem[]>([]);
const [loading, setLoading] = useState(true);
const [refreshing, setRefreshing] = useState(false);

// Estados de filtros
const [searchTerm, setSearchTerm] = useState('');
const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');

// Estados de paginación
const [page, setPage] = useState(1);
const [limit, setLimit] = useState(10);
const [totalPages, setTotalPages] = useState(1);
const [totalCount, setTotalCount] = useState(0);

// Estados de ordenamiento
const [sortBy, setSortBy] = useState('name');
const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

// Estados de modales
const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

// Estados de elementos seleccionados
const [selectedItem, setSelectedItem] = useState<CatalogItem | null>(null);
const [editingItem, setEditingItem] = useState<CatalogItem | null>(null);
const [itemToDelete, setItemToDelete] = useState<CatalogItem | null>(null);

// Estados de formulario
const [formData, setFormData] = useState({
  name: '',
  isActive: true,
  // ... otros campos específicos
});

// Estados de loading para acciones
const [isCreating, setIsCreating] = useState(false);
const [isEditing, setIsEditing] = useState(false);
```

### useEffect para Data Fetching
```typescript
// Fetch principal cuando cambian filtros/paginación
useEffect(() => {
  fetchData();
}, [page, limit, searchTerm, statusFilter, sortBy, sortOrder]);

// Reset de página cuando cambian filtros
useEffect(() => {
  setPage(1);
}, [searchTerm, statusFilter]);
```

---

## ✅ Validaciones

### Validaciones Frontend
```typescript
const handleCreate = async () => {
  // Validaciones básicas
  if (!formData.name.trim()) {
    toast.error('El nombre es requerido');
    return;
  }
  
  if (formData.name.length < 2) {
    toast.error('El nombre debe tener al menos 2 caracteres');
    return;
  }
  
  // Validaciones específicas según catálogo
  if (catalogType === 'document-types' && !formData.format) {
    toast.error('El formato es requerido');
    return;
  }
  
  // Continuar con la creación...
};
```

### Validaciones Backend (API)
```typescript
// En route.ts
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validación de campos requeridos
    if (!body.name || !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }
    
    // Validaciones de negocio específicas
    // ...
    
    // Continuar con la creación
  } catch (error) {
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}
```

---

## 🔌 APIs y Backend

### Estructura de Endpoints
```typescript
// GET /api/catalogs/[catalog-name]
// - Listado con paginación, filtros y ordenamiento
// - Query params: page, limit, search, status, sortBy, sortOrder

// POST /api/catalogs/[catalog-name]
// - Crear nuevo elemento
// - Body: datos del elemento

// PUT /api/catalogs/[catalog-name]
// - Actualizar elemento existente
// - Body: id + datos actualizados

// GET /api/catalogs/[catalog-name]/[id]
// - Obtener elemento específico

// DELETE /api/catalogs/[catalog-name]/[id]?type=logical|physical
// - Eliminar elemento (lógica o física)

// POST /api/catalogs/[catalog-name]/[id]/toggle-status
// - Activar/desactivar elemento
```

### Response Format
```typescript
// Respuesta estándar para listados
{
  "data": CatalogItem[],
  "pagination": {
    "page": number,
    "limit": number,
    "total": number,
    "totalPages": number,
    "hasNext": boolean,
    "hasPrev": boolean
  },
  "categories"?: string[], // Si aplica
  "stats"?: any // Si aplica
}

// Respuesta estándar para operaciones
{
  "success": boolean,
  "message": string,
  "data"?: any
}

// Respuesta de error
{
  "error": string,
  "details"?: any
}
```

---

## 🎨 Colores y Temas

### Paleta de Colores Principal
- **Primario**: Blue (`text-blue-600`, `bg-blue-600`)
- **Éxito**: Green (`text-green-600`, `bg-green-600`)
- **Advertencia**: Orange (`text-orange-600`, `bg-orange-600`)
- **Error**: Red (`text-red-600`, `bg-red-600`)
- **Neutro**: Gray (`text-gray-600`, `bg-gray-50`)

### Colores para Estados
- **Activo**: `bg-green-100 text-green-800` (Badge)
- **Inactivo**: `bg-gray-100 text-gray-800` (Badge)

### Colores para Acciones
- **Ver**: Blue (`text-blue-600`)
- **Editar**: Blue (`text-blue-600`)
- **Activar**: Green (`text-green-600`)
- **Desactivar**: Orange (`text-orange-600`)
- **Eliminar**: Red (`text-red-600`)

---

## ♿ Accesibilidad

### Estándares WCAG 2.1
- **Contraste**: Mínimo 4.5:1 para texto normal
- **Navegación**: Soporte completo para teclado
- **Screen readers**: Labels apropiados en todos los inputs
- **Focus**: Indicadores visibles de foco

### Implementación
```typescript
// Labels en inputs
<Label htmlFor="input-id">Texto descriptivo</Label>
<Input id="input-id" aria-describedby="help-text" />

// Botones con aria-label
<Button aria-label="Eliminar elemento">
  <Trash2 className="h-4 w-4" />
</Button>

// Estados de loading
<Button disabled={isLoading} aria-disabled={isLoading}>
  {isLoading ? 'Cargando...' : 'Acción'}
</Button>
```

---

## 🔧 Patrones de Código

### TypeScript Interfaces
```typescript
// Interface base para todos los catálogos
interface BaseCatalogItem {
  id: string;
  name: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

// Extender para catálogos específicos
interface SpecificCatalogItem extends BaseCatalogItem {
  category?: string;
  description?: string;
  // campos específicos...
}
```

### Custom Hooks (Recomendado)
```typescript
// Hook personalizado para manejo de CRUD
const useCatalogCRUD = (catalogName: string) => {
  // Estados y lógica reutilizable
  // Funciones de fetch, create, update, delete
  // Retornar objetos y funciones necesarias
};
```

### Helpers y Utilities
```typescript
// utils/catalog-helpers.ts
export const formatCatalogName = (name: string) => {
  // Lógica de formateo
};

export const validateCatalogItem = (item: any) => {
  // Lógica de validación
};
```

---

## 📋 Checklist de Implementación

### ✅ Antes de Implementar
- [ ] Definir interface TypeScript del catálogo
- [ ] Crear schema de base de datos con Drizzle
- [ ] Configurar seed data inicial
- [ ] Definir validaciones de negocio

### ✅ Durante Implementación
- [ ] Implementar todos los endpoints de API
- [ ] Crear componente principal siguiendo la estructura estándar
- [ ] Implementar filtros requeridos (búsqueda + estado + específicos)
- [ ] Implementar grid responsive (desktop + mobile)
- [ ] **APLICAR ESTILO VERDE** a todos los checkboxes (`border-green-500 data-[state=checked]:bg-green-500`)
- [ ] **APLICAR COLORES ESTÁNDAR** en dropdown de acciones (verde=activar, naranja=desactivar, rojo=eliminar)
- [ ] Implementar todos los modales CRUD (simple para catálogos, expandido para entidades complejas)
- [ ] Configurar paginación del lado del servidor
- [ ] Implementar ordenamiento por columnas con iconos estándar
- [ ] Aplicar iconografía estándar en todas las acciones
- [ ] Agregar campo isActive en formularios
- [ ] Implementar validaciones frontend y backend
- [ ] Agregar manejo de errores y loading states
- [ ] **IMPLEMENTAR SELECCIÓN MÚLTIPLE** con checkboxes verdes
- [ ] **MODAL EXPANDIDO** para entidades complejas con tabs (usuarios, pacientes, expedientes)

### ✅ Testing y QA
- [ ] Probar todas las operaciones CRUD
- [ ] Verificar responsive design en mobile
- [ ] Testear filtros y paginación
- [ ] Validar accesibilidad básica
- [ ] Probar manejo de errores
- [ ] Verificar consistencia de colores y UI

### ✅ Documentación
- [ ] Actualizar documentación de APIs
- [ ] Documentar campos específicos del catálogo
- [ ] Agregar ejemplos de uso
- [ ] Actualizar changelog

---

## 🎯 Mejores Prácticas y Lecciones Aprendidas

### ✅ Consistencia Visual
1. **Checkboxes siempre verdes**: Todos los selectores deben usar `border-green-500 data-[state=checked]:bg-green-500`
2. **Colores de acciones estandarizados**: Verde=activar, Naranja=desactivar, Rojo=eliminar
3. **Iconografía consistente**: Usar los mismos iconos para las mismas funciones en todo el sistema

### ✅ Arquitectura de Formularios
1. **Modal Simple vs Expandido**: 
   - Simple para catálogos básicos (países, ocupaciones)
   - Expandido con tabs para entidades complejas (usuarios, pacientes, expedientes)
2. **Navigation patterns**: "Ver detalles" usa modales, no páginas separadas
3. **Mobile-first**: Todos los modales deben ser responsivos

### ✅ Gestión de Estado
1. **Evitar conflictos de variables**: Usar nombres descriptivos (`targetUser` vs `user`)
2. **Estados de loading**: Siempre mostrar estados de carga para mejor UX
3. **Validación en tiempo real**: Feedback inmediato en formularios

### ✅ Patrones de Código
```typescript
// ✅ CORRECTO: Nombres descriptivos para evitar conflictos
const targetUser = data?.users.find(u => u.id === userId);
const selectedCountry = countries.find(c => c.id === countryId);

// ❌ INCORRECTO: Variables genéricas que pueden causar conflictos
const user = data?.users.find(u => u.id === userId);
const item = items.find(i => i.id === itemId);
```

### ✅ Responsive Design
1. **Breakpoints consistentes**:
   - Mobile: `<768px` - Vista de cards
   - Tablet: `md:` (768px-1023px) - Vista adaptada
   - Desktop: `lg:` (1024px+) - Vista completa

2. **Grid responsivo**:
   ```typescript
   // Campos de formulario
   <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
   
   // Tabs en modales expandidos
   <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4 mb-4">
   ```

### ✅ Performance
1. **Paginación del servidor**: Siempre implementar paginación backend
2. **Lazy loading**: Para grids con muchos registros
3. **Debounce en búsquedas**: Evitar requests excesivos

### ✅ Accesibilidad
1. **Labels descriptivos**: Todos los inputs deben tener labels apropiados
2. **Estados de focus**: Indicadores visuales claros
3. **Navegación por teclado**: Soporte completo para keyboard navigation

---

## 🏢 CRUDs Complejos/Ampliados - Estándar para Entidades con Múltiples Secciones

### 📋 Diferencias Fundamentales

#### CRUDs Simples (Catálogos)
- **Propósito**: Administrar catálogos básicos (países, ocupaciones, especialidades)
- **Navegación**: Modales para ver/editar/crear
- **Estructura**: Lista + filtros + modales simples
- **Complejidad**: Baja (2-5 campos típicamente)

#### CRUDs Complejos (Entidades Principales)
- **Propósito**: Administrar entidades principales del sistema (usuarios, pacientes, citas, expedientes)
- **Navegación**: Páginas dedicadas con tabs para ver/editar
- **Estructura**: Lista + página de detalles con tabs + formularios complejos
- **Complejidad**: Alta (10+ campos organizados en secciones)

### 🏗️ Arquitectura de CRUDs Complejos

#### Estructura de Archivos
```
app/(dashboard)/dashboard/admin/[entity]/
├── page.tsx                    # Grid principal de listado
├── [id]/
│   └── page.tsx                # Página de detalles con tabs
└── create/
    └── page.tsx                # Página de creación (opcional)

app/api/admin/[entity]/
├── route.ts                    # Endpoints principales
├── [id]/
│   ├── route.ts               # CRUD individual
│   ├── analyze-deletion/
│   │   └── route.ts           # Análisis de impacto de eliminación
│   └── reassign-rejected-role/
│       └── route.ts           # Acciones específicas de la entidad
```

### 🎨 Diseño Visual y UX

#### 1. Header Responsivo Estándar
```typescript
// Layout principal para entidades complejas
<div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
  <div className="flex items-center space-x-3">
    <Button variant="ghost" size="sm" onClick={() => router.push('/dashboard/admin/users')}>
      <ArrowLeft className="h-4 w-4" />
    </Button>
    <div>
      <h1 className="text-2xl font-bold text-gray-900">
        {entity.firstName} {entity.lastName}
      </h1>
      <p className="text-gray-600 text-sm">
        ID: {entity.id}
      </p>
      
      {/* Badges en móvil - IMPORTANTE para responsividad */}
      <div className="flex items-center gap-2 mt-2 lg:hidden">
        <Badge className={statusColors[entity.status]}>
          {statusLabels[entity.status]}
        </Badge>
        {entity.verified && (
          <Badge variant="secondary">Verificado</Badge>
        )}
      </div>
    </div>
  </div>
  
  <div className="flex items-center gap-2">
    {/* Badges en desktop */}
    <div className="hidden lg:flex items-center gap-2">
      <Badge className={statusColors[entity.status]}>
        {statusLabels[entity.status]}
      </Badge>
      {entity.verified && (
        <Badge variant="secondary">Verificado</Badge>
      )}
    </div>
  </div>
</div>
```

#### 2. Sistema de Tabs Personalizado (NO Shadcn/UI)
```typescript
// Implementación de tabs custom siguiendo el estándar establecido
<div className="space-y-4">
  {/* Custom Tab Navigation */}
  <div className="bg-white rounded-lg shadow-sm border">
    <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
      <button
        onClick={() => setActiveTab('general')}
        className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
          activeTab === 'general'
            ? 'bg-blue-500 text-white shadow-md'
            : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
        }`}
      >
        <div className="text-center">
          <div className="font-semibold">Información General</div>
        </div>
      </button>
      {/* Más tabs... */}
    </div>
  </div>
  
  {/* Contenido de los tabs */}
  {activeTab === 'general' && (
    <>
      <div className="space-y-4">
        {/* Cards organizadas verticalmente - NUEVO ESTÁNDAR */}
        <div className="space-y-4">
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            {/* Contenido del card */}
          </Card>
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            {/* Siguiente card */}
          </Card>
        </div>
      </div>
    </>
  )}
</div>
```

#### 3. Organización Vertical de Secciones
**ESTÁNDAR ESTABLECIDO**: Las secciones dentro de cada tab se organizan **verticalmente** (una arriba de la otra) para aprovechar mejor el espacio horizontal.

```typescript
// ✅ CORRECTO: Layout vertical
<div className="space-y-4">
  <div className="space-y-4">  {/* NO usar grid md:grid-cols-2 */}
    <Card>Información Personal</Card>
    <Card>Estado de Verificación</Card>
    <Card>Datos Adicionales</Card>
  </div>
</div>

// ❌ INCORRECTO: Layout de dos columnas (usado previamente)
<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
  <Card>Información Personal</Card>
  <Card>Estado de Verificación</Card>
</div>
```

#### 4. Posicionamiento de Botones de Acción
**ESTÁNDAR**: Los botones de acción siempre en la **parte inferior derecha**, sin paneles adicionales.

```typescript
// Botones de acción - Minimalista y consistente
{isEditMode ? (
  <div className="flex justify-end gap-3 pt-4">
    <Button variant="outline" onClick={() => router.push('/dashboard/admin/users')}>
      Cancelar
    </Button>
    <Button onClick={handleSave} className="bg-emerald-600 hover:bg-emerald-700 text-white">
      <Save className="h-4 w-4 mr-2" />
      {processing ? 'Guardando...' : 'Guardar Cambios'}
    </Button>
  </div>
) : (
  <div className="flex justify-end gap-3 pt-4">
    <Button onClick={() => router.push(`/dashboard/admin/users/${user.id}?mode=edit`)}
           className="bg-emerald-600 hover:bg-emerald-700 text-white">
      <Edit className="h-4 w-4 mr-2" />
      Editar
    </Button>
  </div>
)}
```

### 🎨 Estándares de Color y Visuales

#### Colores Principales
- **Tabs Activos**: `bg-blue-500 text-white shadow-md`
- **Tabs Inactivos**: `text-gray-600 hover:bg-gray-50 hover:text-gray-900`
- **Botón Principal**: `bg-emerald-600 hover:bg-emerald-700 text-white`
- **Botón Secundario**: `variant="outline"`

#### Cards y Sombras
```typescript
// Estilo estándar para cards en CRUDs complejos
<Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
  <CardHeader>
    <CardTitle className="text-lg flex items-center gap-2">
      <IconComponent className="h-5 w-5 text-blue-600" />
      Título de la Sección
    </CardTitle>
  </CardHeader>
  <CardContent>
    {/* Contenido organizado en grid cuando sea necesario */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Campos de información */}
    </div>
  </CardContent>
</Card>
```

### 🔄 Modos de Visualización

#### Modo "Ver Detalles" (Solo Lectura)
- **Navegación**: Desde grid principal al hacer clic en "Ver detalles"
- **URL**: `/dashboard/admin/users/[id]`
- **Características**:
  - Campos mostrados como texto de solo lectura
  - Un solo botón "Editar" en la parte inferior derecha
  - Tabs navegables para ver diferentes secciones
  - No se muestran controles de formulario

#### Modo "Editar" (Formulario)
- **Navegación**: Desde "Ver detalles" o directamente desde grid con "Editar"
- **URL**: `/dashboard/admin/users/[id]?mode=edit`
- **Características**:
  - Campos convertidos en inputs/selects editables
  - Botones "Cancelar" y "Guardar Cambios" en la parte inferior derecha
  - Validación en tiempo real
  - Campos de formulario con tipos apropiados (Select para catálogos)

### 📝 Formularios Inteligentes

#### Tipos de Campo por Contexto
```typescript
// ✅ USAR Select para campos con catálogos
<div>
  <Label className="text-sm font-medium text-gray-700">Tipo de Documento</Label>
  <Select value={formData.documentType} onValueChange={(value) => setFormData({...formData, documentType: value})}>
    <SelectTrigger className="mt-1">
      <SelectValue placeholder="Selecciona tipo de documento" />
    </SelectTrigger>
    <SelectContent>
      {documentTypes.map((type) => (
        <SelectItem key={type.id} value={type.name}>
          {type.name}
        </SelectItem>
      ))}
    </SelectContent>
  </Select>
</div>

// ✅ USAR Select para opciones limitadas
<div>
  <Label className="text-sm font-medium text-gray-700">Género</Label>
  <Select value={formData.gender} onValueChange={(value) => setFormData({...formData, gender: value})}>
    <SelectTrigger className="mt-1">
      <SelectValue placeholder="Selecciona género" />
    </SelectTrigger>
    <SelectContent>
      <SelectItem value="masculino">Masculino</SelectItem>
      <SelectItem value="femenino">Femenino</SelectItem>
      <SelectItem value="otro">Otro</SelectItem>
    </SelectContent>
  </Select>
</div>
```

#### Carga Dinámica de Catálogos
```typescript
// Hook para cargar catálogos relacionados
const fetchCatalogs = async () => {
  try {
    // Cargar tipos de documento
    const documentTypesResponse = await fetch('/api/catalogs/document-types');
    if (documentTypesResponse.ok) {
      const documentTypesData = await documentTypesResponse.json();
      setDocumentTypes(documentTypesData.data || []);
    }

    // Cargar relaciones (para contacto de emergencia)
    const relationshipsResponse = await fetch('/api/catalogs/relationships');
    if (relationshipsResponse.ok) {
      const relationshipsData = await relationshipsResponse.json();
      setRelationships(relationshipsData.data || []);
    }
  } catch (error) {
    console.error('Error cargando catálogos:', error);
  }
};

useEffect(() => {
  fetchUser();
  fetchCatalogs(); // Cargar catálogos al inicializar
}, [resolvedParams.id]);
```

### 🔗 Navegación y Flujo de Usuario

#### Flujo de Navegación Estándar
1. **Lista Principal** → Grid con acciones "Ver detalles" y "Editar"
2. **Ver Detalles** → Página con tabs, botón "Editar"
3. **Modo Edición** → Misma página con formularios, botones "Cancelar" / "Guardar"
4. **Cancelar** → Regresa al grid principal (NO a modo de solo lectura)
5. **Guardar** → Permanece en la página, muestra éxito

#### Gestión de Estados
```typescript
// Estados principales para CRUDs complejos
const [user, setUser] = useState<UserData | null>(null);
const [loading, setLoading] = useState(true);
const [processing, setProcessing] = useState(false);
const [error, setError] = useState('');

// Estados para modo de visualización
const [isEditMode, setIsEditMode] = useState(searchParams.get('mode') === 'edit');
const [activeTab, setActiveTab] = useState('general');

// Estados para catálogos (si aplica)
const [documentTypes, setDocumentTypes] = useState<any[]>([]);
const [relationships, setRelationships] = useState<any[]>([]);

// Estados para el formulario de edición
const [editFormData, setEditFormData] = useState({
  firstName: '',
  lastName: '',
  documentType: '',
  // ... todos los campos editables
});
```

### 🎯 Casos de Uso Específicos

#### Entidades que Requieren CRUD Complejo
1. **Usuarios**: Info personal, contacto, roles, permisos, actividad
2. **Pacientes**: Datos personales, historial médico, alergias, contactos de emergencia
3. **Expedientes Médicos**: Consultas, diagnósticos, tratamientos, evolución
4. **Citas**: Paciente, médico, fecha/hora, motivo, observaciones, seguimiento
5. **Personal Médico**: Información profesional, especialidades, horarios, credenciales
6. **Historias Clínicas**: Antecedentes, exámenes, tratamientos, evolución

#### Patrones de Tabs por Entidad
```typescript
// Usuarios
const userTabs = ['general', 'contact', 'roles', 'activity'];

// Pacientes  
const patientTabs = ['personal', 'medical', 'allergies', 'contacts', 'history'];

// Expedientes
const recordTabs = ['general', 'consultations', 'diagnoses', 'treatments', 'evolution'];

// Citas
const appointmentTabs = ['details', 'patient', 'doctor', 'notes', 'followup'];
```

### 📱 Responsividad Avanzada

#### Breakpoints Específicos para CRUDs Complejos
```typescript
// Header responsivo
<div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">

// Badges móviles vs desktop
<div className="flex items-center gap-2 mt-2 lg:hidden"> {/* Móvil */}
<div className="hidden lg:flex items-center gap-2"> {/* Desktop */}

// Tabs responsivos
<div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">

// Botones responsivos
<Button className="w-full sm:w-auto">

// Grids de contenido
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
```

### ✅ Checklist para CRUDs Complejos

#### Estructura y Navegación
- [ ] Página de listado principal con grid
- [ ] Página de detalles dedicada (`/[id]`)
- [ ] Sistema de tabs personalizado (NO shadcn/ui)
- [ ] Modo "Ver detalles" vs "Editar" con URL params
- [ ] Navegación coherente (Cancelar → Grid, Guardar → Misma página)

#### Visual y UX
- [ ] Header responsivo con badges móviles/desktop
- [ ] Organización vertical de secciones (space-y-4)
- [ ] Botones de acción en parte inferior derecha
- [ ] Colores estándar (azul para tabs, verde esmeralda para acciones)
- [ ] Transiciones suaves y hover effects

#### Formularios y Datos
- [ ] Select para campos con catálogos (no Input libre)
- [ ] Carga dinámica de datos de catálogos
- [ ] Validación en tiempo real
- [ ] Estados de procesamiento visibles
- [ ] Manejo de errores apropiado

#### Responsividad
- [ ] Breakpoints estándar (lg, md, sm)
- [ ] Tabs responsivos con scroll en móvil
- [ ] Grids adaptativos según contenido
- [ ] Botones con ancho completo en móvil

### 🎨 Diferencias Visuales Clave

| Aspecto | CRUDs Simples (Catálogos) | CRUDs Complejos (Entidades) |
|---------|---------------------------|------------------------------|
| **Navegación** | Modales para ver/editar | Páginas dedicadas con tabs |
| **Layout** | Grid + Modales | Grid → Página de detalles |
| **Formularios** | Modal simple | Página completa con secciones |
| **Tabs** | NO aplica | Tabs personalizados (NO shadcn) |
| **Botones** | En footer de modal | Inferior derecha de página |
| **Secciones** | Una sola sección | Múltiples tabs organizados |
| **Responsividad** | Modal responsivo | Header + tabs + secciones responsivas |
| **Complejidad** | 2-5 campos | 10+ campos en múltiples secciones |

---

## 🚀 Próximos Pasos

1. **Estandarización Completa**: Aplicar estos estándares a todos los catálogos existentes
2. **Optimización**: Implementar lazy loading y virtual scrolling para grids grandes
3. **Testing Automatizado**: Crear suite de tests para componentes CRUD
4. **Performance**: Optimizar queries de base de datos y caching
5. **Internacionalización**: Preparar para múltiples idiomas
6. **Analytics**: Implementar tracking de uso de catálogos

---

**📝 Última actualización**: Julio 2025  
**🔄 Versión**: 1.0  
**👥 Equipo**: Desarrollo SGC