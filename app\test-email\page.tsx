'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';

interface Appointment {
  id: string;
  scheduledDate: string;
  startTime: string;
  status: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  consultoryName: string;
}

export default function TestEmailPage() {
  const [email, setEmail] = useState('');
  const [selectedAppointment, setSelectedAppointment] = useState('');
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingAppointments, setLoadingAppointments] = useState(false);

  useEffect(() => {
    fetchAppointments();
  }, []);

  const fetchAppointments = async () => {
    setLoadingAppointments(true);
    try {
      const response = await fetch('/api/test-email');
      const data = await response.json();
      setAppointments(data.appointments || []);
    } catch (error) {
      toast.error('Error al cargar citas');
    } finally {
      setLoadingAppointments(false);
    }
  };

  const sendTestEmail = async () => {
    if (!email) {
      toast.error('Por favor ingresa un email');
      return;
    }

    if (!selectedAppointment) {
      toast.error('Por favor selecciona una cita');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch('/api/test-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ 
          email,
          appointmentId: selectedAppointment 
        }),
      });

      const data = await response.json();
      
      if (data.emailSent) {
        toast.success('¡Email con enlace de check-in enviado! Revisa tu bandeja de entrada');
        toast.info(`Link: ${data.checkInLink}`);
      } else {
        toast.error(data.error || 'Error al enviar email');
      }
    } catch (error) {
      toast.error('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  const getStatusBadge = (status: string) => {
    const colors = {
      confirmed: 'bg-green-100 text-green-800',
      scheduled: 'bg-blue-100 text-blue-800',
      checked_in: 'bg-teal-100 text-teal-800',
      completed: 'bg-gray-100 text-gray-800',
      cancelled: 'bg-red-100 text-red-800',
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Panel de envío de email */}
        <Card>
          <CardHeader>
            <CardTitle>🔗 Probar Email con Check-in</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="text-sm font-medium">Email de destino:</label>
              <Input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="mt-1"
              />
            </div>

            <div>
              <label className="text-sm font-medium">Seleccionar cita:</label>
              <Select value={selectedAppointment} onValueChange={setSelectedAppointment}>
                <SelectTrigger className="mt-1">
                  <SelectValue placeholder="Selecciona una cita..." />
                </SelectTrigger>
                <SelectContent>
                  {appointments.map((appointment) => (
                    <SelectItem key={appointment.id} value={appointment.id}>
                      <div className="flex items-center space-x-2">
                        <span>
                          {appointment.patientFirstName} {appointment.patientLastName}
                        </span>
                        <span className="text-gray-500">
                          - {new Date(appointment.scheduledDate).toLocaleDateString()}
                        </span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <Button 
              onClick={sendTestEmail}
              disabled={loading || !selectedAppointment || !email}
              className="w-full"
            >
              {loading ? 'Enviando...' : 'Enviar Email con Check-in'}
            </Button>

            <div className="text-sm text-gray-600 mt-4">
              <p>✅ Email incluye enlace de check-in</p>
              <p>📧 Template de recordatorio 24h</p>
              <p>📬 Revisa también spam/promociones</p>
              <p>🔗 El enlace permite auto check-in del paciente</p>
            </div>
          </CardContent>
        </Card>

        {/* Panel de citas disponibles */}
        <Card>
          <CardHeader>
            <CardTitle>📅 Citas Disponibles</CardTitle>
          </CardHeader>
          <CardContent>
            {loadingAppointments ? (
              <p className="text-center text-gray-500">Cargando citas...</p>
            ) : appointments.length === 0 ? (
              <p className="text-center text-gray-500">No hay citas disponibles</p>
            ) : (
              <div className="space-y-3 max-h-80 overflow-y-auto">
                {appointments.map((appointment) => (
                  <div 
                    key={appointment.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedAppointment === appointment.id 
                        ? 'border-blue-500 bg-blue-50' 
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedAppointment(appointment.id)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">
                          {appointment.patientFirstName} {appointment.patientLastName}
                        </div>
                        <div className="text-sm text-gray-600">
                          Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Date(appointment.scheduledDate).toLocaleDateString()} - 
                          {new Date(appointment.startTime).toLocaleTimeString('es-ES', {
                            hour: '2-digit',
                            minute: '2-digit'
                          })}
                        </div>
                      </div>
                      <Badge className={getStatusBadge(appointment.status)}>
                        {appointment.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}