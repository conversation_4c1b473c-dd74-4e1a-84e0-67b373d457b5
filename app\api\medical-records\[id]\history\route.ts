import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  patientMedicalHistory, 
  medicalRecords,
  pathologicalHistory,
  nonPathologicalHistory,
  medications
} from '@/db/schema';
import { eq, and, ilike, or } from 'drizzle-orm';

// GET - Obtener antecedentes médicos de un expediente
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const recordId = params.id;

    // Verificar que el expediente existe
    const recordExists = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (recordExists.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Obtener antecedentes médicos
    const historyResult = await db
      .select()
      .from(patientMedicalHistory)
      .where(eq(patientMedicalHistory.medicalRecordId, recordId))
      .limit(1);

    if (historyResult.length === 0) {
      // Si no existe, crear estructura vacía
      const newHistory = {
        id: `hist_${recordId}`,
        medicalRecordId: recordId,
        pathologicalHistory: [],
        nonPathologicalHistory: [],
        familyHistory: [],
        allergies: [],
        hospitalizations: [],
        surgeries: [],
        vaccinations: [],
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = await db.insert(patientMedicalHistory).values(newHistory).returning();
      
      return NextResponse.json({
        data: result[0],
      });
    }

    return NextResponse.json({
      data: historyResult[0],
    });
  } catch (error) {
    console.error('Error fetching medical history:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// PUT - Actualizar antecedentes médicos
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const recordId = params.id;
    const body = await request.json();

    // Verificar que el expediente existe
    const recordExists = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (recordExists.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Campos permitidos para actualización
    const allowedFields = [
      'pathologicalHistory',
      'nonPathologicalHistory',
      'familyHistory',
      'allergies',
      'hospitalizations',
      'surgeries',
      'vaccinations'
    ];

    const updateData: any = {
      updatedAt: new Date(),
      updatedBy: userId,
    };

    // Solo incluir campos permitidos que estén presentes en el body
    allowedFields.forEach(field => {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    });

    // Verificar si ya existe el registro de antecedentes
    const existingHistory = await db
      .select()
      .from(patientMedicalHistory)
      .where(eq(patientMedicalHistory.medicalRecordId, recordId))
      .limit(1);

    let result;

    if (existingHistory.length === 0) {
      // Crear nuevo registro
      const newHistory = {
        id: `hist_${recordId}`,
        medicalRecordId: recordId,
        pathologicalHistory: body.pathologicalHistory || [],
        nonPathologicalHistory: body.nonPathologicalHistory || [],
        familyHistory: body.familyHistory || [],
        allergies: body.allergies || [],
        hospitalizations: body.hospitalizations || [],
        surgeries: body.surgeries || [],
        vaccinations: body.vaccinations || [],
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      result = await db.insert(patientMedicalHistory).values(newHistory).returning();
    } else {
      // Actualizar registro existente
      result = await db
        .update(patientMedicalHistory)
        .set(updateData)
        .where(eq(patientMedicalHistory.medicalRecordId, recordId))
        .returning();
    }

    return NextResponse.json({
      message: 'Antecedentes médicos actualizados exitosamente',
      data: result[0],
    });
  } catch (error) {
    console.error('Error updating medical history:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// DELETE - Limpiar antecedentes médicos (poco común, pero útil para pruebas)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const recordId = params.id;

    // Verificar que el expediente existe
    const recordExists = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, recordId))
      .limit(1);

    if (recordExists.length === 0) {
      return NextResponse.json({ error: 'Expediente no encontrado' }, { status: 404 });
    }

    // Limpiar antecedentes (mantener estructura pero vaciar arrays)
    const result = await db
      .update(patientMedicalHistory)
      .set({
        pathologicalHistory: [],
        nonPathologicalHistory: [],
        familyHistory: [],
        allergies: [],
        hospitalizations: [],
        surgeries: [],
        vaccinations: [],
        updatedAt: new Date(),
        updatedBy: userId,
      })
      .where(eq(patientMedicalHistory.medicalRecordId, recordId))
      .returning();

    return NextResponse.json({
      message: 'Antecedentes médicos limpiados exitosamente',
      data: result[0] || null,
    });
  } catch (error) {
    console.error('Error clearing medical history:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}