import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq, and, gte, lte, isNull, or, isNotNull } from 'drizzle-orm';
import { sendAppointmentReminder } from '@/lib/guardian-utils';

// POST - Enviar recordatorios 24h antes (para ser ejecutado por cron job)
export async function POST(request: NextRequest) {
  try {
    // Verificar header de autorización para cron jobs
    const authHeader = request.headers.get('authorization');
    const cronSecret = process.env.CRON_SECRET || 'dev-secret';
    
    if (authHeader !== `Bearer ${cronSecret}`) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('🕐 Iniciando envío de recordatorios 24h...');

    // Calcular el rango de fechas para citas que necesitan recordatorio 24h
    // Buscar citas que son en exactamente 24 horas (con margen de 1 hora)
    const now = new Date();
    const in23Hours = new Date(now.getTime() + (23 * 60 * 60 * 1000));
    const in25Hours = new Date(now.getTime() + (25 * 60 * 60 * 1000));

    console.log('📅 Buscando citas entre:', {
      desde: in23Hours.toISOString(),
      hasta: in25Hours.toISOString()
    });

    // Buscar citas que necesitan recordatorio 24h
    const appointmentsNeedingReminder = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        consultoryId: appointments.consultoryId,
        reminderSent24h: appointments.reminderSent24h,
        status: appointments.status,
      })
      .from(appointments)
      .where(
        and(
          // Citas programadas entre 23-25 horas desde ahora
          gte(appointments.startTime, in23Hours),
          lte(appointments.startTime, in25Hours),
          // Que tengan paciente asignado
          isNotNull(appointments.patientId),
          // Que no se haya enviado recordatorio 24h aún
          or(
            isNull(appointments.reminderSent24h),
            eq(appointments.reminderSent24h, false)
          ),
          // Solo citas activas
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed')
          )
        )
      );

    console.log(`📋 Encontradas ${appointmentsNeedingReminder.length} citas que necesitan recordatorio 24h`);

    let emailsSent = 0;
    let emailsError = 0;

    // Procesar cada cita
    for (const appointment of appointmentsNeedingReminder) {
      try {
        if (!appointment.patientId) {
          console.log(`⏭️ Saltando cita ${appointment.id} - sin paciente asignado`);
          continue;
        }

        // Obtener información completa de la cita
        const appointmentInfo = await db
          .select({
            id: appointments.id,
            title: appointments.title,
            scheduledDate: appointments.scheduledDate,
            startTime: appointments.startTime,
            endTime: appointments.endTime,
            doctorFirstName: user.firstName,
            doctorLastName: user.lastName,
            consultoryName: consultories.name,
          })
          .from(appointments)
          .leftJoin(user, eq(appointments.doctorId, user.id))
          .leftJoin(consultories, eq(appointments.consultoryId, consultories.id))
          .where(eq(appointments.id, appointment.id))
          .limit(1);

        if (appointmentInfo.length === 0) {
          console.error(`❌ No se pudo obtener información completa de la cita ${appointment.id}`);
          continue;
        }

        const apptInfo = appointmentInfo[0];

        // Enviar recordatorio 24h
        console.log(`📧 Enviando recordatorio 24h para cita ${appointment.id}...`);
        
        const emailResult = await sendAppointmentReminder(
          appointment.id,
          {
            id: apptInfo.id,
            title: apptInfo.title,
            scheduledDate: new Date(apptInfo.scheduledDate),
            startTime: new Date(apptInfo.startTime),
            endTime: new Date(apptInfo.endTime),
            doctorFirstName: apptInfo.doctorFirstName,
            doctorLastName: apptInfo.doctorLastName,
            consultoryName: apptInfo.consultoryName,
          },
          appointment.patientId,
          '24h'
        );

        if (emailResult.success) {
          emailsSent++;
          console.log(`✅ Recordatorio 24h enviado exitosamente para cita ${appointment.id}`);
        } else {
          emailsError++;
          console.error(`❌ Error enviando recordatorio 24h para cita ${appointment.id}:`, emailResult.error);
        }

      } catch (error) {
        emailsError++;
        console.error(`❌ Error procesando cita ${appointment.id}:`, error);
      }

      // Pequeña pausa entre emails para no sobrecargar el servicio
      await new Promise(resolve => setTimeout(resolve, 1000));
    }

    const result = {
      message: 'Proceso de recordatorios 24h completado',
      totalAppointments: appointmentsNeedingReminder.length,
      emailsSent,
      emailsError,
      timestamp: new Date().toISOString(),
    };

    console.log('📊 Resumen del proceso:', result);

    return NextResponse.json(result, { status: 200 });

  } catch (error) {
    console.error('Error en proceso de recordatorios 24h:', error);
    return NextResponse.json(
      { 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      }, 
      { status: 500 }
    );
  }
}