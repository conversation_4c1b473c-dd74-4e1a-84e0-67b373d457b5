import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('🔍 Diagnóstico de estado para usuario:', userId);

    // 1. Obtener datos de Clerk
    const clerk = await clerkClient();
    const clerkUser = await clerk.users.getUser(userId);
    const clerkMetadata = clerkUser.publicMetadata;

    // 2. Obtener datos del usuario en BD
    const userData = await db.execute(sql`
      SELECT id, email, "overallStatus" as status, "createdAt", "updatedAt"
      FROM "user" WHERE id = ${userId} LIMIT 1
    `);

    // 3. Obtener roles del usuario
    const userRoles = await db.execute(sql`
      SELECT role, status, "rejectionReason", "rejectedAt", "createdAt", "updatedAt"
      FROM user_roles WHERE "userId" = ${userId}
      ORDER BY "createdAt" DESC
    `);

    // 4. Obtener solicitudes del usuario
    const userRequests = await db.execute(sql`
      SELECT role, status, "rejectionReason", "reviewedAt", "reviewedBy", "submittedAt"
      FROM "registrationRequests" WHERE "userId" = ${userId}
      ORDER BY "submittedAt" DESC
    `);

    // 5. Analizar estado
    const activeRoles = userRoles.rows.filter(r => r.status === 'active');
    const rejectedRoles = userRoles.rows.filter(r => r.status === 'rejected');
    const pendingRoles = userRoles.rows.filter(r => r.status === 'pending');
    
    const pendingRequests = userRequests.rows.filter(r => r.status === 'pending');
    const rejectedRequests = userRequests.rows.filter(r => r.status === 'rejected');
    const approvedRequests = userRequests.rows.filter(r => r.status === 'approved');

    // 6. Determinar estado del sistema
    let systemStatus = 'unknown';
    let shouldRedirectTo = null;
    let issues = [];

    if (rejectedRoles.length > 0 || rejectedRequests.length > 0) {
      systemStatus = 'rejected';
      shouldRedirectTo = '/onboarding/rejected';
      if (clerkMetadata?.status !== 'rejected') {
        issues.push('Metadatos de Clerk no reflejan estado de rechazo');
      }
    } else if (activeRoles.length > 0) {
      systemStatus = 'active';
      shouldRedirectTo = `/dashboard/${activeRoles[0].role}`;
      if (clerkMetadata?.status !== 'active') {
        issues.push('Metadatos de Clerk no reflejan estado activo');
      }
    } else if (pendingRoles.length > 0 || pendingRequests.length > 0) {
      systemStatus = 'pending';
      shouldRedirectTo = '/onboarding/pending';
      if (clerkMetadata?.status !== 'pending') {
        issues.push('Metadatos de Clerk no reflejan estado pendiente');
      }
    } else {
      systemStatus = 'no_onboarding';
      shouldRedirectTo = '/onboarding';
      if (clerkMetadata?.onboardingCompleted) {
        issues.push('Metadatos de Clerk indican onboarding completado pero no hay roles/solicitudes');
      }
    }

    const diagnosticData = {
      userId,
      systemStatus,
      shouldRedirectTo,
      issues,
      
      // Datos de Clerk
      clerk: {
        metadata: clerkMetadata,
        email: clerkUser.emailAddresses[0]?.emailAddress,
        firstName: clerkUser.firstName,
        lastName: clerkUser.lastName
      },
      
      // Datos de BD
      database: {
        user: userData.rows[0] || null,
        roles: {
          total: userRoles.rows.length,
          active: activeRoles.length,
          rejected: rejectedRoles.length,
          pending: pendingRoles.length,
          details: userRoles.rows
        },
        requests: {
          total: userRequests.rows.length,
          pending: pendingRequests.length,
          rejected: rejectedRequests.length,
          approved: approvedRequests.length,
          details: userRequests.rows
        }
      },
      
      // Análisis
      analysis: {
        hasRejectedData: rejectedRoles.length > 0 || rejectedRequests.length > 0,
        hasActiveData: activeRoles.length > 0,
        hasPendingData: pendingRoles.length > 0 || pendingRequests.length > 0,
        metadataConsistent: issues.length === 0,
        needsSync: issues.length > 0
      }
    };

    console.log('📊 Diagnóstico completado:', {
      systemStatus,
      shouldRedirectTo,
      issues: issues.length,
      rolesCount: userRoles.rows.length,
      requestsCount: userRequests.rows.length
    });

    return NextResponse.json({
      success: true,
      data: diagnosticData
    });

  } catch (error) {
    console.error('❌ Error en diagnóstico:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 