-- 0015_add_service_tags_system.sql
-- Sistema de etiquetas para servicios médicos

-- <PERSON>rear tabla service_tags
CREATE TABLE IF NOT EXISTS "service_tags" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"color" text DEFAULT 'blue',
	"category" text DEFAULT 'general',
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "service_tags_name_unique" UNIQUE("name")
);

-- Crear tabla medical_service_tags (relación muchos-a-muchos)
CREATE TABLE IF NOT EXISTS "medical_service_tags" (
	"serviceId" text NOT NULL,
	"tagId" text NOT NULL,
	"createdAt" timestamp DEFAULT now(),
	PRIMARY KEY("serviceId", "tagId"),
	CONSTRAINT "medical_service_tags_serviceId_medical_services_id_fk" FOREIGN KEY ("serviceId") REFERENCES "medical_services"("id") ON DELETE cascade,
	CONSTRAINT "medical_service_tags_tagId_service_tags_id_fk" FOREIGN KEY ("tagId") REFERENCES "service_tags"("id") ON DELETE cascade
);

-- Crear índices para service_tags
CREATE UNIQUE INDEX IF NOT EXISTS "service_tags_name_idx" ON "service_tags" ("name");
CREATE INDEX IF NOT EXISTS "service_tags_category_idx" ON "service_tags" ("category");
CREATE INDEX IF NOT EXISTS "service_tags_active_idx" ON "service_tags" ("isActive");

-- Crear índices para medical_service_tags
CREATE INDEX IF NOT EXISTS "medical_service_tags_service_idx" ON "medical_service_tags" ("serviceId");
CREATE INDEX IF NOT EXISTS "medical_service_tags_tag_idx" ON "medical_service_tags" ("tagId");