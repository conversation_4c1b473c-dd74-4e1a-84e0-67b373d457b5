import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { nonPathologicalHistory } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or, isNotNull } from 'drizzle-orm';

// GET - Listar antecedentes no patológicos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const subcategory = searchParams.get('subcategory');
    const isPositive = searchParams.get('positive');
    const importance = searchParams.get('importance');
    const ageRelevant = searchParams.get('age');
    const status = searchParams.get('status');
    const orderBy = searchParams.get('orderBy') || 'order';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(nonPathologicalHistory);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(nonPathologicalHistory.name, `%${search}%`),
          ilike(nonPathologicalHistory.description, `%${search}%`),
          ilike(nonPathologicalHistory.category, `%${search}%`),
          ilike(nonPathologicalHistory.subcategory, `%${search}%`)
        )
      );
    }

    if (category) {
      conditions.push(eq(nonPathologicalHistory.category, category));
    }

    if (subcategory) {
      conditions.push(eq(nonPathologicalHistory.subcategory, subcategory));
    }

    if (isPositive !== null) {
      conditions.push(eq(nonPathologicalHistory.isPositive, isPositive === 'true'));
    }

    if (importance) {
      conditions.push(eq(nonPathologicalHistory.importance, importance));
    }

    if (ageRelevant) {
      conditions.push(eq(nonPathologicalHistory.ageRelevant, ageRelevant));
    }

    // Filtro por status (activo por defecto)
    if (status === 'active' || !status) {
      conditions.push(eq(nonPathologicalHistory.isActive, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'name' ? nonPathologicalHistory.name :
                       orderBy === 'category' ? nonPathologicalHistory.category :
                       orderBy === 'importance' ? nonPathologicalHistory.importance :
                       orderBy === 'createdAt' ? nonPathologicalHistory.createdAt :
                       nonPathologicalHistory.order;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const nonPathologicalHistoryData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(nonPathologicalHistory);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Obtener categorías únicas (filtrar valores nulos)
    const categories = await db.select({
      category: nonPathologicalHistory.category
    }).from(nonPathologicalHistory)
    .where(isNotNull(nonPathologicalHistory.category))
    .groupBy(nonPathologicalHistory.category);

    // Obtener subcategorías únicas (filtrar valores nulos)
    const subcategories = await db.select({
      subcategory: nonPathologicalHistory.subcategory
    }).from(nonPathologicalHistory)
    .where(isNotNull(nonPathologicalHistory.subcategory))
    .groupBy(nonPathologicalHistory.subcategory);

    return NextResponse.json({
      data: nonPathologicalHistoryData,
      categories: categories.map(c => c.category),
      subcategories: subcategories.map(s => s.subcategory),
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        positive: nonPathologicalHistoryData.filter(item => item.isPositive).length,
        negative: nonPathologicalHistoryData.filter(item => !item.isPositive).length,
        byImportance: {
          high: nonPathologicalHistoryData.filter(item => item.importance === 'high').length,
          medium: nonPathologicalHistoryData.filter(item => item.importance === 'medium').length,
          low: nonPathologicalHistoryData.filter(item => item.importance === 'low').length
        }
      }
    });

  } catch (error) {
    console.error('Error obteniendo antecedentes no patológicos:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo antecedente no patológico (solo admin y médicos)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Solo administradores y médicos pueden crear antecedentes no patológicos' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      category, 
      subcategory, 
      isPositive, 
      importance, 
      ageRelevant, 
      description, 
      benefits, 
      risks, 
      duration, 
      order 
    } = body;

    // Validaciones
    if (!name || !category || !importance) {
      return NextResponse.json(
        { error: 'Nombre, categoría e importancia son requeridos' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un antecedente con el mismo ID
    const existingHistory = await db.select().from(nonPathologicalHistory).where(eq(nonPathologicalHistory.id, id)).limit(1);
    if (existingHistory.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un antecedente no patológico con este nombre' },
        { status: 400 }
      );
    }

    const newNonPathologicalHistory = {
      id,
      name: name.trim(),
      category: category.trim(),
      subcategory: subcategory?.trim() || null,
      isPositive: Boolean(isPositive),
      importance,
      ageRelevant: ageRelevant || 'all',
      description: description?.trim() || '',
      benefits: benefits || [],
      risks: risks || [],
      duration: duration?.trim() || null,
      order: order ? parseInt(order) : 999
    };

    // Insertar en base de datos
    const [insertedNonPathologicalHistory] = await db.insert(nonPathologicalHistory).values(newNonPathologicalHistory).returning();

    return NextResponse.json({
      message: 'Antecedente no patológico creado exitosamente',
      data: insertedNonPathologicalHistory
    });

  } catch (error) {
    console.error('Error creando antecedente no patológico:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar antecedente no patológico (solo admin y médicos)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json(
        { error: 'Solo administradores y médicos pueden actualizar antecedentes no patológicos' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, category, subcategory, isPositive, importance, ageRelevant, description, benefits, risks, duration, order } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de antecedente no patológico requerido' },
        { status: 400 }
      );
    }

    // Buscar antecedente existente
    const existingHistory = await db.select().from(nonPathologicalHistory).where(eq(nonPathologicalHistory.id, id)).limit(1);
    if (existingHistory.length === 0) {
      return NextResponse.json(
        { error: 'Antecedente no patológico no encontrado' },
        { status: 404 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(category && { category: category.trim() }),
      ...(subcategory !== undefined && { subcategory: subcategory?.trim() || null }),
      ...(isPositive !== undefined && { isPositive: Boolean(isPositive) }),
      ...(importance && { importance }),
      ...(ageRelevant && { ageRelevant }),
      ...(description !== undefined && { description: description.trim() }),
      ...(benefits && { benefits }),
      ...(risks && { risks }),
      ...(duration !== undefined && { duration: duration?.trim() || null }),
      ...(order !== undefined && { order: parseInt(order) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedNonPathologicalHistory] = await db.update(nonPathologicalHistory)
      .set(updateData)
      .where(eq(nonPathologicalHistory.id, id))
      .returning();

    return NextResponse.json({
      message: 'Antecedente no patológico actualizado exitosamente',
      data: updatedNonPathologicalHistory
    });

  } catch (error) {
    console.error('Error actualizando antecedente no patológico:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}