import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { 
  associationCodes, 
  user, 
  userRoles,
  guardianPatientRelations,
  notifications
} from '@/db/schema';
import { auth } from '@clerk/nextjs/server';
import { eq, and, gt, isNull } from 'drizzle-orm';
import { randomBytes } from 'crypto';

// Función para generar ID único
function generateId(): string {
  return randomBytes(16).toString('hex');
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    const { code, relationship }: { 
      code: string;
      relationship: string;
    } = await request.json();

    if (!code || !relationship) {
      return NextResponse.json(
        { success: false, message: 'Código y tipo de relación son requeridos' },
        { status: 400 }
      );
    }

    if (code.length !== 6) {
      return NextResponse.json(
        { success: false, message: 'Código inválido' },
        { status: 400 }
      );
    }

    // Verificar que el usuario es un guardian
    const guardianRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'guardian'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (guardianRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solo los guardians pueden usar códigos de asociación' },
        { status: 403 }
      );
    }

    // Buscar el código de asociación válido
    const associationCodeResult = await db
      .select({
        id: associationCodes.id,
        patientId: associationCodes.patientId,
        expiresAt: associationCodes.expiresAt,
        usedBy: associationCodes.usedBy,
        usedAt: associationCodes.usedAt,
      })
      .from(associationCodes)
      .where(
        and(
          eq(associationCodes.code, code),
          gt(associationCodes.expiresAt, new Date()) // No expirado
          // No verificamos usedBy porque un código puede ser usado por múltiples guardians
        )
      )
      .limit(1);

    if (associationCodeResult.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Código inválido o expirado' },
        { status: 404 }
      );
    }

    const associationCodeData = associationCodeResult[0];
    const patientId = associationCodeData.patientId;

    // Verificar que el guardian no esté ya asociado con este paciente
    const existingRelation = await db
      .select()
      .from(guardianPatientRelations)
      .where(
        and(
          eq(guardianPatientRelations.guardianId, userId),
          eq(guardianPatientRelations.patientId, patientId)
        )
      )
      .limit(1);

    if (existingRelation.length > 0) {
      return NextResponse.json(
        { success: false, message: 'Ya estás asociado con este paciente' },
        { status: 400 }
      );
    }

    // Obtener información del paciente
    const patientResult = await db
      .select({
        id: user.id,
        name: user.name,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        dateOfBirth: user.dateOfBirth
      })
      .from(user)
      .where(eq(user.id, patientId))
      .limit(1);

    if (patientResult.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    const patientData = patientResult[0];

    // Verificar edad del paciente (opcional - algunos pacientes adultos pueden necesitar guardians)
    let patientAge = null;
    if (patientData.dateOfBirth) {
      const birthDate = new Date(patientData.dateOfBirth);
      const today = new Date();
      patientAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        patientAge--;
      }
    }

    // Determinar si es guardian primario (si es el primero en asociarse)
    const existingGuardians = await db
      .select()
      .from(guardianPatientRelations)
      .where(eq(guardianPatientRelations.patientId, patientId));

    const isPrimary = existingGuardians.length === 0;

    // Crear la relación guardian-paciente
    await db.transaction(async (tx) => {
      // 1. Crear la relación
      await tx.insert(guardianPatientRelations).values({
        id: generateId(),
        guardianId: userId,
        patientId: patientId,
        relationship: relationship,
        isPrimary: isPrimary,
        canMakeDecisions: true, // Por defecto pueden tomar decisiones
        validUntil: null, // Sin fecha de expiración por defecto
        createdAt: new Date()
      });

      // 2. Marcar el código como usado por este guardian
      if (!associationCodeData.usedBy) {
        await tx
          .update(associationCodes)
          .set({
            usedBy: userId,
            usedAt: new Date()
          })
          .where(eq(associationCodes.id, associationCodeData.id));
      }

      // 3. Crear notificación para el paciente
      await tx.insert(notifications).values({
        id: generateId(),
        userId: patientId,
        type: 'guardian_association',
        title: 'Nuevo Guardian Asociado',
        message: `Un guardian se ha asociado contigo usando el código de asociación.`,
        read: false,
        data: { 
          guardianId: userId,
          relationship: relationship,
          isPrimary: isPrimary,
          codeId: associationCodeData.id
        },
        createdAt: new Date()
      });

      // 4. Crear notificación para el guardian
      await tx.insert(notifications).values({
        id: generateId(),
        userId: userId,
        type: 'association_success',
        title: 'Asociación Exitosa',
        message: `Te has asociado exitosamente como ${relationship} de ${patientData.firstName} ${patientData.lastName}.`,
        read: false,
        data: { 
          patientId: patientId,
          relationship: relationship,
          isPrimary: isPrimary,
          codeId: associationCodeData.id
        },
        createdAt: new Date()
      });
    });

    return NextResponse.json({
      success: true,
      message: 'Asociación exitosa',
      data: {
        patient: {
          id: patientData.id,
          name: `${patientData.firstName} ${patientData.lastName}`,
          email: patientData.email,
          age: patientAge
        },
        relationship: relationship,
        isPrimaryGuardian: isPrimary,
        canMakeDecisions: true,
        associatedAt: new Date()
      }
    });

  } catch (error) {
    console.error('Error usando código de asociación:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
} 