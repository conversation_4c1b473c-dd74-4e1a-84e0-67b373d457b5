// Cargar variables de entorno
import dotenv from 'dotenv';
import path from 'path';

// Cargar .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

import { sendPatientEmail } from '../lib/email-platform';

async function testNewEmailSystem() {
  console.log('🧪 Probando el nuevo sistema de emails...\n');
  
  // Debug de variables de entorno
  console.log('🔍 Verificando variables de entorno:');
  console.log('RESEND_API_KEY:', process.env.RESEND_API_KEY ? '✅ Presente' : '❌ Faltante');
  console.log('RESEND_FROM_EMAIL:', process.env.RESEND_FROM_EMAIL || '❌ Faltante');
  console.log('');
  
  if (!process.env.RESEND_API_KEY) {
    console.error('❌ Error: RESEND_API_KEY no está configurada');
    return;
  }
  
  try {
    console.log('📧 Enviando email de prueba...');
    
    // Test email de confirmación de cita
    const result = await sendPatientEmail(
      'appointment_created',
      '<EMAIL>', // Cambiar por tu email
      {
        patientName: 'Paciente de Prueba',
        doctorName: 'Dr. García',
        appointmentDate: 'Viernes, 02 de agosto de 2025',
        appointmentTime: '14:00',
        confirmationCode: 'ABC123',
        appointmentType: 'Consulta General',
        consultoryName: 'Consultorio Principal',
        preCheckinLink: 'http://localhost:3000/pre-checkin/test'
      },
      {
        patientId: 'test-patient',
        appointmentId: 'test-appointment',
        flowType: 'test'
      }
    );
    
    console.log('\n📊 Resultado:', JSON.stringify(result, null, 2));
    
    if (result.success) {
      console.log('✅ Email enviado exitosamente!');
      console.log('📬 Revisa tu bandeja de entrada');
    } else {
      console.error('❌ Error al enviar email:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
  }
}

// Ejecutar prueba
testNewEmailSystem();