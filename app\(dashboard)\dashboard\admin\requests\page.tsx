'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Eye, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Search,
  Filter,
  RefreshCw,
  User,
  Users,
  FileText,
  Calendar,
  ArrowUpDown,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface RegistrationRequest {
  id: string;
  userId: string;
  role: string;
  status: string;
  submittedAt: string;
  reviewedAt?: string;
  user: {
    email: string;
    firstName: string;
    lastName: string;
    documentType: string;
    documentNumber: string;
    phone: string;
  };
}

interface RequestsData {
  requests: RegistrationRequest[];
  pagination: {
    page: number;
    limit: number;
    totalPages: number;
    totalCount: number;
  };
  statistics: {
    total: number;
    pending: number;
    approved: number;
    rejected: number;
    reviewing: number;
  };
}

const roleLabels: { [key: string]: string } = {
  doctor: 'Doctor',
  assistant: 'Asistente',
  patient: 'Paciente',
  guardian: 'Guardian',
  provider: 'Proveedor'
};

const statusLabels: { [key: string]: string } = {
  pending: 'Pendiente',
  approved: 'Aprobado',
  rejected: 'Rechazado',
  reviewing: 'En revisión'
};

const statusColors: { [key: string]: string } = {
  pending: 'bg-yellow-100 text-yellow-800',
  approved: 'bg-green-100 text-green-800',
  rejected: 'bg-red-100 text-red-800',
  reviewing: 'bg-blue-100 text-blue-800'
};

export default function AdminRequestsPage() {
  const router = useRouter();
  const [data, setData] = useState<RequestsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState('');
  
  // Filtros
  const [statusFilter, setStatusFilter] = useState('all');
  const [roleFilter, setRoleFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  
  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('submittedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  const fetchRequests = async () => {
    try {
      setError('');
      const params = new URLSearchParams({
        status: statusFilter,
        role: roleFilter,
        page: page.toString(),
        limit: limit.toString()
      });

      const response = await fetch(`/api/admin/requests?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar las solicitudes');
      }

      const result = await response.json();
      if (result.success) {
        setData(result.data);
      } else {
        throw new Error(result.message || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar solicitudes');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  useEffect(() => {
    fetchRequests();
  }, [statusFilter, roleFilter, page, limit]);

  const handleRefresh = () => {
    setRefreshing(true);
    fetchRequests();
  };

  const handleViewDetails = (requestId: string) => {
    router.push(`/dashboard/admin/requests/${requestId}`);
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  const filteredRequests = data?.requests.filter(request => {
    if (!searchTerm) return true;
    const search = searchTerm.toLowerCase();
    return (
      request.user.firstName.toLowerCase().includes(search) ||
      request.user.lastName.toLowerCase().includes(search) ||
      request.user.email.toLowerCase().includes(search) ||
      request.user.documentNumber.includes(search)
    );
  }).sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy) {
      case 'firstName':
        aValue = a.user.firstName.toLowerCase();
        bValue = b.user.firstName.toLowerCase();
        break;
      case 'role':
        aValue = a.role.toLowerCase();
        bValue = b.role.toLowerCase();
        break;
      case 'status':
        aValue = a.status.toLowerCase();
        bValue = b.status.toLowerCase();
        break;
      case 'submittedAt':
        aValue = new Date(a.submittedAt).getTime();
        bValue = new Date(b.submittedAt).getTime();
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  }) || [];

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Solicitudes de Registro</h1>
          <p className="text-gray-600">
            Gestiona las solicitudes de nuevos usuarios
          </p>
        </div>
        <Button 
          onClick={handleRefresh} 
          variant="outline"
          disabled={refreshing}
          className="hover:bg-gray-50 transition-colors"
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Actualizar
        </Button>
      </div>

      {/* Estadísticas */}
      {data && (
        <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-5">
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Total
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-gray-100 flex items-center justify-center">
                <FileText className="h-5 w-5 md:h-6 md:w-6 text-gray-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.statistics.total}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Solicitudes totales</span>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Pendientes
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-yellow-100 flex items-center justify-center">
                <Clock className="h-5 w-5 md:h-6 md:w-6 text-yellow-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.statistics.pending}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Por revisar</span>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                En Revisión
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <Eye className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.statistics.reviewing}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">En proceso</span>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Aprobadas
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
                <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.statistics.approved}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Aceptadas</span>
              </div>
            </CardContent>
          </Card>
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
              <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
                Rechazadas
              </CardTitle>
              <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-red-100 flex items-center justify-center">
                <XCircle className="h-5 w-5 md:h-6 md:w-6 text-red-600" />
              </div>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">{data.statistics.rejected}</div>
              <div className="flex items-center text-xs md:text-sm">
                <span className="text-gray-500">Denegadas</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Filtros */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Filter className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, email o documento..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px] border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                <SelectValue placeholder="Estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="pending">Pendiente</SelectItem>
                <SelectItem value="reviewing">En revisión</SelectItem>
                <SelectItem value="approved">Aprobado</SelectItem>
                <SelectItem value="rejected">Rechazado</SelectItem>
              </SelectContent>
            </Select>
            <Select value={roleFilter} onValueChange={setRoleFilter}>
              <SelectTrigger className="w-[180px] border-gray-200 focus:border-blue-500 focus:ring-blue-500">
                <SelectValue placeholder="Rol" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los roles</SelectItem>
                <SelectItem value="doctor">Doctor</SelectItem>
                <SelectItem value="assistant">Asistente</SelectItem>
                <SelectItem value="patient">Paciente</SelectItem>
                <SelectItem value="guardian">Guardian</SelectItem>
                <SelectItem value="provider">Proveedor</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Tabla de solicitudes */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-600" />
                Listado de Solicitudes
              </CardTitle>
              {data && (
                <Badge variant="outline" className="border-gray-300">
                  {filteredRequests.length} solicitudes
                </Badge>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-0">
          {error ? (
            <div className="p-6 text-center text-red-600">
              {error}
            </div>
          ) : filteredRequests.length === 0 ? (
            <div className="p-6 text-center text-muted-foreground">
              No se encontraron solicitudes
            </div>
          ) : (
            <>
              {/* Desktop Table View */}
              <div className="hidden lg:block overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="border-gray-200">
                      <TableHead 
                        className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort('firstName')}
                      >
                        <div className="flex items-center gap-2">
                          Usuario
                          <SortIcon column="firstName" />
                        </div>
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700">Documento</TableHead>
                      <TableHead 
                        className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort('role')}
                      >
                        <div className="flex items-center gap-2">
                          Rol
                          <SortIcon column="role" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort('status')}
                      >
                        <div className="flex items-center gap-2">
                          Estado
                          <SortIcon column="status" />
                        </div>
                      </TableHead>
                      <TableHead 
                        className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                        onClick={() => handleSort('submittedAt')}
                      >
                        <div className="flex items-center gap-2">
                          Fecha Solicitud
                          <SortIcon column="submittedAt" />
                        </div>
                      </TableHead>
                      <TableHead className="font-semibold text-gray-700">Acciones</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredRequests.map((request) => (
                      <TableRow key={request.id} className="hover:bg-gray-50 transition-colors border-gray-200">
                        <TableCell>
                          <div>
                            <div className="font-medium text-gray-900">
                              {request.user.firstName} {request.user.lastName}
                            </div>
                            <div className="text-sm text-gray-500">
                              {request.user.email}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="text-gray-600">{request.user.documentType?.toUpperCase()}</div>
                            <div className="font-mono text-gray-900">{request.user.documentNumber}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline" className="border-gray-300">
                            {roleLabels[request.role] || request.role}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={statusColors[request.status]}>
                            {statusLabels[request.status] || request.status}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="text-sm">
                            <div className="text-gray-900">
                              {format(new Date(request.submittedAt), 'dd MMM yyyy', { locale: es })}
                            </div>
                            <div className="text-xs text-gray-500">
                              {format(new Date(request.submittedAt), 'HH:mm')}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleViewDetails(request.id)}
                            className="hover:bg-blue-50 hover:text-blue-600 transition-colors"
                          >
                            <Eye className="h-4 w-4 mr-2" />
                            Ver detalles
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              {/* Mobile Cards View */}
              <div className="lg:hidden space-y-4">
                {filteredRequests.map((request) => (
                  <Card key={request.id} className="hover:shadow-md transition-all duration-200">
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1 min-w-0">
                          <p className="font-semibold text-gray-900 truncate">
                            {request.user.firstName} {request.user.lastName}
                          </p>
                          <p className="text-sm text-gray-500 truncate">
                            {request.user.email}
                          </p>
                        </div>
                        
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleViewDetails(request.id)}
                          className="hover:bg-blue-50 hover:text-blue-600 transition-colors ml-2"
                        >
                          <Eye className="h-4 w-4 mr-1" />
                          Ver
                        </Button>
                      </div>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Documento:</span>
                          <div className="text-right">
                            <div className="text-xs text-gray-600">{request.user.documentType?.toUpperCase()}</div>
                            <div className="text-xs font-mono text-gray-900">{request.user.documentNumber}</div>
                          </div>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Rol:</span>
                          <Badge variant="outline" className="text-xs border-gray-300">
                            {roleLabels[request.role] || request.role}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between">
                          <span className="text-xs text-gray-500">Estado:</span>
                          <Badge className={statusColors[request.status]}>
                            {statusLabels[request.status] || request.status}
                          </Badge>
                        </div>

                        <div className="flex items-center justify-between pt-1 border-t border-gray-100">
                          <span className="text-xs text-gray-500">Solicitud:</span>
                          <div className="text-right">
                            <div className="text-xs text-gray-900">
                              {format(new Date(request.submittedAt), 'dd MMM yyyy', { locale: es })}
                            </div>
                            <div className="text-xs text-gray-500">
                              {format(new Date(request.submittedAt), 'HH:mm')}
                            </div>
                          </div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </>
          )}
        </CardContent>
      </Card>

      {/* Paginación */}
      {data && data.pagination.totalPages > 1 && (
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="text-sm text-muted-foreground">
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, data.pagination.totalCount)} de {data.pagination.totalCount} solicitudes
            </div>
            <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            <div className="flex items-center gap-2">
              {Array.from({ length: data.pagination.totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === data.pagination.totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === data.pagination.totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}