import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';

// Validar permisos de admin
const validateAdminPermissions = async () => {
  const { userId, sessionClaims } = await auth();
  
  if (!userId) {
    return { error: 'No autorizado', status: 401 };
  }
  
  const role = sessionClaims?.metadata?.role;
  
  if (role !== 'admin') {
    return { error: 'Permisos insuficientes. Solo administradores pueden acceder.', status: 403 };
  }
  
  return null;
};

// POST - Analizar impacto de eliminación de usuario
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const authError = await validateAdminPermissions();
    if (authError) {
      return NextResponse.json({ error: authError.error }, { status: authError.status });
    }

    const resolvedParams = await params;
    const userId = resolvedParams.id;

    // Verificar que el usuario existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.id, userId))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const currentUser = existingUser[0];

    // Analizar el impacto de la eliminación
    const analysisResult = await analyzeUserDeletionImpact(userId);

    return NextResponse.json({
      success: true,
      user: {
        id: currentUser.id,
        name: `${currentUser.firstName} ${currentUser.lastName}`,
        email: currentUser.email,
        status: currentUser.overallStatus
      },
      analysis: analysisResult,
      summary: generateDeletionSummary(analysisResult)
    });

  } catch (error) {
    console.error('Error analyzing user deletion:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función para analizar el impacto de eliminación de un usuario
async function analyzeUserDeletionImpact(userId: string) {
  // Importar todos los schemas necesarios
  const { 
    notifications, 
    associationCodes, 
    guardianPatientRelations, 
    assistantDoctorRelations, 
    registrationRequests 
  } = await import('@/db/schema');

  const [
    userNotifications,
    userAssociationCodes,
    userGuardianRelations, 
    userAssistantRelations,
    userRegistrationRequests,
    userRolesData,
    roleReferences
  ] = await Promise.all([
    // Notificaciones del usuario
    db.select().from(notifications).where(eq(notifications.userId, userId)),
    
    // Códigos de asociación donde el usuario aparece
    db.select().from(associationCodes).where(
      sql`${associationCodes.patientId} = ${userId} OR ${associationCodes.usedBy} = ${userId}`
    ),
    
    // Relaciones guardian-paciente donde el usuario aparece
    db.select().from(guardianPatientRelations).where(
      sql`${guardianPatientRelations.guardianId} = ${userId} OR ${guardianPatientRelations.patientId} = ${userId}`
    ),
    
    // Relaciones asistente-doctor donde el usuario aparece
    db.select().from(assistantDoctorRelations).where(
      sql`${assistantDoctorRelations.assistantId} = ${userId} OR ${assistantDoctorRelations.doctorId} = ${userId}`
    ),
    
    // Solicitudes de registro del usuario
    db.select().from(registrationRequests).where(eq(registrationRequests.userId, userId)),
    
    // Roles del usuario
    db.select().from(userRoles).where(eq(userRoles.userId, userId)),
    
    // Referencias a este usuario en otros roles
    db.select().from(userRoles).where(
      sql`${userRoles.preferredDoctorId} = ${userId} OR ${userRoles.approvedBy} = ${userId} OR ${userRoles.rejectedBy} = ${userId}`
    )
  ]);

  return {
    notifications: userNotifications,
    associationCodes: userAssociationCodes,
    guardianPatientRelations: userGuardianRelations,
    assistantDoctorRelations: userAssistantRelations,
    registrationRequests: userRegistrationRequests,
    userRoles: userRolesData,
    roleReferences: roleReferences,
    totalRecords: userNotifications.length + userAssociationCodes.length + 
                 userGuardianRelations.length + userAssistantRelations.length + 
                 userRegistrationRequests.length + userRolesData.length + roleReferences.length
  };
}

// Función para generar resumen de eliminación
function generateDeletionSummary(analysis: any) {
  const items = [];
  
  if (analysis.notifications.length > 0) {
    items.push(`${analysis.notifications.length} notificación${analysis.notifications.length > 1 ? 'es' : ''}`);
  }
  
  if (analysis.associationCodes.length > 0) {
    items.push(`${analysis.associationCodes.length} código${analysis.associationCodes.length > 1 ? 's' : ''} de asociación`);
  }
  
  if (analysis.guardianPatientRelations.length > 0) {
    items.push(`${analysis.guardianPatientRelations.length} relación${analysis.guardianPatientRelations.length > 1 ? 'es' : ''} guardian-paciente`);
  }
  
  if (analysis.assistantDoctorRelations.length > 0) {
    items.push(`${analysis.assistantDoctorRelations.length} relación${analysis.assistantDoctorRelations.length > 1 ? 'es' : ''} asistente-doctor`);
  }
  
  if (analysis.registrationRequests.length > 0) {
    items.push(`${analysis.registrationRequests.length} solicitud${analysis.registrationRequests.length > 1 ? 'es' : ''} de registro`);
  }
  
  if (analysis.userRoles.length > 0) {
    items.push(`${analysis.userRoles.length} rol${analysis.userRoles.length > 1 ? 'es' : ''} de usuario`);
  }
  
  if (analysis.roleReferences.length > 0) {
    items.push(`${analysis.roleReferences.length} referencia${analysis.roleReferences.length > 1 ? 's' : ''} en otros roles`);
  }

  return {
    totalItems: analysis.totalRecords,
    itemsList: items,
    hasImpact: analysis.totalRecords > 0,
    warning: analysis.totalRecords > 0 ? 
      'Esta eliminación afectará múltiples registros en el sistema' : 
      'Esta eliminación no afectará otros registros'
  };
}