import { db } from '../db/drizzle';
import { 
  countries, 
  departments, 
  municipalities, 
  occupations, 
  relationships, 
  medicalSpecialties,
  currencies,
  activityTypes,
  mediaSources,
  educationLevels,
  documentTypes,
  maritalStatus
} from '../db/schema';

async function checkCatalogData() {
  console.log('🔍 Checking catalog data in database...\n');

  try {
    // Check basic catalogs
    console.log('=== BASIC CATALOGS ===');
    
    const countriesCount = await db.select().from(countries);
    console.log(`🌍 Countries: ${countriesCount.length} records`);
    if (countriesCount.length > 0) {
      console.log(`   - First country: ${countriesCount[0].name} (${countriesCount[0].code})`);
    }

    const departmentsCount = await db.select().from(departments);
    console.log(`🏛️ Departments: ${departmentsCount.length} records`);

    const municipalitiesCount = await db.select().from(municipalities);
    console.log(`🏘️ Municipalities: ${municipalitiesCount.length} records`);

    const occupationsCount = await db.select().from(occupations);
    console.log(`💼 Occupations: ${occupationsCount.length} records`);

    const relationshipsCount = await db.select().from(relationships);
    console.log(`👨‍👩‍👧‍👦 Relationships: ${relationshipsCount.length} records`);

    const specialtiesCount = await db.select().from(medicalSpecialties);
    console.log(`🩺 Medical Specialties: ${specialtiesCount.length} records`);

    console.log('\n=== NEW CATALOGS ===');

    const currenciesCount = await db.select().from(currencies);
    console.log(`💰 Currencies: ${currenciesCount.length} records`);
    if (currenciesCount.length > 0) {
      console.log(`   - First currency: ${currenciesCount[0].name} (${currenciesCount[0].code})`);
    }

    const activityTypesCount = await db.select().from(activityTypes);
    console.log(`📅 Activity Types: ${activityTypesCount.length} records`);

    const mediaSourcesCount = await db.select().from(mediaSources);
    console.log(`📢 Media Sources: ${mediaSourcesCount.length} records`);

    const educationLevelsCount = await db.select().from(educationLevels);
    console.log(`🎓 Education Levels: ${educationLevelsCount.length} records`);

    const documentTypesCount = await db.select().from(documentTypes);
    console.log(`🏷️ Document Types: ${documentTypesCount.length} records`);

    const maritalStatusCount = await db.select().from(maritalStatus);
    console.log(`💍 Marital Status: ${maritalStatusCount.length} records`);

    console.log('\n=== SUMMARY ===');
    const totalRecords = countriesCount.length + departmentsCount.length + municipalitiesCount.length + 
                        occupationsCount.length + relationshipsCount.length + specialtiesCount.length +
                        currenciesCount.length + activityTypesCount.length + mediaSourcesCount.length +
                        educationLevelsCount.length + documentTypesCount.length + maritalStatusCount.length;
    
    console.log(`📊 Total catalog records: ${totalRecords}`);
    
    if (totalRecords === 0) {
      console.log('⚠️  NO DATA FOUND! Database appears to be empty.');
      console.log('💡 Run: npm run seed to populate the database');
    } else if (countriesCount.length === 0) {
      console.log('⚠️  Basic catalogs (countries, departments, etc.) are missing!');
      console.log('💡 Run: npx tsx db/seed.ts to populate basic catalogs');
    } else {
      console.log('✅ Database has catalog data');
    }

  } catch (error) {
    console.error('❌ Error checking database:', error);
  }

  process.exit(0);
}

checkCatalogData();