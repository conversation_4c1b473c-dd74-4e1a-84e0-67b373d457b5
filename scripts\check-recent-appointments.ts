import { db } from '../db/drizzle';
import { appointments, user } from '../db/schema';
import { desc, eq } from 'drizzle-orm';

async function checkRecentAppointments() {
  console.log('🔍 Buscando citas recientes...\n');
  
  try {
    // Buscar las últimas 5 citas
    const recentAppointments = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        consultoryId: appointments.consultoryId,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        status: appointments.status,
        createdAt: appointments.createdAt,
      })
      .from(appointments)
      .orderBy(desc(appointments.createdAt))
      .limit(5);

    console.log(`📊 Últimas ${recentAppointments.length} citas:\n`);

    for (const appointment of recentAppointments) {
      console.log('─'.repeat(60));
      console.log(`ID: ${appointment.id}`);
      console.log(`Título: ${appointment.title}`);
      console.log(`Fecha programada: ${appointment.scheduledDate}`);
      console.log(`Fecha programada (Date): ${new Date(appointment.scheduledDate)}`);
      console.log(`Fecha como string: ${appointment.scheduledDate.toDateString ? appointment.scheduledDate.toDateString() : 'N/A'}`);
      console.log(`Hora: ${appointment.startTime} - ${appointment.endTime}`);
      console.log(`Estado: ${appointment.status}`);
      console.log(`Doctor ID: ${appointment.doctorId}`);
      console.log(`Consultorio ID: ${appointment.consultoryId}`);
      console.log(`Creada: ${appointment.createdAt}`);
      
      // Simular el filtro de la vista de día
      const appointmentDate = new Date(appointment.scheduledDate);
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      console.log(`Fecha de cita normalizada: ${appointmentDate}`);
      console.log(`Hoy normalizado: ${today}`);
      console.log(`¿Coinciden las fechas?: ${appointmentDate.toDateString() === today.toDateString()}`);
    }

    // También verificar citas para hoy
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const todayAppointments = await db
      .select()
      .from(appointments)
      .where(
        eq(appointments.scheduledDate, today)
      );

    console.log(`\n\n📅 Citas para hoy (${today.toISOString().split('T')[0]}): ${todayAppointments.length}`);

    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

checkRecentAppointments();