import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, medicalSpecialties, consultories } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener parámetros de consulta opcionales
    const { searchParams } = new URL(request.url);
    const consultoryId = searchParams.get('consultoryId');

    // Query para obtener doctores activos con sus datos completos
    let whereConditions = [
      eq(userRoles.role, 'doctor'),
      eq(userRoles.status, 'active'),
      eq(user.overallStatus, 'active')
    ];

    if (consultoryId) {
      whereConditions.push(eq(userRoles.consultoryId, consultoryId));
    }

    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        medicalLicense: userRoles.medicalLicense,
        consultoryId: userRoles.consultoryId,
        specialtyId: userRoles.specialtyId,
        specialtyName: medicalSpecialties.name,
        consultoryName: consultories.name,
        roleStatus: userRoles.status,
        createdAt: userRoles.createdAt
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .leftJoin(medicalSpecialties, and(
        eq(userRoles.specialtyId, medicalSpecialties.id),
        eq(medicalSpecialties.isActive, true)
      ))
      .leftJoin(consultories, eq(userRoles.consultoryId, consultories.id))
      .where(and(...whereConditions));

    // Formatear respuesta
    const formattedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      name: `${doctor.firstName} ${doctor.lastName}`,
      firstName: doctor.firstName,
      lastName: doctor.lastName,
      email: doctor.email,
      phone: doctor.phone,
      specialty: doctor.specialtyName || 'No especificada',
      specialtyId: doctor.specialtyId,
      consultoryId: doctor.consultoryId,
      consultoryName: doctor.consultoryName || 'No asignado',
      medicalLicense: doctor.medicalLicense,
      status: doctor.roleStatus,
      createdAt: doctor.createdAt
    }));

    return NextResponse.json({
      success: true,
      data: formattedDoctors,
      count: formattedDoctors.length
    });

  } catch (error) {
    console.error('Error obteniendo doctores:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}