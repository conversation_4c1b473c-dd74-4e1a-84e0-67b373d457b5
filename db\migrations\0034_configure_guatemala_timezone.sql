-- Migración para configurar timezone de Guatemala
-- Archivo: 0034_configure_guatemala_timezone.sql

-- 1. Configurar timezone de la base de datos
ALTER DATABASE SET timezone = 'America/Guatemala';

-- 2. Convertir columnas de timestamp a timestamptz en appointments
ALTER TABLE appointments 
  ALTER COLUMN "scheduledDate" TYPE timestamptz USING "scheduledDate" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "startTime" TYPE timestamptz USING "startTime" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "endTime" TYPE timestamptz USING "endTime" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "createdAt" TYPE timestamptz USING "createdAt" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "updatedAt" TYPE timestamptz USING "updatedAt" AT TIME ZONE 'America/Guatemala';

-- 3. Actualizar otras tablas importantes con timestamptz
ALTER TABLE user 
  ALTER COLUMN "dateOfBirth" TYPE timestamptz USING "dateOfBirth" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "createdAt" TYPE timestamptz USING "createdAt" AT TIME ZONE 'America/Guatemala',
  ALTER COLUMN "updatedAt" TYPE timestamptz USING "updatedAt" AT TIME ZONE 'America/Guatemala';

-- 4. Verificar configuración
SELECT 
  current_setting('timezone') as current_timezone,
  NOW() as current_time_guatemala,
  NOW() AT TIME ZONE 'UTC' as current_time_utc;