"use client"

import { useTheme } from "next-themes"
import { Toaster as Son<PERSON>, ToasterProps } from "sonner"

const Toaster = ({ ...props }: ToasterProps) => {
  const { theme = "system" } = useTheme()

  return (
    <Sonner
      theme={theme as ToasterProps["theme"]}
      className="toaster group"
      toastOptions={{
        classNames: {
          toast:
            "group toast group-[.toaster]:bg-white group-[.toaster]:text-foreground group-[.toaster]:border group-[.toaster]:border-gray-200 group-[.toaster]:shadow-lg group-[.toaster]:min-w-[400px] group-[.toaster]:max-w-[500px] group-[.toaster]:rounded-lg group-[.toaster]:p-4",
          title: "group-[.toast]:text-base group-[.toast]:font-semibold group-[.toast]:mb-3 group-[.toast]:text-gray-900",
          description: "group-[.toast]:text-gray-700 group-[.toast]:text-sm group-[.toast]:leading-relaxed group-[.toast]:whitespace-pre-line group-[.toast]:mb-4",
          actionButton:
            "group-[.toast]:bg-red-600 group-[.toast]:text-white group-[.toast]:hover:bg-red-700 group-[.toast]:px-4 group-[.toast]:py-2 group-[.toast]:rounded-md group-[.toast]:font-medium group-[.toast]:text-sm group-[.toast]:transition-colors group-[.toast]:mr-2",
          cancelButton:
            "group-[.toast]:bg-gray-500 group-[.toast]:text-white group-[.toast]:hover:bg-gray-600 group-[.toast]:px-4 group-[.toast]:py-2 group-[.toast]:rounded-md group-[.toast]:font-medium group-[.toast]:text-sm group-[.toast]:transition-colors",
        },
      }}
      position="top-center"
      expand={true}
      richColors
      closeButton
      duration={6000}
      {...props}
    />
  )
}

export { Toaster }
