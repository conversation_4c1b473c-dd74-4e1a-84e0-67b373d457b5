# Módulo de Ventas - Sistema de Gestión Clínica

## 📋 Resumen Ejecutivo

### Objetivo
Implementar un módulo completo de ventas que se active al finalizar una consulta médica cuando hay servicios o medicamentos por cobrar. El sistema debe soportar múltiples métodos de pago, integración con facturación electrónica FEL (Guatemala) y estar preparado para futuro manejo de inventario.

### Alcance
- Resumen de venta post-consulta
- Procesamiento de múltiples métodos de pago
- Generación de facturas electrónicas (FEL)
- Integración con pasarela de pago Recurrente
- Soporte para múltiples emisores (NITs)
- Preparación para futuro sistema de inventario

## 🎯 Requisitos Funcionales

### RF-001: Activación del Módulo
- ✅ Solo se activa cuando hay servicios médicos o medicamentos por cobrar
- ✅ Si no hay cobros, redirige directamente a la agenda
- ✅ Mantiene el estado de la consulta como "completada"

### RF-002: Gestión de Precios
- ✅ Servicios médicos con precios definidos en catálogo
- ✅ Medicamentos con precios en catálogo (preparado para inventario)
- ✅ Soporte para múltiples monedas (GTQ, USD)
- ✅ Cálculo automático de totales e impuestos

### RF-003: Métodos de Pago
- ✅ Efectivo
- ✅ Tarjeta de crédito/débito (via Recurrente)
- ✅ Transferencia bancaria
- ✅ Pagos mixtos (múltiples métodos en una transacción)
- ✅ Registro de pagos parciales

### RF-004: Facturación Electrónica
- ✅ Integración con FEL Guatemala
- ✅ Múltiples emisores según servicio/medicamento
- ✅ Generación de múltiples facturas si hay diferentes NITs
- ✅ Almacenamiento de facturas generadas

### RF-005: Preparación para Inventario
- ✅ Estructura de datos lista para control de stock
- ✅ Campos para lotes y fechas de vencimiento
- ✅ Trazabilidad de medicamentos vendidos

## 🏗️ Arquitectura Técnica

### Modelo de Datos

```sql
-- Tabla de transacciones de venta
sales_transactions {
  id: UUID
  consultationId: UUID (FK)
  patientId: UUID (FK)
  doctorId: UUID (FK)
  consultoryId: UUID (FK)
  
  -- Totales
  subtotal: DECIMAL
  taxAmount: DECIMAL
  discountAmount: DECIMAL
  totalAmount: DECIMAL
  currency: VARCHAR (GTQ/USD)
  
  -- Estado
  status: ENUM (pending, partial, completed, cancelled)
  
  -- Auditoría
  createdAt: TIMESTAMP
  createdBy: UUID
  updatedAt: TIMESTAMP
  completedAt: TIMESTAMP
}

-- Detalle de items vendidos
sales_transaction_items {
  id: UUID
  transactionId: UUID (FK)
  
  -- Tipo y referencia
  itemType: ENUM (service, medication)
  itemId: UUID (referencia al servicio o medicamento)
  
  -- Detalles
  description: TEXT
  quantity: INTEGER
  unitPrice: DECIMAL
  discount: DECIMAL
  subtotal: DECIMAL
  taxAmount: DECIMAL
  total: DECIMAL
  
  -- Para facturación
  companyId: UUID (NIT emisor)
  
  -- Preparación inventario
  batchNumber: VARCHAR (futuro)
  expirationDate: DATE (futuro)
  stockLocationId: UUID (futuro)
}

-- Pagos realizados
sales_payments {
  id: UUID
  transactionId: UUID (FK)
  
  -- Método y monto
  paymentMethod: ENUM (cash, card, transfer, credit)
  amount: DECIMAL
  currency: VARCHAR
  
  -- Referencias externas
  referenceNumber: VARCHAR (número de transacción)
  authorizationCode: VARCHAR (código de autorización)
  
  -- Estado
  status: ENUM (pending, processing, completed, failed, refunded)
  
  -- Timestamps
  processedAt: TIMESTAMP
  createdAt: TIMESTAMP
}

-- Facturas generadas
sales_invoices {
  id: UUID
  transactionId: UUID (FK)
  
  -- Datos fiscales
  companyId: UUID (NIT emisor)
  invoiceNumber: VARCHAR
  felNumber: VARCHAR (número FEL)
  felSeries: VARCHAR
  
  -- Montos
  subtotal: DECIMAL
  taxAmount: DECIMAL
  total: DECIMAL
  
  -- Estado
  status: ENUM (draft, generated, sent, cancelled)
  
  -- Archivos
  pdfUrl: TEXT
  xmlUrl: TEXT
  
  -- Timestamps
  generatedAt: TIMESTAMP
  sentAt: TIMESTAMP
  cancelledAt: TIMESTAMP
}

-- Catálogo de precios de medicamentos (preparación)
medication_prices {
  id: UUID
  medicationId: UUID (FK)
  consultoryId: UUID (FK)
  
  -- Precios
  purchasePrice: DECIMAL (costo)
  salePrice: DECIMAL (venta)
  currency: VARCHAR
  
  -- Control
  isActive: BOOLEAN
  effectiveFrom: DATE
  effectiveTo: DATE
  
  -- Auditoría
  createdAt: TIMESTAMP
  updatedAt: TIMESTAMP
}
```

## 📊 Flujo de Trabajo

### 1. Finalización de Consulta
```mermaid
[Consulta Médica]
    ↓
[Finalizar Consulta]
    ↓
¿Hay servicios o medicamentos?
    ↓ NO → [Redirigir a Agenda]
    ↓ SÍ
[Módulo de Ventas]
```

### 2. Proceso de Venta
```mermaid
[Resumen de Venta]
    ↓
[Selección de items a cobrar]
    ↓
[Aplicar descuentos si aplica]
    ↓
[Selección método(s) de pago]
    ↓
[Procesar pago]
    ↓
[Generar factura(s) FEL]
    ↓
[Enviar por email]
    ↓
[Completar y redirigir]
```

## 🚀 Plan de Implementación

### Fase 1: Base de Datos (Día 1)
- [ ] Crear migración con nuevas tablas
- [ ] Agregar campos de precio a medicamentos
- [ ] Actualizar esquema de servicios con companyId
- [ ] Crear índices necesarios

### Fase 2: Backend APIs (Días 2-3)
- [ ] API: GET /api/sales/consultation/[id]/summary
- [ ] API: POST /api/sales/transactions
- [ ] API: POST /api/sales/payments
- [ ] API: POST /api/sales/invoices/generate
- [ ] API: GET /api/sales/transactions/[id]
- [ ] Integración con Recurrente
- [ ] Integración con FEL

### Fase 3: Frontend - Página de Resumen (Días 4-5)
- [ ] Crear /dashboard/doctor/sales/[consultationId]/summary
- [ ] Componente de resumen de servicios
- [ ] Componente de resumen de medicamentos
- [ ] Selector de métodos de pago
- [ ] UI para pagos múltiples
- [ ] Preview de factura

### Fase 4: Lógica de Negocio (Día 6)
- [ ] Cálculo de totales e impuestos
- [ ] Manejo de múltiples NITs emisores
- [ ] Validación de pagos
- [ ] Generación de PDFs

### Fase 5: Integración y Testing (Día 7)
- [ ] Modificar flujo de finalización de consulta
- [ ] Testing de pagos
- [ ] Testing de facturación
- [ ] Manejo de errores

## 📝 Estado del Desarrollo

### ✅ Completado
- [x] Análisis de requisitos
- [x] Diseño de arquitectura
- [x] Documentación del plan

### 🔄 En Progreso
- [ ] Creación de esquemas de base de datos

### ⏳ Pendiente
- [ ] Implementación de APIs
- [ ] Desarrollo de interfaces
- [ ] Integraciones externas
- [ ] Testing completo

## 🔧 Configuración Requerida

### Variables de Entorno
```env
# Recurrente (Pasarela de Pago)
RECURRENTE_API_KEY=
RECURRENTE_API_SECRET=
RECURRENTE_WEBHOOK_SECRET=

# FEL (Facturación Electrónica)
FEL_API_URL=
FEL_API_KEY=
FEL_CERTIFICADO=

# Configuración de Impuestos
TAX_RATE=0.12
DEFAULT_CURRENCY=GTQ
```

### Dependencias
```json
{
  "dependencies": {
    "@recurrente/sdk": "^1.0.0",
    "fel-guatemala": "^1.0.0",
    "puppeteer": "^21.0.0",
    "react-to-print": "^2.14.0"
  }
}
```

## 🎯 Criterios de Aceptación

### CA-001: Activación Condicional
- ✅ El módulo solo se activa si hay cobros pendientes
- ✅ Redirección directa a agenda si no hay cobros

### CA-002: Procesamiento de Pagos
- ✅ Soporta múltiples métodos de pago
- ✅ Calcula correctamente totales e impuestos
- ✅ Registra todos los pagos en base de datos

### CA-003: Facturación
- ✅ Genera facturas FEL válidas
- ✅ Soporta múltiples emisores
- ✅ Envía facturas por email

### CA-004: Experiencia de Usuario
- ✅ Interfaz clara y fácil de usar
- ✅ Tiempo de respuesta < 3 segundos
- ✅ Manejo adecuado de errores

## 🐛 Consideraciones y Riesgos

### Riesgos Identificados
1. **Integración FEL**: Posibles cambios en API gubernamental
2. **Concurrencia**: Manejo de pagos simultáneos
3. **Rollback**: Reversión de transacciones fallidas
4. **Seguridad**: Protección de datos de pago

### Mitigaciones
1. Capa de abstracción para integraciones
2. Bloqueo optimista en transacciones
3. Sistema de logs detallado
4. Encriptación de datos sensibles

## 📈 Métricas de Éxito

- Tiempo promedio de procesamiento de venta: < 30 segundos
- Tasa de error en pagos: < 1%
- Satisfacción del usuario: > 90%
- Facturas generadas correctamente: 100%

## 🔄 Actualizaciones del Documento

| Fecha | Versión | Cambios | Autor |
|-------|---------|---------|-------|
| 2025-01-04 | 1.0.0 | Creación inicial del documento | Sistema |

---

## 📌 Notas Importantes

1. **Inventario**: La estructura está preparada para futuro manejo de inventario pero no se implementará en esta fase
2. **Multi-emisor**: Crítico para el funcionamiento correcto del sistema
3. **Pagos parciales**: Permitir abonos y saldos pendientes
4. **Auditoría**: Todos los movimientos deben quedar registrados

## 🚦 Próximos Pasos Inmediatos

1. ✅ Crear esquemas de base de datos
2. ⏳ Generar migración con Drizzle
3. ⏳ Implementar API de resumen de venta
4. ⏳ Crear página de resumen de venta