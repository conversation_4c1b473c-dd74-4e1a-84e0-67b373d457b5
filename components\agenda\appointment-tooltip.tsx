'use client';

import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeInConsultoryTimezone, formatInConsultoryTimezone, formatLocalTimeFromUTC, formatLocalDateFromUTC } from '@/lib/timezone-utils';
import { 
  Tooltip, 
  TooltipContent, 
  TooltipTrigger 
} from '@/components/ui/tooltip';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Clock, 
  User, 
  Stethoscope, 
  Home, 
  DollarSign,
  AlertCircle,
  Video,
  Phone
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AppointmentTooltipProps {
  appointment: any;
  children: React.ReactNode;
  side?: 'top' | 'right' | 'bottom' | 'left';
}

const statusConfig = {
  scheduled: {
    color: 'bg-blue-100 text-blue-800 border-blue-300',
    icon: Calendar,
    label: 'Programada'
  },
  confirmed: {
    color: 'bg-[#50bed2]/10 text-[#50bed2] border-[#50bed2]/30',
    icon: Calendar,
    label: 'Confirmada'
  },
  in_progress: {
    color: 'bg-purple-100 text-purple-800 border-purple-300',
    icon: Clock,
    label: 'En consulta'
  },
  completed: {
    color: 'bg-gray-100 text-gray-800 border-gray-300',
    icon: Calendar,
    label: 'Completada'
  },
  cancelled: {
    color: 'bg-red-100 text-red-800 border-red-300',
    icon: Calendar,
    label: 'Cancelada'
  },
  urgent: {
    color: 'bg-orange-100 text-orange-800 border-orange-300',
    icon: AlertCircle,
    label: 'Urgente'
  }
};

export function AppointmentTooltip({ 
  appointment, 
  children, 
  side = 'top' 
}: AppointmentTooltipProps) {
  const config = statusConfig[appointment.status as keyof typeof statusConfig];
  const StatusIcon = config?.icon || Calendar;

  const tooltipContent = (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
      {/* Header con paciente y estado */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1">
          <h3 className="font-semibold text-gray-900 text-sm">
            {appointment.patientFirstName && appointment.patientLastName 
              ? `${appointment.patientFirstName} ${appointment.patientLastName}`
              : appointment.activityTypeName || appointment.title
            }
          </h3>
          <div className="flex items-center gap-2 mt-1">
            <Badge 
              variant="outline" 
              className={cn("text-xs", config?.color)}
            >
              <StatusIcon className="h-3 w-3 mr-1" />
              {config?.label}
            </Badge>
            {appointment.isEmergency && (
              <Badge variant="destructive" className="text-xs">
                <AlertCircle className="h-3 w-3 mr-1" />
                Urgente
              </Badge>
            )}
          </div>
        </div>
      </div>

      {/* Información de horario */}
      <div className="space-y-2 mb-3">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Clock className="h-4 w-4" />
          <span className="font-medium">
            {formatLocalTimeFromUTC(appointment.startTime)} -
            {formatLocalTimeFromUTC(appointment.endTime)}
          </span>
          <span className="text-xs text-gray-500">
            ({appointment.duration} min)
          </span>
        </div>
        
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Calendar className="h-4 w-4" />
          <span>
            {formatLocalDateFromUTC(appointment.scheduledDate || appointment.startTime, "EEEE, d 'de' MMMM")}
          </span>
        </div>
      </div>

      {/* Información médica */}
      <div className="space-y-2 mb-3">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Stethoscope className="h-4 w-4" />
          <span>Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</span>
        </div>
        
        {appointment.consultoryName && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Home className="h-4 w-4" />
            <span>{appointment.consultoryName}</span>
          </div>
        )}
        
        {appointment.serviceName && (
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <User className="h-4 w-4" />
            <span>{appointment.serviceName}</span>
          </div>
        )}
      </div>

      {/* Información adicional */}
      {(appointment.estimatedPrice || appointment.isTelemedicine || appointment.chiefComplaint) && (
        <div className="border-t pt-2 space-y-2">
          {appointment.estimatedPrice && (
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <DollarSign className="h-4 w-4" />
              <span>{appointment.currency} {appointment.estimatedPrice}</span>
            </div>
          )}
          
          {appointment.isTelemedicine && (
            <div className="flex items-center gap-2 text-sm text-blue-600">
              <Video className="h-4 w-4" />
              <span>Teleconsulta</span>
            </div>
          )}
          
          {appointment.chiefComplaint && (
            <div className="text-sm">
              <span className="font-medium text-gray-700">Motivo: </span>
              <span className="text-gray-600">{appointment.chiefComplaint}</span>
            </div>
          )}
        </div>
      )}

      {/* Notas si existen */}
      {appointment.description && (
        <div className="border-t pt-2 mt-2">
          <div className="text-sm">
            <span className="font-medium text-gray-700">Notas: </span>
            <span className="text-gray-600">{appointment.description}</span>
          </div>
        </div>
      )}
    </div>
  );

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        {children}
      </TooltipTrigger>
      <TooltipContent 
        side={side}
        className="p-0 bg-transparent border-none shadow-none"
        sideOffset={8}
      >
        {tooltipContent}
      </TooltipContent>
    </Tooltip>
  );
}