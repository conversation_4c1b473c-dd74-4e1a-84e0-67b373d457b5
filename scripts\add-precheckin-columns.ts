import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';

async function addPreCheckinColumns() {
  console.log('🔧 Agregando columnas de pre-checkin a appointments...\n');
  
  try {
    // Agregar columnas de pre-checkin
    await db.execute(sql`
      ALTER TABLE appointments 
      ADD COLUMN IF NOT EXISTS "preCheckinSent" BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS "preCheckinCompleted" BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS "preCheckinToken" TEXT,
      ADD COLUMN IF NOT EXISTS "preCheckinCompletedAt" TIMESTAMP,
      ADD COLUMN IF NOT EXISTS "preCheckinCompletedBy" TEXT,
      ADD COLUMN IF NOT EXISTS "reminderSent48h" BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS "reminderSent24h" BOOLEAN DEFAULT false,
      ADD COLUMN IF NOT EXISTS "invitationSent" BOOLEAN DEFAULT false
    `);
    
    console.log('✅ Columnas agregadas exitosamente');
    
    // Verificar que las columnas existen
    const result = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'appointments' 
      AND column_name IN ('preCheckinSent', 'preCheckinCompleted', 'preCheckinToken', 'preCheckinCompletedAt', 
                          'preCheckinCompletedBy', 'reminderSent48h', 'reminderSent24h', 'invitationSent')
    `);
    
    console.log('\n📊 Columnas verificadas:');
    result.rows.forEach(row => {
      console.log(`  - ${row.column_name}`);
    });
    
    process.exit(0);
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

addPreCheckinColumns();