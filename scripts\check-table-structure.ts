import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';

async function checkTableStructure() {
  try {
    console.log('🔍 Verificando estructura de tablas...');
    
    // Verificar estructura de user_roles
    console.log('\n📋 Estructura de user_roles:');
    const userRolesColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user_roles'
      ORDER BY ordinal_position;
    `);
    
    userRolesColumns.rows.forEach((col: any) => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Verificar estructura de user
    console.log('\n👤 Estructura de user:');
    const userColumns = await db.execute(sql`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'public' 
      AND table_name = 'user'
      ORDER BY ordinal_position;
    `);
    
    userColumns.rows.forEach((col: any) => {
      console.log(`  - ${col.column_name}: ${col.data_type} (${col.is_nullable === 'YES' ? 'NULL' : 'NOT NULL'})`);
    });
    
    // Verificar algunos datos de ejemplo
    console.log('\n👨‍⚕️ Datos de ejemplo en user_roles:');
    const sampleRoles = await db.execute(sql`
      SELECT role, "userId", "specialtyId", "consultoryId", "createdAt"
      FROM user_roles
      LIMIT 3;
    `);
    
    sampleRoles.rows.forEach((role: any, index: number) => {
      console.log(`  ${index + 1}. Role: ${role.role}, UserId: ${role.userId}, SpecialtyId: ${role.specialtyId}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  checkTableStructure()
    .then(() => {
      console.log('\n🎉 Verificación completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { checkTableStructure };