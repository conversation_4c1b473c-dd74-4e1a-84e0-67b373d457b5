'use client';

import React from 'react';
import { Users, Calendar, FileText, Bell, Baby, UserPlus, Heart, CheckCircle, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { usePersonalContext, useUserContexts } from '@/hooks/use-user-contexts';
import { toast } from 'sonner';
import { translateRelationship } from '@/lib/utils';

export default function GuardianDashboard() {
  const { data, currentContext, switchContext } = useUserContexts();
  const guardianContext = usePersonalContext();
  const [appointments, setAppointments] = React.useState([]);
  const [loadingAppointments, setLoadingAppointments] = React.useState(false);
  
  // Cambiar automáticamente a contexto guardian si no está configurado
  React.useEffect(() => {
    if (data && currentContext !== 'guardian' && data.contexts.personal.isGuardian) {
      switchContext('guardian');
    }
  }, [data, currentContext, switchContext]);
  
  // Obtener dependientes directamente de los datos si guardianContext es null
  const dependents = guardianContext?.dependents || data?.contexts.personal.isGuardian?.dependents || [];
  const totalDependents = guardianContext?.totalDependents || data?.contexts.personal.isGuardian?.totalDependents || 0;

  // Obtener citas de los dependientes
  const fetchAppointments = async () => {
    setLoadingAppointments(true);
    try {
      const response = await fetch('/api/appointments?type=dependents&limit=50');
      if (response.ok) {
        const appointmentsData = await response.json();
        
        // Transformar los datos de la API al formato esperado por la UI
        const transformedAppointments = appointmentsData.data?.map(apt => ({
          id: apt.id,
          patientName: `${apt.patientFirstName || ''} ${apt.patientLastName || ''}`.trim() || 'Paciente',
          date: new Date(apt.scheduledDate).toLocaleDateString('es-GT'),
          time: apt.startTime || 'Sin hora',
          doctorName: `${apt.doctorFirstName || ''} ${apt.doctorLastName || ''}`.trim() || 'Doctor',
          preCheckinCompleted: apt.preCheckinCompleted || false,
          type: apt.serviceName || apt.activityTypeName || 'Consulta',
          patientId: apt.patientId,
          status: apt.status
        })) || [];
        
        setAppointments(transformedAppointments);
      } else {
        console.error('Error fetching appointments:', response.status);
        setAppointments([]);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setAppointments([]);
    } finally {
      setLoadingAppointments(false);
    }
  };

  // Cargar citas cuando el contexto esté listo
  React.useEffect(() => {
    if (currentContext === 'guardian') {
      fetchAppointments();
    }
  }, [currentContext]);

  // Función para hacer pre-checkin
  const handlePreCheckin = async (appointmentId: string, patientName: string) => {
    try {
      const response = await fetch(`/api/pre-checkin/${appointmentId}`, {
        method: 'POST',
      });
      
      if (response.ok) {
        toast.success(`Pre-checkin realizado exitosamente para ${patientName}`);
        
        // Actualizar el estado local para mostrar inmediatamente el cambio
        setAppointments(prev => 
          prev.map(apt => 
            apt.id === appointmentId 
              ? { ...apt, preCheckinCompleted: true }
              : apt
          )
        );
      } else {
        toast.error('Error al realizar pre-checkin');
      }
    } catch (error) {
      console.error('Error en pre-checkin:', error);
      toast.error('Error al realizar pre-checkin');
    }
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dashboard Encargado
        </h1>
        <p className="text-gray-600">
          Gestiona el cuidado de tus dependientes desde aquí
        </p>
      </div>
      
      {/* Métricas principales */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Pacientes a Cargo
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Users className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {totalDependents}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Bajo tu cuidado</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Citas Próximas
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-orange-100 flex items-center justify-center">
              <Calendar className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {appointments.length}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Esta semana</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Pre-checkins Pendientes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-purple-100 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {appointments.filter(apt => !apt.preCheckinCompleted).length}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Por completar</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Notificaciones
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
              <Bell className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              --
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Sin leer</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Área para dependientes */}
      <Card>
        <CardHeader>
          <CardTitle>Mis Dependientes</CardTitle>
          <p className="text-sm text-muted-foreground">
            Gestiona la información y cuidado de tus dependientes
          </p>
        </CardHeader>
        <CardContent>
          {dependents.length === 0 ? (
            <div className="text-center py-8">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">
                No tienes dependientes registrados.
              </p>
              <Button className="bg-blue-600 hover:bg-blue-700">
                <UserPlus className="h-4 w-4 mr-2" />
                Agregar Dependiente
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {dependents.map((dependent) => (
                <div
                  key={dependent.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                      {dependent.age < 18 ? (
                        <Baby className="h-6 w-6 text-blue-600" />
                      ) : (
                        <Users className="h-6 w-6 text-blue-600" />
                      )}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{dependent.name}</h3>
                      <div className="flex items-center text-sm text-gray-500 space-x-3">
                        <span>{dependent.age} años</span>
                        <span>•</span>
                        <span className="capitalize">{dependent.relationship}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button variant="outline" size="sm">
                      <Calendar className="h-4 w-4 mr-2" />
                      Ver Citas
                    </Button>
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Expediente
                    </Button>
                  </div>
                </div>
              ))}
              
              <div className="pt-4 border-t">
                <Button variant="outline" className="w-full">
                  <UserPlus className="h-4 w-4 mr-2" />
                  Agregar Otro Dependiente
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Área para citas próximas y pre-checkin */}
      <Card>
        <CardHeader>
          <CardTitle>Citas Próximas</CardTitle>
          <p className="text-sm text-muted-foreground">
            Gestiona las citas de tus dependientes y realiza pre-checkin
          </p>
        </CardHeader>
        <CardContent>
          {loadingAppointments ? (
            <div className="text-center py-8">
              <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4 animate-spin" />
              <p className="text-gray-500">Cargando citas...</p>
            </div>
          ) : appointments.length === 0 ? (
            <div className="text-center py-8">
              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 mb-4">
                No hay citas próximas programadas.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {appointments.map((appointment) => (
                <div
                  key={appointment.id}
                  className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
                >
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                      <Calendar className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <div className="flex items-center space-x-3 mb-1">
                        <h3 className="font-semibold text-gray-900">{appointment.patientName}</h3>
                        <Badge variant={appointment.status === 'completed' ? 'default' : appointment.status === 'confirmed' ? 'secondary' : 'outline'}>
                          {appointment.status === 'scheduled' ? 'Programada' : 
                           appointment.status === 'confirmed' ? 'Confirmada' :
                           appointment.status === 'completed' ? 'Completada' :
                           appointment.status === 'cancelled' ? 'Cancelada' : appointment.status}
                        </Badge>
                      </div>
                      <div className="flex items-center text-sm text-gray-500 space-x-3">
                        <span>{appointment.date}</span>
                        <span>•</span>
                        <span>{appointment.time}</span>
                        <span>•</span>
                        <span className="capitalize">{appointment.doctorName}</span>
                        <span>•</span>
                        <span className="text-xs">{appointment.type}</span>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {appointment.preCheckinCompleted ? (
                      <div className="flex items-center text-green-600">
                        <CheckCircle className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">Pre-checkin completado</span>
                      </div>
                    ) : (
                      <Button 
                        onClick={() => handlePreCheckin(appointment.id, appointment.patientName)}
                        className="bg-blue-600 hover:bg-blue-700"
                        size="sm"
                      >
                        <CheckCircle className="h-4 w-4 mr-2" />
                        Pre-checkin
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <FileText className="h-4 w-4 mr-2" />
                      Ver Detalles
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}