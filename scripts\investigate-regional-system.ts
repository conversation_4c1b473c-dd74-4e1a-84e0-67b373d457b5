import { db } from '@/db/drizzle';
import { user, consultories, countries, systemConfig } from '@/db/schema';

async function investigateRegionalSystem() {
  console.log('🔍 INVESTIGACIÓN DEL SISTEMA REGIONAL');
  console.log('=====================================\n');

  try {
    // 1. Verificar consultorios y sus países
    console.log('1️⃣ CONSULTORIOS Y SUS CONFIGURACIONES:');
    const consultoriesData = await db
      .select()
      .from(consultories)
      .limit(10);
    
    console.log(`Total consultorios: ${consultoriesData.length}`);
    consultoriesData.forEach(c => {
      console.log(`- ${c.name}: País ID=${c.countryId}`);
      if (c.regionalSettings) {
        console.log(`  Configuración regional:`, c.regionalSettings);
      } else {
        console.log(`  Configuración regional: NO CONFIGURADO`);
      }
    });
    console.log('');

    // 2. Verificar países disponibles
    console.log('2️⃣ PAÍSES EN EL SISTEMA:');
    const countriesData = await db
      .select()
      .from(countries)
      .limit(10);
    
    countriesData.forEach(country => {
      console.log(`- ${country.name}: Código telefónico=${country.phoneCode}`);
    });
    console.log('');

    // 3. Analizar formatos de teléfonos en usuarios
    console.log('3️⃣ ANÁLISIS DE TELÉFONOS EN USUARIOS:');
    const usersWithPhones = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        phone: user.phone,
        emergencyPhone: user.emergencyPhone
      })
      .from(user)
      .limit(20);

    console.log('Formatos de teléfonos encontrados:');
    const phoneFormats = new Set<string>();
    const emergencyPhoneFormats = new Set<string>();

    usersWithPhones.forEach(u => {
      if (u.phone) phoneFormats.add(u.phone);
      if (u.emergencyPhone) emergencyPhoneFormats.add(u.emergencyPhone);
    });

    console.log('\n📱 Teléfonos principales:');
    Array.from(phoneFormats).slice(0, 10).forEach(phone => {
      console.log(`  - "${phone}"`);
    });

    console.log('\n🚨 Teléfonos de emergencia:');
    Array.from(emergencyPhoneFormats).slice(0, 10).forEach(phone => {
      console.log(`  - "${phone}"`);
    });

    // 4. Verificar si hay configuración regional por defecto
    console.log('\n4️⃣ CONFIGURACIÓN REGIONAL:');
    
    try {
      const systemConfigData = await db
        .select()
        .from(systemConfig)
        .limit(20);
      
      console.log('Configuración del sistema encontrada:', systemConfigData.length > 0 ? 'SÍ' : 'NO');
      systemConfigData.forEach(config => {
        if (config.category === 'regional') {
          console.log(`- ${config.key}: ${config.value}`);
        }
      });
    } catch (e) {
      console.log('Error accediendo a system_config:', (e as Error).message);
    }

    // 5. Patrones detectados
    console.log('\n5️⃣ PATRONES DETECTADOS:');
    
    const allPhones = Array.from(phoneFormats);
    const withCountryCode = allPhones.filter(p => p.includes('+') || p.includes('502'));
    const withoutCountryCode = allPhones.filter(p => !p.includes('+') && !p.includes('502'));
    const withSpaces = allPhones.filter(p => p.includes(' '));
    const withDashes = allPhones.filter(p => p.includes('-'));

    console.log(`📊 Total teléfonos analizados: ${allPhones.length}`);
    console.log(`📊 Con código país: ${withCountryCode.length}`);
    console.log(`📊 Sin código país: ${withoutCountryCode.length}`);
    console.log(`📊 Con espacios: ${withSpaces.length}`);
    console.log(`📊 Con guiones: ${withDashes.length}`);

    // Ejemplos de cada tipo
    if (withCountryCode.length > 0) {
      console.log(`   Ejemplo con código: "${withCountryCode[0]}"`);
    }
    if (withoutCountryCode.length > 0) {
      console.log(`   Ejemplo sin código: "${withoutCountryCode[0]}"`);
    }

    console.log('\n✅ Investigación completada');

  } catch (error) {
    console.error('❌ Error durante la investigación:', error);
  } finally {
    process.exit(0);
  }
}

investigateRegionalSystem();