import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { validateShortCode } from '@/lib/short-codes';

export async function GET(
  request: NextRequest,
  { params }: { params: { code: string } }
) {
  try {
    const { code } = params;
    
    // Validar formato
    if (!validateShortCode(code)) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Código inválido. Debe tener el formato SGC1234567' 
        },
        { status: 400 }
      );
    }

    // Buscar cita
    const appointment = await db
      .select({
        id: appointments.id,
        shortCode: appointments.shortCode,
        preCheckinToken: appointments.preCheckinToken,
        patientId: appointments.patientId,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        status: appointments.status,
        title: appointments.title,
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
        patientEmail: user.email,
        emailCaptured: appointments.emailCaptured,
        confirmedAt: appointments.confirmedAt
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.patientId, user.id))
      .where(eq(appointments.shortCode, code))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Código no encontrado. Verifica que el código sea correcto.' 
        },
        { status: 404 }
      );
    }

    const apt = appointment[0];
    const now = new Date();
    const appointmentDate = new Date(apt.scheduledDate);
    const hoursUntil = (appointmentDate.getTime() - now.getTime()) / (1000 * 60 * 60);

    return NextResponse.json({
      success: true,
      appointment: {
        ...apt,
        hoursUntil: Math.round(hoursUntil * 10) / 10, // Redondear a 1 decimal
        canConfirm: hoursUntil > -2 && apt.status !== 'cancelled', // Puede confirmar hasta 2h después
        isAlreadyConfirmed: !!apt.confirmedAt,
        isPastDue: hoursUntil < -2
      }
    });

  } catch (error) {
    console.error('Error finding appointment by code:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}