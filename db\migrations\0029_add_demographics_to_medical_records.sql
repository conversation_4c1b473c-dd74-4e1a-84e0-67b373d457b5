-- Migración: Agregar campos demográficos al expediente médico
-- Fecha: 2025-01-27
-- Descripción: Mover datos demográficos y administrativos desde consultas al expediente médico

-- 1. Agregar campos demográficos a medical_records
ALTER TABLE medical_records 
ADD COLUMN demographics jsonb DEFAULT '{}'::jsonb;

-- 2. Agregar campos administrativos a medical_records
ALTER TABLE medical_records 
ADD COLUMN administrative jsonb DEFAULT '{}'::jsonb;

-- 3. Agregar campos de información de contacto
ALTER TABLE medical_records 
ADD COLUMN emergency_contact jsonb DEFAULT '{}'::jsonb;

-- 4. Agregar comentarios para documentar la estructura
COMMENT ON COLUMN medical_records.demographics IS 
'Información demográfica del paciente. Estructura: 
{
  "religion": "id_religion",
  "occupation": "id_ocupacion", 
  "maritalStatus": "id_estado_civil",
  "educationLevel": "id_nivel_educativo",
  "ethnicity": "string",
  "nationality": "string",
  "languagePreference": "string"
}';

COMMENT ON COLUMN medical_records.administrative IS 
'Información administrativa del paciente. Estructura:
{
  "mediaSource": "id_medio_contacto",
  "company": "id_empresa", 
  "preferredConsultory": "id_consultorio",
  "insuranceProvider": "string",
  "insuranceNumber": "string",
  "referredBy": "string"
}';

COMMENT ON COLUMN medical_records.emergency_contact IS 
'Contacto de emergencia del paciente. Estructura:
{
  "name": "string",
  "relationship": "id_parentesco",
  "phone": "string",
  "email": "string",
  "address": "string"
}';