// Cargar variables de entorno
import dotenv from 'dotenv';
import path from 'path';
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

console.log('🔍 DIAGNÓSTICO DEL SISTEMA DE EMAILS\n');

// 1. Verificar variables de entorno
console.log('1. Variables de entorno:');
console.log('   RESEND_API_KEY:', process.env.RESEND_API_KEY ? '✅ Presente' : '❌ Faltante');
console.log('   RESEND_FROM_EMAIL:', process.env.RESEND_FROM_EMAIL || '❌ Faltante');
console.log('   NODE_ENV:', process.env.NODE_ENV || 'undefined');
console.log('');

// 2. Probar Resend directamente
async function testResendDirect() {
  console.log('2. Probando Resend directamente...');
  
  try {
    const { Resend } = require('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);
    
    const result = await resend.emails.send({
      from: process.env.RESEND_FROM_EMAIL || '<EMAIL>',
      to: '<EMAIL>', // Cambiar por tu email
      subject: '🧪 Test directo de Resend',
      html: `
        <h2>Test de Resend</h2>
        <p>Si recibes este email, Resend está funcionando correctamente.</p>
        <p>Timestamp: ${new Date().toISOString()}</p>
      `
    });
    
    console.log('   ✅ Resend directo exitoso:', result);
  } catch (error) {
    console.log('   ❌ Error en Resend directo:', error);
  }
}

// 3. Probar el nuevo sistema de emails
async function testNewEmailSystem() {
  console.log('3. Probando nuevo sistema de emails...');
  
  try {
    const { sendPatientEmail } = require('../lib/email-platform');
    
    const result = await sendPatientEmail(
      'appointment_created',
      '<EMAIL>', // Cambiar por tu email
      {
        patientName: 'Paciente Test',
        doctorName: 'Dr. Test',
        appointmentDate: 'Viernes, 02 de agosto de 2025',
        appointmentTime: '14:00',
        confirmationCode: 'TEST123',
        appointmentType: 'Consulta Test',
        consultoryName: 'Consultorio Test'
      },
      {
        patientId: 'test-patient',
        appointmentId: 'test-appointment',
        flowType: 'test'
      }
    );
    
    console.log('   ✅ Nuevo sistema exitoso:', result);
  } catch (error) {
    console.log('   ❌ Error en nuevo sistema:', error);
  }
}

// 4. Verificar que el template existe
function checkTemplate() {
  console.log('4. Verificando templates...');
  
  try {
    const { emailTemplates } = require('../lib/email-platform/templates');
    
    console.log('   appointment_created:', emailTemplates.appointment_created ? '✅ Existe' : '❌ Faltante');
    console.log('   patient_created:', emailTemplates.patient_created ? '✅ Existe' : '❌ Faltante');
    console.log('   precheckin_invitation:', emailTemplates.precheckin_invitation ? '✅ Existe' : '❌ Faltante');
  } catch (error) {
    console.log('   ❌ Error cargando templates:', error);
  }
}

// Ejecutar diagnósticos
async function runDiagnostics() {
  checkTemplate();
  console.log('');
  await testResendDirect();
  console.log('');
  await testNewEmailSystem();
}

runDiagnostics();