'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Phone, 
  AlertTriangle, 
  Pill, 
  FileText,
  Loader2,
  Heart,
  Shield
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { translateRelationship } from '@/lib/utils';

interface GuardianInfo {
  id: string;
  guardianFirstName: string | null;
  guardianLastName: string | null;
  guardianEmail: string | null;
  guardianPhone: string | null;
  relationship: string;
  isPrimary: boolean;
  canMakeDecisions: boolean;
}

interface PreCheckinFormImprovedProps {
  appointmentData: {
    id: string;
    patientFirstName: string | null;
    patientLastName: string | null;
    patientEmail: string | null;
    isDependent: boolean;
    guardianInfo?: GuardianInfo | null;
    allGuardians?: GuardianInfo[]; // Lista completa de encargados
  };
  isCompleted: boolean;
  showUserContext?: boolean;
  dashboardLink?: string;
}

interface PreCheckinData {
  // Confirmación de asistencia
  willAttend: 'yes' | 'no' | '';
  
  // Información médica relevante
  hasSymptoms: boolean;
  symptoms: string;
  takingMedications: boolean;
  medications: string;
  hasAllergies: boolean;
  allergies: string;
  
  // Información de contacto (solo si es necesaria)
  phone: string;
  
  // Motivo de consulta actualizado
  chiefComplaint: string;
  
  // Observaciones adicionales
  additionalNotes: string;
  
  // Gestión de acompañante
  companionType: 'registered_guardian' | 'different_person' | 'new_guardian' | '';
  selectedGuardianId: string; // Para cuando selecciona un encargado registrado
  
  // Campos para acompañante diferente
  companionName: string;
  companionRelationship: string;
  companionPhone: string;
  companionEmail: string;
  
  // Campos de emergencia
  emergencyContact: string;
  emergencyPhone: string;
  
  // Opciones para crear nueva relación
  shouldCreateNewRelation: boolean;
}

export function PreCheckinFormImproved({ 
  appointmentData, 
  isCompleted, 
  showUserContext = false,
  dashboardLink
}: PreCheckinFormImprovedProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<PreCheckinData>({
    willAttend: '',
    hasSymptoms: false,
    symptoms: '',
    takingMedications: false,
    medications: '',
    hasAllergies: false,
    allergies: '',
    phone: '',
    chiefComplaint: '',
    additionalNotes: '',
    
    // Gestión de acompañante
    companionType: appointmentData.guardianInfo ? 'registered_guardian' : '',
    selectedGuardianId: appointmentData.guardianInfo?.id || '',
    
    // Campos para acompañante diferente
    companionName: '',
    companionRelationship: '',
    companionPhone: '',
    companionEmail: '',
    
    // Campos de emergencia
    emergencyContact: '',
    emergencyPhone: '',
    
    // Opciones para crear nueva relación
    shouldCreateNewRelation: false,
  });

  // Determinar contextos de acompañamiento
  const patientName = appointmentData.patientFirstName;
  const hasRegisteredGuardians = appointmentData.isDependent && (appointmentData.allGuardians?.length || 0) > 0;
  const primaryGuardian = appointmentData.guardianInfo;
  const allGuardians = appointmentData.allGuardians || [];
  const isGuardianCompleting = appointmentData.isDependent && appointmentData.guardianInfo;
  
  // Mostrar campos de acompañante si:
  // 1. Es dependiente sin encargados (caso original)
  // 2. Es dependiente con encargados pero eligió "otra persona"
  const needsCompanionFields = appointmentData.isDependent && (
    !hasRegisteredGuardians || 
    formData.companionType === 'different_person' || 
    formData.companionType === 'new_guardian'
  );

  const updateFormData = (field: keyof PreCheckinData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async () => {
    // Validaciones básicas
    if (!formData.willAttend) {
      toast.error('Por favor confirma si asistirás a la cita');
      return;
    }

    if (formData.willAttend === 'no' && !formData.additionalNotes) {
      toast.error('Por favor explica por qué no podrás asistir');
      return;
    }

    // Validaciones específicas para dependientes 
    if (appointmentData.isDependent && formData.willAttend === 'yes') {
      // Si tiene encargados registrados, debe seleccionar uno o indicar otra persona
      if (hasRegisteredGuardians && !formData.companionType) {
        toast.error('Por favor indica quién acompañará al paciente');
        return;
      }
      
      // Si no tiene encargados registrados o eligió otra persona, validar campos
      if (needsCompanionFields) {
        if (!formData.companionName) {
          toast.error('Por favor indica el nombre del acompañante');
          return;
        }
        if (!formData.companionRelationship) {
          toast.error('Por favor indica la relación del acompañante con el paciente');
          return;
        }
        if (!formData.companionPhone) {
          toast.error('Por favor indica el teléfono del acompañante');
          return;
        }
      }
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/pre-checkin/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId: appointmentData.id,
          formData,
          isDependent: appointmentData.isDependent,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Pre-checkin completado exitosamente');
        
        if (showUserContext && dashboardLink) {
          setTimeout(() => {
            router.push(dashboardLink);
          }, 1500);
        } else {
          router.push(`/pre-checkin/confirmation?appointment=${appointmentData.id}`);
        }
      } else {
        toast.error(result.error || 'Error al enviar pre-checkin');
      }
    } catch (error) {
      console.error('Error submitting pre-checkin:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isCompleted) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Pre-checkin Completado
            </h3>
            <p className="text-green-700">
              Gracias por completar el pre-checkin. Te esperamos en tu cita.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header contextual */}
      {isGuardianCompleting && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-4">
            <div className="flex items-center space-x-3">
              <Shield className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Completando pre-checkin como {translateRelationship(appointmentData.guardianInfo?.relationship)}
                </p>
                <p className="text-xs text-blue-700">
                  Para {patientName} - Como ya estás registrado como responsable, solo necesitamos información médica actual
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Confirmación de asistencia */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>Confirmación de Asistencia</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label className="text-base font-medium">
              {appointmentData.isDependent 
                ? `¿${patientName} asistirá a la cita?`
                : '¿Asistirás a tu cita médica?'
              }
            </Label>
            <RadioGroup 
              value={formData.willAttend} 
              onValueChange={(value) => updateFormData('willAttend', value)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="attend-yes" />
                <Label htmlFor="attend-yes" className="cursor-pointer">
                  ✅ Sí, {appointmentData.isDependent ? 'asistirá' : 'asistiré'} a la cita
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="attend-no" />
                <Label htmlFor="attend-no" className="cursor-pointer">
                  ❌ No, {appointmentData.isDependent ? 'no podrá asistir' : 'no podré asistir'}
                </Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Información del acompañante - Nueva sección mejorada */}
      {appointmentData.isDependent && formData.willAttend === 'yes' && (
        <>
          {/* Mostrar encargados registrados si existen */}
          {hasRegisteredGuardians && (
            <Card className="border-blue-200 bg-blue-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-blue-800">
                  <Shield className="h-5 w-5" />
                  <span>Encargados Registrados</span>
                </CardTitle>
                <p className="text-sm text-blue-700 mt-1">
                  {patientName} tiene los siguientes encargados registrados:
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {allGuardians.map((guardian, index) => (
                    <div 
                      key={guardian.id} 
                      className={`p-3 rounded-lg border-2 transition-all ${
                        guardian.isPrimary 
                          ? 'bg-emerald-50 border-emerald-200' 
                          : 'bg-gray-50 border-gray-200'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium text-gray-900">
                              {guardian.guardianFirstName} {guardian.guardianLastName}
                            </span>
                            {guardian.isPrimary && (
                              <span className="text-xs bg-emerald-100 text-emerald-800 px-2 py-1 rounded-full">
                                Principal
                              </span>
                            )}
                            {guardian.canMakeDecisions && (
                              <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                                Puede decidir
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            {translateRelationship(guardian.relationship)}
                          </p>
                          {guardian.guardianPhone && (
                            <p className="text-sm text-gray-500">
                              📞 {guardian.guardianPhone}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                {/* Selector de tipo de acompañante */}
                <div className="mt-6 space-y-3">
                  <Label className="text-base font-medium">
                    ¿Quién acompañará a {patientName} a la cita?
                  </Label>
                  <RadioGroup 
                    value={formData.companionType} 
                    onValueChange={(value) => updateFormData('companionType', value)}
                  >
                    {/* Opción para cada encargado registrado */}
                    {allGuardians.map((guardian) => (
                      <div key={guardian.id} className="flex items-center space-x-2">
                        <RadioGroupItem 
                          value="registered_guardian" 
                          id={`guardian-${guardian.id}`}
                          onClick={() => updateFormData('selectedGuardianId', guardian.id)}
                        />
                        <Label htmlFor={`guardian-${guardian.id}`} className="cursor-pointer">
                          {guardian.guardianFirstName} {guardian.guardianLastName} ({translateRelationship(guardian.relationship)})
                          {guardian.isPrimary && ' - Principal'}
                        </Label>
                      </div>
                    ))}
                    
                    {/* Opción para otra persona */}
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="different_person" id="different-person" />
                      <Label htmlFor="different-person" className="cursor-pointer">
                        Otra persona (no registrada)
                      </Label>
                    </div>
                    
                    {/* Opción para agregar nuevo encargado */}
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="new_guardian" id="new-guardian" />
                      <Label htmlFor="new-guardian" className="cursor-pointer">
                        Nueva persona que quiero registrar como encargado
                      </Label>
                    </div>
                  </RadioGroup>
                </div>
              </CardContent>
            </Card>
          )}
          
          {/* Campos para acompañante diferente o nuevo encargado */}
          {needsCompanionFields && (
            <Card className="border-amber-200 bg-amber-50">
              <CardHeader>
                <CardTitle className="flex items-center space-x-2 text-amber-800">
                  <User className="h-5 w-5" />
                  <span>
                    {formData.companionType === 'new_guardian' 
                      ? 'Información del Nuevo Encargado' 
                      : 'Información del Acompañante'
                    }
                  </span>
                </CardTitle>
                <p className="text-sm text-amber-700 mt-1">
                  {formData.companionType === 'new_guardian' 
                    ? `Esta persona será registrada como encargado de ${patientName}` 
                    : `Información de la persona que acompañará a ${patientName}`
                  }
                </p>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companionName">Nombre completo *</Label>
                    <Input
                      id="companionName"
                      value={formData.companionName}
                      onChange={(e) => updateFormData('companionName', e.target.value)}
                      placeholder="Nombre completo"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companionRelationship">Relación con el paciente *</Label>
                    <Input
                      id="companionRelationship"
                      value={formData.companionRelationship}
                      onChange={(e) => updateFormData('companionRelationship', e.target.value)}
                      placeholder="Madre, padre, hermano, abuelo..."
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="companionPhone">Teléfono *</Label>
                    <Input
                      id="companionPhone"
                      value={formData.companionPhone}
                      onChange={(e) => updateFormData('companionPhone', e.target.value)}
                      placeholder="+502 1234-5678"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="companionEmail">Correo electrónico</Label>
                    <Input
                      id="companionEmail"
                      type="email"
                      value={formData.companionEmail}
                      onChange={(e) => updateFormData('companionEmail', e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>
                
                {/* Opción para crear nueva relación */}
                {formData.companionType === 'new_guardian' && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg border border-blue-200">
                    <div className="flex items-center space-x-2">
                      <Checkbox
                        id="shouldCreateNewRelation"
                        checked={formData.shouldCreateNewRelation}
                        onCheckedChange={(checked) => updateFormData('shouldCreateNewRelation', checked)}
                      />
                      <Label htmlFor="shouldCreateNewRelation" className="text-sm">
                        Registrar como encargado permanente de {patientName}
                      </Label>
                    </div>
                    <p className="text-xs text-blue-600 mt-1">
                      Esta persona podrá gestionar futuras citas y recibir información médica
                    </p>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </>
      )}

      {/* Información médica actual (solo si va a asistir) */}
      {formData.willAttend === 'yes' && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Heart className="h-5 w-5 text-red-500" />
                <span>Estado de Salud Actual</span>
              </CardTitle>
              <p className="text-sm text-gray-600 mt-1">
                Esta información ayuda al doctor a prepararse mejor para la consulta
              </p>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Síntomas */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasSymptoms"
                    checked={formData.hasSymptoms}
                    onCheckedChange={(checked) => updateFormData('hasSymptoms', checked)}
                  />
                  <Label htmlFor="hasSymptoms" className="cursor-pointer">
                    {appointmentData.isDependent 
                      ? `${patientName} presenta síntomas actualmente`
                      : 'Tengo síntomas actualmente'
                    }
                  </Label>
                </div>
                {formData.hasSymptoms && (
                  <Textarea
                    value={formData.symptoms}
                    onChange={(e) => updateFormData('symptoms', e.target.value)}
                    placeholder="Describe los síntomas: fiebre, dolor, malestar, desde cuándo..."
                    className="mt-2"
                  />
                )}
              </div>

              <Separator />

              {/* Medicamentos */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="takingMedications"
                    checked={formData.takingMedications}
                    onCheckedChange={(checked) => updateFormData('takingMedications', checked)}
                  />
                  <Label htmlFor="takingMedications" className="cursor-pointer flex items-center">
                    <Pill className="h-4 w-4 mr-2 text-blue-500" />
                    {appointmentData.isDependent 
                      ? `${patientName} está tomando medicamentos`
                      : 'Estoy tomando medicamentos actualmente'
                    }
                  </Label>
                </div>
                {formData.takingMedications && (
                  <Textarea
                    value={formData.medications}
                    onChange={(e) => updateFormData('medications', e.target.value)}
                    placeholder="Lista medicamentos con dosis y frecuencia: Ej. Paracetamol 500mg cada 8 horas"
                    className="mt-2"
                  />
                )}
              </div>

              <Separator />

              {/* Alergias */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasAllergies"
                    checked={formData.hasAllergies}
                    onCheckedChange={(checked) => updateFormData('hasAllergies', checked)}
                  />
                  <Label htmlFor="hasAllergies" className="cursor-pointer flex items-center">
                    <AlertTriangle className="h-4 w-4 mr-2 text-red-500" />
                    {appointmentData.isDependent 
                      ? `${patientName} tiene alergias conocidas`
                      : 'Tengo alergias conocidas'
                    }
                  </Label>
                </div>
                {formData.hasAllergies && (
                  <Textarea
                    value={formData.allergies}
                    onChange={(e) => updateFormData('allergies', e.target.value)}
                    placeholder="Describe alergias a: medicamentos, alimentos, sustancias, etc."
                    className="mt-2"
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Teléfono de contacto (solo campo esencial) */}
          {!isGuardianCompleting && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Phone className="h-5 w-5 text-green-600" />
                  <span>Contacto</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Label htmlFor="phone">Teléfono de contacto</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    placeholder="+502 1234-5678"
                  />
                  <p className="text-xs text-gray-500">
                    Para comunicarnos contigo el día de la cita si es necesario
                  </p>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Motivo de consulta */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-purple-600" />
                <span>Motivo de la Consulta</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="chiefComplaint">
                  {appointmentData.isDependent 
                    ? `¿Por qué llevas a ${patientName} al médico?`
                    : '¿Cuál es el motivo principal de tu consulta?'
                  }
                </Label>
                <Textarea
                  id="chiefComplaint"
                  value={formData.chiefComplaint}
                  onChange={(e) => updateFormData('chiefComplaint', e.target.value)}
                  placeholder="Describe brevemente el motivo: control rutinario, síntomas específicos, seguimiento..."
                />
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Motivo de cancelación (si no va a asistir) */}
      {formData.willAttend === 'no' && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <XCircle className="h-5 w-5" />
              <span>Motivo de la Inasistencia</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="additionalNotes">
                Por favor explica por qué no podrás asistir *
              </Label>
              <Textarea
                id="additionalNotes"
                value={formData.additionalNotes}
                onChange={(e) => updateFormData('additionalNotes', e.target.value)}
                placeholder="Explica el motivo de la cancelación para que podamos reprogramar..."
                className="border-red-300 focus:border-red-500"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Observaciones adicionales */}
      {formData.willAttend === 'yes' && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-600" />
              <span>Información Adicional</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="notes">¿Algo más que el doctor deba saber?</Label>
              <Textarea
                id="notes"
                value={formData.additionalNotes}
                onChange={(e) => updateFormData('additionalNotes', e.target.value)}
                placeholder="Cualquier información adicional que consideres importante..."
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Botón de envío */}
      <div className="flex justify-center pt-6">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.willAttend}
          size="lg"
          className="w-full md:w-auto px-8"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Enviando...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Completar Pre-checkin
            </>
          )}
        </Button>
      </div>
    </div>
  );
}