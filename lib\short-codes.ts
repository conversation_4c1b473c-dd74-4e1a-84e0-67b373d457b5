// Utilidades para manejo de códigos cortos de citas

// Letras que evitamos para reducir confusión al hablar
const SAFE_LETTERS = 'ABCDEFGHJKLMNPQRTUVWXY'; // Excluye: I, O, S, Z
const NUMBERS = '0123456789';

export function generateShortCode(): string {
  // Formato nuevo: ABC123 (3 letras + 3 números)
  let letters = '';
  let numbers = '';
  
  // Generar 3 letras aleatorias
  for (let i = 0; i < 3; i++) {
    letters += SAFE_LETTERS[Math.floor(Math.random() * SAFE_LETTERS.length)];
  }
  
  // Generar 3 números aleatorios
  for (let i = 0; i < 3; i++) {
    numbers += NUMBERS[Math.floor(Math.random() * NUMBERS.length)];
  }
  
  return `${letters}${numbers}`;
}

export function validateShortCode(code: string): boolean {
  // Validar formato ABC123 - 3 letras + 3 números
  return /^[ABCDEFGHJKLMNPQRTUVWXY]{3}\d{3}$/.test(code);
}

export function formatShortCodeForSpeech(code: string): string {
  // Convierte ABC123 en formato hablado usando alfabeto fonético español
  const phoneticSpanish = {
    'A': 'A de avión',
    'B': 'B de barco',
    'C': 'C de casa',
    'D': 'D de dado',
    'E': 'E de elefante',
    'F': 'F de fuego',
    'G': 'G de gato',
    'H': 'H de hotel',
    'J': 'J de jardín',
    'K': 'K de kilo',
    'L': 'L de luna',
    'M': 'M de mesa',
    'N': 'N de nube',
    'P': 'P de papel',
    'Q': 'Q de queso',
    'R': 'R de radio',
    'T': 'T de taxi',
    'U': 'U de uva',
    'V': 'V de vaso',
    'W': 'W de whisky',
    'X': 'X de xilófono',
    'Y': 'Y de yate'
  };
  
  if (!validateShortCode(code)) {
    return code; // Si no es válido, devolver como está
  }
  
  const letters = code.substring(0, 3);
  const numbers = code.substring(3);
  
  const phoneticLetters = letters.split('').map(letter => 
    phoneticSpanish[letter as keyof typeof phoneticSpanish] || letter
  );
  
  const phoneticNumbers = numbers.split('');
  
  return [...phoneticLetters, ...phoneticNumbers].join(', ');
}

// Función legacy para compatibilidad con códigos existentes SGC
export function generateLegacyShortCode(): string {
  const prefix = 'SGC'; // Sistema Gestión Citas
  const timestamp = Date.now().toString().slice(-4);
  const random = Math.floor(Math.random() * 900) + 100;
  return `${prefix}${timestamp}${random}`;
}

export function validateLegacyShortCode(code: string): boolean {
  return /^SGC\d{7}$/.test(code);
}

// Función universal que valida ambos formatos
export function validateAnyShortCode(code: string): boolean {
  return validateShortCode(code) || validateLegacyShortCode(code);
}

// Ejemplos de uso:
// generateShortCode() → "ABC123"
// validateShortCode("ABC123") → true
// formatShortCodeForSpeech("ABC123") → "A de avión, B de barco, C de casa, 1, 2, 3"
//
// Legacy:
// generateLegacyShortCode() → "SGC1234567"
// validateLegacyShortCode("SGC1234567") → true
// validateAnyShortCode("ABC123") → true
// validateAnyShortCode("SGC1234567") → true