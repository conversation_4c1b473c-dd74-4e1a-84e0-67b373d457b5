import { db } from '@/db/drizzle';
import { medicalConsultations, appointments, medicalServices, user } from '@/db/schema';
import { eq } from 'drizzle-orm';

async function checkConsultationServices() {
  console.log('🔍 Revisando servicios en consultas existentes...\n');

  try {
    // Obtener consultas con sus citas relacionadas
    const consultations = await db
      .select({
        consultationId: medicalConsultations.id,
        consultationServices: medicalConsultations.services,
        appointmentId: medicalConsultations.appointmentId,
        appointmentServiceId: appointments.serviceId,
        doctorName: user.firstName,
      })
      .from(medicalConsultations)
      .leftJoin(appointments, eq(medicalConsultations.appointmentId, appointments.id))
      .leftJoin(user, eq(medicalConsultations.doctorId, user.id))
      .limit(10);

    console.log(`📋 Encontradas ${consultations.length} consultas:\n`);

    for (const consultation of consultations) {
      console.log(`🏥 Consulta ID: ${consultation.consultationId}`);
      console.log(`👨‍⚕️ Doctor: ${consultation.doctorName || 'N/A'}`);
      console.log(`📅 Cita ID: ${consultation.appointmentId || 'Sin cita asociada'}`);
      console.log(`🔗 ServiceId de la cita: ${consultation.appointmentServiceId || 'N/A'}`);
      console.log(`💼 Servicios en consulta:`, consultation.consultationServices);
      
      // Si hay serviceId en la cita, buscar detalles del servicio
      if (consultation.appointmentServiceId) {
        const serviceDetails = await db
          .select()
          .from(medicalServices)
          .where(eq(medicalServices.id, consultation.appointmentServiceId))
          .limit(1);
        
        if (serviceDetails.length > 0) {
          console.log(`📋 Detalles del servicio: ${serviceDetails[0].name} - ${serviceDetails[0].basePrice} GTQ`);
        } else {
          console.log(`❌ Servicio ${consultation.appointmentServiceId} no encontrado en catálogo`);
        }
      }
      
      console.log('─────────────────────────────────────────────────────\n');
    }

    // Estadísticas rápidas
    const withServices = consultations.filter(c => 
      c.consultationServices && Array.isArray(c.consultationServices) && c.consultationServices.length > 0
    );
    const withAppointmentServices = consultations.filter(c => c.appointmentServiceId);
    
    console.log('📊 ESTADÍSTICAS:');
    console.log(`Total consultas: ${consultations.length}`);
    console.log(`Con servicios en consulta: ${withServices.length}`);
    console.log(`Con serviceId en cita: ${withAppointmentServices.length}`);
    console.log(`Consultas que perdieron servicios: ${withAppointmentServices.length - withServices.length}`);

  } catch (error) {
    console.error('❌ Error al revisar consultas:', error);
  }
}

checkConsultationServices();