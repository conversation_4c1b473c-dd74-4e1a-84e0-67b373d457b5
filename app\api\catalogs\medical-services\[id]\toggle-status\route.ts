import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalServices } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Alternar estado del servicio médico
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar que el usuario tenga permisos de admin
    const userRole = sessionClaims?.metadata?.role;
    if (userRole !== 'admin') {
      return NextResponse.json({ error: 'No tienes permisos para cambiar el estado de servicios médicos' }, { status: 403 });
    }

    const serviceId = params.id;
    
    // Verificar que el servicio existe
    const service = await db.select().from(medicalServices).where(eq(medicalServices.id, serviceId));
    if (service.length === 0) {
      return NextResponse.json({ error: 'Servicio médico no encontrado' }, { status: 404 });
    }

    const currentStatus = service[0].isActive;
    const newStatus = !currentStatus;

    // Actualizar el estado
    const result = await db.update(medicalServices)
      .set({ 
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(medicalServices.id, serviceId))
      .returning();

    return NextResponse.json({
      message: `Servicio médico ${newStatus ? 'activado' : 'desactivado'} exitosamente`,
      data: result[0],
    });
  } catch (error) {
    console.error('Error toggling medical service status:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}