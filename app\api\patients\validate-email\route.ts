import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { success: false, error: 'Email es requerido' },
        { status: 400 }
      );
    }

    // Buscar si el email pertenece a un usuario existente con su rol
    const existingUser = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        status: user.overallStatus,
        role: userRoles.role,
      })
      .from(user)
      .leftJoin(userRoles, eq(user.id, userRoles.userId))
      .where(eq(user.email, email))
      .limit(1);

    if (existingUser.length === 0) {
      return NextResponse.json({
        success: true,
        available: true,
        isSystemUser: false,
      });
    }

    const foundUser = existingUser[0];

    // Determinar si es un usuario del sistema (assistant, doctor, admin)
    // El role puede ser null si no hay userRoles asociado
    const userRole = foundUser.role || '';
    const isSystemUser = ['assistant', 'doctor', 'admin'].includes(userRole);

    return NextResponse.json({
      success: true,
      available: false,
      isSystemUser,
      user: isSystemUser ? {
        id: foundUser.id,
        firstName: foundUser.firstName,
        lastName: foundUser.lastName,
        role: userRole,
        status: foundUser.status,
      } : null,
    });

  } catch (error) {
    console.error('Error validating email:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}