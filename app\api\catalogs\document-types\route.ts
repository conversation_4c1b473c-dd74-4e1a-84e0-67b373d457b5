import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { documentTypes } from '@/db/schema';
import { eq, and, count, asc, desc, ilike, or } from 'drizzle-orm';

// GET - Listar tipos de documento
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const countryId = searchParams.get('countryId');
    const onlyActive = searchParams.get('active') === 'true';
    const onlyRequired = searchParams.get('required') === 'true';
    const orderBy = searchParams.get('orderBy') || 'name';
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(documentTypes);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(documentTypes.name, `%${search}%`),
          ilike(documentTypes.shortName, `%${search}%`),
          ilike(documentTypes.countryName, `%${search}%`)
        )
      );
    }

    if (countryId) {
      conditions.push(eq(documentTypes.countryId, countryId));
    }

    if (onlyActive) {
      conditions.push(eq(documentTypes.isActive, true));
    }

    if (onlyRequired) {
      conditions.push(eq(documentTypes.isRequired, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'shortName' ? documentTypes.shortName :
                       orderBy === 'countryName' ? documentTypes.countryName :
                       orderBy === 'createdAt' ? documentTypes.createdAt :
                       documentTypes.name;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const documentTypesData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(documentTypes);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    // Obtener países únicos
    const allCountries = await db.select({
      countryId: documentTypes.countryId,
      countryName: documentTypes.countryName
    }).from(documentTypes).where(eq(documentTypes.isActive, true));
    
    const countries = Array.from(new Set(allCountries.map(c => JSON.stringify(c))))
      .map(c => JSON.parse(c));

    return NextResponse.json({
      data: documentTypesData,
      countries,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: documentTypesData.filter(doc => doc.isActive).length,
        required: documentTypesData.filter(doc => doc.isRequired).length
      }
    });

  } catch (error) {
    console.error('Error obteniendo tipos de documento:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo tipo de documento (solo admin)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden crear tipos de documento' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { 
      name, 
      shortName, 
      countryId, 
      countryName, 
      format, 
      maxLength, 
      minLength, 
      isRequired, 
      description, 
      example, 
      isActive 
    } = body;

    // Validaciones
    if (!name || !shortName || !countryId || !countryName) {
      return NextResponse.json(
        { error: 'Nombre, nombre corto, ID país y nombre país son requeridos' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = `${countryId.toLowerCase()}-${shortName.toLowerCase()}`
      .replace(/[^a-z0-9\-]/g, '')
      .substring(0, 50);

    // Verificar que no exista un tipo con el mismo ID
    const existingType = await db.select().from(documentTypes).where(eq(documentTypes.id, id)).limit(1);
    if (existingType.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un tipo de documento con este nombre para este país' },
        { status: 400 }
      );
    }

    const newDocumentType = {
      id,
      name: name.trim(),
      shortName: shortName.trim(),
      countryId: countryId.toUpperCase(),
      countryName: countryName.trim(),
      format: format?.trim() || null,
      maxLength: maxLength ? parseInt(maxLength) : null,
      minLength: minLength ? parseInt(minLength) : null,
      isRequired: Boolean(isRequired),
      description: description?.trim() || null,
      example: example?.trim() || null,
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedDocumentType] = await db.insert(documentTypes).values(newDocumentType).returning();

    return NextResponse.json({
      message: 'Tipo de documento creado exitosamente',
      data: insertedDocumentType
    });

  } catch (error) {
    console.error('Error creando tipo de documento:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar tipo de documento (solo admin)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden actualizar tipos de documento' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, shortName, countryName, format, maxLength, minLength, isRequired, description, example, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de tipo de documento requerido' },
        { status: 400 }
      );
    }

    // Buscar tipo existente
    const existingType = await db.select().from(documentTypes).where(eq(documentTypes.id, id)).limit(1);
    if (existingType.length === 0) {
      return NextResponse.json(
        { error: 'Tipo de documento no encontrado' },
        { status: 404 }
      );
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(shortName && { shortName: shortName.trim() }),
      ...(countryName && { countryName: countryName.trim() }),
      ...(format !== undefined && { format: format?.trim() || null }),
      ...(maxLength !== undefined && { maxLength: maxLength ? parseInt(maxLength) : null }),
      ...(minLength !== undefined && { minLength: minLength ? parseInt(minLength) : null }),
      ...(isRequired !== undefined && { isRequired: Boolean(isRequired) }),
      ...(description !== undefined && { description: description?.trim() || null }),
      ...(example !== undefined && { example: example?.trim() || null }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedDocumentType] = await db.update(documentTypes)
      .set(updateData)
      .where(eq(documentTypes.id, id))
      .returning();

    return NextResponse.json({
      message: 'Tipo de documento actualizado exitosamente',
      data: updatedDocumentType
    });

  } catch (error) {
    console.error('Error actualizando tipo de documento:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}