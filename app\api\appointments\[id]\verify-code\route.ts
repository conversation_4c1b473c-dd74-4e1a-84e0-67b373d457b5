import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments } from '@/db/schema';
import { eq } from 'drizzle-orm';

interface RouteParams {
  params: {
    id: string;
  };
}

// POST - Verificar código de confirmación
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;
    const body = await request.json();
    const { code } = body;

    if (!code) {
      return NextResponse.json({ error: 'Código requerido' }, { status: 400 });
    }

    // Obtener la cita y verificar el código
    const result = await db
      .select({
        id: appointments.id,
        shortCode: appointments.shortCode,
        status: appointments.status,
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = result[0];

    // Verificar que la cita no esté ya cancelada
    if (appointment.status === 'cancelled') {
      return NextResponse.json({ error: 'Esta cita ya fue cancelada' }, { status: 400 });
    }

    // Verificar el código
    if (appointment.shortCode !== code.toUpperCase()) {
      return NextResponse.json({ error: 'Código incorrecto' }, { status: 401 });
    }

    return NextResponse.json({ 
      success: true,
      message: 'Código verificado correctamente'
    });
  } catch (error) {
    console.error('Error verifying code:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}