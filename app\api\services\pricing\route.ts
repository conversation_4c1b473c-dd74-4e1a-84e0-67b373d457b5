import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices, medicalServices, user } from '@/db/schema';
import { eq, and, lte, or, isNull, gte } from 'drizzle-orm';

// GET /api/services/pricing - Obtener precio efectivo para un servicio y médico
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const doctorId = searchParams.get('doctorId');
    const serviceId = searchParams.get('serviceId');
    const date = searchParams.get('date') || new Date().toISOString();

    if (!serviceId) {
      return NextResponse.json(
        { error: 'El ID del servicio es requerido' },
        { status: 400 }
      );
    }

    const effectiveDate = new Date(date);

    // Obtener información del servicio médico
    const service = await db
      .select({
        id: medicalServices.id,
        name: medicalServices.name,
        basePrice: medicalServices.basePrice,
        currency: medicalServices.currency,
        category: medicalServices.category,
      })
      .from(medicalServices)
      .where(eq(medicalServices.id, serviceId))
      .limit(1);

    if (!service.length) {
      return NextResponse.json(
        { error: 'Servicio médico no encontrado' },
        { status: 404 }
      );
    }

    const serviceData = service[0];
    let effectivePrice = {
      price: serviceData.basePrice,
      currency: serviceData.currency || 'GTQ',
      source: 'base' as 'base' | 'custom',
      doctorId: null as string | null,
      priceId: null as string | null,
      notes: null as string | null,
    };

    // Si se especifica un médico, buscar precio personalizado activo
    if (doctorId) {
      const customPrice = await db
        .select({
          id: doctorServicePrices.id,
          customPrice: doctorServicePrices.customPrice,
          currency: doctorServicePrices.currency,
          notes: doctorServicePrices.notes,
          effectiveFrom: doctorServicePrices.effectiveFrom,
          effectiveUntil: doctorServicePrices.effectiveUntil,
          // Datos del doctor
          doctorName: user.firstName,
          doctorLastName: user.lastName,
        })
        .from(doctorServicePrices)
        .leftJoin(user, eq(doctorServicePrices.doctorId, user.id))
        .where(
          and(
            eq(doctorServicePrices.doctorId, doctorId),
            eq(doctorServicePrices.serviceId, serviceId),
            eq(doctorServicePrices.isActive, true),
            // Verificar que la fecha está dentro del rango de vigencia
            lte(doctorServicePrices.effectiveFrom, effectiveDate),
            or(
              isNull(doctorServicePrices.effectiveUntil),
              gte(doctorServicePrices.effectiveUntil, effectiveDate)
            )
          )
        )
        .limit(1);

      if (customPrice.length) {
        const custom = customPrice[0];
        effectivePrice = {
          price: custom.customPrice,
          currency: custom.currency || 'GTQ',
          source: 'custom',
          doctorId,
          priceId: custom.id,
          notes: custom.notes,
        };
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        service: {
          id: serviceData.id,
          name: serviceData.name,
          category: serviceData.category,
          basePrice: serviceData.basePrice,
          baseCurrency: serviceData.currency || 'GTQ',
        },
        pricing: effectivePrice,
        requestDate: effectiveDate.toISOString(),
      },
    });

  } catch (error: any) {
    console.error('Error fetching service pricing:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/services/pricing/bulk - Obtener precios para múltiples servicios de un médico
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { doctorId, serviceIds, date } = body;

    if (!serviceIds || !Array.isArray(serviceIds) || serviceIds.length === 0) {
      return NextResponse.json(
        { error: 'Los IDs de servicios son requeridos y deben ser un array' },
        { status: 400 }
      );
    }

    const effectiveDate = new Date(date || new Date().toISOString());
    const results = [];

    // Procesar cada servicio
    for (const serviceId of serviceIds) {
      try {
        // Obtener información del servicio
        const service = await db
          .select({
            id: medicalServices.id,
            name: medicalServices.name,
            basePrice: medicalServices.basePrice,
            currency: medicalServices.currency,
            category: medicalServices.category,
          })
          .from(medicalServices)
          .where(eq(medicalServices.id, serviceId))
          .limit(1);

        if (!service.length) {
          results.push({
            serviceId,
            error: 'Servicio no encontrado',
          });
          continue;
        }

        const serviceData = service[0];
        let effectivePrice = {
          price: serviceData.basePrice,
          currency: serviceData.currency || 'GTQ',
          source: 'base' as 'base' | 'custom',
          doctorId: null as string | null,
          priceId: null as string | null,
          notes: null as string | null,
        };

        // Si se especifica un médico, buscar precio personalizado
        if (doctorId) {
          const customPrice = await db
            .select({
              id: doctorServicePrices.id,
              customPrice: doctorServicePrices.customPrice,
              currency: doctorServicePrices.currency,
              notes: doctorServicePrices.notes,
            })
            .from(doctorServicePrices)
            .where(
              and(
                eq(doctorServicePrices.doctorId, doctorId),
                eq(doctorServicePrices.serviceId, serviceId),
                eq(doctorServicePrices.isActive, true),
                lte(doctorServicePrices.effectiveFrom, effectiveDate),
                or(
                  isNull(doctorServicePrices.effectiveUntil),
                  gte(doctorServicePrices.effectiveUntil, effectiveDate)
                )
              )
            )
            .limit(1);

          if (customPrice.length) {
            const custom = customPrice[0];
            effectivePrice = {
              price: custom.customPrice,
              currency: custom.currency || 'GTQ',
              source: 'custom',
              doctorId,
              priceId: custom.id,
              notes: custom.notes,
            };
          }
        }

        results.push({
          service: {
            id: serviceData.id,
            name: serviceData.name,
            category: serviceData.category,
          },
          pricing: effectivePrice,
        });

      } catch (serviceError) {
        results.push({
          serviceId,
          error: 'Error procesando servicio',
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: results,
      requestDate: effectiveDate.toISOString(),
    });

  } catch (error: any) {
    console.error('Error fetching bulk service pricing:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}