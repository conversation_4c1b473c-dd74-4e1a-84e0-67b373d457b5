/**
 * UTILIDADES DE AUTENTICACIÓN
 * Funciones auxiliares para manejo de autenticación con Clerk
 */

import { auth } from '@clerk/nextjs/server';

/**
 * Obtiene el usuario autenticado actual
 */
export async function getCurrentUser() {
  try {
    const { userId, sessionClaims } = await auth();
    return { userId, sessionClaims };
  } catch (error) {
    console.error('Error obteniendo usuario actual:', error);
    return { userId: null, sessionClaims: null };
  }
}

/**
 * Verifica si el usuario está autenticado
 */
export async function isAuthenticated(): Promise<boolean> {
  const { userId } = await getCurrentUser();
  return !!userId;
}

/**
 * Verifica si el usuario tiene un rol específico
 */
export function hasRole(sessionClaims: any, role: string): boolean {
  return sessionClaims?.metadata?.role === role;
}

/**
 * Obtiene el rol del usuario
 */
export function getUserRole(sessionClaims: any): string | null {
  return sessionClaims?.metadata?.role || null;
}
