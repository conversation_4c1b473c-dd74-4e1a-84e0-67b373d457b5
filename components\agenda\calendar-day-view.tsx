'use client';

import { useState } from 'react';
import Image from 'next/image';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format, setHours, setMinutes, addMinutes, isToday, isBefore, parseISO } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeInConsultoryTimezone, formatLocalTimeFromUTC } from '@/lib/timezone-utils';
import { Calendar, Clock, Plus, User, Stethoscope, Home, DollarSign, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AppointmentActionsMenu } from './appointment-actions-menu';

interface CalendarDayViewProps {
  appointments: any[];
  currentDate: Date;
  onTimeSlotClick?: (date: Date, time: string) => void;
  onAppointmentClick?: (appointment: any) => void;
  onAppointmentEdit?: (appointment: any) => void;
  onAppointmentConfirm?: (appointment: any) => void;
  onAppointmentCancel?: (appointment: any) => void;
  onAppointmentDelete?: (appointment: any) => void;
  onAppointmentCheckIn?: (appointment: any) => void;
  onAppointmentNoShow?: (appointment: any) => void;
  onAppointmentStart?: (appointment: any) => void;
  onAppointmentComplete?: (appointment: any) => void;
  onAppointmentRevertNoShow?: (appointment: any) => void;
  onAppointmentRevertCompleted?: (appointment: any) => void;
  onViewPreCheckin?: (appointment: any) => void;
  onViewConsultation?: (appointment: any) => void;
  loading?: boolean;
  userRole?: 'doctor' | 'assistant' | 'admin';
  doctorInfo?: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    imageUrl?: string;
    clerkImageUrl?: string;
    specialty?: string;
  };
}

const statusConfig = {
  scheduled: {
    color: 'bg-blue-100 border-l-blue-500 text-blue-800',
    icon: Calendar,
    label: 'Programada'
  },
  pending_confirmation: {
    color: 'bg-orange-100 border-l-orange-500 text-orange-800',
    icon: AlertCircle,
    label: 'Pendiente de Confirmación'
  },
  confirmed: {
    color: 'bg-[#50bed2]/10 border-l-[#50bed2] text-[#50bed2]',
    icon: Calendar,
    label: 'Confirmada'
  },
  in_progress: {
    color: 'bg-purple-100 border-l-purple-500 text-purple-800',
    icon: Clock,
    label: 'En consulta'
  },
  completed: {
    color: 'bg-gray-100 border-l-gray-500 text-gray-700',
    icon: Calendar,
    label: 'Completada'
  },
  cancelled: {
    color: 'bg-red-100 border-l-red-500 text-red-700',
    icon: Calendar,
    label: 'Cancelada'
  },
  no_show: {
    color: 'bg-pink-100 border-l-pink-500 text-pink-800',
    icon: AlertCircle,
    label: 'No asistió'
  },
  checked_in: {
    color: 'bg-teal-100 border-l-teal-500 text-teal-800',
    icon: User,
    label: 'Paciente llegó'
  },
  urgent: {
    color: 'bg-orange-100 border-l-orange-500 text-orange-800',
    icon: AlertCircle,
    label: 'Urgente'
  }
};

const timeSlots = [];
for (let hour = 7; hour <= 22; hour++) { // Extendido hasta 10 PM
  for (let minute = 0; minute < 60; minute += 30) {
    const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    timeSlots.push(time);
  }
}

export function CalendarDayView({ 
  appointments, 
  currentDate, 
  onTimeSlotClick, 
  onAppointmentClick,
  onAppointmentEdit,
  onAppointmentConfirm,
  onAppointmentCancel,
  onAppointmentDelete,
  onAppointmentCheckIn,
  onAppointmentNoShow,
  onAppointmentStart,
  onAppointmentComplete,
  onAppointmentRevertNoShow,
  onAppointmentRevertCompleted,
  onViewPreCheckin,
  onViewConsultation,
  loading = false,
  userRole = 'assistant',
  doctorInfo
}: CalendarDayViewProps) {
  const [hoveredSlot, setHoveredSlot] = useState<string | null>(null);

  // Filtrar citas del día actual
  const todayAppointments = appointments.filter(appointment => {
    try {
      // Usar solo la parte de fecha de scheduledDate (ignorar timezone)
      const appointmentDateStr = appointment.scheduledDate.split('T')[0]; // '2025-07-24'
      const currentDateStr = format(currentDate, 'yyyy-MM-dd');
      const matches = appointmentDateStr === currentDateStr;
      
      
      return matches;
    } catch (error) {
      console.error('Error parsing appointment date:', appointment.scheduledDate, error);
      return false;
    }
  });

  // Agrupar citas por hora
  const appointmentsByTime = todayAppointments.reduce((acc, appointment) => {
    const time = formatLocalTimeFromUTC(appointment.startTime);
    if (!acc[time]) {
      acc[time] = [];
    }
    acc[time].push(appointment);
    return acc;
  }, {} as Record<string, any[]>);

  // Obtener citas para un slot específico
  const getAppointmentsForSlot = (time: string) => {
    return appointmentsByTime[time] || [];
  };

  // Verificar si un slot de tiempo ya pasó
  const isSlotPast = (time: string) => {
    // Solo bloquear slots si es el día de hoy
    if (!isToday(currentDate)) {
      return false;
    }
    
    const [hours, minutes] = time.split(':').map(Number);
    const slotDateTime = setMinutes(setHours(currentDate, hours), minutes);
    const now = new Date();
    
    // Agregar un buffer de 15 minutos - no permitir agendar en los próximos 15 minutos
    const bufferTime = addMinutes(now, 15);
    
    return isBefore(slotDateTime, bufferTime);
  };

  const handleSlotClick = (time: string) => {
    // No permitir click en slots pasados
    if (isSlotPast(time)) {
      return;
    }
    
    const [hours, minutes] = time.split(':').map(Number);
    const slotDateTime = setMinutes(setHours(currentDate, hours), minutes);
    onTimeSlotClick?.(slotDateTime, time);
  };

  // Estadísticas del día
  const dayStats = todayAppointments.reduce((acc, appointment) => {
    const status = appointment.status;
    acc[status] = (acc[status] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Clock className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Cargando agenda del día...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estadísticas del día */}
      {todayAppointments.length > 0 && (
        <div className="flex flex-wrap gap-2 justify-end">
          {Object.entries(dayStats).map(([status, count]) => {
            const config = statusConfig[status as keyof typeof statusConfig];
            return count > 0 ? (
              <Badge key={status} variant="outline" className="gap-1">
                <div className={cn("w-2 h-2 rounded-full", config?.color.split(' ')[0])} />
                {count} {config?.label}
              </Badge>
            ) : null;
          })}
        </div>
      )}

      {/* Calendario del día */}
      <Card className="overflow-hidden shadow-sm">
        <CardContent className="p-0">
          <div className="flex">
            {/* Columna lateral del médico - REMOVIDO PARA USAR HEADER CONSISTENTE */}
            {false && doctorInfo && (
              <div className="w-32 bg-gray-50 border-r flex-shrink-0">
                <div className="p-3 border-b">
                  <div className="flex flex-col items-center text-center space-y-2">
                    {/* Foto del médico */}
                    <Avatar className="h-12 w-12">
                      <AvatarImage src={doctorInfo.clerkImageUrl} />
                      <AvatarFallback className="bg-gradient-to-br from-blue-500 to-blue-600 text-white font-semibold text-sm">
                        {doctorInfo.firstName.charAt(0)}{doctorInfo.lastName.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                    
                    {/* Información del médico */}
                    <div>
                      <h3 className="font-semibold text-gray-900 text-sm">
                        {doctorInfo.firstName}
                      </h3>
                      <p className="text-xs text-blue-600 font-medium">
                        {doctorInfo.specialty || 'Medicina General'}
                      </p>
                    </div>
                  </div>
                </div>
                
                {/* Stats del día del médico */}
                <div className="p-3 space-y-1">
                  <div className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    Hoy
                  </div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-gray-600">Citas</span>
                      <span className="font-medium">{todayAppointments.length}</span>
                    </div>
                    {Object.entries(dayStats).map(([status, count]) => {
                      const config = statusConfig[status as keyof typeof statusConfig];
                      return count > 0 ? (
                        <div key={status} className="flex justify-between text-xs">
                          <span className="text-gray-600">{config?.label}</span>
                          <span className="font-medium">{count}</span>
                        </div>
                      ) : null;
                    })}
                  </div>
                </div>
              </div>
            )}

            {/* Área principal del calendario */}
            <div className="flex-1">
              <div className="max-h-[700px] overflow-y-auto">
            {timeSlots.map((time) => {
              const slotAppointments = getAppointmentsForSlot(time);
              const hasAppointments = slotAppointments.length > 0;
              const isHovered = hoveredSlot === time;
              const isPast = isSlotPast(time);
              
              return (
                <div
                  key={time}
                  className={cn(
                    "border-b flex transition-all duration-200",
                    hasAppointments ? "bg-white" : isPast ? "bg-gray-100" : "hover:bg-blue-50",
                    isHovered && !hasAppointments && !isPast && "bg-blue-100",
                    isPast && "opacity-60"
                  )}
                >
                  {/* Columna de hora */}
                  <div className="w-20 p-4 border-r bg-gray-50 flex-shrink-0">
                    <div className="text-sm font-medium text-gray-600 text-center">
                      {time}
                    </div>
                  </div>
                  
                  {/* Columna de contenido */}
                  <div 
                    className={cn(
                      "flex-1 min-h-[80px] p-4 relative",
                      !hasAppointments && !isPast ? "cursor-pointer" : "cursor-not-allowed"
                    )}
                    onClick={() => !hasAppointments && !isPast && handleSlotClick(time)}
                    onMouseEnter={() => !hasAppointments && !isPast && setHoveredSlot(time)}
                    onMouseLeave={() => setHoveredSlot(null)}
                  >
                    {/* Slot vacío */}
                    {!hasAppointments && (
                      <div className="h-full flex items-center justify-center">
                        {isPast ? (
                          <span className="text-gray-400 text-sm flex items-center gap-2">
                            <Clock className="h-4 w-4" />
                            Horario pasado
                          </span>
                        ) : isHovered ? (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="bg-blue-500 hover:bg-blue-600 text-white"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Crear cita
                          </Button>
                        ) : (
                          <span className="text-gray-400 text-sm">Horario disponible</span>
                        )}
                      </div>
                    )}
                    
                    {/* Citas en este slot */}
                    {slotAppointments.map((appointment, index) => {
                      const config = statusConfig[appointment.status as keyof typeof statusConfig];
                      const StatusIcon = config?.icon || Calendar;
                      
                      return (
                        <div
                          key={appointment.id}
                          className={cn(
                            "group p-4 rounded-lg border-l-4 cursor-pointer hover:shadow-md transition-all relative",
                            config?.color || statusConfig.scheduled.color,
                            index > 0 && "mt-3"
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            onAppointmentClick?.(appointment);
                          }}
                        >
                          <div className="space-y-3">
                            {/* Header de la cita */}
                            <div className="flex items-start justify-between">
                              <div className="flex items-center gap-2 flex-1">
                                <StatusIcon className="h-4 w-4" />
                                <div className="flex flex-col">
                                  <span className="font-semibold">
                                    {appointment.patientFirstName && appointment.patientLastName 
                                      ? `${appointment.patientFirstName} ${appointment.patientLastName}`
                                      : appointment.activityTypeName || appointment.title
                                    }
                                  </span>
                                  <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <span className="font-medium">
                                      {formatLocalTimeFromUTC(appointment.startTime)} -
                                      {formatLocalTimeFromUTC(appointment.endTime)}
                                    </span>
                                    <span className="text-xs opacity-75">
                                      ({appointment.duration} min)
                                    </span>
                                    <Badge 
                                      variant="secondary" 
                                      className={cn(
                                        "text-xs px-2 py-0.5 h-5",
                                        appointment.status === 'scheduled' && "bg-blue-100 text-blue-800 border-blue-200",
                                        appointment.status === 'pending_confirmation' && "bg-orange-100 text-orange-800 border-orange-200",
                                        appointment.status === 'confirmed' && "bg-[#50bed2]/10 text-[#50bed2] border-[#50bed2]/20",
                                        appointment.status === 'checked_in' && "bg-teal-100 text-teal-800 border-teal-200",
                                        appointment.status === 'in_progress' && "bg-purple-100 text-purple-800 border-purple-200",
                                        appointment.status === 'completed' && "bg-gray-100 text-gray-700 border-gray-200",
                                        appointment.status === 'cancelled' && "bg-red-100 text-red-700 border-red-200",
                                        appointment.status === 'no_show' && "bg-pink-100 text-pink-800 border-pink-200"
                                      )}
                                    >
                                      {config?.label || 'Programada'}
                                    </Badge>
                                  </div>
                                </div>
                                {appointment.isEmergency && (
                                  <Badge variant="destructive" className="text-xs ml-2">
                                    Urgente
                                  </Badge>
                                )}
                              </div>
                              
                              <AppointmentActionsMenu
                                appointment={appointment}
                                onView={onAppointmentClick}
                                onEdit={onAppointmentEdit}
                                onConfirm={onAppointmentConfirm}
                                onCancel={onAppointmentCancel}
                                onDelete={onAppointmentDelete}
                                onCheckIn={onAppointmentCheckIn}
                                onNoShow={onAppointmentNoShow}
                                onStart={onAppointmentStart}
                                onComplete={onAppointmentComplete}
                                onRevertNoShow={onAppointmentRevertNoShow}
                                onRevertCompleted={onAppointmentRevertCompleted}
                                onViewPreCheckin={onViewPreCheckin}
                                onViewConsultation={onViewConsultation}
                                userRole={userRole}
                                className="ml-2 flex-shrink-0"
                              />
                            </div>
                            
                            {/* Información médica */}
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <Stethoscope className="h-3 w-3" />
                                  <span>Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</span>
                                </div>
                                <div className="flex items-center gap-2">
                                  <Home className="h-3 w-3" />
                                  <span>{appointment.consultoryName}</span>
                                </div>
                              </div>
                              
                              <div className="space-y-2">
                                <div className="flex items-center gap-2">
                                  <User className="h-3 w-3" />
                                  <span>{appointment.serviceName || appointment.title}</span>
                                </div>
                                {appointment.estimatedPrice && (
                                  <div className="flex items-center gap-2">
                                    <DollarSign className="h-3 w-3" />
                                    <span>{appointment.currency} {appointment.estimatedPrice}</span>
                                  </div>
                                )}
                              </div>
                            </div>
                            
                            {/* Motivo de consulta */}
                            {appointment.chiefComplaint && (
                              <div className="bg-white/50 p-2 rounded text-sm">
                                <span className="font-medium">Motivo: </span>
                                {appointment.chiefComplaint}
                              </div>
                            )}
                            
                            {/* Notas */}
                            {appointment.description && (
                              <div className="bg-white/50 p-2 rounded text-sm">
                                <span className="font-medium">Notas: </span>
                                {appointment.description}
                              </div>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              );
            })}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}