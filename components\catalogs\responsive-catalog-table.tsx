'use client';

import React from 'react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card, CardContent } from '@/components/ui/card';
import { Checkbox } from '@/components/ui/checkbox';
import { RefreshCw } from 'lucide-react';

interface ResponsiveCatalogTableProps<T> {
  data: T[];
  loading: boolean;
  emptyIcon: React.ReactNode;
  emptyMessage: string;
  columns: {
    key: string;
    header: string;
    className?: string;
    render: (item: T) => React.ReactNode;
  }[];
  mobileCard: (item: T) => React.ReactNode;
  selectedItems?: string[];
  onSelectItem?: (id: string) => void;
  getItemId: (item: T) => string;
}

export function ResponsiveCatalogTable<T>({
  data,
  loading,
  emptyIcon,
  emptyMessage,
  columns,
  mobileCard,
  selectedItems = [],
  onSelectItem,
  getItemId,
}: ResponsiveCatalogTableProps<T>) {
  // Desktop Table View
  const desktopView = (
    <div className="hidden lg:block overflow-x-auto">
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {onSelectItem && (
                <TableHead className="w-12">
                  <Checkbox />
                </TableHead>
              )}
              {columns.map((column) => (
                <TableHead key={column.key} className={column.className}>
                  {column.header}
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              <TableRow>
                <TableCell colSpan={columns.length + (onSelectItem ? 1 : 0)} className="text-center py-8">
                  <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                  <p className="mt-2 text-gray-600">Cargando...</p>
                </TableCell>
              </TableRow>
            ) : data.length === 0 ? (
              <TableRow>
                <TableCell colSpan={columns.length + (onSelectItem ? 1 : 0)} className="text-center py-8">
                  {emptyIcon}
                  <p className="mt-2 text-gray-600">{emptyMessage}</p>
                </TableCell>
              </TableRow>
            ) : (
              data.map((item) => (
                <TableRow key={getItemId(item)}>
                  {onSelectItem && (
                    <TableCell>
                      <Checkbox 
                        checked={selectedItems.includes(getItemId(item))}
                        onCheckedChange={() => onSelectItem(getItemId(item))}
                      />
                    </TableCell>
                  )}
                  {columns.map((column) => (
                    <TableCell key={column.key} className={column.className}>
                      {column.render(item)}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );

  // Mobile Cards View
  const mobileView = (
    <div className="lg:hidden space-y-4">
      {loading ? (
        <div className="text-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
          <p className="mt-2 text-gray-600">Cargando...</p>
        </div>
      ) : data.length === 0 ? (
        <div className="text-center py-8">
          {emptyIcon}
          <p className="mt-2 text-gray-600">{emptyMessage}</p>
        </div>
      ) : (
        data.map((item) => (
          <Card key={getItemId(item)} className="hover:shadow-md transition-all duration-200">
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-3 flex-1">
                  {onSelectItem && (
                    <Checkbox 
                      className="mt-1"
                      checked={selectedItems.includes(getItemId(item))}
                      onCheckedChange={() => onSelectItem(getItemId(item))}
                    />
                  )}
                  <div className="flex-1">
                    {mobileCard(item)}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))
      )}
    </div>
  );

  return (
    <>
      {desktopView}
      {mobileView}
    </>
  );
}