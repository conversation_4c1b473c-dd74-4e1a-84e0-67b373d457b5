import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';

async function checkDoctorsData() {
  try {
    console.log('🔍 Verificando datos de médicos...');
    
    // Verificar si existe la tabla userRoles
    const userRolesExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'user_roles'
      );
    `);
    
    console.log('📋 Tabla user_roles existe:', userRolesExists.rows[0]?.exists ? 'SÍ ✅' : 'NO ❌');
    
    if (userRolesExists.rows[0]?.exists) {
      // Verificar usuarios con rol de doctor
      const doctorsCount = await db.execute(sql`
        SELECT COUNT(*) as total 
        FROM user_roles 
        WHERE role = 'doctor';
      `);
      
      console.log(`👨‍⚕️ Médicos registrados: ${doctorsCount.rows[0]?.total || 0}`);
      
      // Verificar usuarios activos con rol de doctor
      const activeDoctorsCount = await db.execute(sql`
        SELECT COUNT(*) as total 
        FROM user_roles 
        WHERE role = 'doctor' AND "isActive" = true;
      `);
      
      console.log(`✅ Médicos activos: ${activeDoctorsCount.rows[0]?.total || 0}`);
      
      // Verificar si hay datos en la tabla user
      const usersCount = await db.execute(sql`
        SELECT COUNT(*) as total FROM "user";
      `);
      
      console.log(`👤 Usuarios totales: ${usersCount.rows[0]?.total || 0}`);
      
      // Mostrar algunos médicos de ejemplo
      const sampleDoctors = await db.execute(sql`
        SELECT ur.role, ur."isActive", u."firstName", u."lastName", u.email
        FROM user_roles ur
        INNER JOIN "user" u ON ur."userId" = u.id
        WHERE ur.role = 'doctor'
        LIMIT 5;
      `);
      
      console.log('📝 Médicos de ejemplo:');
      sampleDoctors.rows.forEach((doctor: any, index: number) => {
        console.log(`  ${index + 1}. ${doctor.firstName} ${doctor.lastName} (${doctor.email}) - ${doctor.isActive ? 'Activo' : 'Inactivo'}`);
      });
      
    } else {
      console.log('❌ La tabla user_roles no existe. Verificando tabla user...');
      
      const userExists = await db.execute(sql`
        SELECT EXISTS (
          SELECT FROM information_schema.tables 
          WHERE table_schema = 'public' 
          AND table_name = 'user'
        );
      `);
      
      console.log('📋 Tabla user existe:', userExists.rows[0]?.exists ? 'SÍ ✅' : 'NO ❌');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  checkDoctorsData()
    .then(() => {
      console.log('🎉 Verificación completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { checkDoctorsData };