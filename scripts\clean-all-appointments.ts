#!/usr/bin/env npx tsx

import { db } from '@/db/drizzle';
import { 
  appointments, 
  medicalConsultations, 
  medicalRecords, 
  patientInvitations,
  appointmentAuditLogs,
  guardianPatientRelations 
} from '@/db/schema';
import { sql } from 'drizzle-orm';

/**
 * Script para limpiar completamente todas las citas y datos relacionados
 * ⚠️ PELIGRO: Este script borra TODOS los datos de citas de la base de datos
 */

async function cleanAllAppointments() {
  console.log('🚨 ADVERTENCIA: Este script borrará TODAS las citas y datos relacionados');
  console.log('📋 Tablas que serán limpiadas:');
  console.log('   - appointments (citas médicas)');
  console.log('   - medical_consultations (consultas médicas)');
  console.log('   - medical_records (expedientes médicos)');
  console.log('   - patient_invitations (invitaciones de pacientes)');
  console.log('   - appointment_audit_logs (logs de auditoría)');
  console.log('   - guardian_patient_relations (relaciones tutor-paciente)');
  console.log('');

  try {
    console.log('🧹 Iniciando limpieza de base de datos...');

    // Verificar cantidad de registros antes de borrar
    console.log('📊 Contando registros actuales...');
    
    const counts = await Promise.all([
      db.select({ count: sql<number>`count(*)` }).from(appointments),
      db.select({ count: sql<number>`count(*)` }).from(medicalConsultations),
      db.select({ count: sql<number>`count(*)` }).from(medicalRecords),
      db.select({ count: sql<number>`count(*)` }).from(patientInvitations),
      db.select({ count: sql<number>`count(*)` }).from(guardianPatientRelations)
    ]);

    console.log('📋 Registros encontrados:');
    console.log(`   - Citas: ${counts[0][0].count}`);
    console.log(`   - Consultas médicas: ${counts[1][0].count}`);
    console.log(`   - Expedientes médicos: ${counts[2][0].count}`);
    console.log(`   - Invitaciones de pacientes: ${counts[3][0].count}`);
    console.log(`   - Relaciones tutor-paciente: ${counts[4][0].count}`);
    console.log('');

    const totalRecords = counts.reduce((sum, count) => sum + count[0].count, 0);
    
    if (totalRecords === 0) {
      console.log('✅ No hay registros para borrar. Base de datos ya limpia.');
      return;
    }

    console.log(`🗑️ Se borrarán ${totalRecords} registros en total`);
    console.log('');

    // PASO 1: Borrar en orden correcto para evitar problemas de foreign keys
    console.log('1️⃣ Borrando logs de auditoría de citas...');
    try {
      const auditResult = await db.delete(appointmentAuditLogs);
      console.log(`   ✅ Logs de auditoría borrados`);
    } catch (error) {
      console.log(`   ⚠️ Error borrando logs (tabla puede no existir): ${error}`);
    }

    console.log('2️⃣ Borrando consultas médicas...');
    const consultationsResult = await db.delete(medicalConsultations);
    console.log(`   ✅ Consultas médicas borradas`);

    console.log('3️⃣ Borrando expedientes médicos...');
    const recordsResult = await db.delete(medicalRecords);
    console.log(`   ✅ Expedientes médicos borrados`);

    console.log('4️⃣ Borrando citas médicas...');
    const appointmentsResult = await db.delete(appointments);
    console.log(`   ✅ Citas médicas borradas`);

    console.log('5️⃣ Borrando invitaciones de pacientes...');
    const invitationsResult = await db.delete(patientInvitations);
    console.log(`   ✅ Invitaciones de pacientes borradas`);

    console.log('6️⃣ Borrando relaciones tutor-paciente...');
    const relationsResult = await db.delete(guardianPatientRelations);
    console.log(`   ✅ Relaciones tutor-paciente borradas`);

    console.log('');
    console.log('🎉 ¡LIMPIEZA COMPLETADA EXITOSAMENTE!');
    console.log('');
    console.log('📋 Resumen de registros borrados:');
    console.log(`   - Citas médicas: ${counts[0][0].count}`);
    console.log(`   - Consultas médicas: ${counts[1][0].count}`);
    console.log(`   - Expedientes médicos: ${counts[2][0].count}`);
    console.log(`   - Invitaciones: ${counts[3][0].count}`);
    console.log(`   - Relaciones tutor-paciente: ${counts[4][0].count}`);
    console.log('');
    console.log('✅ La base de datos está ahora limpia y lista para nuevos datos');
    console.log('💡 Puedes empezar a crear citas desde cero');
    console.log('');

    // Verificar que todo se borró correctamente
    console.log('🔍 Verificando limpieza...');
    const finalCounts = await Promise.all([
      db.select({ count: sql<number>`count(*)` }).from(appointments),
      db.select({ count: sql<number>`count(*)` }).from(medicalConsultations),
      db.select({ count: sql<number>`count(*)` }).from(medicalRecords),
      db.select({ count: sql<number>`count(*)` }).from(patientInvitations),
      db.select({ count: sql<number>`count(*)` }).from(guardianPatientRelations)
    ]);

    const remainingRecords = finalCounts.reduce((sum, count) => sum + count[0].count, 0);
    
    if (remainingRecords === 0) {
      console.log('✅ VERIFICACIÓN EXITOSA: Todos los registros fueron borrados');
    } else {
      console.log(`⚠️ ADVERTENCIA: Quedan ${remainingRecords} registros sin borrar`);
      console.log('📋 Registros restantes:');
      console.log(`   - Citas: ${finalCounts[0][0].count}`);
      console.log(`   - Consultas médicas: ${finalCounts[1][0].count}`);
      console.log(`   - Expedientes médicos: ${finalCounts[2][0].count}`);
      console.log(`   - Invitaciones: ${finalCounts[3][0].count}`);
      console.log(`   - Relaciones tutor-paciente: ${finalCounts[4][0].count}`);
    }

  } catch (error) {
    console.error('❌ ERROR durante la limpieza:', error);
    console.log('');
    console.log('🔧 Posibles soluciones:');
    console.log('1. Verificar que la base de datos esté disponible');
    console.log('2. Verificar permisos de base de datos');
    console.log('3. Revisar foreign key constraints');
    console.log('4. Ejecutar migraciones pendientes');
    
    throw error;
  }
}

// Ejecutar si se llama directamente
if (require.main === module) {
  cleanAllAppointments()
    .then(() => {
      console.log('🏁 Script completado exitosamente');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script falló:', error);
      process.exit(1);
    });
}

export { cleanAllAppointments };