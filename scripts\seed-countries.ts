import { db } from '../db/drizzle';
import { countries } from '../db/schema';

async function seedCountries() {
  console.log('🌍 Seeding countries...');
  
  const countriesData = [
    {
      id: 1,
      name: 'Guatemala',
      code: 'GT',
      phoneCode: '+502',
      currency: 'GTQ',
      isActive: true
    },
    {
      id: 2,
      name: 'Estados Unidos',
      code: 'US',
      phoneCode: '+1',
      currency: 'USD',
      isActive: true
    },
    {
      id: 3,
      name: 'México',
      code: 'MX',
      phoneCode: '+52',
      currency: 'MXN',
      isActive: true
    },
    {
      id: 4,
      name: 'El Salvador',
      code: 'SV',
      phoneCode: '+503',
      currency: 'USD',
      isActive: true
    },
    {
      id: 5,
      name: 'Honduras',
      code: 'HN',
      phoneCode: '+504',
      currency: 'HNL',
      isActive: true
    },
    {
      id: 6,
      name: 'Costa Rica',
      code: 'CR',
      phoneCode: '+506',
      currency: 'CRC',
      isActive: true
    },
    {
      id: 7,
      name: 'Nicaragua',
      code: 'NI',
      phoneCode: '+505',
      currency: 'NIO',
      isActive: true
    },
    {
      id: 8,
      name: 'Panamá',
      code: 'PA',
      phoneCode: '+507',
      currency: 'PAB',
      isActive: true
    },
    {
      id: 9,
      name: 'Belice',
      code: 'BZ',
      phoneCode: '+501',
      currency: 'BZD',
      isActive: true
    },
    {
      id: 10,
      name: 'España',
      code: 'ES',
      phoneCode: '+34',
      currency: 'EUR',
      isActive: true
    }
  ];

  try {
    // Clear existing data
    await db.delete(countries);
    console.log('Cleared existing countries');

    // Insert new data
    for (const country of countriesData) {
      await db.insert(countries).values(country).onConflictDoNothing();
      console.log(`✅ Inserted country: ${country.name}`);
    }

    console.log(`🎉 Successfully seeded ${countriesData.length} countries`);
  } catch (error) {
    console.error('❌ Error seeding countries:', error);
    throw error;
  }
}

// Run if called directly
if (require.main === module) {
  seedCountries()
    .then(() => {
      console.log('✅ Countries seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Countries seeding failed:', error);
      process.exit(1);
    });
}

export { seedCountries };