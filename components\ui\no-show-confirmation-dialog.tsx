'use client';

import { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertCircle, User, Clock, Calendar } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface NoShowConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  appointment: any;
  onConfirm: () => Promise<void>;
  isProcessing?: boolean;
}

export function NoShowConfirmationDialog({
  open,
  onOpenChange,
  appointment,
  onConfirm,
  isProcessing = false
}: NoShowConfirmationDialogProps) {
  const [marking, setMarking] = useState(false);

  const handleConfirm = async () => {
    setMarking(true);
    try {
      await onConfirm();
      onOpenChange(false);
    } catch (error) {
      // Error handled by parent
    } finally {
      setMarking(false);
    }
  };

  const isLoading = marking || isProcessing;

  return (
    <Dialog open={open} onOpenChange={!isLoading ? onOpenChange : undefined}>
      <DialogContent className="sm:max-w-[450px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <AlertCircle className="h-5 w-5 text-orange-600" />
            Marcar como No Show
          </DialogTitle>
          <DialogDescription className="text-base">
            ¿Estás seguro de que quieres marcar esta cita como "No Show" (No se presentó)?
          </DialogDescription>
        </DialogHeader>
        
        {appointment && (
          <div className="bg-gray-50 rounded-lg p-4 space-y-3">
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium text-gray-700">Paciente:</span>
                <span className="text-gray-900">
                  {appointment.patientName || appointment.title || 'Sin nombre'}
                </span>
              </div>
              
              {appointment.startTime && (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-700">Hora programada:</span>
                  <span className="text-gray-900">
                    {format(new Date(appointment.startTime), 'HH:mm', { locale: es })}
                  </span>
                </div>
              )}
              
              {appointment.scheduledDate && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span className="font-medium text-gray-700">Fecha:</span>
                  <span className="text-gray-900">
                    {format(new Date(appointment.scheduledDate), 'dd/MM/yyyy', { locale: es })}
                  </span>
                </div>
              )}
            </div>
            
            <div className="mt-3 p-3 bg-orange-50 rounded border border-orange-200">
              <div className="flex items-center gap-2">
                <AlertCircle className="h-4 w-4 text-orange-600" />
                <span className="text-sm font-medium text-orange-800">
                  La cita se marcará como "No se presentó"
                </span>
              </div>
              <p className="text-xs text-orange-700 mt-1">
                Esto indica que el paciente no llegó a su cita programada. Podrás revertir esta acción si fue un error.
              </p>
            </div>
          </div>
        )}

        <DialogFooter className="gap-2">
          <Button
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isLoading}
            className="bg-orange-600 hover:bg-orange-700 text-white"
          >
            {marking ? (
              <>
                <AlertCircle className="mr-2 h-4 w-4 animate-spin" />
                Marcando...
              </>
            ) : (
              <>
                <AlertCircle className="mr-2 h-4 w-4" />
                Marcar No Show
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}