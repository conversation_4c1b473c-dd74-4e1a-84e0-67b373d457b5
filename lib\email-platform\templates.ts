/**
 * TEMPLATES DE EMAIL CENTRALIZADOS
 * Todos los templates del sistema en un solo lugar
 */

import { EmailTemplate } from './types';

export const emailTemplates: Record<string, EmailTemplate> = {
  // ================================
  // TEMPLATES DE PACIENTES
  // ================================
  
  patient_created: {
    subject: (params) => `Bienvenido ${params.patientName} - Cuenta Médica Creada`,
    requiredParams: ['patientName', 'activationLink'],
    tags: ['patient', 'activation', 'welcome'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Cuenta Médica Creada</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
          .footer { text-align: center; margin-top: 30px; color: #666; font-size: 14px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>🎉 ¡Bienvenido ${params.patientName}!</h1>
          </div>
          <div class="content">
            <h2>Tu cuenta médica ha sido creada exitosamente</h2>
            
            ${params.isDependent ? `
              <p>Se ha creado una cuenta médica para <strong>${params.patientName}</strong>.</p>
              <p>Como ${params.relationship || 'responsable'}, puedes activar y gestionar esta cuenta.</p>
            ` : `
              <p>Tu cuenta médica personal ha sido creada y está lista para usar.</p>
            `}
            
            <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3>📋 Próximos pasos:</h3>
              <ol>
                <li>Activa tu cuenta haciendo clic en el botón de abajo</li>
                <li>Completa tu perfil médico</li>
                <li>Agenda tu primera cita</li>
              </ol>
            </div>
            
            <div style="text-align: center;">
              <a href="${params.activationLink}" class="button">
                🔐 Activar Cuenta
              </a>
            </div>
            
            ${params.hasAppointment ? `
              <div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <p><strong>📅 Tienes una cita programada para ${params.appointmentDate}</strong></p>
                <p>Una vez actives tu cuenta, podrás ver todos los detalles.</p>
              </div>
            ` : ''}
            
            <div class="footer">
              <p>Si tienes preguntas, no dudes en contactarnos.</p>
              <p><strong>Sistema Médico</strong></p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  },

  patient_onboarding_completed: {
    subject: (params) => `¡Bienvenido ${params.userName}! Tu registro ha sido completado`,
    requiredParams: ['userName', 'userRole', 'welcomeMessage'],
    tags: ['patient', 'onboarding', 'welcome'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Registro Completado</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #10b981; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .success-badge { background: #dcfce7; color: #166534; padding: 10px; border-radius: 6px; text-align: center; margin: 20px 0; }
          .next-steps { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ ¡Registro Completado!</h1>
          </div>
          <div class="content">
            <div class="success-badge">
              <h2>🎉 ¡Bienvenido ${params.userName}!</h2>
              <p>Tu cuenta como <strong>${params.userRole}</strong> está lista</p>
            </div>
            
            <p>${params.welcomeMessage}</p>
            
            <div class="next-steps">
              <h3>📝 Próximos pasos:</h3>
              <ul>
                ${params.nextSteps.map(step => `<li>${step}</li>`).join('')}
              </ul>
            </div>
            
            <p>Puedes acceder a tu cuenta en cualquier momento desde nuestra plataforma.</p>
            
            <div style="text-align: center; margin-top: 30px;">
              <p>¡Esperamos verte pronto!</p>
              <p><strong>Equipo Médico</strong></p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  },

  // ================================
  // TEMPLATES DE CITAS
  // ================================
  
  appointment_created: {
    subject: (params) => `Cita Confirmada - ${params.appointmentDate} con ${params.doctorName}`,
    requiredParams: ['patientName', 'doctorName', 'appointmentDate', 'appointmentTime', 'confirmationCode'],
    tags: ['appointment', 'confirmation', 'patient'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Confirmación de Cita Médica</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .appointment-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #059669; }
          .confirmation-code { background: #fbbf24; color: #92400e; padding: 15px; border-radius: 6px; text-align: center; font-size: 18px; font-weight: bold; margin: 20px 0; }
          .button { background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Cita Confirmada</h1>
          </div>
          <div class="content">
            <h2>¡Hola ${params.patientName}!</h2>
            <p>Tu cita médica ha sido <strong>confirmada exitosamente</strong>. Aquí tienes todos los detalles:</p>
            
            <div class="appointment-card">
              <h3>📅 Detalles de tu cita</h3>
              <p><strong>Tipo:</strong> ${params.appointmentType}</p>
              <p><strong>Doctor:</strong> ${params.doctorName}</p>
              <p><strong>Fecha:</strong> ${params.appointmentDate}</p>
              <p><strong>Hora:</strong> ${params.appointmentTime}</p>
              <p><strong>Lugar:</strong> ${params.consultoryName}</p>
            </div>

            <div class="confirmation-code">
              <p>Tu código de confirmación:</p>
              <div style="font-size: 24px; letter-spacing: 2px;">${params.confirmationCode}</div>
              <p style="font-size: 12px; margin-top: 10px;">Presenta este código al llegar</p>
            </div>

            <div style="text-align: center; margin: 20px 0;">
              ${params.preCheckinLink ? `
                <a href="${params.preCheckinLink}" class="button">
                  📋 Completar Pre-checkin
                </a>
              ` : ''}
              ${params.cancelLink ? `
                <a href="${params.cancelLink}" class="button" style="background: #dc2626;">
                  ❌ Cancelar Cita
                </a>
              ` : ''}
            </div>

            <h3>📝 Importante:</h3>
            <ul>
              <li>Llega <strong>15 minutos antes</strong> de tu hora</li>
              <li>Trae tu <strong>documento de identidad</strong></li>
              <li>Para cancelar, hazlo con <strong>24h de anticipación</strong></li>
              <li>Recibirás recordatorios antes de tu cita</li>
            </ul>
          </div>
        </div>
      </body>
      </html>
    `
  },

  appointment_cancelled: {
    subject: (params) => `Cita Cancelada - ${params.appointmentDate}`,
    requiredParams: ['patientName', 'doctorName', 'appointmentDate', 'appointmentTime', 'cancellationReason'],
    tags: ['appointment', 'cancellation', 'notification'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Cita Cancelada</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #dc2626; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #fef2f2; padding: 30px; border-radius: 0 0 8px 8px; }
          .info-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc2626; }
          .button { background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>❌ Cita Cancelada</h1>
          </div>
          <div class="content">
            <h2>Hola ${params.patientName},</h2>
            <p>Te confirmamos que tu cita médica ha sido <strong>cancelada exitosamente</strong>.</p>
            
            <div class="info-card">
              <h3>📅 Detalles de la cita cancelada</h3>
              <p><strong>Doctor:</strong> ${params.doctorName}</p>
              <p><strong>Fecha:</strong> ${params.appointmentDate}</p>
              <p><strong>Hora:</strong> ${params.appointmentTime}</p>
              <p><strong>Lugar:</strong> ${params.consultoryName}</p>
              <p><strong>Motivo de cancelación:</strong> ${params.cancellationReason}</p>
            </div>

            <h3>¿Necesitas agendar una nueva cita?</h3>
            <p>Si deseas programar una nueva cita, puedes:</p>
            <ul>
              <li>Llamarnos al teléfono de la clínica</li>
              <li>Usar nuestro sistema de citas en línea</li>
              <li>Visitar nuestras instalaciones</li>
            </ul>

            <p style="margin-top: 30px; color: #666; font-size: 14px;">
              Si no solicitaste esta cancelación, por favor contáctanos inmediatamente.
            </p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  appointment_reminder_24h: {
    subject: (params) => `Recordatorio: Cita mañana con ${params.doctorName}`,
    requiredParams: ['patientName', 'doctorName', 'appointmentDate', 'appointmentTime'],
    tags: ['appointment', 'reminder', '24h'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Recordatorio de Cita - 24 horas</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #f59e0b; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #fffbeb; padding: 30px; border-radius: 0 0 8px 8px; }
          .reminder-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #f59e0b; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>⏰ Recordatorio de Cita</h1>
          </div>
          <div class="content">
            <h2>¡Hola ${params.patientName}!</h2>
            <p>Este es un recordatorio de que tienes una cita médica <strong>mañana</strong>:</p>
            
            <div class="reminder-card">
              <h3>📅 Tu cita es mañana</h3>
              <p><strong>Doctor:</strong> ${params.doctorName}</p>
              <p><strong>Fecha:</strong> ${params.appointmentDate}</p>
              <p><strong>Hora:</strong> ${params.appointmentTime}</p>
              <p><strong>Lugar:</strong> ${params.consultoryName}</p>
            </div>

            <h3>✅ Preparativos:</h3>
            <ul>
              <li>Ten listo tu documento de identidad</li>
              <li>Llega 15 minutos antes</li>
              <li>Trae tus medicamentos actuales</li>
              <li>Lista de síntomas o preguntas</li>
            </ul>

            <p>¡Te esperamos mañana!</p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  // ================================
  // TEMPLATES DE PRE-CHECKIN
  // ================================
  
  precheckin_invitation: {
    subject: (params) => `Pre-registro para tu cita - ${params.appointmentDate}`,
    requiredParams: ['patientName', 'doctorName', 'appointmentDate', 'appointmentTime', 'preCheckinLink'],
    tags: ['appointment', 'precheckin', 'preparation'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Pre-registro de Cita</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #3b82f6; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #eff6ff; padding: 30px; border-radius: 0 0 8px 8px; }
          .info-card { background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #3b82f6; }
          .button { background: #3b82f6; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 20px 0; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>📋 Completa tu Pre-registro</h1>
          </div>
          <div class="content">
            <h2>¡Hola ${params.patientName}!</h2>
            
            ${params.isDependent ? `
              <p>Se acerca la cita médica de <strong>${params.patientName}</strong>.</p>
              <p>Como ${params.relationship || 'responsable'}, puedes completar el pre-registro para ahorrar tiempo el día de la cita.</p>
            ` : `
              <p>Para ahorrar tiempo el día de tu cita, te invitamos a completar tu pre-registro en línea.</p>
            `}
            
            <div class="info-card">
              <h3>📅 Información de la cita</h3>
              <p><strong>Doctor:</strong> ${params.doctorName}</p>
              <p><strong>Fecha:</strong> ${params.appointmentDate}</p>
              <p><strong>Hora:</strong> ${params.appointmentTime}</p>
              <p><strong>Lugar:</strong> ${params.consultoryName}</p>
            </div>

            <div style="text-align: center;">
              <a href="${params.preCheckinLink}" class="button">
                📝 Completar Pre-registro
              </a>
            </div>

            <h3>¿Por qué completar el pre-registro?</h3>
            <ul>
              <li>✅ Reduce el tiempo de espera en recepción</li>
              <li>✅ Actualiza tu información médica</li>
              <li>✅ Describe tus síntomas con anticipación</li>
              <li>✅ Sube documentos relevantes</li>
            </ul>

            <p><em>Este enlace es válido por 48 horas.</em></p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  // ================================
  // TEMPLATES DE CONSULTA
  // ================================
  
  consultation_completed: {
    subject: (params) => `Consulta completada - ${params.doctorName}`,
    requiredParams: ['patientName', 'doctorName', 'consultationDate', 'feedbackLink'],
    tags: ['consultation', 'completed', 'feedback'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Consulta Completada</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background: #059669; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
          .content { background: #f0fdf4; padding: 30px; border-radius: 0 0 8px 8px; }
          .button { background: #059669; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; margin: 10px 5px; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>✅ Consulta Completada</h1>
          </div>
          <div class="content">
            <h2>¡Gracias ${params.patientName}!</h2>
            <p>Tu consulta con <strong>${params.doctorName}</strong> del ${params.consultationDate} ha sido completada.</p>
            
            ${params.prescriptionAttachment ? `
              <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h3>💊 Receta Médica</h3>
                <p>Tu receta ha sido enviada y está disponible en tus documentos.</p>
              </div>
            ` : ''}
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${params.feedbackLink}" class="button">
                ⭐ Calificar Atención
              </a>
            </div>
            
            ${params.followUpDate ? `
              <div style="background: #e0f2fe; padding: 15px; border-radius: 6px; margin: 20px 0;">
                <h3>📅 Seguimiento</h3>
                <p>Tu próxima cita de seguimiento está programada para: <strong>${params.followUpDate}</strong></p>
              </div>
            ` : ''}
            
            <p>Esperamos haberte brindado la mejor atención. ¡Que te mejores pronto!</p>
          </div>
        </div>
      </body>
      </html>
    `
  },

  // ================================
  // TEMPLATES COMBINADOS
  // ================================
  
  appointment_combined_notification: {
    subject: (params) => params.isSameDay ? `Tu cita es HOY con ${params.doctorName}` : `Recordatorio: Cita con ${params.doctorName} - ${params.appointmentDate}`,
    requiredParams: ['patientName', 'doctorName', 'appointmentDate', 'appointmentTime', 'confirmationCode'],
    tags: ['appointment', 'combined', 'reminder', 'precheckin', 'cancel'],
    html: (params) => `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>${params.isSameDay ? 'Tu cita es HOY' : 'Recordatorio de Cita'}</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { 
            background: ${params.isSameDay ? '#dc2626' : '#059669'}; 
            color: white; 
            padding: 20px; 
            text-align: center; 
            border-radius: 8px 8px 0 0; 
          }
          .content { background: #f9f9f9; padding: 30px; border-radius: 0 0 8px 8px; }
          .appointment-card { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
            border-left: 4px solid ${params.isSameDay ? '#dc2626' : '#059669'}; 
          }
          .confirmation-code { 
            background: #fbbf24; 
            color: #92400e; 
            padding: 15px; 
            border-radius: 6px; 
            text-align: center; 
            font-size: 18px; 
            font-weight: bold; 
            margin: 20px 0; 
          }
          .button { 
            color: white; 
            padding: 12px 24px; 
            text-decoration: none; 
            border-radius: 6px; 
            display: inline-block; 
            margin: 10px 5px; 
            text-align: center;
          }
          .btn-primary { background: #059669; }
          .btn-blue { background: #3b82f6; }
          .btn-red { background: #dc2626; }
          .urgent-banner { 
            background: #fee2e2; 
            color: #991b1b; 
            padding: 15px; 
            border-radius: 6px; 
            text-align: center; 
            margin: 20px 0; 
            border: 1px solid #fca5a5;
          }
          .action-section { 
            background: white; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0; 
          }
          .button-container { 
            text-align: center; 
            margin: 20px 0; 
          }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>${params.isSameDay ? '🚨 Tu cita es HOY' : '⏰ Recordatorio de Cita'}</h1>
          </div>
          <div class="content">
            <h2>¡Hola ${params.patientName}!</h2>
            
            ${params.isSameDay ? `
              <div class="urgent-banner">
                <h3>⚠️ ¡Tu cita es HOY!</h3>
                <p>No olvides llegar 15 minutos antes de tu hora programada</p>
              </div>
            ` : `
              <p>Este es un recordatorio de tu próxima cita médica:</p>
            `}
            
            ${params.isDependent ? `
              <p><em>Como ${params.relationship || 'responsable'} de ${params.patientName}, te enviamos este recordatorio.</em></p>
            ` : ''}
            
            <div class="appointment-card">
              <h3>📅 Detalles de la cita</h3>
              <p><strong>Paciente:</strong> ${params.patientName}</p>
              <p><strong>Doctor:</strong> ${params.doctorName}</p>
              <p><strong>Fecha:</strong> ${params.appointmentDate}</p>
              <p><strong>Hora:</strong> ${params.appointmentTime}</p>
              <p><strong>Lugar:</strong> ${params.consultoryName}</p>
              ${params.chiefComplaint ? `<p><strong>Motivo:</strong> ${params.chiefComplaint}</p>` : ''}
            </div>

            <div class="confirmation-code">
              <p>Código de confirmación:</p>
              <div style="font-size: 24px; letter-spacing: 2px;">${params.confirmationCode}</div>
              <p style="font-size: 12px; margin-top: 10px;">Presenta este código al llegar</p>
            </div>

            <!-- SECCIÓN DE ACCIONES -->
            <div class="action-section">
              <h3>🎯 Acciones disponibles</h3>
              
              <div class="button-container">
                ${params.preCheckinLink && !params.preCheckinCompleted ? `
                  <a href="${params.preCheckinLink}" class="button btn-blue">
                    📋 Completar Pre-registro
                  </a>
                ` : params.preCheckinCompleted ? `
                  <div style="background: #dcfce7; color: #166534; padding: 10px; border-radius: 6px; margin: 10px 0;">
                    ✅ Pre-registro completado
                  </div>
                ` : ''}
                
                ${params.cancelLink ? `
                  <a href="${params.cancelLink}" class="button btn-red">
                    ❌ Cancelar Cita
                  </a>
                ` : ''}
              </div>

              ${!params.preCheckinCompleted ? `
                <div style="background: #eff6ff; padding: 15px; border-radius: 6px; margin: 15px 0;">
                  <h4>¿Por qué completar el pre-registro?</h4>
                  <ul style="margin: 10px 0; padding-left: 20px;">
                    <li>✅ Reduce el tiempo de espera</li>
                    <li>✅ Actualiza tu información médica</li>
                    <li>✅ Describe síntomas con anticipación</li>
                    <li>✅ Sube documentos importantes</li>
                  </ul>
                </div>
              ` : ''}
            </div>

            <!-- PREPARATIVOS -->
            <div class="action-section">
              <h3>${params.isSameDay ? '🎒 ¡Prepárate para salir!' : '📝 Preparativos para tu cita'}</h3>
              <ul>
                <li>${params.isSameDay ? '🕐' : '⏰'} Llega <strong>15 minutos antes</strong> de tu hora</li>
                <li>🆔 Trae tu <strong>documento de identidad</strong></li>
                <li>💊 Lista de <strong>medicamentos actuales</strong></li>
                <li>📋 Preguntas o síntomas que quieras consultar</li>
                ${params.isSameDay ? '<li>📱 <strong>No olvides tu código:</strong> ' + params.confirmationCode + '</li>' : ''}
              </ul>
            </div>

            <!-- POLÍTICAS DE CANCELACIÓN -->
            <div style="background: #fef3c7; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <h4>⚠️ Política de cancelación</h4>
              <p>Para cancelar sin penalización, hazlo con <strong>24 horas de anticipación</strong>. 
              Esto nos permite ofrecer el horario a otros pacientes.</p>
            </div>

            <div style="text-align: center; margin-top: 30px; color: #666;">
              <p>Si tienes dudas, contáctanos.</p>
              <p><strong>¡Te esperamos!</strong></p>
            </div>
          </div>
        </div>
      </body>
      </html>
    `
  }
};