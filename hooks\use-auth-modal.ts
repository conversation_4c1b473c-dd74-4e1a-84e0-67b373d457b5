import { create } from 'zustand';

interface AuthModalStore {
  isSignInOpen: boolean;
  isSignUpOpen: boolean;
  openSignIn: () => void;
  openSignUp: () => void;
  closeAll: () => void;
  switchToSignUp: () => void;
  switchToSignIn: () => void;
}

export const useAuthModal = create<AuthModalStore>((set) => ({
  isSignInOpen: false,
  isSignUpOpen: false,
  openSignIn: () => set({ isSignInOpen: true, isSignUpOpen: false }),
  openSignUp: () => set({ isSignUpOpen: true, isSignInOpen: false }),
  closeAll: () => set({ isSignInOpen: false, isSignUpOpen: false }),
  switchToSignUp: () => set({ isSignUpOpen: true, isSignInOpen: false }),
  switchToSignIn: () => set({ isSignInOpen: true, isSignUpOpen: false }),
})); 