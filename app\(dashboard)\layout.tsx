'use client';

import { useUser } from '@clerk/nextjs';
import { usePathname } from 'next/navigation';
import { Sidebar } from '@/components/dashboard/sidebar';
import { Header } from '@/components/dashboard/header';
import { SidebarProvider, useSidebar } from '@/components/dashboard/sidebar-context';

function DashboardContent({ children, extractedRole }: { children: React.ReactNode, extractedRole: string }) {
  const { isCollapsed, isMobileOpen } = useSidebar();
  const isPatient = extractedRole === 'patient';
  
  if (isPatient) {
    // Layout especial para pacientes - sin sidebar pero con header para logout
    return (
      <div className="h-screen flex flex-col bg-gray-50">
        {/* Header solo para logout */}
        <Header />
        
        {/* Contenido principal sin padding del layout tradicional */}
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen flex bg-gray-50 md:bg-gray-100">
      {/* Sidebar */}
      <Sidebar role={extractedRole} />
      
      {/* Main content */}
      <div className="flex-1 flex flex-col transition-all duration-300">
        {/* Header */}
        <Header />
        
        {/* Page content */}
        <main className="flex-1 bg-gray-50 md:bg-gray-100">
          <div className="p-4 md:p-6 lg:p-8">
            <div className="max-w-[1600px] mx-auto">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user } = useUser();
  const pathname = usePathname();

  // Extract role from URL path
  const pathSegments = pathname.split('/');
  const extractedRole = pathSegments[2]; // /dashboard/[role]


  return (
    <SidebarProvider>
      <DashboardContent extractedRole={extractedRole}>
        {children}
      </DashboardContent>
    </SidebarProvider>
  );
}