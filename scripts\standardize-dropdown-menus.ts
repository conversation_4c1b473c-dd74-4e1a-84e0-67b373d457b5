/**
 * Script para estandarizar todos los menús contextuales (dropdown menus) en el proyecto
 * 
 * Patrón estándar:
 * 1. Acciones principales (Ver, Editar)
 * 2. Separador
 * 3. Acciones de estado (Activar/Desactivar)
 * 4. Acción de eliminación
 */

import fs from 'fs';
import path from 'path';

// Función recursiva para buscar archivos
function findFilesRecursively(dir: string, pattern: RegExp): string[] {
  const files: string[] = [];
  
  function walk(currentDir: string) {
    const entries = fs.readdirSync(currentDir, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(currentDir, entry.name);
      
      if (entry.isDirectory() && !entry.name.includes('node_modules')) {
        walk(fullPath);
      } else if (entry.isFile() && pattern.test(entry.name)) {
        files.push(fullPath);
      }
    }
  }
  
  walk(dir);
  return files;
}

// Buscar todos los archivos que contienen DropdownMenuContent
async function findDropdownMenuFiles() {
  const files = findFilesRecursively('app', /\.tsx$/);
  
  const dropdownFiles = [];
  
  for (const file of files) {
    const content = fs.readFileSync(file, 'utf-8');
    if (content.includes('DropdownMenuContent')) {
      dropdownFiles.push(file);
    }
  }
  
  return dropdownFiles;
}

// Analizar estructura del menú
function analyzeMenuStructure(content: string, file: string) {
  const menuMatches = content.match(/<DropdownMenuContent[\s\S]*?<\/DropdownMenuContent>/g);
  
  if (!menuMatches) return null;
  
  const results = [];
  
  for (const menu of menuMatches) {
    const hasViewAction = menu.includes('Ver detalles') || menu.includes('Ver detalle');
    const hasEditAction = menu.includes('Editar');
    const hasToggleAction = menu.includes('Desactivar') || menu.includes('Activar') || menu.includes('toggle');
    const hasDeleteAction = menu.includes('Eliminar') || menu.includes('Delete');
    const separatorCount = (menu.match(/DropdownMenuSeparator/g) || []).length;
    
    // Verificar si tiene la estructura correcta
    const needsStandardization = hasToggleAction && hasDeleteAction && separatorCount < 1;
    
    results.push({
      file,
      hasViewAction,
      hasEditAction,
      hasToggleAction,
      hasDeleteAction,
      separatorCount,
      needsStandardization,
      menuContent: menu
    });
  }
  
  return results;
}

// Main
async function main() {
  console.log('🔍 Buscando archivos con menús contextuales...\n');
  
  const files = await findDropdownMenuFiles();
  console.log(`📄 Encontrados ${files.length} archivos con DropdownMenuContent\n`);
  
  const needsUpdate = [];
  
  for (const file of files) {
    const content = fs.readFileSync(file, 'utf-8');
    const analysis = analyzeMenuStructure(content, file);
    
    if (analysis) {
      for (const menu of analysis) {
        if (menu.needsStandardization) {
          needsUpdate.push(menu);
        }
      }
    }
  }
  
  console.log('📊 Análisis de menús contextuales:\n');
  console.log(`✅ Total de archivos analizados: ${files.length}`);
  console.log(`⚠️  Menús que necesitan estandarización: ${needsUpdate.length}\n`);
  
  if (needsUpdate.length > 0) {
    console.log('📋 Archivos que necesitan actualización:\n');
    for (const menu of needsUpdate) {
      console.log(`- ${menu.file}`);
      console.log(`  • Tiene Ver: ${menu.hasViewAction ? '✓' : '✗'}`);
      console.log(`  • Tiene Editar: ${menu.hasEditAction ? '✓' : '✗'}`);
      console.log(`  • Tiene Toggle: ${menu.hasToggleAction ? '✓' : '✗'}`);
      console.log(`  • Tiene Eliminar: ${menu.hasDeleteAction ? '✓' : '✗'}`);
      console.log(`  • Separadores actuales: ${menu.separatorCount}`);
      console.log('');
    }
  }
  
  // Mostrar patrón recomendado
  console.log('\n📐 PATRÓN ESTÁNDAR RECOMENDADO:\n');
  console.log(`<DropdownMenuContent align="end">
  <DropdownMenuLabel>Acciones</DropdownMenuLabel>
  <DropdownMenuItem onClick={() => handleView(item)}>
    <Eye className="h-4 w-4 mr-2" />
    Ver detalles
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => handleEdit(item)}>
    <Edit className="h-4 w-4 mr-2" />
    Editar
  </DropdownMenuItem>
  <DropdownMenuSeparator />
  <DropdownMenuItem 
    onClick={() => handleToggleStatus(item)}
    className={item.isActive ? "text-orange-600" : "text-green-600"}
  >
    {item.isActive ? (
      <>
        <ToggleLeft className="h-4 w-4 mr-2" />
        Desactivar
      </>
    ) : (
      <>
        <ToggleRight className="h-4 w-4 mr-2" />
        Activar
      </>
    )}
  </DropdownMenuItem>
  <DropdownMenuItem 
    onClick={() => handleDelete(item)}
    className="text-red-600 focus:text-red-600"
  >
    <Trash2 className="h-4 w-4 mr-2" />
    Eliminar
  </DropdownMenuItem>
</DropdownMenuContent>`);
}

main().catch(console.error);