'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { 
  Bot, 
  Send, 
  Mic, 
  Calendar, 
  Clock, 
  User,
  Sparkles,
  MessageCircle,
  X
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AIAssistantChatProps {
  isOpen: boolean;
  onClose: () => void;
  doctorInfo?: any;
}

interface ChatMessage {
  id: string;
  type: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  suggestions?: string[];
  appointmentOptions?: any[];
}

export function AIAssistantChat({ 
  isOpen, 
  onClose, 
  doctorInfo 
}: AIAssistantChatProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    {
      id: '1',
      type: 'assistant',
      content: `¡Hola Dr. ${doctorInfo?.firstName || 'Doctor'}! 👋 Soy tu asistente de agenda inteligente. Puedo ayudarte con:

• 📅 Encontrar espacios disponibles
• ⏰ Programar citas rápidamente  
• 🔍 Buscar información de pacientes
• 📊 Consultar estadísticas de tu agenda
• 🗓️ Gestionar reprogramaciones

¿En qué puedo asistirte hoy?`,
      timestamp: new Date(),
      suggestions: [
        "Espacios disponibles mañana",
        "Programar cita urgente",
        "Ver agenda de esta semana",
        "Buscar paciente María González"
      ]
    }
  ]);
  
  const [inputMessage, setInputMessage] = useState('');
  const [isListening, setIsListening] = useState(false);
  const [isTyping, setIsTyping] = useState(false);

  // Mensajes de ejemplo para el mockup
  const mockResponses = {
    "espacios disponibles": {
      content: "He encontrado estos espacios disponibles para ti:",
      appointmentOptions: [
        {
          date: "2025-07-21",
          time: "09:30",
          duration: "30 min",
          type: "Consulta General",
          priority: "alta"
        },
        {
          date: "2025-07-21", 
          time: "14:00",
          duration: "30 min",
          type: "Control de Niño Sano",
          priority: "media"
        },
        {
          date: "2025-07-22",
          time: "10:15", 
          duration: "30 min",
          type: "Consulta General",
          priority: "baja"
        }
      ]
    },
    "programar cita": {
      content: "¡Perfecto! Te ayudo a programar una nueva cita. ¿Para qué paciente y qué tipo de consulta necesitas?",
      suggestions: [
        "María González - Control mensual",
        "Paciente nuevo - Consulta general", 
        "Luis Pérez - Seguimiento",
        "Cita urgente"
      ]
    },
    "agenda semana": {
      content: "Aquí tienes el resumen de tu agenda esta semana:\n\n📅 **Lunes**: 8 citas programadas\n📅 **Martes**: 6 citas programadas\n📅 **Miércoles**: 10 citas programadas\n📅 **Jueves**: 7 citas programadas\n📅 **Viernes**: 5 citas programadas\n\n✅ Total: 36 citas | 🟢 85% confirmadas"
    }
  };

  const handleSendMessage = () => {
    if (!inputMessage.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');
    setIsTyping(true);

    // Simular respuesta del asistente
    setTimeout(() => {
      const responseKey = Object.keys(mockResponses).find(key => 
        inputMessage.toLowerCase().includes(key)
      );
      
      const response = responseKey ? mockResponses[responseKey] : {
        content: "Entiendo tu consulta. En la versión completa podré ayudarte con eso y mucho más. Por ahora estoy en desarrollo para ofrecerte la mejor experiencia posible. 🚀"
      };

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        type: 'assistant',
        content: response.content,
        timestamp: new Date(),
        suggestions: response.suggestions,
        appointmentOptions: response.appointmentOptions
      };

      setMessages(prev => [...prev, assistantMessage]);
      setIsTyping(false);
    }, 1500);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputMessage(suggestion);
  };

  const handleVoiceInput = () => {
    setIsListening(!isListening);
    // Aquí iría la implementación de speech-to-text
  };

  if (!isOpen) return null;

  return (
    <div className="h-full flex flex-col bg-white">
      <div className="bg-gradient-to-r from-emerald-50 to-green-50 border-b border-emerald-200 p-4 flex-shrink-0">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <div className="relative">
              <Bot className="h-6 w-6 text-emerald-600" />
              <Sparkles className="h-3 w-3 text-emerald-500 absolute -top-1 -right-1" />
            </div>
            Asistente IA de Agenda
            <Badge variant="outline" className="bg-emerald-100 text-emerald-700 border-emerald-300">
              Beta
            </Badge>
          </h3>
          <Button 
            variant="ghost" 
            size="sm"
            onClick={onClose}
            className="hover:bg-emerald-100"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-emerald-700 mt-2">
          Gestiona tu agenda con comandos de voz o texto natural
        </p>
      </div>
      
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Chat Messages */}
        <ScrollArea className="flex-1 p-4">
          <div className="space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  "flex gap-3",
                  message.type === 'user' ? "justify-end" : "justify-start"
                )}
              >
                {message.type === 'assistant' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarFallback className="bg-emerald-100 text-emerald-700">
                      <Bot className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
                
                <div className={cn(
                  "max-w-xs lg:max-w-md px-4 py-3 rounded-lg",
                  message.type === 'user' 
                    ? "bg-blue-500 text-white" 
                    : "bg-gray-100 text-gray-900"
                )}>
                  <p className="text-sm whitespace-pre-line">{message.content}</p>
                  
                  {/* Opciones de citas disponibles */}
                  {message.appointmentOptions && (
                    <div className="mt-3 space-y-2">
                      {message.appointmentOptions.map((option, index) => (
                        <div
                          key={index}
                          className="p-3 bg-white rounded-md border border-gray-200 cursor-pointer hover:bg-emerald-50 transition-colors"
                        >
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-emerald-600" />
                              <span className="font-medium text-sm">{option.date}</span>
                              <Clock className="h-4 w-4 text-blue-600" />
                              <span className="text-sm">{option.time}</span>
                            </div>
                            <Badge 
                              variant="outline"
                              className={cn(
                                "text-xs",
                                option.priority === 'alta' && "border-emerald-300 text-emerald-700",
                                option.priority === 'media' && "border-yellow-300 text-yellow-700",
                                option.priority === 'baja' && "border-gray-300 text-gray-700"
                              )}
                            >
                              {option.priority}
                            </Badge>
                          </div>
                          <p className="text-xs text-gray-600 mt-1">
                            {option.type} • {option.duration}
                          </p>
                        </div>
                      ))}
                    </div>
                  )}
                  
                  {/* Sugerencias rápidas */}
                  {message.suggestions && (
                    <div className="mt-3 flex flex-wrap gap-2">
                      {message.suggestions.map((suggestion, index) => (
                        <Button
                          key={index}
                          variant="outline"
                          size="sm"
                          className="text-xs h-6 bg-white hover:bg-emerald-50 border-emerald-200"
                          onClick={() => handleSuggestionClick(suggestion)}
                        >
                          {suggestion}
                        </Button>
                      ))}
                    </div>
                  )}
                  
                  <p className="text-xs mt-2 opacity-70">
                    {message.timestamp.toLocaleTimeString('es-ES', { 
                      hour: '2-digit', 
                      minute: '2-digit' 
                    })}
                  </p>
                </div>
                
                {message.type === 'user' && (
                  <Avatar className="h-8 w-8 mt-1">
                    <AvatarImage src={doctorInfo?.imageUrl} />
                    <AvatarFallback className="bg-blue-100 text-blue-700">
                      <User className="h-4 w-4" />
                    </AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
            
            {/* Typing indicator */}
            {isTyping && (
              <div className="flex gap-3 justify-start">
                <Avatar className="h-8 w-8 mt-1">
                  <AvatarFallback className="bg-emerald-100 text-emerald-700">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
                <div className="bg-gray-100 px-4 py-3 rounded-lg">
                  <div className="flex gap-1">
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                    <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
        
        {/* Input Area */}
        <div className="border-t p-4 bg-gray-50 flex-shrink-0">
          <div className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                placeholder="Escribe tu consulta o usa comandos de voz..."
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                className="pr-12"
              />
              <Button
                variant="ghost"
                size="sm"
                className={cn(
                  "absolute right-1 top-1 h-8 w-8 p-0",
                  isListening && "bg-red-100 text-red-600"
                )}
                onClick={handleVoiceInput}
              >
                <Mic className="h-4 w-4" />
              </Button>
            </div>
            <Button
              onClick={handleSendMessage}
              disabled={!inputMessage.trim()}
              className="bg-emerald-600 hover:bg-emerald-700"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {/* Quick actions */}
          <div className="flex gap-2 mt-3">
            <Button
              variant="outline"
              size="sm"
              className="text-xs"
              onClick={() => handleSuggestionClick("Muestrame mis espacios disponibles mañana")}
            >
              <Calendar className="h-3 w-3 mr-1" />
              Espacios mañana
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-xs"
              onClick={() => handleSuggestionClick("Programar cita urgente")}
            >
              <Clock className="h-3 w-3 mr-1" />
              Cita urgente
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="text-xs"
              onClick={() => handleSuggestionClick("Resumen de mi agenda esta semana")}
            >
              <MessageCircle className="h-3 w-3 mr-1" />
              Resumen semanal
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}