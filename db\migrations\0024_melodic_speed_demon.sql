CREATE TABLE "appointment_logs" (
	"id" text PRIMARY KEY NOT NULL,
	"appointment_id" text NOT NULL,
	"event_type" text NOT NULL,
	"event_category" text NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"metadata" jsonb,
	"triggered_by" text,
	"triggered_by_role" text,
	"previous_state" jsonb,
	"new_state" jsonb,
	"email_type" text,
	"email_recipient" text,
	"email_status" text,
	"ip_address" text,
	"user_agent" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "nextAppointmentValue" integer;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "nextAppointmentUnit" text;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "nextAppointmentType" text;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "nextAppointmentNotes" text;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "nextAppointmentCalculatedDate" timestamp;--> statement-breakpoint
ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "public"."appointments"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_triggered_by_user_id_fk" FOREIGN KEY ("triggered_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "appointment_logs_appointment_idx" ON "appointment_logs" USING btree ("appointment_id");--> statement-breakpoint
CREATE INDEX "appointment_logs_event_type_idx" ON "appointment_logs" USING btree ("event_type");--> statement-breakpoint
CREATE INDEX "appointment_logs_event_category_idx" ON "appointment_logs" USING btree ("event_category");--> statement-breakpoint
CREATE INDEX "appointment_logs_created_at_idx" ON "appointment_logs" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "appointment_logs_triggered_by_idx" ON "appointment_logs" USING btree ("triggered_by");