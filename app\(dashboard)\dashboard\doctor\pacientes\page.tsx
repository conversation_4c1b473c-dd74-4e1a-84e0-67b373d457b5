'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  User, 
  Plus, 
  Search, 
  RefreshCw, 
  Download,
  ArrowLeft,
  Eye,
  Edit,
  Mail,
  Phone,
  Calendar,
  MoreHorizontal,
  Users,
  Baby,
  Heart,
  TrendingUp,
  Activity,
  Trash2,
  UserCheck,
  UserX
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { PatientDeleteConfirmationDialog } from '@/components/ui/patient-delete-confirmation-dialog';
import { formatPhoneForDisplay } from '@/lib/phone-utils';
import { formatTempEmail } from '@/lib/utils';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  dateOfBirth?: string;
  gender?: string;
  roleStatus: string; // Status viene del userRoles
  createdAt: string;
  lastVisit?: string;
  hasMedicalRecord?: boolean; // Indica si tiene expediente médico
  age?: number;
  isMinor?: boolean;
}

export default function PatientsPage() {
  const router = useRouter();
  const { user } = useUser();
  
  // Estados de datos
  const [patients, setPatients] = useState<Patient[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  
  // Estados de filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [genderFilter, setGenderFilter] = useState<'all' | 'male' | 'female' | 'other'>('all');
  
  // Estados de paginación
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(25);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  
  // Estados para el modal de eliminación
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [patientToDelete, setPatientToDelete] = useState<Patient | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  
  // Estados de estadísticas
  const [stats, setStats] = useState({
    totalPatients: 0,
    activePatients: 0,
    newThisMonth: 0,
    averageAge: 0,
    appointmentsToday: 0
  });

  // Función para obtener pacientes
  const fetchPatients = async () => {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchTerm,
        status: statusFilter,
        gender: genderFilter
      });

      const response = await fetch(`/api/patients?${params}`);
      if (!response.ok) {
        throw new Error('Error al cargar pacientes');
      }

      const data = await response.json();
      setPatients(data.data || []);
      setTotalPages(data.pagination?.totalPages || 1);
      setTotalCount(data.pagination?.total || 0);
      
      // Actualizar estadísticas si están disponibles
      if (data.stats) {
        setStats(data.stats);
      }
      
    } catch (error) {
      console.error('Error fetching patients:', error);
      toast.error('Error al cargar la lista de pacientes');
    }
  };

  // Función para refrescar datos
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPatients();
    setRefreshing(false);
    toast.success('Lista actualizada');
  };

  // Efecto para cargar datos cuando cambian los filtros
  useEffect(() => {
    setLoading(true);
    fetchPatients().finally(() => setLoading(false));
  }, [page, limit, searchTerm, statusFilter, genderFilter]);

  // Reset de página cuando cambian filtros
  useEffect(() => {
    setPage(1);
  }, [searchTerm, statusFilter, genderFilter]);

  // Función para calcular edad
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  // Navegación a páginas dedicadas
  const handleViewDetails = (patient: Patient) => {
    router.push(`/dashboard/doctor/pacientes/${patient.id}`);
  };

  const handleEdit = (patient: Patient) => {
    router.push(`/dashboard/doctor/pacientes/${patient.id}?mode=edit`);
  };

  // Función para cambiar estado del paciente
  const handleToggleStatus = async (patient: Patient) => {
    const newStatus = patient.roleStatus === 'active' ? 'inactive' : 'active';
    const action = newStatus === 'active' ? 'activar' : 'desactivar';
    
    if (!confirm(`¿Estás seguro de que deseas ${action} a ${patient.firstName} ${patient.lastName}?`)) {
      return;
    }

    try {
      const response = await fetch(`/api/patients/${patient.id}/toggle-status`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: newStatus })
      });

      if (!response.ok) {
        throw new Error('Error al cambiar el estado del paciente');
      }

      toast.success(`Paciente ${action === 'activar' ? 'activado' : 'desactivado'} correctamente`);
      await fetchPatients(); // Recargar la lista
    } catch (error) {
      console.error('Error changing patient status:', error);
      toast.error('Error al cambiar el estado del paciente');
    }
  };

  // Función para abrir el modal de eliminación
  const handleDeleteClick = (patient: Patient) => {
    setPatientToDelete(patient);
    setDeleteDialogOpen(true);
  };

  // Función para eliminar paciente (lógica o física)
  const handleDelete = async (type: 'logical' | 'physical') => {
    if (!patientToDelete) return;

    setIsDeleting(true);
    try {
      const endpoint = type === 'logical' 
        ? `/api/patients/${patientToDelete.id}/toggle-status`
        : `/api/patients/${patientToDelete.id}`;
      
      const response = await fetch(endpoint, {
        method: type === 'logical' ? 'PUT' : 'DELETE',
        headers: type === 'logical' ? { 'Content-Type': 'application/json' } : {},
        body: type === 'logical' ? JSON.stringify({ status: 'inactive' }) : undefined
      });

      if (!response.ok) {
        throw new Error('Error al eliminar el paciente');
      }

      toast.success(
        type === 'logical' 
          ? 'Paciente desactivado correctamente' 
          : 'Paciente eliminado permanentemente'
      );
      await fetchPatients(); // Recargar la lista
    } catch (error) {
      console.error('Error deleting patient:', error);
      toast.error('Error al eliminar el paciente');
    } finally {
      setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Gestión de Pacientes
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Administra la información de todos tus pacientes
              </p>
            </div>
          </div>
        </div>
        
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button onClick={() => router.push('/dashboard/doctor/pacientes/create')}>
            <Plus className="h-4 w-4 mr-2" />
            Nuevo Paciente
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Pacientes
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
              <Users className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? '...' : stats.totalPatients}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              {stats.activePatients} activos
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Nuevos Este Mes
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
              <TrendingUp className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? '...' : stats.newThisMonth}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              registrados este mes
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Promedio de Edad
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-purple-100 flex items-center justify-center">
              <Baby className="h-4 w-4 text-purple-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? '...' : Math.round(stats.averageAge)} años
            </div>
            <p className="text-xs text-gray-500 mt-1">
              edad promedio
            </p>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Consultas Hoy
            </CardTitle>
            <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
              <Activity className="h-4 w-4 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {loading ? '...' : stats.appointmentsToday}
            </div>
            <p className="text-xs text-gray-500 mt-1">
              citas programadas
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filtros de Búsqueda */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            {/* Campo de búsqueda principal */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, email o teléfono..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filtros adicionales */}
            <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
              {/* Filtro por estado */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activos</SelectItem>
                  <SelectItem value="inactive">Inactivos</SelectItem>
                </SelectContent>
              </Select>
              
              {/* Filtro por género */}
              <Select value={genderFilter} onValueChange={setGenderFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Todos los géneros" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los géneros</SelectItem>
                  <SelectItem value="male">Masculino</SelectItem>
                  <SelectItem value="female">Femenino</SelectItem>
                  <SelectItem value="other">Otro</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grid de Datos */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg">
            Lista de Pacientes
            {totalCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {totalCount} registros
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Vista Desktop */}
          <div className="hidden lg:block overflow-x-auto">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="font-semibold text-gray-700">
                      Nombre Completo
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">
                      Contacto
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">
                      Edad
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">
                      Género
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">
                      Estado
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700">
                      Expediente
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700 text-right">
                      Acciones
                    </TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="flex items-center justify-center">
                          <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mr-2" />
                          Cargando pacientes...
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : patients.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={7} className="text-center py-8">
                        <div className="text-center">
                          <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                          <p className="text-gray-500">No se encontraron pacientes</p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    patients.map((patient) => (
                      <TableRow key={patient.id} className="hover:bg-gray-50">
                        <TableCell>
                          <div className="flex items-center space-x-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                              {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900">
                                {patient.firstName} {patient.lastName}
                              </div>
                              <div className="text-sm text-gray-500">
                                ID: {patient.id.slice(0, 8)}...
                              </div>
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="space-y-1">
                            <div className="flex items-center text-sm text-gray-900">
                              <Mail className="h-3 w-3 mr-1 text-gray-400" />
                              {patient.email}
                            </div>
                            {patient.phone && (
                              <div className="flex items-center text-sm text-gray-600">
                                <Phone className="h-3 w-3 mr-1 text-gray-400" />
                                {formatPhoneForDisplay(patient.phone)}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>
                          {patient.dateOfBirth ? (
                            <div className="text-sm">
                              <span className="font-medium">{calculateAge(patient.dateOfBirth)} años</span>
                              <div className="text-xs text-gray-500">
                                {format(new Date(patient.dateOfBirth), 'dd/MM/yyyy', { locale: es })}
                              </div>
                            </div>
                          ) : (
                            <span className="text-gray-400 text-sm">No registrada</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">
                            {patient.gender === 'male' ? 'Masculino' : 
                             patient.gender === 'female' ? 'Femenino' : 
                             patient.gender === 'other' ? 'Otro' : 'No especificado'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Badge variant={patient.roleStatus === 'active' ? 'default' : 'secondary'}>
                            {patient.roleStatus === 'active' ? 'Activo' : 'Inactivo'}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center">
                            {patient.hasMedicalRecord ? (
                              <Badge variant="default" className="bg-green-100 text-green-800 border-green-300">
                                <Heart className="h-3 w-3 mr-1" />
                                Con Expediente
                              </Badge>
                            ) : (
                              <Badge variant="secondary" className="bg-orange-100 text-orange-800 border-orange-300">
                                <UserX className="h-3 w-3 mr-1" />
                                Sin Expediente
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => handleViewDetails(patient)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Ver detalles
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => handleEdit(patient)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem onClick={() => handleToggleStatus(patient)}>
                                {patient.roleStatus === 'active' ? (
                                  <>
                                    <UserX className="h-4 w-4 mr-2" />
                                    Desactivar
                                  </>
                                ) : (
                                  <>
                                    <UserCheck className="h-4 w-4 mr-2" />
                                    Activar
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                onClick={() => handleDeleteClick(patient)}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Eliminar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Vista Mobile y Tablet - Mobile First */}
          <div className="lg:hidden space-y-3">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin text-gray-400 mx-auto mb-2" />
                <p className="text-gray-500">Cargando pacientes...</p>
              </div>
            ) : patients.length === 0 ? (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-500">No se encontraron pacientes</p>
              </div>
            ) : (
              patients.map((patient) => (
                <div
                  key={patient.id}
                  className="relative border border-gray-200 rounded-lg p-4 hover:border-blue-300 hover:shadow-md transition-all duration-200 cursor-pointer group bg-white"
                  onClick={() => handleViewDetails(patient)}
                >
                  {/* Layout Mobile-First */}
                  <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4">
                    {/* Header con avatar, nombre y estado - en línea en mobile */}
                    <div className="flex items-center space-x-3 sm:flex-col sm:items-start sm:space-x-0 sm:space-y-2 sm:flex-shrink-0">
                      <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-sm font-semibold flex-shrink-0">
                        {patient.firstName.charAt(0)}{patient.lastName.charAt(0)}
                      </div>
                      
                      {/* Nombre y estado en mobile, solo nombre en tablet+ */}
                      <div className="flex items-center space-x-2 sm:flex-col sm:items-start sm:space-x-0">
                        <h3 className="font-medium text-gray-900 text-sm sm:text-base truncate">
                          {patient.firstName} {patient.lastName}
                        </h3>
                        <div className="sm:mt-1">
                          <Badge variant={patient.roleStatus === 'active' ? 'default' : 'secondary'} className="text-xs">
                            {patient.roleStatus === 'active' ? 'Activo' : 'Inactivo'}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    
                    {/* Información de contacto y detalles */}
                    <div className="flex-1 space-y-2">
                      {/* Grid de información responsivo */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                        {/* Email */}
                        <div className="flex items-center text-gray-600 truncate">
                          <Mail className="h-3 w-3 mr-2 text-gray-400 flex-shrink-0" />
                          <span className="truncate">{formatTempEmail(patient.email)}</span>
                        </div>
                        
                        {/* Teléfono */}
                        {patient.phone && (
                          <div className="flex items-center text-gray-600">
                            <Phone className="h-3 w-3 mr-2 text-gray-400 flex-shrink-0" />
                            <span>{formatPhoneForDisplay(patient.phone)}</span>
                          </div>
                        )}
                        
                        {/* Edad */}
                        {patient.dateOfBirth && (
                          <div className="flex items-center text-gray-600">
                            <Calendar className="h-3 w-3 mr-2 text-gray-400 flex-shrink-0" />
                            <span>{calculateAge(patient.dateOfBirth)} años</span>
                          </div>
                        )}
                        
                        {/* Género */}
                        <div className="flex items-center text-gray-600">
                          <User className="h-3 w-3 mr-2 text-gray-400 flex-shrink-0" />
                          <span>
                            {patient.gender === 'male' ? 'Masculino' : 
                             patient.gender === 'female' ? 'Femenino' : 
                             patient.gender === 'other' ? 'Otro' : 'No especificado'}
                          </span>
                        </div>
                      </div>
                      
                      {/* ID del paciente */}
                      <div className="text-xs text-gray-400">
                        ID: {patient.id.slice(0, 8)}...
                      </div>
                    </div>
                  </div>
                  
                  {/* Botón de acciones - posición absoluta */}
                  <div className="absolute top-3 right-3 sm:top-4 sm:right-4">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 w-8 p-0 opacity-70 group-hover:opacity-100 hover:bg-gray-100 transition-all"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                        <DropdownMenuItem onClick={() => handleViewDetails(patient)}>
                          <Eye className="h-4 w-4 mr-2 text-green-600" />
                          Ver detalles
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleEdit(patient)}>
                          <Edit className="h-4 w-4 mr-2" />
                          Editar
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem onClick={() => handleToggleStatus(patient)}>
                          {patient.roleStatus === 'active' ? (
                            <>
                              <UserX className="h-4 w-4 mr-2" />
                              Desactivar
                            </>
                          ) : (
                            <>
                              <UserCheck className="h-4 w-4 mr-2" />
                              Activar
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDeleteClick(patient)}
                          className="text-red-600 focus:text-red-600"
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          Eliminar
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Paginación - SOLO visible cuando hay múltiples páginas */}
      {totalPages > 1 && (
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
          {/* Información de registros y selector */}
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, totalCount)} de {totalCount} pacientes
            </div>
            
            {/* Selector de cantidad por página */}
            <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Controles de navegación */}
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            
            {/* Números de página con puntos suspensivos */}
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}

      {/* Modal de confirmación de eliminación */}
      <PatientDeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        itemToDelete={patientToDelete ? {
          name: `${patientToDelete.firstName} ${patientToDelete.lastName}`,
          firstName: patientToDelete.firstName,
          lastName: patientToDelete.lastName,
          email: patientToDelete.email
        } : null}
        onDelete={handleDelete}
        isDeleting={isDeleting}
        showPhysicalDelete={true}
      />
    </div>
  );
}