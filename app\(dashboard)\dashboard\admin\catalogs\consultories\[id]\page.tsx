'use client';

import { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  ArrowLeft,
  Edit,
  Loader2,
} from 'lucide-react';
import { toast } from 'sonner';
import Image from 'next/image';

interface Country {
  id: number;
  name: string;
  code: string;
  phoneCode: string;
}

interface Department {
  id: number;
  name: string;
  countryId: number;
}

interface Municipality {
  id: number;
  name: string;
  departmentId: number;
}

interface Consultory {
  id: string;
  name: string;
  code: string;
  type: string;
  specialty: string;
  capacity: number;
  floor: number;
  building: string;
  address?: string;
  phone?: string;
  email?: string;
  logoUrl?: string;
  logoPublicId?: string;
  countryId?: number;
  departmentId?: number;
  municipalityId?: number;
  country?: Country;
  department?: Department;
  municipality?: Municipality;
  isEmergencyCapable: boolean;
  hasAirConditioning: boolean;
  hasWaitingRoom: boolean;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function ViewConsultoryPage() {
  const router = useRouter();
  const params = useParams();
  const consultoryId = params.id as string;
  
  const [consultory, setConsultory] = useState<Consultory | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('basic');

  useEffect(() => {
    if (consultoryId) {
      fetchConsultory();
    }
  }, [consultoryId]);

  const fetchConsultory = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/catalogs/consultories/${consultoryId}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar consultorio');
      }
      
      const result = await response.json();
      setConsultory(result.data);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar los detalles del consultorio');
      router.push('/dashboard/admin/catalogs/consultories');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Cargando consultorio...</span>
      </div>
    );
  }

  if (!consultory) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <p className="text-gray-500 mb-4">No se encontró el consultorio</p>
          <Button onClick={() => router.push('/dashboard/admin/catalogs/consultories')}>
            Volver a la lista
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Detalles del Consultorio</h1>
            <p className="text-gray-600">Información completa del consultorio</p>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => router.push(`/dashboard/admin/catalogs/consultories/${consultoryId}/edit`)}
          >
            <Edit className="h-4 w-4 mr-2" />
            Editar
          </Button>
        </div>
      </div>

      {/* Custom Tabs */}
      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('basic')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'basic'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Información Básica</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('location')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'location'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Ubicación</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'contact'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Contacto</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('config')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'config'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Configuración</div>
              </div>
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <Card>
          <CardContent className="p-6">
            {activeTab === 'basic' && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Información Básica</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="space-y-3">
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Nombre</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.name}</span>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Código</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.code}</span>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Tipo</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.type}</span>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Especialidad</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.specialty}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'location' && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Ubicación Física</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Piso</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.floor}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Edificio</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.building || 'No especificado'}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Capacidad</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.capacity} personas</span>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">País</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.country?.name || 'No especificado'}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Departamento</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.department?.name || 'No especificado'}</span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Municipio</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.municipality?.name || 'No especificado'}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Información de Contacto</h3>
                  <div className="space-y-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Dirección</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-3 rounded-md min-h-[3rem]">{consultory.address || 'No especificada'}</span>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Teléfono</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.phone || 'No especificado'}</span>
                      </div>
                      <div className="flex flex-col space-y-1">
                        <span className="text-sm font-medium text-gray-500">Email</span>
                        <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">{consultory.email || 'No especificado'}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'config' && (
              <div className="space-y-6">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Características</h3>
                  <div className="space-y-3">
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        checked={consultory.isEmergencyCapable}
                        disabled
                        className="h-4 w-4 text-blue-600 rounded border-gray-300 cursor-not-allowed"
                      />
                      <label className="text-sm font-medium text-gray-700">Capacidad de Emergencias</label>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        checked={consultory.hasAirConditioning}
                        disabled
                        className="h-4 w-4 text-blue-600 rounded border-gray-300 cursor-not-allowed"
                      />
                      <label className="text-sm font-medium text-gray-700">Aire Acondicionado</label>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <input
                        type="checkbox"
                        checked={consultory.hasWaitingRoom}
                        disabled
                        className="h-4 w-4 text-blue-600 rounded border-gray-300 cursor-not-allowed"
                      />
                      <label className="text-sm font-medium text-gray-700">Sala de Espera</label>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Estado</h3>
                  <div className="flex items-center space-x-3">
                    <Badge variant={consultory.isActive ? "default" : "secondary"}>
                      {consultory.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                </div>

                {consultory.logoUrl && (
                  <div className="space-y-4">
                    <h3 className="text-lg font-semibold text-gray-900">Branding</h3>
                    <div className="flex items-center space-x-4">
                      <div className="w-20 h-20 rounded-xl overflow-hidden shadow-lg border">
                        <Image
                          src={consultory.logoUrl}
                          alt={`${consultory.name} logo`}
                          width={80}
                          height={80}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <span className="text-sm font-medium text-gray-500">Logo del Consultorio</span>
                        <p className="text-xs text-gray-400 mt-1">Imagen optimizada automáticamente</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Información del Sistema</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Fecha de Creación</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                        {new Date(consultory.createdAt).toLocaleDateString('es-ES', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                    <div className="flex flex-col space-y-1">
                      <span className="text-sm font-medium text-gray-500">Última Actualización</span>
                      <span className="text-sm text-gray-900 bg-gray-50 px-3 py-2 rounded-md">
                        {new Date(consultory.updatedAt).toLocaleDateString('es-ES', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}