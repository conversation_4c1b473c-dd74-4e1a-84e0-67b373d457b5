import { db } from './drizzle';
import { 
  consultories, 
  countries, 
  departments, 
  municipalities, 
  relationships, 
  occupations, 
  medicalSpecialties 
} from './schema';

async function seedCountries() {
  console.log('🌍 Iniciando seed de países...');
  
  const countriesData = [
    {
      id: 1,
      name: 'Guatemala',
      code: 'GT',
      phoneCode: '+502',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'México',
      code: 'MX',
      phoneCode: '+52',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Estados Unidos',
      code: 'US',
      phoneCode: '+1',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'El Salvador',
      code: 'SV',
      phoneCode: '+503',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 5,
      name: 'Honduras',
      code: 'HN',
      phoneCode: '+504',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const country of countriesData) {
    await db.insert(countries).values(country);
    console.log(`✅ País creado: ${country.name} (${country.phoneCode})`);
  }
}

async function seedDepartments() {
  console.log('🏛️ Iniciando seed de departamentos...');
  
  const departmentsData = [
    {
      id: 1,
      name: 'Guatemala',
      code: 'GT01',
      countryId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'Sacatepéquez',
      code: 'GT03',
      countryId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Escuintla',
      code: 'GT05',
      countryId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'Quetzaltenango',
      code: 'GT09',
      countryId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 5,
      name: 'Chimaltenango',
      code: 'GT04',
      countryId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const department of departmentsData) {
    await db.insert(departments).values(department);
    console.log(`✅ Departamento creado: ${department.name}`);
  }
}

async function seedMunicipalities() {
  console.log('🏘️ Iniciando seed de municipios...');
  
  const municipalitiesData = [
    // Guatemala
    {
      id: 1,
      name: 'Guatemala',
      code: 'GT0101',
      departmentId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'Mixco',
      code: 'GT0108',
      departmentId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Villa Nueva',
      code: 'GT0109',
      departmentId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'Villa Canales',
      code: 'GT0116',
      departmentId: 1,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    // Sacatepéquez
    {
      id: 5,
      name: 'Antigua Guatemala',
      code: 'GT0301',
      departmentId: 2,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    // Escuintla
    {
      id: 6,
      name: 'Escuintla',
      code: 'GT0501',
      departmentId: 3,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    // Quetzaltenango
    {
      id: 7,
      name: 'Quetzaltenango',
      code: 'GT0901',
      departmentId: 4,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    // Chimaltenango
    {
      id: 8,
      name: 'Chimaltenango',
      code: 'GT0401',
      departmentId: 5,
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const municipality of municipalitiesData) {
    await db.insert(municipalities).values(municipality);
    console.log(`✅ Municipio creado: ${municipality.name}`);
  }
}

async function seedRelationships() {
  console.log('👨‍👩‍👧‍👦 Iniciando seed de parentescos...');
  
  const relationshipsData = [
    {
      id: 1,
      name: 'Padre',
      code: 'PADRE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'Madre',
      code: 'MADRE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Hermano/a',
      code: 'HERMANO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'Abuelo/a',
      code: 'ABUELO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 5,
      name: 'Tío/a',
      code: 'TIO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 6,
      name: 'Cónyuge',
      code: 'CONYUGE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 7,
      name: 'Amigo/a',
      code: 'AMIGO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 8,
      name: 'Otro',
      code: 'OTRO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const relationship of relationshipsData) {
    await db.insert(relationships).values(relationship);
    console.log(`✅ Parentesco creado: ${relationship.name}`);
  }
}

async function seedOccupations() {
  console.log('💼 Iniciando seed de ocupaciones...');
  
  const occupationsData = [
    {
      id: 1,
      name: 'Médico/a',
      code: 'MEDICO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'Enfermero/a',
      code: 'ENFERMERO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Docente',
      code: 'DOCENTE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'Comerciante',
      code: 'COMERCIANTE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 5,
      name: 'Ama de Casa',
      code: 'AMA_CASA',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 6,
      name: 'Estudiante',
      code: 'ESTUDIANTE',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 7,
      name: 'Ingeniero/a',
      code: 'INGENIERO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 8,
      name: 'Abogado/a',
      code: 'ABOGADO',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 9,
      name: 'Contador/a',
      code: 'CONTADOR',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 10,
      name: 'Otra',
      code: 'OTRA',
      active: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const occupation of occupationsData) {
    await db.insert(occupations).values(occupation);
    console.log(`✅ Ocupación creada: ${occupation.name}`);
  }
}

async function seedMedicalSpecialties() {
  console.log('🩺 Iniciando seed de especialidades médicas...');
  
  const specialtiesData = [
    {
      id: 1,
      name: 'Pediatría General',
      code: 'PEDIATRIA',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 2,
      name: 'Neonatología',
      code: 'NEONATOLOGIA',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 3,
      name: 'Cardiología Pediátrica',
      code: 'CARDIOLOGIA_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 4,
      name: 'Neurología Pediátrica',
      code: 'NEUROLOGIA_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 5,
      name: 'Gastroenterología Pediátrica',
      code: 'GASTRO_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 6,
      name: 'Pneumología Pediátrica',
      code: 'PNEUMO_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 7,
      name: 'Endocrinología Pediátrica',
      code: 'ENDOCRINO_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 8,
      name: 'Cirugía Pediátrica',
      code: 'CIRUGIA_PED',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  for (const specialty of specialtiesData) {
    await db.insert(medicalSpecialties).values(specialty);
    console.log(`✅ Especialidad creada: ${specialty.name}`);
  }
}

// async function seedSystemConfig() {
//   console.log('⚙️ Iniciando seed de configuración del sistema...');
//   
//   const configData = [
//     {
//       id: 1,
//       key: 'system_country',
//       value: {
//         countryId: 1,
//         countryCode: 'GT',
//         countryName: 'Guatemala'
//       },
//       description: 'País principal del sistema',
//       category: 'localization',
//       active: true,
//       createdAt: new Date(),
//       updatedAt: new Date()
//     },
//     {
//       id: 2,
//       key: 'system_timezone',
//       value: {
//         timezone: 'America/Guatemala',
//         offset: '-06:00'
//       },
//       description: 'Zona horaria del sistema',
//       category: 'localization',
//       active: true,
//       createdAt: new Date(),
//       updatedAt: new Date()
//     },
//     {
//       id: 3,
//       key: 'system_locale',
//       value: {
//         locale: 'es-GT',
//         language: 'es',
//         country: 'GT'
//       },
//       description: 'Configuración regional del sistema',
//       category: 'localization',
//       active: true,
//       createdAt: new Date(),
//       updatedAt: new Date()
//     },
//     {
//       id: 4,
//       key: 'location_labels',
//       value: {
//         level1: 'Departamento',
//         level2: 'Municipio',
//         level1Placeholder: 'Selecciona departamento',
//         level2Placeholder: 'Selecciona municipio'
//       },
//       description: 'Etiquetas de localización según país',
//       category: 'localization',
//       active: true,
//       createdAt: new Date(),
//       updatedAt: new Date()
//     }
//   ];

//   for (const config of configData) {
//     await db.insert(systemConfig).values(config);
//     console.log(`✅ Configuración creada: ${config.key}`);
//   }
// }

async function seedConsultories() {
  console.log('🏥 Iniciando seed de consultorios...');
  
  try {
    // Insertar consultorios de ejemplo
    const consultoriesData = [
      {
        id: 'cons_001',
        name: 'Clínica Pediátrica San Carlos',
        address: '5a Avenida 15-45, Zona 10, Guatemala',
        phone: '2234-5678',
        email: '<EMAIL>',
        active: true,
        description: 'Clínica especializada en pediatría con más de 20 años de experiencia',
        services: ['Consulta general', 'Emergencias', 'Cirugía menor', 'Vacunación'],
        workingHours: {
          monday: { start: '07:00', end: '18:00', available: true },
          tuesday: { start: '07:00', end: '18:00', available: true },
          wednesday: { start: '07:00', end: '18:00', available: true },
          thursday: { start: '07:00', end: '18:00', available: true },
          friday: { start: '07:00', end: '18:00', available: true },
          saturday: { start: '08:00', end: '14:00', available: true },
          sunday: { start: '08:00', end: '12:00', available: false }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'cons_002',
        name: 'Centro Médico Mundo Pediatra',
        address: '12 Calle 3-40, Zona 1, Guatemala',
        phone: '2345-6789',
        email: '<EMAIL>',
        active: true,
        description: 'Centro médico principal con especialistas en todas las áreas pediátricas',
        services: ['Consulta general', 'Especialidades', 'Laboratorio', 'Rayos X', 'Farmacia'],
        workingHours: {
          monday: { start: '06:00', end: '20:00', available: true },
          tuesday: { start: '06:00', end: '20:00', available: true },
          wednesday: { start: '06:00', end: '20:00', available: true },
          thursday: { start: '06:00', end: '20:00', available: true },
          friday: { start: '06:00', end: '20:00', available: true },
          saturday: { start: '07:00', end: '18:00', available: true },
          sunday: { start: '08:00', end: '16:00', available: true }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'cons_003',
        name: 'Hospital Infantil Guatemala',
        address: '8a Avenida 11-20, Zona 11, Guatemala',
        phone: '2456-7890',
        email: '<EMAIL>',
        active: true,
        description: 'Hospital especializado en atención pediátrica con servicio de emergencias 24/7',
        services: ['Emergencias 24/7', 'Hospitalización', 'Cirugía', 'UCI Pediátrica', 'Especialidades'],
        workingHours: {
          monday: { start: '00:00', end: '23:59', available: true },
          tuesday: { start: '00:00', end: '23:59', available: true },
          wednesday: { start: '00:00', end: '23:59', available: true },
          thursday: { start: '00:00', end: '23:59', available: true },
          friday: { start: '00:00', end: '23:59', available: true },
          saturday: { start: '00:00', end: '23:59', available: true },
          sunday: { start: '00:00', end: '23:59', available: true }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'cons_004',
        name: 'Clínica Médica Mixco',
        address: '1a Avenida 5-25, Zona 1, Mixco',
        phone: '2567-8901',
        email: '<EMAIL>',
        active: true,
        description: 'Clínica en Mixco con atención pediátrica integral',
        services: ['Consulta general', 'Pediatría', 'Ginecología', 'Laboratorio'],
        workingHours: {
          monday: { start: '07:00', end: '17:00', available: true },
          tuesday: { start: '07:00', end: '17:00', available: true },
          wednesday: { start: '07:00', end: '17:00', available: true },
          thursday: { start: '07:00', end: '17:00', available: true },
          friday: { start: '07:00', end: '17:00', available: true },
          saturday: { start: '08:00', end: '13:00', available: true },
          sunday: { start: '08:00', end: '12:00', available: false }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        id: 'cons_005',
        name: 'Centro Pediátrico Villa Nueva',
        address: '3a Calle 8-15, Zona 4, Villa Nueva',
        phone: '2678-9012',
        email: '<EMAIL>',
        active: true,
        description: 'Centro pediátrico en Villa Nueva con enfoque en medicina preventiva',
        services: ['Consulta general', 'Vacunación', 'Control de niño sano', 'Nutrición'],
        workingHours: {
          monday: { start: '08:00', end: '17:00', available: true },
          tuesday: { start: '08:00', end: '17:00', available: true },
          wednesday: { start: '08:00', end: '17:00', available: true },
          thursday: { start: '08:00', end: '17:00', available: true },
          friday: { start: '08:00', end: '17:00', available: true },
          saturday: { start: '08:00', end: '14:00', available: true },
          sunday: { start: '08:00', end: '12:00', available: false }
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];

    // Insertar cada consultorio
    for (const consultory of consultoriesData) {
      await db.insert(consultories).values(consultory);
      console.log(`✅ Consultorio creado: ${consultory.name}`);
    }
    
  } catch (error) {
    console.error('❌ Error durante el seed de consultorios:', error);
    throw error;
  }
}

async function runAllSeeds() {
  console.log('🌱 Iniciando seed completo de catálogos médicos...');
  
  try {
    await seedCountries();
    await seedDepartments();
    await seedMunicipalities();
    await seedRelationships();
    await seedOccupations();
    await seedMedicalSpecialties();
    // await seedSystemConfig(); // TODO: Fix systemConfig import
    await seedConsultories();

    console.log('🎉 Seed completo exitoso!');
    console.log('📊 Catálogos creados:');
    console.log('   ✅ 5 Países con códigos telefónicos');
    console.log('   ✅ 5 Departamentos de Guatemala');
    console.log('   ✅ 8 Municipios principales');
    console.log('   ✅ 8 Parentescos/relaciones');
    console.log('   ✅ 10 Ocupaciones');
    console.log('   ✅ 8 Especialidades médicas');
    console.log('   ✅ 4 Configuraciones del sistema');
    console.log('   ✅ 5 Consultorios de ejemplo');
    
  } catch (error) {
    console.error('❌ Error durante el seed:', error);
    throw error;
  }
}

// Ejecutar el seed si el script se ejecuta directamente
if (require.main === module) {
  runAllSeeds()
    .then(() => {
      console.log('✨ Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { runAllSeeds, seedConsultories }; 