'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { UserCheck, Building, Shield, FileText, Users, AlertCircle } from 'lucide-react';
import { AssistantSpecificData, AssistantPermissions } from '@/lib/types/onboarding';

interface AssistantSpecificFormProps {
  data: Partial<AssistantSpecificData>;
  onChange: (data: Partial<AssistantSpecificData>) => void;
  onNext: () => void;
  onBack: () => void;
}

interface Doctor {
  id: string;
  name: string;
  specialty: string;
  consultoryName: string;
  consultoryId: string;
}

const POSITIONS = [
  'Enfermero/a Profesional',
  'Asistente Médico',
  'Secretario/a Médico',
  'Auxiliar de Enfermería',
  'Coordinador/a Médico',
  'Asistente Administrativo',
  'Otro'
];

export function AssistantSpecificForm({ data, onChange, onNext, onBack }: AssistantSpecificFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [loadingDoctors, setLoadingDoctors] = useState(true);
  const [selectedDoctors, setSelectedDoctors] = useState<string[]>(data.assignedDoctors || []);

  // Cargar doctores disponibles
  useEffect(() => {
    const fetchDoctors = async () => {
      try {
        const response = await fetch('/api/onboarding/doctors');
        const result = await response.json();
        if (result.success) {
          setDoctors(result.data);
        }
      } catch (error) {
        console.error('Error cargando doctores:', error);
      } finally {
        setLoadingDoctors(false);
      }
    };

    fetchDoctors();
  }, []);

  // Inicializar permisos si no existen
  useEffect(() => {
    if (!data.permissions) {
      const defaultPermissions: AssistantPermissions = {
        canScheduleAppointments: true,
        canHandlePayments: false,
        canAccessMedicalRecords: false,
        canManageInventory: false
      };
      onChange({ ...data, permissions: defaultPermissions });
    }
  }, [data, onChange]);

  const handleInputChange = (field: keyof AssistantSpecificData, value: any) => {
    onChange({ ...data, [field]: value });
    // Limpiar error si existe
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const handlePermissionChange = (permission: keyof AssistantPermissions, checked: boolean) => {
    const currentPermissions = data.permissions || {};
    const updatedPermissions = {
      ...currentPermissions,
      [permission]: checked
    };
    handleInputChange('permissions', updatedPermissions);
  };

  const handleDoctorToggle = (doctorId: string) => {
    const newSelected = selectedDoctors.includes(doctorId)
      ? selectedDoctors.filter(id => id !== doctorId)
      : [...selectedDoctors, doctorId];
    
    setSelectedDoctors(newSelected);
    handleInputChange('assignedDoctors', newSelected);
    
    // Automatically set consultoryId based on selected doctors
    if (newSelected.length > 0) {
      const firstSelectedDoctor = doctors.find(d => d.id === newSelected[0]);
      if (firstSelectedDoctor) {
        handleInputChange('consultoryId', firstSelectedDoctor.consultoryId);
      }
    } else {
      // Clear consultoryId if no doctors selected
      handleInputChange('consultoryId', '');
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validaciones requeridas
    if (!data.position?.trim()) newErrors.position = 'Puesto es requerido';
    if (!data.yearsExperience || data.yearsExperience < 0) newErrors.yearsExperience = 'Años de experiencia es requerido';
    if (!data.consultoryId) newErrors.consultoryId = 'Consultorio es requerido';
    if (!selectedDoctors.length) newErrors.assignedDoctors = 'Debe seleccionar al menos un doctor';

    // Validar años de experiencia
    if (data.yearsExperience && data.yearsExperience > 50) {
      newErrors.yearsExperience = 'Años de experiencia no puede ser mayor a 50';
    }

    // Validar que se seleccionaron doctores del mismo consultorio
    if (selectedDoctors.length > 0) {
      const selectedDoctorInfo = doctors.filter(d => selectedDoctors.includes(d.id));
      const consultoryId = data.consultoryId;
      
      if (consultoryId) {
        const selectedConsultory = selectedDoctorInfo.find(d => d.consultoryId !== consultoryId);
        if (selectedConsultory) {
          newErrors.assignedDoctors = 'Los doctores seleccionados deben pertenecer al mismo consultorio';
        }
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      onNext();
    }
  };

  // Obtener consultorio del primer doctor seleccionado
  const getSelectedConsultory = () => {
    if (selectedDoctors.length > 0) {
      const doctor = doctors.find(d => d.id === selectedDoctors[0]);
      return doctor?.consultoryName;
    }
    return null;
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Información Profesional */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <UserCheck className="h-5 w-5 text-purple-600" />
            <span>Información Profesional</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Posición */}
          <div className="space-y-2">
            <Label>Puesto/Posición *</Label>
            <Select 
              value={data.position} 
              onValueChange={(value) => handleInputChange('position', value)}
            >
              <SelectTrigger className={errors.position ? 'border-red-500' : ''}>
                <SelectValue placeholder="Seleccionar puesto" />
              </SelectTrigger>
              <SelectContent>
                {POSITIONS.map(position => (
                  <SelectItem key={position} value={position}>
                    {position}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.position && <p className="text-red-500 text-sm">{errors.position}</p>}
          </div>

          {/* Años de experiencia */}
          <div className="space-y-2">
            <Label htmlFor="yearsExperience">Años de Experiencia *</Label>
            <Input
              id="yearsExperience"
              type="number"
              min="0"
              max="50"
              value={data.yearsExperience || ''}
              onChange={(e) => handleInputChange('yearsExperience', parseInt(e.target.value))}
              className={errors.yearsExperience ? 'border-red-500' : ''}
              placeholder="5"
            />
            {errors.yearsExperience && <p className="text-red-500 text-sm">{errors.yearsExperience}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Asociación con Doctores */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5 text-purple-600" />
            <span>Asociación con Doctores</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {loadingDoctors ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mx-auto"></div>
              <p className="mt-2">Cargando doctores disponibles...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                Selecciona los doctores con los que trabajarás:
              </div>
              
              {doctors.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No hay doctores disponibles en este momento.
                </div>
              ) : (
                <div className="grid gap-3">
                  {doctors.map(doctor => (
                    <div
                      key={doctor.id}
                      className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 ${
                        selectedDoctors.includes(doctor.id)
                          ? 'border-purple-500 bg-purple-50 shadow-sm'
                          : 'border-gray-200 hover:border-gray-300 hover:shadow-sm'
                      }`}
                      onClick={() => handleDoctorToggle(doctor.id)}
                    >
                      <div className="flex items-start space-x-3">
                        <div className="flex-shrink-0 mt-1">
                          <Checkbox
                            checked={selectedDoctors.includes(doctor.id)}
                            onCheckedChange={() => handleDoctorToggle(doctor.id)}
                            className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 border-2 border-green-300 hover:border-green-400"
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="font-medium text-gray-900">{doctor.name}</div>
                          <div className="text-sm text-gray-500 mt-1">{doctor.specialty}</div>
                          <div className="text-sm text-gray-600 mt-1">
                            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100">
                              {doctor.consultoryName === 'No asignado' ? 'Sin consultorio asignado' : doctor.consultoryName}
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {errors.assignedDoctors && <p className="text-red-500 text-sm">{errors.assignedDoctors}</p>}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Consultorio asignado */}
      {getSelectedConsultory() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Building className="h-5 w-5 text-purple-600" />
              <span>Consultorio Asignado</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-purple-50 p-4 rounded-lg">
              <div className="font-medium">Consultorio:</div>
              <div className="text-purple-700">{getSelectedConsultory()}</div>
              <div className="text-sm text-gray-600 mt-2">
                Este consultorio se asignará automáticamente basado en los doctores seleccionados.
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Permisos especiales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="h-5 w-5 text-purple-600" />
            <span>Permisos Especiales</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-sm text-gray-600 mb-4">
              Selecciona los permisos que necesitas para tu trabajo:
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={data.permissions?.canScheduleAppointments || false}
                  onCheckedChange={(checked) => handlePermissionChange('canScheduleAppointments', checked as boolean)}
                />
                <Label className="text-sm">
                  <span className="font-medium">Agendar Citas</span>
                  <div className="text-gray-500">Crear, modificar y cancelar citas médicas</div>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={data.permissions?.canHandlePayments || false}
                  onCheckedChange={(checked) => handlePermissionChange('canHandlePayments', checked as boolean)}
                />
                <Label className="text-sm">
                  <span className="font-medium">Manejar Pagos</span>
                  <div className="text-gray-500">Procesar pagos y facturación</div>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={data.permissions?.canAccessMedicalRecords || false}
                  onCheckedChange={(checked) => handlePermissionChange('canAccessMedicalRecords', checked as boolean)}
                />
                <Label className="text-sm">
                  <span className="font-medium">Acceso a Expedientes</span>
                  <div className="text-gray-500">Ver y editar expedientes médicos (requiere aprobación del doctor)</div>
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  checked={data.permissions?.canManageInventory || false}
                  onCheckedChange={(checked) => handlePermissionChange('canManageInventory', checked as boolean)}
                />
                <Label className="text-sm">
                  <span className="font-medium">Gestionar Inventario</span>
                  <div className="text-gray-500">Manejar medicamentos y suministros</div>
                </Label>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Errores de validación */}
      {Object.keys(errors).length > 0 && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-start space-x-2">
              <AlertCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
              <div>
                <h4 className="font-medium text-red-800 mb-2">Por favor, complete los siguientes campos:</h4>
                <ul className="space-y-1">
                  {Object.entries(errors).map(([field, message]) => (
                    <li key={field} className="text-sm text-red-700">• {message}</li>
                  ))}
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <span>← Volver</span>
        </Button>
        <Button 
          onClick={handleNext}
          className="bg-purple-600 hover:bg-purple-700 flex items-center space-x-2"
          disabled={selectedDoctors.length === 0}
        >
          <span>Continuar →</span>
        </Button>
      </div>
    </div>
  );
} 