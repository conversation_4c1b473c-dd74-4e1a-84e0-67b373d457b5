-- SCRIPT DE EMERGENCIA: Corregir TODAS las fechas mal guardadas
-- EJECUTAR EN NEON SQL EDITOR

-- PASO 1: Configurar timezone permanentemente
ALTER DATABASE SET timezone = 'America/Guatemala';

-- PASO 2: <PERSON>rregi<PERSON> fechas en tabla APPOINTMENTS
UPDATE appointments SET
  "createdAt" = "createdAt" - INTERVAL '6 hours',
  "updatedAt" = "updatedAt" - INTERVAL '6 hours',
  "startTime" = "startTime" - INTERVAL '6 hours',
  "endTime" = "endTime" - INTERVAL '6 hours'
WHERE "createdAt" > '2025-08-01 20:00:00'; -- Solo registros de hoy que están mal

-- PASO 3: Corregir fechas en tabla USER
UPDATE "user" SET
  "createdAt" = "createdAt" - INTERVAL '6 hours',
  "updatedAt" = "updatedAt" - INTERVAL '6 hours'
WHERE "createdAt" > '2025-08-01 20:00:00'; -- Solo registros recientes mal

-- PASO 4: Corregi<PERSON> otras tablas con auditoría (agregar según necesites)
-- USER_ROLES
UPDATE user_roles SET
  "createdAt" = "createdAt" - INTERVAL '6 hours',
  "updatedAt" = "updatedAt" - INTERVAL '6 hours'
WHERE "createdAt" > '2025-08-01 20:00:00';

-- MEDICAL_CONSULTATIONS
UPDATE medical_consultations SET
  "createdAt" = "createdAt" - INTERVAL '6 hours',
  "updatedAt" = "updatedAt" - INTERVAL '6 hours',
  "consultationDate" = "consultationDate" - INTERVAL '6 hours'
WHERE "createdAt" > '2025-08-01 20:00:00';

-- PASO 5: Verificar correcciones
SELECT 
  'appointments' as tabla,
  COUNT(*) as registros_corregidos,
  MIN("createdAt") as fecha_minima,
  MAX("createdAt") as fecha_maxima
FROM appointments
WHERE "createdAt" > '2025-08-01 14:00:00' -- Ahora debería mostrar horas correctas de Guatemala

UNION ALL

SELECT 
  'user' as tabla,
  COUNT(*) as registros_corregidos,
  MIN("createdAt") as fecha_minima,
  MAX("createdAt") as fecha_maxima
FROM "user"
WHERE "createdAt" > '2025-08-01 14:00:00';

-- PASO 6: Verificar timezone final
SELECT 
  current_setting('timezone') as timezone_actual,
  NOW() as hora_guatemala_actual;