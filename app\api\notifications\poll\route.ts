import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { notifications } from '@/db/schema';
import { eq, and, desc, gt } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const lastCheck = searchParams.get('lastCheck');
    
    let hasNew = false;
    let newNotifications = [];

    if (lastCheck) {
      // Verificar si hay notificaciones nuevas desde la última verificación
      newNotifications = await db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.userId, userId),
            gt(notifications.createdAt, new Date(lastCheck))
          )
        )
        .orderBy(desc(notifications.createdAt));
      
      hasNew = newNotifications.length > 0;
    } else {
      // Si no hay lastCheck, verificar si hay notificaciones no leídas
      const unreadNotifications = await db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.userId, userId),
            eq(notifications.read, false)
          )
        )
        .limit(5);
      
      hasNew = unreadNotifications.length > 0;
      newNotifications = unreadNotifications;
    }

    return NextResponse.json({
      success: true,
      hasNew,
      newNotifications,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error polling notifications:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}