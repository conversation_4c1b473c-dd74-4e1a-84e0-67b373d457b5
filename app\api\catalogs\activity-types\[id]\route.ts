import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { activityTypes } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener tipo de actividad por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;

    const activityType = await db.select()
      .from(activityTypes)
      .where(eq(activityTypes.id, id))
      .limit(1);

    if (activityType.length === 0) {
      return NextResponse.json({ error: 'Tipo de actividad no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: activityType[0] 
    });

  } catch (error) {
    console.error('Error obteniendo tipo de actividad:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar tipo de actividad
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para eliminar tipos de actividad' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical';

    // Verificar que el tipo de actividad existe
    const existingType = await db.select()
      .from(activityTypes)
      .where(eq(activityTypes.id, id))
      .limit(1);

    if (existingType.length === 0) {
      return NextResponse.json({ error: 'Tipo de actividad no encontrado' }, { status: 404 });
    }

    const activityType = existingType[0];

    // Validación: No permitir eliminar tipo activo SOLO para eliminación lógica
    if (activityType.isActive && deleteType !== 'physical') {
      return NextResponse.json({ 
        error: 'No se puede eliminar un tipo de actividad activo', 
        suggestion: 'Desactive el tipo de actividad antes de eliminarlo',
        code: 'ACTIVE_ACTIVITY_TYPE'
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física
      await db.delete(activityTypes)
        .where(eq(activityTypes.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Tipo de actividad eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedType] = await db.update(activityTypes)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(activityTypes.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedType,
        message: 'Tipo de actividad desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error eliminando tipo de actividad:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}