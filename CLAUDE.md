# Reglas y Contexto para el Desarrollo

## Reglas Importantes




### ❌ NO HACER:
1. **NO crear soluciones temporales o parches** - Siempre implementar soluciones completas y permanentes
2. **NO comentar código "por si acaso"** - Si algo no se necesita, eliminarlo
3. **NO hacer cambios sin entender el contexto completo**
4. **NO asumir que algo funciona sin verificarlo**

### ✅ SIEMPRE:
1. **Implementar soluciones completas y definitivas**
2. **Mantener consistencia en todo el código**
3. **Seguir los patrones existentes del proyecto**
4. **Asegurar que todos los cambios sean permanentes y bien estructurados**
5. **Primero, analiza el problema, lee el código base para encontrar los archivos relevantes y escribe un plan en task/todo.md.
6. **El plan debe incluir una lista de tareas pendientes que puedas marcar a medida que las completes.
7. **Antes de empezar a trabajar, contáctame y verificaré el plan.
8. **<PERSON><PERSON>, empieza a trabajar en las tareas pendientes, marcán<PERSON>las como completadas a medida que avanzas. 
9. **Por favor, en cada paso del proceso, simplemente dame una explicación detallada de los cambios que realizaste.
10. **Simplifica al máximo cada tarea y cambio de código que realices. Queremos evitar cambios masivos o complejos. Cada cambio debe afectar la menor cantidad de código posible. Todo se basa en la simplicidad.

## Contexto del Proyecto

- **Base de datos**: PostgreSQL con Neon Database
- **ORM**: Drizzle
- **Framework**: Next.js 14 con App Router
- **Autenticación**: Clerk
- **Estilos**: Tailwind CSS

## Comandos Importantes

```bash
# Migraciones de base de datos
npx drizzle-kit generate
npx drizzle-kit push
npx drizzle-kit migrate

# Desarrollo
npm run dev

# Seed de datos
npm run seed
```

## Estructura de APIs

Todos los APIs deben retornar:
```json
{
  "data": [],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 100,
    "totalPages": 10,
    "hasNext": true,
    "hasPrev": false
  }
}
```

## Campos de Auditoría

TODAS las tablas deben tener:
- `createdAt`: timestamp con default now()
- `updatedAt`: timestamp con default now()

Estos campos son críticos para seguridad y trazabilidad.