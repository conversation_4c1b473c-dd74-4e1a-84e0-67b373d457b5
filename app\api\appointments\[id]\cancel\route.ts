import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { sendPatientEmail } from '@/lib/email-platform/core';

interface RouteParams {
  params: {
    id: string;
  };
}

// POST - Cancelar cita sin autenticación (usando código)
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const resolvedParams = await params;
    const appointmentId = resolvedParams.id;
    const body = await request.json();
    const { code, reason } = body;

    if (!code || !reason) {
      return NextResponse.json({ 
        error: 'Código y razón de cancelación son requeridos' 
      }, { status: 400 });
    }

    // Obtener la cita completa con información relacionada
    const result = await db
      .select({
        // Datos de la cita
        id: appointments.id,
        shortCode: appointments.shortCode,
        status: appointments.status,
        title: appointments.title,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        patientId: appointments.patientId,
        doctorId: appointments.doctorId,
        
        // Datos del paciente
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
        patientEmail: user.email,
        
        // Datos del consultorio
        consultoryName: consultories.name,
      })
      .from(appointments)
      .leftJoin(user, eq(appointments.patientId, user.id))
      .leftJoin(consultories, eq(appointments.consultoryId, consultories.id))
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (result.length === 0) {
      return NextResponse.json({ error: 'Cita no encontrada' }, { status: 404 });
    }

    const appointment = result[0];

    // Verificar el código
    if (appointment.shortCode !== code.toUpperCase()) {
      return NextResponse.json({ error: 'Código incorrecto' }, { status: 401 });
    }

    // Verificar el estado
    if (appointment.status === 'cancelled') {
      return NextResponse.json({ error: 'Esta cita ya fue cancelada' }, { status: 400 });
    }

    if (appointment.status === 'completed') {
      return NextResponse.json({ 
        error: 'No se puede cancelar una cita que ya fue completada' 
      }, { status: 400 });
    }

    // Cancelar la cita
    await db
      .update(appointments)
      .set({
        status: 'cancelled',
        cancellationReason: reason,
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId));

    // Obtener información del doctor para el email
    const doctorResult = await db
      .select({
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(eq(user.id, appointment.doctorId))
      .limit(1);

    const doctorInfo = doctorResult[0] || { firstName: '', lastName: '' };

    // Enviar email de confirmación de cancelación
    if (appointment.patientEmail && !appointment.patientEmail.includes('@temp.local')) {
      try {
        await sendPatientEmail(
          'appointment_cancelled',
          appointment.patientEmail,
          {
            patientName: `${appointment.patientFirstName} ${appointment.patientLastName}`,
            doctorName: `Dr. ${doctorInfo.firstName} ${doctorInfo.lastName}`,
            appointmentDate: appointment.scheduledDate.toLocaleDateString('es-ES', {
              weekday: 'long',
              year: 'numeric',
              month: 'long',
              day: 'numeric'
            }),
            appointmentTime: appointment.startTime.toLocaleTimeString('es-ES', {
              hour: '2-digit',
              minute: '2-digit'
            }),
            consultoryName: appointment.consultoryName || 'Consultorio Médico',
            cancellationReason: reason,
          },
          {
            patientId: appointment.patientId,
            appointmentId: appointment.id,
            flowType: 'appointment_cancellation'
          }
        );
        console.log('✅ Email de cancelación enviado a:', appointment.patientEmail);
      } catch (error) {
        console.error('❌ Error enviando email de cancelación:', error);
        // No fallar la cancelación si el email falla
      }
    }

    return NextResponse.json({ 
      success: true,
      message: 'Cita cancelada exitosamente',
      data: {
        appointmentId: appointment.id,
        status: 'cancelled',
        cancelledAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Error cancelling appointment:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}