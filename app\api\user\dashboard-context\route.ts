import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, guardianPatientRelations, user } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const action = searchParams.get('action'); // 'precheckin'
    const patientId = searchParams.get('patientId'); // ID del paciente para el pre-checkin
    const isDependent = searchParams.get('isDependent') === 'true';

    console.log('🔍 Dashboard Context API:', { userId, action, patientId, isDependent });

    // Para pre-checkin, determinar el dashboard correcto basado en el contexto
    if (action === 'precheckin') {
      if (isDependent && patientId) {
        // Verificar si el usuario es guardián de este paciente
        const guardianRelation = await db
          .select({
            guardianId: guardianPatientRelations.guardianId,
            patientId: guardianPatientRelations.patientId,
            relationship: guardianPatientRelations.relationship,
          })
          .from(guardianPatientRelations)
          .where(
            and(
              eq(guardianPatientRelations.guardianId, userId),
              eq(guardianPatientRelations.patientId, patientId)
            )
          )
          .limit(1);

        if (guardianRelation.length > 0) {
          console.log('✅ Usuario es guardián del paciente → dashboard/guardian');
          return NextResponse.json({
            success: true,
            dashboardUrl: '/dashboard/guardian',
            context: 'guardian',
            reason: 'User is guardian of patient for pre-checkin'
          });
        }
      } else if (!isDependent && patientId === userId) {
        // Es pre-checkin del usuario mismo
        console.log('✅ Pre-checkin del usuario mismo → dashboard');
        return NextResponse.json({
          success: true,
          dashboardUrl: '/dashboard',
          context: 'patient',
          reason: 'User is the patient for pre-checkin'
        });
      }
    }

    // Fallback: obtener roles del usuario para determinar dashboard principal
    const userRolesData = await db
      .select({
        role: userRoles.role,
        status: userRoles.status,
      })
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.status, 'active')
        )
      );

    // Determinar dashboard por prioridad de roles
    const roles = userRolesData.map(r => r.role);
    
    let dashboardUrl = '/dashboard';
    let context = 'default';
    
    if (roles.includes('admin')) {
      dashboardUrl = '/dashboard/admin';
      context = 'admin';
    } else if (roles.includes('doctor')) {
      dashboardUrl = '/dashboard/doctor';
      context = 'doctor';
    } else if (roles.includes('assistant')) {
      dashboardUrl = '/dashboard/assistant';
      context = 'assistant';
    } else if (roles.includes('guardian')) {
      dashboardUrl = '/dashboard/guardian';
      context = 'guardian';
    }

    console.log('✅ Dashboard determinado por roles:', { roles, dashboardUrl, context });

    return NextResponse.json({
      success: true,
      dashboardUrl,
      context,
      roles,
      reason: 'Determined by user roles'
    });

  } catch (error) {
    console.error('Error in dashboard context API:', error);
    return NextResponse.json(
      { success: false, error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}