'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Mail, CheckCircle, RefreshCw, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface AppointmentData {
  id: string;
  patientFirstName: string;
  patientLastName: string;
  scheduledDate: string;
  startTime: string;
  title: string;
  hoursUntil: number;
  canConfirm: boolean;
  isAlreadyConfirmed: boolean;
  patientEmail: string | null;
  emailCaptured: string | null;
  isPastDue: boolean;
}

export default function ConfirmarPage() {
  const router = useRouter();
  const [step, setStep] = useState<'code' | 'confirm' | 'success'>('code');
  const [code, setCode] = useState('');
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [appointment, setAppointment] = useState<AppointmentData | null>(null);

  const handleCodeSubmit = async () => {
    if (code.length < 6) {
      toast.error('Ingresa un código válido (ej: SGC1234567)');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/appointments/by-code/${code.toUpperCase()}`);
      const data = await response.json();

      if (data.success) {
        setAppointment(data.appointment);
        
        if (data.appointment.isAlreadyConfirmed) {
          setStep('success');
          toast.success('Tu cita ya está confirmada');
        } else if (data.appointment.isPastDue) {
          toast.error('Esta cita ya pasó. No se puede confirmar.');
        } else if (data.appointment.canConfirm) {
          // Pre-llenar email si existe
          if (data.appointment.patientEmail || data.appointment.emailCaptured) {
            setEmail(data.appointment.patientEmail || data.appointment.emailCaptured);
          }
          setStep('confirm');
        } else {
          toast.error('Esta cita no se puede confirmar (cancelada o expirada)');
        }
      } else {
        toast.error(data.error || 'Código no válido');
      }
    } catch (error) {
      toast.error('Error de conexión. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleConfirmation = async () => {
    if (!email || !email.includes('@')) {
      toast.error('Ingresa un email válido');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/appointments/${appointment?.id}/confirm`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: email,
          confirmedVia: 'web'
        })
      });

      const data = await response.json();

      if (data.success) {
        setStep('success');
        toast.success('¡Cita confirmada exitosamente!');
      } else {
        toast.error(data.error || 'Error al confirmar');
      }
    } catch (error) {
      toast.error('Error de conexión. Intenta nuevamente.');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      action();
    }
  };

  if (step === 'code') {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-white p-4">
        <Card className="w-full max-w-md shadow-xl">
          <CardHeader className="text-center">
            <div className="h-20 w-20 bg-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <Calendar className="h-10 w-10 text-white" />
            </div>
            <CardTitle className="text-2xl text-gray-900">Confirmar Cita</CardTitle>
            <p className="text-gray-600">Ingresa el código que recibiste por teléfono</p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="code">Código de confirmación</Label>
              <Input
                id="code"
                placeholder="Ej: SGC1234567"
                value={code}
                onChange={(e) => setCode(e.target.value.toUpperCase())}
                onKeyPress={(e) => handleKeyPress(e, handleCodeSubmit)}
                className="text-center text-xl font-mono tracking-wider h-14"
                maxLength={10}
              />
              <p className="text-xs text-gray-500 text-center">
                El código tiene el formato: SGC seguido de 7 números
              </p>
            </div>
            
            <Button 
              onClick={handleCodeSubmit} 
              className="w-full h-12 text-lg"
              disabled={code.length < 6 || loading}
            >
              {loading ? (
                <RefreshCw className="h-5 w-5 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-5 w-5 mr-2" />
              )}
              Buscar Cita
            </Button>

            <div className="text-center text-sm text-gray-500">
              <p>¿Problemas con tu código?</p>
              <p>Llama al consultorio para ayuda</p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 'confirm' && appointment) {
    const isToday = appointment.hoursUntil < 24 && appointment.hoursUntil > 0;
    const isSoon = appointment.hoursUntil < 2 && appointment.hoursUntil > 0;

    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-white p-4">
        <Card className="w-full max-w-lg shadow-xl">
          <CardHeader className="text-center">
            <div className="h-20 w-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <User className="h-10 w-10 text-white" />
            </div>
            <CardTitle className="text-2xl text-gray-900">Confirmar Asistencia</CardTitle>
            <p className="text-gray-600">Confirma que asistirás a tu cita</p>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Información de la cita */}
            <div className="bg-gray-50 p-4 rounded-lg space-y-3">
              <div className="flex items-center justify-between">
                <h3 className="font-semibold text-gray-900">Detalles de tu cita:</h3>
                {isSoon && (
                  <Badge variant="destructive" className="animate-pulse">
                    ¡MUY PRONTO!
                  </Badge>
                )}
                {isToday && !isSoon && (
                  <Badge className="bg-orange-500 text-white">
                    HOY
                  </Badge>
                )}
              </div>
              
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="font-medium">{appointment.patientFirstName} {appointment.patientLastName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-gray-500" />
                  <span>{format(new Date(appointment.scheduledDate), 'EEEE, dd \'de\' MMMM \'de\' yyyy', { locale: es })}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <span>{format(new Date(appointment.startTime), 'hh:mm a')}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-green-700 border-green-300">
                    {appointment.title}
                  </Badge>
                </div>
              </div>
              
              {appointment.hoursUntil > 0 && (
                <div className={`text-sm p-2 rounded ${
                  isSoon ? 'bg-red-100 text-red-700' : 
                  isToday ? 'bg-orange-100 text-orange-700' :
                  'bg-blue-100 text-blue-700'
                }`}>
                  {isSoon ? (
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4" />
                      <span>⏰ Tu cita es en {Math.abs(appointment.hoursUntil).toFixed(1)} horas</span>
                    </div>
                  ) : (
                    <span>⏰ Tu cita es en {Math.abs(appointment.hoursUntil).toFixed(1)} horas</span>
                  )}
                </div>
              )}
            </div>

            {/* Captura de email */}
            <div className="space-y-2">
              <Label htmlFor="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email para confirmaciones y recordatorios
              </Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                onKeyPress={(e) => handleKeyPress(e, handleConfirmation)}
                className="h-12"
                required
              />
              <p className="text-xs text-gray-500">
                📧 Te enviaremos recordatorios y el enlace para completar información médica
              </p>
            </div>

            <Button 
              onClick={handleConfirmation}
              className="w-full h-12 text-lg bg-green-600 hover:bg-green-700"
              disabled={!email || loading}
            >
              {loading ? (
                <RefreshCw className="h-5 w-5 animate-spin mr-2" />
              ) : (
                <CheckCircle className="h-5 w-5 mr-2" />
              )}
              Confirmar mi Asistencia
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (step === 'success' && appointment) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-green-50 to-white p-4">
        <Card className="w-full max-w-lg shadow-xl">
          <CardHeader className="text-center">
            <div className="h-20 w-20 bg-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="h-10 w-10 text-white" />
            </div>
            <CardTitle className="text-2xl text-green-900">¡Cita Confirmada!</CardTitle>
            <p className="text-green-600">Tu asistencia ha sido confirmada exitosamente</p>
          </CardHeader>
          <CardContent className="space-y-6 text-center">
            <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
              <h3 className="font-semibold text-green-900 mb-2">Detalles de tu cita confirmada:</h3>
              <div className="space-y-1 text-sm text-green-700">
                <p className="font-medium">{appointment.patientFirstName} {appointment.patientLastName}</p>
                <p>{format(new Date(appointment.scheduledDate), 'EEEE, dd \'de\' MMMM', { locale: es })}</p>
                <p>{format(new Date(appointment.startTime), 'hh:mm a')}</p>
                <p>{appointment.title}</p>
              </div>
            </div>

            <div className="space-y-3 text-sm text-gray-600">
              <div className="flex items-center justify-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Confirmación registrada en el sistema</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Mail className="h-4 w-4 text-blue-600" />
                <span>Recibirás recordatorios por email</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Calendar className="h-4 w-4 text-purple-600" />
                <span>Te enviaremos formulario médico previo</span>
              </div>
              <div className="flex items-center justify-center gap-2">
                <Clock className="h-4 w-4 text-orange-600" />
                <span>Llega 15 minutos antes de tu cita</span>
              </div>
            </div>

            <div className="pt-4 space-y-2">
              <Button 
                onClick={() => window.close()}
                className="w-full"
                variant="outline"
              >
                Cerrar
              </Button>
              <p className="text-xs text-gray-500">
                ¿Necesitas cambiar algo? Llama al consultorio
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return null;
}