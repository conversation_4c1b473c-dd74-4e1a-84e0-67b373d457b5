import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, medicalSpecialties, consultories } from '@/db/schema';
import { eq, and, isNotNull } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    const clerkUser = await currentUser();

    if (!userId || !clerkUser) {
      return NextResponse.json(
        { success: false, error: 'No autorizado' },
        { status: 401 }
      );
    }

    // Obtener el usuario actual para saber su consultorio
    // En la nueva estructura, userId de Clerk es el id en la tabla user
    const currentUserData = await db
      .select({
        consultoryId: userRoles.consultoryId,
      })
      .from(userRoles)
      .where(eq(userRoles.userId, userId))
      .limit(1);

    if (currentUserData.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Usuario no encontrado' },
        { status: 404 }
      );
    }

    const userConsultoryId = currentUserData[0].consultoryId;

    // Si el paciente no tiene consultorio, mostrar todos los doctores activos
    // Esto es común ya que los pacientes generalmente no están asignados a un consultorio específico
    
    // Obtener doctores activos (del consultorio del paciente o todos si no tiene)
    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        image: user.image,
        email: user.email,
        specialtyId: userRoles.specialtyId,
        specialtyName: medicalSpecialties.name,
        consultoryId: userRoles.consultoryId,
        consultoryName: consultories.name,
        // TODO: Agregar campo isFavorite basado en preferencias del paciente
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
      .leftJoin(consultories, eq(userRoles.consultoryId, consultories.id))
      .where(
        and(
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active'),
          // Si el usuario tiene consultorio, filtrar por ese, sino mostrar todos
          userConsultoryId ? eq(userRoles.consultoryId, userConsultoryId) : isNotNull(userRoles.consultoryId),
          // Solo doctores con perfil completo
          isNotNull(userRoles.specialtyId)
        )
      );

    // Formatear la respuesta
    const formattedDoctors = doctors.map(doctor => ({
      id: doctor.id,
      firstName: doctor.firstName,
      lastName: doctor.lastName,
      imageUrl: doctor.image || null,
      specialties: doctor.specialtyName ? [doctor.specialtyName] : [],
      consultoryId: doctor.consultoryId,
      consultoryName: doctor.consultoryName || null,
      // TODO: Implementar lógica de favoritos
      isFavorite: false
    }));

    return NextResponse.json({
      success: true,
      data: formattedDoctors,
      pagination: {
        page: 1,
        limit: formattedDoctors.length,
        total: formattedDoctors.length,
        totalPages: 1,
        hasNext: false,
        hasPrev: false,
      },
    });

  } catch (error) {
    console.error('Error fetching doctors:', error);
    console.error('Stack trace:', error instanceof Error ? error.stack : 'No stack trace');
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}