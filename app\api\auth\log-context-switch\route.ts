import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { fromContext, toContext, timestamp } = await request.json();

    // Log para auditoría (en producción esto iría a una tabla de logs)
    console.log(`[CONTEXT_SWITCH] User: ${userId}, From: ${fromContext}, To: ${toContext}, Time: ${timestamp}`);

    // Aquí podrías guardar en una tabla de auditoría si es necesario
    // await db.insert(contextSwitchLogs).values({
    //   userId,
    //   fromContext,
    //   toContext,
    //   timestamp: new Date(timestamp),
    // });

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error('Error logging context switch:', error);
    return NextResponse.json(
      { error: 'Error al registrar cambio de contexto' },
      { status: 500 }
    );
  }
}