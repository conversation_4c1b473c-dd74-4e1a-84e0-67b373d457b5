# ANÁLISIS COMPLETO DE CASOS DE USO - SISTEMA MÉDICO

## 🎯 OBJETIVO
Entender TODOS los escenarios posibles y simplificar el flujo de emails y procesos.

## 👥 TIPOS DE USUARIOS

### 1. **PACIENTE ADULTO (18+)**
- Puede crear su propia cuenta
- Recibe emails directamente
- Gestiona sus propias citas

### 2. **PACIENTE MENOR (<18) - DEPENDIENTE**
- NO puede crear cuenta propia
- Necesita ENCARGADO/GUARDIAN
- Emails van al encargado

### 3. **ENCARGADO/GUARDIAN**
- Adulto responsable del menor
- Recibe emails del menor
- Gestiona citas del menor

## 📋 CASOS DE USO IDENTIFICADOS

### CASO 1: PACIENTE ADULTO NUEVO + CITA
**Situación**: Doctor crea cita para adulto que no tiene cuenta
**¿Qué pasa?**
- Se crea paciente con email temporal
- Se genera cita
- ¿Se envía activación? ¿A quién?
- ¿Qué puede hacer sin activar?

### CASO 2: PACIENTE MENOR NUEVO + CITA  
**Situación**: Doctor crea cita para menor que no tiene encargado registrado
**¿Qué pasa?**
- Se crea paciente menor
- ¿Se identifica al encargado cómo?
- ¿Se crea cuenta del encargado?
- ¿Emails a quién?

### CASO 3: PACIENTE MENOR CON ENCARGADO YA REGISTRADO + CITA
**Situación**: Doctor crea cita para menor que YA tiene encargado con cuenta
**¿Qué pasa?**
- Solo se crea cita
- Emails van al encargado existente
- ¿Se necesita activación?

### CASO 4: CITA MISMO DÍA vs CITA FUTURA
**Situación**: Timing de la cita afecta el flujo
**¿Qué pasa?**
- Mismo día: Pre-checkin inmediato
- Futura: Pre-checkin 48h antes
- ¿Confirmación siempre igual?

## ❓ PREGUNTAS CRÍTICAS SIN RESPUESTA CLARA

### 1. **ACTIVACIÓN DE CUENTA**
- ¿Para QUÉ sirve exactamente?
- ¿Qué puede hacer el usuario SIN activar?
- ¿Qué puede hacer el usuario DESPUÉS de activar?
- ¿Es obligatorio activar para usar pre-checkin?

### 2. **PRE-CHECKIN**
- ¿Se puede hacer pre-checkin SIN cuenta activada?
- ¿El link de pre-checkin requiere login?
- ¿O es un formulario público con token?

### 3. **GESTIÓN DE CITAS**
- ¿Encargado puede ver TODAS las citas del menor?
- ¿Puede cancelar/reprogramar?
- ¿Necesita cuenta activada para esto?

### 4. **CÓDIGOS Y TOKENS**
- Código de confirmación: ¿Para qué se usa?
- Token de activación: ¿Para qué se usa?
- ¿Son procesos separados o conectados?

## 🔍 ANÁLISIS DEL CÓDIGO ACTUAL

### PROBLEMAS IDENTIFICADOS:

1. **FLUJO CONFUSO**
   - Activación + Confirmación + Pre-checkin mezclados
   - No está claro qué es obligatorio vs opcional

2. **EMAILS REDUNDANTES**
   - Confirmación incluye pre-checkin
   - Luego se envía email separado de pre-checkin
   - Usuario no sabe cuál usar

3. **TIMING INCONSISTENTE**
   - Algunos emails inmediatos
   - Otros con delays
   - Lógica no está clara

4. **PROPÓSITO UNCLEAR**
   - ¿Para qué activar cuenta?
   - ¿Qué beneficios tiene?
   - ¿Es solo para encargados o también adultos?

## 💡 PROPUESTA DE SIMPLIFICACIÓN

### PRINCIPIOS BASE:
1. **UN EMAIL = UNA ACCIÓN CLARA**
2. **PROCESOS SEPARADOS NO MEZCLADOS**
3. **TIMING LÓGICO Y PREDECIBLE**
4. **BENEFICIOS CLAROS PARA EL USUARIO**

### FLUJO PROPUESTO SIMPLIFICADO:

#### A. CREACIÓN DE CITA
1. **Email único**: "Tu cita está confirmada"
   - Detalles de cita
   - Código para emergencias/cambios
   - NO incluir otros procesos

#### B. ACTIVACIÓN (si aplica)
2. **Email separado**: "Activa tu cuenta" 
   - Solo para gestionar perfil/historial
   - Opcional, no bloquea nada
   - Beneficios claros explicados

#### C. PRE-CHECKIN (timing inteligente)
3. **Email cuando corresponde**:
   - Cita >48h: Email 48h antes
   - Cita ≤48h: Email inmediato
   - Formulario funciona sin login

## ❓ DECISIONES NECESARIAS

### 1. **¿Activación es obligatoria?**
- [ ] SÍ: Usuario debe activar para usar pre-checkin
- [ ] NO: Pre-checkin funciona sin activar

### 2. **¿Qué incluye cuenta activada?**
- [ ] Solo ver historial médico
- [ ] Gestionar perfil del paciente
- [ ] Cancelar/reprogramar citas
- [ ] Ver todas las citas futuras

### 3. **¿Pre-checkin requiere login?**
- [ ] SÍ: Debe tener cuenta activada
- [ ] NO: Formulario público con token

### 4. **¿Cuántos emails máximo por cita?**
- [ ] 1 email: Todo en uno
- [ ] 2 emails: Confirmación + Pre-checkin
- [ ] 3 emails: Confirmación + Activación + Pre-checkin

## 🎯 RECOMENDACIÓN PERSONAL

**FLUJO ULTRA-SIMPLIFICADO:**

### PARA CUALQUIER CITA:
1. **Email 1**: Confirmación de cita (solo eso)
2. **Email 2**: Pre-checkin (cuando corresponde por timing)
3. **Email 3**: Activación (solo si nunca se ha enviado antes)

### REGLAS:
- Pre-checkin NO requiere cuenta activada
- Activación es para gestión futura (opcional)
- Un email = un propósito específico
- Timing claro y predecible

## ❓ ¿QUÉ OPINAS?

¿Te parece este análisis? ¿Qué decisiones tomamos para cada punto?