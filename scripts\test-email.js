// Script para probar Resend directamente
require('dotenv').config({ path: '.env.local' });

async function testEmail() {
  const email = process.argv[2];
  
  if (!email) {
    console.log('❌ Por favor proporciona un email');
    console.log('Uso: node scripts/test-email.js <EMAIL>');
    process.exit(1);
  }

  if (!process.env.RESEND_API_KEY) {
    console.log('❌ RESEND_API_KEY no encontrada en .env.local');
    process.exit(1);
  }

  console.log('📧 Enviando email de prueba a:', email);
  console.log('🔑 API Key encontrada:', process.env.RESEND_API_KEY.substring(0, 10) + '...');

  try {
    // Importar Resend
    const { Resend } = require('resend');
    const resend = new Resend(process.env.RESEND_API_KEY);

    // Enviar email
    const data = await resend.emails.send({
      from: '<EMAIL>',
      to: [email],
      subject: 'Test Email - SGC Sistema',
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #10b981;">¡Email de Prueba Exitoso! 🎉</h1>
          <p>Si estás viendo este mensaje, Resend está configurado correctamente.</p>
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          <p><strong>Detalles:</strong></p>
          <ul>
            <li>Enviado desde: SGC Sistema Médico</li>
            <li>API Key: ${process.env.RESEND_API_KEY.substring(0, 10)}...</li>
            <li>Fecha: ${new Date().toLocaleString()}</li>
          </ul>
          <hr style="border: 1px solid #eee; margin: 20px 0;">
          <p style="color: #666; font-size: 14px;">
            Ahora puedes crear citas y recibirás los emails automáticamente.
          </p>
        </div>
      `,
    });

    console.log('✅ Email enviado exitosamente!');
    console.log('📬 ID del mensaje:', data.id);
    console.log('\n👉 Revisa tu bandeja de entrada (también spam/promociones)');
    
  } catch (error) {
    console.error('❌ Error al enviar email:', error.message);
    if (error.message.includes('API key')) {
      console.log('💡 Verifica que tu API key sea válida');
    }
  }
}

testEmail();