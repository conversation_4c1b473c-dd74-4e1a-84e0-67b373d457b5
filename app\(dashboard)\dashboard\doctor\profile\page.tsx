'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { User, Clock, Settings, Calendar, MapPin, Phone, Mail, Shield, Save, RefreshCw } from 'lucide-react';
import { DoctorScheduleManager } from '@/components/doctor/schedule-manager';
import { PersonalInfoForm } from '@/components/doctor/personal-info-form';
import { toast } from 'sonner';

interface DoctorProfile {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  specialties: string[];
  consultory: {
    id: string;
    name: string;
    address?: string;
  };
  licenseNumber?: string;
  roles: string[];
}

export default function DoctorProfilePage() {
  const { user } = useUser();
  const [profile, setProfile] = useState<DoctorProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('personal');

  useEffect(() => {
    fetchDoctorProfile();
  }, []);

  const fetchDoctorProfile = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/doctor/profile');
      const data = await response.json();
      
      if (data.success) {
        setProfile(data.data);
      } else {
        toast.error('Error al cargar el perfil');
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="icon-xl icon-info animate-spin mx-auto mb-4" />
          <p className="text-gray-600">Cargando perfil...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header del perfil */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20">
            <AvatarImage src={user?.imageUrl} />
            <AvatarFallback className="text-lg">
              {profile?.firstName?.[0]}{profile?.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Dr. {profile?.firstName} {profile?.lastName}
            </h1>
            <p className="text-gray-600">Perfil Médico</p>
            
            <div className="flex flex-wrap gap-2 mt-2">
              {profile?.specialties.map((specialty, index) => (
                <Badge key={index} variant="secondary">
                  {specialty}
                </Badge>
              ))}
              {profile?.roles.map((role, index) => (
                <Badge key={index} variant="outline">
                  {role}
                </Badge>
              ))}
            </div>
          </div>
        </div>

        <Card className="lg:w-80">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <MapPin className="icon-md icon-location" />
              Consultorio
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="font-medium">{profile?.consultory?.name}</p>
              {profile?.consultory?.address && (
                <p className="text-sm text-gray-600">{profile.consultory.address}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Información rápida */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Mail className="icon-xl icon-communication" />
            <div>
              <p className="text-sm text-gray-600">Email</p>
              <p className="font-medium">{profile?.email}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Phone className="icon-xl icon-phone" />
            <div>
              <p className="text-sm text-gray-600">Teléfono</p>
              <p className="font-medium">{profile?.phone || 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Shield className="icon-xl icon-document" />
            <div>
              <p className="text-sm text-gray-600">Licencia Médica</p>
              <p className="font-medium">{profile?.licenseNumber || 'No registrada'}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sistema de Tabs Personalizado siguiendo el estándar */}
      <div className="space-y-4">
        {/* Custom Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('personal')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'personal'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Información Personal</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('schedule')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'schedule'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Horarios de Trabajo</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('settings')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'settings'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Configuración</div>
              </div>
            </button>
          </div>
        </div>
        
        {/* Contenido de los tabs organizados verticalmente */}
        {activeTab === 'personal' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <User className="icon-md icon-user" />
                  Información Personal
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Actualiza tu información personal y profesional
                </p>
              </CardHeader>
              <CardContent>
                <PersonalInfoForm profile={profile} onUpdate={fetchDoctorProfile} />
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'schedule' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="icon-md icon-time" />
                  Gestión de Horarios
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Configura tus horarios de trabajo y disponibilidad para citas
                </p>
              </CardHeader>
              <CardContent>
                <DoctorScheduleManager />
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'settings' && (
          <div className="space-y-4">
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Settings className="icon-md icon-info" />
                  Configuración de Cuenta
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Configuraciones de seguridad y preferencias
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">Autenticación de dos factores</h3>
                      <p className="text-sm text-gray-600">
                        Añade una capa extra de seguridad a tu cuenta
                      </p>
                    </div>
                    <Button variant="outline">Configurar</Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">Notificaciones por email</h3>
                      <p className="text-sm text-gray-600">
                        Recibe notificaciones sobre citas y mensajes
                      </p>
                    </div>
                    <Button variant="outline">Configurar</Button>
                  </div>

                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">Cambiar contraseña</h3>
                      <p className="text-sm text-gray-600">
                        Actualiza tu contraseña de acceso
                      </p>
                    </div>
                    <Button variant="outline">Cambiar</Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}