import { NextRequest, NextResponse } from 'next/server';
import { auth, currentUser } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  appointments, 
  medicalConsultations, 
  medicalRecords, 
  patientInvitations,
  guardianPatientRelations 
} from '@/db/schema';
import { sql, count } from 'drizzle-orm';

// GET - Vista previa de qué se va a limpiar
export async function GET() {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener conteos actuales
    const [
      appointmentsCount,
      consultationsCount,
      recordsCount,
      invitationsCount,
      relationsCount
    ] = await Promise.all([
      db.select({ count: count() }).from(appointments),
      db.select({ count: count() }).from(medicalConsultations),
      db.select({ count: count() }).from(medicalRecords),
      db.select({ count: count() }).from(patientInvitations),
      db.select({ count: count() }).from(guardianPatientRelations)
    ]);

    const totalRecords = 
      appointmentsCount[0].count +
      consultationsCount[0].count +
      recordsCount[0].count +
      invitationsCount[0].count +
      relationsCount[0].count;

    return NextResponse.json({
      success: true,
      preview: {
        totalRecords,
        breakdown: {
          appointments: appointmentsCount[0].count,
          medicalConsultations: consultationsCount[0].count,
          medicalRecords: recordsCount[0].count,
          patientInvitations: invitationsCount[0].count,
          guardianPatientRelations: relationsCount[0].count
        },
        willKeep: [
          'Usuarios (pacientes, doctores, admins)',
          'Consultorios',
          'Catálogos (medicamentos, servicios, etc.)',
          'Configuración del sistema'
        ]
      }
    });

  } catch (error) {
    console.error('Error getting appointments preview:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor al obtener vista previa' 
    }, { status: 500 });
  }
}

// POST - Ejecutar limpieza de citas
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();
    
    if (!userId || !user) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { confirmationPhrase, confirmDelete, cleanType } = body;

    // Validaciones de seguridad
    const requiredPhrases = {
      'appointments-only': 'LIMPIAR SOLO CITAS MEDICAS',
      'appointments-and-relations': 'LIMPIAR CITAS Y RELACIONES'
    };

    const requiredPhrase = requiredPhrases[cleanType];
    if (!requiredPhrase) {
      return NextResponse.json({ 
        error: 'Tipo de limpieza no válido' 
      }, { status: 400 });
    }

    if (confirmationPhrase !== requiredPhrase) {
      return NextResponse.json({ 
        error: 'Frase de confirmación incorrecta' 
      }, { status: 400 });
    }

    if (!confirmDelete) {
      return NextResponse.json({ 
        error: 'Debe confirmar la eliminación' 
      }, { status: 400 });
    }

    // Obtener conteos antes de limpiar
    const beforeCounts = await Promise.all([
      db.select({ count: count() }).from(appointments),
      db.select({ count: count() }).from(medicalConsultations),
      db.select({ count: count() }).from(medicalRecords),
      db.select({ count: count() }).from(patientInvitations),
      db.select({ count: count() }).from(guardianPatientRelations)
    ]);

    console.log('🧹 Iniciando limpieza de citas:', cleanType);
    console.log('👤 Ejecutado por:', user.firstName, user.lastName, `(${user.emailAddresses[0]?.emailAddress})`);

    // Ejecutar limpieza en orden correcto
    const deletionResults = {};

    try {
      // 1. Consultas médicas (dependen de appointments)
      console.log('🗑️ Eliminando consultas médicas...');
      await db.delete(medicalConsultations);
      deletionResults.medicalConsultations = beforeCounts[1][0].count;

      // 2. Expedientes médicos
      console.log('🗑️ Eliminando expedientes médicos...');
      await db.delete(medicalRecords);
      deletionResults.medicalRecords = beforeCounts[2][0].count;

      // 3. Citas médicas
      console.log('🗑️ Eliminando citas médicas...');
      await db.delete(appointments);
      deletionResults.appointments = beforeCounts[0][0].count;

      // 4. Invitaciones de pacientes
      console.log('🗑️ Eliminando invitaciones de pacientes...');
      await db.delete(patientInvitations);
      deletionResults.patientInvitations = beforeCounts[3][0].count;

      // 5. Relaciones tutor-paciente (solo si se especifica)
      if (cleanType === 'appointments-and-relations') {
        console.log('🗑️ Eliminando relaciones tutor-paciente...');
        await db.delete(guardianPatientRelations);
        deletionResults.guardianPatientRelations = beforeCounts[4][0].count;
      }

    } catch (deleteError) {
      console.error('❌ Error durante eliminación:', deleteError);
      return NextResponse.json({ 
        error: 'Error durante la eliminación: ' + deleteError.message 
      }, { status: 500 });
    }

    // Verificar limpieza
    const afterCounts = await Promise.all([
      db.select({ count: count() }).from(appointments),
      db.select({ count: count() }).from(medicalConsultations),
      db.select({ count: count() }).from(medicalRecords),
      db.select({ count: count() }).from(patientInvitations),
      db.select({ count: count() }).from(guardianPatientRelations)
    ]);

    const totalDeleted = Object.values(deletionResults).reduce((sum, count) => sum + count, 0);
    const remainingRecords = afterCounts.reduce((sum, result) => sum + result[0].count, 0);

    console.log('✅ Limpieza completada');
    console.log('📊 Registros eliminados:', totalDeleted);
    console.log('📊 Registros restantes:', remainingRecords);

    // Preparar respuesta
    const result = {
      success: true,
      message: `Limpieza de citas completada exitosamente. ${totalDeleted} registros eliminados.`,
      summary: {
        cleanType,
        totalDeleted,
        remainingRecords,
        executedBy: `${user.firstName} ${user.lastName}`,
        executedAt: new Date().toISOString()
      },
      details: {
        before: {
          appointments: beforeCounts[0][0].count,
          medicalConsultations: beforeCounts[1][0].count,
          medicalRecords: beforeCounts[2][0].count,
          patientInvitations: beforeCounts[3][0].count,
          guardianPatientRelations: beforeCounts[4][0].count
        },
        deleted: deletionResults,
        after: {
          appointments: afterCounts[0][0].count,
          medicalConsultations: afterCounts[1][0].count,
          medicalRecords: afterCounts[2][0].count,
          patientInvitations: afterCounts[3][0].count,
          guardianPatientRelations: afterCounts[4][0].count
        }
      }
    };

    return NextResponse.json(result);

  } catch (error) {
    console.error('❌ Error en limpieza de citas:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor durante la limpieza' 
    }, { status: 500 });
  }
}