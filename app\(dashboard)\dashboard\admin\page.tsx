'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Users, 
  Calendar, 
  TrendingUp,
  Eye,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';
import { toast } from 'sonner';

interface RequestsSummary {
  total: number;
  pending: number;
  approved: number;
  rejected: number;
  reviewing: number;
}

interface RecentRequest {
  id: string;
  user: {
    firstName: string;
    lastName: string;
    email: string;
  };
  role: string;
  status: string;
  submittedAt: string;
}

export default function AdminDashboard() {
  const router = useRouter();
  const [requestsSummary, setRequestsSummary] = useState<RequestsSummary | null>(null);
  const [recentRequests, setRecentRequests] = useState<RecentRequest[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch('/api/admin/requests?limit=5');
        if (response.ok) {
          const data = await response.json();
          if (data.success) {
            setRequestsSummary(data.data.statistics);
            setRecentRequests(data.data.requests.slice(0, 5));
          }
        }
      } catch (error) {
        toast.error('Error al cargar datos del dashboard');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const roleLabels: { [key: string]: string } = {
    doctor: 'Doctor',
    assistant: 'Asistente',
    patient: 'Paciente',
    guardian: 'Guardian',
    provider: 'Proveedor'
  };

  const statusColors: { [key: string]: string } = {
    pending: 'bg-yellow-100 text-yellow-800',
    approved: 'bg-green-100 text-green-800',
    rejected: 'bg-red-100 text-red-800',
    reviewing: 'bg-blue-100 text-blue-800'
  };

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Dashboard Administrador
        </h1>
        <p className="text-gray-600">
          Resumen general del sistema y solicitudes pendientes
        </p>
      </div>
      
      {/* Métricas principales */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Solicitudes Pendientes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-orange-100 flex items-center justify-center">
              <Clock className="h-5 w-5 md:h-6 md:w-6 text-orange-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {loading ? '...' : requestsSummary?.pending || 0}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Requieren revisión</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Total Solicitudes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-blue-100 flex items-center justify-center">
              <Users className="h-5 w-5 md:h-6 md:w-6 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {loading ? '...' : requestsSummary?.total || 0}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Todas las solicitudes</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Aprobadas
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-emerald-100 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 md:h-6 md:w-6 text-emerald-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {loading ? '...' : requestsSummary?.approved || 0}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Usuarios activos</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-gray-600 uppercase tracking-wide">
              Rechazadas
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-red-100 flex items-center justify-center">
              <XCircle className="h-5 w-5 md:h-6 md:w-6 text-red-600" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-gray-900 mb-1 md:mb-2">
              {loading ? '...' : requestsSummary?.rejected || 0}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-gray-500">Solicitudes denegadas</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Solicitudes recientes */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Solicitudes Recientes</CardTitle>
            <p className="text-sm text-muted-foreground">
              Últimas solicitudes de registro enviadas
            </p>
          </div>
          <Button
            onClick={() => router.push('/dashboard/admin/requests')}
            variant="outline"
          >
            Ver todas
          </Button>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            </div>
          ) : recentRequests.length === 0 ? (
            <div className="text-center py-8">
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No hay solicitudes recientes</p>
            </div>
          ) : (
            <div className="space-y-4">
              {recentRequests.map((request) => (
                <div 
                  key={request.id}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-3 mb-2">
                      <p className="font-medium">
                        {request.user.firstName} {request.user.lastName}
                      </p>
                      <Badge variant="outline">
                        {roleLabels[request.role] || request.role}
                      </Badge>
                      <Badge className={statusColors[request.status]}>
                        {request.status === 'pending' ? 'Pendiente' : 
                         request.status === 'approved' ? 'Aprobada' :
                         request.status === 'rejected' ? 'Rechazada' : 
                         'En revisión'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">{request.user.email}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(request.submittedAt).toLocaleDateString('es-ES', {
                        day: 'numeric',
                        month: 'long',
                        year: 'numeric',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => router.push(`/dashboard/admin/requests/${request.id}`)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Acciones rápidas */}
      <Card>
        <CardHeader>
          <CardTitle>Acciones Rápidas</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button 
              className="h-auto p-4 flex flex-col items-center gap-2"
              variant="outline"
              onClick={() => router.push('/dashboard/admin/requests')}
            >
              <Clock className="h-12 w-12" />
              <span>Revisar Solicitudes</span>
            </Button>
            <Button 
              className="h-auto p-4 flex flex-col items-center gap-2"
              variant="outline"
              onClick={() => router.push('/dashboard/admin/users')}
            >
              <Users className="h-12 w-12" />
              <span>Gestionar Usuarios</span>
            </Button>
            <Button 
              className="h-auto p-4 flex flex-col items-center gap-2"
              variant="outline"
              onClick={() => router.push('/dashboard/admin/reports')}
            >
              <TrendingUp className="h-12 w-12" />
              <span>Ver Reportes</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}