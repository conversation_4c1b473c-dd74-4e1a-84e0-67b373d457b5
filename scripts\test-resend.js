// Test oficial de Resend según documentación
const { Resend } = require('resend');

async function testResend() {
  const email = process.argv[2] || '<EMAIL>';
  const apiKey = process.env.RESEND_API_KEY || 're_FXiJzeCL_JHGpryrCEUZVgWAinPeGq5yG';
  
  console.log('🔧 Configuración:');
  console.log('📧 Email destino:', email);
  console.log('🔑 API Key:', apiKey.substring(0, 20) + '...');
  console.log('');

  try {
    const resend = new Resend(apiKey);
    
    console.log('📤 Enviando email...');
    
    // Usando la estructura exacta de la documentación
    const { data, error } = await resend.emails.send({
      from: 'Acme <<EMAIL>>',
      to: [email],
      subject: 'Hello World - Test SGC',
      html: '<strong>¡Funciona!</strong><p>Este es un email de prueba desde SGC Sistema.</p>',
    });

    if (error) {
      console.error('❌ Error de Resend:', error);
      return;
    }

    console.log('✅ Email enviado exitosamente!');
    console.log('📬 Respuesta de Resend:', data);
    console.log('\n👉 Revisa tu email (incluyendo spam)');
    
  } catch (error) {
    console.error('❌ Error:', error.message);
    console.log('\n💡 Posibles causas:');
    console.log('1. API key inválida');
    console.log('2. Email no verificado en Resend');
    console.log('3. Límite de envíos alcanzado');
  }
}

// Ejecutar inmediatamente
testResend();