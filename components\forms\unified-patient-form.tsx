'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { DateInput } from '@/components/ui/date-input';
import { Separator } from '@/components/ui/separator';
import { PhoneInput } from '@/components/ui/phone-input';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Heart, 
  Save,
  Calendar,
  FileText,
  Home,
  AlertCircle,
  Check,
  RefreshCw,
  Baby,
  UserPlus,
  Shield,
  IdCard
} from 'lucide-react';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useRegionalConfig } from '@/hooks/use-regional-config';

// Tipos de datos del formulario
export interface PatientFormData {
  // Información básica
  firstName: string;
  lastName: string;
  documentType: string;
  documentNumber: string;
  dateOfBirth: string;
  gender: string;
  
  // Información de contacto
  email: string;
  phone: string;
  alternativePhone: string;
  
  // Ubicación (solo en modo complete)
  address: string;
  countryId: string;
  departmentId: string;
  municipalityId: string;
  
  // Contacto de emergencia (solo en modo complete)
  emergencyContact: string;
  emergencyEmail: string;
  emergencyPhone: string;
  emergencyRelationshipId: string;
  
  // Información adicional (solo en modo complete)
  occupationId: string;
  religionId: string;
  educationLevelId: string;
  maritalStatusId: string;
  
  // Opciones contextuales
  sendInvitation: boolean;
  
  // Información del guardián (para menores)
  isMinor?: boolean;
  guardianOption?: 'emergency' | 'different' | 'later';
  guardianData?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    relationship: string;
  };
}

export interface UnifiedPatientFormProps {
  mode: 'quick' | 'complete';
  context?: {
    showEmailInvitation?: boolean;
    doctorId?: string;
    requiredFields?: (keyof PatientFormData)[];
    theme?: 'public' | 'private';
    simplifiedUI?: boolean;
  };
  onSubmit: (data: PatientFormData) => Promise<void>;
  initialData?: Partial<PatientFormData>;
  loading?: boolean;
  onCancel?: () => void;
}

interface CatalogItem {
  id: string | number;
  name: string;
  code?: string;
  phoneCode?: string;
  countryId?: number;
  departmentId?: number;
}

export function UnifiedPatientForm({
  mode,
  context = {},
  onSubmit,
  initialData = {},
  loading = false,
  onCancel
}: UnifiedPatientFormProps) {
  const { getPhonePlaceholder } = useRegionalConfig();
  const [catalogsLoading, setCatalogsLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Estados para catálogos
  const [documentTypes, setDocumentTypes] = useState<CatalogItem[]>([]);
  const [relationships, setRelationships] = useState<CatalogItem[]>([]);
  const [countries, setCountries] = useState<CatalogItem[]>([]);
  const [departments, setDepartments] = useState<CatalogItem[]>([]);
  const [municipalities, setMunicipalities] = useState<CatalogItem[]>([]);
  const [occupations, setOccupations] = useState<CatalogItem[]>([]);
  const [religions, setReligions] = useState<CatalogItem[]>([]);
  const [educationLevels, setEducationLevels] = useState<CatalogItem[]>([]);
  const [maritalStatuses, setMaritalStatuses] = useState<CatalogItem[]>([]);
  
  // Estados para campos dependientes
  const [filteredDepartments, setFilteredDepartments] = useState<CatalogItem[]>([]);
  const [filteredMunicipalities, setFilteredMunicipalities] = useState<CatalogItem[]>([]);
  const [selectedCountryPhoneCode, setSelectedCountryPhoneCode] = useState<string>('');
  
  // Estados para manejo de menores
  const [isMinor, setIsMinor] = useState(false);
  const [guardianOption, setGuardianOption] = useState<'emergency' | 'different' | 'later'>('emergency');
  const [guardianData, setGuardianData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    relationship: ''
  });
  
  // Estado del formulario
  const [formData, setFormData] = useState<PatientFormData>({
    firstName: '',
    lastName: '',
    documentType: '',
    documentNumber: '',
    dateOfBirth: '',
    gender: '',
    email: '',
    phone: '',
    alternativePhone: '',
    address: '',
    countryId: '',
    departmentId: '',
    municipalityId: '',
    emergencyContact: '',
    emergencyEmail: '',
    emergencyPhone: '',
    emergencyRelationshipId: '',
    occupationId: '',
    religionId: '',
    educationLevelId: '',
    maritalStatusId: '',
    sendInvitation: false,
    ...initialData
  });
  
  // Estados para validación de email
  const [emailValidation, setEmailValidation] = useState<{
    isValidating: boolean;
    isSystemUser: boolean;
    systemUser: any;
    showGuardianModal: boolean;
  }>({
    isValidating: false,
    isSystemUser: false,
    systemUser: null,
    showGuardianModal: false,
  });

  // Cargar catálogos según el modo
  useEffect(() => {
    const loadCatalogs = async () => {
      setCatalogsLoading(true);
      try {
        const catalogPromises = [
          fetch('/api/catalogs/document-types?active=true&limit=100').then(r => r.json()),
        ];

        // Solo cargar catálogos adicionales en modo complete
        if (mode === 'complete') {
          catalogPromises.push(
            fetch('/api/catalogs/relationships?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/countries?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/departments?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/municipalities?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/occupations?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/religions?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/education-levels?active=true&limit=100').then(r => r.json()),
            fetch('/api/catalogs/marital-status?active=true&limit=100').then(r => r.json())
          );
        }

        const results = await Promise.all(catalogPromises);
        
        setDocumentTypes(results[0]?.data || []);
        
        if (mode === 'complete' && results.length > 1) {
          setRelationships(results[1]?.data || []);
          setCountries(results[2]?.data || []);
          setDepartments(results[3]?.data || []);
          setMunicipalities(results[4]?.data || []);
          setOccupations(results[5]?.data || []);
          setReligions(results[6]?.data || []);
          setEducationLevels(results[7]?.data || []);
          setMaritalStatuses(results[8]?.data || []);
        }
      } catch (error) {
        console.error('Error loading catalogs:', error);
        toast.error('Error cargando catálogos');
      } finally {
        setCatalogsLoading(false);
      }
    };

    loadCatalogs();
  }, [mode]);

  // Filtrar departamentos cuando cambia el país
  useEffect(() => {
    if (formData.countryId && mode === 'complete') {
      const filtered = departments.filter(dept => 
        dept.countryId === parseInt(formData.countryId)
      );
      setFilteredDepartments(filtered);
      
      // Limpiar departamento y municipio si no están en el nuevo país
      if (formData.departmentId && !filtered.find(d => d.id.toString() === formData.departmentId)) {
        setFormData(prev => ({
          ...prev,
          departmentId: '',
          municipalityId: ''
        }));
      }
    }
  }, [formData.countryId, departments, mode]);

  // Filtrar municipios cuando cambia el departamento
  useEffect(() => {
    if (formData.departmentId && mode === 'complete') {
      const filtered = municipalities.filter(muni => 
        muni.departmentId === parseInt(formData.departmentId)
      );
      setFilteredMunicipalities(filtered);
      
      // Limpiar municipio si no está en el nuevo departamento
      if (formData.municipalityId && !filtered.find(m => m.id.toString() === formData.municipalityId)) {
        setFormData(prev => ({
          ...prev,
          municipalityId: ''
        }));
      }
    }
  }, [formData.departmentId, municipalities, mode]);

  const updateFormData = (field: keyof PatientFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Detectar si es menor de edad cuando se actualiza la fecha de nacimiento
    if (field === 'dateOfBirth' && value) {
      const birthDate = new Date(value);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      
      setIsMinor(age < 18);
    }
    
    // Validar email en tiempo real si aplica
    if (field === 'email' && value && value.includes('@') && context.showEmailInvitation !== false) {
      validateEmail(value);
    } else if (field === 'email' && !value) {
      setEmailValidation({
        isValidating: false,
        isSystemUser: false,
        systemUser: null,
        showGuardianModal: false,
      });
    }
  };

  const validateEmail = async (email: string) => {
    setEmailValidation(prev => ({ ...prev, isValidating: true }));
    
    try {
      const response = await fetch('/api/patients/validate-email', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });
      
      const result = await response.json();
      
      if (result.success && result.isSystemUser) {
        setEmailValidation({
          isValidating: false,
          isSystemUser: true,
          systemUser: result.user,
          showGuardianModal: false, // En este contexto solo mostramos warning
        });
      } else {
        setEmailValidation({
          isValidating: false,
          isSystemUser: false,
          systemUser: null,
          showGuardianModal: false,
        });
      }
    } catch (error) {
      console.error('Error validating email:', error);
      setEmailValidation({
        isValidating: false,
        isSystemUser: false,
        systemUser: null,
        showGuardianModal: false,
      });
    }
  };

  const handleCountryChange = (countryId: string) => {
    const selectedCountry = countries.find(c => c.id.toString() === countryId);
    if (selectedCountry) {
      setSelectedCountryPhoneCode(selectedCountry.phoneCode || '+502');
      updateFormData('countryId', countryId);
    }
  };

  const isFormValid = () => {
    // Campos básicos siempre requeridos
    if (!formData.firstName || !formData.lastName || !formData.dateOfBirth) {
      return false;
    }

    // Validaciones adicionales según modo
    if (mode === 'complete') {
      if (!formData.countryId || !formData.departmentId || !formData.municipalityId) {
        return false;
      }
      if (!formData.emergencyContact || !formData.emergencyEmail || !formData.emergencyPhone || !formData.emergencyRelationshipId) {
        return false;
      }
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFormValid()) {
      toast.error('Por favor completa todos los campos requeridos');
      return;
    }

    // Validar guardián si es menor
    if (isMinor && guardianOption === 'different') {
      if (!guardianData.firstName || !guardianData.lastName || !guardianData.phone || !guardianData.relationship) {
        toast.error('Por favor completa la información del guardián');
        return;
      }
    }

    setSubmitting(true);
    try {
      // Preparar datos para enviar
      const dataToSubmit = {
        ...formData,
        isMinor,
        guardianOption: isMinor ? guardianOption : undefined,
        guardianData: isMinor && guardianOption === 'different' ? guardianData : undefined
      };
      
      await onSubmit(dataToSubmit);
    } catch (error) {
      console.error('Error submitting form:', error);
      // El error se maneja en el componente padre
    } finally {
      setSubmitting(false);
    }
  };

  const showEmailInvitation = context.showEmailInvitation !== false && formData.email;
  const isPublicTheme = context.theme === 'public';
  const cardClassName = isPublicTheme 
    ? "hover:shadow-md transition-all duration-200 border border-gray-200 shadow-sm" 
    : "hover:shadow-lg transition-all duration-300 border-0 shadow-sm";

  // Helper para calcular edad
  const getAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const birthDate = new Date(dateOfBirth);
    const today = new Date();
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    
    return age;
  };

  // Helper para formatear documento
  const getDocumentDisplay = () => {
    const docType = documentTypes.find(dt => dt.id.toString() === formData.documentType);
    const docTypeName = docType?.name || 'Documento';
    return formData.documentNumber ? `${docTypeName}: ${formData.documentNumber}` : 'Sin documento';
  };

  // Helper para formatear edad
  const getAgeDisplay = () => {
    const age = getAge(formData.dateOfBirth);
    return age !== null ? `${age} años` : 'Sin fecha de nacimiento';
  };

  return (
    <div className="space-y-6">
      {/* Header del perfil del paciente - Más compacto */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-4">
        <div className="flex items-center gap-3">
          <Avatar className="h-16 w-16">
            <AvatarFallback className="text-base bg-blue-100 text-blue-700">
              {formData.firstName?.[0] || 'P'}{formData.lastName?.[0] || 'A'}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {formData.firstName || formData.lastName 
                ? `${formData.firstName} ${formData.lastName}`.trim()
                : 'Nuevo Paciente'
              }
            </h1>
            <p className="text-gray-600 text-sm">
              {mode === 'quick' ? 'Registro Rápido' : 'Registro Completo'}
            </p>
            
            {isMinor && (
              <div className="flex items-center gap-2 mt-1">
                <Baby className="h-4 w-4 text-amber-600" />
                <span className="text-sm text-amber-700 font-medium">Paciente Menor de Edad</span>
              </div>
            )}
          </div>
        </div>

        {onCancel && (
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            className="flex items-center gap-2 shrink-0"
          >
            Cerrar
          </Button>
        )}
      </div>

      {/* Información rápida - Más compacto */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
        <Card>
          <CardContent className="flex items-center gap-3 pt-4 pb-4">
            <Mail className="h-6 w-6 text-blue-600" />
            <div>
              <p className="text-xs text-gray-600">Email</p>
              <p className="font-medium text-sm">{formData.email || 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-4 pb-4">
            <Phone className="h-6 w-6 text-green-600" />
            <div>
              <p className="text-xs text-gray-600">Teléfono</p>
              <p className="font-medium text-sm">{formData.phone || 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-4 pb-4">
            <IdCard className="h-6 w-6 text-purple-600" />
            <div>
              <p className="text-xs text-gray-600">Documento / Edad</p>
              <p className="font-medium text-sm">
                {formData.documentNumber ? getDocumentDisplay() : getAgeDisplay()}
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Layout principal con sidebar condicional */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Formulario principal */}
        <div className={cn("flex-1", isMinor && "lg:w-2/3")}>
          <form onSubmit={handleSubmit} className="space-y-4">
        {/* Información Básica */}
        <Card className={cardClassName}>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              Información Básica
            </CardTitle>
            <p className="text-sm text-gray-600">
              Datos personales del paciente
            </p>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium text-gray-700">
                  Nombres <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => updateFormData('firstName', e.target.value)}
                  placeholder="Nombres del paciente"
                  className="text-base"
                  required
                  autoFocus
                />
              </div>
            
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium text-gray-700">
                  Apellidos <span className="text-red-500 ml-1">*</span>
                </Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => updateFormData('lastName', e.target.value)}
                  placeholder="Apellidos del paciente"
                  className="text-base"
                  required
                />
              </div>
            
              <div className="space-y-2">
                <Label htmlFor="dateOfBirth" className="text-sm font-medium text-gray-700">
                  Fecha de Nacimiento <span className="text-red-500 ml-1">*</span>
                </Label>
                <DateInput
                  value={formData.dateOfBirth ? new Date(formData.dateOfBirth) : undefined}
                  onChange={(date) => updateFormData('dateOfBirth', date ? date.toISOString().split('T')[0] : '')}
                  maxDate={new Date()}
                  required
                />
                {isMinor && (
                  <div className="flex items-center gap-3 text-sm text-amber-700 bg-amber-50 p-3 rounded-lg border border-amber-200">
                    <Baby className="w-5 h-5 text-amber-600" />
                    <span className="font-medium">Este paciente es menor de edad. Se requerirá información del guardián.</span>
                  </div>
                )}
              </div>
            
              <div className="space-y-2">
                <Label htmlFor="gender" className="text-sm font-medium text-gray-700">Género</Label>
                <Select 
                  value={formData.gender} 
                  onValueChange={(value) => updateFormData('gender', value)}
                >
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="Seleccionar género" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">Masculino</SelectItem>
                    <SelectItem value="female">Femenino</SelectItem>
                    <SelectItem value="other">Otro</SelectItem>
                  </SelectContent>
                </Select>
              </div>
          </div>
        </CardContent>
      </Card>

        {/* Documentación */}
        <Card className={cardClassName}>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-600" />
              Documentación
            </CardTitle>
            <p className="text-sm text-gray-600">
              Identificación del paciente
            </p>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label htmlFor="documentType" className="text-sm font-medium text-gray-700">Tipo de Documento</Label>
                <Select 
                  value={formData.documentType} 
                  onValueChange={(value) => updateFormData('documentType', value)}
                >
                  <SelectTrigger className="text-base">
                    <SelectValue placeholder="Seleccionar tipo" />
                  </SelectTrigger>
                <SelectContent>
                  {documentTypes.map((docType) => (
                    <SelectItem key={docType.id} value={docType.id.toString()}>
                      {docType.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="documentNumber">Número de Documento</Label>
              <Input
                id="documentNumber"
                value={formData.documentNumber}
                onChange={(e) => updateFormData('documentNumber', e.target.value)}
                placeholder="Número de documento"
              />
            </div>
          </div>
        </CardContent>
      </Card>

        {/* Información de Contacto */}
        <Card className={cardClassName}>
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Phone className="h-5 w-5 text-purple-600" />
              Información de Contacto
            </CardTitle>
            <p className="text-sm text-gray-600">
              Datos de contacto y comunicación
            </p>
          </CardHeader>
          <CardContent className="space-y-5">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm font-medium text-gray-700">Email</Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  placeholder="<EMAIL>"
                  className={cn(
                    "text-base",
                    emailValidation.isSystemUser && "border-amber-500 bg-amber-50"
                  )}
                />
                {emailValidation.isValidating && (
                  <RefreshCw className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 animate-spin text-blue-500" />
                )}
              </div>
              {emailValidation.isSystemUser && emailValidation.systemUser && (
                <div className="flex items-center gap-2 text-sm text-amber-700 bg-amber-50 p-2 rounded">
                  <AlertCircle className="w-4 h-4" />
                  <span>
                    Email pertenece a {emailValidation.systemUser.firstName} {emailValidation.systemUser.lastName} 
                    ({emailValidation.systemUser.role === 'assistant' ? 'Asistente' : 'Doctor'})
                  </span>
                </div>
              )}
            </div>
            
            <div className="space-y-2">
              <PhoneInput
                id="phone"
                label="Teléfono"
                value={formData.phone}
                onChange={(value) => updateFormData('phone', value)}
              />
            </div>
            
            {mode === 'complete' && (
              <PhoneInput
                id="alternativePhone"
                label="Teléfono Alternativo"
                value={formData.alternativePhone}
                onChange={(value) => updateFormData('alternativePhone', value)}
              />
            )}
          </div>
        </CardContent>
      </Card>

      {/* Campos adicionales para modo complete */}
      {mode === 'complete' && (
        <>
          {/* Ubicación */}
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <MapPin className="h-5 w-5 text-red-600" />
                Ubicación
              </CardTitle>
              <p className="text-sm text-gray-600">
                Dirección y ubicación geográfica
              </p>
            </CardHeader>
            <CardContent className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="country">
                    País <span className="text-red-500">*</span>
                  </Label>
                  <Select 
                    value={formData.countryId} 
                    onValueChange={handleCountryChange}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar país" />
                    </SelectTrigger>
                    <SelectContent>
                      {countries.map((country) => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                          {country.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="department">
                    Departamento <span className="text-red-500">*</span>
                  </Label>
                  <Select 
                    value={formData.departmentId} 
                    onValueChange={(value) => updateFormData('departmentId', value)}
                    disabled={!formData.countryId}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar departamento" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredDepartments.map((department) => (
                        <SelectItem key={department.id} value={department.id.toString()}>
                          {department.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="municipality">
                    Municipio <span className="text-red-500">*</span>
                  </Label>
                  <Select 
                    value={formData.municipalityId} 
                    onValueChange={(value) => updateFormData('municipalityId', value)}
                    disabled={!formData.departmentId}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar municipio" />
                    </SelectTrigger>
                    <SelectContent>
                      {filteredMunicipalities.map((municipality) => (
                        <SelectItem key={municipality.id} value={municipality.id.toString()}>
                          {municipality.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="address">Dirección Completa</Label>
                <Input
                  id="address"
                  value={formData.address}
                  onChange={(e) => updateFormData('address', e.target.value)}
                  placeholder="Dirección completa"
                />
              </div>
            </CardContent>
          </Card>

          {/* Contacto de Emergencia */}
          <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg flex items-center gap-2">
                <Heart className="h-5 w-5 text-red-600" />
                Contacto de Emergencia
              </CardTitle>
              <p className="text-sm text-gray-600">
                Persona de contacto en caso de emergencia
              </p>
            </CardHeader>
            <CardContent className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="emergencyContact">
                    Nombre del Contacto <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={(e) => updateFormData('emergencyContact', e.target.value)}
                    placeholder="Nombre completo"
                    required
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="emergencyEmail">
                    Email del Contacto <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="emergencyEmail"
                    type="email"
                    value={formData.emergencyEmail}
                    onChange={(e) => updateFormData('emergencyEmail', e.target.value)}
                    placeholder="<EMAIL>"
                    required
                  />
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="emergencyRelationship">
                    Relación <span className="text-red-500">*</span>
                  </Label>
                  <Select 
                    value={formData.emergencyRelationshipId} 
                    onValueChange={(value) => updateFormData('emergencyRelationshipId', value)}
                    required
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar relación" />
                    </SelectTrigger>
                    <SelectContent>
                      {relationships.map((relationship) => (
                        <SelectItem key={relationship.id} value={relationship.id.toString()}>
                          {relationship.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <PhoneInput
                  id="emergencyPhone"
                  label="Teléfono de Emergencia"
                  value={formData.emergencyPhone}
                  onChange={(value) => updateFormData('emergencyPhone', value)}
                  required
                />
              </div>
            </CardContent>
          </Card>

          {/* Guardián Legal (solo para menores) */}
          {isMinor && (
            <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm border-l-4 border-l-amber-500">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <UserPlus className="h-5 w-5 text-purple-600" />
                  Guardián Legal / Encargado
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Información del responsable legal del menor
                </p>
              </CardHeader>
              <CardContent className="space-y-5">
                <div className="space-y-3">
                  <Label>¿Cómo desea registrar al guardián?</Label>
                  <div className="space-y-2">
                    <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="guardianOption"
                        value="emergency"
                        checked={guardianOption === 'emergency'}
                        onChange={(e) => setGuardianOption(e.target.value as any)}
                        className="w-4 h-4"
                      />
                      <div>
                        <div className="font-medium">Usar contacto de emergencia como guardián</div>
                        <div className="text-sm text-gray-600">
                          {formData.emergencyContact ? (
                            `${formData.emergencyContact} será el guardián principal`
                          ) : (
                            'Complete primero el contacto de emergencia'
                          )}
                        </div>
                      </div>
                    </label>
                    
                    <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="guardianOption"
                        value="different"
                        checked={guardianOption === 'different'}
                        onChange={(e) => setGuardianOption(e.target.value as any)}
                        className="w-4 h-4"
                      />
                      <div>
                        <div className="font-medium">Agregar guardián diferente</div>
                        <div className="text-sm text-gray-600">Registrar otra persona como guardián</div>
                      </div>
                    </label>
                    
                    <label className="flex items-center gap-3 p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                      <input
                        type="radio"
                        name="guardianOption"
                        value="later"
                        checked={guardianOption === 'later'}
                        onChange={(e) => setGuardianOption(e.target.value as any)}
                        className="w-4 h-4"
                      />
                      <div>
                        <div className="font-medium">Configurar guardián después</div>
                        <div className="text-sm text-gray-600">Se podrá agregar más adelante</div>
                      </div>
                    </label>
                  </div>
                </div>

                {/* Mostrar formulario si selecciona guardián diferente */}
                {guardianOption === 'different' && (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4 p-4 border rounded-lg bg-gray-50">
                    <div className="space-y-2">
                      <Label htmlFor="guardianFirstName">
                        Nombre del Guardián <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="guardianFirstName"
                        value={guardianData.firstName}
                        onChange={(e) => setGuardianData(prev => ({ ...prev, firstName: e.target.value }))}
                        placeholder="Nombres"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="guardianLastName">
                        Apellidos del Guardián <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="guardianLastName"
                        value={guardianData.lastName}
                        onChange={(e) => setGuardianData(prev => ({ ...prev, lastName: e.target.value }))}
                        placeholder="Apellidos"
                        required
                      />
                    </div>
                    
                    <div className="space-y-2">
                      <Label htmlFor="guardianEmail">Email del Guardián</Label>
                      <Input
                        id="guardianEmail"
                        type="email"
                        value={guardianData.email}
                        onChange={(e) => setGuardianData(prev => ({ ...prev, email: e.target.value }))}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    
                    <PhoneInput
                      id="guardianPhone"
                      label="Teléfono del Guardián"
                      value={guardianData.phone}
                      onChange={(value) => setGuardianData(prev => ({ ...prev, phone: value }))}
                      required
                    />
                    
                    <div className="space-y-2">
                      <Label htmlFor="guardianRelationship">
                        Relación con el Paciente <span className="text-red-500">*</span>
                      </Label>
                      <Select 
                        value={guardianData.relationship} 
                        onValueChange={(value) => setGuardianData(prev => ({ ...prev, relationship: value }))}
                        required
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar relación" />
                        </SelectTrigger>
                        <SelectContent>
                          {relationships.map((relationship) => (
                            <SelectItem key={relationship.id} value={relationship.id.toString()}>
                              {relationship.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Información Adicional */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Home className="h-5 w-5 text-gray-600" />
                Información Adicional
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-5">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="occupation">Ocupación</Label>
                  <Select 
                    value={formData.occupationId} 
                    onValueChange={(value) => updateFormData('occupationId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar ocupación" />
                    </SelectTrigger>
                    <SelectContent>
                      {occupations.map((occupation) => (
                        <SelectItem key={occupation.id} value={occupation.id.toString()}>
                          {occupation.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="religion">Religión</Label>
                  <Select 
                    value={formData.religionId} 
                    onValueChange={(value) => updateFormData('religionId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar religión" />
                    </SelectTrigger>
                    <SelectContent>
                      {religions.map((religion) => (
                        <SelectItem key={religion.id} value={religion.id.toString()}>
                          {religion.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="educationLevel">Nivel de Educación</Label>
                  <Select 
                    value={formData.educationLevelId} 
                    onValueChange={(value) => updateFormData('educationLevelId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar nivel" />
                    </SelectTrigger>
                    <SelectContent>
                      {educationLevels.map((level) => (
                        <SelectItem key={level.id} value={level.id.toString()}>
                          {level.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="maritalStatus">Estado Civil</Label>
                  <Select 
                    value={formData.maritalStatusId} 
                    onValueChange={(value) => updateFormData('maritalStatusId', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Seleccionar estado" />
                    </SelectTrigger>
                    <SelectContent>
                      {maritalStatuses.map((status) => (
                        <SelectItem key={status.id} value={status.id.toString()}>
                          {status.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Opciones */}
      {showEmailInvitation && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5 text-blue-600" />
              Opciones
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="sendInvitation"
                checked={formData.sendInvitation}
                onCheckedChange={(checked) => updateFormData('sendInvitation', checked)}
              />
              <Label htmlFor="sendInvitation" className="text-sm">
                Enviar invitación por email para activar cuenta
              </Label>
            </div>
          </CardContent>
        </Card>
      )}

        {/* Botones de acción */}
        <Card className="bg-gray-50 border-0">
          <CardContent className="pt-6">
            <div className="flex justify-end gap-4">
              {onCancel && (
                <Button 
                  type="button" 
                  variant="outline" 
                  onClick={onCancel}
                  disabled={submitting || catalogsLoading}
                  className="px-6"
                >
                  Cancelar
                </Button>
              )}
              <Button 
                type="submit" 
                disabled={submitting || catalogsLoading || !isFormValid()}
                className="flex items-center gap-2 px-6 bg-blue-600 hover:bg-blue-700"
              >
                {submitting ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <Save className="h-4 w-4" />
                )}
                {submitting ? 'Guardando...' : (mode === 'quick' ? 'Crear Paciente' : 'Guardar Paciente')}
              </Button>
            </div>
          </CardContent>
            </Card>
          </form>
        </div>

        {/* Sidebar - Card del Guardián/Encargado (solo para menores) */}
        {isMinor && (
          <div className="lg:w-80">
            <Card className="sticky top-6">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <UserPlus className="h-5 w-5 text-amber-600" />
                  Guardián Legal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {guardianOption === 'emergency' && formData.emergencyContact ? (
                    <div>
                      <p className="font-medium text-amber-700">Usando contacto de emergencia</p>
                      <p className="text-sm text-gray-600">{formData.emergencyContact}</p>
                      {formData.emergencyEmail && (
                        <p className="text-sm text-gray-600">{formData.emergencyEmail}</p>
                      )}
                      {formData.emergencyPhone && (
                        <p className="text-sm text-gray-600">{formData.emergencyPhone}</p>
                      )}
                    </div>
                  ) : guardianOption === 'different' && guardianData.firstName ? (
                    <div>
                      <p className="font-medium text-amber-700">Guardián diferente</p>
                      <p className="text-sm text-gray-600">{guardianData.firstName} {guardianData.lastName}</p>
                      {guardianData.email && (
                        <p className="text-sm text-gray-600">{guardianData.email}</p>
                      )}
                      {guardianData.phone && (
                        <p className="text-sm text-gray-600">{guardianData.phone}</p>
                      )}
                    </div>
                  ) : (
                    <div className="text-center py-4">
                      <UserPlus className="h-12 w-12 text-gray-300 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">
                        {guardianOption === 'later' 
                          ? 'Se registrará posteriormente' 
                          : 'Complete la información del guardián en el formulario'
                        }
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
}