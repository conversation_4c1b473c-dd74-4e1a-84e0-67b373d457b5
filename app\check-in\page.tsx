'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  Clock, 
  Calendar, 
  User, 
  Loader2, 
  AlertCircle, 
  Search,
  MapPin,
  DollarSign,
  Stethoscope
} from 'lucide-react';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';

interface AppointmentData {
  id: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  status: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  serviceName: string;
  consultoryName: string;
  estimatedPrice: string;
  currency: string;
  shortCode: string;
  preCheckinCompleted: boolean;
}

export default function CheckInPage() {
  const router = useRouter();
  const [code, setCode] = useState('');
  const [appointment, setAppointment] = useState<AppointmentData | null>(null);
  const [loading, setLoading] = useState(false);
  const [searching, setSearching] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const searchAppointment = async () => {
    if (!code.trim()) {
      toast.error('Por favor ingresa tu código de cita');
      return;
    }

    setSearching(true);
    setError(null);
    setAppointment(null);

    try {
      const response = await fetch(`/api/appointments/by-code/${code.trim().toUpperCase()}`);
      const result = await response.json();

      if (!response.ok) {
        setError(result.error || 'Código no encontrado');
        return;
      }

      if (result.success && result.data) {
        setAppointment(result.data);
      } else {
        setError('Cita no encontrada');
      }
    } catch (error) {
      console.error('Error searching appointment:', error);
      setError('Error de conexión. Por favor intenta nuevamente.');
    } finally {
      setSearching(false);
    }
  };

  const handleCheckIn = async () => {
    if (!appointment) return;

    setCheckingIn(true);
    try {
      const response = await fetch(`/api/appointments/check-in`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          shortCode: appointment.shortCode,
          appointmentId: appointment.id
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Error al realizar check-in');
      }

      setSuccess(true);
      toast.success('¡Check-in realizado exitosamente!');
      
      // Redirect after 3 seconds
      setTimeout(() => {
        router.push('/');
      }, 3000);

    } catch (error: any) {
      console.error('Error during check-in:', error);
      toast.error(error.message || 'Error al realizar check-in');
    } finally {
      setCheckingIn(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      searchAppointment();
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <Badge className="bg-[#50bed2]/10 text-[#50bed2]">Confirmada</Badge>;
      case 'pending_confirmation':
        return <Badge className="bg-yellow-100 text-yellow-800">Pendiente</Badge>;
      case 'checked_in':
        return <Badge className="bg-blue-100 text-blue-800">Ya registrada</Badge>;
      case 'completed':
        return <Badge className="bg-gray-100 text-gray-800">Completada</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <h2 className="text-xl font-semibold text-gray-900">¡Check-in Exitoso!</h2>
              <p className="text-gray-600">
                Su llegada ha sido registrada. El personal médico ha sido notificado.
              </p>
              <p className="text-sm text-gray-500">
                Redirigiendo en unos segundos...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <div className="w-full max-w-2xl space-y-6">
        
        {/* Header */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Check-in de Cita</h1>
          <p className="text-gray-600">Ingresa tu código de cita para registrar tu llegada</p>
        </div>

        {/* Search Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Search className="h-5 w-5 text-blue-600" />
              Buscar tu Cita
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="code">Código de Cita</Label>
              <div className="flex gap-2 mt-1">
                <Input
                  id="code"
                  value={code}
                  onChange={(e) => setCode(e.target.value.toUpperCase())}
                  onKeyPress={handleKeyPress}
                  placeholder="ABC123"
                  className="font-mono text-lg"
                  maxLength={10}
                />
                <Button 
                  onClick={searchAppointment}
                  disabled={searching || !code.trim()}
                  className="px-6"
                >
                  {searching ? (
                    <Loader2 className="h-4 w-4 animate-spin" />
                  ) : (
                    <Search className="h-4 w-4" />
                  )}
                </Button>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Formato: 3 letras + 3 números (ej: ABC123) o SGC + 7 números. Lo encuentras en tu email de confirmación.
              </p>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <div className="flex items-center gap-2 text-red-700">
                  <AlertCircle className="h-4 w-4" />
                  <span className="font-medium">{error}</span>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Appointment Details */}
        {appointment && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-600" />
                  Información de tu Cita
                </span>
                {getStatusBadge(appointment.status)}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              
              {/* Patient Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Paciente</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <User className="h-4 w-4 text-gray-400" />
                    <span className="font-medium">
                      {appointment.patientFirstName} {appointment.patientLastName}
                    </span>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Código</Label>
                  <div className="font-mono text-lg font-bold text-blue-600 mt-1">
                    {appointment.shortCode}
                  </div>
                </div>
              </div>

              <Separator />

              {/* Appointment Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Fecha y Hora</Label>
                  <div className="space-y-1 mt-1">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-400" />
                      <span>{formatDate(appointment.scheduledDate)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-4 w-4 text-gray-400" />
                      <span>
                        {new Date(appointment.startTime).toLocaleTimeString('es-GT', {
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </span>
                    </div>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Doctor</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Stethoscope className="h-4 w-4 text-gray-400" />
                    <span>
                      Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                    </span>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Servicio</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <span className="font-medium">{appointment.serviceName}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Consultorio</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <MapPin className="h-4 w-4 text-gray-400" />
                    <span>{appointment.consultoryName}</span>
                  </div>
                </div>
              </div>

              {appointment.estimatedPrice && (
                <div>
                  <Label className="text-sm font-medium text-gray-600">Precio Estimado</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <DollarSign className="h-4 w-4 text-gray-400" />
                    <span className="font-semibold text-green-600">
                      {appointment.currency} {appointment.estimatedPrice}
                    </span>
                  </div>
                </div>
              )}

              <Separator />

              {/* Pre-checkin Status */}
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="flex items-center gap-2">
                  {appointment.preCheckinCompleted ? (
                    <>
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-700 font-medium">
                        ✅ Pre-checkin completado
                      </span>
                    </>
                  ) : (
                    <>
                      <AlertCircle className="h-4 w-4 text-orange-600" />
                      <span className="text-sm text-orange-700 font-medium">
                        ⏳ Pre-checkin pendiente
                      </span>
                    </>
                  )}
                </div>
              </div>

              {/* Check-in Button */}
              <div className="pt-4">
                {appointment.status === 'checked_in' ? (
                  <div className="text-center">
                    <Badge className="bg-blue-100 text-blue-800 text-lg py-2 px-4">
                      ✅ Ya registraste tu llegada
                    </Badge>
                    <p className="text-sm text-gray-600 mt-2">
                      El personal médico ha sido notificado de tu llegada
                    </p>
                  </div>
                ) : appointment.status === 'completed' ? (
                  <div className="text-center">
                    <Badge className="bg-gray-100 text-gray-800 text-lg py-2 px-4">
                      ✅ Consulta completada
                    </Badge>
                  </div>
                ) : (
                  <Button 
                    onClick={handleCheckIn}
                    disabled={checkingIn}
                    className="w-full bg-[#50bed2] hover:bg-[#50bed2]/90 text-lg py-3"
                  >
                    {checkingIn ? (
                      <>
                        <Loader2 className="h-5 w-5 mr-2 animate-spin" />
                        Registrando llegada...
                      </>
                    ) : (
                      <>
                        <CheckCircle className="h-5 w-5 mr-2" />
                        Registrar mi llegada
                      </>
                    )}
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Instructions */}
        <Card className="bg-blue-50 border-blue-200">
          <CardContent className="p-4">
            <h3 className="font-medium text-blue-900 mb-2">¿Necesitas ayuda?</h3>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• Tu código de cita está en el email de confirmación</li>
              <li>• También puedes encontrarlo en los mensajes de recordatorio</li>
              <li>• Si no encuentras tu código, comunícate con recepción</li>
              <li>• Registra tu llegada al menos 15 minutos antes de tu cita</li>
            </ul>
          </CardContent>
        </Card>

      </div>
    </div>
  );
}