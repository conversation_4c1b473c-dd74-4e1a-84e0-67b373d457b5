import { db } from '@/db/drizzle';
import { appointments, medicalServices } from '@/db/schema';
import { eq } from 'drizzle-orm';

async function checkSpecificAppointment() {
  const appointmentId = '5QS5sGhaDr2JgUVwbgZ4K';
  
  console.log(`🔍 Revisando cita específica: ${appointmentId}\n`);

  try {
    // Obtener todos los datos de la cita
    const appointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      console.log('❌ Cita no encontrada');
      return;
    }

    const apt = appointment[0];
    console.log('📋 DATOS COMPLETOS DE LA CITA:');
    console.log('─────────────────────────────────────');
    console.log('ID:', apt.id);
    console.log('Título:', apt.title);
    console.log('Descripción:', apt.description);
    console.log('ServiceId:', apt.serviceId || '❌ NULL/UNDEFINED');
    console.log('ActivityTypeId:', apt.activityTypeId);
    console.log('Precio estimado:', apt.estimatedPrice);
    console.log('Duración:', apt.duration);
    console.log('Estado:', apt.status);
    console.log('Doctor ID:', apt.doctorId);
    console.log('Paciente ID:', apt.patientId);
    console.log('Consultorio ID:', apt.consultoryId);
    console.log('Motivo principal:', apt.chiefComplaint);
    
    // Si tiene serviceId, buscar el servicio
    if (apt.serviceId) {
      console.log('\n🔍 Buscando detalles del servicio...');
      const service = await db
        .select()
        .from(medicalServices)
        .where(eq(medicalServices.id, apt.serviceId))
        .limit(1);
        
      if (service.length > 0) {
        console.log('✅ Servicio encontrado:');
        console.log('- Nombre:', service[0].name);
        console.log('- Precio base:', service[0].basePrice);
        console.log('- Categoría:', service[0].category);
      } else {
        console.log('❌ Servicio no encontrado en catálogo');
      }
    } else {
      console.log('\n❌ Esta cita NO TIENE serviceId asignado');
      console.log('💡 Esto explica por qué no se transfieren servicios a la consulta');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  }
}

checkSpecificAppointment();