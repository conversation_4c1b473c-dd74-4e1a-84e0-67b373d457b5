import { db } from '@/db/drizzle';
import { user, guardianPatientRelations, appointments } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { generateCheckInLink } from '@/lib/email';
import { sendPatientEmail } from '@/lib/email-platform';
import crypto from 'crypto';

// Tipos para trabajar con guardianes
export interface PatientInfo {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  dateOfBirth: string | null;
}

export interface GuardianInfo {
  id: string;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  relationship: string;
  isPrimary: boolean;
  canMakeDecisions: boolean;
}

export interface AppointmentInfo {
  id: string;
  title: string;
  scheduledDate: Date;
  startTime: Date;
  endTime: Date;
  doctorFirstName: string | null;
  doctorLastName: string | null;
  consultoryName: string | null;
}

// Función para detectar si un paciente es dependiente (tiene email temporal)
export function isPatientDependent(patient: PatientInfo): boolean {
  if (!patient.email) return false;
  
  // Los pacientes dependientes tienen emails con formato: <EMAIL>
  return patient.email.includes('@temp.local') || patient.email.startsWith('patient_');
}

// Función para obtener guardianes de un paciente
export async function getPatientGuardians(patientId: string): Promise<GuardianInfo[]> {
  try {
    const guardians = await db
      .select({
        id: guardianPatientRelations.guardianId,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        relationship: guardianPatientRelations.relationship,
        isPrimary: guardianPatientRelations.isPrimary,
        canMakeDecisions: guardianPatientRelations.canMakeDecisions,
      })
      .from(guardianPatientRelations)
      .leftJoin(user, eq(guardianPatientRelations.guardianId, user.id))
      .where(eq(guardianPatientRelations.patientId, patientId));

    return guardians.map(guardian => ({
      id: guardian.id,
      firstName: guardian.firstName,
      lastName: guardian.lastName,
      email: guardian.email,
      phone: guardian.phone,
      relationship: guardian.relationship,
      isPrimary: guardian.isPrimary || false,
      canMakeDecisions: guardian.canMakeDecisions || false,
    }));
  } catch (error) {
    console.error('Error getting patient guardians:', error);
    return [];
  }
}

// Función para obtener el guardián principal
export async function getPrimaryGuardian(patientId: string): Promise<GuardianInfo | null> {
  const guardians = await getPatientGuardians(patientId);
  
  // Buscar guardián principal primero
  const primaryGuardian = guardians.find(g => g.isPrimary);
  if (primaryGuardian) return primaryGuardian;
  
  // Si no hay principal, tomar el primero que puede tomar decisiones
  const decisionMaker = guardians.find(g => g.canMakeDecisions);
  if (decisionMaker) return decisionMaker;
  
  // Si no hay ninguno específico, tomar el primero
  return guardians.length > 0 ? guardians[0] : null;
}

// Función para generar token único de pre-checkin
export function generatePreCheckinToken(): string {
  return crypto.randomUUID();
}

// Función para determinar quién debe recibir el pre-checkin
export async function determinePreCheckinRecipient(patientId: string): Promise<{
  recipient: 'patient' | 'guardian';
  email: string;
  isDependent: boolean;
  guardianInfo?: GuardianInfo;
  patientInfo: PatientInfo;
}> {
  // Obtener información del paciente
  const patientData = await db
    .select({
      id: user.id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      dateOfBirth: user.dateOfBirth,
    })
    .from(user)
    .where(eq(user.id, patientId))
    .limit(1);

  if (patientData.length === 0) {
    throw new Error('Paciente no encontrado');
  }

  const patient = patientData[0] as PatientInfo;

  // Verificar si es dependiente
  if (isPatientDependent(patient)) {
    // Es dependiente, enviar al guardián
    const guardian = await getPrimaryGuardian(patientId);
    
    if (!guardian || !guardian.email) {
      throw new Error('No se encontró guardián con email válido para el paciente dependiente');
    }

    return {
      recipient: 'guardian',
      email: guardian.email,
      isDependent: true,
      guardianInfo: guardian,
      patientInfo: patient,
    };
  } else {
    // Es paciente independiente
    if (!patient.email) {
      throw new Error('Paciente no tiene email válido');
    }

    return {
      recipient: 'patient',
      email: patient.email,
      isDependent: false,
      patientInfo: patient,
    };
  }
}

// Función principal para enviar pre-checkin
export async function sendPreCheckinEmail(
  appointmentId: string,
  appointmentInfo: AppointmentInfo,
  patientId: string,
  isSameDay: boolean = false
): Promise<{ success: boolean; error?: string }> {
  try {
    // Determinar quién debe recibir el email
    const recipientInfo = await determinePreCheckinRecipient(patientId);
    
    // Generar token único
    const preCheckinToken = generatePreCheckinToken();
    
    // Actualizar la cita con el token
    await db
      .update(appointments)
      .set({
        preCheckinToken,
        preCheckinSent: true,
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId));

    // Crear el link de pre-checkin
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const preCheckinLink = `${baseUrl}/pre-checkin/${appointmentId}/${preCheckinToken}${
      recipientInfo.isDependent ? `?for=${recipientInfo.guardianInfo?.id}` : ''
    }`;

    // Formatear fecha y hora
    const appointmentDate = appointmentInfo.scheduledDate.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    
    const appointmentTime = appointmentInfo.startTime.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
    });

    // Preparar datos del email usando el nuevo sistema
    const emailParams = {
      patientName: `${recipientInfo.patientInfo.firstName} ${recipientInfo.patientInfo.lastName}`,
      doctorName: `${appointmentInfo.doctorFirstName} ${appointmentInfo.doctorLastName}`,
      appointmentDate,
      appointmentTime,
      preCheckinLink,
      consultoryName: appointmentInfo.consultoryName || 'Consultorio Médico',
      isDependent: recipientInfo.isDependent,
      guardianName: recipientInfo.isDependent && recipientInfo.guardianInfo
        ? `${recipientInfo.guardianInfo.firstName} ${recipientInfo.guardianInfo.lastName}`
        : undefined,
      relationship: recipientInfo.guardianInfo?.relationship,
    };

    // Enviar email usando el nuevo sistema unificado
    const emailResult = await sendPatientEmail(
      'precheckin_invitation',
      recipientInfo.email,
      emailParams,
      {
        patientId,
        appointmentId,
        flowType: isSameDay ? 'precheckin_urgent' : 'precheckin_normal'
      }
    );

    if (!emailResult.success) {
      console.error('Error sending pre-checkin email:', emailResult.error);
      return { success: false, error: 'Error enviando email' };
    }

    console.log('Pre-checkin email sent successfully:', {
      appointmentId,
      recipient: recipientInfo.recipient,
      email: recipientInfo.email,
      isDependent: recipientInfo.isDependent,
    });

    return { success: true };

  } catch (error) {
    console.error('Error in sendPreCheckinEmail:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}

// Función para enviar recordatorios automáticos
export async function sendAppointmentReminder(
  appointmentId: string,
  appointmentInfo: AppointmentInfo,
  patientId: string,
  reminderType: '48h' | '24h'
): Promise<{ success: boolean; error?: string }> {
  try {
    // Determinar quién debe recibir el email
    const recipientInfo = await determinePreCheckinRecipient(patientId);
    
    // Formatear fecha y hora
    const appointmentDate = appointmentInfo.scheduledDate.toLocaleDateString('es-ES', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
    
    const appointmentTime = appointmentInfo.startTime.toLocaleTimeString('es-ES', {
      hour: '2-digit',
      minute: '2-digit',
    });

    // Generar enlace de check-in
    const checkInLink = generateCheckInLink(appointmentInfo.id);

    // Preparar parámetros del email
    const emailParams = {
      patientName: `${recipientInfo.patientInfo.firstName} ${recipientInfo.patientInfo.lastName}`,
      doctorName: `${appointmentInfo.doctorFirstName} ${appointmentInfo.doctorLastName}`,
      appointmentDate,
      appointmentTime,
      consultoryName: appointmentInfo.consultoryName || 'Consultorio Médico',
    };

    // Enviar email usando el nuevo sistema
    const eventType = reminderType === '48h' ? 'appointment_reminder_48h' : 'appointment_reminder_24h';
    const emailResult = await sendPatientEmail(
      eventType,
      recipientInfo.email,
      emailParams,
      {
        patientId,
        appointmentId,
        flowType: 'appointment_reminder'
      }
    );

    if (!emailResult.success) {
      return { success: false, error: 'Error enviando recordatorio' };
    }

    // Actualizar estado del recordatorio
    const updateField = reminderType === '48h' ? 'reminderSent48h' : 'reminderSent24h';
    await db
      .update(appointments)
      .set({
        [updateField]: true,
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId));

    return { success: true };

  } catch (error) {
    console.error('Error sending reminder:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Error desconocido'
    };
  }
}