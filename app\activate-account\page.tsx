'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useSignUp, useSignIn } from '@clerk/nextjs';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  CheckCircle, 
  AlertCircle, 
  RefreshCw, 
  User, 
  Lock, 
  Mail,
  ArrowRight
} from 'lucide-react';
import { toast } from 'sonner';

interface PatientData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
}

function ActivateAccountContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { signUp, setActive } = useSignUp();
  const { signIn } = useSignIn();
  
  const [loading, setLoading] = useState(true);
  const [step, setStep] = useState<'validating' | 'create_account' | 'success' | 'error'>('validating');
  const [patientData, setPatientData] = useState<PatientData | null>(null);
  const [error, setError] = useState<string>('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [creating, setCreating] = useState(false);

  const token = searchParams.get('token');

  useEffect(() => {
    if (token) {
      validateToken();
    } else {
      setError('Token de activación no válido');
      setStep('error');
      setLoading(false);
    }
  }, [token]);

  const validateToken = async () => {
    try {
      const response = await fetch(`/api/patients/activate/validate?token=${token}`);
      const data = await response.json();

      if (response.ok && data.success) {
        setPatientData(data.data);
        setStep('create_account');
      } else {
        setError(data.error || 'Token inválido o expirado');
        setStep('error');
      }
    } catch (error) {
      setError('Error al validar token');
      setStep('error');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateAccount = async () => {
    if (!patientData) return;
    
    if (password.length < 8) {
      toast.error('La contraseña debe tener al menos 8 caracteres');
      return;
    }

    if (password !== confirmPassword) {
      toast.error('Las contraseñas no coinciden');
      return;
    }

    setCreating(true);
    try {
      // 1. Crear cuenta en Clerk
      const signUpResult = await signUp?.create({
        emailAddress: patientData.email,
        password: password,
        firstName: patientData.firstName,
        lastName: patientData.lastName,
      });

      if (!signUpResult?.createdUserId) {
        throw new Error('No se pudo crear la cuenta');
      }

      // 2. Activar cuenta en nuestro sistema
      const activateResponse = await fetch('/api/patients/activate/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          token,
          clerkUserId: signUpResult.createdUserId,
        }),
      });

      const activateData = await activateResponse.json();

      if (!activateResponse.ok) {
        throw new Error(activateData.error || 'Error al activar cuenta');
      }

      // 3. Iniciar sesión automáticamente
      if (signUpResult.status === 'complete' && setActive) {
        await setActive({ session: signUpResult.createdSessionId });
        setStep('success');
        
        // Redirigir después de unos segundos
        setTimeout(() => {
          router.push('/dashboard');
        }, 3000);
      }

    } catch (error) {
      console.error('Error creating account:', error);
      toast.error(error instanceof Error ? error.message : 'Error al crear cuenta');
    } finally {
      setCreating(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Validando invitación...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        
        {/* Header */}
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900">
            Activar Cuenta
          </h2>
          <p className="mt-2 text-gray-600">
            Completa tu registro para acceder al portal médico
          </p>
        </div>

        {step === 'create_account' && patientData && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                Datos del Paciente
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Datos del paciente */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-sm space-y-1">
                  <div><strong>Nombre:</strong> {patientData.firstName} {patientData.lastName}</div>
                  <div><strong>Email:</strong> {patientData.email}</div>
                  {patientData.phone && (
                    <div><strong>Teléfono:</strong> {patientData.phone}</div>
                  )}
                </div>
              </div>

              {/* Formulario de contraseña */}
              <div className="space-y-4">
                <div>
                  <Label htmlFor="password">Contraseña</Label>
                  <div className="relative mt-1">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="password"
                      type="password"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      placeholder="Mínimo 8 caracteres"
                      className="pl-10"
                      minLength={8}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="confirmPassword">Confirmar Contraseña</Label>
                  <div className="relative mt-1">
                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="confirmPassword"
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                      placeholder="Repetir contraseña"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              <Button
                onClick={handleCreateAccount}
                disabled={creating || !password || !confirmPassword}
                className="w-full"
              >
                {creating ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Creando cuenta...
                  </>
                ) : (
                  <>
                    <ArrowRight className="h-4 w-4 mr-2" />
                    Activar Cuenta
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        )}

        {step === 'success' && (
          <Card>
            <CardContent className="text-center py-8">
              <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                ¡Cuenta Activada!
              </h3>
              <p className="text-gray-600 mb-4">
                Tu cuenta ha sido creada exitosamente. Serás redirigido al portal en unos segundos.
              </p>
              <Button onClick={() => router.push('/dashboard')}>
                Ir al Portal
              </Button>
            </CardContent>
          </Card>
        )}

        {step === 'error' && (
          <Card>
            <CardContent className="text-center py-8">
              <AlertCircle className="h-16 w-16 text-red-600 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                Error de Activación
              </h3>
              <Alert className="mb-4">
                <AlertDescription>
                  {error}
                </AlertDescription>
              </Alert>
              <p className="text-gray-600 mb-4">
                Por favor contacta al consultorio para obtener una nueva invitación.
              </p>
              <Button 
                variant="outline" 
                onClick={() => router.push('/')}
              >
                Volver al Inicio
              </Button>
            </CardContent>
          </Card>
        )}

      </div>
    </div>
  );
}

export default function ActivateAccountPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    }>
      <ActivateAccountContent />
    </Suspense>
  );
}