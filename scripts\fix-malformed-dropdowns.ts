/**
 * Script para corregir menús mal formateados y agregar separadores faltantes
 */

import fs from 'fs';

// Lista de archivos que sabemos que tienen problemas
const problematicFiles = [
  'app/(dashboard)/dashboard/admin/catalogs/currencies/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/departments/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/doctor-service-prices/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/media-sources/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/medical-specialties/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/medications/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/municipalities/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/non-pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/occupations/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/relationships/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/symptoms/page.tsx',
  'app/(dashboard)/dashboard/admin/users/page.tsx'
];

function fixFile(filePath: string): boolean {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let updated = false;
    
    // 1. Corregir etiquetas duplicadas malformadas
    const malformedPattern = /<DropdownMenuContent<DropdownMenuContent align="end">/g;
    if (content.match(malformedPattern)) {
      content = content.replace(malformedPattern, '<DropdownMenuContent align="end">');
      updated = true;
      console.log(`  ✓ Corregido formato malformado`);
    }
    
    // 2. Buscar y agregar separadores faltantes
    // Patrón: después de "Editar" y antes de acciones destructivas
    const needsSeparatorPattern = /(Editar\s*<\/DropdownMenuItem>\s*)(<DropdownMenuItem[^>]*onClick=\{[^}]*(?:toggle|Toggle|Desactivar|Activar)[^}]*\})/g;
    
    content = content.replace(needsSeparatorPattern, (match, before, after) => {
      if (!match.includes('DropdownMenuSeparator')) {
        updated = true;
        console.log(`  ✓ Agregado separador`);
        return before + '\n                              <DropdownMenuSeparator />\n                              ' + after;
      }
      return match;
    });
    
    // También para versión móvil (menos indentación)
    const mobileSeparatorPattern = /(Editar\s*<\/DropdownMenuItem>\s*)(<DropdownMenuItem[^>]*onClick=\{[^}]*(?:toggle|Toggle|Desactivar|Activar)[^}]*\})/g;
    
    content = content.replace(mobileSeparatorPattern, (match, before, after) => {
      if (!match.includes('DropdownMenuSeparator') && before.includes('            ')) {
        updated = true;
        console.log(`  ✓ Agregado separador (móvil)`);
        return before + '\n                          <DropdownMenuSeparator />\n                          ' + after;
      }
      return match;
    });
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf-8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`  ❌ Error: ${error}`);
    return false;
  }
}

async function main() {
  console.log('🔧 Corrigiendo menús mal formateados...\n');
  
  let totalFixed = 0;
  
  for (const file of problematicFiles) {
    console.log(`📄 ${file}`);
    
    if (fs.existsSync(file)) {
      if (fixFile(file)) {
        console.log(`  ✅ Archivo corregido`);
        totalFixed++;
      } else {
        console.log(`  ⏭️  Sin cambios necesarios`);
      }
    } else {
      console.log(`  ⚠️  Archivo no encontrado`);
    }
    console.log('');
  }
  
  console.log(`📊 Resumen: ${totalFixed} archivos corregidos de ${problematicFiles.length} procesados`);
}

main().catch(console.error);