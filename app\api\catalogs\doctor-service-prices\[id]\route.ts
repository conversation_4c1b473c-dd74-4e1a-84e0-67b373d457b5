import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices, medicalServices, user } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/catalogs/doctor-service-prices/[id] - Obtener precio específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const data = await db
      .select({
        id: doctorServicePrices.id,
        doctorId: doctorServicePrices.doctorId,
        serviceId: doctorServicePrices.serviceId,
        customPrice: doctorServicePrices.customPrice,
        currency: doctorServicePrices.currency,
        isActive: doctorServicePrices.isActive,
        effectiveFrom: doctorServicePrices.effectiveFrom,
        effectiveUntil: doctorServicePrices.effectiveUntil,
        notes: doctorServicePrices.notes,
        createdAt: doctorServicePrices.createdAt,
        updatedAt: doctorServicePrices.updatedAt,
        // Datos del doctor
        doctorName: user.firstName,
        doctorLastName: user.lastName,
        doctorEmail: user.email,
        // Datos del servicio
        serviceName: medicalServices.name,
        serviceCategory: medicalServices.category,
        serviceBasePrice: medicalServices.basePrice,
      })
      .from(doctorServicePrices)
      .leftJoin(user, eq(doctorServicePrices.doctorId, user.id))
      .leftJoin(medicalServices, eq(doctorServicePrices.serviceId, medicalServices.id))
      .where(eq(doctorServicePrices.id, id))
      .limit(1);

    if (!data.length) {
      return NextResponse.json(
        { error: 'Precio personalizado no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data[0],
    });

  } catch (error: any) {
    console.error('Error fetching doctor service price:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/catalogs/doctor-service-prices/[id] - Eliminar precio personalizado
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'logical'; // logical o physical

    // Verificar que el precio existe
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(eq(doctorServicePrices.id, id))
      .limit(1);

    if (!existingPrice.length) {
      return NextResponse.json(
        { error: 'Precio personalizado no encontrado' },
        { status: 404 }
      );
    }

    if (type === 'logical') {
      // Eliminación lógica - marcar como inactivo
      await db
        .update(doctorServicePrices)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(doctorServicePrices.id, id));

      return NextResponse.json({
        success: true,
        message: 'Precio personalizado desactivado exitosamente',
      });

    } else if (type === 'physical') {
      // Eliminación física - borrar registro completamente
      await db
        .delete(doctorServicePrices)
        .where(eq(doctorServicePrices.id, id));

      return NextResponse.json({
        success: true,
        message: 'Precio personalizado eliminado permanentemente',
      });

    } else {
      return NextResponse.json(
        { error: 'Tipo de eliminación no válido. Use "logical" o "physical"' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error deleting doctor service price:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}