import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

export async function GET() {
  try {
    const result = await db.execute(sql`
      SELECT id, name, address, phone, email, "businessHours", active, "createdAt", "updatedAt"
      FROM consultories 
      WHERE active = true 
      ORDER BY name
    `);
    
    const consultoriesData = result.rows;

    return NextResponse.json({
      success: true,
      data: consultoriesData
    });
  } catch (error) {
    console.error('Error fetching consultories:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch consultories' 
      },
      { status: 500 }
    );
  }
} 