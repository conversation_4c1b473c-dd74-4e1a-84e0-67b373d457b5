'use client';

import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface DurationPickerProps {
  value: string; // Duration in minutes as string
  onChange: (duration: string) => void;
  disabled?: boolean;
  className?: string;
}

export function DurationPicker({ value, onChange, disabled = false, className }: DurationPickerProps) {
  const durations = [
    { value: '15', label: '15 min', description: 'Consulta rápida' },
    { value: '30', label: '30 min', description: 'Consulta estándar', isDefault: true },
    { value: '45', label: '45 min', description: 'Consulta extendida' },
    { value: '60', label: '1 hora', description: 'Evaluación completa' },
    { value: '90', label: '1h 30m', description: 'Procedimiento' },
    { value: '120', label: '2 horas', description: 'Cirugía menor' }
  ];

  if (disabled) {
    const selectedDuration = durations.find(d => d.value === value);
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        {selectedDuration?.label || `${value} minutos`}
      </div>
    );
  }

  return (
    <div className={cn('space-y-3', className)}>
      <Label className="text-sm font-medium text-gray-700">
        <Clock className="h-4 w-4 inline mr-1" />
        Duración de la cita
      </Label>
      
      <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
        {durations.map((duration) => (
          <Button
            key={duration.value}
            type="button"
            variant={value === duration.value ? "default" : "outline"}
            onClick={() => onChange(duration.value)}
            className={cn(
              "h-auto p-3 flex flex-col items-start text-left relative",
              value === duration.value && "bg-blue-500 text-white hover:bg-blue-600",
              duration.isDefault && value !== duration.value && "ring-2 ring-blue-200"
            )}
          >
            <div className="font-medium text-sm">
              {duration.label}
            </div>
            <div className={cn(
              "text-xs mt-1 opacity-75",
              value === duration.value ? "text-blue-100" : "text-gray-500"
            )}>
              {duration.description}
            </div>
            
            {duration.isDefault && value !== duration.value && (
              <div className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs px-1 py-0.5 rounded-full text-[10px]">
                Recomendado
              </div>
            )}
          </Button>
        ))}
      </div>
      
      {/* Información adicional */}
      <div className="text-xs text-gray-500 mt-2">
        💡 La duración recomendada para consultas generales es de 30 minutos
      </div>
    </div>
  );
}