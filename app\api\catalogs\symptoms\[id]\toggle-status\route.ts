import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { symptoms } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/catalogs/symptoms/[id]/toggle-status - Activar/Desactivar síntoma
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Obtener el síntoma actual
    const currentSymptom = await db
      .select()
      .from(symptoms)
      .where(eq(symptoms.id, id))
      .limit(1);

    if (!currentSymptom.length) {
      return NextResponse.json(
        { error: 'Síntoma no encontrado' },
        { status: 404 }
      );
    }

    const symptom = currentSymptom[0];
    const newStatus = !symptom.isActive;

    // Actualizar estado
    await db
      .update(symptoms)
      .set({
        isActive: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(symptoms.id, id));

    return NextResponse.json({
      success: true,
      message: `Síntoma ${newStatus ? 'activado' : 'desactivado'} exitosamente`,
      data: {
        id,
        isActive: newStatus,
      },
    });

  } catch (error: any) {
    console.error('Error toggling symptom status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}