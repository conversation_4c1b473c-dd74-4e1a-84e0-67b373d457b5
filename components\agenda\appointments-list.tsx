'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { 
  CheckCircle, 
  Clock, 
  Calendar,
  User,
  X,
  AlertCircle,
  RefreshCw,
  Stethoscope,
  Home,
  DollarSign,
  FileText
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeInConsultoryTimezone, formatInConsultoryTimezone, formatLocalTimeFromUTC, formatLocalDateFromUTC } from '@/lib/timezone-utils';
import { toast } from 'sonner';
import { cn, formatDate } from '@/lib/utils';
import { AppointmentActionsMenu } from './appointment-actions-menu';

interface AppointmentsListProps {
  appointments: any[];
  loading: boolean;
  onEdit?: (appointment: any) => void;
  onView?: (appointment: any) => void;
  onConfirm?: (appointment: any) => void;
  onCancel?: (appointment: any) => void;
  onDelete?: (appointment: any) => void;
  onCheckIn?: (appointment: any) => void;
  onNoShow?: (appointment: any) => void;
  onComplete?: (appointment: any) => void;
  onStartConsultation?: (appointment: any) => void;
  onViewConsultation?: (appointment: any) => void;
  onRevertNoShow?: (appointment: any) => void;
  onViewPreCheckin?: (appointment: any) => void;
  onRefresh?: () => void;
  showDoctor?: boolean;
  showAuditInfo?: boolean;
  userRole?: 'doctor' | 'assistant' | 'admin';
}

const statusConfig = {
  scheduled: {
    label: 'Programada',
    icon: Calendar,
    color: 'bg-blue-100 text-blue-800 border-blue-300',
  },
  pending_confirmation: {
    label: 'Pendiente de Confirmación',
    icon: AlertCircle,
    color: 'bg-orange-100 text-orange-800 border-orange-300',
  },
  confirmed: {
    label: 'Confirmada',
    icon: CheckCircle,
    color: 'bg-[#50bed2]/10 text-[#50bed2] border-[#50bed2]/30',
  },
  checked_in: {
    label: 'Paciente llegó',
    icon: User,
    color: 'bg-teal-100 text-teal-800 border-teal-300',
  },
  in_progress: {
    label: 'En consulta',
    icon: Clock,
    color: 'bg-purple-100 text-purple-800 border-purple-300',
  },
  completed: {
    label: 'Completada',
    icon: CheckCircle,
    color: 'bg-gray-100 text-gray-800 border-gray-300',
  },
  cancelled: {
    label: 'Cancelada',
    icon: X,
    color: 'bg-red-100 text-red-800 border-red-300',
  },
  no_show: {
    label: 'No asistió',
    icon: X,
    color: 'bg-pink-100 text-pink-800 border-pink-300',
  },
};

export function AppointmentsList({ 
  appointments, 
  loading, 
  onEdit,
  onView,
  onConfirm,
  onCancel,
  onDelete,
  onCheckIn,
  onNoShow,
  onComplete, 
  onStartConsultation,
  onViewConsultation,
  onRevertNoShow,
  onViewPreCheckin,
  onRefresh,
  showDoctor = false,
  showAuditInfo = false,
  userRole = 'assistant'
}: AppointmentsListProps) {
  const [updatingStatus, setUpdatingStatus] = useState<string | null>(null);

  const handleConfirm = (appointment: any) => {
    if (onConfirm) {
      onConfirm(appointment);
    }
  };

  const handleCancel = (appointment: any) => {
    if (onCancel) {
      onCancel(appointment);
    }
  };

  const handleDelete = (appointment: any) => {
    if (onDelete) {
      onDelete(appointment);
    }
  };

  const handleComplete = (appointment: any) => {
    if (onComplete) {
      onComplete(appointment);
    }
  };

  const handleStartConsultation = (appointment: any) => {
    if (onStartConsultation) {
      onStartConsultation(appointment);
    }
  };

  const handleCheckIn = (appointment: any) => {
    if (onCheckIn) {
      onCheckIn(appointment);
    }
  };

  const handleNoShow = (appointment: any) => {
    if (onNoShow) {
      onNoShow(appointment);
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
            <span className="ml-2 text-gray-500">Cargando citas...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (appointments.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-gray-500">
            No se encontraron citas para los filtros seleccionados
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center justify-between">
          <span>Lista de Citas</span>
          <Badge variant="outline">{appointments.length} citas</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Vista desktop: tabla */}
        <div className="hidden lg:block overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Fecha y Hora</TableHead>
                <TableHead>Paciente</TableHead>
                {showDoctor && <TableHead>Médico</TableHead>}
                <TableHead>Servicio</TableHead>
                <TableHead>Consultorio</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead>Precio</TableHead>
                {showAuditInfo && <TableHead>Auditoría</TableHead>}
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {appointments.map((appointment) => {
                // Debug: Log status mapping
                console.log(`🎯 Mapping status for appointment ${appointment.id}: "${appointment.status}" -> `, statusConfig[appointment.status] ? 'FOUND' : 'NOT FOUND (using scheduled fallback)');
                
                const statusInfo = statusConfig[appointment.status] || statusConfig.scheduled;
                const StatusIcon = statusInfo.icon;
                
                return (
                  <TableRow key={appointment.id}>
                    <TableCell>
                      <div className="space-y-1">
                        <div className="font-medium">
                          {formatDate(appointment.scheduledDate)}
                        </div>
                        <div className="text-sm text-gray-600">
                          {formatLocalTimeFromUTC(appointment.startTime)} -
                          {formatLocalTimeFromUTC(appointment.endTime)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <div>
                          <div className="font-medium">
                            {appointment.patientFirstName} {appointment.patientLastName}
                          </div>
                          <div className="text-sm text-gray-600">
                            {appointment.patientEmail}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    {showDoctor && (
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Stethoscope className="h-4 w-4 text-gray-400" />
                          <div>
                            <div className="font-medium">
                              Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    )}
                    <TableCell>
                      <div className="text-sm">
                        {appointment.serviceName || appointment.title}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Home className="h-4 w-4 text-gray-400" />
                        <span className="text-sm">{appointment.consultoryName}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge className={cn('gap-1', statusInfo.color)}>
                        <StatusIcon className="h-3 w-3" />
                        {statusInfo.label}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <DollarSign className="h-4 w-4 text-gray-400" />
                        <span className="font-medium">
                          {appointment.currency} {appointment.estimatedPrice || '0.00'}
                        </span>
                      </div>
                    </TableCell>
                    {showAuditInfo && (
                      <TableCell>
                        <div className="text-xs text-gray-500">
                          <div>Creado: {formatInConsultoryTimezone(appointment.createdAt, 'dd/MM HH:mm')}</div>
                          {appointment.updatedAt && (
                            <div>Actualizado: {formatInConsultoryTimezone(appointment.updatedAt, 'dd/MM HH:mm')}</div>
                          )}
                        </div>
                      </TableCell>
                    )}
                    <TableCell className="text-right">
                      <AppointmentActionsMenu
                        appointment={appointment}
                        onView={onView}
                        onEdit={onEdit}
                        onConfirm={onConfirm}
                        onCancel={onCancel}
                        onDelete={onDelete}
                        onCheckIn={onCheckIn}
                        onNoShow={onNoShow}
                        onStart={onStartConsultation}
                        onComplete={onComplete}
                        onRevertNoShow={onRevertNoShow}
                        onViewPreCheckin={onViewPreCheckin}
                        onViewConsultation={onViewConsultation}
                        userRole={userRole}
                        className="opacity-100"
                      />
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
        
        {/* Vista mobile: cards */}
        <div className="lg:hidden space-y-4">
          {appointments.map((appointment) => {
            // Debug: Log status mapping for mobile view
            console.log(`📱 Mobile mapping status for appointment ${appointment.id}: "${appointment.status}" -> `, statusConfig[appointment.status] ? 'FOUND' : 'NOT FOUND (using scheduled fallback)');
            
            const statusInfo = statusConfig[appointment.status] || statusConfig.scheduled;
            const StatusIcon = statusInfo.icon;
            
            return (
              <Card key={appointment.id} className="p-4">
                <div className="space-y-3">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="font-medium">
                        {appointment.patientFirstName} {appointment.patientLastName}
                      </div>
                      <div className="text-sm text-gray-600">
                        {formatDate(appointment.scheduledDate)} -
                        {formatLocalTimeFromUTC(appointment.startTime)}
                      </div>
                    </div>
                    <Badge className={cn('gap-1', statusInfo.color)}>
                      <StatusIcon className="h-3 w-3" />
                      {statusInfo.label}
                    </Badge>
                  </div>
                  
                  {showDoctor && (
                    <div className="text-sm text-gray-600">
                      Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                    </div>
                  )}
                  
                  <div className="flex items-center justify-between text-sm">
                    <span>{appointment.consultoryName}</span>
                    <span className="font-medium">
                      {appointment.currency} {appointment.estimatedPrice || '0.00'}
                    </span>
                  </div>
                  
                  <div className="flex justify-end gap-2">
                    {onView && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => onView(appointment)}
                      >
                        Ver
                      </Button>
                    )}
                    {onEdit && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => onEdit(appointment)}
                      >
                        Editar
                      </Button>
                    )}
                    {appointment.status === 'scheduled' && onConfirm && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleConfirm(appointment)}
                        className="text-green-600"
                      >
                        Confirmar
                      </Button>
                    )}
                    {appointment.status === 'confirmed' && onStartConsultation && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleStartConsultation(appointment)}
                        className="text-purple-600"
                      >
                        <Stethoscope className="h-3 w-3 mr-1" />
                        Iniciar Consulta
                      </Button>
                    )}
                    {appointment.status === 'in_progress' && onComplete && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleComplete(appointment)}
                        className="text-blue-600"
                      >
                        <FileText className="h-3 w-3 mr-1" />
                        Completar
                      </Button>
                    )}
                    {['scheduled', 'confirmed'].includes(appointment.status) && onCancel && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => handleCancel(appointment)}
                        className="text-red-600"
                      >
                        Cancelar
                      </Button>
                    )}
                  </div>
                </div>
              </Card>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}