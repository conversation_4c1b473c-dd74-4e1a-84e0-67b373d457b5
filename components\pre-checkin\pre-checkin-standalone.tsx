'use client';

import { PreCheckinFormImproved } from '@/components/pre-checkin/pre-checkin-form-improved';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, User, Shield } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { translateRelationship } from '@/lib/utils';

interface PreCheckinStandaloneProps {
  appointmentData: {
    id: string;
    title: string | null;
    scheduledDate: Date;
    startTime: Date;
    endTime: Date;
    status: string;
    preCheckinCompleted: boolean | null;
    preCheckinCompletedAt: Date | null;
    patientId: string | null;
    patientFirstName: string | null;
    patientLastName: string | null;
    patientEmail: string | null;
    doctorFirstName: string | null;
    doctorLastName: string | null;
    isDependent: boolean;
    guardianInfo?: {
      guardianId: string;
      relationship: string;
      guardianFirstName: string | null;
      guardianLastName: string | null;
      guardianEmail: string | null;
    } | null;
  };
  appointmentDate: string;
  appointmentTime: string;
  appointmentEndTime: string;
  isAlreadyCompleted: boolean;
}

export function PreCheckinStandalone({
  appointmentData,
  appointmentDate,
  appointmentTime,
  appointmentEndTime,
  isAlreadyCompleted,
}: PreCheckinStandaloneProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-8 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            {appointmentData.isDependent ? '👨‍👩‍👧‍👦 Pre-checkin Familiar' : '📋 Pre-checkin Médico'}
          </h1>
          <p className="text-gray-600">
            {appointmentData.isDependent 
              ? `Completando pre-checkin para ${appointmentData.patientFirstName} ${appointmentData.patientLastName}`
              : 'Confirma tu asistencia y actualiza tu información médica'
            }
          </p>
        </div>

        {/* Estado del pre-checkin */}
        {isAlreadyCompleted ? (
          <Card className="mb-8 border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center justify-center space-x-2 text-green-700">
                <Shield className="h-6 w-6" />
                <span className="font-semibold">Pre-checkin ya completado</span>
              </div>
              <p className="text-center text-sm text-green-600 mt-2">
                Completado el {format(new Date(appointmentData.preCheckinCompletedAt!), 'dd/MM/yyyy HH:mm', { locale: es })}
              </p>
            </CardContent>
          </Card>
        ) : null}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Información de la cita */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  <span>Información de la Cita</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Paciente */}
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">
                      {appointmentData.patientFirstName} {appointmentData.patientLastName}
                    </p>
                    <p className="text-sm text-gray-500">Paciente</p>
                  </div>
                </div>

                {/* Guardián (si aplica) */}
                {appointmentData.isDependent && appointmentData.guardianInfo && (
                  <div className="flex items-center space-x-3 bg-amber-50 p-3 rounded-lg">
                    <Shield className="h-4 w-4 text-amber-600" />
                    <div>
                      <p className="font-medium text-amber-900">
                        {appointmentData.guardianInfo.guardianFirstName} {appointmentData.guardianInfo.guardianLastName}
                      </p>
                      <p className="text-sm text-amber-700 capitalize">
                        {translateRelationship(appointmentData.guardianInfo.relationship)}
                      </p>
                    </div>
                  </div>
                )}

                {/* Doctor */}
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900">
                      Dr. {appointmentData.doctorFirstName} {appointmentData.doctorLastName}
                    </p>
                    <p className="text-sm text-gray-500">Médico tratante</p>
                  </div>
                </div>

                {/* Fecha y hora */}
                <div className="flex items-center space-x-3">
                  <Clock className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="font-medium text-gray-900 capitalize">{appointmentDate}</p>
                    <p className="text-sm text-gray-500">{appointmentTime} - {appointmentEndTime}</p>
                  </div>
                </div>

                {/* Estado */}
                <div className="flex items-center space-x-3">
                                  <Badge variant={appointmentData.status === 'confirmed' ? 'default' : 'secondary'}>
                  {appointmentData.status === 'scheduled' && 'Programada'}
                  {appointmentData.status === 'pending_confirmation' && 'Pendiente de Confirmación'}
                  {appointmentData.status === 'confirmed' && 'Confirmada'}
                  {appointmentData.status === 'checked_in' && 'Paciente llegó'}
                  {appointmentData.status === 'in_progress' && 'En consulta'}
                  {appointmentData.status === 'completed' && 'Completada'}
                  {appointmentData.status === 'cancelled' && 'Cancelada'}
                  {appointmentData.status === 'no_show' && 'No asistió'}
                </Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Formulario de pre-checkin */}
          <div className="lg:col-span-2">
            <PreCheckinFormImproved 
              appointmentData={appointmentData}
              isCompleted={isAlreadyCompleted}
              showUserContext={false}
            />
          </div>
        </div>
      </div>
    </div>
  );
}