import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  medicalRecords,
  user
} from '@/db/schema';
import { eq, and, desc, ne } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    patientId: string;
  }>;
}

// GET - Obtener consultas anteriores de un paciente específico
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { patientId } = await params;
    const { searchParams } = new URL(request.url);
    const currentConsultationId = searchParams.get('exclude'); // Excluir consulta actual
    const limit = parseInt(searchParams.get('limit') || '3'); // Máximo 3 consultas por defecto
    
    console.log('Fetching patient consultations:', { patientId, currentConsultationId, limit });

    // Obtener consultas anteriores del paciente (excluyendo la actual)
    let query = db
      .select({
        id: medicalConsultations.id,
        consultationDate: medicalConsultations.consultationDate,
        diagnoses: medicalConsultations.diagnoses,
        prescriptions: medicalConsultations.prescriptions,
        vitalSigns: medicalConsultations.vitalSigns,
        recommendations: medicalConsultations.recommendations,
        status: medicalConsultations.status,
        createdAt: medicalConsultations.createdAt,
        // Campos de próxima cita
        nextAppointmentValue: medicalConsultations.nextAppointmentValue,
        nextAppointmentUnit: medicalConsultations.nextAppointmentUnit,
        nextAppointmentType: medicalConsultations.nextAppointmentType,
        nextAppointmentNotes: medicalConsultations.nextAppointmentNotes,
      })
      .from(medicalConsultations)
      .leftJoin(medicalRecords, eq(medicalConsultations.medicalRecordId, medicalRecords.id))
      .where(
        and(
          eq(medicalRecords.patientId, patientId),
          eq(medicalConsultations.status, 'completed') // Solo consultas completadas
        )
      )
      .orderBy(desc(medicalConsultations.consultationDate))
      .limit(limit);

    // Excluir consulta actual si se proporciona
    if (currentConsultationId) {
      query = query.where(
        and(
          eq(medicalRecords.patientId, patientId),
          eq(medicalConsultations.status, 'completed'),
          ne(medicalConsultations.id, currentConsultationId)
        )
      );
    }

    const consultations = await query;

    console.log('Found consultations:', consultations.length);

    // Procesar y resumir las consultas
    const consultationSummaries = consultations.map(consultation => {
      // Obtener diagnóstico principal
      const primaryDiagnosis = Array.isArray(consultation.diagnoses) 
        ? consultation.diagnoses.find((dx: any) => dx.type === 'primary') || consultation.diagnoses[0]
        : null;

      // Obtener medicamento principal
      const primaryMedication = Array.isArray(consultation.prescriptions) && consultation.prescriptions.length > 0
        ? consultation.prescriptions[0]
        : null;

      // Obtener signos vitales clave
      const vitalSigns = consultation.vitalSigns || {};
      const bloodPressure = vitalSigns.bloodPressure || null;
      const weight = vitalSigns.weight || null;
      const temperature = vitalSigns.temperature || null;

      // Calcular días desde la consulta
      const daysSince = Math.floor((new Date().getTime() - new Date(consultation.consultationDate).getTime()) / (1000 * 60 * 60 * 24));

      return {
        id: consultation.id,
        date: consultation.consultationDate,
        daysSince,
        diagnosis: primaryDiagnosis?.description || 'Sin diagnóstico registrado',
        diagnosisCode: primaryDiagnosis?.code || null,
        medication: primaryMedication ? {
          name: primaryMedication.medication || 'Medicamento no especificado',
          dose: primaryMedication.dose || '',
          frequency: primaryMedication.frequency || ''
        } : null,
        vitalSigns: {
          bloodPressure,
          weight,
          temperature
        },
        alertNote: consultation.recommendations ? 
          (consultation.recommendations.length > 100 ? 
            consultation.recommendations.substring(0, 100) + '...' : 
            consultation.recommendations
          ) : null,
        nextAppointment: consultation.nextAppointmentValue ? {
          value: consultation.nextAppointmentValue,
          unit: consultation.nextAppointmentUnit,
          type: consultation.nextAppointmentType,
          notes: consultation.nextAppointmentNotes
        } : null
      };
    });

    return NextResponse.json({
      success: true,
      data: consultationSummaries
    });

  } catch (error) {
    console.error('Error fetching patient consultations:', error);
    
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}