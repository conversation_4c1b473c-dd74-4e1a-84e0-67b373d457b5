CREATE TABLE "associationCodes" (
	"id" text PRIMARY KEY NOT NULL,
	"code" text NOT NULL,
	"patientId" text NOT NULL,
	"expiresAt" timestamp NOT NULL,
	"usedBy" text,
	"usedAt" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "associationCodes_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "consultories" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"address" text,
	"phone" text,
	"email" text,
	"active" boolean DEFAULT true,
	"description" text,
	"services" jsonb,
	"workingHours" jsonb,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "doctorAssistantRelations" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"assistantId" text NOT NULL,
	"consultoryId" text,
	"permissions" jsonb,
	"active" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "notifications" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"type" text NOT NULL,
	"title" text NOT NULL,
	"message" text NOT NULL,
	"read" boolean DEFAULT false,
	"data" jsonb,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "patientGuardianRelations" (
	"id" text PRIMARY KEY NOT NULL,
	"patientId" text NOT NULL,
	"guardianId" text NOT NULL,
	"relationship" text,
	"isPrimary" boolean DEFAULT false,
	"canMakeDecisions" boolean DEFAULT true,
	"validUntil" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "registrationRequests" (
	"id" text PRIMARY KEY NOT NULL,
	"userId" text NOT NULL,
	"role" text NOT NULL,
	"status" text DEFAULT 'pending',
	"generalData" jsonb,
	"specificData" jsonb,
	"reviewedBy" text,
	"reviewedAt" timestamp,
	"reviewNotes" text,
	"rejectionReason" text,
	"submittedAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "role" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "status" text DEFAULT 'pending';--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "statusReason" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "activatedAt" timestamp;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "activatedBy" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "generalProfile" jsonb;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "roleSpecificData" jsonb;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "documents" jsonb;--> statement-breakpoint
ALTER TABLE "associationCodes" ADD CONSTRAINT "associationCodes_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "associationCodes" ADD CONSTRAINT "associationCodes_usedBy_user_id_fk" FOREIGN KEY ("usedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctorAssistantRelations" ADD CONSTRAINT "doctorAssistantRelations_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctorAssistantRelations" ADD CONSTRAINT "doctorAssistantRelations_assistantId_user_id_fk" FOREIGN KEY ("assistantId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctorAssistantRelations" ADD CONSTRAINT "doctorAssistantRelations_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "notifications" ADD CONSTRAINT "notifications_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patientGuardianRelations" ADD CONSTRAINT "patientGuardianRelations_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patientGuardianRelations" ADD CONSTRAINT "patientGuardianRelations_guardianId_user_id_fk" FOREIGN KEY ("guardianId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD CONSTRAINT "registrationRequests_userId_user_id_fk" FOREIGN KEY ("userId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD CONSTRAINT "registrationRequests_reviewedBy_user_id_fk" FOREIGN KEY ("reviewedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;