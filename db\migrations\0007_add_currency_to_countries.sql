-- Agregar campo currency a la tabla countries
ALTER TABLE "countries" ADD COLUMN IF NOT EXISTS "currency" text;

-- Actualizar valores por defecto para algunos países comunes
UPDATE "countries" SET "currency" = 'GTQ' WHERE "code" = 'GT';
UPDATE "countries" SET "currency" = 'USD' WHERE "code" = 'US';
UPDATE "countries" SET "currency" = 'MXN' WHERE "code" = 'MX';
UPDATE "countries" SET "currency" = 'EUR' WHERE "code" = 'ES';
UPDATE "countries" SET "currency" = 'CRC' WHERE "code" = 'CR';
UPDATE "countries" SET "currency" = 'HNL' WHERE "code" = 'HN';
UPDATE "countries" SET "currency" = 'NIO' WHERE "code" = 'NI';
UPDATE "countries" SET "currency" = 'PAB' WHERE "code" = 'PA';