import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';

async function checkActualRoles() {
  try {
    console.log('🔍 Verificando roles reales en la base de datos...');
    
    // Ver todos los roles únicos
    const uniqueRoles = await db.execute(sql`
      SELECT DISTINCT role, COUNT(*) as count
      FROM user_roles 
      GROUP BY role
      ORDER BY role;
    `);
    
    console.log('📋 Roles encontrados:');
    uniqueRoles.rows.forEach((roleInfo: any) => {
      console.log(`  - "${roleInfo.role}": ${roleInfo.count} usuarios`);
    });
    
    // Ver todos los status únicos
    const uniqueStatus = await db.execute(sql`
      SELECT DISTINCT status, COUNT(*) as count
      FROM user_roles 
      GROUP BY status
      ORDER BY status;
    `);
    
    console.log('\n📊 Status encontrados:');
    uniqueStatus.rows.forEach((statusInfo: any) => {
      console.log(`  - "${statusInfo.status}": ${statusInfo.count} usuarios`);
    });
    
    // Ver usuarios con rol que contiene "doctor" (case insensitive)
    const doctorLikeRoles = await db.execute(sql`
      SELECT ur.role, ur.status, u."firstName", u."lastName", u.email
      FROM user_roles ur
      INNER JOIN "user" u ON ur."userId" = u.id
      WHERE LOWER(ur.role) LIKE '%doctor%'
      ORDER BY ur.role;
    `);
    
    console.log('\n👨‍⚕️ Usuarios con rol que contiene "doctor":');
    doctorLikeRoles.rows.forEach((doctor: any, index: number) => {
      console.log(`  ${index + 1}. Role: "${doctor.role}", Status: "${doctor.status}", Nombre: ${doctor.firstName} ${doctor.lastName} (${doctor.email})`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  checkActualRoles()
    .then(() => {
      console.log('\n🎉 Verificación completada');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { checkActualRoles };