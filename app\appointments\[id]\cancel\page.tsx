'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { AlertCircle, Calendar, Clock, MapPin, User, CheckCircle } from 'lucide-react';

interface AppointmentDetails {
  id: string;
  title: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  consultoryName: string;
  scheduledDate: string;
  startTime: string;
  status: string;
}

export default function CancelAppointmentPage() {
  const params = useParams();
  const router = useRouter();
  const appointmentId = params.id as string;

  const [loading, setLoading] = useState(false);
  const [verifying, setVerifying] = useState(true);
  const [appointment, setAppointment] = useState<AppointmentDetails | null>(null);
  const [confirmationCode, setConfirmationCode] = useState('');
  const [cancellationReason, setCancellationReason] = useState('');
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [step, setStep] = useState<'verify' | 'cancel' | 'success'>('verify');

  // Verificar cita al cargar
  useEffect(() => {
    fetchAppointmentDetails();
  }, [appointmentId]);

  const fetchAppointmentDetails = async () => {
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/public`);
      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'No se pudo encontrar la cita');
        setVerifying(false);
        return;
      }

      if (data.data.status === 'cancelled') {
        setError('Esta cita ya fue cancelada anteriormente');
        setVerifying(false);
        return;
      }

      if (data.data.status === 'completed') {
        setError('No se puede cancelar una cita que ya fue completada');
        setVerifying(false);
        return;
      }

      setAppointment(data.data);
      setVerifying(false);
    } catch (error) {
      setError('Error al cargar los detalles de la cita');
      setVerifying(false);
    }
  };

  const handleVerifyCode = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!confirmationCode.trim()) {
      setError('Por favor ingresa el código de confirmación');
      return;
    }

    // Verificar código
    setLoading(true);
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/verify-code`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ code: confirmationCode.toUpperCase() })
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'Código incorrecto');
        setLoading(false);
        return;
      }

      // Código correcto, pasar al siguiente paso
      setStep('cancel');
      setLoading(false);
    } catch (error) {
      setError('Error al verificar el código');
      setLoading(false);
    }
  };

  const handleCancelAppointment = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');

    if (!cancellationReason.trim()) {
      setError('Por favor proporciona una razón para la cancelación');
      return;
    }

    setLoading(true);
    try {
      const response = await fetch(`/api/appointments/${appointmentId}/cancel`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          code: confirmationCode.toUpperCase(),
          reason: cancellationReason
        })
      });

      const data = await response.json();

      if (!response.ok) {
        setError(data.error || 'No se pudo cancelar la cita');
        setLoading(false);
        return;
      }

      // Éxito
      setSuccess(true);
      setStep('success');
      setLoading(false);
    } catch (error) {
      setError('Error al cancelar la cita');
      setLoading(false);
    }
  };

  if (verifying) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Cargando información de la cita...</p>
        </div>
      </div>
    );
  }

  if (error && !appointment) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="max-w-md w-full p-6">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold mb-2">Error</h2>
            <p className="text-gray-600">{error}</p>
            <Button
              onClick={() => router.push('/')}
              className="mt-4"
              variant="outline"
            >
              Volver al inicio
            </Button>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Cancelar Cita Médica</h1>
          <p className="text-gray-600 mt-2">
            {step === 'verify' && 'Verifica tu identidad para continuar'}
            {step === 'cancel' && 'Confirma la cancelación de tu cita'}
            {step === 'success' && 'Cita cancelada exitosamente'}
          </p>
        </div>

        {/* Detalles de la cita */}
        {appointment && step !== 'success' && (
          <Card className="mb-6 p-6">
            <h3 className="font-semibold mb-4">Información de la cita</h3>
            <div className="grid gap-3 text-sm">
              <div className="flex items-start gap-3">
                <User className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Paciente</p>
                  <p className="text-gray-600">{appointment.patientFirstName} {appointment.patientLastName}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <User className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Doctor</p>
                  <p className="text-gray-600">Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Calendar className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Fecha</p>
                  <p className="text-gray-600">
                    {new Date(appointment.scheduledDate).toLocaleDateString('es-ES', {
                      weekday: 'long',
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Clock className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Hora</p>
                  <p className="text-gray-600">
                    {new Date(appointment.startTime).toLocaleTimeString('es-ES', {
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="font-medium">Lugar</p>
                  <p className="text-gray-600">{appointment.consultoryName}</p>
                </div>
              </div>
            </div>
          </Card>
        )}

        {/* Step 1: Verificar código */}
        {step === 'verify' && (
          <Card className="p-6">
            <form onSubmit={handleVerifyCode}>
              <div className="mb-6">
                <Label htmlFor="code">Código de confirmación</Label>
                <Input
                  id="code"
                  type="text"
                  placeholder="Ingresa tu código (ej: ABC123)"
                  value={confirmationCode}
                  onChange={(e) => setConfirmationCode(e.target.value.toUpperCase())}
                  className="mt-1"
                  maxLength={10}
                />
                <p className="text-sm text-gray-500 mt-1">
                  Este código fue enviado en tu email de confirmación
                </p>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => router.push('/')}
                  className="flex-1"
                >
                  Cancelar
                </Button>
                <Button
                  type="submit"
                  className="flex-1"
                  disabled={loading || !confirmationCode}
                >
                  {loading ? 'Verificando...' : 'Continuar'}
                </Button>
              </div>
            </form>
          </Card>
        )}

        {/* Step 2: Confirmar cancelación */}
        {step === 'cancel' && (
          <Card className="p-6">
            <form onSubmit={handleCancelAppointment}>
              <div className="mb-6">
                <Label htmlFor="reason">Razón de cancelación</Label>
                <Textarea
                  id="reason"
                  placeholder="Por favor, indica el motivo de la cancelación..."
                  value={cancellationReason}
                  onChange={(e) => setCancellationReason(e.target.value)}
                  className="mt-1"
                  rows={4}
                />
              </div>

              <div className="mb-6 p-4 bg-amber-50 border border-amber-200 rounded-md">
                <p className="text-sm text-amber-800">
                  <strong>Política de cancelación:</strong> Las citas deben cancelarse con al menos 
                  24 horas de anticipación. Cancelaciones tardías pueden estar sujetas a cargos.
                </p>
              </div>

              {error && (
                <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
                  {error}
                </div>
              )}

              <div className="flex gap-3">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setStep('verify')}
                  className="flex-1"
                  disabled={loading}
                >
                  Atrás
                </Button>
                <Button
                  type="submit"
                  variant="destructive"
                  className="flex-1"
                  disabled={loading || !cancellationReason}
                >
                  {loading ? 'Cancelando...' : 'Confirmar cancelación'}
                </Button>
              </div>
            </form>
          </Card>
        )}

        {/* Step 3: Éxito */}
        {step === 'success' && (
          <Card className="p-8">
            <div className="text-center">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
              <h2 className="text-2xl font-semibold mb-2">Cita cancelada exitosamente</h2>
              <p className="text-gray-600 mb-6">
                Tu cita ha sido cancelada. Recibirás un email de confirmación en breve.
              </p>
              <p className="text-sm text-gray-500 mb-6">
                Si necesitas agendar una nueva cita, puedes contactarnos o usar nuestro sistema de citas en línea.
              </p>
              <Button onClick={() => router.push('/')} className="w-full sm:w-auto">
                Volver al inicio
              </Button>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}