'use client';

import { useState, useMemo, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Stethoscope, DollarSign, X } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Service {
  id: string;
  name: string;
  description?: string;
  basePrice: number;
  currency: string;
  category?: string;
}

interface ServiceSelectorProps {
  services: Service[];
  value: string;
  onChange: (serviceId: string) => void;
  disabled?: boolean;
  placeholder?: string;
  className?: string;
  autoOpen?: boolean; // Nueva prop para abrir automáticamente
}

export function ServiceSelector({ 
  services, 
  value, 
  onChange, 
  disabled = false, 
  placeholder = "Seleccionar servicio médico...",
  className,
  autoOpen = false
}: ServiceSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  // Auto-abrir cuando se agrega un nuevo servicio
  useEffect(() => {
    if (autoOpen && !value && !disabled) {
      setIsOpen(true);
    }
  }, [autoOpen, value, disabled]);

  // Filtrar servicios basado en el término de búsqueda
  const filteredServices = useMemo(() => {
    if (!searchTerm) return services;
    
    const term = searchTerm.toLowerCase();
    return services.filter(service =>
      service.name.toLowerCase().includes(term) ||
      service.description?.toLowerCase().includes(term) ||
      service.category?.toLowerCase().includes(term)
    );
  }, [services, searchTerm]);

  const selectedService = services.find(s => s.id === value);

  const handleServiceSelect = (serviceId: string) => {
    onChange(serviceId);
    setIsOpen(false);
    setSearchTerm('');
  };

  const clearSelection = () => {
    onChange('');
    setIsOpen(false);
  };

  if (disabled && selectedService) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md', className)}>
        <div className="text-sm font-medium text-gray-900">
          {selectedService.name}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          {selectedService.currency} {selectedService.basePrice}
        </div>
      </div>
    );
  }

  if (disabled) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        No especificado
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <Button
        type="button"
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-11 justify-start text-left font-normal"
      >
        <Stethoscope className="mr-2 h-4 w-4 text-gray-400" />
        {selectedService ? (
          <div className="flex items-center justify-between w-full">
            <div className="flex flex-col text-left">
              <span className="text-sm font-medium">{selectedService.name}</span>
              <span className="text-xs text-gray-500">
                {selectedService.currency} {selectedService.basePrice}
              </span>
            </div>
            <X 
              className="h-4 w-4 text-gray-400 hover:text-gray-600"
              onClick={(e) => {
                e.stopPropagation();
                clearSelection();
              }}
            />
          </div>
        ) : (
          <span className="text-gray-500">{placeholder}</span>
        )}
      </Button>
      
      {isOpen && (
        <>
          {/* Overlay para cerrar */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Panel de selección */}
          <Card className="absolute z-50 top-12 left-0 w-full max-w-lg shadow-lg border">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Campo de búsqueda */}
                <div className="relative">
                  <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Buscar servicios médicos..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 h-10"
                    autoFocus
                  />
                </div>
                
                {/* Lista de servicios */}
                <div className="max-h-60 overflow-y-auto space-y-2">
                  {filteredServices.length > 0 ? (
                    filteredServices.map((service) => (
                      <Button
                        key={service.id}
                        type="button"
                        variant="ghost"
                        onClick={() => handleServiceSelect(service.id)}
                        className={cn(
                          "w-full p-3 h-auto text-left justify-start hover:bg-blue-50",
                          value === service.id && "bg-blue-50"
                        )}
                      >
                        <div className="flex items-start justify-between w-full">
                          <div className="flex-1">
                            <div className="font-medium text-sm text-gray-900">
                              {service.name}
                            </div>
                            {service.description && (
                              <div className="text-xs text-gray-600 mt-1">
                                {service.description}
                              </div>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              <Badge variant="outline" className="text-xs">
                                <DollarSign className="h-3 w-3 mr-1" />
                                {service.currency} {service.basePrice}
                              </Badge>
                              {service.category && (
                                <Badge variant="secondary" className="text-xs">
                                  {service.category}
                                </Badge>
                              )}
                            </div>
                          </div>
                        </div>
                      </Button>
                    ))
                  ) : (
                    <div className="text-center py-6 text-gray-500">
                      <Stethoscope className="h-8 w-8 mx-auto mb-2 text-gray-300" />
                      <p className="text-sm">No se encontraron servicios</p>
                      <p className="text-xs">Intenta con otro término de búsqueda</p>
                    </div>
                  )}
                </div>
                
                {/* Información adicional */}
                {searchTerm && filteredServices.length > 0 && (
                  <div className="text-xs text-gray-500 border-t pt-2">
                    {filteredServices.length} servicio{filteredServices.length !== 1 ? 's' : ''} encontrado{filteredServices.length !== 1 ? 's' : ''}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}