import { seedCurrencies } from './currencies-seed';
import { seedActivityTypes } from './activity-types-seed';
import { seedMediaSources } from './media-sources-seed';
import { seedEducationLevels } from './education-levels-seed';
import { seedDocumentTypes } from './document-types-seed';
import { seedMaritalStatus } from './marital-status-seed';
import { seedPathologicalHistory } from './pathological-history-seed';
import { seedNonPathologicalHistory } from './non-pathological-history-seed';

/**
 * Ejecuta todos los seeds de los nuevos catálogos implementados en Fase 1
 * Este script migra todos los datos mock a la base de datos PostgreSQL
 */
export async function seedNewCatalogs() {
  console.log('🚀 Starting new catalogs seeding process...');
  console.log('📊 Seeding 8 new catalog tables with production-ready data\n');

  try {
    // Ejecutar seeds en orden secuencial para evitar conflictos
    await seedCurrencies();
    await seedActivityTypes();
    await seedMediaSources();
    await seedEducationLevels();
    await seedDocumentTypes();
    await seedMaritalStatus();
    await seedPathologicalHistory();
    await seedNonPathologicalHistory();

    console.log('\n🎉 All new catalogs seeded successfully!');
    console.log('📈 Phase 1 catalogs are now fully integrated with PostgreSQL');
    console.log('🔄 Ready for API migration from mock data to database queries');
    
  } catch (error) {
    console.error('\n❌ Error during new catalogs seeding:', error);
    throw error;
  }
}

// Ejecutar si este archivo se ejecuta directamente
if (require.main === module) {
  seedNewCatalogs()
    .then(() => {
      console.log('✅ Seeding completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Seeding failed:', error);
      process.exit(1);
    });
}