'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { MedicalConsultationInterface } from '@/components/doctor/medical-consultation-interface';
import { 
  ArrowLeft,
  User,
  Calendar,
  Clock,
  AlertCircle,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  age: number;
  email?: string;
}

interface Appointment {
  id: string;
  scheduledDate: string;
  duration: number;
  status: string;
  serviceName: string;
  preCheckinData?: any;
  preCheckinCompleted: boolean;
  chiefComplaint?: string;
}

interface MedicalRecord {
  id?: string;
  demographics?: any;
  vitalSigns?: any;
  allergies?: any[];
  currentMedications?: any[];
  medicalEmergencyContact?: string;
  medicalEmergencyPhone?: string;
  doctorNotes?: string;
}

export default function MedicalConsultationPage() {
  const params = useParams();
  const router = useRouter();
  const { user } = useUser();
  const appointmentId = params.appointmentId as string;

  const [loading, setLoading] = useState(true);
  const [patient, setPatient] = useState<Patient | null>(null);
  const [appointment, setAppointment] = useState<Appointment | null>(null);
  const [medicalRecord, setMedicalRecord] = useState<MedicalRecord | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (appointmentId && user?.id) {
      fetchAppointmentData();
    }
  }, [appointmentId, user?.id]);

  const fetchAppointmentData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Obtener información de la cita
      const appointmentResponse = await fetch(`/api/appointments/${appointmentId}`);
      const appointmentResult = await appointmentResponse.json();

      if (!appointmentResponse.ok) {
        throw new Error(appointmentResult.error || 'Error al cargar la cita');
      }

      const appointmentData = appointmentResult.data;
      console.log('🔍 appointmentData completo:', appointmentData);

      // Verificar que es el doctor de la cita
      if (appointmentData.doctorId !== user?.id) {
        throw new Error('No autorizado para ver esta cita');
      }

      // Obtener información del paciente
      console.log('🔍 appointmentData.patientId:', appointmentData.patientId);
      const patientResponse = await fetch(`/api/patients/${appointmentData.patientId}`);
      const patientResult = await patientResponse.json();
      console.log('🔍 patientResponse.ok:', patientResponse.ok);
      console.log('🔍 patientResponse.status:', patientResponse.status);

      if (!patientResponse.ok) {
        throw new Error(patientResult.error || 'Error al cargar información del paciente');
      }

      const patientData = patientResult.data;
      console.log('🔍 Datos del paciente recibidos:', patientData);
      console.log('🔍 patientResult completo:', patientResult);

      // Validar que patientData existe
      if (!patientData) {
        throw new Error('No se recibieron datos del paciente');
      }

      // Calcular edad del paciente
      let age = 0;
      let birthDate = new Date();
      
      if (patientData && patientData.dateOfBirth) {
        birthDate = new Date(patientData.dateOfBirth);
        const today = new Date();
        age = today.getFullYear() - birthDate.getFullYear();
        const monthDiff = today.getMonth() - birthDate.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
          age--;
        }
      } else {
        console.warn('⚠️ patientData o dateOfBirth no está definido:', patientData);
        // Usar valores por defecto si no hay fecha de nacimiento
        birthDate = new Date('1990-01-01'); // Fecha por defecto
        age = new Date().getFullYear() - 1990; // Edad aproximada por defecto
      }

      // Buscar expediente médico existente
      let existingRecord = null;
      try {
        const recordResponse = await fetch(`/api/medical-records?patientId=${appointmentData.patientId}&limit=1`);
        const recordResult = await recordResponse.json();
        
        if (recordResponse.ok && recordResult.data && recordResult.data.length > 0) {
          // Obtener detalles completos del expediente
          const recordDetailResponse = await fetch(`/api/medical-records/${recordResult.data[0].id}`);
          const recordDetailResult = await recordDetailResponse.json();
          
          if (recordDetailResponse.ok) {
            existingRecord = recordDetailResult.data;
          }
        }
      } catch (recordError) {
        console.warn('No se pudo cargar el expediente médico:', recordError);
      }

      setPatient({
        id: patientData.id,
        firstName: patientData.firstName,
        lastName: patientData.lastName,
        dateOfBirth: birthDate,
        age,
        email: patientData.email
      });

      console.log('🔍 Estado de la cita recibido:', appointmentData.status);
      
      setAppointment({
        id: appointmentData.id,
        scheduledDate: appointmentData.scheduledDate,
        duration: appointmentData.duration || 30,
        status: appointmentData.status,
        serviceName: appointmentData.serviceName || 'Consulta médica',
        preCheckinData: appointmentData.preCheckinData,
        preCheckinCompleted: appointmentData.preCheckinCompleted || false,
        chiefComplaint: appointmentData.chiefComplaint
      });

      setMedicalRecord(existingRecord);

    } catch (error) {
      console.error('Error fetching appointment data:', error);
      setError(error instanceof Error ? error.message : 'Error desconocido');
      toast.error('Error al cargar la información de la cita');
    } finally {
      setLoading(false);
    }
  };

  const handleConsultationComplete = async (consultationData: any) => {
    try {
      const response = await fetch('/api/medical-consultations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(consultationData),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Error al registrar consulta');
      }

      toast.success('Consulta médica completada exitosamente');
      
      // Regresar a la agenda
      setTimeout(() => {
        router.push('/dashboard/doctor/agenda');
      }, 1500);

    } catch (error) {
      console.error('Error completing consultation:', error);
      throw error;
    }
  };

  const handleCancel = () => {
    router.push('/dashboard/doctor/agenda');
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
            <p className="text-gray-600">Cargando información de la consulta...</p>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-red-800 mb-2">Error</h3>
              <p className="text-red-700 mb-4">{error}</p>
              <Button onClick={() => router.push('/dashboard/doctor/agenda')} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver a la Agenda
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!patient || !appointment) {
    return (
      <div className="container mx-auto p-6">
        <Card className="border-orange-200 bg-orange-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 text-orange-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold text-orange-800 mb-2">
                Información Incompleta
              </h3>
              <p className="text-orange-700 mb-4">
                No se pudo cargar la información completa de la cita
              </p>
              <Button onClick={() => router.push('/dashboard/doctor/agenda')} variant="outline">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Volver a la Agenda
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Agenda
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Consulta Médica</h1>
            <p className="text-gray-600">
              {appointment.serviceName} • {new Date(appointment.scheduledDate).toLocaleDateString('es-ES')}
            </p>
          </div>
        </div>
        <Badge 
          variant={
            appointment.status === 'confirmed' ? 'default' :
            appointment.status === 'checked_in' ? 'secondary' :
            appointment.status === 'in_progress' ? 'default' :
            appointment.status === 'completed' ? 'default' :
            appointment.status === 'cancelled' ? 'destructive' :
            appointment.status === 'no_show' ? 'destructive' :
            appointment.status === 'scheduled' ? 'secondary' :
            appointment.status === 'pending_confirmation' ? 'outline' :
            'secondary'
          }
        >
          {appointment.status === 'scheduled' && 'Programada'}
          {appointment.status === 'pending_confirmation' && 'Pendiente Confirmación'}
          {appointment.status === 'confirmed' && 'Confirmada'}
          {appointment.status === 'checked_in' && 'Paciente Llegó'}
          {appointment.status === 'in_progress' && 'En Progreso'}
          {appointment.status === 'completed' && 'Completada'}
          {appointment.status === 'cancelled' && 'Cancelada'}
          {appointment.status === 'no_show' && 'No se presentó'}
          {!['scheduled', 'pending_confirmation', 'confirmed', 'checked_in', 'in_progress', 'completed', 'cancelled', 'no_show'].includes(appointment.status) && 'Estado desconocido'}
        </Badge>
      </div>

      <Separator />

      {/* Interfaz de consulta médica */}
      <MedicalConsultationInterface
        patient={patient}
        appointment={appointment}
        existingMedicalRecord={medicalRecord}
        onConsultationComplete={handleConsultationComplete}
        onCancel={handleCancel}
        loading={loading}
      />
    </div>
  );
}