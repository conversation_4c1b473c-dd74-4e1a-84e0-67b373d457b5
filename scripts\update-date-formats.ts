#!/usr/bin/env node
import * as fs from 'fs';
import * as path from 'path';
import { glob } from 'glob';

// Patrones a buscar y reemplazar
const replacements = [
  {
    // format(new Date(date), 'dd/MM/yyyy', { locale: es }) -> formatDate(date)
    pattern: /format\s*\(\s*new\s+Date\s*\(\s*([^)]+)\s*\)\s*,\s*['"`]dd\/MM\/yyyy['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDate($1)'
  },
  {
    // format(date, 'dd/MM/yyyy', { locale: es }) -> formatDate(date)
    pattern: /format\s*\(\s*([^,]+)\s*,\s*['"`]dd\/MM\/yyyy['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDate($1)'
  },
  {
    // format(new Date(date), 'dd/MM/yyyy HH:mm', { locale: es }) -> formatDateTime(date)
    pattern: /format\s*\(\s*new\s+Date\s*\(\s*([^)]+)\s*\)\s*,\s*['"`]dd\/MM\/yyyy\s+HH:mm['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatDateTime($1)'
  },
  {
    // format(date, 'HH:mm', { locale: es }) -> formatTime(date)
    pattern: /format\s*\(\s*([^,]+)\s*,\s*['"`]HH:mm['"`]\s*(?:,\s*\{\s*locale:\s*es\s*\})?\s*\)/g,
    replacement: 'formatTime($1)'
  }
];

// Archivos a excluir
const excludePatterns = [
  '**/node_modules/**',
  '**/dist/**',
  '**/build/**',
  '**/.next/**',
  '**/docs/**',
  '**/scripts/**',
  '**/lib/utils.ts' // No modificar el archivo utils mismo
];

async function updateDateFormats() {
  try {
    // Buscar archivos TypeScript y TypeScript React
    const files = await glob('**/*.{ts,tsx}', {
      ignore: excludePatterns
    });

    console.log(`Found ${files.length} files to check...`);

    let updatedFiles = 0;
    let totalReplacements = 0;

    for (const file of files) {
      const filePath = path.resolve(file);
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      let fileReplacements = 0;

      // Aplicar cada patrón de reemplazo
      for (const { pattern, replacement } of replacements) {
        const matches = content.match(pattern);
        if (matches) {
          content = content.replace(pattern, replacement);
          modified = true;
          fileReplacements += matches.length;
        }
      }

      // Si se modificó el archivo
      if (modified) {
        // Verificar si necesita importar formatDate
        const hasFormatImport = content.includes('import { format }') || content.includes('import { format,');
        const hasUtilsImport = content.includes('from "@/lib/utils"') || content.includes('from "@/lib/utils\'');
        const needsFormatDate = content.includes('formatDate(') || content.includes('formatDateTime(') || content.includes('formatTime(');

        if (needsFormatDate && !content.includes('formatDate')) {
          // Agregar import de formatDate si no existe
          if (hasUtilsImport) {
            // Ya tiene import de utils, agregar formatDate
            content = content.replace(
              /import\s*\{([^}]+)\}\s*from\s*["']@\/lib\/utils["']/,
              (match, imports) => {
                const importList = imports.split(',').map(i => i.trim());
                if (!importList.some(i => i.includes('formatDate'))) {
                  importList.push('formatDate');
                }
                if (content.includes('formatDateTime(') && !importList.some(i => i.includes('formatDateTime'))) {
                  importList.push('formatDateTime');
                }
                if (content.includes('formatTime(') && !importList.some(i => i.includes('formatTime'))) {
                  importList.push('formatTime');
                }
                return `import { ${importList.join(', ')} } from "@/lib/utils"`;
              }
            );
          } else {
            // No tiene import de utils, agregar uno nuevo
            const imports = [];
            if (content.includes('formatDate(')) imports.push('formatDate');
            if (content.includes('formatDateTime(')) imports.push('formatDateTime');
            if (content.includes('formatTime(')) imports.push('formatTime');
            
            const importStatement = `import { ${imports.join(', ')} } from "@/lib/utils";\n`;
            
            // Agregar después de otros imports
            if (content.includes('import ')) {
              const lastImportIndex = content.lastIndexOf('import ');
              const endOfLine = content.indexOf('\n', lastImportIndex);
              content = content.slice(0, endOfLine + 1) + importStatement + content.slice(endOfLine + 1);
            } else {
              // Si no hay imports, agregar al principio
              content = importStatement + content;
            }
          }
        }

        // Remover imports de date-fns si ya no se usan
        if (hasFormatImport && !content.includes('format(')) {
          content = content.replace(/import\s*\{\s*format\s*\}\s*from\s*["']date-fns["'];?\n?/g, '');
          content = content.replace(/import\s*\{\s*format\s*,\s*/g, 'import { ');
          content = content.replace(/,\s*format\s*\}/g, ' }');
          content = content.replace(/,\s*format\s*,/g, ',');
        }

        // Guardar el archivo actualizado
        fs.writeFileSync(filePath, content);
        updatedFiles++;
        totalReplacements += fileReplacements;
        console.log(`✓ Updated ${file} (${fileReplacements} replacements)`);
      }
    }

    console.log(`\n✅ Complete! Updated ${updatedFiles} files with ${totalReplacements} total replacements.`);
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

// Ejecutar
updateDateFormats();