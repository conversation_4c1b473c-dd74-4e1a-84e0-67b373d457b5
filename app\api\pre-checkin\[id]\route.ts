import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, guardianPatientRelations } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    const appointmentId = params.id;

    // 1. Verificar que la cita existe
    const appointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (appointment.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Cita no encontrada' },
        { status: 404 }
      );
    }

    const apt = appointment[0];

    // 2. Verificar que el usuario tiene permisos (es el paciente o es guardian del paciente)
    let hasPermission = false;
    
    if (apt.patientId === userId) {
      hasPermission = true;
    } else {
      // Verificar si es guardian del paciente
      const guardianRelation = await db
        .select()
        .from(guardianPatientRelations)
        .where(
          and(
            eq(guardianPatientRelations.guardianId, userId),
            eq(guardianPatientRelations.patientId, apt.patientId)
          )
        )
        .limit(1);
      
      hasPermission = guardianRelation.length > 0;
    }

    if (!hasPermission) {
      return NextResponse.json(
        { success: false, message: 'No tienes permisos para realizar pre-checkin en esta cita' },
        { status: 403 }
      );
    }

    // 3. Actualizar la cita con el timestamp de pre-checkin
    const now = new Date();
    const updatedAppointment = await db
      .update(appointments)
      .set({
        checkedInAt: now,
        updatedAt: now
      })
      .where(eq(appointments.id, appointmentId))
      .returning();

    if (updatedAppointment.length > 0) {
      return NextResponse.json({
        success: true,
        message: 'Pre-checkin realizado exitosamente',
        data: {
          appointmentId,
          preCheckinTime: now.toISOString(),
          status: 'completed'
        }
      });
    } else {
      return NextResponse.json(
        { success: false, message: 'Error al actualizar la cita' },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('Error en pre-checkin:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}