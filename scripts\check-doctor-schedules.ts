import { db } from '@/db/drizzle';
import { doctorSchedules, user, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

async function checkDoctorSchedules() {
  try {
    console.log('🔍 Verificando horarios de doctores...\n');

    // Obtener todos los doctores activos
    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      );

    console.log(`📋 Encontrados ${doctors.length} doctores activos:\n`);

    for (const doctor of doctors) {
      console.log(`👨‍⚕️ Dr. ${doctor.firstName} ${doctor.lastName} (${doctor.id})`);
      
      // Obtener horarios de este doctor
      const schedules = await db
        .select()
        .from(doctorSchedules)
        .where(eq(doctorSchedules.doctorId, doctor.id));

      if (schedules.length === 0) {
        console.log('   ❌ Sin horarios configurados');
      } else {
        console.log(`   ✅ ${schedules.length} horarios encontrados:`);
        schedules.forEach(schedule => {
          const dayNames = ['Dom', 'Lun', 'Mar', 'Mie', 'Jue', 'Vie', 'Sab'];
          const status = schedule.isActive ? '🟢' : '🔴';
          const onlineBooking = schedule.allowOnlineBooking ? '📱' : '❌';
          console.log(`      ${status} ${dayNames[schedule.dayOfWeek]}: ${schedule.startTime}-${schedule.endTime} ${onlineBooking}`);
        });
      }
      console.log('');
    }

  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

checkDoctorSchedules();