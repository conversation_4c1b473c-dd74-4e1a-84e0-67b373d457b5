{"name": "apple-pass", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "seed": "npx tsx db/seed.ts", "seed:colors": "npx tsx scripts/seed-consultory-colors.ts", "make-admin": "npx tsx scripts/make-admin.ts", "clean:all": "npx tsx scripts/clean-all-appointments.ts", "clean:appointments": "npx tsx scripts/clean-appointments-only.ts", "test:emails": "node scripts/test-email-cron.js both", "test:precheckin": "node scripts/test-email-cron.js precheckin", "test:reminders": "node scripts/test-email-cron.js reminders"}, "dependencies": {"@ai-sdk/openai": "^1.3.22", "@aws-sdk/client-s3": "^3.800.0", "@clerk/nextjs": "^6.23.3", "@hookform/resolvers": "^5.0.1", "@icons-pack/react-simple-icons": "^12.7.0", "@neondatabase/serverless": "^1.0.0", "@polar-sh/better-auth": "^1.0.1", "@polar-sh/sdk": "^0.32.16", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-use-controllable-state": "^1.2.2", "@react-pdf/renderer": "^4.3.0", "@shikijs/transformers": "^3.3.0", "@tabler/icons-react": "^3.34.0", "@tanstack/react-query": "^5.76.1", "@uploadthing/react": "^7.3.0", "@vercel/analytics": "^1.5.0", "@walletpass/pass-js": "^6.9.1", "ai": "^4.3.16", "apn": "^2.2.0", "balloons-js": "^0.0.3", "bufferutil": "^4.0.9", "class-variance-authority": "^0.7.1", "cloudinary": "^2.7.0", "clsx": "^2.1.1", "cmdk": "^1.1.1", "color": "^5.0.0", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "form-data": "^4.0.2", "framer-motion": "^12.12.1", "lodash": "^4.17.21", "lucide-react": "^0.503.0", "motion": "^12.10.5", "nanoid": "^5.1.5", "next": "15.3.1", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.6", "pg": "^8.16.0", "posthog-js": "^1.248.1", "posthog-node": "^4.18.0", "qrcode.react": "^4.2.0", "react": "^19.0.0", "react-day-picker": "^9.8.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.8", "react-select": "^5.10.2", "recharts": "^2.15.3", "remark-gfm": "^4.0.1", "resend": "^4.7.0", "shiki": "^3.3.0", "slug": "^11.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "ua-parser-js": "^2.0.3", "uploadthing": "^7.6.0", "ws": "^8.18.3", "zod": "^3.24.3", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/lodash": "^4.17.17", "@types/node": "^20.17.31", "@types/react": "^19", "@types/react-dom": "^19", "@types/slug": "^5.0.9", "drizzle-kit": "^0.31.0", "eslint": "^9", "eslint-config-next": "15.3.1", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.3.0", "typescript": "^5"}}