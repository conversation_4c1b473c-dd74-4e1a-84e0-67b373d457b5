'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Plus,
  Search,
  Filter,
  RefreshCw,
  Calendar,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  Download,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Clock,
  ArrowLeft,
  Users,
  Repeat,
  ToggleLeft
} from 'lucide-react';
import { toast } from 'sonner';

interface ActivityType {
  id: string;
  name: string;
  category: string;
  color: string;
  duration: number;
  requiresPatient: boolean;
  allowsRecurrence: boolean;
  respectsSchedule: boolean;
  icon: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface CategoryInfo {
  id: string;
  name: string;
  color: string;
}

export default function ActivityTypesPage() {
  const router = useRouter();
  const [activityTypes, setActivityTypes] = useState<ActivityType[]>([]);
  const [categories, setCategories] = useState<CategoryInfo[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState<string>('all');
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [patientFilter, setPatientFilter] = useState<'all' | 'requires' | 'no-requires'>('all');
  const [selectedActivityTypes, setSelectedActivityTypes] = useState<string[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingActivityType, setEditingActivityType] = useState<ActivityType | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [selectedActivityType, setSelectedActivityType] = useState<ActivityType | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [activityTypeToDelete, setActivityTypeToDelete] = useState<ActivityType | null>(null);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    color: '',
    duration: '',
    requiresPatient: false,
    allowsRecurrence: false,
    respectsSchedule: true,
    icon: 'calendar',
    description: '',
    isActive: true
  });

  // Fetch activity types
  const fetchActivityTypes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(categoryFilter !== 'all' && { category: categoryFilter }),
        ...(activeFilter !== 'all' && { active: (activeFilter === 'active').toString() }),
        ...(patientFilter !== 'all' && { patient: patientFilter === 'requires' ? 'true' : 'false' }),
        sortBy: sortBy,
        sortOrder: sortOrder
      });

      const response = await fetch(`/api/catalogs/activity-types?${params}`);
      if (!response.ok) throw new Error('Error fetching activity types');
      
      const data = await response.json();
      setActivityTypes(data.data || []);
      setCategories(data.categories || []);
      setTotalPages(data.pagination?.totalPages || 1);
      setTotalCount(data.pagination?.total || 0);
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar los tipos de actividad');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchActivityTypes();
  };

  useEffect(() => {
    fetchActivityTypes();
  }, [page, limit, searchTerm, categoryFilter, activeFilter, patientFilter, sortBy, sortOrder]);

  // Reset page when filters change
  useEffect(() => {
    setPage(1);
  }, [searchTerm, categoryFilter, activeFilter, patientFilter]);

  // Handle create
  const handleCreate = async () => {
    try {
      const response = await fetch('/api/catalogs/activity-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          duration: parseInt(formData.duration)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error creating activity type');
      }

      toast.success('Tipo de actividad creado exitosamente');
      setIsCreateDialogOpen(false);
      resetForm();
      fetchActivityTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  // Handle edit
  const handleEdit = async () => {
    if (!editingActivityType) return;

    try {
      const response = await fetch('/api/catalogs/activity-types', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingActivityType.id,
          ...formData,
          duration: parseInt(formData.duration)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error updating activity type');
      }

      toast.success('Tipo de actividad actualizado exitosamente');
      setIsEditDialogOpen(false);
      setEditingActivityType(null);
      resetForm();
      fetchActivityTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      color: '',
      duration: '',
      requiresPatient: false,
      allowsRecurrence: false,
      respectsSchedule: true,
      icon: 'calendar',
      description: '',
      isActive: true
    });
  };

  const openEditDialog = (activityType: ActivityType) => {
    setEditingActivityType(activityType);
    setFormData({
      name: activityType.name,
      category: activityType.category,
      color: activityType.color,
      duration: activityType.duration.toString(),
      requiresPatient: activityType.requiresPatient,
      allowsRecurrence: activityType.allowsRecurrence,
      respectsSchedule: activityType.respectsSchedule,
      icon: activityType.icon,
      description: activityType.description || '',
      isActive: activityType.isActive
    });
    setIsEditDialogOpen(true);
  };

  const openDetailsDialog = (activityType: ActivityType) => {
    setSelectedActivityType(activityType);
    setIsDetailsDialogOpen(true);
  };

  const openDeleteDialog = (activityType: ActivityType) => {
    setActivityTypeToDelete(activityType);
    setIsDeleteDialogOpen(true);
  };

  const handleDelete = async (deleteType: 'logical' | 'physical' = 'logical') => {
    if (!activityTypeToDelete) return;
    
    try {
      const response = await fetch(`/api/catalogs/activity-types/${activityTypeToDelete.id}?type=${deleteType}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error eliminando tipo de actividad');
      }

      const result = await response.json();
      toast.success(result.message || 'Tipo de actividad eliminado exitosamente');
      setIsDeleteDialogOpen(false);
      setActivityTypeToDelete(null);
      fetchActivityTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const toggleStatus = async (activityType: ActivityType) => {
    try {
      const response = await fetch(`/api/catalogs/activity-types/${activityType.id}/toggle-status`, {
        method: 'POST'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error cambiando estado');
      }

      toast.success(`Tipo de actividad ${activityType.isActive ? 'desactivado' : 'activado'} exitosamente`);
      fetchActivityTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // Filtering and sorting is now handled on the server side

  const getCategoryColor = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category?.color || '#6B7280';
  };

  const getCategoryName = (categoryId: string) => {
    const category = categories.find(c => c.id === categoryId);
    return category?.name || categoryId;
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="hover:bg-green-100 hover:text-green-600 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestión de Tipos de Actividad</h1>
              <p className="text-sm lg:text-base text-gray-600">Administra los tipos de actividad del sistema</p>
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            disabled={refreshing}
            className="hover:bg-gray-50 transition-colors w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto">
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Tipo de Actividad
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[600px]">
              <DialogHeader>
                <DialogTitle>Crear Nuevo Tipo de Actividad</DialogTitle>
                <DialogDescription>
                  Completa la información para crear un nuevo tipo de actividad
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">Nombre</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData({...formData, name: e.target.value})}
                      placeholder="ej. Consulta General"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">Categoría</Label>
                    <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar categoría" />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category.id} value={category.id}>
                            {category.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="duration">Duración (minutos)</Label>
                    <Input
                      id="duration"
                      type="number"
                      min="5"
                      max="480"
                      value={formData.duration}
                      onChange={(e) => setFormData({...formData, duration: e.target.value})}
                      placeholder="ej. 30"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="color">Color</Label>
                    <Input
                      id="color"
                      type="color"
                      value={formData.color || getCategoryColor(formData.category)}
                      onChange={(e) => setFormData({...formData, color: e.target.value})}
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">Descripción</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => setFormData({...formData, description: e.target.value})}
                    placeholder="Descripción del tipo de actividad..."
                    className="min-h-[60px]"
                  />
                </div>
                <div className="flex items-center space-x-6">
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="requiresPatient"
                      checked={formData.requiresPatient}
                      onCheckedChange={(checked) => setFormData({...formData, requiresPatient: !!checked})}
                    />
                    <Label htmlFor="requiresPatient">Requiere paciente</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="allowsRecurrence"
                      checked={formData.allowsRecurrence}
                      onCheckedChange={(checked) => setFormData({...formData, allowsRecurrence: !!checked})}
                    />
                    <Label htmlFor="allowsRecurrence">Permite recurrencia</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="respectsSchedule"
                      checked={formData.respectsSchedule}
                      onCheckedChange={(checked) => setFormData({...formData, respectsSchedule: !!checked})}
                    />
                    <Label htmlFor="respectsSchedule">Respeta horarios configurados</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id="isActive"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
                    />
                    <Label htmlFor="isActive">Activo</Label>
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancelar
                </Button>
                <Button onClick={handleCreate}>
                  Crear Tipo de Actividad
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">{totalCount}</p>
              </div>
              <Calendar className="h-8 w-8 text-indigo-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Activos</p>
                <p className="text-2xl font-bold text-green-600">
                  {activityTypes.filter(t => t.isActive).length}
                </p>
              </div>
              <Calendar className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Con Paciente</p>
                <p className="text-2xl font-bold text-blue-600">
                  {activityTypes.filter(t => t.requiresPatient).length}
                </p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Recurrentes</p>
                <p className="text-2xl font-bold text-purple-600">
                  {activityTypes.filter(t => t.allowsRecurrence).length}
                </p>
              </div>
              <Repeat className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
            {(searchTerm || categoryFilter !== 'all' || activeFilter !== 'all' || patientFilter !== 'all') && (
              <Badge variant="secondary" className="ml-2">
                {[searchTerm, categoryFilter !== 'all', activeFilter !== 'all', patientFilter !== 'all'].filter(Boolean).length} activos
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-3 sm:items-end">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre o descripción..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Todas las categorías" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las categorías</SelectItem>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select value={activeFilter} onValueChange={setActiveFilter as any}>
                <SelectTrigger className="w-full sm:w-40">
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="active">Activos</SelectItem>
                  <SelectItem value="inactive">Inactivos</SelectItem>
                </SelectContent>
              </Select>
              <Select value={patientFilter} onValueChange={setPatientFilter as any}>
                <SelectTrigger className="w-full sm:w-48">
                  <SelectValue placeholder="Todas las actividades" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todas las actividades</SelectItem>
                  <SelectItem value="requires">Requiere paciente</SelectItem>
                  <SelectItem value="no-requires">No requiere paciente</SelectItem>
                </SelectContent>
              </Select>
              
          </div>
          
          {(searchTerm || categoryFilter !== 'all' || activeFilter !== 'all' || patientFilter !== 'all') && (
            <div className="mt-3">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setSearchTerm('');
                  setCategoryFilter('all');
                  setActiveFilter('all');
                  setPatientFilter('all');
                }}
                className="flex items-center space-x-2"
              >
                <RefreshCw className="h-4 w-4" />
                <span>Limpiar filtros</span>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5 text-blue-600" />
                Lista de Tipos de Actividad
              </CardTitle>
              <Badge variant="outline" className="border-gray-300">
                {totalCount} tipos de actividad
              </Badge>
            </div>
            {selectedActivityTypes.length > 0 && (
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">
                  {selectedActivityTypes.length} seleccionados
                </span>
                <Button variant="outline" size="sm">
                  Acciones masivas
                </Button>
              </div>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-12">
                    <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                  </TableHead>
                  <TableHead 
                    className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('name')}
                  >
                    <div className="flex items-center gap-2">
                      Actividad
                      <SortIcon column="name" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('category')}
                  >
                    <div className="flex items-center gap-2">
                      Categoría
                      <SortIcon column="category" />
                    </div>
                  </TableHead>
                  <TableHead 
                    className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('duration')}
                  >
                    <div className="flex items-center gap-2">
                      Duración
                      <SortIcon column="duration" />
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-gray-700">Opciones</TableHead>
                  <TableHead 
                    className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                    onClick={() => handleSort('isActive')}
                  >
                    <div className="flex items-center gap-2">
                      Estado
                      <SortIcon column="isActive" />
                    </div>
                  </TableHead>
                  <TableHead className="font-semibold text-gray-700 text-right">Acciones</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                      <p className="mt-2 text-gray-600">Cargando tipos de actividad...</p>
                    </TableCell>
                  </TableRow>
                ) : activityTypes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8">
                      <Calendar className="h-12 w-12 text-gray-400 mx-auto" />
                      <p className="mt-2 text-gray-600">No se encontraron tipos de actividad</p>
                    </TableCell>
                  </TableRow>
                ) : (
                  activityTypes.map((activityType) => (
                    <TableRow key={activityType.id}>
                      <TableCell>
                        <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-3">
                          <div 
                            className="w-3 h-3 rounded-full" 
                            style={{ backgroundColor: activityType.color }}
                          />
                          <div>
                            <div className="font-medium">{activityType.name}</div>
                            {activityType.description && (
                              <div className="text-sm text-gray-500 truncate max-w-xs">
                                {activityType.description}
                              </div>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" style={{ borderColor: getCategoryColor(activityType.category) }}>
                          {getCategoryName(activityType.category)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center space-x-1">
                          <Clock className="h-3 w-3 text-gray-400" />
                          <span className="text-sm">{activityType.duration} min</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-1">
                          {activityType.requiresPatient && (
                            <Badge variant="outline" className="text-xs">
                              <Users className="h-3 w-3 mr-1" />
                              Paciente
                            </Badge>
                          )}
                          {activityType.allowsRecurrence && (
                            <Badge variant="outline" className="text-xs">
                              <Repeat className="h-3 w-3 mr-1" />
                              Recurrente
                            </Badge>
                          )}
                          <Badge 
                            variant={activityType.respectsSchedule ? "default" : "destructive"} 
                            className="text-xs"
                          >
                            <Clock className="h-3 w-3 mr-1" />
                            {activityType.respectsSchedule ? "Horario" : "Libre"}
                          </Badge>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={activityType.isActive ? "default" : "secondary"}>
                          {activityType.isActive ? "Activo" : "Inactivo"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openDetailsDialog(activityType)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Ver detalles
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openEditDialog(activityType)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => toggleStatus(activityType)}
                              className={activityType.isActive ? "text-orange-600" : "text-green-600"}
                            >
                              <ToggleLeft className="h-4 w-4 mr-2" />
                              {activityType.isActive ? 'Desactivar' : 'Activar'}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className="text-red-600" 
                              onClick={() => openDeleteDialog(activityType)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex flex-col sm:flex-row items-center justify-between px-2 py-4 gap-4">
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                Mostrando {Math.min((page - 1) * limit + 1, totalCount)} a {Math.min(page * limit, totalCount)} de {totalCount} tipos de actividad
              </div>
              <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="5">5 por página</SelectItem>
                  <SelectItem value="10">10 por página</SelectItem>
                  <SelectItem value="20">20 por página</SelectItem>
                  <SelectItem value="50">50 por página</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {totalPages > 1 && (
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(prev => Math.max(1, prev - 1))}
                  disabled={page === 1}
                >
                  Anterior
                </Button>
                
                {/* Page numbers */}
                <div className="flex items-center space-x-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNumber;
                    if (totalPages <= 5) {
                      pageNumber = i + 1;
                    } else if (page <= 3) {
                      pageNumber = i + 1;
                    } else if (page >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = page - 2 + i;
                    }
                    
                    return (
                      <Button
                        key={pageNumber}
                        variant={page === pageNumber ? "default" : "outline"}
                        size="sm"
                        className="w-8 h-8 p-0"
                        onClick={() => setPage(pageNumber)}
                      >
                        {pageNumber}
                      </Button>
                    );
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={page === totalPages}
                >
                  Siguiente
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Editar Tipo de Actividad</DialogTitle>
            <DialogDescription>
              Modifica la información del tipo de actividad
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Nombre</Label>
                <Input
                  id="edit-name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-category">Categoría</Label>
                <Select value={formData.category} onValueChange={(value) => setFormData({...formData, category: value})}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="edit-duration">Duración (minutos)</Label>
                <Input
                  id="edit-duration"
                  type="number"
                  min="5"
                  max="480"
                  value={formData.duration}
                  onChange={(e) => setFormData({...formData, duration: e.target.value})}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-color">Color</Label>
                <Input
                  id="edit-color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({...formData, color: e.target.value})}
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="edit-description">Descripción</Label>
              <Textarea
                id="edit-description"
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="min-h-[60px]"
              />
            </div>
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="edit-requiresPatient"
                  checked={formData.requiresPatient}
                  onCheckedChange={(checked) => setFormData({...formData, requiresPatient: !!checked})}
                />
                <Label htmlFor="edit-requiresPatient">Requiere paciente</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="edit-allowsRecurrence"
                  checked={formData.allowsRecurrence}
                  onCheckedChange={(checked) => setFormData({...formData, allowsRecurrence: !!checked})}
                />
                <Label htmlFor="edit-allowsRecurrence">Permite recurrencia</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="edit-respectsSchedule"
                  checked={formData.respectsSchedule}
                  onCheckedChange={(checked) => setFormData({...formData, respectsSchedule: !!checked})}
                />
                <Label htmlFor="edit-respectsSchedule">Respeta horarios configurados</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox 
                  id="edit-isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
                />
                <Label htmlFor="edit-isActive">Activo</Label>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit}>
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Details Dialog */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              Detalles del Tipo de Actividad
            </DialogTitle>
            <DialogDescription>
              Información detallada del tipo de actividad
            </DialogDescription>
          </DialogHeader>
          {selectedActivityType && (
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                  <p className="text-sm text-gray-900">{selectedActivityType.name}</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Categoría</Label>
                  <p className="text-sm text-gray-900">{getCategoryName(selectedActivityType.category)}</p>
                </div>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Duración</Label>
                  <p className="text-sm text-gray-900">{selectedActivityType.duration} minutos</p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Color</Label>
                  <div className="flex items-center space-x-2">
                    <div 
                      className="w-4 h-4 rounded-full border" 
                      style={{ backgroundColor: selectedActivityType.color }}
                    />
                    <span className="text-sm text-gray-900">{selectedActivityType.color}</span>
                  </div>
                </div>
              </div>

              <div>
                <Label className="text-sm font-medium text-gray-700">Descripción</Label>
                <p className="text-sm text-gray-900">{selectedActivityType.description || 'Sin descripción'}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Requiere paciente</Label>
                  <p className="text-sm text-gray-900">
                    {selectedActivityType.requiresPatient ? 'Sí' : 'No'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Permite recurrencia</Label>
                  <p className="text-sm text-gray-900">
                    {selectedActivityType.allowsRecurrence ? 'Sí' : 'No'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Respeta horarios configurados</Label>
                  <p className="text-sm text-gray-900">
                    {selectedActivityType.respectsSchedule ? 'Sí' : 'No'}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Estado</Label>
                  <Badge variant={selectedActivityType.isActive ? "default" : "secondary"}>
                    {selectedActivityType.isActive ? "Activo" : "Inactivo"}
                  </Badge>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Creado</Label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedActivityType.createdAt).toLocaleDateString()}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Actualizado</Label>
                  <p className="text-sm text-gray-900">
                    {new Date(selectedActivityType.updatedAt).toLocaleDateString()}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDetailsDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Trash2 className="h-5 w-5 text-red-500" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Esta acción no se puede deshacer. Seleccione el tipo de eliminación que desea realizar.
            </DialogDescription>
          </DialogHeader>
          {activityTypeToDelete && (
            <div className="py-4">
              <div className="p-4 bg-gray-50 rounded-lg mb-4">
                <p className="text-sm font-medium text-gray-900 mb-2">
                  Tipo de actividad a eliminar:
                </p>
                <p className="text-lg font-semibold text-gray-800">
                  {activityTypeToDelete.name}
                </p>
                <p className="text-sm text-gray-600 mt-1">
                  Categoría: {activityTypeToDelete.category}
                </p>
              </div>
              
              <div className="space-y-3">
                <div className="p-3 border border-orange-200 bg-orange-50 rounded-lg">
                  <p className="text-sm font-medium text-orange-800 mb-1">
                    Eliminación Lógica (Recomendada)
                  </p>
                  <p className="text-xs text-orange-700">
                    El tipo de actividad se marcará como inactivo pero se mantendrá en la base de datos para referencia histórica.
                  </p>
                </div>
                
                <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
                  <p className="text-sm font-medium text-red-800 mb-1">
                    Eliminación Física (Permanente)
                  </p>
                  <p className="text-xs text-red-700">
                    El tipo de actividad se eliminará completamente de la base de datos. Esta acción es irreversible.
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancelar
            </Button>
            <Button 
              variant="outline" 
              className="bg-orange-500 text-white hover:bg-orange-600"
              onClick={() => handleDelete('logical')}
            >
              Eliminar Lógicamente
            </Button>
            <Button 
              variant="destructive"
              onClick={() => handleDelete('physical')}
            >
              Eliminar Físicamente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}