import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { patientInvitations, user } from '@/db/schema';
import { eq, and, gte } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const searchParams = request.nextUrl.searchParams;
    const token = searchParams.get('token');

    if (!token) {
      return NextResponse.json(
        { success: false, error: 'Token no proporcionado' },
        { status: 400 }
      );
    }

    // Buscar invitación por token
    const invitations = await db
      .select({
        id: patientInvitations.id,
        patientUserId: patientInvitations.patientUserId,
        guardianEmail: patientInvitations.guardianEmail,
        status: patientInvitations.status,
        expiresAt: patientInvitations.expiresAt,
      })
      .from(patientInvitations)
      .where(
        and(
          eq(patientInvitations.invitationToken, token),
          eq(patientInvitations.status, 'pending'),
          gte(patientInvitations.expiresAt, new Date())
        )
      )
      .limit(1);

    if (invitations.length === 0) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Token inválido o expirado. Por favor solicita una nueva invitación.' 
        },
        { status: 400 }
      );
    }

    const invitation = invitations[0];

    // Obtener información del paciente
    const patients = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        dateOfBirth: user.dateOfBirth,
      })
      .from(user)
      .where(eq(user.id, invitation.patientUserId))
      .limit(1);

    if (patients.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    const patient = patients[0];

    // Calcular si es menor de edad
    let isMinor = false;
    if (patient.dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(patient.dateOfBirth);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age - 1;
      }
      isMinor = age < 18;
    }

    return NextResponse.json({
      success: true,
      data: {
        id: patient.id,
        firstName: patient.firstName,
        lastName: patient.lastName,
        email: invitation.guardianEmail, // Usar el email del guardián para la activación
        phone: patient.phone,
        isMinor,
        invitationId: invitation.id,
      }
    });

  } catch (error) {
    console.error('Error validating activation token:', error);
    return NextResponse.json(
      { success: false, error: 'Error al validar token' },
      { status: 500 }
    );
  }
}