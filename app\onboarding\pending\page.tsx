'use client';

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Clock, CheckCircle, Mail } from 'lucide-react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@clerk/nextjs';

export default function PendingApprovalPage() {
  const router = useRouter();
  const { signOut } = useAuth();

  const handleSignOut = async () => {
    await signOut();
    router.push('/');
  };

  return (
    <div className="min-h-screen flex items-center justify-center p-6 bg-gray-50">
      <Card className="max-w-md w-full text-center shadow-lg">
        <CardHeader className="pb-4">
          <div className="mx-auto mb-4 relative">
            <div className="absolute inset-0 animate-pulse">
              <Clock className="h-16 w-16 text-orange-400 mx-auto opacity-75" />
            </div>
            <Clock className="h-16 w-16 text-orange-400 mx-auto relative" />
          </div>
          <CardTitle className="text-2xl font-bold mb-2">
            ¡Solicitud Enviada con Éxito!
          </CardTitle>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="bg-orange-50 p-4 rounded-lg">
            <p className="text-gray-700 mb-2">
              Tu solicitud ha sido recibida y está <strong>pendiente de aprobación</strong>.
            </p>
            <p className="text-sm text-gray-600">
              Un administrador revisará tu información y documentos en las próximas 24-48 horas.
            </p>
          </div>

          <div className="space-y-4 text-left">
            <div className="flex items-start gap-3">
              <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
              <div>
                <p className="font-medium">Datos recibidos</p>
                <p className="text-sm text-gray-600">Tu información ha sido guardada correctamente</p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <Mail className="h-5 w-5 text-blue-500 mt-0.5" />
              <div>
                <p className="font-medium">Notificación por email</p>
                <p className="text-sm text-gray-600">Recibirás un correo cuando tu cuenta sea aprobada</p>
              </div>
            </div>
          </div>

          <div className="pt-2">
            <Button
              onClick={handleSignOut}
              className="w-full"
              variant="outline"
            >
              Cerrar Sesión
            </Button>
          </div>

          <p className="text-xs text-gray-500 pt-2">
            Si tienes alguna pregunta, puedes <NAME_EMAIL>
          </p>
        </CardContent>
      </Card>
    </div>
  );
}