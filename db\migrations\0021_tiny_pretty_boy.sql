CREATE TABLE "doctor_favorite_medications" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"medicationId" text NOT NULL,
	"dosage" text,
	"frequency" text,
	"notes" text,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "medical_consultations" (
	"id" text PRIMARY KEY NOT NULL,
	"medicalRecordId" text NOT NULL,
	"appointmentId" text,
	"doctorId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"consultationDate" timestamp NOT NULL,
	"consultationType" text NOT NULL,
	"chiefComplaint" text NOT NULL,
	"currentIllness" text,
	"vitalSigns" jsonb,
	"physicalExam" jsonb,
	"diagnoses" jsonb,
	"treatment" jsonb,
	"prescriptions" jsonb,
	"recommendations" text,
	"nextAppointment" timestamp,
	"followUpInstructions" text,
	"attachments" jsonb,
	"status" text DEFAULT 'completed',
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	"createdBy" text NOT NULL,
	"updatedBy" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "medical_documents" (
	"id" text PRIMARY KEY NOT NULL,
	"medicalRecordId" text NOT NULL,
	"consultationId" text,
	"fileName" text NOT NULL,
	"originalName" text NOT NULL,
	"fileType" text NOT NULL,
	"fileSize" integer NOT NULL,
	"filePath" text NOT NULL,
	"documentType" text NOT NULL,
	"title" text NOT NULL,
	"description" text,
	"documentDate" timestamp NOT NULL,
	"relatedDiagnosis" text,
	"laboratory" text,
	"physician" text,
	"visibility" text DEFAULT 'doctor_only',
	"isConfidential" boolean DEFAULT false,
	"status" text DEFAULT 'pending_review',
	"reviewedBy" text,
	"reviewedDate" timestamp,
	"reviewNotes" text,
	"uploadedAt" timestamp DEFAULT now(),
	"uploadedBy" text NOT NULL,
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "medical_records" (
	"id" text PRIMARY KEY NOT NULL,
	"patientId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"primaryDoctorId" text NOT NULL,
	"recordNumber" text NOT NULL,
	"openDate" timestamp DEFAULT now() NOT NULL,
	"status" text DEFAULT 'active',
	"patientSummary" jsonb,
	"isMinor" boolean DEFAULT false,
	"guardianInfo" jsonb,
	"lastAccessDate" timestamp,
	"totalConsultations" integer DEFAULT 0,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	"createdBy" text NOT NULL,
	"updatedBy" text NOT NULL,
	CONSTRAINT "medical_records_recordNumber_unique" UNIQUE("recordNumber")
);
--> statement-breakpoint
CREATE TABLE "patient_medical_history" (
	"id" text PRIMARY KEY NOT NULL,
	"medicalRecordId" text NOT NULL,
	"pathologicalHistory" jsonb,
	"nonPathologicalHistory" jsonb,
	"familyHistory" jsonb,
	"allergies" jsonb,
	"hospitalizations" jsonb,
	"surgeries" jsonb,
	"vaccinations" jsonb,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	"updatedBy" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "prescription_templates" (
	"id" text PRIMARY KEY NOT NULL,
	"doctorId" text NOT NULL,
	"name" text NOT NULL,
	"content" text NOT NULL,
	"category" text,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "appointments" ALTER COLUMN "patientId" DROP NOT NULL;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "regional_settings" jsonb;--> statement-breakpoint
ALTER TABLE "doctor_favorite_medications" ADD CONSTRAINT "doctor_favorite_medications_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "doctor_favorite_medications" ADD CONSTRAINT "doctor_favorite_medications_medicationId_medications_id_fk" FOREIGN KEY ("medicationId") REFERENCES "public"."medications"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_medicalRecordId_medical_records_id_fk" FOREIGN KEY ("medicalRecordId") REFERENCES "public"."medical_records"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_appointmentId_appointments_id_fk" FOREIGN KEY ("appointmentId") REFERENCES "public"."appointments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD CONSTRAINT "medical_consultations_updatedBy_user_id_fk" FOREIGN KEY ("updatedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_medicalRecordId_medical_records_id_fk" FOREIGN KEY ("medicalRecordId") REFERENCES "public"."medical_records"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_consultationId_medical_consultations_id_fk" FOREIGN KEY ("consultationId") REFERENCES "public"."medical_consultations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_reviewedBy_user_id_fk" FOREIGN KEY ("reviewedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_documents" ADD CONSTRAINT "medical_documents_uploadedBy_user_id_fk" FOREIGN KEY ("uploadedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_primaryDoctorId_user_id_fk" FOREIGN KEY ("primaryDoctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_updatedBy_user_id_fk" FOREIGN KEY ("updatedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patient_medical_history" ADD CONSTRAINT "patient_medical_history_medicalRecordId_medical_records_id_fk" FOREIGN KEY ("medicalRecordId") REFERENCES "public"."medical_records"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patient_medical_history" ADD CONSTRAINT "patient_medical_history_updatedBy_user_id_fk" FOREIGN KEY ("updatedBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "prescription_templates" ADD CONSTRAINT "prescription_templates_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "doctor_medication_idx" ON "doctor_favorite_medications" USING btree ("doctorId","medicationId");--> statement-breakpoint
CREATE INDEX "doctor_favorite_medications_doctor_idx" ON "doctor_favorite_medications" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "doctor_favorite_medications_medication_idx" ON "doctor_favorite_medications" USING btree ("medicationId");--> statement-breakpoint
CREATE INDEX "medical_consultations_record_idx" ON "medical_consultations" USING btree ("medicalRecordId");--> statement-breakpoint
CREATE INDEX "medical_consultations_doctor_idx" ON "medical_consultations" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "medical_consultations_appointment_idx" ON "medical_consultations" USING btree ("appointmentId");--> statement-breakpoint
CREATE INDEX "medical_consultations_date_idx" ON "medical_consultations" USING btree ("consultationDate");--> statement-breakpoint
CREATE INDEX "medical_consultations_status_idx" ON "medical_consultations" USING btree ("status");--> statement-breakpoint
CREATE INDEX "medical_consultations_type_idx" ON "medical_consultations" USING btree ("consultationType");--> statement-breakpoint
CREATE INDEX "medical_documents_record_idx" ON "medical_documents" USING btree ("medicalRecordId");--> statement-breakpoint
CREATE INDEX "medical_documents_consultation_idx" ON "medical_documents" USING btree ("consultationId");--> statement-breakpoint
CREATE INDEX "medical_documents_type_idx" ON "medical_documents" USING btree ("documentType");--> statement-breakpoint
CREATE INDEX "medical_documents_status_idx" ON "medical_documents" USING btree ("status");--> statement-breakpoint
CREATE INDEX "medical_documents_uploaded_idx" ON "medical_documents" USING btree ("uploadedAt");--> statement-breakpoint
CREATE INDEX "medical_documents_date_idx" ON "medical_documents" USING btree ("documentDate");--> statement-breakpoint
CREATE INDEX "medical_records_patient_idx" ON "medical_records" USING btree ("patientId");--> statement-breakpoint
CREATE INDEX "medical_records_doctor_idx" ON "medical_records" USING btree ("primaryDoctorId");--> statement-breakpoint
CREATE INDEX "medical_records_consultory_idx" ON "medical_records" USING btree ("consultoryId");--> statement-breakpoint
CREATE INDEX "medical_records_status_idx" ON "medical_records" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "medical_records_number_idx" ON "medical_records" USING btree ("recordNumber");--> statement-breakpoint
CREATE INDEX "medical_records_created_idx" ON "medical_records" USING btree ("createdAt");--> statement-breakpoint
CREATE UNIQUE INDEX "patient_medical_history_record_idx" ON "patient_medical_history" USING btree ("medicalRecordId");--> statement-breakpoint
CREATE INDEX "patient_medical_history_updated_idx" ON "patient_medical_history" USING btree ("updatedAt");--> statement-breakpoint
CREATE INDEX "prescription_templates_doctor_idx" ON "prescription_templates" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "prescription_templates_active_idx" ON "prescription_templates" USING btree ("isActive");