# Manejo Óptimo de Transacciones y Operaciones Críticas de Base de Datos

## Resumen Ejecutivo

Este documento describe las mejores prácticas para manejar operaciones críticas de base de datos en el sistema administrativo, especialmente aquellas que requieren consistencia transaccional.

## Problema Identificado

### Driver Neon HTTP vs Neon Serverless

**Problema original:**
```typescript
// ❌ CONFIGURACIÓN PROBLEMÁTICA (db/drizzle.ts)
import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';

const sql = neon(process.env.DATABASE_URL!);
export const db = drizzle(sql);

// ❌ ESTO FALLA
await db.transaction(async (tx) => {
  // Error: No transactions support in neon-http driver
});
```

**Solución implementada:**
```typescript
// ✅ CONFIGURACIÓN CORRECTA (db/drizzle.ts)
import { drizzle } from 'drizzle-orm/neon-serverless';
import { Pool } from '@neondatabase/serverless';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL!,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

export const db = drizzle(pool);
```

## Cuándo Usar Transacciones

### ✅ Operaciones que REQUIEREN transacciones:

1. **Eliminación de datos relacionados** (caso actual)
2. **Transferencias de dinero/créditos**
3. **Actualizaciones de inventario**
4. **Cambios de estado complejos**
5. **Creación de registros dependientes**

### ❌ Operaciones que NO requieren transacciones:

1. **Operaciones de solo lectura**
2. **Eliminaciones/actualizaciones simples de una tabla**
3. **Logs y métricas**
4. **Cachés y datos no críticos**
5. **Operaciones con servicios externos** (Clerk, S3, etc.)

## Patrones Recomendados

### 1. Patrón de Eliminación Segura

```typescript
// ✅ PATRÓN RECOMENDADO
export async function deleteUserSafely(userId: string, options: DeletionOptions) {
  // 1. Análisis previo
  const analysis = await analyzeUserDeletionImpact(userId);
  
  // 2. Validaciones
  if (!analysis.canDelete) {
    throw new Error('Usuario no puede ser eliminado');
  }

  // 3. Operaciones de BD en transacción
  await db.transaction(async (tx) => {
    // Eliminar en orden: hijos → padres
    if (analysis.notifications.length > 0) {
      await tx.delete(notifications).where(eq(notifications.userId, userId));
    }
    
    if (analysis.associationCodes.length > 0) {
      await tx.delete(associationCodes).where(eq(associationCodes.patientId, userId));
      await tx.delete(associationCodes).where(eq(associationCodes.usedBy, userId));
    }
    
    // ... otras eliminaciones
    
    // Usuario al final
    await tx.delete(user).where(eq(user.id, userId));
  });

  // 4. Servicios externos FUERA de la transacción
  if (options.deleteFromClerk) {
    try {
      await clerk.users.deleteUser(userId);
    } catch (clerkError) {
      // Log error pero no hacer rollback de BD
      console.error('Error eliminando de Clerk:', clerkError);
    }
  }
}
```

### 2. Patrón de Compensación para Servicios Externos

```typescript
// lib/compensation.ts
export class CompensationManager {
  private compensationActions: (() => Promise<void>)[] = [];

  addCompensation(action: () => Promise<void>) {
    this.compensationActions.push(action);
  }

  async executeCompensations() {
    for (const action of this.compensationActions.reverse()) {
      try {
        await action();
      } catch (error) {
        console.error('Error en compensación:', error);
      }
    }
  }
}

// Uso en operaciones complejas
export async function complexOperation(data: any) {
  const compensation = new CompensationManager();
  
  try {
    // 1. Operaciones de BD (atómicas)
    await db.transaction(async (tx) => {
      // Operaciones críticas
    });

    // 2. Servicios externos con compensación
    await externalService.create(data);
    compensation.addCompensation(() => externalService.delete(data.id));

    await anotherService.update(data);
    compensation.addCompensation(() => anotherService.revert(data.id));

  } catch (error) {
    await compensation.executeCompensations();
    throw error;
  }
}
```

### 3. Patrón de Transferencia Segura

```typescript
// ✅ TRANSFERENCIA DE CRÉDITOS SEGURA
export async function transferCredits(fromUserId: string, toUserId: string, amount: number) {
  // Validaciones previas
  const fromUser = await db.select().from(userCredits).where(eq(userCredits.userId, fromUserId));
  if (fromUser[0].credits < amount) {
    throw new Error('Créditos insuficientes');
  }

  // Operación atómica
  await db.transaction(async (tx) => {
    // Debitar
    await tx.update(userCredits).set({ 
      credits: sql`${userCredits.credits} - ${amount}` 
    }).where(eq(userCredits.userId, fromUserId));
    
    // Acreditar
    await tx.update(userCredits).set({ 
      credits: sql`${userCredits.credits} + ${amount}` 
    }).where(eq(userCredits.userId, toUserId));
    
    // Registrar transacción
    await tx.insert(creditTransactions).values({
      fromUserId,
      toUserId,
      amount,
      type: 'transfer',
      timestamp: new Date()
    });
  });
}
```

## Configuración de Desarrollo vs Producción

### Desarrollo
```typescript
// config/database.ts
export const getDatabaseConfig = () => {
  const env = process.env.NODE_ENV || 'development';

  const configs = {
    development: {
      connectionString: process.env.DATABASE_URL,
      max: 5,
      idleTimeoutMillis: 10000,
      ssl: false,
      logging: true
    },
    production: {
      connectionString: process.env.DATABASE_URL,
      max: 20,
      idleTimeoutMillis: 30000,
      connectionTimeoutMillis: 2000,
      ssl: { rejectUnauthorized: false },
      logging: false
    }
  };

  return configs[env] || configs.development;
};
```

### Monitoreo de Transacciones

```typescript
// lib/db-monitor.ts
export const createMonitoredDB = (pool: Pool) => {
  const db = drizzle(pool);
  
  return new Proxy(db, {
    get(target, prop) {
      if (prop === 'transaction') {
        return async (callback: any) => {
          const startTime = Date.now();
          try {
            const result = await target.transaction(callback);
            const duration = Date.now() - startTime;
            
            if (duration > 1000) { // Log transacciones lentas
              console.warn(`Transacción lenta: ${duration}ms`);
            }
            
            return result;
          } catch (error) {
            console.error('Error en transacción:', error);
            throw error;
          }
        };
      }
      return target[prop];
    }
  });
};
```

## Checklist de Implementación

### ✅ Configuración Base
- [ ] Cambiar a `drizzle-orm/neon-serverless`
- [ ] Configurar Pool con límites apropiados
- [ ] Instalar dependencias: `npm install ws bufferutil`

### ✅ Operaciones Críticas
- [ ] Identificar operaciones que requieren transacciones
- [ ] Implementar análisis previo para operaciones complejas
- [ ] Separar operaciones de BD de servicios externos
- [ ] Añadir manejo de errores y logging

### ✅ Patrones de Seguridad
- [ ] Usar transacciones para operaciones relacionadas
- [ ] Implementar compensación para servicios externos
- [ ] Validar datos antes de operaciones críticas
- [ ] Registrar auditoría de operaciones importantes

## Casos de Uso Específicos del Sistema

### 1. Eliminación de Usuario (Implementado)
```typescript
// app/api/admin/users/[id]/route.ts
// Patrón: Análisis → Transacción → Servicios externos
```

### 2. Aprobación de Solicitudes
```typescript
// app/api/admin/requests/[id]/approve/route.ts
export async function approveRequest(requestId: string, adminId: string) {
  await db.transaction(async (tx) => {
    // 1. Actualizar solicitud
    await tx.update(registrationRequests).set({
      status: 'approved',
      approvedBy: adminId,
      approvedAt: new Date()
    }).where(eq(registrationRequests.id, requestId));
    
    // 2. Activar usuario
    await tx.update(user).set({
      overallStatus: 'active'
    }).where(eq(user.id, request.userId));
    
    // 3. Crear notificación
    await tx.insert(notifications).values({
      userId: request.userId,
      message: 'Tu solicitud ha sido aprobada',
      type: 'approval'
    });
  });
  
  // Enviar email (fuera de transacción)
  await sendApprovalEmail(request.userId);
}
```

### 3. Cambio de Roles Masivo
```typescript
// app/api/admin/users/bulk-actions/route.ts
export async function bulkUpdateRoles(userIds: string[], newRole: string, adminId: string) {
  await db.transaction(async (tx) => {
    // Actualizar todos los usuarios
    await tx.update(userRoles).set({
      role: newRole,
      updatedBy: adminId,
      updatedAt: new Date()
    }).where(inArray(userRoles.userId, userIds));
    
    // Registrar auditoría
    await tx.insert(auditLog).values(
      userIds.map(userId => ({
        userId,
        action: 'role_change',
        previousValue: 'old_role', // obtener del análisis previo
        newValue: newRole,
        adminId,
        timestamp: new Date()
      }))
    );
  });
}
```

## Troubleshooting Común

### Error: "No transactions support in neon-http driver"
```bash
# Solución: Cambiar configuración
npm install ws bufferutil
# Actualizar db/drizzle.ts según la configuración correcta
```

### Transacciones Lentas
```typescript
// Añadir monitoreo
const startTime = Date.now();
await db.transaction(async (tx) => {
  // operaciones
});
const duration = Date.now() - startTime;
if (duration > 1000) console.warn(`Transacción lenta: ${duration}ms`);
```

### Deadlocks
```typescript
// Siempre acceder a tablas en el mismo orden
await db.transaction(async (tx) => {
  // 1. Siempre primero: tablas hijas
  await tx.delete(notifications)...
  await tx.delete(associationCodes)...
  
  // 2. Después: tablas padre
  await tx.delete(userRoles)...
  await tx.delete(user)...
});
```

## Métricas y Monitoreo

### Métricas Clave
- Duración de transacciones
- Tasa de fallos de transacciones
- Número de rollbacks
- Conexiones activas del pool

### Alertas Recomendadas
- Transacciones > 5 segundos
- Tasa de fallos > 5%
- Pool de conexiones > 80% utilización
- Operaciones críticas fallidas

## Conclusión

La implementación de transacciones apropiadas es crucial para mantener la integridad de datos en operaciones críticas. El cambio de `neon-http` a `neon-serverless` driver permite el uso de transacciones reales, mejorando significativamente la robustez del sistema.

**Regla de oro:** Si una operación modifica múltiples tablas relacionadas o requiere consistencia atómica, usa transacciones. Para servicios externos, implementa compensación. 