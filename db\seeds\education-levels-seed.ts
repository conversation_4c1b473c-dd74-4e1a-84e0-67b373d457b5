import { db } from '@/db/drizzle';
import { educationLevels } from '@/db/schema';

export const educationLevelsData = [
  {
    id: 'no-education',
    name: 'Sin educación formal',
    order: 0,
    description: 'Persona sin estudios formales'
  },
  {
    id: 'primary-incomplete',
    name: 'Primaria incompleta',
    order: 1,
    description: 'Educación primaria no terminada'
  },
  {
    id: 'primary-complete',
    name: 'Primaria completa',
    order: 2,
    description: 'Educación primaria finalizada (6to grado)'
  },
  {
    id: 'secondary-incomplete',
    name: 'Secundaria incompleta',
    order: 3,
    description: 'Educación básica o diversificado no terminado'
  },
  {
    id: 'secondary-complete',
    name: 'Secundaria completa',
    order: 4,
    description: 'Básicos y diversificado completos'
  },
  {
    id: 'technical-education',
    name: 'Educación técnica',
    order: 5,
    description: 'Carrera técnica o vocacional'
  },
  {
    id: 'university-incomplete',
    name: 'Universidad incompleta',
    order: 6,
    description: 'Estudios universitarios no finalizados'
  },
  {
    id: 'university-complete',
    name: 'Universidad completa',
    order: 7,
    description: 'Licenciatura o título universitario'
  },
  {
    id: 'postgraduate',
    name: 'Posgrado',
    order: 8,
    description: 'Especialización, maestría o postgrado'
  },
  {
    id: 'doctorate',
    name: 'Doctorado',
    order: 9,
    description: 'Grado doctoral o PhD'
  }
];

export async function seedEducationLevels() {
  console.log('🎓 Seeding education levels...');
  
  try {
    await db.insert(educationLevels).values(educationLevelsData).onConflictDoNothing();
    console.log('✅ Education levels seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding education levels:', error);
    throw error;
  }
}