import { db } from '@/db/drizzle';
import { pathologicalHistory } from '@/db/schema';

export const pathologicalHistoryData = [
  // Enfermedades cardiovasculares
  {
    id: 'hypertension',
    name: 'Hipertensión arterial',
    category: 'Cardiovascular',
    icd11Code: 'BA00',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: false,
    riskLevel: 'medium',
    description: 'Presión arterial elevada de forma crónica',
    symptoms: ['Dolor de cabeza', 'Mareos', 'Visión borrosa'],
    treatments: ['Medicamentos antihipertensivos', 'Dieta baja en sodio', 'Ejercicio'],
    order: 1
  },
  {
    id: 'congenital-heart-disease',
    name: 'Cardiopatía congénita',
    category: 'Cardiovascular',
    icd11Code: 'LA8Y',
    severity: 'high',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Malformación del corazón presente desde el nacimiento',
    symptoms: ['Cianosis', 'Fatiga', 'Dificultad respiratoria'],
    treatments: ['Cirugía correctiva', 'Medicamentos cardiológicos', 'Seguimiento cardiopediátrico'],
    order: 2
  },

  // Enfermedades respiratorias
  {
    id: 'asthma',
    name: 'Asma bronquial',
    category: 'Respiratorio',
    icd11Code: 'CA23',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'medium',
    description: 'Enfermedad crónica que inflama y estrecha las vías respiratorias',
    symptoms: ['Sibilancias', 'Tos seca', 'Dificultad respiratoria', 'Opresión en el pecho'],
    treatments: ['Broncodilatadores', 'Corticoides inhalados', 'Evitar desencadenantes'],
    order: 3
  },
  {
    id: 'bronchiolitis',
    name: 'Bronquiolitis',
    category: 'Respiratorio',
    icd11Code: 'CA20.1',
    severity: 'moderate',
    isHereditary: false,
    requiresSpecialistFollow: false,
    commonInChildren: true,
    riskLevel: 'medium',
    description: 'Infección viral de las vías respiratorias pequeñas en lactantes',
    symptoms: ['Tos', 'Sibilancias', 'Dificultad para alimentarse'],
    treatments: ['Cuidados de soporte', 'Humidificación', 'Monitoreo respiratorio'],
    order: 4
  },

  // Enfermedades endocrinas
  {
    id: 'diabetes-type1',
    name: 'Diabetes mellitus tipo 1',
    category: 'Endocrino',
    icd11Code: '5A10',
    severity: 'high',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Enfermedad autoinmune que destruye las células productoras de insulina',
    symptoms: ['Poliuria', 'Polidipsia', 'Pérdida de peso', 'Fatiga'],
    treatments: ['Insulinoterapia', 'Monitoreo de glucosa', 'Educación diabetológica'],
    order: 5
  },
  {
    id: 'hypothyroidism',
    name: 'Hipotiroidismo',
    category: 'Endocrino',
    icd11Code: '5A00',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: false,
    riskLevel: 'low',
    description: 'Deficiencia de hormonas tiroideas',
    symptoms: ['Fatiga', 'Aumento de peso', 'Intolerancia al frío'],
    treatments: ['Levotiroxina', 'Monitoreo hormonal'],
    order: 6
  },

  // Enfermedades neurológicas
  {
    id: 'epilepsy',
    name: 'Epilepsia',
    category: 'Neurológico',
    icd11Code: '8A61',
    severity: 'high',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Trastorno neurológico caracterizado por convulsiones recurrentes',
    symptoms: ['Convulsiones', 'Pérdida de conciencia', 'Movimientos involuntarios'],
    treatments: ['Anticonvulsivantes', 'Evitar desencadenantes', 'Seguimiento neurológico'],
    order: 7
  },
  {
    id: 'cerebral-palsy',
    name: 'Parálisis cerebral',
    category: 'Neurológico',
    icd11Code: '8A05',
    severity: 'high',
    isHereditary: false,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Grupo de trastornos del movimiento y postura por lesión cerebral',
    symptoms: ['Espasticidad', 'Problemas de coordinación', 'Retraso del desarrollo'],
    treatments: ['Fisioterapia', 'Terapia ocupacional', 'Medicamentos antiespasmódicos'],
    order: 8
  },

  // Enfermedades gastrointestinales
  {
    id: 'gastroesophageal-reflux',
    name: 'Reflujo gastroesofágico',
    category: 'Gastrointestinal',
    icd11Code: 'DA22',
    severity: 'moderate',
    isHereditary: false,
    requiresSpecialistFollow: false,
    commonInChildren: true,
    riskLevel: 'low',
    description: 'Retorno del contenido gástrico al esófago',
    symptoms: ['Regurgitación', 'Vómitos', 'Irritabilidad', 'Tos nocturna'],
    treatments: ['Cambios alimentarios', 'Posición adecuada', 'Medicamentos antiácidos'],
    order: 9
  },
  {
    id: 'celiac-disease',
    name: 'Enfermedad celíaca',
    category: 'Gastrointestinal',
    icd11Code: 'DA90.0',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'medium',
    description: 'Intolerancia permanente al gluten',
    symptoms: ['Diarrea crónica', 'Distensión abdominal', 'Retraso del crecimiento'],
    treatments: ['Dieta libre de gluten', 'Suplementos vitamínicos', 'Seguimiento nutricional'],
    order: 10
  },

  // Enfermedades renales
  {
    id: 'chronic-kidney-disease',
    name: 'Enfermedad renal crónica',
    category: 'Renal',
    icd11Code: 'GB61',
    severity: 'high',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: false,
    riskLevel: 'high',
    description: 'Pérdida gradual de la función renal',
    symptoms: ['Fatiga', 'Hinchazón', 'Cambios en la orina'],
    treatments: ['Control de presión arterial', 'Dieta especial', 'Diálisis eventual'],
    order: 11
  },

  // Enfermedades hematológicas
  {
    id: 'sickle-cell-anemia',
    name: 'Anemia falciforme',
    category: 'Hematológico',
    icd11Code: '3A51.00',
    severity: 'high',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Enfermedad genética que afecta los glóbulos rojos',
    symptoms: ['Crisis de dolor', 'Fatiga', 'Ictericia', 'Infecciones frecuentes'],
    treatments: ['Manejo del dolor', 'Hidroxiurea', 'Transfusiones sanguíneas'],
    order: 12
  },

  // Trastornos del desarrollo
  {
    id: 'autism-spectrum',
    name: 'Trastorno del espectro autista',
    category: 'Desarrollo',
    icd11Code: '6A02',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'medium',
    description: 'Trastorno del neurodesarrollo que afecta la comunicación y comportamiento',
    symptoms: ['Dificultades sociales', 'Comportamientos repetitivos', 'Problemas de comunicación'],
    treatments: ['Terapia conductual', 'Terapia del habla', 'Educación especializada'],
    order: 13
  },
  {
    id: 'adhd',
    name: 'TDAH (Trastorno por déficit de atención)',
    category: 'Desarrollo',
    icd11Code: '6A05',
    severity: 'moderate',
    isHereditary: true,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'low',
    description: 'Trastorno que afecta la atención, hiperactividad e impulsividad',
    symptoms: ['Falta de atención', 'Hiperactividad', 'Impulsividad'],
    treatments: ['Medicamentos estimulantes', 'Terapia conductual', 'Apoyo educativo'],
    order: 14
  },

  // Enfermedades oncológicas
  {
    id: 'leukemia',
    name: 'Leucemia infantil',
    category: 'Oncológico',
    icd11Code: '2A60',
    severity: 'high',
    isHereditary: false,
    requiresSpecialistFollow: true,
    commonInChildren: true,
    riskLevel: 'high',
    description: 'Cáncer de la sangre y médula ósea',
    symptoms: ['Fatiga extrema', 'Fiebre', 'Moretones fáciles', 'Pérdida de peso'],
    treatments: ['Quimioterapia', 'Radioterapia', 'Trasplante de médula ósea'],
    order: 15
  }
];

export async function seedPathologicalHistory() {
  console.log('🩺 Seeding pathological history...');
  
  try {
    await db.insert(pathologicalHistory).values(pathologicalHistoryData).onConflictDoNothing();
    console.log('✅ Pathological history seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding pathological history:', error);
    throw error;
  }
}