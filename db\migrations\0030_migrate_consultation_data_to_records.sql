-- Migración: Migrar datos demográficos de consultas a expedientes médicos
-- Fecha: 2025-01-27
-- Descripción: Mover datos demográficos y administrativos desde consultas al expediente médico

-- 1. Migrar datos demográficos de consultas a expedientes médicos
UPDATE medical_records mr
SET demographics = COALESCE(
  (
    SELECT mc.demographics
    FROM medical_consultations mc
    WHERE mc.medical_record_id = mr.id
      AND mc.demographics IS NOT NULL
      AND mc.demographics != '{}'::jsonb
    ORDER BY mc.updated_at DESC
    LIMIT 1
  ),
  '{}'::jsonb
)
WHERE mr.demographics = '{}'::jsonb OR mr.demographics IS NULL;

-- 2. Migrar datos administrativos de consultas a expedientes médicos
UPDATE medical_records mr
SET administrative = COALESCE(
  (
    SELECT mc.administrative
    FROM medical_consultations mc
    WHERE mc.medical_record_id = mr.id
      AND mc.administrative IS NOT NULL
      AND mc.administrative != '{}'::jsonb
    ORDER BY mc.updated_at DESC
    LIMIT 1
  ),
  '{}'::jsonb
)
WHERE mr.administrative = '{}'::jsonb OR mr.administrative IS NULL;

-- 3. Migrar antecedentes patológicos a la tabla patient_medical_history
INSERT INTO patient_medical_history (id, medical_record_id, pathological_history, non_pathological_history, updated_by)
SELECT 
  gen_random_uuid()::text as id,
  mr.id as medical_record_id,
  COALESCE(mc.pathological_history, '[]'::jsonb) as pathological_history,
  COALESCE(mc.non_pathological_history, '[]'::jsonb) as non_pathological_history,
  mr.created_by as updated_by
FROM medical_records mr
LEFT JOIN LATERAL (
  SELECT 
    mc.pathological_history,
    mc.non_pathological_history
  FROM medical_consultations mc
  WHERE mc.medical_record_id = mr.id
    AND (
      (mc.pathological_history IS NOT NULL AND mc.pathological_history != '[]'::jsonb)
      OR (mc.non_pathological_history IS NOT NULL AND mc.non_pathological_history != '[]'::jsonb)
    )
  ORDER BY mc.updated_at DESC
  LIMIT 1
) mc ON true
WHERE NOT EXISTS (
  SELECT 1 FROM patient_medical_history pmh 
  WHERE pmh.medical_record_id = mr.id
)
AND (
  mc.pathological_history IS NOT NULL 
  OR mc.non_pathological_history IS NOT NULL
);

-- 4. Comentario sobre limpieza futura
-- NOTA: Los datos migrados permanecerán en medical_consultations por compatibilidad
-- En una fase posterior se pueden eliminar las columnas redundantes:
-- ALTER TABLE medical_consultations DROP COLUMN demographics;
-- ALTER TABLE medical_consultations DROP COLUMN administrative;
-- ALTER TABLE medical_consultations DROP COLUMN pathological_history;
-- ALTER TABLE medical_consultations DROP COLUMN non_pathological_history;