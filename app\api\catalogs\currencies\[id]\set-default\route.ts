import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { currencies } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Establecer como moneda predeterminada
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar la moneda predeterminada.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que la moneda existe y está activa
    const existingCurrency = await db
      .select()
      .from(currencies)
      .where(eq(currencies.id, id))
      .limit(1);

    if (existingCurrency.length === 0) {
      return NextResponse.json({ error: 'Moneda no encontrada' }, { status: 404 });
    }

    if (!existingCurrency[0].isActive) {
      return NextResponse.json({ 
        error: 'No se puede establecer como predeterminada una moneda inactiva.' 
      }, { status: 400 });
    }

    // Si ya es la predeterminada, no hacer nada
    if (existingCurrency[0].isDefault) {
      return NextResponse.json({ 
        success: true, 
        data: existingCurrency[0],
        message: 'Esta moneda ya es la predeterminada'
      });
    }

    // Quitar el flag de todas las demás monedas
    await db
      .update(currencies)
      .set({ isDefault: false, updatedAt: new Date() });

    // Establecer esta como predeterminada
    const [updatedCurrency] = await db
      .update(currencies)
      .set({
        isDefault: true,
        updatedAt: new Date()
      })
      .where(eq(currencies.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedCurrency,
      message: 'Moneda establecida como predeterminada exitosamente'
    });

  } catch (error) {
    console.error('Error setting default currency:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}