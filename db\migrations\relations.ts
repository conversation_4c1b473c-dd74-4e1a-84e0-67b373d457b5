import { relations } from "drizzle-orm/relations";
import { user, associationCodes, doctorAssistantRelations, consultories, notifications, patientGuardianRelations, registrationRequests, session, subscription, countries, departments, municipalities, occupations, relationships, account, appointments, medicalRecords, userRoles, userConsultories, userDocuments, userSpecialties, medicalSpecialties } from "./schema";

export const associationCodesRelations = relations(associationCodes, ({one}) => ({
	user_patientId: one(user, {
		fields: [associationCodes.patientId],
		references: [user.id],
		relationName: "associationCodes_patientId_user_id"
	}),
	user_usedBy: one(user, {
		fields: [associationCodes.usedBy],
		references: [user.id],
		relationName: "associationCodes_usedBy_user_id"
	}),
}));

export const userRelations = relations(user, ({one, many}) => ({
	associationCodes_patientId: many(associationCodes, {
		relationName: "associationCodes_patientId_user_id"
	}),
	associationCodes_usedBy: many(associationCodes, {
		relationName: "associationCodes_usedBy_user_id"
	}),
	doctorAssistantRelations_doctorId: many(doctorAssistantRelations, {
		relationName: "doctorAssistantRelations_doctorId_user_id"
	}),
	doctorAssistantRelations_assistantId: many(doctorAssistantRelations, {
		relationName: "doctorAssistantRelations_assistantId_user_id"
	}),
	notifications: many(notifications),
	patientGuardianRelations_patientId: many(patientGuardianRelations, {
		relationName: "patientGuardianRelations_patientId_user_id"
	}),
	patientGuardianRelations_guardianId: many(patientGuardianRelations, {
		relationName: "patientGuardianRelations_guardianId_user_id"
	}),
	registrationRequests_userId: many(registrationRequests, {
		relationName: "registrationRequests_userId_user_id"
	}),
	registrationRequests_reviewedBy: many(registrationRequests, {
		relationName: "registrationRequests_reviewedBy_user_id"
	}),
	sessions: many(session),
	subscriptions: many(subscription),
	country: one(countries, {
		fields: [user.countryId],
		references: [countries.id]
	}),
	department: one(departments, {
		fields: [user.departmentId],
		references: [departments.id]
	}),
	municipality: one(municipalities, {
		fields: [user.municipalityId],
		references: [municipalities.id]
	}),
	occupation: one(occupations, {
		fields: [user.occupationId],
		references: [occupations.id]
	}),
	relationship: one(relationships, {
		fields: [user.emergencyRelationshipId],
		references: [relationships.id]
	}),
	accounts: many(account),
	appointments_patientId: many(appointments, {
		relationName: "appointments_patientId_user_id"
	}),
	appointments_doctorId: many(appointments, {
		relationName: "appointments_doctorId_user_id"
	}),
	appointments_createdBy: many(appointments, {
		relationName: "appointments_createdBy_user_id"
	}),
	appointments_lastModifiedBy: many(appointments, {
		relationName: "appointments_lastModifiedBy_user_id"
	}),
	medicalRecords_patientId: many(medicalRecords, {
		relationName: "medicalRecords_patientId_user_id"
	}),
	medicalRecords_doctorId: many(medicalRecords, {
		relationName: "medicalRecords_doctorId_user_id"
	}),
	medicalRecords_createdBy: many(medicalRecords, {
		relationName: "medicalRecords_createdBy_user_id"
	}),
	medicalRecords_lastModifiedBy: many(medicalRecords, {
		relationName: "medicalRecords_lastModifiedBy_user_id"
	}),
	medicalRecords_reviewedBy: many(medicalRecords, {
		relationName: "medicalRecords_reviewedBy_user_id"
	}),
	userRoles: many(userRoles),
	userConsultories: many(userConsultories),
	userDocuments_userId: many(userDocuments, {
		relationName: "userDocuments_userId_user_id"
	}),
	userDocuments_verifiedBy: many(userDocuments, {
		relationName: "userDocuments_verifiedBy_user_id"
	}),
	userDocuments_uploadedBy: many(userDocuments, {
		relationName: "userDocuments_uploadedBy_user_id"
	}),
	userSpecialties: many(userSpecialties),
}));

export const doctorAssistantRelationsRelations = relations(doctorAssistantRelations, ({one}) => ({
	user_doctorId: one(user, {
		fields: [doctorAssistantRelations.doctorId],
		references: [user.id],
		relationName: "doctorAssistantRelations_doctorId_user_id"
	}),
	user_assistantId: one(user, {
		fields: [doctorAssistantRelations.assistantId],
		references: [user.id],
		relationName: "doctorAssistantRelations_assistantId_user_id"
	}),
	consultory: one(consultories, {
		fields: [doctorAssistantRelations.consultoryId],
		references: [consultories.id]
	}),
}));

export const consultoriesRelations = relations(consultories, ({many}) => ({
	doctorAssistantRelations: many(doctorAssistantRelations),
	appointments: many(appointments),
	medicalRecords: many(medicalRecords),
	userConsultories: many(userConsultories),
}));

export const notificationsRelations = relations(notifications, ({one}) => ({
	user: one(user, {
		fields: [notifications.userId],
		references: [user.id]
	}),
}));

export const patientGuardianRelationsRelations = relations(patientGuardianRelations, ({one}) => ({
	user_patientId: one(user, {
		fields: [patientGuardianRelations.patientId],
		references: [user.id],
		relationName: "patientGuardianRelations_patientId_user_id"
	}),
	user_guardianId: one(user, {
		fields: [patientGuardianRelations.guardianId],
		references: [user.id],
		relationName: "patientGuardianRelations_guardianId_user_id"
	}),
}));

export const registrationRequestsRelations = relations(registrationRequests, ({one}) => ({
	user_userId: one(user, {
		fields: [registrationRequests.userId],
		references: [user.id],
		relationName: "registrationRequests_userId_user_id"
	}),
	user_reviewedBy: one(user, {
		fields: [registrationRequests.reviewedBy],
		references: [user.id],
		relationName: "registrationRequests_reviewedBy_user_id"
	}),
}));

export const sessionRelations = relations(session, ({one}) => ({
	user: one(user, {
		fields: [session.userId],
		references: [user.id]
	}),
}));

export const subscriptionRelations = relations(subscription, ({one}) => ({
	user: one(user, {
		fields: [subscription.userId],
		references: [user.id]
	}),
}));

export const countriesRelations = relations(countries, ({many}) => ({
	users: many(user),
	departments: many(departments),
}));

export const departmentsRelations = relations(departments, ({one, many}) => ({
	users: many(user),
	municipalities: many(municipalities),
	country: one(countries, {
		fields: [departments.countryId],
		references: [countries.id]
	}),
}));

export const municipalitiesRelations = relations(municipalities, ({one, many}) => ({
	users: many(user),
	department: one(departments, {
		fields: [municipalities.departmentId],
		references: [departments.id]
	}),
}));

export const occupationsRelations = relations(occupations, ({many}) => ({
	users: many(user),
}));

export const relationshipsRelations = relations(relationships, ({many}) => ({
	users: many(user),
}));

export const accountRelations = relations(account, ({one}) => ({
	user: one(user, {
		fields: [account.userId],
		references: [user.id]
	}),
}));

export const appointmentsRelations = relations(appointments, ({one}) => ({
	user_patientId: one(user, {
		fields: [appointments.patientId],
		references: [user.id],
		relationName: "appointments_patientId_user_id"
	}),
	user_doctorId: one(user, {
		fields: [appointments.doctorId],
		references: [user.id],
		relationName: "appointments_doctorId_user_id"
	}),
	consultory: one(consultories, {
		fields: [appointments.consultoryId],
		references: [consultories.id]
	}),
	user_createdBy: one(user, {
		fields: [appointments.createdBy],
		references: [user.id],
		relationName: "appointments_createdBy_user_id"
	}),
	user_lastModifiedBy: one(user, {
		fields: [appointments.lastModifiedBy],
		references: [user.id],
		relationName: "appointments_lastModifiedBy_user_id"
	}),
}));

export const medicalRecordsRelations = relations(medicalRecords, ({one}) => ({
	user_patientId: one(user, {
		fields: [medicalRecords.patientId],
		references: [user.id],
		relationName: "medicalRecords_patientId_user_id"
	}),
	user_doctorId: one(user, {
		fields: [medicalRecords.doctorId],
		references: [user.id],
		relationName: "medicalRecords_doctorId_user_id"
	}),
	consultory: one(consultories, {
		fields: [medicalRecords.consultoryId],
		references: [consultories.id]
	}),
	user_createdBy: one(user, {
		fields: [medicalRecords.createdBy],
		references: [user.id],
		relationName: "medicalRecords_createdBy_user_id"
	}),
	user_lastModifiedBy: one(user, {
		fields: [medicalRecords.lastModifiedBy],
		references: [user.id],
		relationName: "medicalRecords_lastModifiedBy_user_id"
	}),
	user_reviewedBy: one(user, {
		fields: [medicalRecords.reviewedBy],
		references: [user.id],
		relationName: "medicalRecords_reviewedBy_user_id"
	}),
}));

export const userRolesRelations = relations(userRoles, ({one}) => ({
	user: one(user, {
		fields: [userRoles.userId],
		references: [user.id]
	}),
}));

export const userConsultoriesRelations = relations(userConsultories, ({one}) => ({
	user: one(user, {
		fields: [userConsultories.userId],
		references: [user.id]
	}),
	consultory: one(consultories, {
		fields: [userConsultories.consultoryId],
		references: [consultories.id]
	}),
}));

export const userDocumentsRelations = relations(userDocuments, ({one}) => ({
	user_userId: one(user, {
		fields: [userDocuments.userId],
		references: [user.id],
		relationName: "userDocuments_userId_user_id"
	}),
	user_verifiedBy: one(user, {
		fields: [userDocuments.verifiedBy],
		references: [user.id],
		relationName: "userDocuments_verifiedBy_user_id"
	}),
	user_uploadedBy: one(user, {
		fields: [userDocuments.uploadedBy],
		references: [user.id],
		relationName: "userDocuments_uploadedBy_user_id"
	}),
}));

export const userSpecialtiesRelations = relations(userSpecialties, ({one}) => ({
	user: one(user, {
		fields: [userSpecialties.userId],
		references: [user.id]
	}),
	medicalSpecialty: one(medicalSpecialties, {
		fields: [userSpecialties.specialtyId],
		references: [medicalSpecialties.id]
	}),
}));

export const medicalSpecialtiesRelations = relations(medicalSpecialties, ({many}) => ({
	userSpecialties: many(userSpecialties),
}));