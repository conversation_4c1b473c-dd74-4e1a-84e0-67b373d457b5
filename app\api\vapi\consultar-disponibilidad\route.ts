import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, doctorSchedules, doctorScheduleExceptions, user } from '@/db/schema';
import { eq, and, gte, lte, or, isNotNull } from 'drizzle-orm';

interface VAPISlot {
  id: string;
  spoken: string;
  date: string;
  time: string;
}

export async function POST(request: NextRequest) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { doctorId, fecha, limite = 5 } = body;

    if (!doctorId) {
      return NextResponse.json(
        { success: false, error: 'doctorId es requerido' },
        { status: 400 }
      );
    }

    // Verificar que el doctor existe y está activo
    const doctor = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
      })
      .from(user)
      .where(
        and(
          eq(user.id, doctorId),
          eq(user.role, 'doctor'),
          eq(user.isActive, true)
        )
      )
      .limit(1);

    if (doctor.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Doctor no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const doctorInfo = doctor[0];

    // Obtener horarios del doctor
    const doctorSchedule = await db
      .select()
      .from(doctorSchedules)
      .where(
        and(
          eq(doctorSchedules.doctorId, doctorId),
          eq(doctorSchedules.isActive, true),
          eq(doctorSchedules.allowOnlineBooking, true)
        )
      );

    if (doctorSchedule.length === 0) {
      return NextResponse.json({
        success: true,
        data: {
          doctorName: `${doctorInfo.firstName} ${doctorInfo.lastName}`,
          slots: [],
          message: 'Doctor no tiene horarios disponibles para reserva online'
        }
      });
    }

    // Generar slots disponibles
    const availableSlots: VAPISlot[] = [];
    const now = new Date();
    const maxDaysToCheck = fecha ? 1 : 14; // Si especifica fecha, solo ese día, sino próximos 14 días
    const appointmentDuration = 30;

    // Si se especifica fecha, empezar desde esa fecha
    const startDate = fecha ? new Date(fecha) : now;
    
    for (let dayOffset = 0; dayOffset < maxDaysToCheck && availableSlots.length < limite; dayOffset++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + dayOffset);
      currentDate.setHours(0, 0, 0, 0);
      
      const dayOfWeek = currentDate.getDay();
      
      // Buscar horario para este día de la semana
      const daySchedule = doctorSchedule.find(schedule => schedule.dayOfWeek === dayOfWeek);
      if (!daySchedule) continue;
      
      // Verificar excepciones
      const exceptions = await db
        .select()
        .from(doctorScheduleExceptions)
        .where(
          and(
            eq(doctorScheduleExceptions.doctorId, doctorId),
            eq(doctorScheduleExceptions.exceptionDate, currentDate)
          )
        );
      
      if (exceptions.some(ex => ex.type === 'closed' || (ex.type === 'vacation' && ex.isAllDay))) {
        continue;
      }
      
      // Obtener citas ya programadas
      const startOfDay = new Date(currentDate);
      const endOfDay = new Date(currentDate);
      endOfDay.setHours(23, 59, 59, 999);
      
      const existingAppointments = await db
        .select({
          startTime: appointments.startTime,
          endTime: appointments.endTime,
        })
        .from(appointments)
        .where(
          and(
            eq(appointments.doctorId, doctorId),
            gte(appointments.startTime, startOfDay),
            lte(appointments.startTime, endOfDay),
            or(
              eq(appointments.status, 'scheduled'),
              eq(appointments.status, 'confirmed'),
              eq(appointments.status, 'checked_in'),
              eq(appointments.status, 'in_progress')
            )
          )
        );
      
      // Generar slots
      const workStartTime = daySchedule.startTime || '08:00';
      const workEndTime = daySchedule.endTime || '17:00';
      
      const [startHour, startMinute] = workStartTime.split(':').map(Number);
      const [endHour, endMinute] = workEndTime.split(':').map(Number);
      
      const workStart = new Date(currentDate);
      workStart.setHours(startHour, startMinute, 0, 0);
      
      const workEnd = new Date(currentDate);
      workEnd.setHours(endHour, endMinute, 0, 0);
      
      // Solo ofrecer slots que sean al menos 2 horas en el futuro (a menos que se especifique fecha)
      const minBookingTime = fecha ? currentDate : new Date(now.getTime() + (2 * 60 * 60 * 1000));
      
      let currentSlot = new Date(workStart);
      
      while (currentSlot < workEnd && availableSlots.length < limite) {
        if (currentSlot < minBookingTime) {
          currentSlot = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
          continue;
        }
        
        const slotEnd = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
        
        // Verificar conflictos con citas existentes
        const hasConflict = existingAppointments.some(apt => {
          const aptStart = new Date(apt.startTime);
          const aptEnd = new Date(apt.endTime);
          return (currentSlot < aptEnd && slotEnd > aptStart);
        });
        
        // Verificar horario de almuerzo
        let inLunchBreak = false;
        if (daySchedule.lunchBreakStart && daySchedule.lunchBreakEnd) {
          const [lunchStartHour, lunchStartMinute] = daySchedule.lunchBreakStart.split(':').map(Number);
          const [lunchEndHour, lunchEndMinute] = daySchedule.lunchBreakEnd.split(':').map(Number);
          
          const lunchStart = new Date(currentDate);
          lunchStart.setHours(lunchStartHour, lunchStartMinute, 0, 0);
          
          const lunchEnd = new Date(currentDate);
          lunchEnd.setHours(lunchEndHour, lunchEndMinute, 0, 0);
          
          inLunchBreak = currentSlot < lunchEnd && slotEnd > lunchStart;
        }
        
        if (!hasConflict && !inLunchBreak) {
          const slotId = `${doctorId}-${currentSlot.getTime()}`;
          
          // Formatear para voz - más natural para VAPI
          const dayName = currentSlot.toLocaleDateString('es-GT', { weekday: 'long' });
          const date = currentSlot.getDate();
          const month = currentSlot.toLocaleDateString('es-GT', { month: 'long' });
          const time = currentSlot.toLocaleTimeString('es-GT', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
          });
          
          // Formato optimizado para voz
          const spokenSlot = `${dayName} ${date} de ${month} a las ${time}`;
          
          availableSlots.push({
            id: slotId,
            spoken: spokenSlot,
            date: currentSlot.toISOString().split('T')[0],
            time: currentSlot.toISOString()
          });
        }
        
        currentSlot = new Date(currentSlot.getTime() + (appointmentDuration * 60 * 1000));
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        doctorName: `${doctorInfo.firstName} ${doctorInfo.lastName}`,
        slots: availableSlots,
        message: availableSlots.length > 0 
          ? `Encontré ${availableSlots.length} horarios disponibles`
          : 'No hay horarios disponibles en las próximas semanas'
      }
    });

  } catch (error) {
    console.error('Error en consultar-disponibilidad:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}