import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { relationships } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener parentesco por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const relationship = await db
      .select()
      .from(relationships)
      .where(eq(relationships.id, parseInt(id)))
      .limit(1);

    if (relationship.length === 0) {
      return NextResponse.json({ error: 'Parentesco no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: relationship[0] 
    });

  } catch (error) {
    console.error('Error fetching relationship:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar parentesco
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar parentescos.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, category, isActive } = body;

    // Validar datos requeridos
    if (!name) {
      return NextResponse.json({ 
        error: 'El nombre es requerido' 
      }, { status: 400 });
    }

    // Verificar que el parentesco existe
    const existingRelationship = await db
      .select()
      .from(relationships)
      .where(eq(relationships.id, parseInt(id)))
      .limit(1);

    if (existingRelationship.length === 0) {
      return NextResponse.json({ error: 'Parentesco no encontrado' }, { status: 404 });
    }

    // Actualizar parentesco
    const [updatedRelationship] = await db
      .update(relationships)
      .set({
        name,
        category: category || null,
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(relationships.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedRelationship,
      message: 'Parentesco actualizado exitosamente'
    });

  } catch (error) {
    console.error('Error updating relationship:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar parentesco
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar parentescos.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que el parentesco existe
    const existingRelationship = await db
      .select()
      .from(relationships)
      .where(eq(relationships.id, parseInt(id)))
      .limit(1);

    if (existingRelationship.length === 0) {
      return NextResponse.json({ error: 'Parentesco no encontrado' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(relationships)
        .where(eq(relationships.id, parseInt(id)));

      return NextResponse.json({ 
        success: true, 
        message: 'Parentesco eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedRelationship] = await db
        .update(relationships)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(relationships.id, parseInt(id)))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedRelationship,
        message: 'Parentesco desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting relationship:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}