'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { 
  ArrowLeft,
  Save,
  X,
  Upload,
  Image
} from 'lucide-react';
import { toast } from 'sonner';
import { FileUpload } from '@/components/ui/file-upload';

interface Country {
  id: number;
  name: string;
  code: string;
  phoneCode: string;
}

interface Department {
  id: number;
  name: string;
  countryId: number;
}

interface Municipality {
  id: number;
  name: string;
  departmentId: number;
}

export default function CreateConsultoryPage() {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [touched, setTouched] = useState<Set<string>>(new Set());

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    code: '',
    type: '',
    specialty: '',
    capacity: '1',
    floor: '1',
    building: '',
    address: '',
    phone: '',
    email: '',
    logoUrl: '',
    logoPublicId: '',
    countryId: '',
    departmentId: '',
    municipalityId: '',
    isEmergencyCapable: false,
    hasAirConditioning: true,
    hasWaitingRoom: true,
    isActive: true
  });

  // Form errors
  const [formErrors, setFormErrors] = useState({
    name: '',
    code: '',
    type: '',
    specialty: '',
    capacity: '',
    floor: ''
  });

  // Geographic data
  const [countries, setCountries] = useState<Country[]>([]);
  const [departments, setDepartments] = useState<Department[]>([]);
  const [municipalities, setMunicipalities] = useState<Municipality[]>([]);

  useEffect(() => {
    fetchCountries();
  }, []);

  const fetchCountries = async () => {
    try {
      const response = await fetch('/api/catalogs/countries');
      if (response.ok) {
        const data = await response.json();
        setCountries(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching countries:', error);
    }
  };

  const fetchDepartments = async (countryId: string) => {
    if (!countryId) {
      setDepartments([]);
      setMunicipalities([]);
      return;
    }
    
    try {
      const response = await fetch(`/api/catalogs/departments?countryId=${countryId}`);
      if (response.ok) {
        const data = await response.json();
        setDepartments(data.data || []);
        setMunicipalities([]);
      }
    } catch (error) {
      console.error('Error fetching departments:', error);
    }
  };

  const fetchMunicipalities = async (departmentId: string) => {
    if (!departmentId) {
      setMunicipalities([]);
      return;
    }
    
    try {
      const response = await fetch(`/api/catalogs/municipalities?departmentId=${departmentId}`);
      if (response.ok) {
        const data = await response.json();
        setMunicipalities(data.data || []);
      }
    } catch (error) {
      console.error('Error fetching municipalities:', error);
    }
  };


  const updateFormData = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setTouched(prev => new Set(prev).add(field));
    validateField(field, value);
  };

  const validateField = (field: string, value: any) => {
    const newErrors = { ...formErrors };
    
    switch (field) {
      case 'name':
        newErrors.name = !value?.trim() ? 'El nombre es requerido' : '';
        break;
      case 'code':
        newErrors.code = !value?.trim() ? 'El código es requerido' : '';
        break;
      case 'type':
        newErrors.type = !value ? 'El tipo es requerido' : '';
        break;
      case 'specialty':
        newErrors.specialty = !value ? 'La especialidad es requerida' : '';
        break;
      case 'capacity':
        const capacity = parseInt(value);
        newErrors.capacity = (!capacity || capacity < 1) ? 'La capacidad debe ser mayor a 0' : '';
        break;
      case 'floor':
        const floor = parseInt(value);
        newErrors.floor = (!floor || floor < 1) ? 'El piso debe ser mayor a 0' : '';
        break;
    }
    
    setFormErrors(newErrors);
  };

  const validateForm = () => {
    const errors = {
      name: '',
      code: '',
      type: '',
      specialty: '',
      capacity: '',
      floor: ''
    };

    if (!formData.name.trim()) {
      errors.name = 'El nombre es requerido';
    }

    if (!formData.code.trim()) {
      errors.code = 'El código es requerido';
    }

    if (!formData.type) {
      errors.type = 'El tipo es requerido';
    }

    if (!formData.specialty) {
      errors.specialty = 'La especialidad es requerida';
    }

    const capacity = parseInt(formData.capacity);
    if (!capacity || capacity < 1) {
      errors.capacity = 'La capacidad debe ser mayor a 0';
    }

    const floor = parseInt(formData.floor);
    if (!floor || floor < 1) {
      errors.floor = 'El piso debe ser mayor a 0';
    }

    setFormErrors(errors);
    return Object.values(errors).every(error => error === '');
  };



  const handleSubmit = async () => {
    if (!validateForm()) {
      setActiveTab('basic'); // Ir a la primera sección donde están los errores
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/catalogs/consultories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ...formData,
          capacity: parseInt(formData.capacity),
          floor: parseInt(formData.floor),
          countryId: formData.countryId ? parseInt(formData.countryId) : null,
          departmentId: formData.departmentId ? parseInt(formData.departmentId) : null,
          municipalityId: formData.municipalityId ? parseInt(formData.municipalityId) : null,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        toast.error(result.error || 'Error al crear consultorio');
        return;
      }

      toast.success('Consultorio creado exitosamente');
      router.push('/dashboard/admin/catalogs/consultories');
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al crear consultorio');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.back()}
            className="hover:bg-gray-100"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Crear Consultorio</h1>
            <p className="text-gray-600">Completa la información para crear un nuevo consultorio</p>
          </div>
        </div>
        
        <div className="flex space-x-3">
          <Button
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            <X className="h-4 w-4 mr-2" />
            Cancelar
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={isSubmitting}
          >
            <Save className="h-4 w-4 mr-2" />
            {isSubmitting ? 'Guardando...' : 'Crear Consultorio'}
          </Button>
        </div>
      </div>

      {/* Custom Tabs */}
      <div className="space-y-4">
        {/* Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('basic')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'basic'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Información Básica</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('location')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'location'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Ubicación</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'contact'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Contacto</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('config')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'config'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Configuración</div>
              </div>
            </button>
          </div>
        </div>

        {/* Tab Content */}
        <Card>
          <CardContent className="p-6">

            {/* Tab Content */}
            {activeTab === 'basic' && (
              <div className="space-y-6">
              {/* Información Básica */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Información Básica</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name" className={formErrors.name ? "text-red-600" : ""}>
                      Nombre <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => updateFormData('name', e.target.value)}
                      placeholder="ej. Consultorio Pediatría Principal"
                      className={formErrors.name ? "border-red-500" : ""}
                    />
                    {formErrors.name && <p className="text-sm text-red-600">{formErrors.name}</p>}
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="code" className={formErrors.code ? "text-red-600" : ""}>
                      Código <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="code"
                      value={formData.code}
                      onChange={(e) => updateFormData('code', e.target.value.toUpperCase())}
                      placeholder="ej. PED-001"
                      className={formErrors.code ? "border-red-500" : ""}
                    />
                    {formErrors.code && <p className="text-sm text-red-600">{formErrors.code}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="type" className={formErrors.type ? "text-red-600" : ""}>
                      Tipo <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.type} onValueChange={(value) => updateFormData('type', value)}>
                      <SelectTrigger className={formErrors.type ? "border-red-500" : ""}>
                        <SelectValue placeholder="Seleccionar tipo" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="consultation">Consulta</SelectItem>
                        <SelectItem value="procedure">Procedimientos</SelectItem>
                        <SelectItem value="surgery">Cirugía</SelectItem>
                        <SelectItem value="emergency">Emergencia</SelectItem>
                        <SelectItem value="laboratory">Laboratorio</SelectItem>
                        <SelectItem value="imaging">Imagenología</SelectItem>
                      </SelectContent>
                    </Select>
                    {formErrors.type && <p className="text-sm text-red-600">{formErrors.type}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="specialty" className={formErrors.specialty ? "text-red-600" : ""}>
                      Especialidad <span className="text-red-500">*</span>
                    </Label>
                    <Select value={formData.specialty} onValueChange={(value) => updateFormData('specialty', value)}>
                      <SelectTrigger className={formErrors.specialty ? "border-red-500" : ""}>
                        <SelectValue placeholder="Seleccionar especialidad" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="general">Medicina General</SelectItem>
                        <SelectItem value="pediatrics">Pediatría</SelectItem>
                        <SelectItem value="cardiology">Cardiología</SelectItem>
                        <SelectItem value="dermatology">Dermatología</SelectItem>
                        <SelectItem value="neurology">Neurología</SelectItem>
                        <SelectItem value="gynecology">Ginecología</SelectItem>
                        <SelectItem value="orthopedics">Ortopedia</SelectItem>
                        <SelectItem value="psychiatry">Psiquiatría</SelectItem>
                      </SelectContent>
                    </Select>
                    {formErrors.specialty && <p className="text-sm text-red-600">{formErrors.specialty}</p>}
                  </div>
                </div>
              </div>

              {/* Ubicación Física */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Ubicación Física</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="floor" className={formErrors.floor ? "text-red-600" : ""}>
                      Piso <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="floor"
                      type="number"
                      min="1"
                      value={formData.floor}
                      onChange={(e) => updateFormData('floor', e.target.value)}
                      className={formErrors.floor ? "border-red-500" : ""}
                    />
                    {formErrors.floor && <p className="text-sm text-red-600">{formErrors.floor}</p>}
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="building">Edificio</Label>
                    <Input
                      id="building"
                      value={formData.building}
                      onChange={(e) => updateFormData('building', e.target.value)}
                      placeholder="ej. Torre A"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="capacity" className={formErrors.capacity ? "text-red-600" : ""}>
                      Capacidad <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="capacity"
                      type="number"
                      min="1"
                      value={formData.capacity}
                      onChange={(e) => updateFormData('capacity', e.target.value)}
                      className={formErrors.capacity ? "border-red-500" : ""}
                    />
                    {formErrors.capacity && <p className="text-sm text-red-600">{formErrors.capacity}</p>}
                  </div>
                </div>

                {/* Ubicación Geográfica */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="country">País</Label>
                    <Select 
                      value={formData.countryId} 
                      onValueChange={(value) => {
                        updateFormData('countryId', value);
                        updateFormData('departmentId', '');
                        updateFormData('municipalityId', '');
                        fetchDepartments(value);
                      }}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar país" />
                      </SelectTrigger>
                      <SelectContent>
                        {countries.map((country) => (
                          <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="department">Departamento</Label>
                    <Select 
                      value={formData.departmentId}
                      onValueChange={(value) => {
                        updateFormData('departmentId', value);
                        updateFormData('municipalityId', '');
                        fetchMunicipalities(value);
                      }}
                      disabled={!formData.countryId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar departamento" />
                      </SelectTrigger>
                      <SelectContent>
                        {departments.map((department) => (
                          <SelectItem key={department.id} value={department.id.toString()}>
                            {department.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="municipality">Municipio</Label>
                    <Select 
                      value={formData.municipalityId}
                      onValueChange={(value) => updateFormData('municipalityId', value)}
                      disabled={!formData.departmentId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Seleccionar municipio" />
                      </SelectTrigger>
                      <SelectContent>
                        {municipalities.map((municipality) => (
                          <SelectItem key={municipality.id} value={municipality.id.toString()}>
                            {municipality.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* Información de Contacto */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Información de Contacto</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="address">Dirección</Label>
                    <Textarea
                      id="address"
                      value={formData.address}
                      onChange={(e) => updateFormData('address', e.target.value)}
                      placeholder="Dirección completa del consultorio"
                      rows={3}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="phone">Teléfono</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => updateFormData('phone', e.target.value)}
                        placeholder="ej. +502 1234-5678"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="email">Email</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => updateFormData('email', e.target.value)}
                        placeholder="ej. <EMAIL>"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'location' && (
              <div className="space-y-6">
                {/* Ubicación Física */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Ubicación Física</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="floor" className={formErrors.floor ? "text-red-600" : ""}>
                        Piso <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="floor"
                        type="number"
                        min="1"
                        value={formData.floor}
                        onChange={(e) => updateFormData('floor', e.target.value)}
                        className={formErrors.floor ? "border-red-500" : ""}
                      />
                      {formErrors.floor && <p className="text-sm text-red-600">{formErrors.floor}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="building">Edificio</Label>
                      <Input
                        id="building"
                        value={formData.building}
                        onChange={(e) => updateFormData('building', e.target.value)}
                        placeholder="ej. Torre A"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="capacity" className={formErrors.capacity ? "text-red-600" : ""}>
                        Capacidad <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="capacity"
                        type="number"
                        min="1"
                        value={formData.capacity}
                        onChange={(e) => updateFormData('capacity', e.target.value)}
                        className={formErrors.capacity ? "border-red-500" : ""}
                      />
                      {formErrors.capacity && <p className="text-sm text-red-600">{formErrors.capacity}</p>}
                    </div>
                  </div>

                  {/* Ubicación Geográfica */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="country">País</Label>
                      <Select 
                        value={formData.countryId} 
                        onValueChange={(value) => {
                          updateFormData('countryId', value);
                          updateFormData('departmentId', '');
                          updateFormData('municipalityId', '');
                          fetchDepartments(value);
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar país" />
                        </SelectTrigger>
                        <SelectContent>
                          {countries.map((country) => (
                            <SelectItem key={country.id} value={country.id.toString()}>
                              {country.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="department">Departamento</Label>
                      <Select 
                        value={formData.departmentId}
                        onValueChange={(value) => {
                          updateFormData('departmentId', value);
                          updateFormData('municipalityId', '');
                          fetchMunicipalities(value);
                        }}
                        disabled={!formData.countryId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar departamento" />
                        </SelectTrigger>
                        <SelectContent>
                          {departments.map((department) => (
                            <SelectItem key={department.id} value={department.id.toString()}>
                              {department.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="municipality">Municipio</Label>
                      <Select 
                        value={formData.municipalityId}
                        onValueChange={(value) => updateFormData('municipalityId', value)}
                        disabled={!formData.departmentId}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Seleccionar municipio" />
                        </SelectTrigger>
                        <SelectContent>
                          {municipalities.map((municipality) => (
                            <SelectItem key={municipality.id} value={municipality.id.toString()}>
                              {municipality.name}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'contact' && (
              <div className="space-y-6">
                {/* Información de Contacto */}
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900">Información de Contacto</h3>
                  <div className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor="address">Dirección</Label>
                      <Textarea
                        id="address"
                        value={formData.address}
                        onChange={(e) => updateFormData('address', e.target.value)}
                        placeholder="Dirección completa del consultorio"
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="phone">Teléfono</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => updateFormData('phone', e.target.value)}
                          placeholder="ej. +502 1234-5678"
                        />
                      </div>

                      <div className="space-y-2">
                        <Label htmlFor="email">Email</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => updateFormData('email', e.target.value)}
                          placeholder="ej. <EMAIL>"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {activeTab === 'config' && (
              <div className="space-y-6">
              {/* Configuración */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Configuración</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="emergency"
                      checked={formData.isEmergencyCapable}
                      onCheckedChange={(checked) => updateFormData('isEmergencyCapable', !!checked)}
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                    <Label htmlFor="emergency" className="text-sm font-medium">
                      Capacidad para emergencias
                    </Label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="aircon"
                      checked={formData.hasAirConditioning}
                      onCheckedChange={(checked) => updateFormData('hasAirConditioning', !!checked)}
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                    <Label htmlFor="aircon" className="text-sm font-medium">
                      Aire acondicionado
                    </Label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="waiting"
                      checked={formData.hasWaitingRoom}
                      onCheckedChange={(checked) => updateFormData('hasWaitingRoom', !!checked)}
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                    <Label htmlFor="waiting" className="text-sm font-medium">
                      Sala de espera
                    </Label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <Checkbox
                      id="active"
                      checked={formData.isActive}
                      onCheckedChange={(checked) => updateFormData('isActive', !!checked)}
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    />
                    <Label htmlFor="active" className="text-sm font-medium">
                      Activo
                    </Label>
                  </div>
                </div>
              </div>

              {/* Branding */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Branding</h3>
                <div className="space-y-4">
                  <FileUpload
                    preset="consultorio_logo"
                    onUploadComplete={(url, fileInfo) => {
                      updateFormData('logoUrl', url);
                      updateFormData('logoPublicId', fileInfo?.public_id || '');
                    }}
                    onUploadError={(error) => {
                      toast.error(`Error subiendo logo: ${error}`);
                    }}
                    currentFile={formData.logoUrl}
                    accept="image/*"
                    maxSize={5}
                    label="Logo del Consultorio"
                    className="space-y-2"
                  />
                </div>
              </div>
              </div>
            )}
          </CardContent>
      </Card>
    </div>
  );
}