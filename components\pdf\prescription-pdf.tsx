import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

// Estilos para el PDF
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 40,
    fontFamily: 'Helvetica',
  },
  header: {
    flexDirection: 'row',
    marginBottom: 30,
    borderBottom: '2pt solid #2563EB',
    paddingBottom: 20,
  },
  logo: {
    width: 80,
    height: 80,
    marginRight: 20,
  },
  consultoryInfo: {
    flex: 1,
    justifyContent: 'center',
  },
  consultoryName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 4,
  },
  consultoryDetails: {
    fontSize: 10,
    color: '#6B7280',
    marginBottom: 2,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    color: '#2563EB',
    marginBottom: 30,
    textTransform: 'uppercase',
    letterSpacing: 2,
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#374151',
    marginBottom: 10,
    borderBottom: '1pt solid #E5E7EB',
    paddingBottom: 5,
  },
  patientInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 20,
    backgroundColor: '#F9FAFB',
    padding: 15,
    borderRadius: 8,
  },
  patientDetail: {
    fontSize: 11,
    marginBottom: 3,
    color: '#374151',
  },
  patientLabel: {
    fontWeight: 'bold',
    color: '#1F2937',
  },
  prescriptionItem: {
    marginBottom: 15,
    padding: 12,
    border: '1pt solid #D1D5DB',
    borderRadius: 6,
    backgroundColor: '#FEFEFE',
  },
  medicationName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 6,
  },
  prescriptionDetail: {
    fontSize: 10,
    color: '#4B5563',
    marginBottom: 3,
  },
  prescriptionLabel: {
    fontWeight: 'bold',
    color: '#374151',
  },
  doctorSection: {
    marginTop: 40,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  doctorInfo: {
    width: '48%',
  },
  signatureArea: {
    width: '48%',
    textAlign: 'center',
  },
  doctorName: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#1F2937',
    marginBottom: 3,
  },
  doctorDetail: {
    fontSize: 10,
    color: '#6B7280',
    marginBottom: 2,
  },
  signatureLine: {
    borderTop: '1pt solid #9CA3AF',
    marginTop: 60,
    paddingTop: 8,
    fontSize: 10,
    color: '#6B7280',
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 40,
    right: 40,
    textAlign: 'center',
    fontSize: 8,
    color: '#9CA3AF',
    borderTop: '1pt solid #E5E7EB',
    paddingTop: 10,
  },
  date: {
    textAlign: 'right',
    fontSize: 10,
    color: '#6B7280',
    marginBottom: 20,
  },
});

interface PrescriptionData {
  medication: string;
  medicationName?: string; // Nombre del medicamento
  medicationDetails?: string; // Detalles adicionales (forma farmacéutica, concentración)
  dose: string;
  frequency: string;
  duration: string;
  route: string;
  instructions: string;
}

interface PatientData {
  name: string;
  documentNumber?: string;
  age?: number;
  phone?: string;
}

interface DoctorData {
  name: string;
  medicalLicense?: string;
  specialty?: string;
  phone?: string;
  email?: string;
}

interface ConsultoryData {
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  logoUrl?: string;
}

interface PrescriptionPDFProps {
  prescriptions: PrescriptionData[];
  patient: PatientData;
  doctor: DoctorData;
  consultory: ConsultoryData;
  consultationDate: Date;
  diagnoses?: (string | { code?: string; type?: string; description?: string })[]; // Diagnósticos pueden ser strings u objetos
}

export const PrescriptionPDF = ({ 
  prescriptions, 
  patient, 
  doctor, 
  consultory, 
  consultationDate,
  diagnoses = []
}: PrescriptionPDFProps) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header con logo y datos del consultorio */}
      <View style={styles.header}>
        {consultory.logoUrl && (
          <Image style={styles.logo} src={consultory.logoUrl} />
        )}
        <View style={styles.consultoryInfo}>
          <Text style={styles.consultoryName}>{consultory.name || 'Consultorio Médico'}</Text>
          {consultory.address && (
            <Text style={styles.consultoryDetails}>Direccion: {String(consultory.address)}</Text>
          )}
          {consultory.phone && (
            <Text style={styles.consultoryDetails}>Telefono: {String(consultory.phone)}</Text>
          )}
          {consultory.email && (
            <Text style={styles.consultoryDetails}>Email: {String(consultory.email)}</Text>
          )}
        </View>
      </View>

      {/* Fecha */}
      <Text style={styles.date}>
        {format(consultationDate, "dd 'de' MMMM 'de' yyyy", { locale: es })}
      </Text>

      {/* Título */}
      <Text style={styles.title}>Prescripción Médica</Text>

      {/* Información del paciente */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Datos del Paciente</Text>
        <View style={styles.patientInfo}>
          <View>
            <Text style={styles.patientDetail}>
              <Text style={styles.patientLabel}>Nombre: </Text>
              {patient.name || 'Paciente'}
            </Text>
            {patient.documentNumber && (
              <Text style={styles.patientDetail}>
                <Text style={styles.patientLabel}>Documento: </Text>
                {String(patient.documentNumber)}
              </Text>
            )}
          </View>
          <View>
            {patient.age && (
              <Text style={styles.patientDetail}>
                <Text style={styles.patientLabel}>Edad: </Text>
                {String(patient.age)} años
              </Text>
            )}
            {patient.phone && (
              <Text style={styles.patientDetail}>
                <Text style={styles.patientLabel}>Teléfono: </Text>
                {String(patient.phone)}
              </Text>
            )}
          </View>
        </View>
      </View>

      {/* Diagnósticos */}
      {diagnoses && diagnoses.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Diagnósticos</Text>
          {diagnoses.map((diagnosis, index) => {
            // Manejar tanto strings como objetos
            const diagnosisText = typeof diagnosis === 'string' 
              ? diagnosis 
              : diagnosis?.description || diagnosis?.code || 'Diagnóstico no especificado';
            
            return (
              <Text key={index} style={styles.prescriptionDetail}>
                {index + 1}. {diagnosisText}
              </Text>
            );
          })}
        </View>
      )}

      {/* Prescripciones */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Medicamentos Prescritos</Text>
        {prescriptions.map((prescription, index) => {
          // Asegurar que el nombre del medicamento sea string
          const medicationName = typeof prescription.medication === 'string' 
            ? prescription.medication 
            : prescription.medication?.name || prescription.medication?.description || 'Medicamento no especificado';
          
          return (
            <View key={index} style={styles.prescriptionItem}>
              <Text style={styles.medicationName}>
                {index + 1}. {prescription.medicationName || medicationName}
              </Text>
              {prescription.medicationDetails && (
                <Text style={styles.prescriptionDetail}>
                  <Text style={styles.prescriptionLabel}>Presentación: </Text>
                  {prescription.medicationDetails}
                </Text>
              )}
            
            <Text style={styles.prescriptionDetail}>
              <Text style={styles.prescriptionLabel}>Dosis: </Text>
              {prescription.dose || 'No especificada'}
            </Text>
            
            <Text style={styles.prescriptionDetail}>
              <Text style={styles.prescriptionLabel}>Frecuencia: </Text>
              {prescription.frequency || 'No especificada'}
            </Text>
            
            <Text style={styles.prescriptionDetail}>
              <Text style={styles.prescriptionLabel}>Duración: </Text>
              {prescription.duration || 'No especificada'}
            </Text>
            
            <Text style={styles.prescriptionDetail}>
              <Text style={styles.prescriptionLabel}>Vía: </Text>
              {prescription.route || 'Oral'}
            </Text>
            
            {prescription.instructions && typeof prescription.instructions === 'string' && (
              <Text style={styles.prescriptionDetail}>
                <Text style={styles.prescriptionLabel}>Instrucciones: </Text>
                {prescription.instructions}
              </Text>
            )}
          </View>
        );
        })}
      </View>

      {/* Información del doctor y área de firma */}
      <View style={styles.doctorSection}>
        <View style={styles.doctorInfo}>
          <Text style={styles.sectionTitle}>Médico Tratante</Text>
          <Text style={styles.doctorName}>{doctor.name || 'Doctor'}</Text>
          {doctor.medicalLicense && (
            <Text style={styles.doctorDetail}>
              Licencia Médica: {String(doctor.medicalLicense)}
            </Text>
          )}
          {doctor.specialty && (
            <Text style={styles.doctorDetail}>
              Especialidad: {String(doctor.specialty)}
            </Text>
          )}
          {doctor.phone && (
            <Text style={styles.doctorDetail}>
              Teléfono: {String(doctor.phone)}
            </Text>
          )}
          {doctor.email && (
            <Text style={styles.doctorDetail}>
              Email: {String(doctor.email)}
            </Text>
          )}
        </View>
        
        <View style={styles.signatureArea}>
          <View style={styles.signatureLine}>
            <Text>Firma del Médico</Text>
          </View>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text>
          Este documento es una prescripción médica oficial. 
          Para cualquier consulta, contacte al consultorio médico.
        </Text>
      </View>
    </Page>
  </Document>
);