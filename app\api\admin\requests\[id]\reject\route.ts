import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  registrationRequests, 
  user, 
  userRoles, 
  notifications 
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { randomBytes } from 'crypto';

function generateId(): string {
  return randomBytes(16).toString('hex');
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar que el usuario es admin
    const adminRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'admin'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (adminRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Acceso denegado. Solo administradores.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { reason, notes } = body;

    if (!reason) {
      return NextResponse.json(
        { success: false, message: 'Se requiere una razón para el rechazo' },
        { status: 400 }
      );
    }

    // Obtener la solicitud
    const requestResult = await db
      .select()
      .from(registrationRequests)
      .where(eq(registrationRequests.id, params.id))
      .limit(1);

    if (requestResult.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    const registrationRequest = requestResult[0];

    if (registrationRequest.status !== 'pending' && registrationRequest.status !== 'reviewing') {
      return NextResponse.json(
        { success: false, message: 'Esta solicitud ya ha sido procesada' },
        { status: 400 }
      );
    }

    // Iniciar transacción para rechazar
    await db.transaction(async (tx) => {
      // 1. Actualizar estado de la solicitud
      await tx
        .update(registrationRequests)
        .set({
          status: 'rejected',
          reviewedBy: userId,
          reviewedAt: new Date(),
          reviewNotes: notes,
          rejectionReason: reason,
          updatedAt: new Date()
        })
        .where(eq(registrationRequests.id, params.id));

      // 2. Actualizar estado del rol
      await tx
        .update(userRoles)
        .set({
          status: 'rejected',
          rejectedBy: userId,
          rejectedAt: new Date(),
          rejectionReason: reason,
          updatedAt: new Date()
        })
        .where(
          and(
            eq(userRoles.userId, registrationRequest.userId),
            eq(userRoles.role, registrationRequest.role)
          )
        );

      // 3. Crear notificación para el usuario
      await tx.insert(notifications).values({
        id: generateId(),
        userId: registrationRequest.userId,
        type: 'rejection',
        title: 'Solicitud Rechazada',
        message: `Tu solicitud como ${registrationRequest.role} ha sido rechazada. Razón: ${reason}`,
        read: false,
        data: { 
          requestId: params.id, 
          role: registrationRequest.role,
          rejectedBy: userId,
          reason: reason,
          notes: notes
        },
        createdAt: new Date()
      });
    });

    // 4. Actualizar metadata de Clerk
    const clerk = await clerkClient();
    await clerk.users.updateUserMetadata(registrationRequest.userId, {
      publicMetadata: {
        onboardingCompleted: true,
        role: registrationRequest.role,
        status: 'rejected'
      }
    });

    // TODO: Enviar email de notificación al usuario

    return NextResponse.json({
      success: true,
      message: 'Solicitud rechazada exitosamente'
    });

  } catch (error) {
    console.error('Error rejecting registration request:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}