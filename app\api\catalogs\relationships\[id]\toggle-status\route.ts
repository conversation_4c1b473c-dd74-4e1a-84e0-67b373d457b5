import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { relationships } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Activar/Desactivar parentesco
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de parentescos.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el parentesco existe
    const existingRelationship = await db
      .select()
      .from(relationships)
      .where(eq(relationships.id, parseInt(id)))
      .limit(1);

    if (existingRelationship.length === 0) {
      return NextResponse.json({ error: 'Parentesco no encontrado' }, { status: 404 });
    }

    // Cambiar el estado
    const newStatus = !existingRelationship[0].isActive;
    const [updatedRelationship] = await db
      .update(relationships)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(relationships.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedRelationship,
      message: `Parentesco ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling relationship status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}