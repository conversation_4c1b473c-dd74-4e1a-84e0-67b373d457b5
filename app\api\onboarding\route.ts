import { NextRequest, NextResponse } from 'next/server';
import { currentUser, clerkClient } from '@clerk/nextjs/server';

export async function POST(req: NextRequest) {
  try {
    // Usar currentUser() que es más confiable en API routes
    const user = await currentUser();
    
    if (!user || !user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { role } = body;
    
    if (!role) {
      return NextResponse.json({ error: 'Role is required' }, { status: 400 });
    }

    // Validar que el rol es válido
    const validRoles = ['doctor', 'assistant', 'patient', 'guardian', 'provider'];
    if (!validRoles.includes(role)) {
      return NextResponse.json({ error: 'Invalid role' }, { status: 400 });
    }
    
    // Actualizar metadata usando clerkClient - usar tanto publicMetadata como unsafeMetadata
    const clerk = await clerkClient();
    const updatedUser = await clerk.users.updateUserMetadata(user.id, {
      publicMetadata: {
        role,
        onboardingCompleted: true,
        timestamp: new Date().toISOString()
      },
      unsafeMetadata: {
        role,
        onboardingCompleted: true,
        timestamp: new Date().toISOString()
      }
    });

    return NextResponse.json({ 
      success: true, 
      role,
      userId: user.id,
      redirectUrl: `/dashboard/${role}`,
      metadata: {
        public: updatedUser.publicMetadata,
        unsafe: updatedUser.unsafeMetadata
      }
    });

  } catch (error) {
    return NextResponse.json(
      { 
        error: 'Failed to update user metadata',
        details: error instanceof Error ? error.message : 'Unknown error'
      }, 
      { status: 500 }
    );
  }
}