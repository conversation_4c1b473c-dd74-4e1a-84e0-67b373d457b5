import { db } from './drizzle';
// Importar desde el esquema real de la BD
import { 
  consultories, 
  countries, 
  departments, 
  municipalities, 
  relationships, 
  occupations, 
  medicalSpecialties
} from './migrations/schema';

async function seedCountries() {
  console.log('🌍 Iniciando seed de países...');
  
  const countriesData = [
    {
      name: 'Guatemala',
      code: 'GT',
      phoneCode: '+502',
      active: true
    },
    {
      name: 'México',
      code: 'MX',
      phoneCode: '+52',
      active: true
    },
    {
      name: 'Estados Unidos',
      code: 'US',
      phoneCode: '+1',
      active: true
    },
    {
      name: 'El Salvador',
      code: 'SV',
      phoneCode: '+503',
      active: true
    },
    {
      name: 'Honduras',
      code: 'HN',
      phoneCode: '+504',
      active: true
    }
  ];

  try {
    for (const country of countriesData) {
      await db.insert(countries).values(country).onConflictDoNothing();
      console.log(`✅ País creado/verificado: ${country.name} (${country.phoneCode})`);
    }
  } catch (error) {
    console.error('Error en seed de países:', error);
  }
}

async function seedDepartments() {
  console.log('🏛️ Iniciando seed de departamentos...');
  
  const departmentsData = [
    {
      name: 'Guatemala',
      code: 'GT01',
      countryId: 1,
      active: true
    },
    {
      name: 'Sacatepéquez',
      code: 'GT03',
      countryId: 1,
      active: true
    },
    {
      name: 'Escuintla',
      code: 'GT05',
      countryId: 1,
      active: true
    },
    {
      name: 'Quetzaltenango',
      code: 'GT09',
      countryId: 1,
      active: true
    },
    {
      name: 'Chimaltenango',
      code: 'GT04',
      countryId: 1,
      active: true
    }
  ];

  try {
    for (const department of departmentsData) {
      await db.insert(departments).values(department).onConflictDoNothing();
      console.log(`✅ Departamento creado/verificado: ${department.name}`);
    }
  } catch (error) {
    console.error('Error en seed de departamentos:', error);
  }
}

async function seedMunicipalities() {
  console.log('🏘️ Iniciando seed de municipios...');
  
  const municipalitiesData = [
    // Guatemala
    {
      name: 'Guatemala',
      code: 'GT0101',
      departmentId: 1,
      active: true
    },
    {
      name: 'Mixco',
      code: 'GT0108',
      departmentId: 1,
      active: true
    },
    {
      name: 'Villa Nueva',
      code: 'GT0109',
      departmentId: 1,
      active: true
    },
    {
      name: 'Villa Canales',
      code: 'GT0116',
      departmentId: 1,
      active: true
    },
    // Sacatepéquez
    {
      name: 'Antigua Guatemala',
      code: 'GT0301',
      departmentId: 2,
      active: true
    },
    // Escuintla
    {
      name: 'Escuintla',
      code: 'GT0501',
      departmentId: 3,
      active: true
    },
    // Quetzaltenango
    {
      name: 'Quetzaltenango',
      code: 'GT0901',
      departmentId: 4,
      active: true
    },
    // Chimaltenango
    {
      name: 'Chimaltenango',
      code: 'GT0401',
      departmentId: 5,
      active: true
    }
  ];

  try {
    for (const municipality of municipalitiesData) {
      await db.insert(municipalities).values(municipality).onConflictDoNothing();
      console.log(`✅ Municipio creado/verificado: ${municipality.name}`);
    }
  } catch (error) {
    console.error('Error en seed de municipios:', error);
  }
}

async function seedRelationships() {
  console.log('👨‍👩‍👧‍👦 Iniciando seed de relaciones...');
  
  const relationshipsData = [
    {
      name: 'Padre',
      code: 'PADRE',
      active: true
    },
    {
      name: 'Madre',
      code: 'MADRE',
      active: true
    },
    {
      name: 'Hermano/a',
      code: 'HERMANO',
      active: true
    },
    {
      name: 'Abuelo/a',
      code: 'ABUELO',
      active: true
    },
    {
      name: 'Tío/a',
      code: 'TIO',
      active: true
    },
    {
      name: 'Tutor Legal',
      code: 'TUTOR_LEGAL',
      active: true
    },
    {
      name: 'Primo/a',
      code: 'PRIMO',
      active: true
    },
    {
      name: 'Otro familiar',
      code: 'OTRO_FAMILIAR',
      active: true
    }
  ];

  try {
    for (const relationship of relationshipsData) {
      await db.insert(relationships).values(relationship).onConflictDoNothing();
      console.log(`✅ Relación creada/verificada: ${relationship.name}`);
    }
  } catch (error) {
    console.error('Error en seed de relaciones:', error);
  }
}

async function seedOccupations() {
  console.log('💼 Iniciando seed de ocupaciones...');
  
  const occupationsData = [
    {
      name: 'Médico',
      code: 'MEDICO',
      active: true
    },
    {
      name: 'Enfermera/o',
      code: 'ENFERMERO',
      active: true
    },
    {
      name: 'Maestro/a',
      code: 'MAESTRO',
      active: true
    },
    {
      name: 'Ingeniero/a',
      code: 'INGENIERO',
      active: true
    },
    {
      name: 'Abogado/a',
      code: 'ABOGADO',
      active: true
    },
    {
      name: 'Comerciante',
      code: 'COMERCIANTE',
      active: true
    },
    {
      name: 'Ama de casa',
      code: 'AMA_CASA',
      active: true
    },
    {
      name: 'Estudiante',
      code: 'ESTUDIANTE',
      active: true
    },
    {
      name: 'Empleado público',
      code: 'EMPLEADO_PUBLICO',
      active: true
    },
    {
      name: 'Otro',
      code: 'OTRO',
      active: true
    }
  ];

  try {
    for (const occupation of occupationsData) {
      await db.insert(occupations).values(occupation).onConflictDoNothing();
      console.log(`✅ Ocupación creada/verificada: ${occupation.name}`);
    }
  } catch (error) {
    console.error('Error en seed de ocupaciones:', error);
  }
}

async function seedMedicalSpecialties() {
  console.log('🩺 Iniciando seed de especialidades médicas...');
  
  const specialtiesData = [
    {
      name: 'Pediatría General',
      code: 'PEDIATRIA_GENERAL',
      active: true
    },
    {
      name: 'Neonatología',
      code: 'NEONATOLOGIA',
      active: true
    },
    {
      name: 'Cardiología Pediátrica',
      code: 'CARDIOLOGIA_PED',
      active: true
    },
    {
      name: 'Neurología Pediátrica',
      code: 'NEUROLOGIA_PED',
      active: true
    },
    {
      name: 'Gastroenterología Pediátrica',
      code: 'GASTRO_PED',
      active: true
    },
    {
      name: 'Neumología Pediátrica',
      code: 'PNEUMO_PED',
      active: true
    },
    {
      name: 'Endocrinología Pediátrica',
      code: 'ENDOCRINO_PED',
      active: true
    },
    {
      name: 'Cirugía Pediátrica',
      code: 'CIRUGIA_PED',
      active: true
    }
  ];

  try {
    for (const specialty of specialtiesData) {
      await db.insert(medicalSpecialties).values(specialty).onConflictDoNothing();
      console.log(`✅ Especialidad creada/verificada: ${specialty.name}`);
    }
  } catch (error) {
    console.error('Error en seed de especialidades médicas:', error);
  }
}

async function seedConsultories() {
  console.log('🏥 Iniciando seed de consultorios...');
  
  const consultoriesData = [
    {
      id: 'cons_001',
      name: 'Clínica Pediátrica San Carlos',
      address: '5a Avenida 15-45, Zona 10, Guatemala',
      phone: '2234-5678',
      email: '<EMAIL>',
      active: true,
      description: 'Clínica especializada en pediatría con más de 20 años de experiencia',
      services: ['Consulta general', 'Emergencias', 'Cirugía menor', 'Vacunación'],
      workingHours: {
        monday: { start: '07:00', end: '18:00', available: true },
        tuesday: { start: '07:00', end: '18:00', available: true },
        wednesday: { start: '07:00', end: '18:00', available: true },
        thursday: { start: '07:00', end: '18:00', available: true },
        friday: { start: '07:00', end: '18:00', available: true },
        saturday: { start: '08:00', end: '14:00', available: true },
        sunday: { start: '08:00', end: '12:00', available: false }
      }
    },
    {
      id: 'cons_002',
      name: 'Centro Médico Mundo Pediatra',
      address: '12 Calle 3-40, Zona 1, Guatemala',
      phone: '2345-6789',
      email: '<EMAIL>',
      active: false, // Solo uno debe estar activo
      description: 'Centro médico principal con especialistas en todas las áreas pediátricas',
      services: ['Consulta general', 'Especialidades', 'Laboratorio', 'Rayos X', 'Farmacia'],
      workingHours: {
        monday: { start: '06:00', end: '20:00', available: true },
        tuesday: { start: '06:00', end: '20:00', available: true },
        wednesday: { start: '06:00', end: '20:00', available: true },
        thursday: { start: '06:00', end: '20:00', available: true },
        friday: { start: '06:00', end: '20:00', available: true },
        saturday: { start: '07:00', end: '18:00', available: true },
        sunday: { start: '08:00', end: '16:00', available: true }
      }
    }
  ];

  try {
    for (const consultory of consultoriesData) {
      await db.insert(consultories).values(consultory).onConflictDoNothing();
      console.log(`✅ Consultorio creado/verificado: ${consultory.name} (${consultory.active ? 'ACTIVO' : 'inactivo'})`);
    }
  } catch (error) {
    console.error('Error en seed de consultorios:', error);
  }
}

async function runAllSeeds() {
  console.log('🌱 Iniciando seed completo de catálogos médicos...');
  
  try {
    await seedCountries();
    await seedDepartments();
    await seedMunicipalities();
    await seedRelationships();
    await seedOccupations();
    await seedMedicalSpecialties();
    await seedConsultories();

    console.log('🎉 Seed completo exitoso!');
    console.log('📊 Catálogos creados/verificados:');
    console.log('   ✅ 5 Países con códigos telefónicos');
    console.log('   ✅ 5 Departamentos de Guatemala');
    console.log('   ✅ 8 Municipios principales');
    console.log('   ✅ 8 Parentescos/relaciones');
    console.log('   ✅ 10 Ocupaciones');
    console.log('   ✅ 8 Especialidades médicas');
    console.log('   ✅ 2 Consultorios (1 activo para onboarding)');
    
  } catch (error) {
    console.error('❌ Error durante el seed:', error);
    throw error;
  }
}

// Ejecutar el seed si el script se ejecuta directamente
if (require.main === module) {
  runAllSeeds()
    .then(() => {
      console.log('✨ Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { runAllSeeds }; 