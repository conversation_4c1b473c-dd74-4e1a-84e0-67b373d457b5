import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Calendar, ArrowRight, Clock } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

const blogPosts = [
  {
    id: 1,
    title: "Guía completa de vacunación infantil 2024",
    excerpt: "Todo lo que necesitas saber sobre el calendario de vacunación actualizado y la importancia de mantener al día las inmunizaciones.",
    category: "Prevención",
    date: "15 Dic 2024",
    readTime: "5 min",
    image: "https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    featured: true
  },
  {
    id: 2,
    title: "Desarrollo motor en los primeros años",
    excerpt: "Hitos importantes del desarrollo motor infantil y cuándo es necesario consultar con un especialista.",
    category: "Desarrollo",
    date: "12 Dic 2024",
    readTime: "7 min",
    image: "https://images.unsplash.com/photo-1544027993-37dbfe43562a?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    featured: false
  },
  {
    id: 3,
    title: "Alimentación complementaria: primeros pasos",
    excerpt: "Consejos prácticos para introducir alimentos sólidos en la dieta del bebé de manera segura y nutritiva.",
    category: "Nutrición",
    date: "10 Dic 2024",
    readTime: "6 min",
    image: "https://images.unsplash.com/photo-1576673442511-7e39b6545c87?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80",
    featured: false
  }
];

export default function BlogSection() {
  return (
    <section className="py-20 bg-white">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-2xl text-center mb-16">
          <h2 className="text-3xl font-bold tracking-tight text-foreground sm:text-4xl">
            Últimas Entradas del Blog
          </h2>
          <p className="mt-4 text-lg text-muted-foreground">
            Artículos especializados sobre pediatría, desarrollo infantil y consejos para padres
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {blogPosts.map((post) => (
            <Card key={post.id} className={`group hover:shadow-xl transition-all duration-300 border-0 overflow-hidden ${post.featured ? 'lg:col-span-2 lg:row-span-1' : ''}`}>
              <div className="relative">
                <div className="aspect-video overflow-hidden">
                  <Image
                    src={post.image}
                    alt={post.title}
                    width={500}
                    height={300}
                    className="h-full w-full object-cover transition-transform duration-300 group-hover:scale-105"
                  />
                </div>
                <div className="absolute top-4 left-4">
                  <Badge variant="secondary" className="bg-white/90 text-primary">
                    {post.category}
                  </Badge>
                </div>
              </div>

              <CardHeader className="pb-2">
                <div className="flex items-center gap-4 text-sm text-muted-foreground mb-3">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {post.date}
                  </div>
                  <div className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {post.readTime}
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-foreground group-hover:text-primary transition-colors duration-300">
                  {post.title}
                </h3>
              </CardHeader>

              <CardContent className="pt-0">
                <p className="text-muted-foreground mb-4 leading-relaxed">
                  {post.excerpt}
                </p>
                <Button variant="ghost" className="p-0 h-auto text-primary hover:text-primary/80" asChild>
                  <Link href={`/blog/${post.id}`} className="inline-flex items-center gap-2">
                    Leer más
                    <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                  </Link>
                </Button>
              </CardContent>
            </Card>
          ))}
        </div>

        <div className="mt-12 text-center">
          <Button variant="outline" size="lg" asChild>
            <Link href="/blog" className="inline-flex items-center gap-2">
              Ver todos los artículos
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
} 