import { useUser } from '@clerk/nextjs';
import { useEffect, useState } from 'react';

export function useUserSync() {
  const { user, isLoaded } = useUser();
  const [isSyncing, setIsSyncing] = useState(false);
  const [isSynced, setIsSynced] = useState(false);

  useEffect(() => {
    const syncUser = async () => {
      if (!isLoaded || !user || isSyncing || isSynced) return;

      // No ejecutar sync para usuarios pending/rejected - el middleware maneja las redirecciones
      const userStatus = user.publicMetadata?.status;
      if (userStatus === 'pending' || userStatus === 'rejected') {
        setIsSynced(true);
        return;
      }

      console.log('🔄 Ejecutando sincronización de usuario...');
      setIsSyncing(true);
      
      try {
        const response = await fetch('/api/auth/sync', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
        });

        if (response.ok) {
          const data = await response.json();
          console.log('✅ Usuario sincronizado:', data.message);
          setIsSynced(true);
          
          // SIMPLE: Si hay redirección y NO estamos ya en esa página, redirigir
          if (data.shouldRedirect && data.redirectTo) {
            const currentPath = window.location.pathname;
            
            // No redirigir desde páginas públicas si el destino es una página de aviso
            const isOnPublicPage = ['/', '/blog', '/about', '/contact', '/pricing', '/privacy-policy', '/terms-of-service'].some(route => 
              currentPath === route || currentPath.startsWith(route + '/')
            );
            const isGoingToNoticePage = data.redirectTo === '/onboarding/pending' || data.redirectTo === '/onboarding/rejected';
            
            // No redirigir desde páginas de pre-checkin - permiten acceso contextual
            const isOnPreCheckinPage = currentPath.startsWith('/pre-checkin/');
            
            if (isOnPublicPage && isGoingToNoticePage) {
              console.log('✅ Usuario en página pública, no redirigiendo a página de aviso');
              return;
            }
            
            if (isOnPreCheckinPage) {
              console.log('✅ Usuario en pre-checkin, permitiendo acceso contextual');
              return;
            }
            
            if (currentPath !== data.redirectTo) {
              console.log('🚀 Redirigiendo de', currentPath, 'a:', data.redirectTo);
              window.location.replace(data.redirectTo);
              return;
            } else {
              console.log('✅ Ya estamos en la página correcta:', currentPath);
              return;
            }
          }

          // Si no hay redirección específica pero hay cambios de metadatos, recargar solo una vez
          if (data.metadataUpdated && !data.shouldRedirect) {
            console.log('🔄 Recargando para aplicar cambios...');
            window.location.reload();
            return;
          }
        } else {
          console.error('❌ Error en respuesta de sincronización:', response.status);
        }
      } catch (error) {
        console.error('❌ Error sincronizando usuario:', error);
      } finally {
        setIsSyncing(false);
      }
    };

    syncUser();
  }, [isLoaded, user, isSynced]);

  return { isSyncing, isSynced };
} 