"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface ClearableSelectProps {
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  children: React.ReactNode;
  className?: string;
  disabled?: boolean;
  clearText?: string;
}

export function ClearableSelect({
  value,
  onValueChange,
  placeholder = "Seleccionar opción",
  clearText = "Limpiar selección",
  children,
  className,
  disabled,
}: ClearableSelectProps) {
  console.log('ClearableSelect render - value:', value, 'type:', typeof value, 'show clear?', value && value !== "");
  
  // Usar un valor especial para representar "vacío" que sea compatible con Radix UI
  const EMPTY_VALUE = "__empty__";
  const controlledValue = value && value !== "" ? value : EMPTY_VALUE;
  
  // Manejar los valores especiales para limpiar la selección
  const handleValueChange = (newValue: string) => {
    console.log('🔄 ClearableSelect handleValueChange called with:', newValue);
    if (newValue === "none" || newValue === EMPTY_VALUE) {
      console.log('🧹 Clearing selection, calling onValueChange("")');
      onValueChange("");
    } else {
      console.log('✅ Setting new value:', newValue);
      onValueChange(newValue);
    }
  };
  
  return (
    <Select value={controlledValue} onValueChange={handleValueChange} disabled={disabled}>
      <SelectTrigger className={className}>
        <SelectValue placeholder={placeholder}>
          {/* Solo mostrar el valor si no es el valor vacío */}
          {controlledValue !== EMPTY_VALUE ? controlledValue : ""}
        </SelectValue>
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={EMPTY_VALUE} className="text-gray-500 italic">
          {clearText}
        </SelectItem>
        {children}
      </SelectContent>
    </Select>
  );
}