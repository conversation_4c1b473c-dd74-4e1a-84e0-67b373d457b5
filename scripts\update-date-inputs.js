// Script para actualizar todos los inputs type="date" a DateInput
const fs = require('fs');
const path = require('path');

// Archivos que contienen type="date"
const filesToUpdate = [
  'app/(dashboard)/dashboard/admin/users/[id]/page.tsx',
  'app/(dashboard)/dashboard/doctor/pacientes/page.tsx',
  'app/(dashboard)/dashboard/doctor/expedientes/[id]/page.tsx',
  'app/(dashboard)/dashboard/admin/users/[id]/edit/page.tsx',
  'app/(dashboard)/dashboard/admin/users/create/page.tsx'
];

function updateFile(filePath) {
  const fullPath = path.join(process.cwd(), filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⏭️  Saltando ${filePath} (no existe)`);
    return false;
  }

  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;

    // Verificar si el archivo tiene type="date"
    if (!content.includes('type="date"')) {
      console.log(`ℹ️  ${filePath} (sin campos de fecha)`);
      return false;
    }

    // Verificar si el archivo ya importa DateInput
    const hasDateInputImport = content.includes('DateInput');
    
    // Reemplazar los inputs type="date"
    // Este regex busca inputs con type="date" y captura sus atributos
    const dateInputRegex = /<Input\s+([^>]*?)type="date"([^>]*?)\/>/g;
    
    let newContent = content.replace(dateInputRegex, (match, before, after) => {
      modified = true;
      
      // Extraer value y onChange si existen
      const valueMatch = (before + after).match(/value={([^}]+)}/);
      const onChangeMatch = (before + after).match(/onChange={([^}]+)}/);
      
      let valueAttr = '';
      let onChangeAttr = '';
      
      if (valueMatch) {
        // Convertir el formato de value para DateInput
        const oldValue = valueMatch[1];
        if (oldValue.includes('.toISOString().split')) {
          // Es una fecha siendo convertida a string
          valueAttr = `value={${oldValue.replace(/\.toISOString\(\)\.split\('\[T\]'\)\[0\]/, '').replace(' ? new Date(', ' ? new Date(').replace(') : \'\'', ') : undefined')}}`;
        } else {
          valueAttr = `value={${oldValue} ? new Date(${oldValue}) : undefined}`;
        }
      }
      
      if (onChangeMatch) {
        // Convertir el formato de onChange para DateInput
        const oldOnChange = onChangeMatch[1];
        if (oldOnChange.includes('e.target.value')) {
          // Extraer el nombre del campo
          const fieldMatch = oldOnChange.match(/\(([^,]+),/);
          if (fieldMatch) {
            onChangeAttr = `onChange={(date) => ${fieldMatch[1]}(date || null)}`;
          }
        }
      }
      
      // Remover value y onChange del resto de atributos
      let otherAttrs = (before + after)
        .replace(/value={[^}]+}/, '')
        .replace(/onChange={[^}]+}/, '')
        .replace(/type="date"/, '')
        .trim();
      
      // Construir el nuevo DateInput
      return `<DateInput ${otherAttrs} ${valueAttr} ${onChangeAttr} maxDate={new Date()} />`;
    });
    
    if (modified) {
      // Agregar import si no existe
      if (!hasDateInputImport) {
        // Buscar dónde insertar el import
        const componentImportRegex = /import\s+{[^}]*}\s+from\s+['"]@\/components\/ui\/[^'"]+['"]/;
        const lastImportRegex = /import[^;]+from[^;]+;(?=\s*\n(?!import))/;
        
        const importStatement = "import { DateInput } from '@/components/ui/date-input';";
        
        if (componentImportRegex.test(newContent)) {
          // Si ya hay imports de componentes UI, agregar después del último
          const matches = newContent.match(/import\s+{[^}]*}\s+from\s+['"]@\/components\/ui\/[^'"]+['"]/g);
          if (matches) {
            const lastUIImport = matches[matches.length - 1];
            newContent = newContent.replace(lastUIImport, `${lastUIImport}\n${importStatement}`);
          }
        } else if (lastImportRegex.test(newContent)) {
          // Si no, agregar después del último import
          const matches = newContent.match(/import[^;]+from[^;]+;/g);
          if (matches) {
            const lastImport = matches[matches.length - 1];
            newContent = newContent.replace(lastImport, `${lastImport}\n${importStatement}`);
          }
        }
      }
      
      fs.writeFileSync(fullPath, newContent);
      console.log(`✅ ${filePath} actualizado`);
      return true;
    } else {
      console.log(`ℹ️  ${filePath} (sin cambios necesarios)`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error en ${filePath}:`, error.message);
    return false;
  }
}

console.log('🔄 Actualizando inputs de fecha a DateInput...\n');

let updatedCount = 0;
for (const file of filesToUpdate) {
  if (updateFile(file)) {
    updatedCount++;
  }
}

console.log(`\n✅ Actualización completada: ${updatedCount}/${filesToUpdate.length} archivos modificados`);

if (updatedCount > 0) {
  console.log('\n📝 Los campos de fecha ahora:');
  console.log('   - Permiten escribir directamente (ej: 02/11/1975)');
  console.log('   - Tienen selector de calendario opcional');
  console.log('   - Validan automáticamente el formato');
  console.log('   - No permiten fechas futuras para fecha de nacimiento');
}