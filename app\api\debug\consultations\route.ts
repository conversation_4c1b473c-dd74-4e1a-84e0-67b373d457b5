import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalConsultations } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    console.log('User ID:', userId);

    // Obtener todas las consultas
    const allConsultations = await db
      .select()
      .from(medicalConsultations);

    console.log('Total consultas en DB:', allConsultations.length);
    console.log('Consultas:', allConsultations);

    // Obtener consultas del doctor actual
    const doctorConsultations = await db
      .select()
      .from(medicalConsultations)
      .where(eq(medicalConsultations.doctorId, userId));

    console.log('Consultas del doctor:', doctorConsultations);

    return NextResponse.json({
      userId,
      totalConsultations: allConsultations.length,
      doctorConsultations: doctorConsultations.length,
      allConsultations,
      doctorConsultations
    });

  } catch (error) {
    console.error('Error in debug endpoint:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}