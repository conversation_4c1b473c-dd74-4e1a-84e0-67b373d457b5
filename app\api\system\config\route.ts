import { NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { systemConfig } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function GET(request: Request) {
  try {
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const key = searchParams.get('key');

    let whereConditions = [eq(systemConfig.active, true)];

    if (category) {
      whereConditions.push(eq(systemConfig.category, category));
    }

    if (key) {
      whereConditions.push(eq(systemConfig.key, key));
    }

    const configs = await db
      .select()
      .from(systemConfig)
      .where(and(...whereConditions));

    // Si se solicita una clave específica, devolver solo ese valor
    if (key && configs.length > 0) {
      return NextResponse.json({
        success: true,
        data: configs[0].value
      });
    }

    // Si se solicita una categoría, devolver objeto indexado por clave
    if (category) {
      const configObject = configs.reduce((acc, config) => {
        acc[config.key] = config.value;
        return acc;
      }, {} as Record<string, any>);

      return NextResponse.json({
        success: true,
        data: configObject
      });
    }

    // Devolver todas las configuraciones organizadas por categoría
    const configsByCategory = configs.reduce((acc, config) => {
      const cat = config.category || 'general';
      if (!acc[cat]) {
        acc[cat] = {};
      }
      acc[cat][config.key] = config.value;
      return acc;
    }, {} as Record<string, Record<string, any>>);

    return NextResponse.json({
      success: true,
      data: configsByCategory
    });

  } catch (error) {
    console.error('Error fetching system config:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to fetch system configuration' 
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: Request) {
  try {
    const { key, value, description } = await request.json();

    if (!key || !value) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Key and value are required' 
        },
        { status: 400 }
      );
    }

    // Actualizar la configuración
    await db
      .update(systemConfig)
      .set({
        value,
        description: description || undefined,
        updatedAt: new Date()
      })
      .where(eq(systemConfig.key, key));

    return NextResponse.json({
      success: true,
      message: 'Configuration updated successfully'
    });

  } catch (error) {
    console.error('Error updating system config:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Failed to update system configuration' 
      },
      { status: 500 }
    );
  }
} 