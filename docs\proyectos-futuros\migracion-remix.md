# Migración de Next.js a Remix

## ¿Por qué considerar Remix?

Remix es un framework full-stack moderno que ofrece varias ventajas sobre Next.js, especialmente para aplicaciones con mucha interactividad y manejo de datos del servidor.

### Ventajas principales:
- **Mejor manejo de formularios**: Formularios nativos sin necesidad de estado cliente
- **Nested routing**: Rutas anidadas más poderosas
- **Mejor manejo de errores**: Error boundaries integrados
- **Progressive enhancement**: Funciona sin JavaScript
- **Menor complejidad**: No hay distinción entre cliente/servidor components

## Stack Actual vs Stack con Remix

```bash
# Stack actual → Stack con Remix
- Next.js 14 → Remix
- React → React (sin cambios)
- TypeScript → TypeScript (sin cambios)
- Tailwind CSS → Tailwind CSS (sin cambios)
- Clerk → Clerk (sin cambios)
- Drizzle ORM → Drizzle ORM (sin cambios)
- PostgreSQL → PostgreSQL (sin cambios)
- Neon Database → Neon Database (sin cambios)
```

## Plan de Migración

### 1. Estructura de Carpetas

```bash
# Next.js
/app
  /(dashboard)
    /dashboard
      /patient
        page.tsx
  /api
    /appointments
      route.ts
/components
/lib
/db

# Remix
/app
  /routes
    dashboard.patient.tsx
    api.appointments.tsx
  /components (sin cambios)
  /lib (sin cambios)
/db (sin cambios)
```

### 2. Cambios en Rutas API

**Next.js (App Router):**
```typescript
// /app/api/appointments/route.ts
export async function GET(request: NextRequest) {
  // lógica GET
}

export async function POST(request: NextRequest) {
  // lógica POST
}
```

**Remix:**
```typescript
// /app/routes/api.appointments.tsx
import type { LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";

// GET
export async function loader({ request }: LoaderFunctionArgs) {
  // lógica GET
}

// POST/PUT/DELETE
export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  // o
  const json = await request.json();
  
  switch (request.method) {
    case "POST":
      // lógica POST
    case "PUT":
      // lógica PUT
    case "DELETE":
      // lógica DELETE
  }
}
```

### 3. Cambios en Páginas

**Next.js:**
```tsx
'use client';

import { useEffect, useState } from 'react';

export default function PatientDashboard() {
  const [data, setData] = useState(null);
  
  useEffect(() => {
    fetch('/api/data')
      .then(res => res.json())
      .then(setData);
  }, []);
  
  return <div>{/* UI */}</div>;
}
```

**Remix:**
```tsx
import type { LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";

export async function loader({ request }: LoaderFunctionArgs) {
  // Datos se cargan en el servidor
  const data = await db.query.user.findMany();
  return json({ data });
}

export default function PatientDashboard() {
  const { data } = useLoaderData<typeof loader>();
  
  return <div>{/* UI */}</div>;
}
```

### 4. Manejo de Formularios

**Next.js:**
```tsx
const handleSubmit = async (e) => {
  e.preventDefault();
  const res = await fetch('/api/appointments', {
    method: 'POST',
    body: JSON.stringify(data)
  });
};
```

**Remix:**
```tsx
import { Form } from "@remix-run/react";

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  // Procesar en el servidor
  return redirect('/success');
}

export default function AppointmentForm() {
  return (
    <Form method="post">
      {/* Formulario nativo, sin useState */}
      <input name="title" />
      <button type="submit">Enviar</button>
    </Form>
  );
}
```

### 5. Autenticación con Clerk

**Next.js:**
```tsx
import { auth } from '@clerk/nextjs/server';

export async function GET() {
  const { userId } = await auth();
}
```

**Remix:**
```tsx
import { getAuth } from "@clerk/remix/ssr.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { userId } = await getAuth(request);
}
```

### 6. Middleware

**Next.js (middleware.ts):**
```typescript
export function middleware(request: NextRequest) {
  // lógica
}
```

**Remix (en root.tsx):**
```typescript
export async function loader({ request }: LoaderFunctionArgs) {
  // Verificaciones globales aquí
}
```

## Estimación de Tiempo

Para nuestro proyecto actual:
- **Preparación**: 1 semana (setup, configuración, pruebas)
- **Migración de componentes**: 1 semana (sin cambios mayores)
- **Migración de rutas**: 2-3 semanas
- **Testing y ajustes**: 1 semana
- **Total estimado**: 4-6 semanas

## Recursos para Aprender Remix

### 📚 Documentación Oficial
- **Docs**: https://remix.run/docs
- **Tutorial**: https://remix.run/docs/en/main/start/tutorial
- **GitHub**: https://github.com/remix-run/remix
- **Blog**: https://remix.run/blog

### 🎥 Videos y Cursos
- **Canal YouTube Oficial**: https://www.youtube.com/@Remix-Run
- **Kent C. Dodds**: https://kentcdodds.com
- **Epic Web Dev**: https://epicreact.dev/epic-web
- **Frontend Masters**: "Remix Fundamentals"
- **Egghead.io**: Cursos de Remix

### 💬 Comunidad
- **Discord**: https://rmx.as/discord
- **Twitter/X**:
  - @remix_run (oficial)
  - @ryanflorence (co-creador)
  - @mjackson (co-creador)
  - @kentcdodds (educator)

## Comandos para Empezar

```bash
# Proyecto básico
npx create-remix@latest

# Con TypeScript
npx create-remix@latest --template remix-run/remix/templates/remix-ts

# Stack completo (similar al nuestro)
npx create-remix@latest --template remix-run/blues-stack
```

## Conceptos Clave a Estudiar

1. **Loaders**: Cargan datos en el servidor (reemplazan getServerSideProps)
2. **Actions**: Manejan mutaciones (POST/PUT/DELETE)
3. **Nested Routes**: Sistema de rutas anidadas
4. **Forms**: Manejo nativo de formularios sin JavaScript
5. **Error Boundaries**: Manejo de errores por ruta
6. **Meta Functions**: SEO y meta tags
7. **Resource Routes**: Para APIs y webhooks
8. **Progressive Enhancement**: Funcionalidad sin JS

## Ejemplo de Migración: Página de Paciente

### Next.js Original:
```tsx
// app/(dashboard)/dashboard/patient/page.tsx
'use client';

export default function PatientDashboard() {
  const [patientData, setPatientData] = useState(null);
  
  useEffect(() => {
    fetch('/api/auth/current-user')
      .then(res => res.json())
      .then(result => setPatientData(result.data));
  }, []);
  
  return <div>{/* UI */}</div>;
}
```

### Remix Migrado:
```tsx
// app/routes/dashboard.patient.tsx
import { json, type LoaderFunctionArgs } from "@remix-run/node";
import { useLoaderData } from "@remix-run/react";
import { getAuth } from "@clerk/remix/ssr.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const { userId } = await getAuth(request);
  
  if (!userId) {
    throw redirect('/login');
  }
  
  const patientData = await db.query.user.findFirst({
    where: eq(user.clerkId, userId)
  });
  
  return json({ patientData });
}

export default function PatientDashboard() {
  const { patientData } = useLoaderData<typeof loader>();
  
  return <div>{/* UI - exactamente igual */}</div>;
}
```

## Consideraciones Finales

### Ventajas de migrar:
- Mejor performance (menos JavaScript al cliente)
- Código más simple y mantenible
- Mejor SEO
- Progressive enhancement gratis
- Menos problemas de hidratación

### Desventajas:
- Tiempo de migración (4-6 semanas)
- Curva de aprendizaje para el equipo
- Menos ecosistema que Next.js
- Cambio de paradigma en algunos patrones

### Recomendación:
Considerar la migración cuando:
1. El proyecto esté estable
2. Haya tiempo dedicado para la migración
3. El equipo esté dispuesto a aprender
4. Se busque mejorar performance y simplicidad

## Próximos Pasos

1. Hacer POC con una página simple
2. Evaluar la experiencia de desarrollo
3. Medir mejoras de performance
4. Decidir si proceder con migración completa