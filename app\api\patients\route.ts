import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, patientInvitations, appointments } from '@/db/schema';
import { eq, ilike, or, and, sql, desc, asc, count, gte, lte } from 'drizzle-orm';
import { generateId } from '@/lib/utils';
import { sendEmail, emailTemplates } from '@/lib/email';
import { formatPhoneForDisplay, formatPhoneForStorage } from '@/lib/phone-utils';
import crypto from 'crypto';
import { startOfDay, endOfDay, startOfMonth } from 'date-fns';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Obtener parámetros de búsqueda
    const searchParams = request.nextUrl.searchParams;
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '25');
    const search = searchParams.get('search') || '';
    const statusFilter = searchParams.get('status') || 'all';
    const genderFilter = searchParams.get('gender') || 'all';
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    // Calcular offset
    const offset = (page - 1) * limit;

    // Construir condiciones WHERE para usuarios que son pacientes
    const conditions = [
      eq(userRoles.role, 'patient'),
      eq(userRoles.status, 'active')
    ];

    // Aplicar filtro de búsqueda
    if (search) {
      conditions.push(
        or(
          ilike(user.firstName, `%${search}%`),
          ilike(user.lastName, `%${search}%`),
          ilike(user.email, `%${search}%`),
          ilike(user.documentNumber, `%${search}%`)
        )!
      );
    }

    // Aplicar filtro de género
    if (genderFilter !== 'all') {
      conditions.push(eq(user.gender, genderFilter));
    }

    // Construir WHERE clause
    const whereClause = and(...conditions);

    // Obtener total de registros
    const totalResult = await db
      .select({ count: count() })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(whereClause);
    
    const totalCount = Number(totalResult[0]?.count || 0);
    const totalPages = Math.ceil(totalCount / limit);

    // Construir ordenamiento dinámico
    let orderByClause;
    switch (sortBy) {
      case 'name':
        orderByClause = sortOrder === 'desc' 
          ? [desc(user.firstName), desc(user.lastName)]
          : [asc(user.firstName), asc(user.lastName)];
        break;
      case 'email':
        orderByClause = sortOrder === 'desc' ? [desc(user.email)] : [asc(user.email)];
        break;
      case 'createdAt':
        orderByClause = sortOrder === 'desc' ? [desc(userRoles.createdAt)] : [asc(userRoles.createdAt)];
        break;
      default:
        orderByClause = [asc(user.firstName), asc(user.lastName)];
    }

    // Obtener pacientes con información de expediente médico
    const patients = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
        image: user.image,
        address: user.address,
        emergencyContact: user.emergencyContact,
        emergencyPhone: user.emergencyPhone,
        roleStatus: userRoles.status,
        roleCreatedAt: userRoles.createdAt,
        roleUpdatedAt: userRoles.updatedAt,
        // Verificar si tiene expediente médico
        hasMedicalRecord: sql<boolean>`EXISTS (
          SELECT 1 FROM medical_records 
          WHERE medical_records."patientId" = ${user.id}
        )`
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(whereClause)
      .orderBy(...orderByClause)
      .limit(limit)
      .offset(offset);

    // Procesar los datos para incluir edad y formatear teléfonos
    const processedPatients = patients.map(patient => ({
      ...patient,
      phone: patient.phone ? formatPhoneForDisplay(patient.phone) : null,
      emergencyPhone: patient.emergencyPhone ? formatPhoneForDisplay(patient.emergencyPhone) : null,
      age: patient.dateOfBirth ? calculateAge(patient.dateOfBirth.toISOString()) : null,
      isMinor: patient.dateOfBirth ? calculateAge(patient.dateOfBirth.toISOString()) < 18 : false
    }));

    // Calcular estadísticas
    const stats = await calculatePatientStats(userId);

    return NextResponse.json({
      data: processedPatients,
      stats,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });

  } catch (error) {
    console.error('Error fetching patients:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const data = await request.json();

    // Validaciones básicas
    if (!data.firstName || !data.lastName || !data.email) {
      return NextResponse.json(
        { error: 'Nombre, apellido y email son requeridos' },
        { status: 400 }
      );
    }

    // Verificar si el email ya existe
    const existingUser = await db
      .select()
      .from(user)
      .where(eq(user.email, data.email))
      .limit(1);

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un usuario con este email' },
        { status: 400 }
      );
    }

    // Crear el usuario
    const newUser = await db.insert(user).values({
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phone ? formatPhoneForStorage(data.phone) : null,
      alternativePhone: data.alternativePhone ? formatPhoneForStorage(data.alternativePhone) : null,
      dateOfBirth: data.dateOfBirth ? new Date(data.dateOfBirth) : null,
      gender: data.gender || null,
      documentType: data.documentType || null,
      documentNumber: data.documentNumber || null,
      address: data.address || null,
      emergencyContact: data.emergencyContact || null,
      emergencyPhone: data.emergencyPhone ? formatPhoneForStorage(data.emergencyPhone) : null,
      overallStatus: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    if (newUser.length === 0) {
      return NextResponse.json(
        { error: 'Error al crear el usuario' },
        { status: 500 }
      );
    }

    const createdUser = newUser[0];

    // Asignar el rol de paciente
    await db.insert(userRoles).values({
      userId: createdUser.id,
      role: 'patient',
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date()
    });

    // Generar token de activación e invitación
    const invitationToken = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días
    
    // Crear invitación de activación
    await db.insert(patientInvitations).values({
      id: generateId(),
      patientUserId: createdUser.id,
      guardianEmail: createdUser.email, // El paciente mismo es quien activará
      invitationToken,
      status: 'pending',
      expiresAt,
      createdAt: new Date()
    });

    // Enviar email de activación
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
    const activationLink = `${baseUrl}/activate-account?token=${invitationToken}`;
    
    // TODO: Obtener información del doctor y consultorio del contexto
    const emailResult = await sendEmail({
      to: createdUser.email,
      subject: 'Activación de Cuenta - Portal Médico',
      html: emailTemplates.accountActivation({
        patientName: `${createdUser.firstName} ${createdUser.lastName}`,
        doctorName: 'Dr. Sistema', // TODO: Usar doctor actual del contexto
        consultoryName: 'Consultorio Médico', // TODO: Usar consultorio actual del contexto
        activationLink,
        appointmentDate: 'próximamente',
        recipientType: 'patient'
      })
    });

    if (!emailResult.success) {
      console.warn('No se pudo enviar email de activación:', emailResult.error);
    }
    
    return NextResponse.json({
      id: createdUser.id,
      firstName: createdUser.firstName,
      lastName: createdUser.lastName,
      email: createdUser.email,
      phone: createdUser.phone ? formatPhoneForDisplay(createdUser.phone) : null,
      requiresActivation: true,
      activationToken: invitationToken, // Para debug/testing
      message: emailResult.success 
        ? 'Paciente creado exitosamente. Se ha enviado email de activación.'
        : 'Paciente creado exitosamente. Email de activación pendiente.'
    });

  } catch (error) {
    console.error('Error creating patient:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// Función auxiliar para calcular edad
function calculateAge(birthDate: string): number {
  const birth = new Date(birthDate);
  const today = new Date();
  let age = today.getFullYear() - birth.getFullYear();
  const monthDiff = today.getMonth() - birth.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--;
  }
  
  return age;
}

async function calculatePatientStats(doctorId: string) {
  try {
    // Total de pacientes del doctor
    const totalPatientsResult = await db
      .select({ count: count() })
      .from(userRoles)
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active')
        )
      );
    const totalPatients = totalPatientsResult[0]?.count || 0;

    // Pacientes activos (ya lo tenemos arriba)
    const activePatients = totalPatients;

    // Pacientes nuevos este mes
    const startOfThisMonth = startOfMonth(new Date());
    const newThisMonthResult = await db
      .select({ count: count() })
      .from(userRoles)
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active'),
          gte(userRoles.createdAt, startOfThisMonth)
        )
      );
    const newThisMonth = newThisMonthResult[0]?.count || 0;

    // Edad promedio de los pacientes
    const patientsWithAge = await db
      .select({
        dateOfBirth: user.dateOfBirth
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active')
        )
      );

    let totalAge = 0;
    let countWithAge = 0;
    
    patientsWithAge.forEach(patient => {
      if (patient.dateOfBirth) {
        const age = calculateAge(patient.dateOfBirth.toISOString());
        totalAge += age;
        countWithAge++;
      }
    });

    const averageAge = countWithAge > 0 ? totalAge / countWithAge : 0;

    // Consultas programadas para hoy
    const todayStart = startOfDay(new Date());
    const todayEnd = endOfDay(new Date());
    
    const appointmentsTodayResult = await db
      .select({ count: count() })
      .from(appointments)
      .where(
        and(
          eq(appointments.doctorId, doctorId),
          gte(appointments.scheduledDate, todayStart),
          lte(appointments.scheduledDate, todayEnd),
          or(
            eq(appointments.status, 'scheduled'),
            eq(appointments.status, 'confirmed')
          )
        )
      );
    const appointmentsToday = appointmentsTodayResult[0]?.count || 0;

    return {
      totalPatients,
      activePatients,
      newThisMonth,
      averageAge: Math.round(averageAge),
      appointmentsToday
    };
  } catch (error) {
    console.error('Error calculating patient stats:', error);
    return {
      totalPatients: 0,
      activePatients: 0,
      newThisMonth: 0,
      averageAge: 0,
      appointmentsToday: 0
    };
  }
}