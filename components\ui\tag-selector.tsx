'use client';

import * as React from 'react';
import { X, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Input } from '@/components/ui/input';

interface Tag {
  id: string;
  name: string;
  color?: string;
  category?: string;
}

interface TagSelectorProps {
  availableTags: Tag[];
  selectedTags: string[];
  onChange: (tags: string[]) => void;
  placeholder?: string;
  className?: string;
}

export function TagSelector({
  availableTags,
  selectedTags,
  onChange,
  placeholder = 'Seleccionar tags...',
  className,
}: TagSelectorProps) {
  const [isOpen, setIsOpen] = React.useState(false);
  const [search, setSearch] = React.useState('');

  const selectedTagObjects = availableTags.filter((tag) =>
    selectedTags.includes(tag.id)
  );

  const filteredTags = availableTags.filter((tag) =>
    tag.name.toLowerCase().includes(search.toLowerCase())
  );

  const groupedTags = filteredTags.reduce((acc, tag) => {
    const category = tag.category || 'general';
    if (!acc[category]) {
      acc[category] = [];
    }
    acc[category].push(tag);
    return acc;
  }, {} as Record<string, Tag[]>);

  const getCategoryLabel = (category: string) => {
    const labels: Record<string, string> = {
      requirement: 'Requerimientos',
      specialty: 'Especialidades',
      type: 'Tipo de Servicio',
      duration: 'Duración',
      general: 'General',
    };
    return labels[category] || category;
  };

  const getBadgeColor = (color?: string) => {
    const colorMap: Record<string, string> = {
      red: 'bg-red-100 text-red-800 border-red-200 hover:bg-red-200',
      green: 'bg-green-100 text-green-800 border-green-200 hover:bg-green-200',
      blue: 'bg-blue-100 text-blue-800 border-blue-200 hover:bg-blue-200',
      yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200 hover:bg-yellow-200',
      purple: 'bg-purple-100 text-purple-800 border-purple-200 hover:bg-purple-200',
      orange: 'bg-orange-100 text-orange-800 border-orange-200 hover:bg-orange-200',
      pink: 'bg-pink-100 text-pink-800 border-pink-200 hover:bg-pink-200',
      cyan: 'bg-cyan-100 text-cyan-800 border-cyan-200 hover:bg-cyan-200',
      gray: 'bg-gray-100 text-gray-800 border-gray-200 hover:bg-gray-200',
    };
    return colorMap[color || 'blue'] || colorMap.blue;
  };

  const toggleTag = (tagId: string) => {
    if (selectedTags.includes(tagId)) {
      onChange(selectedTags.filter((id) => id !== tagId));
    } else {
      onChange([...selectedTags, tagId]);
    }
  };

  const removeTag = (tagId: string) => {
    onChange(selectedTags.filter((id) => id !== tagId));
  };

  return (
    <div className={cn('w-full space-y-2', className)}>
      {/* Selected Tags Display */}
      <div className="min-h-[38px] rounded-md border border-input bg-background px-3 py-2">
        {selectedTagObjects.length > 0 ? (
          <div className="flex flex-wrap gap-1">
            {selectedTagObjects.map((tag) => (
              <Badge
                key={tag.id}
                variant="outline"
                className={cn(
                  'text-xs pr-1.5 hover:pr-1.5',
                  getBadgeColor(tag.color)
                )}
              >
                {tag.name}
                <button
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    removeTag(tag.id);
                  }}
                  className="ml-1 hover:text-red-600"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))}
          </div>
        ) : (
          <span className="text-sm text-muted-foreground">{placeholder}</span>
        )}
      </div>

      {/* Add Tags Button */}
      <Button
        type="button"
        variant="outline"
        size="sm"
        className="w-full"
        onClick={() => setIsOpen(true)}
      >
        <Plus className="h-4 w-4 mr-2" />
        Agregar Tags
      </Button>

      {/* Tag Selection Dialog */}
      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="max-w-lg max-h-[90vh] p-0">
          <DialogHeader className="px-6 pt-6 pb-2">
            <DialogTitle>Seleccionar Tags</DialogTitle>
          </DialogHeader>
          
          <div className="px-6 pb-2">
            <Input
              placeholder="Buscar tags..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="w-full"
            />
          </div>

          <ScrollArea className="max-h-[60vh] px-6 pb-6">
            <div className="space-y-4">
              {Object.entries(groupedTags).map(([category, tags]) => (
                <div key={category}>
                  <h3 className="text-sm font-medium text-muted-foreground mb-2">
                    {getCategoryLabel(category)}
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {tags.map((tag) => {
                      const isSelected = selectedTags.includes(tag.id);
                      return (
                        <Badge
                          key={tag.id}
                          variant={isSelected ? 'default' : 'outline'}
                          className={cn(
                            'cursor-pointer transition-colors',
                            isSelected
                              ? 'bg-primary text-primary-foreground hover:bg-primary/90'
                              : getBadgeColor(tag.color)
                          )}
                          onClick={() => toggleTag(tag.id)}
                        >
                          {tag.name}
                          {isSelected && (
                            <X className="h-3 w-3 ml-1" />
                          )}
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>
    </div>
  );
}