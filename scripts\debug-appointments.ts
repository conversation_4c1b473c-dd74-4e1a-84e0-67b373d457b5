#!/usr/bin/env tsx

import { db } from '../db/drizzle';
import { appointments, user } from '../db/schema';
import { eq, and, gte, lte, count } from 'drizzle-orm';

async function debugAppointments() {
  console.log('🔍 Debug: Verificando citas en la base de datos...\n');

  try {
    // 1. Verificar total de citas
    const totalAppointments = await db
      .select({ count: count() })
      .from(appointments);
    
    console.log(`📊 Total de citas en DB: ${totalAppointments[0]?.count || 0}`);

    // 2. Verificar citas por estado
    const appointmentsByStatus = await db
      .select({
        status: appointments.status,
        count: count()
      })
      .from(appointments)
      .groupBy(appointments.status);

    console.log('\n📈 Citas por estado:');
    appointmentsByStatus.forEach(stat => {
      console.log(`  ${stat.status}: ${stat.count}`);
    });

    // 3. Verificar citas de hoy
    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
    const todayStart = new Date(today + 'T00:00:00.000Z');
    const todayEnd = new Date(today + 'T23:59:59.999Z');

    const todayAppointments = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        status: appointments.status,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
      })
      .from(appointments)
      .where(
        and(
          gte(appointments.scheduledDate, todayStart),
          lte(appointments.scheduledDate, todayEnd)
        )
      );

    console.log(`\n📅 Citas para hoy (${today}): ${todayAppointments.length}`);
    
    if (todayAppointments.length > 0) {
      console.log('\nDetalles de citas de hoy:');
      todayAppointments.forEach(apt => {
        console.log(`  ID: ${apt.id}`);
        console.log(`  Título: ${apt.title}`);
        console.log(`  Doctor ID: ${apt.doctorId}`);
        console.log(`  Paciente ID: ${apt.patientId}`);
        console.log(`  Estado: ${apt.status}`);
        console.log(`  Fecha: ${apt.scheduledDate}`);
        console.log(`  Hora: ${apt.startTime}`);
        console.log('  ---');
      });
    }

    // 4. Verificar usuarios que son doctores
    const doctors = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.role,
        overallStatus: user.overallStatus,
      })
      .from(user)
      .where(eq(user.role, 'doctor'));

    console.log(`\n👨‍⚕️ Doctores en el sistema: ${doctors.length}`);
    
    if (doctors.length > 0) {
      console.log('\nDetalles de doctores:');
      doctors.forEach(doctor => {
        console.log(`  ID: ${doctor.id}`);
        console.log(`  Nombre: ${doctor.firstName} ${doctor.lastName}`);
        console.log(`  Estado: ${doctor.overallStatus}`);
        console.log('  ---');
      });

      // 5. Para cada doctor, verificar sus citas de hoy
      for (const doctor of doctors) {
        const doctorTodayAppointments = await db
          .select({
            id: appointments.id,
            title: appointments.title,
            status: appointments.status,
            scheduledDate: appointments.scheduledDate,
            startTime: appointments.startTime,
          })
          .from(appointments)
          .where(
            and(
              eq(appointments.doctorId, doctor.id),
              gte(appointments.scheduledDate, todayStart),
              lte(appointments.scheduledDate, todayEnd)
            )
          );

        console.log(`\n📋 Citas hoy para Dr. ${doctor.firstName} ${doctor.lastName}: ${doctorTodayAppointments.length}`);
        
        if (doctorTodayAppointments.length > 0) {
          const pending = doctorTodayAppointments.filter(apt => 
            apt.status === 'scheduled' || apt.status === 'pending_confirmation'
          ).length;
          
          console.log(`  📌 Pendientes: ${pending}`);
          console.log(`  📌 Total: ${doctorTodayAppointments.length}`);
          
          doctorTodayAppointments.forEach(apt => {
            console.log(`    - ${apt.title} (${apt.status}) - ${apt.startTime}`);
          });
        }
      }
    }

    // 6. Verificar citas futuras
    const futureAppointments = await db
      .select({ count: count() })
      .from(appointments)
      .where(gte(appointments.scheduledDate, new Date()));

    console.log(`\n🔮 Citas futuras: ${futureAppointments[0]?.count || 0}`);

  } catch (error) {
    console.error('❌ Error en debug:', error);
  }
}

// Ejecutar el debug
debugAppointments().then(() => {
  console.log('\n✅ Debug completado');
  process.exit(0);
}).catch(error => {
  console.error('❌ Error ejecutando debug:', error);
  process.exit(1);
});