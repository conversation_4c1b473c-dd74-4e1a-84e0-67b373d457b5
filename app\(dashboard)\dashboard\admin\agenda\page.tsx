'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { Calendar, Clock, Plus, Search, Filter, ChevronLeft, ChevronRight, Users, BarChart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, addDays, subDays } from 'date-fns';
import { es } from 'date-fns/locale';
import { AppointmentsList } from '@/components/agenda/appointments-list';
import { AppointmentModal } from '@/components/agenda/appointment-modal';
import { CalendarWeekView } from '@/components/agenda/calendar-week-view';
import { CalendarMonthView } from '@/components/agenda/calendar-month-view';
import { CalendarDayView } from '@/components/agenda/calendar-day-view';
import { DoctorHeader } from '@/components/agenda/doctor-header';
import { toast } from 'sonner';

export default function AdminAgendaPage() {
  const { user } = useUser();
  const [appointments, setAppointments] = useState([]);
  const [doctors, setDoctors] = useState([]);
  const [consultories, setConsultories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [viewMode, setViewMode] = useState<'day' | 'week' | 'month' | 'list'>('list');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [selectedDoctorId, setSelectedDoctorId] = useState('all');
  const [consultoryFilter, setConsultoryFilter] = useState('all');
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState(null);
  const [globalStats, setGlobalStats] = useState({
    totalToday: 0,
    totalWeek: 0,
    totalMonth: 0,
    byDoctor: {} as Record<string, number>,
    byConsultory: {} as Record<string, number>
  });
  const [stats, setStats] = useState({
    total: 0,
    scheduled: 0,
    confirmed: 0,
    completed: 0,
    cancelled: 0
  });
  const [selectedDoctorInfo, setSelectedDoctorInfo] = useState(null);

  // Cargar doctores
  const fetchDoctors = async () => {
    try {
      const response = await fetch('/api/catalogs/doctor-service-prices/doctors');
      const data = await response.json();
      if (response.ok) {
        setDoctors(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar doctores:', error);
    }
  };

  // Cargar consultorios
  const fetchConsultories = async () => {
    try {
      const response = await fetch('/api/catalogs/consultories?isActive=true');
      const data = await response.json();
      if (response.ok) {
        setConsultories(data.data || []);
      }
    } catch (error) {
      console.error('Error al cargar consultorios:', error);
    }
  };

  // Obtener estadísticas globales
  const fetchGlobalStats = async () => {
    try {
      // Obtener citas de hoy
      const today = format(new Date(), 'yyyy-MM-dd');
      const todayResponse = await fetch(`/api/appointments?dateFrom=${today}&dateTo=${today}`);
      const todayData = await todayResponse.json();
      
      // Obtener citas de la semana
      const weekStart = format(startOfWeek(new Date(), { locale: es }), 'yyyy-MM-dd');
      const weekEnd = format(endOfWeek(new Date(), { locale: es }), 'yyyy-MM-dd');
      const weekResponse = await fetch(`/api/appointments?dateFrom=${weekStart}&dateTo=${weekEnd}`);
      const weekData = await weekResponse.json();
      
      // Obtener citas del mes
      const monthStart = format(startOfMonth(new Date()), 'yyyy-MM-dd');
      const monthEnd = format(endOfMonth(new Date()), 'yyyy-MM-dd');
      const monthResponse = await fetch(`/api/appointments?dateFrom=${monthStart}&dateTo=${monthEnd}`);
      const monthData = await monthResponse.json();
      
      if (todayResponse.ok && weekResponse.ok && monthResponse.ok) {
        setGlobalStats({
          totalToday: todayData.stats?.total || 0,
          totalWeek: weekData.stats?.total || 0,
          totalMonth: monthData.stats?.total || 0,
          byDoctor: {},
          byConsultory: {}
        });
      }
    } catch (error) {
      console.error('Error al cargar estadísticas globales:', error);
    }
  };

  // Obtener información específica del médico seleccionado
  const fetchSelectedDoctorInfo = async (doctorId: string) => {
    if (doctorId === 'all') {
      setSelectedDoctorInfo(null);
      return;
    }
    
    try {
      console.log('🔍 Obteniendo información del médico seleccionado (Admin):', doctorId);
      
      const response = await fetch(`/api/auth/current-user?userId=${doctorId}`);
      const data = await response.json();
      
      if (response.ok && data.success) {
        const userInfo = data.data;
        
        setSelectedDoctorInfo({
          id: userInfo.id,
          firstName: userInfo.firstName || userInfo.name?.split(' ')[0] || 'Doctor',
          lastName: userInfo.lastName || userInfo.name?.split(' ')[1] || '',
          email: userInfo.email,
          imageUrl: userInfo.imageUrl,
          clerkImageUrl: userInfo.imageUrl, // Para admins, usamos la imagen de la BD
          specialty: userInfo.medicalSpecialty || 'Medicina General'
        });
        
        console.log('✅ Información del médico seleccionado cargada (Admin):', userInfo);
      } else {
        console.error('❌ Error al obtener información del médico (Admin):', data);
        setSelectedDoctorInfo(null);
      }
    } catch (error) {
      console.error('Error al obtener información del médico seleccionado (Admin):', error);
      setSelectedDoctorInfo(null);
    }
  };

  // Obtener citas
  const fetchAppointments = async () => {
    try {
      setLoading(true);
      
      // Calcular rango de fechas según la vista
      let dateFrom, dateTo;
      if (viewMode === 'day') {
        dateFrom = format(currentDate, 'yyyy-MM-dd');
        dateTo = format(currentDate, 'yyyy-MM-dd');
      } else if (viewMode === 'week') {
        dateFrom = format(startOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
        dateTo = format(endOfWeek(currentDate, { locale: es }), 'yyyy-MM-dd');
      } else if (viewMode === 'month') {
        dateFrom = format(startOfMonth(currentDate), 'yyyy-MM-dd');
        dateTo = format(endOfMonth(currentDate), 'yyyy-MM-dd');
      }

      const params = new URLSearchParams({
        ...(selectedDoctorId !== 'all' && { doctorId: selectedDoctorId }),
        ...(consultoryFilter !== 'all' && { consultoryId: consultoryFilter }),
        ...(dateFrom && { dateFrom }),
        ...(dateTo && { dateTo }),
        ...(searchTerm && { search: searchTerm }),
        ...(statusFilter !== 'all' && { status: statusFilter }),
        orderBy: 'scheduledDate',
        orderDirection: 'asc',
        limit: '100'
      });

      const response = await fetch(`/api/appointments?${params}`);
      const data = await response.json();

      if (response.ok) {
        setAppointments(data.data || []);
        if (data.stats?.byStatus) {
          setStats({
            total: data.stats.total || 0,
            scheduled: data.stats.byStatus.scheduled || 0,
            confirmed: data.stats.byStatus.confirmed || 0,
            completed: data.stats.byStatus.completed || 0,
            cancelled: data.stats.byStatus.cancelled || 0
          });
        }
      } else {
        toast.error('Error al cargar las citas');
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchDoctors();
    fetchConsultories();
    fetchGlobalStats();
  }, []);

  useEffect(() => {
    fetchAppointments();
  }, [currentDate, viewMode, searchTerm, statusFilter, selectedDoctorId, consultoryFilter]);

  // Obtener información del médico cuando cambie el filtro
  useEffect(() => {
    if (selectedDoctorId && selectedDoctorId !== 'all') {
      fetchSelectedDoctorInfo(selectedDoctorId);
    } else {
      setSelectedDoctorInfo(null);
    }
  }, [selectedDoctorId]);

  // Navegación de fechas
  const navigateDate = (direction: 'prev' | 'next') => {
    if (viewMode === 'day') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 1) : addDays(currentDate, 1));
    } else if (viewMode === 'week') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 7) : addDays(currentDate, 7));
    } else if (viewMode === 'month') {
      setCurrentDate(direction === 'prev' ? subDays(currentDate, 30) : addDays(currentDate, 30));
    }
  };

  // Formato del período actual
  const formatCurrentPeriod = () => {
    if (viewMode === 'day') {
      return format(currentDate, "EEEE, d 'de' MMMM 'de' yyyy", { locale: es });
    } else if (viewMode === 'week') {
      const start = startOfWeek(currentDate, { locale: es });
      const end = endOfWeek(currentDate, { locale: es });
      return `${format(start, "d MMM", { locale: es })} - ${format(end, "d MMM yyyy", { locale: es })}`;
    } else if (viewMode === 'month') {
      return format(currentDate, "MMMM 'de' yyyy", { locale: es });
    }
    return '';
  };

  // Calcular estadísticas para el header
  const getDoctorStats = () => {
    const today = format(new Date(), 'yyyy-MM-dd');
    const todayAppointments = appointments.filter(app => 
      format(new Date(app.scheduledDate), 'yyyy-MM-dd') === today
    );
    
    const weekStart = startOfWeek(new Date(), { locale: es });
    const weekEnd = endOfWeek(new Date(), { locale: es });
    const weekAppointments = appointments.filter(app => {
      const appDate = new Date(app.scheduledDate);
      return appDate >= weekStart && appDate <= weekEnd;
    });
    
    const confirmedCount = appointments.filter(app => app.status === 'confirmed').length;
    
    return {
      todayCount: todayAppointments.length,
      weekCount: weekAppointments.length,
      confirmedCount
    };
  };

  return (
    <div className="space-y-6 px-2 sm:px-4 lg:px-6 py-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Calendar className="h-6 w-6 text-blue-600" />
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Agenda Médica - Administrador</h1>
              <p className="text-sm lg:text-base text-gray-600">
                Gestión completa de citas médicas del sistema
              </p>
            </div>
          </div>
        </div>
        
        <Button onClick={() => setIsCreateModalOpen(true)}>
          <Plus className="h-4 w-4 mr-2" />
          Nueva Cita
        </Button>
      </div>

      {/* Selector de médico destacado para admin */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-800">Vista de administrador: Puedes ver y gestionar las citas de todos los médicos</span>
            </div>
            <Select value={selectedDoctorId} onValueChange={setSelectedDoctorId}>
              <SelectTrigger className="w-64 bg-white">
                <SelectValue placeholder="Seleccionar médico" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">
                  <span className="font-semibold">Todos los médicos</span>
                </SelectItem>
                {doctors.map((doctor: any) => (
                  <SelectItem key={doctor.id} value={doctor.id}>
                    Dr. {doctor.firstName} {doctor.lastName}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Estadísticas globales para admin */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Citas Hoy</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-blue-600">{globalStats.totalToday}</div>
            <p className="text-xs text-gray-500 mt-1">En todos los consultorios</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Citas Esta Semana</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-green-600">{globalStats.totalWeek}</div>
            <p className="text-xs text-gray-500 mt-1">Total acumulado</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Citas Este Mes</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-purple-600">{globalStats.totalMonth}</div>
            <p className="text-xs text-gray-500 mt-1">Total del mes</p>
          </CardContent>
        </Card>
      </div>

      {/* Estadísticas de citas filtradas */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-900">{stats.total}</div>
            <p className="text-sm text-gray-600">Total citas</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">{stats.scheduled}</div>
            <p className="text-sm text-gray-600">Programadas</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">{stats.confirmed}</div>
            <p className="text-sm text-gray-600">Confirmadas</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-gray-600">{stats.completed}</div>
            <p className="text-sm text-gray-600">Completadas</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-red-600">{stats.cancelled}</div>
            <p className="text-sm text-gray-600">Canceladas</p>
          </CardContent>
        </Card>
      </div>

      {/* Toolbar de navegación y filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0">
            {/* Navegación de fecha */}
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('prev')}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button 
                  variant="outline"
                  onClick={() => setCurrentDate(new Date())}
                >
                  Hoy
                </Button>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => navigateDate('next')}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="text-lg font-semibold text-gray-900">
                {formatCurrentPeriod()}
              </div>
            </div>
            
            {/* Selector de vista */}
            <Tabs value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
              <TabsList>
                <TabsTrigger value="day">Día</TabsTrigger>
                <TabsTrigger value="week">Semana</TabsTrigger>
                <TabsTrigger value="month">Mes</TabsTrigger>
                <TabsTrigger value="list">Lista</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
        </CardContent>
      </Card>

      {/* Filtros de búsqueda */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label>Buscar paciente</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Nombre del paciente..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <div>
              <Label>Consultorio</Label>
              <Select value={consultoryFilter} onValueChange={setConsultoryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos los consultorios" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los consultorios</SelectItem>
                  {consultories.map((consultory: any) => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      {consultory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Estado</Label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Todos los estados" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los estados</SelectItem>
                  <SelectItem value="scheduled">📅 Programadas</SelectItem>
                  <SelectItem value="confirmed">✅ Confirmadas</SelectItem>
                  <SelectItem value="in_progress">🟢 En consulta</SelectItem>
                  <SelectItem value="completed">✅ Completadas</SelectItem>
                  <SelectItem value="cancelled">❌ Canceladas</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vista principal */}
      {viewMode === 'list' && (
        <AppointmentsList 
          appointments={appointments}
          loading={loading}
          onEdit={(appointment) => {
            setSelectedAppointment(appointment);
            setIsCreateModalOpen(true);
          }}
          onRefresh={fetchAppointments}
          showDoctor={true}
          showAuditInfo={true}
        />
      )}

      {viewMode === 'day' && (
        <>
          <DoctorHeader
            doctorInfo={selectedDoctorInfo}
            availableDoctors={doctors}
            onDoctorChange={(doctorId) => setSelectedDoctorId(doctorId)}
            stats={getDoctorStats()}
            isSelectable={true}
            className="mb-4"
          />
          <CalendarDayView
            appointments={appointments}
            currentDate={currentDate}
            loading={loading}
            doctorInfo={selectedDoctorInfo}
            onTimeSlotClick={(date, time) => {
              setCurrentDate(date);
              setIsCreateModalOpen(true);
            }}
            onAppointmentClick={(appointment) => {
              setSelectedAppointment(appointment);
              setIsCreateModalOpen(true);
            }}
          />
        </>
      )}

      {viewMode === 'week' && (
        <CalendarWeekView
          appointments={appointments}
          currentDate={currentDate}
          loading={loading}
          onTimeSlotClick={(date, time) => {
            setCurrentDate(date);
            setIsCreateModalOpen(true);
          }}
          onAppointmentClick={(appointment) => {
            setSelectedAppointment(appointment);
            setIsCreateModalOpen(true);
          }}
        />
      )}

      {viewMode === 'month' && (
        <CalendarMonthView
          appointments={appointments}
          currentDate={currentDate}
          loading={loading}
          onDateClick={(date) => {
            setCurrentDate(date);
            setViewMode('day');
          }}
          onAppointmentClick={(appointment) => {
            setSelectedAppointment(appointment);
            setIsCreateModalOpen(true);
          }}
        />
      )}

      {/* Modal de creación/edición */}
      <AppointmentModal
        isOpen={isCreateModalOpen}
        onClose={() => {
          setIsCreateModalOpen(false);
          setSelectedAppointment(null);
        }}
        appointment={selectedAppointment}
        doctorId={selectedDoctorId !== 'all' ? selectedDoctorId : undefined}
        onSuccess={fetchAppointments}
      />
    </div>
  );
}