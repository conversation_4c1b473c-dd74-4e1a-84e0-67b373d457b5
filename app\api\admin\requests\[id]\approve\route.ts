import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  registrationRequests, 
  user, 
  userRoles, 
  notifications,
  assistantDoctorRelations,
  guardianPatientRelations,
  medicalSpecialties,
  consultories
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { randomBytes } from 'crypto';

function generateId(): string {
  return randomBytes(16).toString('hex');
}

// Función para mapear especialidades a specialtyId
async function mapSpecialtyToId(specialtyName: string): Promise<number | null> {
  if (!specialtyName) return null;
  
  try {
    const specialty = await db
      .select()
      .from(medicalSpecialties)
      .where(eq(medicalSpecialties.name, specialtyName))
      .limit(1);
    
    return specialty.length > 0 ? specialty[0].id : null;
  } catch (error) {
    console.error('Error mapeando especialidad:', error);
    return null;
  }
}

// Función para mapear consultorio por nombre a ID
async function mapConsultoryToId(consultoryName: string): Promise<string | null> {
  if (!consultoryName) return null;
  
  try {
    const consultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.name, consultoryName))
      .limit(1);
    
    return consultory.length > 0 ? consultory[0].id : null;
  } catch (error) {
    console.error('Error mapeando consultorio:', error);
    return null;
  }
}

// Función para procesar datos específicos según el rol
async function processSpecificData(role: string, specificData: any): Promise<any> {
  const processed: any = {};
  
  switch (role) {
    case 'doctor':
      // Mapear especialidad a specialtyId
      if (specificData.specialty) {
        processed.specialtyId = await mapSpecialtyToId(specificData.specialty);
      }
      
      // Asignar consultorio y licencia médica
      if (specificData.consultoryId) {
        processed.consultoryId = specificData.consultoryId;
      }
      if (specificData.medicalLicense) {
        processed.medicalLicense = specificData.medicalLicense;
      }
      
      // Guardar datos complejos en roleData
      processed.roleData = {
        specialty: specificData.specialty,
        workSchedule: specificData.workSchedule,
        university: specificData.university,
        graduationYear: specificData.graduationYear,
        subSpecialties: specificData.subSpecialties || []
      };
      break;
      
    case 'assistant':
      // Datos específicos del asistente - mapear consultorio por nombre a ID
      if (specificData.consultoryId) {
        // Si consultoryId es un nombre, mapearlo a ID
        const consultoryId = await mapConsultoryToId(specificData.consultoryId);
        processed.consultoryId = consultoryId || specificData.consultoryId;
      }
      
      processed.roleData = {
        position: specificData.position,
        yearsExperience: specificData.yearsExperience,
        assignedDoctors: specificData.assignedDoctors || [],
        permissions: specificData.permissions || {}
      };
      break;
      
    case 'patient':
      // Pacientes solo guardan información básica - información médica se maneja en primera consulta
      if (specificData.preferredDoctorId) {
        processed.preferredDoctorId = specificData.preferredDoctorId;
      }
      
      // Solo datos mínimos - información médica removida del onboarding
      processed.roleData = {
        onboardingCompleted: true,
        medicalInfoSource: 'first_consultation' // Indicar que info médica viene de consulta
      };
      break;
      
    case 'guardian':
      // Datos específicos del guardian
      processed.roleData = {
        relationship: specificData.relationship,
        associationCode: specificData.associationCode,
        createMinor: specificData.createMinor || false,
        minorData: specificData.minorData || null
      };
      break;
      
    case 'provider':
      // Datos específicos del proveedor
      processed.roleData = {
        companyName: specificData.companyName,
        nit: specificData.nit,
        companyAddress: specificData.companyAddress,
        companyPhone: specificData.companyPhone,
        serviceTypes: specificData.serviceTypes || [],
        consultoryIds: specificData.consultoryIds || []
      };
      break;
      
    default:
      processed.roleData = specificData;
  }
  
  return processed;
}

// Función para crear registros adicionales según el rol
async function createAdditionalRecords(userId: string, role: string, specificData: any, processed: any): Promise<void> {
  try {
    switch (role) {
      case 'assistant':
        // Crear relaciones asistente-doctor
        if (specificData.assignedDoctors && specificData.assignedDoctors.length > 0) {
          for (const doctorId of specificData.assignedDoctors) {
            await db.insert(assistantDoctorRelations).values({
              id: generateId(),
              assistantId: userId,
              doctorId: doctorId,
              consultoryId: processed.consultoryId,
              permissions: specificData.permissions || {},
              isActive: true,
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        }
        break;
        
      case 'guardian':
        // Crear relación guardian-paciente si se especifica un código de asociación
        if (specificData.associationCode) {
          // TODO: Implementar lógica para validar y usar código de asociación
          console.log('⚠️ Código de asociación detectado:', specificData.associationCode);
        }
        
        // Crear menor si se especifica
        if (specificData.createMinor && specificData.minorData) {
          // TODO: Implementar lógica para crear paciente menor
          console.log('⚠️ Crear menor detectado:', specificData.minorData);
        }
        break;
        
      case 'patient':
        // Los pacientes no necesitan registros adicionales por el momento
        // Las relaciones se crean desde el lado del guardian
        break;
        
      case 'doctor':
        // Los doctores no necesitan registros adicionales por el momento
        // Las relaciones se crean desde el lado del asistente
        break;
        
      case 'provider':
        // Los proveedores no necesitan registros adicionales por el momento
        // Las relaciones con consultorios se manejan en roleData
        break;
        
      default:
        console.log('ℹ️ No se requieren registros adicionales para el rol:', role);
        break;
    }
  } catch (error) {
    console.error('Error creando registros adicionales:', error);
    throw error;
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    const { id } = await params;
    
    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    // Verificar que el usuario es admin
    const adminRole = await db
      .select()
      .from(userRoles)
      .where(
        and(
          eq(userRoles.userId, userId),
          eq(userRoles.role, 'admin'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (adminRole.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Acceso denegado. Solo administradores.' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { notes } = body;

    // Obtener la solicitud
    const requestResult = await db
      .select()
      .from(registrationRequests)
      .where(eq(registrationRequests.id, id))
      .limit(1);

    if (requestResult.length === 0) {
      return NextResponse.json(
        { success: false, message: 'Solicitud no encontrada' },
        { status: 404 }
      );
    }

    const registrationRequest = requestResult[0];

    if (registrationRequest.status !== 'pending' && registrationRequest.status !== 'reviewing') {
      return NextResponse.json(
        { success: false, message: 'Esta solicitud ya ha sido procesada' },
        { status: 400 }
      );
    }

    // Aprobar solicitud paso a paso
    try {
      // 1. Actualizar estado de la solicitud
      await db
        .update(registrationRequests)
        .set({
          status: 'approved',
          reviewedBy: userId,
          reviewedAt: new Date(),
          reviewNotes: notes,
          updatedAt: new Date()
        })
        .where(eq(registrationRequests.id, id));

      // 2. Actualizar estado del usuario
      await db
        .update(user)
        .set({
          overallStatus: 'active',
          updatedAt: new Date()
        })
        .where(eq(user.id, registrationRequest.userId));

      // 3. Procesar datos específicos según el rol
      const processedData = await processSpecificData(
        registrationRequest.role,
        registrationRequest.specificData || {}
      );
      
      console.log('🔄 Datos procesados para rol:', registrationRequest.role, processedData);

      // 4. Crear o actualizar rol del usuario
      const existingRole = await db
        .select()
        .from(userRoles)
        .where(
          and(
            eq(userRoles.userId, registrationRequest.userId),
            eq(userRoles.role, registrationRequest.role)
          )
        )
        .limit(1);

      if (existingRole.length > 0) {
        // Actualizar rol existente con datos específicos
        await db
          .update(userRoles)
          .set({
            status: 'active',
            specialtyId: processedData.specialtyId,
            consultoryId: processedData.consultoryId,
            preferredDoctorId: processedData.preferredDoctorId,
            medicalLicense: processedData.medicalLicense,
            roleData: processedData.roleData,
            approvedBy: userId,
            approvedAt: new Date(),
            updatedAt: new Date()
          })
          .where(eq(userRoles.id, existingRole[0].id));
      } else {
        // Crear nuevo rol con datos específicos
        await db.insert(userRoles).values({
          id: generateId(),
          userId: registrationRequest.userId,
          role: registrationRequest.role,
          status: 'active',
          specialtyId: processedData.specialtyId,
          consultoryId: processedData.consultoryId,
          preferredDoctorId: processedData.preferredDoctorId,
          medicalLicense: processedData.medicalLicense,
          roleData: processedData.roleData,
          approvedBy: userId,
          approvedAt: new Date(),
          createdAt: new Date(),
          updatedAt: new Date()
        });
      }
      
      // 5. Crear registros adicionales según el rol
      await createAdditionalRecords(
        registrationRequest.userId,
        registrationRequest.role,
        registrationRequest.specificData || {},
        processedData
      );

      // 6. Crear notificación para el usuario
      await db.insert(notifications).values({
        id: generateId(),
        userId: registrationRequest.userId,
        type: 'approval',
        title: '¡Solicitud Aprobada!',
        message: `Tu solicitud como ${registrationRequest.role} ha sido aprobada. Ya puedes acceder a todas las funcionalidades del sistema.`,
        read: false,
        data: { 
          requestId: id, 
          role: registrationRequest.role,
          approvedBy: userId 
        },
        createdAt: new Date()
      });
    } catch (stepError) {
      console.error('Error en paso de aprobación:', stepError);
      throw stepError;
    }

    // 7. Actualizar metadata de Clerk
    const clerk = await clerkClient();
    await clerk.users.updateUserMetadata(registrationRequest.userId, {
      publicMetadata: {
        onboardingCompleted: true,
        role: registrationRequest.role,
        status: 'active'
      }
    });

    // TODO: Enviar email de confirmación al usuario

    return NextResponse.json({
      success: true,
      message: 'Solicitud aprobada exitosamente'
    });

  } catch (error) {
    console.error('Error approving registration request:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}