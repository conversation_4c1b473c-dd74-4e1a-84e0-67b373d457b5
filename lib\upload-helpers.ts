// Helper functions for uploading files during onboarding

export interface UploadResult {
  success: boolean;
  url?: string;
  publicId?: string;
  error?: string;
}

/**
 * Upload a single file to Cloudinary
 */
export async function uploadFile(file: File, preset: string): Promise<UploadResult> {
  try {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('preset', preset);

    const response = await fetch('/api/upload', {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      const errorData = await response.json();
      return {
        success: false,
        error: errorData.error || 'Error uploading file'
      };
    }

    const result = await response.json();
    
    if (result.success) {
      return {
        success: true,
        url: result.secure_url,
        publicId: result.public_id
      };
    } else {
      return {
        success: false,
        error: result.error || 'Upload failed'
      };
    }
  } catch (error) {
    console.error('Upload error:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error'
    };
  }
}

/**
 * Upload multiple documents with their respective presets
 */
export async function uploadDocuments(
  documents: { [key: string]: File },
  documentConfig: { [key: string]: string } // key -> preset mapping
): Promise<{ [key: string]: UploadResult }> {
  const results: { [key: string]: UploadResult } = {};
  
  // Upload files in parallel for better performance
  const uploadPromises = Object.entries(documents).map(async ([key, file]) => {
    const preset = documentConfig[key];
    if (!preset) {
      results[key] = {
        success: false,
        error: `No preset configured for ${key}`
      };
      return;
    }

    const result = await uploadFile(file, preset);
    results[key] = result;
  });

  await Promise.all(uploadPromises);
  return results;
}

/**
 * Document presets for different roles
 */
export const DOCUMENT_PRESETS = {
  // Doctor documents
  licensePhoto: 'medical_documents',
  diplomaPhoto: 'medical_documents', 
  cvPdf: 'medical_documents',
  
  // Assistant documents
  certificatePhoto: 'medical_documents',
  
  // Provider documents
  nitDocument: 'medical_documents',
  commercialLicense: 'medical_documents',
  catalog: 'medical_documents',
  
  // Patient documents
  insuranceCard: 'medical_documents',
  
  // Guardian documents
  legalDocuments: 'medical_documents',
} as const;

/**
 * Get document URLs from upload results
 */
export function getDocumentUrls(uploadResults: { [key: string]: UploadResult }): { [key: string]: string } {
  const urls: { [key: string]: string } = {};
  
  Object.entries(uploadResults).forEach(([key, result]) => {
    if (result.success && result.url) {
      urls[key] = result.url;
    }
  });
  
  return urls;
}

/**
 * Validate file types for documents
 */
export function validateDocumentFile(file: File, documentType: string): { valid: boolean; error?: string } {
  const maxSize = 10 * 1024 * 1024; // 10MB
  
  if (file.size > maxSize) {
    return {
      valid: false,
      error: 'El archivo debe ser menor a 10MB'
    };
  }

  // Define allowed types per document
  const allowedTypes: { [key: string]: string[] } = {
    licensePhoto: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    diplomaPhoto: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    cvPdf: ['application/pdf'],
    certificatePhoto: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    nitDocument: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
    commercialLicense: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png'],
    catalog: ['application/pdf'],
    insuranceCard: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
    legalDocuments: ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png']
  };

  const allowed = allowedTypes[documentType] || ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
  
  if (!allowed.includes(file.type)) {
    return {
      valid: false,
      error: `Tipo de archivo no válido. Permitidos: ${allowed.map(t => t.split('/')[1]).join(', ')}`
    };
  }

  return { valid: true };
}