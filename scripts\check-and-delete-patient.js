import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import { user, userRoles, guardianPatientRelations } from '../db/schema.js';
import { eq, and } from 'drizzle-orm';

config({ path: '.env.local' });

const sql = postgres(process.env.DATABASE_URL);
const db = drizzle(sql);

async function checkAndDeletePatient() {
  const documentNumber = '2499176101545';
  
  console.log(`🔍 Buscando paciente con documento: ${documentNumber}`);
  
  try {
    // Buscar el paciente
    const patients = await db
      .select()
      .from(user)
      .where(eq(user.documentNumber, documentNumber));
    
    if (patients.length === 0) {
      console.log('❌ No se encontró ningún paciente con ese documento');
      process.exit(0);
    }
    
    const patient = patients[0];
    console.log('✅ Paciente encontrado:', {
      id: patient.id,
      name: `${patient.firstName} ${patient.lastName}`,
      email: patient.email,
      documentNumber: patient.documentNumber
    });
    
    // Verificar si es un paciente (no un doctor o asistente)
    const roles = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, patient.id));
    
    console.log('📋 Roles del usuario:', roles.map(r => r.role));
    
    const isOnlyPatient = roles.length === 1 && roles[0].role === 'patient';
    
    if (!isOnlyPatient) {
      console.log('⚠️  Este usuario tiene otros roles además de paciente. No se puede eliminar automáticamente.');
      process.exit(1);
    }
    
    // Preguntar confirmación
    console.log('\n⚠️  ¿Deseas eliminar este registro de paciente? (s/n)');
    
    process.stdin.resume();
    process.stdin.setEncoding('utf8');
    
    process.stdin.on('data', async function (text) {
      const answer = text.trim().toLowerCase();
      
      if (answer === 's' || answer === 'si') {
        try {
          // Eliminar relaciones de guardián si existen
          await db
            .delete(guardianPatientRelations)
            .where(eq(guardianPatientRelations.patientId, patient.id));
          
          // Eliminar rol
          await db
            .delete(userRoles)
            .where(eq(userRoles.userId, patient.id));
          
          // Eliminar usuario
          await db
            .delete(user)
            .where(eq(user.id, patient.id));
          
          console.log('✅ Paciente eliminado exitosamente');
        } catch (error) {
          console.error('❌ Error eliminando paciente:', error);
        }
      } else {
        console.log('❌ Operación cancelada');
      }
      
      process.exit(0);
    });
    
  } catch (error) {
    console.error('Error:', error);
    process.exit(1);
  }
}

checkAndDeletePatient();