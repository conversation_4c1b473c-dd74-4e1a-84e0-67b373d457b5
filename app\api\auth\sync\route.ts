import { NextRequest, NextResponse } from 'next/server';
import { auth, clerkClient } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';
import { user, userRoles, registrationRequests } from '@/db/schema';

export async function GET(request: NextRequest) {
  return POST(request);
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();

    if (!userId) {
      return NextResponse.json(
        { success: false, message: 'No autorizado' },
        { status: 401 }
      );
    }

    console.log('📡 API Sync: Iniciando sincronización para usuario:', userId);

    // Obtener datos de Clerk primero
    const clerk = await clerkClient();
    const clerkUser = await clerk.users.getUser(userId);
    const userEmail = clerkUser.emailAddresses[0]?.emailAddress || '';

    console.log('👤 API Sync: Usuario de Clerk:', {
      userId,
      email: userEmail,
      currentMetadata: clerkUser.publicMetadata
    });

    // Verificar si el usuario existe por ID
    const existingUserById = await db.execute(sql`
      SELECT id, email, "overallStatus" as status FROM "user" WHERE id = ${userId} LIMIT 1
    `);

    if (existingUserById.rows.length > 0) {
      // Usuario existe, pero verificar si necesita actualizar metadatos de Clerk
      const existingUser = existingUserById.rows[0];
      
      console.log('🗄️ API Sync: Usuario encontrado en DB:', existingUser);
      
      // Obtener roles activos
      const rolesQuery = await db.execute(sql`
        SELECT role, status FROM user_roles WHERE "userId" = ${userId} AND status = 'active'
      `);
      
      // Obtener roles rechazados
      const rejectedRolesQuery = await db.execute(sql`
        SELECT role, status FROM user_roles WHERE "userId" = ${userId} AND status = 'rejected'
      `);
      
      const activeRoles = rolesQuery.rows;
      const rejectedRoles = rejectedRolesQuery.rows;
      const primaryRole = activeRoles[0]?.role;
      const userStatus = existingUser.status;

      console.log('🔑 API Sync: Roles del usuario:', {
        activeRoles,
        rejectedRoles,
        primaryRole,
        userStatus
      });

      // PRIORIDAD 1: Verificar si usuario está rechazado (simple como pendientes)
      if (rejectedRoles.length > 0) {
        const rejectedRole = rejectedRoles[0].role as string;
        console.log('🚨 API Sync: Usuario rechazado detectado:', rejectedRole);
        
        // Actualizar metadatos de Clerk si no están correctos
        if (clerkUser.publicMetadata?.status !== 'rejected') {
          const rejectedMetadata = {
            role: rejectedRole,
            status: 'rejected',
            onboardingCompleted: true
          };

          console.log('🔄 API Sync: Actualizando metadatos de rechazo:', rejectedMetadata);
          
          try {
            await clerk.users.updateUserMetadata(userId, {
              publicMetadata: rejectedMetadata
            });
            console.log('✅ API Sync: Metadatos de rechazo actualizados en Clerk');
          } catch (metadataError) {
            console.error('❌ API Sync: Error actualizando metadatos de rechazo:', metadataError);
          }
        }

        // Si acabamos de actualizar metadatos, forzar redirección
        const metadataWasUpdated = clerkUser.publicMetadata?.status !== 'rejected';
        
        console.log('✅ API Sync: Usuario rechazado - metadatos actualizados');
        return NextResponse.json({
          success: true,
          message: 'Usuario rechazado detectado',
          metadataUpdated: metadataWasUpdated,
          shouldRedirect: metadataWasUpdated, // Solo redirigir si acabamos de actualizar
          redirectTo: '/onboarding/rejected'
        });
      }

      // PRIORIDAD 2: Si el usuario está activo y tiene roles
      if (userStatus === 'active' && primaryRole && activeRoles.length > 0) {
        // Actualizar metadatos de Clerk si no están correctos
        if (clerkUser.publicMetadata?.status !== 'active' || clerkUser.publicMetadata?.role !== primaryRole) {
          const newMetadata = {
            role: primaryRole,
            roles: activeRoles.map(r => r.role),
            status: userStatus,
            onboardingCompleted: true
          };

          console.log('🔄 API Sync: Actualizando metadatos de Clerk con:', newMetadata);

          try {
            await clerk.users.updateUserMetadata(userId, {
              publicMetadata: newMetadata
            });
            
            console.log('✅ API Sync: Metadatos actualizados exitosamente');

            // Verificar que se actualizaron
            const updatedUser = await clerk.users.getUser(userId);
            console.log('🔍 API Sync: Metadatos después de actualización:', updatedUser.publicMetadata);
            
          } catch (metadataError) {
            console.error('❌ API Sync: Error actualizando metadatos de Clerk:', metadataError);
          }
        }

        console.log('✅ API Sync: Usuario activo con rol, retornando información');
        return NextResponse.json({
          success: true,
          message: 'Usuario ya existe en la base de datos',
          user: existingUser,
          roles: activeRoles,
          primaryRole,
          metadataUpdated: true,
          shouldRedirect: true,
          redirectTo: `/dashboard/${primaryRole}`
        });
      }
      
      // PRIORIDAD 3: Usuario existe pero no está activo o sin rol
      console.log('⚠️ API Sync: Usuario existe pero no está activo o sin rol');
      
      // Si el usuario está pendiente, verificar si ya completó onboarding
      if (userStatus === 'pending') {
        const hasCompletedOnboarding = clerkUser.publicMetadata?.onboardingCompleted;
        
        // Si ya completó onboarding, va a página de espera
        if (hasCompletedOnboarding) {
          console.log('✅ API Sync: Usuario pendiente con onboarding completado - ir a página de espera');
          return NextResponse.json({
            success: true,
            message: 'Usuario pendiente de aprobación',
            user: existingUser,
            roles: activeRoles,
            metadataUpdated: false,
            shouldRedirect: true,
            redirectTo: '/onboarding/pending'
          });
        } else {
          // Si no ha completado onboarding, permitir que lo complete
          console.log('✅ API Sync: Usuario pendiente sin onboarding - permitir completar onboarding');
          return NextResponse.json({
            success: true,
            message: 'Usuario pendiente - completar onboarding',
            user: existingUser,
            roles: activeRoles,
            metadataUpdated: false,
            shouldRedirect: true,
            redirectTo: '/onboarding'
          });
        }
      }
      
      return NextResponse.json({
        success: true,
        message: 'Usuario ya existe en la base de datos',
        user: existingUser,
        roles: activeRoles,
        metadataUpdated: userStatus === 'active' && primaryRole,
        shouldReload: userStatus === 'active' && primaryRole
      });
    }

    // Verificar si existe un usuario con el mismo email pero diferente ID
    const existingUserByEmail = await db.execute(sql`
      SELECT id, email, "overallStatus" as status FROM "user" WHERE email = ${userEmail} LIMIT 1
    `);

    if (existingUserByEmail.rows.length > 0) {
      // Usuario pre-existente en DB (ej: admin) - transferir a nuevo ID de Clerk
      const existingUser = existingUserByEmail.rows[0];
      
      // 1. Obtener todos los datos del usuario existente
      const fullUserQuery = await db.execute(sql`
        SELECT * FROM "user" WHERE email = ${userEmail}
      `);
      const userData = fullUserQuery.rows[0];
      
      // 2. Obtener roles existentes
      const rolesQuery = await db.execute(sql`
        SELECT role, status, "roleData" FROM user_roles WHERE "userId" = ${existingUser.id}
      `);
      
      // 3. Obtener solicitudes rechazadas del usuario existente
      const rejectedRequestsQuery = await db.execute(sql`
        SELECT role, status, "rejectionReason" 
        FROM "registrationRequests" 
        WHERE "userId" = ${existingUser.id} AND status = 'rejected'
        ORDER BY "reviewedAt" DESC
        LIMIT 1
      `);
      
      await db.execute(sql`BEGIN`);
      
      try {
        // 4. Primero eliminar usuario viejo (esto también elimina roles por CASCADE)
        await db.execute(sql`DELETE FROM "user" WHERE id = ${existingUser.id}`);

        // 5. Crear nuevo usuario con ID de Clerk y datos preservados
        const insertResult = await db.insert(user).values({
          id: userId,
          name: clerkUser.fullName || userData.name,
          email: userEmail,
          emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified',
          image: clerkUser.imageUrl || userData.image,
          overallStatus: userData.overallStatus || userData.status || 'active',
          firstName: clerkUser.firstName || userData.firstName || userData.first_name,
          lastName: clerkUser.lastName || userData.lastName || userData.last_name,
          documentType: userData.documentType || userData.document_type,
          documentNumber: userData.documentNumber || userData.document_number,
          dateOfBirth: userData.dateOfBirth ? new Date(userData.dateOfBirth) : (userData.date_of_birth ? new Date(userData.date_of_birth) : null),
          gender: userData.gender,
          phone: userData.phone,
          alternativePhone: userData.alternativePhone || userData.alternative_phone,
          countryId: userData.countryId || userData.country_id,
          departmentId: userData.departmentId || userData.department_id,
          municipalityId: userData.municipalityId || userData.municipality_id,
          occupationId: userData.occupationId || userData.occupation_id,
          address: userData.address,
          emergencyContact: userData.emergencyContact || userData.emergency_contact,
          emergencyPhone: userData.emergencyPhone || userData.emergency_phone,
          emergencyRelationshipId: userData.emergencyRelationshipId || userData.emergency_relationship_id,
          createdAt: userData.createdAt ? new Date(userData.createdAt) : new Date(),
          updatedAt: new Date()
        }).returning({ id: user.id, email: user.email, status: user.overallStatus });

        // 6. Recrear roles con el nuevo user_id
        for (const role of rolesQuery.rows) {
          await db.insert(userRoles).values({
            id: `${userId}-${role.role}`,
            userId: userId,
            role: role.role,
            status: role.status,
            roleData: role.roleData || role.role_data,
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }

        // 7. Recrear solicitudes rechazadas con el nuevo user_id
        for (const request of rejectedRequestsQuery.rows) {
          await db.insert(registrationRequests).values({
            id: sql`gen_random_uuid()`,
            userId: userId,
            role: request.role,
            status: 'rejected',
            rejectionReason: request.rejectionReason || 'Migrado desde usuario anterior',
            reviewedAt: new Date(),
            submittedAt: new Date(),
            updatedAt: new Date()
          });
        }

        await db.execute(sql`COMMIT`);

        const activeRoles = rolesQuery.rows.filter(r => r.status === 'active');
        const rejectedRoles = rolesQuery.rows.filter(r => r.status === 'rejected');
        const primaryRole = activeRoles[0]?.role;
        const userStatus = insertResult[0].status;

        // 8. Verificar si hay roles rechazados o solicitudes rechazadas después de la migración
        if (rejectedRoles.length > 0 || rejectedRequestsQuery.rows.length > 0) {
          const rejectedRole = rejectedRoles.length > 0 ? rejectedRoles[0].role : rejectedRequestsQuery.rows[0].role;
          
          // Actualizar metadatos de Clerk para rechazo
          try {
            await clerk.users.updateUserMetadata(userId, {
              publicMetadata: {
                role: rejectedRole,
                status: 'rejected',
                onboardingCompleted: true
              }
            });
          } catch (metadataError) {
            console.error('Error actualizando metadatos de rechazo:', metadataError);
          }

          return NextResponse.json({
            success: true,
            message: 'Usuario pre-existente migrado a Clerk - rechazado',
            forceRedirect: '/onboarding/rejected'
          });
        }

        // 9. Actualizar metadatos de Clerk si está activo
        if (userStatus === 'active' && primaryRole) {
          try {
            await clerk.users.updateUserMetadata(userId, {
              publicMetadata: {
                role: primaryRole,
                roles: activeRoles.map(r => r.role),
                status: userStatus,
                onboardingCompleted: true
              }
            });
          } catch (metadataError) {
            console.error('Error actualizando metadatos de Clerk:', metadataError);
          }
        }

        // Si tiene roles activos, retornar información pero NO redirigir
        if (userStatus === 'active' && primaryRole) {
          return NextResponse.json({
            success: true,
            message: 'Usuario pre-existente migrado a Clerk',
            user: insertResult[0],
            roles: activeRoles,
            primaryRole,
            metadataUpdated: true,
            shouldRedirect: true,
            redirectTo: `/dashboard/${primaryRole}`
          });
        }

        return NextResponse.json({
          success: true,
          message: 'Usuario pre-existente migrado a Clerk',
          user: insertResult[0],
          roles: activeRoles
        });

      } catch (transactionError) {
        await db.execute(sql`ROLLBACK`);
        throw transactionError;
      }
    }

    // Si no existe el usuario, crear uno nuevo
    console.log('🆕 API Sync: Usuario no existe, creando nuevo usuario');
    
    const insertResult = await db.insert(user).values({
      id: userId,
      name: clerkUser.fullName || '',
      email: userEmail,
      emailVerified: clerkUser.emailAddresses[0]?.verification?.status === 'verified',
      image: clerkUser.imageUrl || '',
      overallStatus: 'pending',
      firstName: clerkUser.firstName || '',
      lastName: clerkUser.lastName || '',
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning({ id: user.id, email: user.email, status: user.overallStatus });

    console.log('✅ API Sync: Nuevo usuario creado:', insertResult[0]);

    return NextResponse.json({
      success: true,
      message: 'Nuevo usuario creado exitosamente',
      user: insertResult[0],
      shouldRedirect: true,
      redirectTo: '/onboarding'
    });

  } catch (error) {
    console.error('❌ Error en API Sync:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
} 