'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { 
  Calendar,
  Mail,
  User,
  Settings,
  Activity,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw,
  Eye,
  EyeOff
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

interface AppointmentLog {
  id: string;
  eventType: string;
  eventCategory: string;
  title: string;
  description: string | null;
  metadata: any;
  triggeredByRole: string | null;
  emailType: string | null;
  emailRecipient: string | null;
  emailStatus: string | null;
  createdAt: string;
  triggeredByFirstName: string | null;
  triggeredByLastName: string | null;
  triggeredByEmail: string | null;
}

interface LogsData {
  appointmentId: string;
  logs: Appoint<PERSON><PERSON>og[];
  logsByCategory: Record<string, AppointmentLog[]>;
  stats: {
    totalEvents: number;
    emailsSent: number;
    emailsFailed: number;
    statusChanges: number;
    systemEvents: number;
    userActions: number;
  };
  timeline: Array<{
    id: string;
    timestamp: string;
    title: string;
    description: string | null;
    type: string;
    category: string;
    user: string;
    metadata: any;
  }>;
}

interface AppointmentLogsViewerProps {
  appointmentId: string;
}

export function AppointmentLogsViewer({ appointmentId }: AppointmentLogsViewerProps) {
  const [logsData, setLogsData] = useState<LogsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showMetadata, setShowMetadata] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  useEffect(() => {
    fetchLogs();
  }, [appointmentId]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/appointments/${appointmentId}/logs`);
      const result = await response.json();
      
      if (result.success) {
        setLogsData(result.data);
      }
    } catch (error) {
      console.error('Error fetching logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const getEventIcon = (eventType: string, eventCategory: string) => {
    switch (eventCategory) {
      case 'email':
        return <Mail className="h-4 w-4" />;
      case 'user_action':
        return <User className="h-4 w-4" />;
      case 'system':
        return <Settings className="h-4 w-4" />;
      case 'schedule':
        return <Calendar className="h-4 w-4" />;
      default:
        return <Activity className="h-4 w-4" />;
    }
  };

  const getEventColor = (eventType: string, emailStatus?: string | null) => {
    if (eventType === 'email_sent') {
      return emailStatus === 'sent' ? 'text-green-600' : 'text-red-600';
    }
    
    switch (eventType) {
      case 'created':
        return 'text-blue-600';
      case 'confirmed':
        return 'text-green-600';
      case 'cancelled':
        return 'text-red-600';
      case 'completed':
        return 'text-emerald-600';
      case 'status_changed':
        return 'text-orange-600';
      case 'precheckin_completed':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusBadge = (emailStatus: string | null) => {
    if (!emailStatus) return null;
    
    const variants = {
      sent: 'bg-green-100 text-green-800',
      failed: 'bg-red-100 text-red-800',
      delivered: 'bg-blue-100 text-blue-800',
      opened: 'bg-purple-100 text-purple-800'
    };
    
    return (
      <Badge className={variants[emailStatus as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>
        {emailStatus}
      </Badge>
    );
  };

  const filteredLogs = selectedCategory === 'all' 
    ? logsData?.logs || []
    : logsData?.logsByCategory[selectedCategory] || [];

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <RefreshCw className="h-6 w-6 animate-spin mr-2" />
          Cargando historial...
        </CardContent>
      </Card>
    );
  }

  if (!logsData) {
    return (
      <Card>
        <CardContent className="text-center py-8">
          <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
          <p className="text-gray-500">No se pudo cargar el historial de la cita</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Estadísticas */}
      <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{logsData.stats.totalEvents}</div>
            <div className="text-sm text-gray-500">Total Eventos</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{logsData.stats.emailsSent}</div>
            <div className="text-sm text-gray-500">Emails Enviados</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{logsData.stats.emailsFailed}</div>
            <div className="text-sm text-gray-500">Emails Fallidos</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{logsData.stats.statusChanges}</div>
            <div className="text-sm text-gray-500">Cambios Estado</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{logsData.stats.systemEvents}</div>
            <div className="text-sm text-gray-500">Eventos Sistema</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-indigo-600">{logsData.stats.userActions}</div>
            <div className="text-sm text-gray-500">Acciones Usuario</div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Historial de Eventos</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowMetadata(!showMetadata)}
              >
                {showMetadata ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                {showMetadata ? 'Ocultar' : 'Mostrar'} Detalles
              </Button>
              <Button variant="outline" size="sm" onClick={fetchLogs}>
                <RefreshCw className="h-4 w-4" />
                Actualizar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Selector de categoría */}
          <div className="flex flex-wrap gap-2 mb-4">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              Todos ({logsData.stats.totalEvents})
            </Button>
            {Object.entries(logsData.logsByCategory).map(([category, logs]) => (
              <Button
                key={category}
                variant={selectedCategory === category ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(category)}
              >
                {category} ({logs.length})
              </Button>
            ))}
          </div>

          <Separator className="mb-4" />

          {/* Timeline de eventos */}
          <div className="space-y-4">
            {filteredLogs.map((log, index) => (
              <div key={log.id} className="flex items-start space-x-3">
                {/* Icono y línea temporal */}
                <div className="flex flex-col items-center">
                  <div className={`p-2 rounded-full bg-gray-100 ${getEventColor(log.eventType, log.emailStatus)}`}>
                    {getEventIcon(log.eventType, log.eventCategory)}
                  </div>
                  {index < filteredLogs.length - 1 && (
                    <div className="w-px h-8 bg-gray-200 mt-2" />
                  )}
                </div>

                {/* Contenido del evento */}
                <div className="flex-1 min-w-0">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{log.title}</h4>
                      <div className="flex items-center space-x-2">
                        {log.emailStatus && getStatusBadge(log.emailStatus)}
                        <Badge variant="outline" className="text-xs">
                          {log.eventCategory}
                        </Badge>
                      </div>
                    </div>
                    
                    {log.description && (
                      <p className="text-sm text-gray-600 mb-2">{log.description}</p>
                    )}
                    
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <div className="flex items-center space-x-4">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {format(new Date(log.createdAt), 'dd/MM/yyyy HH:mm:ss', { locale: es })}
                        </span>
                        {(log.triggeredByFirstName || log.triggeredByRole) && (
                          <span className="flex items-center">
                            <User className="h-3 w-3 mr-1" />
                            {log.triggeredByFirstName && log.triggeredByLastName
                              ? `${log.triggeredByFirstName} ${log.triggeredByLastName}`
                              : log.triggeredByRole || 'Sistema'
                            }
                          </span>
                        )}
                        {log.emailRecipient && (
                          <span className="flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {log.emailRecipient}
                          </span>
                        )}
                      </div>
                    </div>

                    {/* Metadata expandible */}
                    {showMetadata && log.metadata && (
                      <div className="mt-3 p-2 bg-gray-100 rounded text-xs">
                        <details>
                          <summary className="cursor-pointer font-medium">Metadatos</summary>
                          <pre className="mt-2 whitespace-pre-wrap">
                            {JSON.stringify(log.metadata, null, 2)}
                          </pre>
                        </details>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredLogs.length === 0 && (
            <div className="text-center py-8">
              <AlertCircle className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">No hay eventos en esta categoría</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}