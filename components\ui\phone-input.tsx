'use client';

import { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { 
  formatPhoneAsYouType, 
  validatePhone, 
  getPhonePlaceholder,
  formatPhoneForStorage 
} from '@/lib/phone-utils';

interface PhoneInputProps {
  label?: string;
  value?: string;
  onChange?: (value: string) => void;
  onBlur?: () => void;
  placeholder?: string;
  required?: boolean;
  disabled?: boolean;
  className?: string;
  error?: string;
  id?: string;
}

export function PhoneInput({
  label,
  value = "",
  onChange,
  onBlur,
  placeholder,
  required = false,
  disabled = false,
  className = "",
  error: externalError,
  id = "phone",
  ...props
}: PhoneInputProps) {
  const [displayValue, setDisplayValue] = useState("");
  const [internalError, setInternalError] = useState("");

  // Sincronizar con valor externo
  useEffect(() => {
    if (value) {
      // Mostrar valor formateado
      const formatted = formatPhoneAsYouType(value.replace(/\D/g, ''));
      setDisplayValue(formatted);
    } else {
      setDisplayValue("");
    }
  }, [value]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    
    // Solo permitir dígitos y algunos caracteres especiales
    const sanitized = inputValue.replace(/[^\d\-\s]/g, '');
    
    // Formatear mientras escribe
    const formatted = formatPhoneAsYouType(sanitized);
    setDisplayValue(formatted);
    
    // Limpiar error previo
    setInternalError("");
    
    // Notificar cambio con solo los dígitos
    const digitsOnly = sanitized.replace(/\D/g, '');
    onChange?.(digitsOnly);
  };

  const handleBlur = () => {
    // Validar al perder foco
    if (displayValue) {
      const validation = validatePhone(displayValue);
      setInternalError(validation.isValid ? "" : validation.error || "");
      
      // Si es válido, formatear para storage y notificar
      if (validation.isValid) {
        const storageFormat = formatPhoneForStorage(displayValue);
        onChange?.(storageFormat);
      }
    }
    
    onBlur?.();
  };

  const finalError = externalError || internalError;
  const finalPlaceholder = placeholder || getPhonePlaceholder();

  return (
    <div className="space-y-2">
      {label && (
        <Label htmlFor={id} className="text-sm font-medium text-gray-700">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </Label>
      )}
      
      <div className="relative">
        <Input
          id={id}
          type="tel"
          value={displayValue}
          onChange={handleChange}
          onBlur={handleBlur}
          placeholder={finalPlaceholder}
          disabled={disabled}
          required={required}
          className={cn(
            "text-base font-mono tracking-wide",
            finalError && "border-red-500 focus:border-red-500 focus:ring-red-500",
            className
          )}
          {...props}
        />
        
        {/* Indicador de país */}
        <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
          <span className="text-xs text-gray-400 font-medium bg-gray-50 px-2 py-1 rounded">
            🇬🇹
          </span>
        </div>
      </div>
      
      {finalError && (
        <p className="text-sm text-red-600 flex items-center gap-1">
          <span className="text-red-500">⚠️</span>
          {finalError}
        </p>
      )}
      
    </div>
  );
}