'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Phone, 
  AlertTriangle, 
  Pills, 
  FileText,
  Loader2
} from 'lucide-react';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';
import { cn } from '@/lib/utils';

interface PreCheckinFormProps {
  appointmentData: {
    id: string;
    patientFirstName: string | null;
    patientLastName: string | null;
    patientEmail: string | null;
    isDependent: boolean;
    isFirstVisit: boolean;
    scheduledDate: string;
    guardianInfo?: {
      guardianFirstName: string | null;
      guardianLastName: string | null;
      guardianEmail: string | null;
      relationship: string;
    } | null;
  };
  isCompleted: boolean;
  showUserContext?: boolean;
  dashboardLink?: string;
  mode?: 'complete' | 'express' | 'presential';
}

interface PreCheckinData {
  // Confirmación de asistencia
  willAttend: 'yes' | 'no' | '';
  
  // Síntomas actuales
  hasSymptoms: boolean;
  symptoms: string;
  
  // Medicamentos
  takingMedications: boolean;
  medications: string;
  
  // Alergias
  hasAllergies: boolean;
  allergies: string;
  
  // Información de contacto actualizada
  phone: string;
  emergencyContact: string;
  emergencyPhone: string;
  
  // Para dependientes - quién acompañará
  companionName: string;
  companionRelationship: string;
  companionPhone: string;
  
  // Motivo de consulta actualizado
  chiefComplaint: string;
  
  // Observaciones adicionales
  additionalNotes: string;
}

export function PreCheckinForm({ 
  appointmentData, 
  isCompleted, 
  showUserContext = false,
  dashboardLink,
  mode = 'complete'
}: PreCheckinFormProps) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<PreCheckinData>({
    willAttend: '',
    hasSymptoms: false,
    symptoms: '',
    takingMedications: false,
    medications: '',
    hasAllergies: false,
    allergies: '',
    phone: '',
    emergencyContact: '',
    emergencyPhone: '',
    companionName: '',
    companionRelationship: '',
    companionPhone: '',
    chiefComplaint: '',
    additionalNotes: '',
  });

  const updateFormData = (field: keyof PreCheckinData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Determinar qué campos mostrar según el modo y tipo de visita
  const shouldShowField = (field: string) => {
    // Campos que siempre se muestran
    const alwaysShow = ['willAttend', 'chiefComplaint'];
    if (alwaysShow.includes(field)) return true;

    // En modo presencial (tablet), solo campos esenciales
    if (mode === 'presential') {
      return ['symptoms', 'medications', 'allergies'].includes(field);
    }

    // En modo express, campos básicos médicos
    if (mode === 'express') {
      return ['symptoms', 'medications', 'allergies', 'contactInfo'].includes(field);
    }

    // En modo complete, mostrar según si es primera visita
    if (mode === 'complete') {
      // Primera visita: todo
      if (appointmentData.isFirstVisit) {
        return true;
      }
      // Reconsulta: enfocar en cambios
      const reconsultaFields = ['symptoms', 'medications', 'allergies', 'contactInfo', 'additionalNotes'];
      return reconsultaFields.includes(field);
    }

    return true;
  };

  // Determinar el tiempo límite según el modo
  const getTimeLimit = () => {
    switch (mode) {
      case 'presential': return 5; // 5 minutos
      case 'express': return 10; // 10 minutos
      default: return null; // Sin límite
    }
  };

  // Textos dinámicos según el contexto
  const getFormTitle = () => {
    if (mode === 'express') return 'Pre-checkin Express';
    if (mode === 'presential') return 'Check-in Rápido';
    if (appointmentData.isFirstVisit) return 'Pre-checkin - Primera Visita';
    return 'Pre-checkin - Reconsulta';
  };

  const getFormDescription = () => {
    if (mode === 'express') return 'Complete la información básica antes de su cita.';
    if (mode === 'presential') return 'Complete esta información rápida antes de pasar con el doctor.';
    if (appointmentData.isFirstVisit) return 'Ayúdanos a conocerte mejor completando esta información médica.';
    return 'Actualice cualquier cambio desde su última visita.';
  };

  const handleSubmit = async () => {
    // Validaciones básicas
    if (!formData.willAttend) {
      toast.error('Por favor confirma si asistirás a la cita');
      return;
    }

    if (formData.willAttend === 'no' && !formData.additionalNotes) {
      toast.error('Por favor explica por qué no podrás asistir');
      return;
    }

    if (appointmentData.isDependent && formData.willAttend === 'yes') {
      if (!formData.companionName) {
        toast.error('Por favor indica quién acompañará al paciente');
        return;
      }
    }

    setIsSubmitting(true);

    try {
      const response = await fetch('/api/pre-checkin/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId: appointmentData.id,
          formData,
          isDependent: appointmentData.isDependent,
        }),
      });

      const result = await response.json();

      if (response.ok && result.success) {
        toast.success('Pre-checkin completado exitosamente');
        
        // Redirigir según el contexto del usuario
        if (showUserContext && dashboardLink) {
          // Usuario autenticado - ir al dashboard
          setTimeout(() => {
            router.push(dashboardLink);
          }, 1500); // Esperar un poco para que el usuario vea el mensaje de éxito
        } else {
          // Usuario no autenticado - ir a página de confirmación
          router.push(`/pre-checkin/confirmation?appointment=${appointmentData.id}`);
        }
      } else {
        toast.error(result.error || 'Error al enviar pre-checkin');
      }
    } catch (error) {
      console.error('Error submitting pre-checkin:', error);
      toast.error('Error de conexión. Intenta nuevamente.');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isCompleted) {
    return (
      <Card className="border-green-200 bg-green-50">
        <CardContent className="pt-6">
          <div className="text-center">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-green-800 mb-2">
              Pre-checkin Completado
            </h3>
            <p className="text-green-700">
              Gracias por completar el pre-checkin. Te esperamos en tu cita.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Agregar indicador de tiempo si hay límite
  const timeLimit = getTimeLimit();
  
  return (
    <div className="space-y-6">
      {/* Header con información del formulario */}
      <Card className={cn(
        mode === 'express' && "border-orange-200 bg-orange-50",
        mode === 'presential' && "border-blue-200 bg-blue-50"
      )}>
        <CardContent className="pt-4">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">{getFormTitle()}</h2>
            <p className="text-gray-600 mb-2">{getFormDescription()}</p>
            {timeLimit && (
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-white rounded-full border text-sm">
                <AlertTriangle className="h-4 w-4 text-orange-500" />
                <span>Tiempo estimado: {timeLimit} minutos</span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Confirmación de asistencia */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <span>Confirmación de Asistencia</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label className="text-base font-medium">
              {appointmentData.isDependent 
                ? `¿${appointmentData.patientFirstName} asistirá a la cita?`
                : '¿Asistirás a tu cita médica?'
              }
            </Label>
            <RadioGroup 
              value={formData.willAttend} 
              onValueChange={(value) => updateFormData('willAttend', value)}
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="yes" id="attend-yes" />
                <Label htmlFor="attend-yes" className="cursor-pointer">
                  ✅ Sí, {appointmentData.isDependent ? 'asistirá' : 'asistiré'} a la cita
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="no" id="attend-no" />
                <Label htmlFor="attend-no" className="cursor-pointer">
                  ❌ No, {appointmentData.isDependent ? 'no podrá asistir' : 'no podré asistir'}
                </Label>
              </div>
            </RadioGroup>
          </div>
        </CardContent>
      </Card>

      {/* Información de acompañante (solo para dependientes) */}
      {appointmentData.isDependent && formData.willAttend === 'yes' && shouldShowField('companionInfo') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <User className="h-5 w-5 text-blue-600" />
              <span>Información del Acompañante</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="companionName">Nombre del acompañante *</Label>
                <Input
                  id="companionName"
                  value={formData.companionName}
                  onChange={(e) => updateFormData('companionName', e.target.value)}
                  placeholder="Nombre completo"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="companionRelationship">Relación con el paciente</Label>
                <Input
                  id="companionRelationship"
                  value={formData.companionRelationship}
                  onChange={(e) => updateFormData('companionRelationship', e.target.value)}
                  placeholder="Madre, padre, hermano..."
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="companionPhone">Teléfono del acompañante</Label>
              <Input
                id="companionPhone"
                value={formData.companionPhone}
                onChange={(e) => updateFormData('companionPhone', e.target.value)}
                placeholder="+502 1234-5678"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Información médica actual (solo si va a asistir) */}
      {formData.willAttend === 'yes' && shouldShowField('symptoms') && (
        <>
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <span>Estado de Salud Actual</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Síntomas */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasSymptoms"
                    checked={formData.hasSymptoms}
                    onCheckedChange={(checked) => updateFormData('hasSymptoms', checked)}
                  />
                  <Label htmlFor="hasSymptoms" className="cursor-pointer">
                    {appointmentData.isDependent 
                      ? `${appointmentData.patientFirstName} presenta síntomas actualmente`
                      : 'Tengo síntomas actualmente'
                    }
                  </Label>
                </div>
                {formData.hasSymptoms && (
                  <Textarea
                    value={formData.symptoms}
                    onChange={(e) => updateFormData('symptoms', e.target.value)}
                    placeholder="Describe los síntomas: fiebre, dolor, malestar..."
                    className="mt-2"
                  />
                )}
              </div>

              <Separator />

              {/* Medicamentos */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="takingMedications"
                    checked={formData.takingMedications}
                    onCheckedChange={(checked) => updateFormData('takingMedications', checked)}
                  />
                  <Label htmlFor="takingMedications" className="cursor-pointer">
                    {appointmentData.isDependent 
                      ? `${appointmentData.patientFirstName} está tomando medicamentos`
                      : 'Estoy tomando medicamentos actualmente'
                    }
                  </Label>
                </div>
                {formData.takingMedications && (
                  <Textarea
                    value={formData.medications}
                    onChange={(e) => updateFormData('medications', e.target.value)}
                    placeholder="Lista todos los medicamentos y dosis: Paracetamol 500mg cada 8h..."
                    className="mt-2"
                  />
                )}
              </div>

              <Separator />

              {/* Alergias */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="hasAllergies"
                    checked={formData.hasAllergies}
                    onCheckedChange={(checked) => updateFormData('hasAllergies', checked)}
                  />
                  <Label htmlFor="hasAllergies" className="cursor-pointer">
                    {appointmentData.isDependent 
                      ? `${appointmentData.patientFirstName} tiene alergias conocidas`
                      : 'Tengo alergias conocidas'
                    }
                  </Label>
                </div>
                {formData.hasAllergies && (
                  <Textarea
                    value={formData.allergies}
                    onChange={(e) => updateFormData('allergies', e.target.value)}
                    placeholder="Describe las alergias: medicamentos, alimentos, sustancias..."
                    className="mt-2"
                  />
                )}
              </div>
            </CardContent>
          </Card>

          {/* Información de contacto */}
          {shouldShowField('contactInfo') && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <Phone className="h-5 w-5 text-green-600" />
                  <span>Información de Contacto</span>
                </CardTitle>
              </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Teléfono principal</Label>
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                    placeholder="+502 1234-5678"
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="emergencyContact">Contacto de emergencia</Label>
                  <Input
                    id="emergencyContact"
                    value={formData.emergencyContact}
                    onChange={(e) => updateFormData('emergencyContact', e.target.value)}
                    placeholder="Nombre del contacto"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="emergencyPhone">Teléfono de emergencia</Label>
                <Input
                  id="emergencyPhone"
                  value={formData.emergencyPhone}
                  onChange={(e) => updateFormData('emergencyPhone', e.target.value)}
                  placeholder="+502 8765-4321"
                />
              </div>
            </CardContent>
          </Card>
          )}

          {/* Motivo de consulta */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5 text-purple-600" />
                <span>Motivo de Consulta</span>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="chiefComplaint">
                  {appointmentData.isDependent 
                    ? `¿Por qué llevas a ${appointmentData.patientFirstName} al médico?`
                    : '¿Cuál es el motivo principal de tu consulta?'
                  }
                </Label>
                <Textarea
                  id="chiefComplaint"
                  value={formData.chiefComplaint}
                  onChange={(e) => updateFormData('chiefComplaint', e.target.value)}
                  placeholder="Describe brevemente el motivo de la consulta médica..."
                />
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {/* Motivo de cancelación (si no va a asistir) */}
      {formData.willAttend === 'no' && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-red-700">
              <XCircle className="h-5 w-5" />
              <span>Motivo de la Inasistencia</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="additionalNotes">
                Por favor explica por qué no podrás asistir *
              </Label>
              <Textarea
                id="additionalNotes"
                value={formData.additionalNotes}
                onChange={(e) => updateFormData('additionalNotes', e.target.value)}
                placeholder="Explica el motivo de la cancelación..."
                className="border-red-300 focus:border-red-500"
              />
            </div>
          </CardContent>
        </Card>
      )}

      {/* Observaciones adicionales */}
      {shouldShowField('additionalNotes') && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-600" />
              <span>Observaciones Adicionales</span>
            </CardTitle>
          </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Label htmlFor="notes">¿Algo más que el doctor deba saber?</Label>
            <Textarea
              id="notes"
              value={formData.additionalNotes}
              onChange={(e) => updateFormData('additionalNotes', e.target.value)}
              placeholder="Información adicional que consideres importante..."
            />
          </div>
        </CardContent>
      </Card>
      )}

      {/* Botón de envío */}
      <div className="flex justify-center pt-6">
        <Button
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.willAttend}
          size="lg"
          className="w-full md:w-auto px-8"
        >
          {isSubmitting ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Enviando...
            </>
          ) : (
            <>
              <CheckCircle className="h-4 w-4 mr-2" />
              Completar Pre-checkin
            </>
          )}
        </Button>
      </div>
    </div>
  );
}