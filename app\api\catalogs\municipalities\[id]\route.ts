import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { municipalities, departments } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener municipalidad por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const municipality = await db
      .select({
        id: municipalities.id,
        name: municipalities.name,
        departmentId: municipalities.departmentId,
        departmentName: departments.name,
        isActive: municipalities.isActive,
        createdAt: municipalities.createdAt,
        updatedAt: municipalities.updatedAt
      })
      .from(municipalities)
      .leftJoin(departments, eq(municipalities.departmentId, departments.id))
      .where(eq(municipalities.id, id))
      .limit(1);

    if (municipality.length === 0) {
      return NextResponse.json({ error: 'Municipalidad no encontrada' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: municipality[0] 
    });

  } catch (error) {
    console.error('Error fetching municipality:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar municipalidad
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar municipalidades.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, departmentId, isActive } = body;

    // Validar datos requeridos
    if (!name || !departmentId) {
      return NextResponse.json({ 
        error: 'Nombre y departamento son requeridos' 
      }, { status: 400 });
    }

    // Verificar que la municipalidad existe
    const existingMunicipality = await db
      .select()
      .from(municipalities)
      .where(eq(municipalities.id, id))
      .limit(1);

    if (existingMunicipality.length === 0) {
      return NextResponse.json({ error: 'Municipalidad no encontrada' }, { status: 404 });
    }

    // Verificar que el departamento existe
    const department = await db
      .select()
      .from(departments)
      .where(eq(departments.id, departmentId))
      .limit(1);

    if (department.length === 0) {
      return NextResponse.json({ error: 'Departamento no encontrado' }, { status: 404 });
    }

    // Actualizar municipalidad
    const [updatedMunicipality] = await db
      .update(municipalities)
      .set({
        name,
        departmentId: parseInt(departmentId),
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(municipalities.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedMunicipality,
      message: 'Municipalidad actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error updating municipality:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar municipalidad
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar municipalidades.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que la municipalidad existe
    const existingMunicipality = await db
      .select()
      .from(municipalities)
      .where(eq(municipalities.id, id))
      .limit(1);

    if (existingMunicipality.length === 0) {
      return NextResponse.json({ error: 'Municipalidad no encontrada' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(municipalities)
        .where(eq(municipalities.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Municipalidad eliminada físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedMunicipality] = await db
        .update(municipalities)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(municipalities.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedMunicipality,
        message: 'Municipalidad desactivada exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting municipality:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}