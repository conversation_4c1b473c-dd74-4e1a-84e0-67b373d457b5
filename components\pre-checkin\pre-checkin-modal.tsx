'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Checkbox } from '@/components/ui/checkbox';
import { Separator } from '@/components/ui/separator';
import { FileUpload } from '@/components/ui/file-upload';
import { 
  CheckCircle, 
  XCircle, 
  User, 
  Phone, 
  AlertTriangle, 
  Pill, 
  FileText,
  Loader2,
  Heart,
  Shield,
  Upload,
  X,
  ChevronRight,
  ChevronLeft
} from 'lucide-react';
import { toast } from 'sonner';

interface PreCheckinModalProps {
  isOpen: boolean;
  onClose: () => void;
  appointmentId?: string;
  onSuccess?: () => void;
}

interface DocumentFile {
  type: 'lab_results' | 'imaging' | 'prescription' | 'insurance' | 'other';
  url: string;
  filename: string;
  description?: string;
  uploadedAt: string;
}

interface PreCheckinData {
  // Confirmación de asistencia
  willAttend: 'yes' | 'no' | '';
  
  // Información médica
  hasSymptoms: boolean;
  symptoms: string;
  takingMedications: boolean;
  medications: string;
  hasAllergies: boolean;
  allergies: string;
  
  // Motivo de consulta
  chiefComplaint: string;
  
  // Observaciones adicionales
  additionalNotes: string;
  
  // Documentos adjuntos
  documents: DocumentFile[];
}

type WizardStep = 'attendance' | 'symptoms' | 'medications' | 'allergies' | 'documents' | 'review';

const DOCUMENT_TYPES = {
  lab_results: 'Exámenes de Laboratorio',
  imaging: 'Estudios de Imagen',
  prescription: 'Recetas Médicas',
  insurance: 'Documentos de Seguro',
  other: 'Otros Documentos'
} as const;

export default function PreCheckinModal({ 
  isOpen, 
  onClose, 
  appointmentId,
  onSuccess 
}: PreCheckinModalProps) {
  const { user } = useUser();
  const [currentStep, setCurrentStep] = useState<WizardStep>('attendance');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<PreCheckinData>({
    willAttend: '',
    hasSymptoms: false,
    symptoms: '',
    takingMedications: false,
    medications: '',
    hasAllergies: false,
    allergies: '',
    additionalNotes: '',
    documents: []
  });

  // Reset form when modal opens
  useEffect(() => {
    if (isOpen && appointmentId) {
      // Load existing pre-checkin data if exists
      loadExistingData();
    }
  }, [isOpen, appointmentId]);

  const loadExistingData = async () => {
    if (!appointmentId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/pre-checkin/submit?appointmentId=${appointmentId}`);
      const result = await response.json();
      
      if (response.ok && result.data?.preCheckinData) {
        const existingData = result.data.preCheckinData;
        setFormData(prev => ({
          ...prev,
          ...existingData,
          documents: existingData.documents || []
        }));
      }
    } catch (error) {
      console.error('Error loading pre-checkin data:', error);
    } finally {
      setLoading(false);
    }
  };


  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }));
  };

  const updateDocumentType = (index: number, type: DocumentFile['type']) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.map((doc, i) => 
        i === index ? { ...doc, type } : doc
      )
    }));
  };

  const updateDocumentDescription = (index: number, description: string) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.map((doc, i) => 
        i === index ? { ...doc, description } : doc
      )
    }));
  };

  const handleSubmit = async () => {
    if (!appointmentId) {
      toast.error('ID de cita no encontrado');
      return;
    }

    // Validate required fields
    if (formData.willAttend === '') {
      toast.error('Por favor confirma tu asistencia');
      return;
    }

    if (formData.willAttend === 'no') {
      // If not attending, we should probably cancel the appointment
      toast.error('Si no puedes asistir, por favor cancela tu cita desde el menú principal');
      return;
    }

    setIsSubmitting(true);
    try {
      const response = await fetch('/api/pre-checkin/submit', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          appointmentId,
          formData: {
            ...formData,
            completedAt: new Date().toISOString(),
            completedBy: user?.id
          },
          isDependent: false // TODO: Determinar si es dependiente basado en la edad u otros factores
        }),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success('Pre-checkin completado exitosamente');
        onSuccess?.();
      } else {
        toast.error(result.error || 'Error al guardar pre-checkin');
      }
    } catch (error) {
      console.error('Error submitting pre-checkin:', error);
      toast.error('Error de conexión');
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    const steps: WizardStep[] = ['attendance', 'symptoms', 'medications', 'allergies', 'documents', 'review'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
    }
  };

  const prevStep = () => {
    const steps: WizardStep[] = ['attendance', 'symptoms', 'medications', 'allergies', 'documents', 'review'];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
    }
  };

  const renderStep = () => {
    switch (currentStep) {
      case 'attendance':
        return (
          <div className="space-y-4">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Confirmación de Asistencia</h2>
              <p className="text-gray-600 text-sm">¿Podrás asistir a tu cita médica?</p>
            </div>
            
            <div className="space-y-3">
              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, willAttend: 'yes' }));
                  setTimeout(() => setCurrentStep('symptoms'), 300);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.willAttend === 'yes'
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-green-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.willAttend === 'yes' ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }`}>
                      {formData.willAttend === 'yes' && (
                        <CheckCircle className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">✅ Sí, asistiré a mi cita</p>
                      <p className="text-sm opacity-75">Confirmo que podré asistir</p>
                    </div>
                  </div>
                </div>
              </button>

              <button
                type="button"
                onClick={() => setFormData(prev => ({ ...prev, willAttend: 'no' }))}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.willAttend === 'no'
                    ? 'border-red-500 bg-red-50 text-red-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-red-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.willAttend === 'no' ? 'border-red-500 bg-red-500' : 'border-gray-300'
                    }`}>
                      {formData.willAttend === 'no' && (
                        <X className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">❌ No podré asistir</p>
                      <p className="text-sm opacity-75">Necesito cancelar mi cita</p>
                    </div>
                  </div>
                </div>
              </button>
            </div>
          </div>
        );

      case 'symptoms':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Heart className="h-12 w-12 text-red-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Motivo de Consulta y Síntomas</h2>
              <p className="text-gray-600 text-sm">Cuéntanos por qué vienes y si tienes síntomas</p>
            </div>
            
            {/* Motivo de consulta - Campo obligatorio */}
            <div className="space-y-2">
              <Label htmlFor="chiefComplaint" className="text-base font-medium">
                ¿Cuál es el motivo de tu consulta? <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="chiefComplaint"
                value={formData.chiefComplaint}
                onChange={(e) => setFormData(prev => ({ ...prev, chiefComplaint: e.target.value }))}
                placeholder="Ej: Vengo por control rutinario, dolor de estómago desde hace 3 días, seguimiento de tratamiento..."
                rows={3}
                className="resize-none"
                required
              />
              <p className="text-xs text-gray-500">Describe brevemente por qué necesitas esta consulta médica</p>
            </div>
            
            <Separator className="my-4" />
            
            <div className="space-y-3">
              <Label className="text-base font-medium">¿Tienes síntomas actuales?</Label>
              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, hasSymptoms: false, symptoms: '' }));
                  if (formData.chiefComplaint) {
                    setTimeout(() => setCurrentStep('medications'), 300);
                  }
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.hasSymptoms === false
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-green-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.hasSymptoms === false ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }`}>
                      {formData.hasSymptoms === false && (
                        <CheckCircle className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">✅ No tengo síntomas</p>
                      <p className="text-sm opacity-75">Me siento bien</p>
                    </div>
                  </div>
                </div>
              </button>

              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, hasSymptoms: true }));
                  // Focus en el textarea después de que se renderice
                  setTimeout(() => {
                    const textarea = document.getElementById('symptoms');
                    if (textarea) {
                      textarea.focus();
                    }
                  }, 100);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.hasSymptoms === true
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-orange-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.hasSymptoms === true ? 'border-orange-500 bg-orange-500' : 'border-gray-300'
                    }`}>
                      {formData.hasSymptoms === true && (
                        <Heart className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">⚠️ Sí tengo síntomas</p>
                      <p className="text-sm opacity-75">Necesito reportar molestias</p>
                    </div>
                  </div>
                </div>
              </button>
            </div>
            
            {formData.hasSymptoms && (
              <div className="space-y-2">
                <Label htmlFor="symptoms" className="text-base font-medium">Describe tus síntomas</Label>
                <Textarea
                  id="symptoms"
                  value={formData.symptoms}
                  onChange={(e) => setFormData(prev => ({ ...prev, symptoms: e.target.value }))}
                  placeholder="Ej: Dolor de cabeza desde ayer, fiebre leve, tos con flema..."
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">Incluye cuándo comenzaron y su intensidad</p>
              </div>
            )}
            
            {!formData.hasSymptoms && formData.hasSymptoms !== null && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <p className="text-sm">Perfecto, sin síntomas para reportar</p>
              </div>
            )}
          </div>
        );

      case 'medications':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Pill className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Medicamentos Actuales</h2>
              <p className="text-gray-600 text-sm">¿Tomas algún medicamento actualmente?</p>
            </div>
            
            <div className="space-y-3">
              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, takingMedications: false, medications: '' }));
                  setTimeout(() => setCurrentStep('allergies'), 300);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.takingMedications === false
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-green-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.takingMedications === false ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }`}>
                      {formData.takingMedications === false && (
                        <CheckCircle className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">✅ No tomo medicamentos</p>
                      <p className="text-sm opacity-75">Sin medicamentos actuales</p>
                    </div>
                  </div>
                </div>
              </button>

              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, takingMedications: true }));
                  // Focus en el textarea después de que se renderice
                  setTimeout(() => {
                    const textarea = document.getElementById('medications');
                    if (textarea) {
                      textarea.focus();
                    }
                  }, 100);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.takingMedications === true
                    ? 'border-blue-500 bg-blue-50 text-blue-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-blue-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.takingMedications === true ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                    }`}>
                      {formData.takingMedications === true && (
                        <Pill className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">💊 Sí tomo medicamentos</p>
                      <p className="text-sm opacity-75">Incluye vitaminas y suplementos</p>
                    </div>
                  </div>
                </div>
              </button>
            </div>
            
            {formData.takingMedications && (
              <div className="space-y-2">
                <Label htmlFor="medications" className="text-base font-medium">Lista de medicamentos</Label>
                <Textarea
                  id="medications"
                  value={formData.medications}
                  onChange={(e) => setFormData(prev => ({ ...prev, medications: e.target.value }))}
                  placeholder="Ej: Losartán 50mg (1 por día), Aspirina 100mg (1 por día)..."
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">Incluye nombre, dosis y frecuencia</p>
              </div>
            )}
            
            {!formData.takingMedications && formData.takingMedications !== null && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <p className="text-sm">Perfecto, sin medicamentos actuales</p>
              </div>
            )}
          </div>
        );

      case 'allergies':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <AlertTriangle className="h-12 w-12 text-orange-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Alergias Médicas</h2>
              <p className="text-gray-600 text-sm">¿Tienes alguna alergia médica conocida?</p>
            </div>
            
            <div className="space-y-3">
              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, hasAllergies: false, allergies: '' }));
                  setTimeout(() => setCurrentStep('documents'), 300);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.hasAllergies === false
                    ? 'border-green-500 bg-green-50 text-green-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-green-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.hasAllergies === false ? 'border-green-500 bg-green-500' : 'border-gray-300'
                    }`}>
                      {formData.hasAllergies === false && (
                        <CheckCircle className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">✅ No tengo alergias</p>
                      <p className="text-sm opacity-75">Sin alergias conocidas</p>
                    </div>
                  </div>
                </div>
              </button>

              <button
                type="button"
                onClick={() => {
                  setFormData(prev => ({ ...prev, hasAllergies: true }));
                  // Focus en el textarea después de que se renderice
                  setTimeout(() => {
                    const textarea = document.getElementById('allergies');
                    if (textarea) {
                      textarea.focus();
                    }
                  }, 100);
                }}
                className={`w-full p-4 rounded-lg border-2 transition-all duration-200 ${
                  formData.hasAllergies === true
                    ? 'border-orange-500 bg-orange-50 text-orange-700'
                    : 'border-gray-200 bg-white text-gray-700 hover:border-orange-300'
                }`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-6 h-6 rounded-full border-2 flex items-center justify-center ${
                      formData.hasAllergies === true ? 'border-orange-500 bg-orange-500' : 'border-gray-300'
                    }`}>
                      {formData.hasAllergies === true && (
                        <AlertTriangle className="w-4 h-4 text-white" />
                      )}
                    </div>
                    <div className="text-left">
                      <p className="font-medium">⚠️ Sí tengo alergias</p>
                      <p className="text-sm opacity-75">Medicamentos, alimentos, etc.</p>
                    </div>
                  </div>
                </div>
              </button>
            </div>
            
            {formData.hasAllergies && (
              <div className="space-y-2">
                <Label htmlFor="allergies" className="text-base font-medium">Describe tus alergias</Label>
                <Textarea
                  id="allergies"
                  value={formData.allergies}
                  onChange={(e) => setFormData(prev => ({ ...prev, allergies: e.target.value }))}
                  placeholder="Ej: Alérgico a la penicilina (sarpullido), mariscos (hinchazón)..."
                  rows={4}
                  className="resize-none"
                />
                <p className="text-xs text-gray-500">Incluye la reacción que tienes</p>
              </div>
            )}
            
            {!formData.hasAllergies && formData.hasAllergies !== null && (
              <div className="text-center py-8 text-gray-500">
                <CheckCircle className="h-8 w-8 mx-auto mb-2 text-green-500" />
                <p className="text-sm">Excelente, sin alergias conocidas</p>
              </div>
            )}
          </div>
        );

      case 'documents':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Upload className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Documentos Médicos</h2>
              <p className="text-gray-600 text-sm">Adjunta exámenes, recetas o documentos relevantes</p>
            </div>
            
            <div className="space-y-4">
              {formData.documents.length < 3 ? (
                <div className="bg-purple-50 rounded-lg border border-purple-200 p-4">
                  <FileUpload
                    preset="medical_documents"
                    onUploadComplete={(url, fileInfo) => {
                      // Verificar límite antes de agregar
                      if (formData.documents.length >= 3) {
                        toast.error('Máximo 3 documentos permitidos');
                        return;
                      }
                      
                      const document: DocumentFile = {
                        type: 'other',
                        url: url,
                        filename: fileInfo?.original_filename || 'Documento',
                        description: '',
                        uploadedAt: new Date().toISOString()
                      };
                      setFormData(prev => ({
                        ...prev,
                        documents: [...prev.documents, document]
                      }));
                      toast.success('Documento subido exitosamente');
                    }}
                    onUploadError={(error) => {
                      toast.error(`Error subiendo documento: ${error}`);
                    }}
                    accept="image/*,.pdf,.doc,.docx"
                    maxSize={10} // 10MB
                    label="Subir documento médico"
                  />
                  <p className="text-xs text-purple-600 mt-2 text-center">
                    Formatos: PDF, imágenes, Word • Máximo 10MB por archivo • {3 - formData.documents.length} documento(s) restante(s)
                  </p>
                </div>
              ) : (
                <div className="bg-gray-50 rounded-lg border border-gray-200 p-4 text-center">
                  <p className="text-sm text-gray-600">✅ Máximo de 3 documentos alcanzado</p>
                  <p className="text-xs text-gray-500 mt-1">Elimina un documento si necesitas subir otro</p>
                </div>
              )}
              
              {formData.documents.length > 0 ? (
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">📎 Documentos ({formData.documents.length}/3):</Label>
                  <div className="space-y-2">
                    {formData.documents.map((doc, index) => (
                      <div key={index} className="bg-white border rounded-lg p-3">
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex-1 min-w-0 mr-2">
                            <p className="font-medium text-sm truncate">{doc.filename}</p>
                            <p className="text-xs text-gray-500">{DOCUMENT_TYPES[doc.type]}</p>
                          </div>
                          <button
                            type="button"
                            onClick={() => removeDocument(index)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            <X className="h-4 w-4" />
                          </button>
                        </div>
                        
                        <div className="grid grid-cols-2 gap-2">
                          <select
                            value={doc.type}
                            onChange={(e) => updateDocumentType(index, e.target.value as DocumentFile['type'])}
                            className="text-xs p-2 border rounded bg-white"
                          >
                            {Object.entries(DOCUMENT_TYPES).map(([value, label]) => (
                              <option key={value} value={value}>{label}</option>
                            ))}
                          </select>
                          
                          <Input
                            value={doc.description || ''}
                            onChange={(e) => updateDocumentDescription(index, e.target.value)}
                            placeholder="Descripción opcional"
                            className="text-xs p-2 h-auto"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ) : (
                <div className="text-center py-6 text-gray-500">
                  <FileText className="h-6 w-6 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm">Sin documentos</p>
                  <p className="text-xs">Opcional</p>
                </div>
              )}
            </div>
          </div>
        );

      case 'review':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Resumen Final</h2>
              <p className="text-gray-600 text-sm">Revisa tu información antes de enviar</p>
            </div>
            
            <div className="space-y-3">
              <div className="bg-white rounded-lg border border-gray-200 p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Asistencia</span>
                  <span className={`text-sm font-medium ${formData.willAttend === 'yes' ? 'text-green-600' : 'text-red-600'}`}>
                    {formData.willAttend === 'yes' ? '✅ Confirmo asistencia' : '❌ No asistiré'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Síntomas</span>
                  <span className={`text-sm font-medium ${formData.hasSymptoms ? 'text-orange-600' : 'text-green-600'}`}>
                    {formData.hasSymptoms ? '⚠️ Reportó síntomas' : '✅ Sin síntomas'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Medicamentos</span>
                  <span className={`text-sm font-medium ${formData.takingMedications ? 'text-blue-600' : 'text-green-600'}`}>
                    {formData.takingMedications ? '💊 Toma medicamentos' : '✅ Sin medicamentos'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Alergias</span>
                  <span className={`text-sm font-medium ${formData.hasAllergies ? 'text-orange-600' : 'text-green-600'}`}>
                    {formData.hasAllergies ? '⚠️ Tiene alergias' : '✅ Sin alergias'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium text-gray-600">Documentos</span>
                  <span className="text-sm font-medium text-purple-600">
                    📎 {formData.documents.length}/3 archivo(s)
                  </span>
                </div>
              </div>
              
              {(formData.chiefComplaint || formData.symptoms || formData.medications || formData.allergies) && (
                <div className="bg-blue-50 rounded-lg border border-blue-200 p-4 space-y-2">
                  <Label className="text-sm font-medium text-blue-900">Información médica detallada:</Label>
                  {formData.chiefComplaint && (
                    <div>
                      <span className="text-xs font-medium text-blue-700">Motivo de consulta: </span>
                      <span className="text-xs text-blue-700">{formData.chiefComplaint}</span>
                    </div>
                  )}
                  {formData.symptoms && (
                    <div>
                      <span className="text-xs font-medium text-blue-700">Síntomas adicionales: </span>
                      <span className="text-xs text-blue-700">{formData.symptoms}</span>
                    </div>
                  )}
                  {formData.medications && (
                    <div>
                      <span className="text-xs font-medium text-blue-700">Medicamentos: </span>
                      <span className="text-xs text-blue-700">{formData.medications}</span>
                    </div>
                  )}
                  {formData.allergies && (
                    <div>
                      <span className="text-xs font-medium text-blue-700">Alergias: </span>
                      <span className="text-xs text-blue-700">{formData.allergies}</span>
                    </div>
                  )}
                </div>
              )}
              
              <div className="bg-gray-50 rounded-lg border border-gray-200 p-4">
                <Label htmlFor="additionalNotes" className="text-sm font-medium text-gray-700">
                  Observaciones adicionales (opcional)
                </Label>
                <Textarea
                  id="additionalNotes"
                  value={formData.additionalNotes}
                  onChange={(e) => setFormData(prev => ({ ...prev, additionalNotes: e.target.value }))}
                  placeholder="Cualquier información adicional importante..."
                  rows={2}
                  className="mt-2 resize-none text-sm bg-white"
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const steps: WizardStep[] = ['attendance', 'symptoms', 'medications', 'allergies', 'documents', 'review'];
  const currentStepIndex = steps.indexOf(currentStep);
  const progress = ((currentStepIndex + 1) / steps.length) * 100;

  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Pre-checkin Médico</DialogTitle>
            <DialogDescription>
              Cargando información...
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Cargando...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[95vw] max-w-lg max-h-[95vh] overflow-y-auto p-0">
        <div className="sticky top-0 bg-white border-b border-gray-200 p-4 z-10">
          <DialogHeader className="space-y-2">
            <DialogTitle className="text-center text-lg">Pre-checkin Médico</DialogTitle>
            <DialogDescription className="text-center text-sm">
              Paso {currentStepIndex + 1} de {steps.length}
            </DialogDescription>
          </DialogHeader>

          {/* Progress Bar */}
          <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
              style={{ width: `${progress}%` }}
            />
          </div>
        </div>

        {/* Step Content */}
        <div className="p-4">
          {renderStep()}
        </div>

        {/* Navigation Buttons */}
        <div className="sticky bottom-0 bg-white border-t border-gray-200 p-4">
          <div className="flex gap-3">
            {currentStepIndex > 0 && (
              <Button
                variant="outline"
                onClick={prevStep}
                className="flex-1"
              >
                <ChevronLeft className="h-4 w-4 mr-2" />
                Anterior
              </Button>
            )}

            {currentStep === 'review' ? (
              <Button 
                onClick={handleSubmit}
                disabled={isSubmitting || formData.willAttend === ''}
                className="flex-1 bg-green-600 hover:bg-green-700 py-3"
                size="lg"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                    Enviando...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Completar Pre-checkin
                  </>
                )}
              </Button>
            ) : (
              <Button 
                onClick={nextStep}
                disabled={currentStep === 'attendance' && formData.willAttend === ''}
                className="flex-1 py-3"
                size="lg"
              >
                Siguiente
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            )}
          </div>
          
          <Button 
            variant="ghost" 
            onClick={onClose}
            className="w-full mt-2 text-gray-500"
            size="sm"
          >
            Cancelar
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}