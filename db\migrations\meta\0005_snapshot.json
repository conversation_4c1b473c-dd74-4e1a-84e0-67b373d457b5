{"id": "2dce55d4-70ac-404b-a5a9-cd6fa274f7d2", "prevId": "86d8134f-e1a6-4bfd-86c5-57d0cc487fdb", "version": "7", "dialect": "postgresql", "tables": {"public.activity_types": {"name": "activity_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true}, "requiresPatient": {"name": "requiresPatient", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "allowsRecurrence": {"name": "allowsRecurrence", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"activity_types_category_idx": {"name": "activity_types_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_types_active_idx": {"name": "activity_types_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "activity_types_order_idx": {"name": "activity_types_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assistant_doctor_relations": {"name": "assistant_doctor_relations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "assistantId": {"name": "assistantId", "type": "text", "primaryKey": false, "notNull": true}, "doctorId": {"name": "doctorId", "type": "text", "primaryKey": false, "notNull": true}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"assistant_doctor_idx": {"name": "assistant_doctor_idx", "columns": [{"expression": "assistantId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "assistant_relations_idx": {"name": "assistant_relations_idx", "columns": [{"expression": "assistantId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "doctor_relations_idx": {"name": "doctor_relations_idx", "columns": [{"expression": "doctorId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"assistant_doctor_relations_assistantId_user_id_fk": {"name": "assistant_doctor_relations_assistantId_user_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "user", "columnsFrom": ["assistantId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assistant_doctor_relations_doctorId_user_id_fk": {"name": "assistant_doctor_relations_doctorId_user_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "user", "columnsFrom": ["doctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assistant_doctor_relations_consultoryId_consultories_id_fk": {"name": "assistant_doctor_relations_consultoryId_consultories_id_fk", "tableFrom": "assistant_doctor_relations", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.association_codes": {"name": "association_codes", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp", "primaryKey": false, "notNull": true}, "usedBy": {"name": "usedBy", "type": "text", "primaryKey": false, "notNull": false}, "usedAt": {"name": "usedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"association_code_idx": {"name": "association_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "association_patient_idx": {"name": "association_patient_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "association_expires_idx": {"name": "association_expires_idx", "columns": [{"expression": "expiresAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"association_codes_patientId_user_id_fk": {"name": "association_codes_patientId_user_id_fk", "tableFrom": "association_codes", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "association_codes_usedBy_user_id_fk": {"name": "association_codes_usedBy_user_id_fk", "tableFrom": "association_codes", "tableTo": "user", "columnsFrom": ["usedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"association_codes_code_unique": {"name": "association_codes_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.consultories": {"name": "consultories", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": false}, "specialty": {"name": "specialty", "type": "text", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": false, "default": 1}, "floor": {"name": "floor", "type": "integer", "primaryKey": false, "notNull": false}, "building": {"name": "building", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}, "businessHours": {"name": "businessHours", "type": "jsonb", "primaryKey": false, "notNull": false}, "equipment": {"name": "equipment", "type": "jsonb", "primaryKey": false, "notNull": false}, "services": {"name": "services", "type": "jsonb", "primaryKey": false, "notNull": false}, "isEmergencyCapable": {"name": "isEmergencyCapable", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "hasAirConditioning": {"name": "hasAirConditioning", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "hasWaitingRoom": {"name": "hasWaitingRoom", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "accessibility": {"name": "accessibility", "type": "jsonb", "primaryKey": false, "notNull": false}, "active": {"name": "active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"consultories_active_idx": {"name": "consultories_active_idx", "columns": [{"expression": "active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_is_active_idx": {"name": "consultories_is_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_code_idx": {"name": "consultories_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "consultories_type_idx": {"name": "consultories_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_building_idx": {"name": "consultories_building_idx", "columns": [{"expression": "building", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "consultories_emergency_idx": {"name": "consultories_emergency_idx", "columns": [{"expression": "isEmergencyCapable", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"consultories_code_unique": {"name": "consultories_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.countries": {"name": "countries", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": false}, "phoneCode": {"name": "phoneCode", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"countries_code_unique": {"name": "countries_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.currencies": {"name": "currencies", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "text", "primaryKey": false, "notNull": true}, "symbol": {"name": "symbol", "type": "text", "primaryKey": false, "notNull": true}, "exchangeRate": {"name": "exchangeRate", "type": "real", "primaryKey": false, "notNull": true, "default": 1}, "isDefault": {"name": "isDefault", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"currencies_code_idx": {"name": "currencies_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "currencies_active_idx": {"name": "currencies_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "currencies_default_idx": {"name": "currencies_default_idx", "columns": [{"expression": "isDefault", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"currencies_code_unique": {"name": "currencies_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.departments": {"name": "departments", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"departments_countryId_countries_id_fk": {"name": "departments_countryId_countries_id_fk", "tableFrom": "departments", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_types": {"name": "document_types", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "shortName": {"name": "shortName", "type": "text", "primaryKey": false, "notNull": true}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": true}, "countryName": {"name": "countryName", "type": "text", "primaryKey": false, "notNull": true}, "format": {"name": "format", "type": "text", "primaryKey": false, "notNull": true}, "maxLength": {"name": "max<PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true}, "minLength": {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "integer", "primaryKey": false, "notNull": true}, "isRequired": {"name": "isRequired", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "example": {"name": "example", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"document_types_country_idx": {"name": "document_types_country_idx", "columns": [{"expression": "countryId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_types_active_idx": {"name": "document_types_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_types_required_idx": {"name": "document_types_required_idx", "columns": [{"expression": "isRequired", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"document_types_countryId_countries_id_fk": {"name": "document_types_countryId_countries_id_fk", "tableFrom": "document_types", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.education_levels": {"name": "education_levels", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"education_levels_order_idx": {"name": "education_levels_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "education_levels_active_idx": {"name": "education_levels_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.guardian_patient_relations": {"name": "guardian_patient_relations", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "guardianId": {"name": "guardianId", "type": "text", "primaryKey": false, "notNull": true}, "patientId": {"name": "patientId", "type": "text", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "text", "primaryKey": false, "notNull": false}, "isPrimary": {"name": "isPrimary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "canMakeDecisions": {"name": "canMakeDecisions", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "validUntil": {"name": "validUntil", "type": "timestamp", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"guardian_patient_idx": {"name": "guardian_patient_idx", "columns": [{"expression": "guardianId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "guardian_relations_idx": {"name": "guardian_relations_idx", "columns": [{"expression": "guardianId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "patient_relations_idx": {"name": "patient_relations_idx", "columns": [{"expression": "patientId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"guardian_patient_relations_guardianId_user_id_fk": {"name": "guardian_patient_relations_guardianId_user_id_fk", "tableFrom": "guardian_patient_relations", "tableTo": "user", "columnsFrom": ["guardianId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "guardian_patient_relations_patientId_user_id_fk": {"name": "guardian_patient_relations_patientId_user_id_fk", "tableFrom": "guardian_patient_relations", "tableTo": "user", "columnsFrom": ["patientId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.marital_status": {"name": "marital_status", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "allowsSpouse": {"name": "allowsSpouse", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "legalImplications": {"name": "legalImplications", "type": "text", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"marital_status_order_idx": {"name": "marital_status_order_idx", "columns": [{"expression": "order", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "marital_status_active_idx": {"name": "marital_status_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "marital_status_spouse_idx": {"name": "marital_status_spouse_idx", "columns": [{"expression": "allowsSpouse", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.media_sources": {"name": "media_sources", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "trackingEnabled": {"name": "trackingEnabled", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "cost": {"name": "cost", "type": "real", "primaryKey": false, "notNull": false, "default": 0}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "icon": {"name": "icon", "type": "text", "primaryKey": false, "notNull": false}, "color": {"name": "color", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"media_sources_type_idx": {"name": "media_sources_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_category_idx": {"name": "media_sources_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_active_idx": {"name": "media_sources_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "media_sources_tracking_idx": {"name": "media_sources_tracking_idx", "columns": [{"expression": "trackingEnabled", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.medical_specialties": {"name": "medical_specialties", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.municipalities": {"name": "municipalities", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "departmentId": {"name": "departmentId", "type": "integer", "primaryKey": false, "notNull": true}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {"municipalities_departmentId_departments_id_fk": {"name": "municipalities_departmentId_departments_id_fk", "tableFrom": "municipalities", "tableTo": "departments", "columnsFrom": ["departmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.non_pathological_history": {"name": "non_pathological_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "subcategory": {"name": "subcategory", "type": "text", "primaryKey": false, "notNull": false}, "isPositive": {"name": "isPositive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "importance": {"name": "importance", "type": "text", "primaryKey": false, "notNull": true}, "ageRelevant": {"name": "ageR<PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "benefits": {"name": "benefits", "type": "jsonb", "primaryKey": false, "notNull": false}, "risks": {"name": "risks", "type": "jsonb", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "text", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"non_pathological_history_category_idx": {"name": "non_pathological_history_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_subcategory_idx": {"name": "non_pathological_history_subcategory_idx", "columns": [{"expression": "subcategory", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_positive_idx": {"name": "non_pathological_history_positive_idx", "columns": [{"expression": "isPositive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_importance_idx": {"name": "non_pathological_history_importance_idx", "columns": [{"expression": "importance", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_age_idx": {"name": "non_pathological_history_age_idx", "columns": [{"expression": "ageR<PERSON><PERSON>", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "non_pathological_history_active_idx": {"name": "non_pathological_history_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notifications": {"name": "notifications", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "text", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "read": {"name": "read", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "data": {"name": "data", "type": "jsonb", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"notifications_user_idx": {"name": "notifications_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_read_idx": {"name": "notifications_read_idx", "columns": [{"expression": "read", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "notifications_type_idx": {"name": "notifications_type_idx", "columns": [{"expression": "type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"notifications_userId_user_id_fk": {"name": "notifications_userId_user_id_fk", "tableFrom": "notifications", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.occupations": {"name": "occupations", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pathological_history": {"name": "pathological_history", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": true}, "icd11Code": {"name": "icd11Code", "type": "text", "primaryKey": false, "notNull": false}, "severity": {"name": "severity", "type": "text", "primaryKey": false, "notNull": true}, "isHereditary": {"name": "isHereditary", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "requiresSpecialistFollow": {"name": "requiresSpecialist<PERSON><PERSON><PERSON>", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "commonInChildren": {"name": "commonInChildren", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "riskLevel": {"name": "riskLevel", "type": "text", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "symptoms": {"name": "symptoms", "type": "jsonb", "primaryKey": false, "notNull": false}, "treatments": {"name": "treatments", "type": "jsonb", "primaryKey": false, "notNull": false}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"pathological_history_category_idx": {"name": "pathological_history_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_severity_idx": {"name": "pathological_history_severity_idx", "columns": [{"expression": "severity", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_risk_idx": {"name": "pathological_history_risk_idx", "columns": [{"expression": "riskLevel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_hereditary_idx": {"name": "pathological_history_hereditary_idx", "columns": [{"expression": "isHereditary", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_children_idx": {"name": "pathological_history_children_idx", "columns": [{"expression": "commonInChildren", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_active_idx": {"name": "pathological_history_active_idx", "columns": [{"expression": "isActive", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pathological_history_icd11_idx": {"name": "pathological_history_icd11_idx", "columns": [{"expression": "icd11Code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.registrationRequests": {"name": "registrationRequests", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "generalData": {"name": "generalData", "type": "jsonb", "primaryKey": false, "notNull": false}, "specificData": {"name": "specificData", "type": "jsonb", "primaryKey": false, "notNull": false}, "reviewedBy": {"name": "reviewedBy", "type": "text", "primaryKey": false, "notNull": false}, "reviewedAt": {"name": "reviewedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "reviewNotes": {"name": "reviewNotes", "type": "text", "primaryKey": false, "notNull": false}, "rejectionReason": {"name": "rejectionReason", "type": "text", "primaryKey": false, "notNull": false}, "submittedAt": {"name": "submittedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"registration_requests_user_idx": {"name": "registration_requests_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "registration_requests_status_idx": {"name": "registration_requests_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "registration_requests_submitted_idx": {"name": "registration_requests_submitted_idx", "columns": [{"expression": "submittedAt", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"registrationRequests_userId_user_id_fk": {"name": "registrationRequests_userId_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "registrationRequests_reviewedBy_user_id_fk": {"name": "registrationRequests_reviewedBy_user_id_fk", "tableFrom": "registrationRequests", "tableTo": "user", "columnsFrom": ["reviewedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.relationships": {"name": "relationships", "schema": "", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "category": {"name": "category", "type": "text", "primaryKey": false, "notNull": false}, "isActive": {"name": "isActive", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user": {"name": "user", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "firstName": {"name": "firstName", "type": "text", "primaryKey": false, "notNull": false}, "lastName": {"name": "lastName", "type": "text", "primaryKey": false, "notNull": false}, "documentType": {"name": "documentType", "type": "text", "primaryKey": false, "notNull": false}, "documentNumber": {"name": "documentNumber", "type": "text", "primaryKey": false, "notNull": false}, "dateOfBirth": {"name": "dateOfBirth", "type": "timestamp", "primaryKey": false, "notNull": false}, "gender": {"name": "gender", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "alternativePhone": {"name": "alternativePhone", "type": "text", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "countryId": {"name": "countryId", "type": "integer", "primaryKey": false, "notNull": false}, "departmentId": {"name": "departmentId", "type": "integer", "primaryKey": false, "notNull": false}, "municipalityId": {"name": "municipalityId", "type": "integer", "primaryKey": false, "notNull": false}, "occupationId": {"name": "occupationId", "type": "integer", "primaryKey": false, "notNull": false}, "emergencyContact": {"name": "emergencyContact", "type": "text", "primaryKey": false, "notNull": false}, "emergencyPhone": {"name": "emergencyPhone", "type": "text", "primaryKey": false, "notNull": false}, "emergencyRelationshipId": {"name": "emergencyRelationshipId", "type": "integer", "primaryKey": false, "notNull": false}, "overallStatus": {"name": "overallStatus", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"email_idx": {"name": "email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "overall_status_idx": {"name": "overall_status_idx", "columns": [{"expression": "overallStatus", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "document_idx": {"name": "document_idx", "columns": [{"expression": "documentType", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "documentNumber", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_countryId_countries_id_fk": {"name": "user_countryId_countries_id_fk", "tableFrom": "user", "tableTo": "countries", "columnsFrom": ["countryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_departmentId_departments_id_fk": {"name": "user_departmentId_departments_id_fk", "tableFrom": "user", "tableTo": "departments", "columnsFrom": ["departmentId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_municipalityId_municipalities_id_fk": {"name": "user_municipalityId_municipalities_id_fk", "tableFrom": "user", "tableTo": "municipalities", "columnsFrom": ["municipalityId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_occupationId_occupations_id_fk": {"name": "user_occupationId_occupations_id_fk", "tableFrom": "user", "tableTo": "occupations", "columnsFrom": ["occupationId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_emergencyRelationshipId_relationships_id_fk": {"name": "user_emergencyRelationshipId_relationships_id_fk", "tableFrom": "user", "tableTo": "relationships", "columnsFrom": ["emergencyRelationshipId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"user_email_unique": {"name": "user_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_roles": {"name": "user_roles", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "userId": {"name": "userId", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "text", "primaryKey": false, "notNull": false, "default": "'pending'"}, "consultoryId": {"name": "consultoryId", "type": "text", "primaryKey": false, "notNull": false}, "specialtyId": {"name": "specialtyId", "type": "integer", "primaryKey": false, "notNull": false}, "preferredDoctorId": {"name": "preferredDoctorId", "type": "text", "primaryKey": false, "notNull": false}, "medicalLicense": {"name": "medicalLicense", "type": "text", "primaryKey": false, "notNull": false}, "roleData": {"name": "roleData", "type": "jsonb", "primaryKey": false, "notNull": false}, "approvedBy": {"name": "approvedBy", "type": "text", "primaryKey": false, "notNull": false}, "approvedAt": {"name": "approvedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejectedBy": {"name": "rejectedBy", "type": "text", "primaryKey": false, "notNull": false}, "rejectedAt": {"name": "rejectedAt", "type": "timestamp", "primaryKey": false, "notNull": false}, "rejectionReason": {"name": "rejectionReason", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updatedAt": {"name": "updatedAt", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"user_role_idx": {"name": "user_role_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_roles_user_idx": {"name": "user_roles_user_idx", "columns": [{"expression": "userId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_roles_role_idx": {"name": "user_roles_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_roles_status_idx": {"name": "user_roles_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_roles_userId_user_id_fk": {"name": "user_roles_userId_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["userId"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_roles_consultoryId_consultories_id_fk": {"name": "user_roles_consultoryId_consultories_id_fk", "tableFrom": "user_roles", "tableTo": "consultories", "columnsFrom": ["consultoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_specialtyId_medical_specialties_id_fk": {"name": "user_roles_specialtyId_medical_specialties_id_fk", "tableFrom": "user_roles", "tableTo": "medical_specialties", "columnsFrom": ["specialtyId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_preferredDoctorId_user_id_fk": {"name": "user_roles_preferredDoctorId_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["preferredDoctorId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_approvedBy_user_id_fk": {"name": "user_roles_approvedBy_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["approvedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_roles_rejectedBy_user_id_fk": {"name": "user_roles_rejectedBy_user_id_fk", "tableFrom": "user_roles", "tableTo": "user", "columnsFrom": ["rejectedBy"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}