'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  BookOpen,
  Search,
  Eye,
  Edit,
  Plus,
  Filter,
  Settings,
  Info,
  Users,
  MapPin,
  Briefcase,
  Shield,
  Lock,
  Unlock,
  AlertCircle
} from 'lucide-react';

// Catálogos disponibles para asistentes con permisos configurables
const assistantCatalogs = [
  {
    id: 'specialties',
    name: 'Especialidades Médicas',
    description: 'Catálogo de especialidades médicas disponibles',
    icon: '🩺',
    count: 20,
    category: 'Médico',
    permission: 'read',
    configurable: false,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'occupations',
    name: 'Ocupaciones',
    description: 'Lista de ocupaciones profesionales',
    icon: '💼',
    count: 45,
    category: 'Demográfico',
    permission: 'edit',
    configurable: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'relationships',
    name: '<PERSON>rentes<PERSON>',
    description: 'Tipos de relación familiar para encargados',
    icon: '👨‍👩‍👧‍👦',
    count: 12,
    category: 'Demográfico',
    permission: 'edit',
    configurable: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'departments',
    name: 'Departamentos',
    description: 'Departamentos de Guatemala',
    icon: '🏛️',
    count: 22,
    category: 'Geográfico',
    permission: 'read',
    configurable: false,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'municipalities',
    name: 'Municipios',
    description: 'Municipios por departamento',
    icon: '🏘️',
    count: 340,
    category: 'Geográfico',
    permission: 'read',
    configurable: false,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'media',
    name: 'Medios/Origen de Pacientes',
    description: 'Canales de adquisición de pacientes',
    icon: '📢',
    count: 0,
    category: 'Marketing',
    permission: 'edit',
    configurable: true,
    lastUpdated: null,
    available: false
  },
  {
    id: 'activities',
    name: 'Tipos de Actividad',
    description: 'Tipos de citas y actividades médicas',
    icon: '📅',
    count: 0,
    category: 'Operativo',
    permission: 'edit',
    configurable: true,
    lastUpdated: null,
    available: false
  },
  {
    id: 'payments',
    name: 'Formas de Pago',
    description: 'Métodos de pago disponibles',
    icon: '💳',
    count: 0,
    category: 'Financiero',
    permission: 'edit',
    configurable: true,
    lastUpdated: null,
    available: false
  },
  {
    id: 'insurance',
    name: 'Aseguradoras',
    description: 'Compañías de seguros médicos',
    icon: '🏥',
    count: 0,
    category: 'Financiero',
    permission: 'read',
    configurable: false,
    lastUpdated: null,
    available: false
  }
];

const categories = [
  { id: 'all', name: 'Todos', icon: BookOpen },
  { id: 'Médico', name: 'Médico', icon: Users },
  { id: 'Demográfico', name: 'Demográfico', icon: Users },
  { id: 'Geográfico', name: 'Geográfico', icon: MapPin },
  { id: 'Marketing', name: 'Marketing', icon: Briefcase },
  { id: 'Operativo', name: 'Operativo', icon: Settings },
  { id: 'Financiero', name: 'Financiero', icon: Shield }
];

const permissions = [
  { id: 'all', name: 'Todos', icon: BookOpen },
  { id: 'read', name: 'Solo Lectura', icon: Eye },
  { id: 'edit', name: 'Edición', icon: Edit }
];

export default function AssistantCatalogsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedPermission, setSelectedPermission] = useState('all');
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(true);

  const filteredCatalogs = assistantCatalogs.filter(catalog => {
    const matchesSearch = catalog.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         catalog.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || catalog.category === selectedCategory;
    const matchesPermission = selectedPermission === 'all' || catalog.permission === selectedPermission;
    const matchesAvailability = !showOnlyAvailable || catalog.available !== false;
    
    return matchesSearch && matchesCategory && matchesPermission && matchesAvailability;
  });

  const availableCatalogs = assistantCatalogs.filter(c => c.available !== false).length;
  const editableCatalogs = assistantCatalogs.filter(c => c.permission === 'edit' && c.available !== false).length;
  const totalRecords = assistantCatalogs.filter(c => c.available !== false).reduce((sum, c) => sum + c.count, 0);

  const getPermissionIcon = (permission: string) => {
    switch (permission) {
      case 'edit': return <Edit className="h-4 w-4" />;
      case 'read': return <Eye className="h-4 w-4" />;
      default: return <Lock className="h-4 w-4" />;
    }
  };

  const getPermissionColor = (permission: string) => {
    switch (permission) {
      case 'edit': return 'bg-green-100 text-green-800 border-green-200';
      case 'read': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Catálogos Administrativos</h1>
            <p className="text-gray-600">Gestiona catálogos según tus permisos asignados</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configuración
          </Button>
          <Button variant="outline" size="sm">
            <Info className="h-4 w-4 mr-2" />
            Ayuda
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Catálogos Disponibles</p>
                <p className="text-3xl font-bold text-purple-600">{availableCatalogs}</p>
              </div>
              <div className="h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-purple-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Con Permisos de Edición</p>
                <p className="text-3xl font-bold text-green-600">{editableCatalogs}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <Edit className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Registros</p>
                <p className="text-3xl font-bold text-blue-600">{totalRecords.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros y Búsqueda */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Búsqueda */}
            <div className="md:col-span-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar catálogos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filtro por categoría */}
            <div className="flex flex-wrap gap-2">
              {categories.slice(0, 4).map((category) => {
                const Icon = category.icon;
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="flex items-center space-x-1"
                  >
                    <Icon className="h-3 w-3" />
                    <span className="text-xs">{category.name}</span>
                  </Button>
                );
              })}
            </div>

            {/* Filtro por permisos */}
            <div className="flex space-x-2">
              {permissions.map((permission) => {
                const Icon = permission.icon;
                return (
                  <Button
                    key={permission.id}
                    variant={selectedPermission === permission.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedPermission(permission.id)}
                    className="flex items-center space-x-1"
                  >
                    <Icon className="h-3 w-3" />
                    <span className="text-xs">{permission.name}</span>
                  </Button>
                );
              })}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Catálogos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCatalogs.map((catalog) => (
          <Card 
            key={catalog.id}
            className={`transition-all hover:shadow-md ${
              catalog.available !== false
                ? catalog.permission === 'edit'
                  ? 'border-green-200 hover:border-green-300'
                  : 'border-blue-200 hover:border-blue-300'
                : 'border-gray-200 bg-gray-50'
            }`}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{catalog.icon}</div>
                  <div>
                    <CardTitle className="text-base">{catalog.name}</CardTitle>
                    <Badge variant="outline" className="mt-1">
                      {catalog.category}
                    </Badge>
                  </div>
                </div>
                <div className="space-y-1">
                  <Badge className={getPermissionColor(catalog.permission)}>
                    <div className="flex items-center space-x-1">
                      {getPermissionIcon(catalog.permission)}
                      <span className="text-xs">
                        {catalog.permission === 'edit' ? 'Edición' : 'Lectura'}
                      </span>
                    </div>
                  </Badge>
                  {catalog.configurable && (
                    <Badge variant="outline" className="text-xs bg-yellow-50 border-yellow-200 text-yellow-800">
                      <Settings className="h-3 w-3 mr-1" />
                      Configurable
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4">{catalog.description}</p>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Registros:</span>
                  <span className="font-medium">{catalog.count.toLocaleString()}</span>
                </div>
                
                {catalog.lastUpdated && (
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Última actualización:</span>
                    <span className="font-medium">{catalog.lastUpdated}</span>
                  </div>
                )}
                
                <div className="pt-2 space-y-2">
                  {catalog.available !== false ? (
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm" className="flex-1">
                        <Eye className="h-3 w-3 mr-1" />
                        Ver
                      </Button>
                      {catalog.permission === 'edit' && (
                        <Button size="sm" className="flex-1">
                          <Edit className="h-3 w-3 mr-1" />
                          Editar
                        </Button>
                      )}
                    </div>
                  ) : (
                    <Button variant="outline" className="w-full" size="sm" disabled>
                      <AlertCircle className="h-3 w-3 mr-2" />
                      No Implementado
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mensaje cuando no hay resultados */}
      {filteredCatalogs.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron catálogos</h3>
            <p className="text-gray-600">
              Intenta cambiar los filtros o el término de búsqueda.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Información sobre permisos */}
      <Card className="border-purple-200 bg-purple-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="h-10 w-10 bg-purple-100 rounded-full flex items-center justify-center">
              <Shield className="h-5 w-5 text-purple-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-purple-900 mb-2">Información sobre Permisos</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-purple-800 text-sm">
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <Eye className="h-4 w-4" />
                    <span className="font-medium">Solo Lectura:</span>
                  </div>
                  <p>Puedes consultar pero no modificar los registros</p>
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <Edit className="h-4 w-4" />
                    <span className="font-medium">Edición:</span>
                  </div>
                  <p>Puedes agregar, modificar y eliminar registros</p>
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <Settings className="h-4 w-4" />
                    <span className="font-medium">Configurable:</span>
                  </div>
                  <p>El administrador puede cambiar tus permisos</p>
                </div>
                <div>
                  <div className="flex items-center space-x-2 mb-1">
                    <Lock className="h-4 w-4" />
                    <span className="font-medium">Fijo:</span>
                  </div>
                  <p>Permisos definidos por el sistema</p>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}