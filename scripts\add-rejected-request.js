require('dotenv').config({ path: '.env.local' });
const { Pool } = require('pg');

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: { rejectUnauthorized: false }
});

async function addRejectedRequest() {
  try {
    console.log('🔄 Agregando solicitud rechazada...');
    
    // Insertar solicitud rechazada
    await pool.query(`
      INSERT INTO "registrationRequests" (id, "userId", role, status, "rejectionReason", "reviewedAt", "submittedAt", "updatedAt")
      VALUES (gen_random_uuid(), 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg', 'assistant', 'rejected', 'Documentación incompleta - Se requiere completar certificaciones', NOW(), NOW(), NOW())
    `);
    console.log('✅ Solicitud rechazada insertada');
    
    console.log('🎉 Solicitud rechazada agregada exitosamente');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await pool.end();
  }
}

addRejectedRequest();