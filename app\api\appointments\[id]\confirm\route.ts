import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { email, confirmedVia } = await request.json();
    const appointmentId = params.id;

    // Validar email
    if (!email || !email.includes('@')) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Email inválido' 
        },
        { status: 400 }
      );
    }

    // Verificar que la cita existe
    const existingAppointment = await db
      .select({ 
        patientId: appointments.patientId,
        status: appointments.status,
        confirmedAt: appointments.confirmedAt
      })
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (existingAppointment.length === 0) {
      return NextResponse.json(
        { 
          success: false,
          error: 'Cita no encontrada' 
        },
        { status: 404 }
      );
    }

    const appointment = existingAppointment[0];

    // Verificar si ya está confirmada
    if (appointment.confirmedAt) {
      return NextResponse.json(
        { 
          success: true,
          message: 'La cita ya estaba confirmada anteriormente',
          alreadyConfirmed: true
        }
      );
    }

    // Actualizar cita como confirmada
    await db.update(appointments)
      .set({
        status: 'confirmed',
        emailCaptured: email,
        confirmedAt: new Date(),
        confirmedVia: confirmedVia || 'web',
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    // Verificar si el paciente necesita actualizar su email
    if (appointment.patientId) {
      const patient = await db
        .select({ email: user.email })
        .from(user)
        .where(eq(user.id, appointment.patientId))
        .limit(1);

      // Si el paciente no tenía email o es diferente, actualizarlo
      if (patient[0] && (!patient[0].email || patient[0].email !== email)) {
        await db.update(user)
          .set({ 
            email: email,
            updatedAt: new Date()
          })
          .where(eq(user.id, appointment.patientId));
      }
    }

    // Enviar emails de confirmación y activación
    console.log('🔔 Enviando emails de confirmación post-VAPI');
    
    // Usar el sistema unificado de emails
    setImmediate(async () => {
      try {
        const { sendAppointmentEmailsAsync } = await import('@/lib/email-platform/appointment-emails');
        await sendAppointmentEmailsAsync(appointmentId, appointment.patientId, {
          triggerType: 'vapi_confirmation',
          capturedEmail: email
        });
      } catch (error) {
        console.error('❌ Error enviando emails post-confirmación VAPI:', error);
      }
    });

    return NextResponse.json({
      success: true,
      message: 'Cita confirmada exitosamente. Te enviaremos recordatorios por email.',
      canReceivePreCheckin: true
    });

  } catch (error) {
    console.error('Error confirming appointment:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error interno del servidor' 
      },
      { status: 500 }
    );
  }
}