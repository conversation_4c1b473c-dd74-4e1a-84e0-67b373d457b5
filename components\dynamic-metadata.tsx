'use client';

import { useEffect } from 'react';
import { useConsultoryInfo } from '@/hooks/use-consultory-info';

export default function DynamicMetadata() {
  const { consultory, isLoading } = useConsultoryInfo();

  useEffect(() => {
    if (!isLoading && consultory?.name) {
      // Actualizar el título dinámicamente
      document.title = `${consultory.name} - Sistema de Gestión Clínica Pediátrica`;
      
      // Actualizar meta description si es necesario
      const metaDescription = document.querySelector('meta[name="description"]');
      if (metaDescription) {
        metaDescription.setAttribute('content', 
          `Sistema integral de gestión clínica pediátrica de ${consultory.name}. Expedientes médicos, control de citas, seguimiento de crecimiento y desarrollo.`
        );
      }

      // Actualizar meta tags de Open Graph
      const ogTitle = document.querySelector('meta[property="og:title"]');
      if (ogTitle) {
        ogTitle.setAttribute('content', `${consultory.name} - Sistema de Gestión Clínica`);
      }

      const ogSiteName = document.querySelector('meta[property="og:site_name"]');
      if (ogSiteName) {
        ogSiteName.setAttribute('content', consultory.name);
      }
    }
  }, [consultory, isLoading]);

  return null; // Este componente no renderiza nada visible
}