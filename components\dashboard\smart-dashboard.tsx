'use client';

import { useState } from 'react';
import { useUserContexts } from '@/hooks/use-user-contexts';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Calendar, 
  Users, 
  Stethoscope, 
  Clock, 
  Bell,
  User,
  Heart,
  Building,
  Package,
  ArrowRight,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Timer
} from 'lucide-react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { translateRelationship } from '@/lib/utils';

export function SmartDashboard() {
  const { data, loading, currentContext, switchContext } = useUserContexts();
  const router = useRouter();
  const [switching, setSwitching] = useState(false);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
        <span className="ml-2 text-gray-600">Cargando contextos...</span>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-400" />
        <h3 className="text-lg font-medium text-gray-900">Error al cargar contextos</h3>
        <p className="text-gray-600">No se pudieron cargar los contextos del usuario</p>
      </div>
    );
  }

  const handleContextSwitch = async (newContext: string) => {
    setSwitching(true);
    try {
      await switchContext(newContext);
    } finally {
      setSwitching(false);
    }
  };

  const contextNames: Record<string, { name: string; icon: any; color: string }> = {
    doctor: { name: 'Médico', icon: Stethoscope, color: 'blue' },
    assistant: { name: 'Asistente', icon: Users, color: 'green' },
    patient: { name: 'Paciente', icon: User, color: 'purple' },
    guardian: { name: 'Encargado', icon: Heart, color: 'orange' },
    provider: { name: 'Proveedor', icon: Package, color: 'indigo' },
    full: { name: 'Completo', icon: Building, color: 'gray' }
  };

  const currentContextInfo = contextNames[currentContext] || contextNames.full;
  const ContextIcon = currentContextInfo.icon;

  return (
    <div className="space-y-6">
      {/* Header con contexto actual */}
      <div className="flex items-center justify-between bg-white p-4 rounded-lg border">
        <div className="flex items-center gap-3">
          <div className={`p-2 bg-${currentContextInfo.color}-100 rounded-lg`}>
            <ContextIcon className={`h-5 w-5 text-${currentContextInfo.color}-600`} />
          </div>
          <div>
            <h2 className="text-xl font-semibold">
              Buenos días, {data.user.firstName}
            </h2>
            <p className="text-sm text-gray-600">
              Trabajando como: <span className="font-medium">{currentContextInfo.name}</span>
            </p>
          </div>
        </div>

        {data.hasMultipleContexts && (
          <Button 
            variant="outline" 
            onClick={() => switchContext('full')}
            disabled={switching}
          >
            {switching ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <ArrowRight className="h-4 w-4 mr-2" />
            )}
            Cambiar Contexto
          </Button>
        )}
      </div>

      {/* Accesos rápidos */}
      {data.quickAccess.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-yellow-600" />
              Acciones Pendientes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {data.quickAccess.map((item, index) => (
                <div 
                  key={index}
                  className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    // Manejar acciones específicas
                    if (item.action === 'view_appointment') {
                      router.push(`/dashboard/patient/appointments/${item.data.id}`);
                    } else if (item.action === 'complete_precheckin') {
                      router.push(`/pre-checkin/${item.data.appointmentId}`);
                    }
                  }}
                >
                  <div className="flex items-center gap-3">
                    <div className={`p-2 rounded-lg ${
                      item.priority === 'high' ? 'bg-red-100' :
                      item.priority === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'
                    }`}>
                      {item.type === 'appointment' && <Calendar className="h-4 w-4" />}
                      {item.type === 'precheckin' && <CheckCircle className="h-4 w-4" />}
                      {item.type === 'task' && <Timer className="h-4 w-4" />}
                    </div>
                    <div>
                      <p className="font-medium">{item.title}</p>
                      <Badge 
                        variant={item.priority === 'high' ? 'destructive' : 'secondary'}
                        className="text-xs"
                      >
                        {item.priority === 'high' ? 'Urgente' : 
                         item.priority === 'medium' ? 'Importante' : 'Pendiente'}
                      </Badge>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-gray-400" />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Contenido específico por contexto */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Contexto de Trabajo */}
        {currentContext !== 'patient' && currentContext !== 'guardian' && (
          <WorkContextSection 
            workContext={data.contexts.work}
            currentContext={currentContext}
            onContextSwitch={handleContextSwitch}
            switching={switching}
          />
        )}

        {/* Contexto Personal */}
        {(currentContext === 'patient' || currentContext === 'guardian' || currentContext === 'full') && (
          <PersonalContextSection 
            personalContext={data.contexts.personal}
            currentContext={currentContext}
            onContextSwitch={handleContextSwitch}
            switching={switching}
          />
        )}
      </div>

      {/* Modo completo: Mostrar todos los contextos */}
      {currentContext === 'full' && data.hasMultipleContexts && (
        <AllContextsSection 
          workContext={data.contexts.work}
          personalContext={data.contexts.personal}
          onContextSwitch={handleContextSwitch}
          switching={switching}
        />
      )}
    </div>
  );
}

function WorkContextSection({ 
  workContext, 
  currentContext, 
  onContextSwitch, 
  switching 
}: any) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Building className="h-5 w-5 text-blue-600" />
          Trabajo
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {workContext.isDoctor && (
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold flex items-center gap-2">
                  <Stethoscope className="h-4 w-4" />
                  Médico - {workContext.isDoctor.specialty}
                </h3>
                <p className="text-sm text-gray-600">{workContext.isDoctor.consultory}</p>
              </div>
              {currentContext !== 'doctor' && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => onContextSwitch('doctor')}
                  disabled={switching}
                >
                  Cambiar
                </Button>
              )}
            </div>
          </div>
        )}

        {workContext.isAssistant && (
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  Asistente Médico
                </h3>
                <p className="text-sm text-gray-600">
                  {workContext.isAssistant.forDoctors?.length > 0 
                    ? `Asistente de: ${workContext.isAssistant.forDoctors.map((d: any) => d.name).join(', ')}`
                    : 'Asistente médico'
                  }
                </p>
              </div>
              {currentContext !== 'assistant' && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => onContextSwitch('assistant')}
                  disabled={switching}
                >
                  Cambiar
                </Button>
              )}
            </div>
          </div>
        )}

        {workContext.isProvider && (
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold flex items-center gap-2">
                  <Package className="h-4 w-4" />
                  Proveedor
                </h3>
                <p className="text-sm text-gray-600">
                  {workContext.isProvider.company} - {workContext.isProvider.serviceType}
                </p>
              </div>
              {currentContext !== 'provider' && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => onContextSwitch('provider')}
                  disabled={switching}
                >
                  Cambiar
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function PersonalContextSection({ 
  personalContext, 
  currentContext, 
  onContextSwitch, 
  switching 
}: any) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5 text-purple-600" />
          Personal
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {personalContext.isPatient && (
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold flex items-center gap-2">
                  <User className="h-4 w-4" />
                  Mis Citas Médicas
                </h3>
                <p className="text-sm text-gray-600">
                  {personalContext.isPatient.appointments?.length || 0} próximas citas
                </p>
              </div>
              {currentContext !== 'patient' && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => onContextSwitch('patient')}
                  disabled={switching}
                >
                  Ver Citas
                </Button>
              )}
            </div>
          </div>
        )}

        {personalContext.isGuardian && (
          <div className="p-4 border rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold flex items-center gap-2">
                  <Heart className="h-4 w-4" />
                  Mis Dependientes
                </h3>
                <p className="text-sm text-gray-600">
                  {personalContext.isGuardian.totalDependents} personas bajo tu cuidado
                </p>
                <div className="mt-2 space-y-1">
                  {personalContext.isGuardian.dependents?.slice(0, 3).map((dep: any) => (
                    <div key={dep.id} className="text-xs text-gray-500">
                      • {dep.name} ({dep.age} años) - {dep.relationship}
                    </div>
                  ))}
                </div>
              </div>
              {currentContext !== 'guardian' && (
                <Button 
                  size="sm" 
                  variant="outline"
                  onClick={() => onContextSwitch('guardian')}
                  disabled={switching}
                >
                  Gestionar
                </Button>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function AllContextsSection({ 
  workContext, 
  personalContext, 
  onContextSwitch, 
  switching 
}: any) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Todos mis Contextos</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          {Object.entries(workContext).map(([key, value]: [string, any]) => {
            const contextKey = key.replace('is', '').toLowerCase();
            const contextInfo = {
              doctor: { name: 'Médico', icon: Stethoscope, color: 'blue' },
              assistant: { name: 'Asistente', icon: Users, color: 'green' },
              provider: { name: 'Proveedor', icon: Package, color: 'indigo' }
            }[contextKey];

            if (!contextInfo) return null;

            const Icon = contextInfo.icon;

            return (
              <Button
                key={key}
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => onContextSwitch(contextKey)}
                disabled={switching}
              >
                <Icon className={`h-6 w-6 text-${contextInfo.color}-600`} />
                <span className="text-sm font-medium">{contextInfo.name}</span>
              </Button>
            );
          })}

          {Object.entries(personalContext).map(([key, value]: [string, any]) => {
            const contextKey = key.replace('is', '').toLowerCase();
            const contextInfo = {
              patient: { name: 'Paciente', icon: User, color: 'purple' },
              guardian: { name: 'Encargado', icon: Heart, color: 'orange' }
            }[contextKey];

            if (!contextInfo) return null;

            const Icon = contextInfo.icon;

            return (
              <Button
                key={key}
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => onContextSwitch(contextKey)}
                disabled={switching}
              >
                <Icon className={`h-6 w-6 text-${contextInfo.color}-600`} />
                <span className="text-sm font-medium">{contextInfo.name}</span>
              </Button>
            );
          })}
        </div>
      </CardContent>
    </Card>
  );
}