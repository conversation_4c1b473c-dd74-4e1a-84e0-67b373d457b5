'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Plus,
  Search,
  Filter,
  RefreshCw,
  FileText,
  Edit,
  Trash2,
  MoreHorizontal,
  Download,
  ArrowLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  Eye,
  ToggleLeft,
  ToggleRight,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface DocumentType {
  id: string;
  name: string;
  shortName: string;
  countryId: number;
  countryName: string;
  format: string;
  maxLength: number;
  minLength: number;
  isRequired: boolean;
  description?: string;
  example?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Country {
  countryId: number;
  countryName: string;
}

export default function DocumentTypesPage() {
  const router = useRouter();
  const [documentTypes, setDocumentTypes] = useState<DocumentType[]>([]);
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [countryFilter, setCountryFilter] = useState<string>('all');
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [editingType, setEditingType] = useState<DocumentType | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [viewingType, setViewingType] = useState<DocumentType | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [deletingType, setDeletingType] = useState<DocumentType | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    shortName: '',
    countryId: '',
    format: '',
    maxLength: '',
    minLength: '',
    isRequired: false,
    description: '',
    example: '',
    isActive: true
  });
  
  // Form validation errors
  const [formErrors, setFormErrors] = useState({
    name: '',
    shortName: '',
    countryId: '',
    format: '',
    maxLength: '',
    minLength: ''
  });

  // Fetch document types
  const fetchDocumentTypes = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        ...(searchTerm && { search: searchTerm }),
        ...(activeFilter !== 'all' && { active: (activeFilter === 'active').toString() }),
        ...(countryFilter !== 'all' && { countryId: countryFilter })
      });

      const response = await fetch(`/api/catalogs/document-types?${params}`);
      if (!response.ok) throw new Error('Error fetching document types');
      
      const data = await response.json();
      setDocumentTypes(data.data || []);
      setCountries(data.countries || []);
      
      // Actualizar información de paginación
      if (data.pagination) {
        setTotalPages(data.pagination.totalPages || 1);
        setTotalCount(data.pagination.total || 0);
      }
    } catch (error) {
      console.error('Error:', error);
      toast.error('Error al cargar los tipos de documento');
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const handleRefresh = () => {
    setRefreshing(true);
    fetchDocumentTypes();
  };

  useEffect(() => {
    fetchDocumentTypes();
  }, [page, limit, searchTerm, activeFilter, countryFilter]);

  useEffect(() => {
    // Resetear a la primera página cuando cambien los filtros
    if (page !== 1) {
      setPage(1);
    }
  }, [searchTerm, activeFilter, countryFilter]);

  // Handle create
  const handleCreate = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const selectedCountry = countries.find(c => c.countryId.toString() === formData.countryId);
      
      const response = await fetch('/api/catalogs/document-types', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          countryId: parseInt(formData.countryId),
          countryName: selectedCountry?.countryName || '',
          maxLength: parseInt(formData.maxLength),
          minLength: parseInt(formData.minLength)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error creating document type');
      }

      toast.success('Tipo de documento creado exitosamente');
      setIsCreateDialogOpen(false);
      resetForm();
      fetchDocumentTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  // Handle edit
  const handleEdit = async () => {
    if (!editingType) return;

    if (!validateForm()) {
      return;
    }

    try {
      const selectedCountry = countries.find(c => c.countryId.toString() === formData.countryId);
      
      const response = await fetch('/api/catalogs/document-types', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingType.id,
          ...formData,
          countryId: parseInt(formData.countryId),
          countryName: selectedCountry?.countryName || editingType.countryName,
          maxLength: parseInt(formData.maxLength),
          minLength: parseInt(formData.minLength)
        })
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error updating document type');
      }

      toast.success('Tipo de documento actualizado exitosamente');
      setIsEditDialogOpen(false);
      setEditingType(null);
      resetForm();
      fetchDocumentTypes();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const resetForm = () => {
    setFormData({
      name: '',
      shortName: '',
      countryId: '',
      format: '',
      maxLength: '',
      minLength: '',
      isRequired: false,
      description: '',
      example: '',
      isActive: true
    });
    setFormErrors({
      name: '',
      shortName: '',
      countryId: '',
      format: '',
      maxLength: '',
      minLength: ''
    });
  };

  // Form validation
  const validateForm = () => {
    const errors = {
      name: '',
      shortName: '',
      countryId: '',
      format: '',
      maxLength: '',
      minLength: ''
    };
    let isValid = true;

    if (!formData.name || formData.name.trim() === '') {
      errors.name = 'El nombre es requerido';
      isValid = false;
    }

    if (!formData.shortName || formData.shortName.trim() === '') {
      errors.shortName = 'El nombre corto es requerido';
      isValid = false;
    }

    if (!formData.countryId) {
      errors.countryId = 'El país es requerido';
      isValid = false;
    }

    if (!formData.format || formData.format.trim() === '') {
      errors.format = 'El formato es requerido';
      isValid = false;
    }

    if (!formData.maxLength || formData.maxLength.trim() === '') {
      errors.maxLength = 'La longitud máxima es requerida';
      isValid = false;
    } else {
      const maxLen = parseInt(formData.maxLength);
      if (isNaN(maxLen) || maxLen <= 0) {
        errors.maxLength = 'Debe ser un número mayor a 0';
        isValid = false;
      }
    }

    if (!formData.minLength || formData.minLength.trim() === '') {
      errors.minLength = 'La longitud mínima es requerida';
      isValid = false;
    } else {
      const minLen = parseInt(formData.minLength);
      const maxLen = parseInt(formData.maxLength);
      if (isNaN(minLen) || minLen <= 0) {
        errors.minLength = 'Debe ser un número mayor a 0';
        isValid = false;
      } else if (!isNaN(maxLen) && minLen > maxLen) {
        errors.minLength = 'No puede ser mayor a la longitud máxima';
        isValid = false;
      }
    }

    setFormErrors(errors);
    return isValid;
  };

  const openEditDialog = (docType: DocumentType) => {
    setEditingType(docType);
    setFormData({
      name: docType.name || '',
      shortName: docType.shortName || '',
      countryId: docType.countryId?.toString() || '',
      format: docType.format || '',
      maxLength: docType.maxLength?.toString() || '',
      minLength: docType.minLength?.toString() || '',
      isRequired: docType.isRequired || false,
      description: docType.description || '',
      example: docType.example || '',
      isActive: docType.isActive
    });
    setIsEditDialogOpen(true);
  };

  // Ver detalles del tipo
  const openViewDialog = (docType: DocumentType) => {
    setViewingType(docType);
    setIsViewDialogOpen(true);
  };

  // Confirmar eliminación
  const openDeleteDialog = (docType: DocumentType) => {
    setDeletingType(docType);
    setIsDeleteDialogOpen(true);
  };

  // Activar/Desactivar tipo
  const handleToggleStatus = async (docType: DocumentType) => {
    try {
      setIsProcessing(true);
      const response = await fetch('/api/catalogs/document-types', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: docType.id,
          name: docType.name,
          shortName: docType.shortName,
          countryId: docType.countryId,
          countryName: docType.countryName,
          format: docType.format,
          maxLength: docType.maxLength,
          minLength: docType.minLength,
          isRequired: docType.isRequired,
          description: docType.description,
          example: docType.example,
          isActive: !docType.isActive
        })
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Error al cambiar estado');
      }

      toast.success(`Tipo ${!docType.isActive ? 'activado' : 'desactivado'} exitosamente`);
      fetchDocumentTypes();
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // Eliminar tipo (físico o lógico)
  const handleDelete = async (type: 'hard' | 'soft') => {
    if (!deletingType) return;

    try {
      setIsProcessing(true);
      const response = await fetch(`/api/catalogs/document-types?id=${deletingType.id}&type=${type}`, {
        method: 'DELETE'
      });

      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || 'Error al eliminar tipo de documento');
      }

      toast.success(result.message);
      setIsDeleteDialogOpen(false);
      setDeletingType(null);
      fetchDocumentTypes();
    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // La paginación y filtrado ahora se maneja en el servidor
  const filteredTypes = documentTypes.sort((a, b) => {
    let aValue: any, bValue: any;
    
    switch (sortBy) {
      case 'name':
        aValue = a.name.toLowerCase();
        bValue = b.name.toLowerCase();
        break;
      case 'shortName':
        aValue = a.shortName.toLowerCase();
        bValue = b.shortName.toLowerCase();
        break;
      case 'countryName':
        aValue = a.countryName.toLowerCase();
        bValue = b.countryName.toLowerCase();
        break;
      case 'isActive':
        aValue = a.isActive ? 1 : 0;
        bValue = b.isActive ? 1 : 0;
        break;
      case 'createdAt':
        aValue = new Date(a.createdAt).getTime();
        bValue = new Date(b.createdAt).getTime();
        break;
      default:
        return 0;
    }
    
    if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
    return 0;
  })

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.back()}
              className="hover:bg-green-100 hover:text-green-600 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Gestión de Tipos de Documento</h1>
              <p className="text-gray-600 text-sm lg:text-base">Administra los tipos de documento del sistema</p>
            </div>
          </div>
        </div>
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button 
            onClick={handleRefresh} 
            variant="outline" 
            size="sm" 
            disabled={refreshing}
            className="hover:bg-gray-50 transition-colors w-full sm:w-auto"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant="outline" size="sm" className="w-full sm:w-auto">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button className="w-full sm:w-auto" onClick={() => resetForm()}>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo Tipo
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Filters */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col space-y-4 sm:flex-row sm:justify-between sm:items-end sm:space-y-0 sm:space-x-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por nombre, código o país..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-200 focus:border-blue-500 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="flex space-x-2">
              <Select value={activeFilter} onValueChange={(value: 'all' | 'active' | 'inactive') => setActiveFilter(value)}>
                <SelectTrigger className="w-40">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos</SelectItem>
                  <SelectItem value="active">Activos</SelectItem>
                  <SelectItem value="inactive">Inactivos</SelectItem>
                </SelectContent>
              </Select>
              <Select value={countryFilter} onValueChange={setCountryFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="País" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Todos los países</SelectItem>
                  {countries.map((country) => (
                    <SelectItem key={country.countryId} value={country.countryId.toString()}>
                      {country.countryName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Table */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-4">
              <CardTitle className="text-lg flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-600" />
                Lista de Tipos de Documento
              </CardTitle>
              <Badge variant="outline" className="border-gray-300">
                {totalCount} tipos
              </Badge>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {/* Desktop Table View */}
          <div className="hidden lg:block overflow-x-auto">
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('name')}
                    >
                      <div className="flex items-center gap-2">
                        Tipo
                        <SortIcon column="name" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('shortName')}
                    >
                      <div className="flex items-center gap-2">
                        Código
                        <SortIcon column="shortName" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('countryName')}
                    >
                      <div className="flex items-center gap-2">
                        País
                        <SortIcon column="countryName" />
                      </div>
                    </TableHead>
                    <TableHead 
                      className="font-semibold text-gray-700 cursor-pointer hover:bg-gray-50"
                      onClick={() => handleSort('isActive')}
                    >
                      <div className="flex items-center gap-2">
                        Estado
                        <SortIcon column="isActive" />
                      </div>
                    </TableHead>
                    <TableHead className="font-semibold text-gray-700 text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {loading ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                        <p className="mt-2 text-gray-600">Cargando tipos...</p>
                      </TableCell>
                    </TableRow>
                  ) : filteredTypes.length === 0 ? (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                        <p className="mt-2 text-gray-600">No se encontraron tipos</p>
                      </TableCell>
                    </TableRow>
                  ) : (
                    filteredTypes.map((docType) => (
                      <TableRow key={docType.id}>
                        <TableCell>
                          <Checkbox className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-400" />
                            <span className="font-medium">{docType.name}</span>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{docType.shortName}</Badge>
                        </TableCell>
                        <TableCell>
                          <span className="text-sm text-gray-600">{docType.countryName}</span>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center space-x-2">
                            <Badge variant={docType.isActive ? "default" : "secondary"}>
                              {docType.isActive ? "Activo" : "Inactivo"}
                            </Badge>
                            {docType.isRequired && (
                              <Badge variant="outline" className="text-xs">
                                Requerido
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                              <DropdownMenuItem onClick={() => openViewDialog(docType)}>
                                <Eye className="h-4 w-4 mr-2" />
                                Ver detalles
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => openEditDialog(docType)}>
                                <Edit className="h-4 w-4 mr-2" />
                                Editar
                              </DropdownMenuItem>
                              <DropdownMenuSeparator />
                              <DropdownMenuItem 
                                onClick={() => handleToggleStatus(docType)}
                                disabled={isProcessing}
                                className={docType.isActive ? "text-orange-600" : "text-green-600"}
                              >
                                {docType.isActive ? (
                                  <>
                                    <ToggleLeft className="h-4 w-4 mr-2" />
                                    Desactivar
                                  </>
                                ) : (
                                  <>
                                    <ToggleRight className="h-4 w-4 mr-2" />
                                    Activar
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="text-red-600" 
                                onClick={() => openDeleteDialog(docType)}
                              >
                                <Trash2 className="h-4 w-4 mr-2" />
                                Eliminar
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Mobile Cards View */}
          <div className="lg:hidden space-y-4">
            {loading ? (
              <div className="text-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin mx-auto" />
                <p className="mt-2 text-gray-600">Cargando...</p>
              </div>
            ) : filteredTypes.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto" />
                <p className="mt-2 text-gray-600">No se encontraron tipos</p>
              </div>
            ) : (
              filteredTypes.map((docType) => (
                <Card key={docType.id} className="hover:shadow-md transition-all duration-200">
                  <CardContent className="p-4">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        <Checkbox className="mt-1 border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500" />
                        <div className="space-y-1">
                          <div className="flex items-center space-x-2">
                            <FileText className="h-4 w-4 text-blue-400" />
                            <span className="font-medium">{docType.name}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">Código:</span>
                            <Badge variant="outline" className="text-xs">{docType.shortName}</Badge>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-500">País:</span>
                            <span className="text-sm">{docType.countryName}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge variant={docType.isActive ? "default" : "secondary"} className="text-xs">
                              {docType.isActive ? "Activo" : "Inactivo"}
                            </Badge>
                            {docType.isRequired && (
                              <Badge variant="outline" className="text-xs">
                                Requerido
                              </Badge>
                            )}
                          </div>
                        </div>
                      </div>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => openViewDialog(docType)}>
                            <Eye className="h-4 w-4 mr-2" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => openEditDialog(docType)}>
                            <Edit className="h-4 w-4 mr-2" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(docType)}
                            disabled={isProcessing}
                            className={docType.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            {docType.isActive ? (
                              <>
                                <ToggleLeft className="h-4 w-4 mr-2" />
                                Desactivar
                              </>
                            ) : (
                              <>
                                <ToggleRight className="h-4 w-4 mr-2" />
                                Activar
                              </>
                            )}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600" 
                            onClick={() => openDeleteDialog(docType)}
                          >
                            <Trash2 className="h-4 w-4 mr-2" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </CardContent>
                </Card>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-center lg:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="text-sm text-gray-600">
              Mostrando {((page - 1) * limit) + 1} a {Math.min(page * limit, totalCount)} de {totalCount} tipos
            </div>
            <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 por página</SelectItem>
                <SelectItem value="25">25 por página</SelectItem>
                <SelectItem value="50">50 por página</SelectItem>
                <SelectItem value="100">100 por página</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page - 1)}
              disabled={page === 1}
              className="hover:bg-gray-50"
            >
              Anterior
            </Button>
            <div className="flex items-center gap-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1)
                .filter(p => p === 1 || p === totalPages || (p >= page - 2 && p <= page + 2))
                .map((p, idx, arr) => (
                  <div key={p} className="flex items-center gap-2">
                    {idx > 0 && arr[idx - 1] !== p - 1 && <span className="text-gray-400">...</span>}
                    <Button
                      variant={p === page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setPage(p)}
                      className={p === page ? "" : "hover:bg-gray-50"}
                    >
                      {p}
                    </Button>
                  </div>
                ))}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setPage(page + 1)}
              disabled={page === totalPages}
              className="hover:bg-gray-50"
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}

      {/* Dialog Crear */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Crear Tipo de Documento
            </DialogTitle>
            <DialogDescription>
              Completa la información para crear un nuevo tipo de documento.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="create-name">Nombre *</Label>
              <Input
                id="create-name"
                placeholder="Ej: Documento de Identidad Personal"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className={formErrors.name ? 'border-red-500' : ''}
              />
              {formErrors.name && <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>}
            </div>
            <div>
              <Label htmlFor="create-shortName">Nombre Corto *</Label>
              <Input
                id="create-shortName"
                placeholder="Ej: DPI"
                value={formData.shortName}
                onChange={(e) => setFormData({...formData, shortName: e.target.value})}
                className={formErrors.shortName ? 'border-red-500' : ''}
              />
              {formErrors.shortName && <p className="text-sm text-red-500 mt-1">{formErrors.shortName}</p>}
            </div>
            <div>
              <Label htmlFor="create-country">País *</Label>
              <Select value={formData.countryId} onValueChange={(value) => setFormData({...formData, countryId: value})}>
                <SelectTrigger className={formErrors.countryId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Seleccionar país" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country.countryId} value={country.countryId.toString()}>
                      {country.countryName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.countryId && <p className="text-sm text-red-500 mt-1">{formErrors.countryId}</p>}
            </div>
            <div>
              <Label htmlFor="create-format">Formato (Regex) *</Label>
              <Input
                id="create-format"
                placeholder="Ej: ^[0-9]{13}$"
                value={formData.format}
                onChange={(e) => setFormData({...formData, format: e.target.value})}
                className={formErrors.format ? 'border-red-500' : ''}
              />
              {formErrors.format && <p className="text-sm text-red-500 mt-1">{formErrors.format}</p>}
            </div>
            <div>
              <Label htmlFor="create-minLength">Longitud Mínima *</Label>
              <Input
                id="create-minLength"
                type="number"
                placeholder="1"
                min="1"
                value={formData.minLength}
                onChange={(e) => setFormData({...formData, minLength: e.target.value})}
                className={formErrors.minLength ? 'border-red-500' : ''}
              />
              {formErrors.minLength && <p className="text-sm text-red-500 mt-1">{formErrors.minLength}</p>}
            </div>
            <div>
              <Label htmlFor="create-maxLength">Longitud Máxima *</Label>
              <Input
                id="create-maxLength"
                type="number"
                placeholder="20"
                min="1"
                value={formData.maxLength}
                onChange={(e) => setFormData({...formData, maxLength: e.target.value})}
                className={formErrors.maxLength ? 'border-red-500' : ''}
              />
              {formErrors.maxLength && <p className="text-sm text-red-500 mt-1">{formErrors.maxLength}</p>}
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="create-example">Ejemplo</Label>
              <Input
                id="create-example"
                placeholder="Ej: 1234567890123"
                value={formData.example}
                onChange={(e) => setFormData({...formData, example: e.target.value})}
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="create-description">Descripción</Label>
              <Textarea
                id="create-description"
                placeholder="Descripción opcional..."
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="create-isRequired"
                checked={formData.isRequired}
                onCheckedChange={(checked) => setFormData({...formData, isRequired: !!checked})}
              />
              <Label htmlFor="create-isRequired">Requerido para el país</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="create-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
              />
              <Label htmlFor="create-isActive">Activo</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreate}>
              Crear Tipo
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Editar */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Editar Tipo de Documento
            </DialogTitle>
            <DialogDescription>
              Modifica la información del tipo de documento.
            </DialogDescription>
          </DialogHeader>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="edit-name">Nombre *</Label>
              <Input
                id="edit-name"
                placeholder="Ej: Documento de Identidad Personal"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className={formErrors.name ? 'border-red-500' : ''}
              />
              {formErrors.name && <p className="text-sm text-red-500 mt-1">{formErrors.name}</p>}
            </div>
            <div>
              <Label htmlFor="edit-shortName">Nombre Corto *</Label>
              <Input
                id="edit-shortName"
                placeholder="Ej: DPI"
                value={formData.shortName}
                onChange={(e) => setFormData({...formData, shortName: e.target.value})}
                className={formErrors.shortName ? 'border-red-500' : ''}
              />
              {formErrors.shortName && <p className="text-sm text-red-500 mt-1">{formErrors.shortName}</p>}
            </div>
            <div>
              <Label htmlFor="edit-country">País *</Label>
              <Select value={formData.countryId} onValueChange={(value) => setFormData({...formData, countryId: value})}>
                <SelectTrigger className={formErrors.countryId ? 'border-red-500' : ''}>
                  <SelectValue placeholder="Seleccionar país" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country.countryId} value={country.countryId.toString()}>
                      {country.countryName}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {formErrors.countryId && <p className="text-sm text-red-500 mt-1">{formErrors.countryId}</p>}
            </div>
            <div>
              <Label htmlFor="edit-format">Formato (Regex) *</Label>
              <Input
                id="edit-format"
                placeholder="Ej: ^[0-9]{13}$"
                value={formData.format}
                onChange={(e) => setFormData({...formData, format: e.target.value})}
                className={formErrors.format ? 'border-red-500' : ''}
              />
              {formErrors.format && <p className="text-sm text-red-500 mt-1">{formErrors.format}</p>}
            </div>
            <div>
              <Label htmlFor="edit-minLength">Longitud Mínima *</Label>
              <Input
                id="edit-minLength"
                type="number"
                placeholder="1"
                min="1"
                value={formData.minLength}
                onChange={(e) => setFormData({...formData, minLength: e.target.value})}
                className={formErrors.minLength ? 'border-red-500' : ''}
              />
              {formErrors.minLength && <p className="text-sm text-red-500 mt-1">{formErrors.minLength}</p>}
            </div>
            <div>
              <Label htmlFor="edit-maxLength">Longitud Máxima *</Label>
              <Input
                id="edit-maxLength"
                type="number"
                placeholder="20"
                min="1"
                value={formData.maxLength}
                onChange={(e) => setFormData({...formData, maxLength: e.target.value})}
                className={formErrors.maxLength ? 'border-red-500' : ''}
              />
              {formErrors.maxLength && <p className="text-sm text-red-500 mt-1">{formErrors.maxLength}</p>}
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="edit-example">Ejemplo</Label>
              <Input
                id="edit-example"
                placeholder="Ej: 1234567890123"
                value={formData.example}
                onChange={(e) => setFormData({...formData, example: e.target.value})}
              />
            </div>
            <div className="md:col-span-2">
              <Label htmlFor="edit-description">Descripción</Label>
              <Textarea
                id="edit-description"
                placeholder="Descripción opcional..."
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                rows={3}
              />
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-isRequired"
                checked={formData.isRequired}
                onCheckedChange={(checked) => setFormData({...formData, isRequired: !!checked})}
              />
              <Label htmlFor="edit-isRequired">Requerido para el país</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="edit-isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) => setFormData({...formData, isActive: !!checked})}
              />
              <Label htmlFor="edit-isActive">Activo</Label>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit}>
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Ver Detalles */}
      <Dialog open={isViewDialogOpen} onOpenChange={setIsViewDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              Detalles del Tipo de Documento
            </DialogTitle>
          </DialogHeader>
          {viewingType && (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-500 text-sm">Nombre</Label>
                  <p className="font-medium text-gray-900">{viewingType.name}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Nombre Corto</Label>
                  <p className="font-medium text-gray-900">{viewingType.shortName}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">País</Label>
                  <p className="font-medium text-gray-900">{viewingType.countryName}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Formato</Label>
                  <p className="font-mono text-sm bg-gray-100 p-2 rounded">{viewingType.format}</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Longitud</Label>
                  <p className="font-medium text-gray-900">{viewingType.minLength} - {viewingType.maxLength} caracteres</p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Propiedades</Label>
                  <div className="space-y-1">
                    <Badge variant={viewingType.isActive ? "default" : "secondary"}>
                      {viewingType.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                    {viewingType.isRequired && (
                      <Badge variant="outline">Requerido</Badge>
                    )}
                  </div>
                </div>
                {viewingType.example && (
                  <div className="md:col-span-2">
                    <Label className="text-gray-500 text-sm">Ejemplo</Label>
                    <p className="font-mono text-sm bg-gray-100 p-2 rounded">{viewingType.example}</p>
                  </div>
                )}
                {viewingType.description && (
                  <div className="md:col-span-2">
                    <Label className="text-gray-500 text-sm">Descripción</Label>
                    <p className="text-gray-900">{viewingType.description}</p>
                  </div>
                )}
                <div>
                  <Label className="text-gray-500 text-sm">Fecha de Creación</Label>
                  <p className="text-sm text-gray-600">
                    {new Date(viewingType.createdAt).toLocaleDateString('es-ES', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
                <div>
                  <Label className="text-gray-500 text-sm">Última Actualización</Label>
                  <p className="text-sm text-gray-600">
                    {new Date(viewingType.updatedAt).toLocaleDateString('es-ES', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Dialog Confirmar Eliminación */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Selecciona el tipo de eliminación para el tipo {deletingType?.name}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
              <h4 className="font-semibold text-amber-800 mb-2">Eliminación Lógica (Recomendado)</h4>
              <p className="text-sm text-amber-700">
                El tipo se marcará como inactivo pero se mantendrá en la base de datos para preservar la integridad de los datos históricos.
              </p>
            </div>
            
            <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">Eliminación Física (Permanente)</h4>
              <p className="text-sm text-red-700">
                El tipo se eliminará completamente de la base de datos. Esta acción no se puede deshacer.
              </p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
              disabled={isProcessing}
            >
              Cancelar
            </Button>
            <Button
              variant="secondary"
              onClick={() => handleDelete('soft')}
              disabled={isProcessing}
            >
              {isProcessing ? 'Procesando...' : 'Eliminación Lógica'}
            </Button>
            <Button
              variant="destructive"
              onClick={() => handleDelete('hard')}
              disabled={isProcessing}
            >
              {isProcessing ? 'Procesando...' : 'Eliminación Física'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}