import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { religions } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/catalogs/religions/[id] - Obtener religión específica
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const data = await db
      .select()
      .from(religions)
      .where(eq(religions.id, id))
      .limit(1);

    if (!data.length) {
      return NextResponse.json(
        { error: 'Religión no encontrada' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data[0],
    });

  } catch (error: any) {
    console.error('Error fetching religion:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/catalogs/religions/[id] - Eliminar religión
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'logical'; // logical o physical

    // Verificar que la religión existe
    const existingReligion = await db
      .select()
      .from(religions)
      .where(eq(religions.id, id))
      .limit(1);

    if (!existingReligion.length) {
      return NextResponse.json(
        { error: 'Religión no encontrada' },
        { status: 404 }
      );
    }

    if (type === 'logical') {
      // Eliminación lógica - marcar como inactivo
      await db
        .update(religions)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(religions.id, id));

      return NextResponse.json({
        success: true,
        message: 'Religión desactivada exitosamente',
      });

    } else if (type === 'physical') {
      // Eliminación física - borrar registro completamente
      await db
        .delete(religions)
        .where(eq(religions.id, id));

      return NextResponse.json({
        success: true,
        message: 'Religión eliminada permanentemente',
      });

    } else {
      return NextResponse.json(
        { error: 'Tipo de eliminación no válido. Use "logical" o "physical"' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error deleting religion:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}