'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import {
  ArrowLeft,
  RefreshCw,
  Download,
  Plus,
  Search,
  Eye,
  Edit,
  Trash2,
  MoreHorizontal,
  ToggleLeft,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON><PERSON>eader,
  Dialog<PERSON>itle,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';

// Interfaces
interface DoctorServicePrice {
  id: string;
  doctorId: string;
  serviceId: string;
  customPrice: string;
  currency: string;
  isActive: boolean;
  effectiveFrom: string;
  effectiveUntil: string | null;
  notes: string | null;
  createdAt: string;
  updatedAt: string;
  // Datos relacionados
  doctorName: string;
  doctorLastName: string;
  doctorEmail: string;
  serviceName: string;
  serviceCategory: string;
  serviceBasePrice: string;
}

interface Doctor {
  id: string;
  name: string;
  firstName: string;
  lastName: string;
  email: string;
  medicalLicense: string;
  specialty: string;
  isActive: boolean;
}

interface MedicalService {
  id: string;
  name: string;
  category: string;
  basePrice: string;
  currency: string;
  isActive: boolean;
}

export default function DoctorServicePricesPage() {
  const router = useRouter();

  // Estados de datos
  const [prices, setPrices] = useState<DoctorServicePrice[]>([]);
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [services, setServices] = useState<MedicalService[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Estados de filtros
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive'>('all');
  const [doctorFilter, setDoctorFilter] = useState('all');
  const [serviceFilter, setServiceFilter] = useState('all');

  // Estados de paginación
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [totalPages, setTotalPages] = useState(1);
  const [totalCount, setTotalCount] = useState(0);

  // Estados de ordenamiento
  const [sortBy, setSortBy] = useState('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // Estados de modales
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  // Estados de elementos seleccionados
  const [selectedPrice, setSelectedPrice] = useState<DoctorServicePrice | null>(null);
  const [editingPrice, setEditingPrice] = useState<DoctorServicePrice | null>(null);
  const [priceToDelete, setPriceToDelete] = useState<DoctorServicePrice | null>(null);
  const [selectedPrices, setSelectedPrices] = useState<string[]>([]);

  // Estados de formulario
  const [formData, setFormData] = useState({
    doctorId: '',
    serviceId: '',
    customPrice: '',
    currency: 'GTQ',
    isActive: true,
    effectiveFrom: '',
    effectiveUntil: '',
    notes: '',
  });

  // Estados de loading para acciones
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);

  // Fetch de datos
  const fetchData = async () => {
    try {
      setRefreshing(true);
      
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
        search: searchTerm,
        status: statusFilter,
        sortBy: sortBy,
        sortOrder: sortOrder,
      });

      if (doctorFilter !== 'all') {
        params.append('doctorId', doctorFilter);
      }

      if (serviceFilter !== 'all') {
        params.append('serviceId', serviceFilter);
      }

      const response = await fetch(`/api/catalogs/doctor-service-prices?${params}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar los precios');
      }

      const data = await response.json();
      setPrices(data.data || []);
      setTotalPages(data.pagination?.totalPages || 1);
      setTotalCount(data.pagination?.total || 0);

    } catch (error: any) {
      toast.error('Error al cargar los precios: ' + error.message);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Fetch de médicos y servicios para selectores
  const fetchDoctorsAndServices = async () => {
    try {
      // Cargar médicos
      const doctorsResponse = await fetch('/api/catalogs/doctor-service-prices/doctors?onlyActive=true');
      if (doctorsResponse.ok) {
        const doctorsData = await doctorsResponse.json();
        setDoctors(doctorsData.data || []);
      }

      // Cargar servicios médicos
      const servicesResponse = await fetch('/api/catalogs/medical-services');
      if (servicesResponse.ok) {
        const servicesData = await servicesResponse.json();
        setServices(servicesData.data || []);
      }
    } catch (error) {
      console.error('Error cargando médicos y servicios:', error);
    }
  };

  useEffect(() => {
    fetchData();
  }, [page, limit, searchTerm, statusFilter, doctorFilter, serviceFilter, sortBy, sortOrder]);

  useEffect(() => {
    setPage(1);
  }, [searchTerm, statusFilter, doctorFilter, serviceFilter]);

  useEffect(() => {
    fetchDoctorsAndServices();
  }, []);

  // Handlers
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
  };

  const handleSort = (column: string) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(column);
      setSortOrder('asc');
    }
  };

  const SortIcon = ({ column }: { column: string }) => {
    if (sortBy !== column) return <ArrowUpDown className="h-4 w-4" />;
    return sortOrder === 'asc' ? <ArrowUp className="h-4 w-4" /> : <ArrowDown className="h-4 w-4" />;
  };

  // Handlers de selección múltiple
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPrices(prices.map(price => price.id));
    } else {
      setSelectedPrices([]);
    }
  };

  const handleSelectPrice = (priceId: string, checked: boolean) => {
    if (checked) {
      setSelectedPrices(prev => [...prev, priceId]);
    } else {
      setSelectedPrices(prev => prev.filter(id => id !== priceId));
    }
  };

  // Handlers de modales
  const openDetailsDialog = (price: DoctorServicePrice) => {
    setSelectedPrice(price);
    setIsDetailsDialogOpen(true);
  };

  const openEditDialog = (price: DoctorServicePrice) => {
    setEditingPrice(price);
    setFormData({
      doctorId: price.doctorId,
      serviceId: price.serviceId,
      customPrice: price.customPrice,
      currency: price.currency,
      isActive: price.isActive,
      effectiveFrom: price.effectiveFrom.split('T')[0],
      effectiveUntil: price.effectiveUntil ? price.effectiveUntil.split('T')[0] : '',
      notes: price.notes || '',
    });
    setIsEditDialogOpen(true);
  };

  const openDeleteDialog = (price: DoctorServicePrice) => {
    setPriceToDelete(price);
    setIsDeleteDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      doctorId: '',
      serviceId: '',
      customPrice: '',
      currency: 'GTQ',
      isActive: true,
      effectiveFrom: '',
      effectiveUntil: '',
      notes: '',
    });
  };

  // CRUD Operations
  const handleCreate = async () => {
    try {
      if (!formData.doctorId || !formData.serviceId || !formData.customPrice) {
        toast.error('Por favor complete todos los campos requeridos');
        return;
      }

      if (parseFloat(formData.customPrice) <= 0) {
        toast.error('El precio debe ser mayor a 0');
        return;
      }

      setIsCreating(true);

      const response = await fetch('/api/catalogs/doctor-service-prices', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...formData,
          customPrice: parseFloat(formData.customPrice),
          effectiveFrom: formData.effectiveFrom || undefined,
          effectiveUntil: formData.effectiveUntil || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error creando precio personalizado');
      }

      toast.success('Precio personalizado creado exitosamente');
      setIsCreateDialogOpen(false);
      resetForm();
      fetchData();

    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsCreating(false);
    }
  };

  const handleEdit = async () => {
    try {
      if (!editingPrice || !formData.customPrice) {
        toast.error('Por favor complete todos los campos requeridos');
        return;
      }

      if (parseFloat(formData.customPrice) <= 0) {
        toast.error('El precio debe ser mayor a 0');
        return;
      }

      setIsEditing(true);

      const response = await fetch('/api/catalogs/doctor-service-prices', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          id: editingPrice.id,
          customPrice: parseFloat(formData.customPrice),
          currency: formData.currency,
          isActive: formData.isActive,
          effectiveFrom: formData.effectiveFrom || undefined,
          effectiveUntil: formData.effectiveUntil || undefined,
          notes: formData.notes || undefined,
        }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error actualizando precio personalizado');
      }

      toast.success('Precio personalizado actualizado exitosamente');
      setIsEditDialogOpen(false);
      setEditingPrice(null);
      resetForm();
      fetchData();

    } catch (error: any) {
      toast.error(error.message);
    } finally {
      setIsEditing(false);
    }
  };

  const toggleStatus = async (price: DoctorServicePrice) => {
    try {
      const response = await fetch(`/api/catalogs/doctor-service-prices/${price.id}/toggle-status`, {
        method: 'POST'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error cambiando estado');
      }

      toast.success(`Precio personalizado ${price.isActive ? 'desactivado' : 'activado'} exitosamente`);
      fetchData();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleDelete = async (type: 'logical' | 'physical') => {
    try {
      if (!priceToDelete) return;

      const response = await fetch(`/api/catalogs/doctor-service-prices/${priceToDelete.id}?type=${type}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error eliminando precio');
      }

      toast.success(`Precio personalizado ${type === 'logical' ? 'desactivado' : 'eliminado permanentemente'} exitosamente`);
      setIsDeleteDialogOpen(false);
      setPriceToDelete(null);
      fetchData();
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  return (
    <div className=\"space-y-6 p-6\">
      {/* Header */}
      <div className=\"flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0\">
        <div>
          <div className=\"flex items-center space-x-3 mb-2\">
            <Button variant=\"ghost\" size=\"sm\" onClick={() => router.back()}>
              <ArrowLeft className=\"h-4 w-4\" />
            </Button>
            <div>
              <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">
                Gestión de Precios por Médico
              </h1>
              <p className=\"text-sm lg:text-base text-gray-600\">
                Administra precios personalizados por médico para servicios médicos del sistema
              </p>
            </div>
          </div>
        </div>
        
        <div className=\"flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3\">
          <Button onClick={handleRefresh} variant=\"outline\" size=\"sm\" disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button variant=\"outline\" size=\"sm\">
            <Download className=\"h-4 w-4 mr-2\" />
            Exportar
          </Button>
          <Button onClick={() => setIsCreateDialogOpen(true)}>
            <Plus className=\"h-4 w-4 mr-2\" />
            Nuevo Precio
          </Button>
        </div>
      </div>

      {/* Card de Filtros */}
      <Card className=\"hover:shadow-lg transition-all duration-300 border-0 shadow-sm\">
        <CardHeader>
          <CardTitle className=\"text-lg flex items-center gap-2\">
            <Search className=\"h-5 w-5 text-blue-600\" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\">
            {/* Búsqueda por texto */}
            <div className=\"relative\">
              <Search className=\"absolute left-3 top-3 h-4 w-4 text-gray-400\" />
              <Input
                placeholder=\"Buscar médico o servicio...\"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className=\"pl-10\"
              />
            </div>
            
            {/* Filtro por estado */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger>
                <SelectValue placeholder=\"Todos los estados\" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=\"all\">Todos los estados</SelectItem>
                <SelectItem value=\"active\">Activos</SelectItem>
                <SelectItem value=\"inactive\">Inactivos</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Filtro por médico */}
            <Select value={doctorFilter} onValueChange={setDoctorFilter}>
              <SelectTrigger>
                <SelectValue placeholder=\"Todos los médicos\" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=\"all\">Todos los médicos</SelectItem>
                {doctors.map((doctor) => (
                  <SelectItem key={doctor.id} value={doctor.id}>
                    {doctor.name} {doctor.specialty && `(${doctor.specialty})`}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            
            {/* Filtro por servicio */}
            <Select value={serviceFilter} onValueChange={setServiceFilter}>
              <SelectTrigger>
                <SelectValue placeholder=\"Todos los servicios\" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value=\"all\">Todos los servicios</SelectItem>
                {services.map((service) => (
                  <SelectItem key={service.id} value={service.id}>
                    {service.name} ({service.category})
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {/* Mostrar totales */}
            <div className=\"flex items-center justify-center\">
              <span className=\"text-sm text-gray-600\">
                Total: {totalCount} precios
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card principal con grid de datos */}
      <Card className=\"hover:shadow-lg transition-all duration-300 border-0 shadow-sm\">
        <CardContent className=\"p-0\">
          {loading ? (
            <div className=\"flex items-center justify-center py-8\">
              <RefreshCw className=\"h-8 w-8 animate-spin\" />
              <span className=\"ml-2\">Cargando precios...</span>
            </div>
          ) : (
            <>
              {/* Vista Desktop */}
              <div className=\"hidden lg:block overflow-x-auto\">
                <div className=\"rounded-md border\">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead className=\"w-12\">
                          <Checkbox 
                            className=\"border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500\"
                            checked={selectedPrices.length === prices.length && prices.length > 0}
                            onCheckedChange={handleSelectAll}
                          />
                        </TableHead>
                        <TableHead 
                          className=\"font-semibold text-gray-700 cursor-pointer hover:bg-gray-50\"
                          onClick={() => handleSort('doctor')}
                        >
                          <div className=\"flex items-center gap-2\">
                            Médico
                            <SortIcon column=\"doctor\" />
                          </div>
                        </TableHead>
                        <TableHead 
                          className=\"font-semibold text-gray-700 cursor-pointer hover:bg-gray-50\"
                          onClick={() => handleSort('service')}
                        >
                          <div className=\"flex items-center gap-2\">
                            Servicio
                            <SortIcon column=\"service\" />
                          </div>
                        </TableHead>
                        <TableHead 
                          className=\"font-semibold text-gray-700 cursor-pointer hover:bg-gray-50\"
                          onClick={() => handleSort('price')}
                        >
                          <div className=\"flex items-center gap-2\">
                            Precio Personalizado
                            <SortIcon column=\"price\" />
                          </div>
                        </TableHead>
                        <TableHead className=\"font-semibold text-gray-700\">
                          Precio Base
                        </TableHead>
                        <TableHead 
                          className=\"font-semibold text-gray-700 cursor-pointer hover:bg-gray-50\"
                          onClick={() => handleSort('isActive')}
                        >
                          <div className=\"flex items-center gap-2\">
                            Estado
                            <SortIcon column=\"isActive\" />
                          </div>
                        </TableHead>
                        <TableHead className=\"font-semibold text-gray-700\">
                          Vigencia
                        </TableHead>
                        <TableHead className=\"font-semibold text-gray-700 text-right\">
                          Acciones
                        </TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {prices.map((price) => (
                        <TableRow key={price.id} className=\"hover:bg-gray-50\">
                          <TableCell>
                            <Checkbox
                              className=\"border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500\"
                              checked={selectedPrices.includes(price.id)}
                              onCheckedChange={(checked) => handleSelectPrice(price.id, checked as boolean)}
                            />
                          </TableCell>
                          <TableCell>
                            <div className=\"space-y-1\">
                              <div className=\"font-medium\">
                                {price.doctorName} {price.doctorLastName}
                              </div>
                              <div className=\"text-sm text-gray-500\">
                                {price.doctorEmail}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className=\"space-y-1\">
                              <div className=\"font-medium\">{price.serviceName}</div>
                              <Badge variant=\"outline\" className=\"text-xs\">
                                {price.serviceCategory}
                              </Badge>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className=\"font-medium text-green-600\">
                              {price.currency} {parseFloat(price.customPrice).toFixed(2)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className=\"text-gray-500\">
                              {price.currency} {parseFloat(price.serviceBasePrice || '0').toFixed(2)}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge 
                              variant={price.isActive ? \"default\" : \"secondary\"}
                              className={price.isActive ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}
                            >
                              {price.isActive ? \"Activo\" : \"Inactivo\"}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className=\"text-sm\">
                              <div>Desde: {new Date(price.effectiveFrom).toLocaleDateString()}</div>
                              {price.effectiveUntil && (
                                <div>Hasta: {new Date(price.effectiveUntil).toLocaleDateString()}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell className=\"text-right\">
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">
                                  <MoreHorizontal className=\"h-4 w-4\" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align=\"end\">
                                <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                                <DropdownMenuItem onClick={() => openDetailsDialog(price)}>
                                  <Eye className=\"h-4 w-4 mr-2\" />
                                  Ver detalles
                                </DropdownMenuItem>
                                <DropdownMenuItem onClick={() => openEditDialog(price)}>
                                  <Edit className=\"h-4 w-4 mr-2\" />
                                  Editar
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  onClick={() => toggleStatus(price)}
                                  className={price.isActive ? \"text-orange-600\" : \"text-green-600\"}
                                >
                                  <ToggleLeft className=\"h-4 w-4 mr-2\" />
                                  {price.isActive ? 'Desactivar' : 'Activar'}
                                </DropdownMenuItem>
                                <DropdownMenuItem 
                                  className=\"text-red-600\" 
                                  onClick={() => openDeleteDialog(price)}
                                >
                                  <Trash2 className=\"h-4 w-4 mr-2\" />
                                  Eliminar
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>

              {/* Vista Mobile */}
              <div className=\"lg:hidden space-y-4 p-4\">
                {prices.map((price) => (
                  <Card key={price.id} className=\"hover:shadow-md transition-all duration-200\">
                    <CardContent className=\"p-4\">
                      <div className=\"flex items-start justify-between\">
                        <div className=\"flex items-start space-x-3\">
                          <Checkbox 
                            className=\"mt-1 border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500\"
                            checked={selectedPrices.includes(price.id)}
                            onCheckedChange={(checked) => handleSelectPrice(price.id, checked as boolean)}
                          />
                          <div className=\"space-y-2\">
                            <div className=\"flex items-center space-x-2\">
                              <span className=\"font-medium\">{price.doctorName} {price.doctorLastName}</span>
                            </div>
                            <div className=\"text-sm text-gray-500\">{price.serviceName}</div>
                            <div className=\"flex items-center space-x-2\">
                              <span className=\"font-medium text-green-600\">
                                {price.currency} {parseFloat(price.customPrice).toFixed(2)}
                              </span>
                              <Badge 
                                variant={price.isActive ? \"default\" : \"secondary\"}
                                className={price.isActive ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}
                              >
                                {price.isActive ? \"Activo\" : \"Inactivo\"}
                              </Badge>
                            </div>
                          </div>
                        </div>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">
                              <MoreHorizontal className=\"h-4 w-4\" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align=\"end\">
                            <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                            <DropdownMenuItem onClick={() => openDetailsDialog(price)}>
                              <Eye className=\"h-4 w-4 mr-2\" />
                              Ver detalles
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openEditDialog(price)}>
                              <Edit className=\"h-4 w-4 mr-2\" />
                              Editar
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => toggleStatus(price)}
                              className={price.isActive ? \"text-orange-600\" : \"text-green-600\"}
                            >
                              <ToggleLeft className=\"h-4 w-4 mr-2\" />
                              {price.isActive ? 'Desactivar' : 'Activar'}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              className=\"text-red-600\" 
                              onClick={() => openDeleteDialog(price)}
                            >
                              <Trash2 className=\"h-4 w-4 mr-2\" />
                              Eliminar
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {/* Paginación */}
              {totalPages > 1 && (
                <div className=\"flex flex-col sm:flex-row items-center justify-between px-6 py-4 gap-4\">
                  <div className=\"flex items-center space-x-4\">
                    <div className=\"text-sm text-gray-600\">
                      Mostrando {Math.min((page - 1) * limit + 1, totalCount)} a {Math.min(page * limit, totalCount)} de {totalCount} precios
                    </div>
                    <Select value={limit.toString()} onValueChange={(value) => setLimit(Number(value))}>
                      <SelectTrigger className=\"w-40\">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value=\"5\">5 por página</SelectItem>
                        <SelectItem value=\"10\">10 por página</SelectItem>
                        <SelectItem value=\"20\">20 por página</SelectItem>
                        <SelectItem value=\"50\">50 por página</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className=\"flex items-center space-x-2\">
                    <Button
                      variant=\"outline\"
                      size=\"sm\"
                      onClick={() => setPage(prev => Math.max(1, prev - 1))}
                      disabled={page === 1}
                    >
                      Anterior
                    </Button>
                    
                    <div className=\"flex items-center space-x-1\">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        let pageNumber;
                        if (totalPages <= 5) {
                          pageNumber = i + 1;
                        } else if (page <= 3) {
                          pageNumber = i + 1;
                        } else if (page >= totalPages - 2) {
                          pageNumber = totalPages - 4 + i;
                        } else {
                          pageNumber = page - 2 + i;
                        }
                        
                        return (
                          <Button
                            key={pageNumber}
                            variant={page === pageNumber ? \"default\" : \"outline\"}
                            size=\"sm\"
                            className=\"w-8 h-8 p-0\"
                            onClick={() => setPage(pageNumber)}
                          >
                            {pageNumber}
                          </Button>
                        );
                      })}
                    </div>
                    
                    <Button
                      variant=\"outline\"
                      size=\"sm\"
                      onClick={() => setPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={page === totalPages}
                    >
                      Siguiente
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Modal de Creación */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className=\"sm:max-w-[600px]\">
          <DialogHeader>
            <DialogTitle>Crear Nuevo Precio Personalizado</DialogTitle>
            <DialogDescription>
              Establece un precio personalizado para un médico y servicio específico
            </DialogDescription>
          </DialogHeader>
          
          <div className=\"grid gap-4 py-4\">
            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">
              {/* Médico */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-doctor\">Médico *</Label>
                <Select 
                  value={formData.doctorId} 
                  onValueChange={(value) => setFormData({...formData, doctorId: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder=\"Seleccione médico\" />
                  </SelectTrigger>
                  <SelectContent>
                    {doctors.map((doctor) => (
                      <SelectItem key={doctor.id} value={doctor.id}>
                        {doctor.name} {doctor.specialty && `(${doctor.specialty})`}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Servicio */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-service\">Servicio Médico *</Label>
                <Select 
                  value={formData.serviceId} 
                  onValueChange={(value) => setFormData({...formData, serviceId: value})}
                >
                  <SelectTrigger>
                    <SelectValue placeholder=\"Seleccione servicio\" />
                  </SelectTrigger>
                  <SelectContent>
                    {services.map((service) => (
                      <SelectItem key={service.id} value={service.id}>
                        {service.name} ({service.category}) - Base: {service.currency} {parseFloat(service.basePrice).toFixed(2)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Precio personalizado */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-price\">Precio Personalizado *</Label>
                <Input
                  id=\"create-price\"
                  type=\"number\"
                  step=\"0.01\"
                  min=\"0\"
                  value={formData.customPrice}
                  onChange={(e) => setFormData({...formData, customPrice: e.target.value})}
                  placeholder=\"0.00\"
                />
              </div>

              {/* Moneda */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-currency\">Moneda</Label>
                <Select 
                  value={formData.currency} 
                  onValueChange={(value) => setFormData({...formData, currency: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=\"GTQ\">GTQ - Quetzal</SelectItem>
                    <SelectItem value=\"USD\">USD - Dólar</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Fecha efectiva desde */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-effective-from\">Vigente Desde</Label>
                <Input
                  id=\"create-effective-from\"
                  type=\"date\"
                  value={formData.effectiveFrom}
                  onChange={(e) => setFormData({...formData, effectiveFrom: e.target.value})}
                />
              </div>

              {/* Fecha efectiva hasta */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-effective-until\">Vigente Hasta</Label>
                <Input
                  id=\"create-effective-until\"
                  type=\"date\"
                  value={formData.effectiveUntil}
                  onChange={(e) => setFormData({...formData, effectiveUntil: e.target.value})}
                />
              </div>

              {/* Estado */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"create-active\">Estado *</Label>
                <Select 
                  value={formData.isActive.toString()} 
                  onValueChange={(value) => setFormData({...formData, isActive: value === 'true'})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=\"true\">Activo</SelectItem>
                    <SelectItem value=\"false\">Inactivo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notas */}
            <div className=\"space-y-2\">
              <Label htmlFor=\"create-notes\">Notas</Label>
              <Textarea
                id=\"create-notes\"
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                placeholder=\"Notas adicionales sobre este precio personalizado...\"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant=\"outline\" onClick={() => setIsCreateDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleCreate} disabled={isCreating}>
              {isCreating ? (
                <>
                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />
                  Creando...
                </>
              ) : (
                'Crear Precio'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Edición */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className=\"sm:max-w-[600px]\">
          <DialogHeader>
            <DialogTitle>Editar Precio Personalizado</DialogTitle>
            <DialogDescription>
              Modifica los detalles del precio personalizado
            </DialogDescription>
          </DialogHeader>
          
          <div className=\"grid gap-4 py-4\">
            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">
              {/* Mostrar médico y servicio (solo lectura) */}
              <div className=\"space-y-2\">
                <Label>Médico</Label>
                <div className=\"p-2 bg-gray-100 rounded text-sm\">
                  {editingPrice?.doctorName} {editingPrice?.doctorLastName}
                </div>
              </div>

              <div className=\"space-y-2\">
                <Label>Servicio Médico</Label>
                <div className=\"p-2 bg-gray-100 rounded text-sm\">
                  {editingPrice?.serviceName} ({editingPrice?.serviceCategory})
                </div>
              </div>

              {/* Precio personalizado */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"edit-price\">Precio Personalizado *</Label>
                <Input
                  id=\"edit-price\"
                  type=\"number\"
                  step=\"0.01\"
                  min=\"0\"
                  value={formData.customPrice}
                  onChange={(e) => setFormData({...formData, customPrice: e.target.value})}
                  placeholder=\"0.00\"
                />
              </div>

              {/* Moneda */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"edit-currency\">Moneda</Label>
                <Select 
                  value={formData.currency} 
                  onValueChange={(value) => setFormData({...formData, currency: value})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=\"GTQ\">GTQ - Quetzal</SelectItem>
                    <SelectItem value=\"USD\">USD - Dólar</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Fecha efectiva desde */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"edit-effective-from\">Vigente Desde</Label>
                <Input
                  id=\"edit-effective-from\"
                  type=\"date\"
                  value={formData.effectiveFrom}
                  onChange={(e) => setFormData({...formData, effectiveFrom: e.target.value})}
                />
              </div>

              {/* Fecha efectiva hasta */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"edit-effective-until\">Vigente Hasta</Label>
                <Input
                  id=\"edit-effective-until\"
                  type=\"date\"
                  value={formData.effectiveUntil}
                  onChange={(e) => setFormData({...formData, effectiveUntil: e.target.value})}
                />
              </div>

              {/* Estado */}
              <div className=\"space-y-2\">
                <Label htmlFor=\"edit-active\">Estado *</Label>
                <Select 
                  value={formData.isActive.toString()} 
                  onValueChange={(value) => setFormData({...formData, isActive: value === 'true'})}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value=\"true\">Activo</SelectItem>
                    <SelectItem value=\"false\">Inactivo</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Notas */}
            <div className=\"space-y-2\">
              <Label htmlFor=\"edit-notes\">Notas</Label>
              <Textarea
                id=\"edit-notes\"
                value={formData.notes}
                onChange={(e) => setFormData({...formData, notes: e.target.value})}
                placeholder=\"Notas adicionales sobre este precio personalizado...\"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant=\"outline\" onClick={() => setIsEditDialogOpen(false)}>
              Cancelar
            </Button>
            <Button onClick={handleEdit} disabled={isEditing}>
              {isEditing ? (
                <>
                  <RefreshCw className=\"h-4 w-4 mr-2 animate-spin\" />
                  Actualizando...
                </>
              ) : (
                'Actualizar Precio'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Detalles */}
      <Dialog open={isDetailsDialogOpen} onOpenChange={setIsDetailsDialogOpen}>
        <DialogContent className=\"sm:max-w-[600px]\">
          <DialogHeader>
            <DialogTitle>Detalles del Precio Personalizado</DialogTitle>
            <DialogDescription>
              Información detallada del precio personalizado
            </DialogDescription>
          </DialogHeader>
          
          {selectedPrice && (
            <div className=\"space-y-4\">
              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Médico</Label>
                  <p className=\"text-sm text-gray-900\">{selectedPrice.doctorName} {selectedPrice.doctorLastName}</p>
                  <p className=\"text-xs text-gray-500\">{selectedPrice.doctorEmail}</p>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Servicio Médico</Label>
                  <p className=\"text-sm text-gray-900\">{selectedPrice.serviceName}</p>
                  <Badge variant=\"outline\" className=\"text-xs mt-1\">
                    {selectedPrice.serviceCategory}
                  </Badge>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Precio Personalizado</Label>
                  <p className=\"text-sm text-green-600 font-medium\">
                    {selectedPrice.currency} {parseFloat(selectedPrice.customPrice).toFixed(2)}
                  </p>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Precio Base del Servicio</Label>
                  <p className=\"text-sm text-gray-500\">
                    {selectedPrice.currency} {parseFloat(selectedPrice.serviceBasePrice || '0').toFixed(2)}
                  </p>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Vigencia</Label>
                  <div className=\"text-sm text-gray-900\">
                    <div>Desde: {new Date(selectedPrice.effectiveFrom).toLocaleDateString()}</div>
                    {selectedPrice.effectiveUntil && (
                      <div>Hasta: {new Date(selectedPrice.effectiveUntil).toLocaleDateString()}</div>
                    )}
                  </div>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Estado</Label>
                  <div>
                    <Badge 
                      variant={selectedPrice.isActive ? \"default\" : \"secondary\"}
                      className={selectedPrice.isActive ? \"bg-green-100 text-green-800\" : \"bg-gray-100 text-gray-800\"}
                    >
                      {selectedPrice.isActive ? \"Activo\" : \"Inactivo\"}
                    </Badge>
                  </div>
                </div>
              </div>

              {selectedPrice.notes && (
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Notas</Label>
                  <p className=\"text-sm text-gray-900 bg-gray-50 p-2 rounded mt-1\">
                    {selectedPrice.notes}
                  </p>
                </div>
              )}

              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t\">
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Fecha de Creación</Label>
                  <p className=\"text-sm text-gray-900\">
                    {new Date(selectedPrice.createdAt).toLocaleString()}
                  </p>
                </div>
                <div>
                  <Label className=\"text-sm font-medium text-gray-700\">Última Actualización</Label>
                  <p className=\"text-sm text-gray-900\">
                    {new Date(selectedPrice.updatedAt).toLocaleString()}
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter>
            <Button variant=\"outline\" onClick={() => setIsDetailsDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Eliminación */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className=\"sm:max-w-[500px]\">
          <DialogHeader>
            <DialogTitle className=\"flex items-center gap-2\">
              <Trash2 className=\"h-5 w-5 text-red-500\" />
              Confirmar Eliminación
            </DialogTitle>
            <DialogDescription>
              Esta acción no se puede deshacer. Seleccione el tipo de eliminación.
            </DialogDescription>
          </DialogHeader>
          
          {priceToDelete && (
            <div className=\"py-4\">
              <div className=\"p-4 bg-gray-50 rounded-lg mb-4\">
                <p className=\"text-sm font-medium text-gray-900 mb-2\">
                  Precio personalizado a eliminar:
                </p>
                <p className=\"text-lg font-semibold text-gray-800\">
                  {priceToDelete.doctorName} {priceToDelete.doctorLastName} - {priceToDelete.serviceName}
                </p>
                <p className=\"text-sm text-gray-600\">
                  Precio: {priceToDelete.currency} {parseFloat(priceToDelete.customPrice).toFixed(2)}
                </p>
              </div>
              
              <div className=\"space-y-3\">
                <div className=\"p-3 border border-orange-200 bg-orange-50 rounded-lg\">
                  <p className=\"text-sm font-medium text-orange-800 mb-1\">
                    Eliminación Lógica (Recomendada)
                  </p>
                  <p className=\"text-xs text-orange-700\">
                    Se marcará como inactivo pero se mantendrá para referencia histórica.
                  </p>
                </div>
                
                <div className=\"p-3 border border-red-200 bg-red-50 rounded-lg\">
                  <p className=\"text-sm font-medium text-red-800 mb-1\">
                    Eliminación Física (Permanente)
                  </p>
                  <p className=\"text-xs text-red-700\">
                    Se eliminará completamente. Esta acción es irreversible.
                  </p>
                </div>
              </div>
            </div>
          )}
          
          <DialogFooter className=\"flex gap-2\">
            <Button variant=\"outline\" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button 
              variant=\"outline\" 
              className=\"bg-orange-500 text-white hover:bg-orange-600\"
              onClick={() => handleDelete('logical')}
            >
              Eliminar Lógicamente
            </Button>
            <Button 
              variant=\"destructive\"
              onClick={() => handleDelete('physical')}
            >
              Eliminar Físicamente
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}