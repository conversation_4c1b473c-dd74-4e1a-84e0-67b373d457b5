import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, guardianPatientRelations } from '@/db/schema';
import { generateId, generateShortId } from '@/lib/utils';
import { formatPhoneForStorage, formatPhoneForDisplay } from '@/lib/phone-utils';
import { eq, and, or, sql } from 'drizzle-orm';
import { sendPatientCreatedEmail } from '@/lib/email-platform';

export async function POST(request: NextRequest) {
  try {
    const { userId: currentUserId } = await auth();
    
    if (!currentUserId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    console.log('Received request body:', body);
    
    const {
      firstName,
      lastName,
      email,
      phone,
      documentType,
      documentNumber,
      dateOfBirth,
      gender,
      address,
      countryId,
      departmentId,
      municipalityId,
      emergencyContact,
      emergencyEmail,
      emergencyPhone,
      emergencyRelationshipId,
      // Datos opcionales
      alternativePhone,
      occupationId,
      // Datos para guardián
      guardianId,
      guardianRelationship = 'Asistente médico',
      // Nuevos datos para menores
      isMinor,
      guardianOption,
      guardianData,
    } = body;
    
    console.log('Guardian data:', { guardianId, guardianRelationship });

    // Validaciones básicas
    if (!firstName || !lastName) {
      return NextResponse.json(
        { error: 'Nombre y apellido son requeridos' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un usuario con ese email
    if (email) {
      const existingEmail = await db
        .select()
        .from(user)
        .where(eq(user.email, email))
        .limit(1);

      if (existingEmail.length > 0) {
        return NextResponse.json(
          { 
            error: 'Este email ya está registrado. Si es el responsable de este paciente, use un email diferente (ejemplo: <EMAIL>) o deje el campo vacío para generar uno temporal que luego podrá activar.',
            type: 'email_exists',
            existingUser: {
              id: existingEmail[0].id,
              name: `${existingEmail[0].firstName} ${existingEmail[0].lastName}`,
              email: existingEmail[0].email
            }
          },
          { status: 400 }
        );
      }
    }

    // Verificar si ya existe un usuario con ese documento (solo si ambos campos tienen valor)
    if (documentType && documentNumber && documentType.trim() && documentNumber.trim()) {
      const existingDoc = await db
        .select()
        .from(user)
        .where(and(
          eq(user.documentType, documentType),
          eq(user.documentNumber, documentNumber)
        ))
        .limit(1);

      if (existingDoc.length > 0) {
        return NextResponse.json(
          { 
            error: 'Ya existe un paciente con ese documento',
            type: 'document_exists',
            existingUser: {
              id: existingDoc[0].id,
              name: `${existingDoc[0].firstName} ${existingDoc[0].lastName}`,
              documentNumber: existingDoc[0].documentNumber,
              documentType: existingDoc[0].documentType
            }
          },
          { status: 400 }
        );
      }
    }

    // Generar email único si no se proporciona, si hay guardián, o si es menor con email duplicado
    let patientEmail = email;
    
    // Auto-resolver conflicto de emails para menores
    if (isMinor && guardianOption === 'emergency' && email && emergencyEmail && email === emergencyEmail) {
      console.log('🔄 Resolviendo conflicto de emails: menor y guardián tienen el mismo email');
      patientEmail = null; // Forzar generación de email temporal para el menor
    }
    
    if (!patientEmail || guardianId) {
      // Si hay guardianId, siempre usar email temporal independientemente del email proporcionado
      let attempts = 0;
      do {
        patientEmail = `patient_${generateShortId()}@temp.local`;
        const emailExists = await db
          .select()
          .from(user)
          .where(eq(user.email, patientEmail))
          .limit(1);
        
        if (emailExists.length === 0) break;
        attempts++;
      } while (attempts < 5); // Máximo 5 intentos
      
      if (attempts >= 5) {
        return NextResponse.json(
          { error: 'Error generando email único' },
          { status: 500 }
        );
      }
      
      if (isMinor && email === emergencyEmail) {
        console.log(`✅ Email temporal generado para menor: ${patientEmail}, guardián mantiene: ${emergencyEmail}`);
      }
    }

    // Crear transacción para usuario y rol
    const result = await db.transaction(async (tx) => {
      // 1. Crear usuario sin Clerk ID
      const newUserId = generateId();
      const [newUser] = await tx
        .insert(user)
        .values({
          id: newUserId,
          email: patientEmail,
          emailVerified: false,
          firstName,
          lastName,
          name: `${firstName} ${lastName}`,
          documentType: documentType || null,
          documentNumber: documentNumber || null,
          dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : null,
          gender,
          phone: phone ? formatPhoneForStorage(phone) : null,
          alternativePhone: alternativePhone ? formatPhoneForStorage(alternativePhone) : null,
          address,
          countryId: countryId ? parseInt(countryId) : null,
          departmentId: departmentId ? parseInt(departmentId) : null,
          municipalityId: municipalityId ? parseInt(municipalityId) : null,
          occupationId: occupationId ? parseInt(occupationId) : null,
          emergencyContact,
          emergencyPhone: emergencyPhone ? formatPhoneForStorage(emergencyPhone) : null,
          emergencyRelationshipId: emergencyRelationshipId ? parseInt(emergencyRelationshipId) : null,
          overallStatus: 'active', // Paciente creado por personal médico - activo de inmediato
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      // 2. Crear rol de paciente
      await tx.insert(userRoles).values({
        id: generateId(),
        userId: newUserId,
        role: 'patient',
        status: 'active', // Rol activo aunque no tenga cuenta
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // 3. Manejar guardián para menores de edad
      if (isMinor && guardianOption === 'emergency') {
        // Usar contacto de emergencia como guardián - crear usuario si es necesario
        if (emergencyContact && emergencyEmail && emergencyPhone && emergencyRelationshipId) {
          // Buscar si ya existe un usuario con ese email o teléfono (excluyendo al paciente recién creado)
          const existingGuardian = await tx
            .select()
            .from(user)
            .where(and(
              or(
                eq(user.email, emergencyEmail),
                eq(user.phone, emergencyPhone)
              ),
              // Excluir al paciente que acabamos de crear
              sql`${user.id} != ${newUserId}`
            ))
            .limit(1);

          let guardianUserId;
          if (existingGuardian.length > 0) {
            guardianUserId = existingGuardian[0].id;
            
            // Actualizar email si era temporal y ahora tenemos uno real
            if (existingGuardian[0].email?.includes('@temp.local') && emergencyEmail) {
              await tx.update(user)
                .set({ 
                  email: emergencyEmail,
                  emailVerified: true,
                  updatedAt: new Date()
                })
                .where(eq(user.id, existingGuardian[0].id));
            }
          } else {
            // Crear usuario guardián con email real
            const guardianId = generateId();
            
            const [newGuardian] = await tx
              .insert(user)
              .values({
                id: guardianId,
                email: emergencyEmail,
                emailVerified: true, // Email real proporcionado
                firstName: emergencyContact.split(' ')[0] || emergencyContact,
                lastName: emergencyContact.split(' ').slice(1).join(' ') || '',
                name: emergencyContact,
                phone: emergencyPhone ? formatPhoneForStorage(emergencyPhone) : null,
                overallStatus: 'active',
                createdAt: new Date(),
                updatedAt: new Date(),
              })
              .returning();

            // Crear rol de guardián
            await tx.insert(userRoles).values({
              id: generateId(),
              userId: guardianId,
              role: 'guardian',
              status: 'active',
              createdAt: new Date(),
              updatedAt: new Date(),
            });

            guardianUserId = newGuardian.id;
          }

          // Crear relación guardián-paciente
          await tx.insert(guardianPatientRelations).values({
            id: generateId(),
            guardianId: guardianUserId,
            patientId: newUserId,
            relationship: emergencyRelationshipId.toString(),
            isPrimary: true,
            canMakeDecisions: true,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          });
        }
      } else if (isMinor && guardianOption === 'different' && guardianData) {
        // Crear usuario guardián diferente
        const guardianId = generateId();
        const guardianEmail = guardianData.email || `guardian_${generateShortId()}@temp.local`;
        
        const [newGuardian] = await tx
          .insert(user)
          .values({
            id: guardianId,
            email: guardianEmail,
            emailVerified: !!guardianData.email,
            firstName: guardianData.firstName,
            lastName: guardianData.lastName,
            name: `${guardianData.firstName} ${guardianData.lastName}`,
            phone: guardianData.phone ? formatPhoneForStorage(guardianData.phone) : null,
            overallStatus: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
          })
          .returning();

        // Crear rol de guardián
        await tx.insert(userRoles).values({
          id: generateId(),
          userId: guardianId,
          role: 'guardian',
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        // Crear relación guardián-paciente
        await tx.insert(guardianPatientRelations).values({
          id: generateId(),
          guardianId: guardianId,
          patientId: newUserId,
          relationship: guardianData.relationship,
          isPrimary: true,
          canMakeDecisions: true,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      } else if (guardianId) {
        // Lógica original para guardián existente
        await tx.insert(guardianPatientRelations).values({
          id: generateId(),
          guardianId,
          patientId: newUserId,
          relationship: guardianRelationship,
          isPrimary: true,
          canMakeDecisions: true,
          status: 'active',
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }

      return newUser;
    });

    // Calcular edad si tiene fecha de nacimiento
    let age = null;
    if (result.dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(result.dateOfBirth);
      age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
    }

    // *** ENVIAR EMAIL CON NUEVO SISTEMA ***
    try {
      await sendPatientCreatedEmail(
        result.id,
        `${result.firstName} ${result.lastName}`,
        currentUserId
      );
      console.log('✅ Email de paciente creado enviado via nuevo sistema');
    } catch (error) {
      console.error('❌ Error enviando email de paciente creado:', error);
      // No fallar la operación por error de email
    }

    return NextResponse.json({
      success: true,
      patient: {
        id: result.id,
        firstName: result.firstName,
        lastName: result.lastName,
        email: result.email,
        phone: result.phone ? formatPhoneForDisplay(result.phone) : null,
        documentType: result.documentType,
        documentNumber: result.documentNumber,
        dateOfBirth: result.dateOfBirth,
        age,
        gender: result.gender,
        address: result.address,
        hasClerkAccount: false,
        status: result.overallStatus,
        hasGuardian: !!guardianId,
        createdAt: result.createdAt,
      },
      message: 'Paciente registrado exitosamente',
    });

  } catch (error) {
    console.error('Error registrando paciente:', error);
    
    // Manejar errores específicos de base de datos
    if (error && typeof error === 'object' && 'code' in error) {
      const dbError = error as any;
      
      if (dbError.code === '23505') { // Unique constraint violation
        if (dbError.constraint === 'user_email_unique') {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Ya existe un usuario con ese email'
            },
            { status: 400 }
          );
        }
        
        if (dbError.constraint && dbError.constraint.includes('document')) {
          return NextResponse.json(
            { 
              success: false, 
              error: 'Ya existe un usuario con ese documento'
            },
            { status: 400 }
          );
        }
        
        return NextResponse.json(
          { 
            success: false, 
            error: 'Ya existe un registro con esa información'
          },
          { status: 400 }
        );
      }
    }
    
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al registrar paciente',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}