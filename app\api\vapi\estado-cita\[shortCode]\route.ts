import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { appointments, user, consultories } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';

export async function GET(
  request: NextRequest,
  { params }: { params: { shortCode: string } }
) {
  try {
    // Validar que la petición viene de VAPI
    const apiKey = request.headers.get('x-vapi-key');
    const expectedKey = process.env.VAPI_API_KEY;
    
    if (!expectedKey || apiKey !== expectedKey) {
      return NextResponse.json(
        { success: false, error: 'No autorizado - API Key inválida' },
        { status: 401 }
      );
    }

    const shortCode = params.shortCode;

    if (!shortCode) {
      return NextResponse.json(
        { success: false, error: 'ShortCode requerido' },
        { status: 400 }
      );
    }

    console.log(`🔍 Consultando estado de cita - ShortCode: ${shortCode}`);

    // 1. Buscar la cita por shortCode
    const appointmentResult = await db
      .select({
        id: appointments.id,
        title: appointments.title,
        status: appointments.status,
        scheduledDate: appointments.scheduledDate,
        startTime: appointments.startTime,
        endTime: appointments.endTime,
        shortCode: appointments.shortCode,
        doctorId: appointments.doctorId,
        patientId: appointments.patientId,
        consultoryId: appointments.consultoryId,
        chiefComplaint: appointments.chiefComplaint,
        tempPatientData: appointments.tempPatientData,
        confirmedAt: appointments.confirmedAt,
        emailCaptured: appointments.emailCaptured,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinToken: appointments.preCheckinToken,
        createdAt: appointments.createdAt,
        updatedAt: appointments.updatedAt,
        cancelledAt: appointments.cancelledAt,
        cancellationReason: appointments.cancellationReason,
      })
      .from(appointments)
      .where(eq(appointments.shortCode, shortCode.toUpperCase()))
      .limit(1);

    if (appointmentResult.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Cita no encontrada con ese código' },
        { status: 404 }
      );
    }

    const appointment = appointmentResult[0];

    // 2. Obtener información del paciente
    let patientInfo = null;
    if (appointment.patientId) {
      const patientResult = await db
        .select({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone,
          dateOfBirth: user.dateOfBirth,
        })
        .from(user)
        .where(eq(user.id, appointment.patientId))
        .limit(1);
      
      if (patientResult.length > 0) {
        const patient = patientResult[0];
        patientInfo = {
          id: patient.id,
          nombre: `${patient.firstName} ${patient.lastName}`,
          email: patient.email,
          telefono: patient.phone,
          tieneEmailReal: !!(patient.email && !patient.email.includes('@temp.local'))
        };
      }
    } else if (appointment.tempPatientData) {
      // Paciente temporal (creado por VAPI pero no confirmado)
      try {
        const tempData = JSON.parse(appointment.tempPatientData);
        patientInfo = {
          id: null,
          nombre: tempData.name || 'Paciente Temporal',
          email: null,
          telefono: tempData.phone || null,
          tieneEmailReal: false,
          esTemporal: true
        };
      } catch (e) {
        console.warn('Error parsing tempPatientData:', e);
      }
    }

    // 3. Obtener información del doctor
    let doctorInfo = null;
    if (appointment.doctorId) {
      const doctorResult = await db
        .select({
          id: user.id,
          firstName: user.firstName,
          lastName: user.lastName,
        })
        .from(user)
        .where(eq(user.id, appointment.doctorId))
        .limit(1);
      
      if (doctorResult.length > 0) {
        const doctor = doctorResult[0];
        doctorInfo = {
          id: doctor.id,
          nombre: `${doctor.firstName} ${doctor.lastName}`
        };
      }
    }

    // 4. Obtener información del consultorio
    let consultorioInfo = null;
    if (appointment.consultoryId) {
      const consultoryResult = await db
        .select({
          id: consultories.id,
          name: consultories.name,
        })
        .from(consultories)
        .where(eq(consultories.id, appointment.consultoryId))
        .limit(1);
      
      if (consultoryResult.length > 0) {
        const consultory = consultoryResult[0];
        consultorioInfo = {
          id: consultory.id,
          nombre: consultory.name
        };
      }
    }

    // 5. Calcular tiempos relevantes
    const ahora = new Date();
    const tiempoHastaCita = appointment.startTime.getTime() - ahora.getTime();
    const horasHastaCita = tiempoHastaCita / (1000 * 60 * 60);
    const minutosHastaCita = tiempoHastaCita / (1000 * 60);

    // Determinar si la cita expiró (más de 24h sin confirmar para VAPI)
    const horasDesdeCreacion = (ahora.getTime() - appointment.createdAt.getTime()) / (1000 * 60 * 60);
    const haExpirado = appointment.status === 'pending_confirmation' && horasDesdeCreacion > 24;

    // 6. Determinar estado legible
    const estadosLegibles: Record<string, string> = {
      'pending_confirmation': 'Pendiente de confirmación',
      'confirmed': 'Confirmada',
      'scheduled': 'Agendada',
      'checked_in': 'Paciente llegó',
      'in_progress': 'En consulta',
      'completed': 'Completada',
      'cancelled': 'Cancelada',
      'no_show': 'Paciente no llegó'
    };

    const estadoLegible = haExpirado ? 'Expirada' : (estadosLegibles[appointment.status] || appointment.status);

    console.log(`📊 Estado de cita ${shortCode}: ${estadoLegible}`);

    // 7. Formatear respuesta
    return NextResponse.json({
      success: true,
      data: {
        shortCode: appointment.shortCode,
        citaId: appointment.id,
        estado: appointment.status,
        estadoLegible,
        haExpirado,
        
        // Información de la cita
        fecha: format(appointment.scheduledDate, 'yyyy-MM-dd'),
        fechaLegible: format(appointment.scheduledDate, "EEEE, dd 'de' MMMM 'de' yyyy", { locale: es }),
        hora: format(appointment.startTime, 'HH:mm'),
        horaLegible: format(appointment.startTime, 'h:mm a'),
        duracion: Math.round((appointment.endTime.getTime() - appointment.startTime.getTime()) / (1000 * 60)),
        motivoConsulta: appointment.chiefComplaint,
        
        // Participantes
        paciente: patientInfo,
        doctor: doctorInfo,
        consultorio: consultorioInfo,
        
        // Estados y confirmaciones
        confirmadoEn: appointment.confirmedAt?.toISOString() || null,
        emailCapturado: appointment.emailCaptured,
        preCheckinCompletado: appointment.preCheckinCompleted || false,
        
        // Cancelación (si aplica)
        canceladoEn: appointment.cancelledAt?.toISOString() || null,
        motivoCancelacion: appointment.cancellationReason,
        
        // Tiempos
        horasHastaCita: Math.round(horasHastaCita * 100) / 100,
        minutosHastaCita: Math.round(minutosHastaCita),
        validoHasta: appointment.status === 'pending_confirmation' ? 
          new Date(appointment.createdAt.getTime() + 24 * 60 * 60 * 1000).toISOString() : null,
        
        // Metadata
        creadoEn: appointment.createdAt.toISOString(),
        actualizadoEn: appointment.updatedAt.toISOString(),
      }
    });

  } catch (error) {
    console.error('Error en estado-cita VAPI:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error interno del servidor',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}