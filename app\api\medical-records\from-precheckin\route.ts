import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalRecords, user, consultories, patientMedicalHistory, userRoles, appointments } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// POST - Crear/actualizar expediente médico desde pre-checkin
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Verificar rol del usuario
    const userRoleRecord = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, userId))
      .limit(1);

    if (!userRoleRecord.length) {
      return NextResponse.json({ error: 'Usuario sin roles asignados' }, { status: 403 });
    }

    const userRole = userRoleRecord[0].role;

    // Solo doctores pueden crear/actualizar expedientes
    if (!['doctor', 'assistant', 'admin'].includes(userRole)) {
      return NextResponse.json({ error: 'No autorizado para gestionar expedientes' }, { status: 403 });
    }

    const body = await request.json();
    const {
      patientId,
      appointmentId,
      demographics,
      vitalSigns,
      allergies,
      currentMedications,
      medicalEmergencyContact,
      medicalEmergencyPhone,
      doctorNotes
    } = body;

    // Validaciones básicas
    if (!patientId) {
      return NextResponse.json({ 
        error: 'patientId es requerido' 
      }, { status: 400 });
    }

    // Obtener información del paciente
    const patientResult = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        dateOfBirth: user.dateOfBirth,
        consultoryId: userRoles.consultoryId
      })
      .from(user)
      .leftJoin(userRoles, and(
        eq(userRoles.userId, user.id),
        eq(userRoles.role, 'patient')
      ))
      .where(eq(user.id, patientId))
      .limit(1);

    if (patientResult.length === 0) {
      return NextResponse.json({ error: 'Paciente no encontrado' }, { status: 400 });
    }

    const patient = patientResult[0];

    // Obtener información de la cita si se proporciona
    let appointmentInfo = null;
    if (appointmentId) {
      const appointmentResult = await db
        .select({
          id: appointments.id,
          doctorId: appointments.doctorId,
          consultoryId: appointments.consultoryId,
          preCheckinData: appointments.preCheckinData
        })
        .from(appointments)
        .where(eq(appointments.id, appointmentId))
        .limit(1);

      if (appointmentResult.length > 0) {
        appointmentInfo = appointmentResult[0];
      }
    }

    // Determinar consultorio - prioridad: cita, paciente, doctor actual
    let consultoryId = appointmentInfo?.consultoryId || 
                       patient.consultoryId || 
                       userRoleRecord[0].consultoryId;

    if (!consultoryId) {
      return NextResponse.json({ error: 'No se pudo determinar el consultorio' }, { status: 400 });
    }

    // Verificar que el consultorio existe
    const consultoryResult = await db
      .select()
      .from(consultories)
      .where(and(eq(consultories.id, consultoryId), eq(consultories.isActive, true)))
      .limit(1);

    if (consultoryResult.length === 0) {
      return NextResponse.json({ error: 'Consultorio no encontrado o inactivo' }, { status: 400 });
    }

    // Verificar si ya existe un expediente activo para el paciente
    const existingRecord = await db
      .select()
      .from(medicalRecords)
      .where(and(
        eq(medicalRecords.patientId, patientId),
        eq(medicalRecords.status, 'active')
      ))
      .limit(1);

    let medicalRecordId;
    let isNewRecord = false;

    if (existingRecord.length === 0) {
      // Crear nuevo expediente
      const recordNumber = await generateRecordNumber();
      
      // Calcular edad para determinar si es menor
      const isMinor = calculateAge(patient.dateOfBirth) < 18;

      const newRecord = {
        id: nanoid(),
        patientId,
        consultoryId,
        primaryDoctorId: appointmentInfo?.doctorId || userId,
        recordNumber,
        openDate: new Date(),
        status: 'active' as const,
        patientSummary: {
          fullName: `${patient.firstName} ${patient.lastName}`,
          dateOfBirth: patient.dateOfBirth,
          age: calculateAge(patient.dateOfBirth)
        },
        isMinor,
        guardianInfo: null, // Se actualizará después si es necesario
        demographics: demographics || {},
        vitalSigns: vitalSigns || {},
        allergies: allergies || [],
        currentMedications: currentMedications || [],
        medicalEmergencyContact: medicalEmergencyContact || '',
        medicalEmergencyPhone: medicalEmergencyPhone || '',
        doctorNotes: doctorNotes || '',
        totalConsultations: 0,
        lastAccessDate: new Date(),
        createdBy: userId,
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = await db.insert(medicalRecords).values(newRecord).returning();
      medicalRecordId = result[0].id;
      isNewRecord = true;

      // Crear estructura inicial de antecedentes médicos
      await db.insert(patientMedicalHistory).values({
        id: nanoid(),
        medicalRecordId,
        pathologicalHistory: [],
        nonPathologicalHistory: [],
        familyHistory: [],
        allergies: allergies || [],
        hospitalizations: [],
        surgeries: [],
        vaccinations: [],
        updatedBy: userId,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

    } else {
      // Actualizar expediente existente
      medicalRecordId = existingRecord[0].id;

      const updateData = {
        demographics: demographics || existingRecord[0].demographics,
        vitalSigns: vitalSigns || existingRecord[0].vitalSigns,
        allergies: allergies || existingRecord[0].allergies,
        currentMedications: currentMedications || existingRecord[0].currentMedications,
        medicalEmergencyContact: medicalEmergencyContact || existingRecord[0].medicalEmergencyContact,
        medicalEmergencyPhone: medicalEmergencyPhone || existingRecord[0].medicalEmergencyPhone,
        doctorNotes: doctorNotes ? 
          (existingRecord[0].doctorNotes ? 
            `${existingRecord[0].doctorNotes}\n\n[${new Date().toLocaleString()}] ${doctorNotes}` : 
            doctorNotes
          ) : existingRecord[0].doctorNotes,
        lastAccessDate: new Date(),
        updatedBy: userId,
        updatedAt: new Date(),
      };

      await db
        .update(medicalRecords)
        .set(updateData)
        .where(eq(medicalRecords.id, medicalRecordId));

      // Actualizar antecedentes médicos si hay alergias nuevas
      if (allergies && allergies.length > 0) {
        await db
          .update(patientMedicalHistory)
          .set({
            allergies: allergies,
            updatedBy: userId,
            updatedAt: new Date(),
          })
          .where(eq(patientMedicalHistory.medicalRecordId, medicalRecordId));
      }
    }

    // Obtener el registro actualizado
    const finalRecord = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.id, medicalRecordId))
      .limit(1);

    return NextResponse.json({
      message: isNewRecord ? 'Expediente médico creado exitosamente' : 'Expediente médico actualizado exitosamente',
      data: finalRecord[0],
      isNewRecord,
      medicalRecordId
    }, { status: isNewRecord ? 201 : 200 });

  } catch (error) {
    console.error('Error processing medical record from pre-checkin:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// Función auxiliar para generar número de expediente único
async function generateRecordNumber(): Promise<string> {
  const year = new Date().getFullYear();
  const prefix = `EXP-${year}-`;
  
  // Buscar el último número para este año
  const lastRecord = await db
    .select({ recordNumber: medicalRecords.recordNumber })
    .from(medicalRecords)
    .where(eq(medicalRecords.recordNumber, `${prefix}%`))
    .orderBy(medicalRecords.recordNumber)
    .limit(1);

  if (lastRecord.length === 0) {
    return `${prefix}001`;
  }

  // Extraer el número secuencial y incrementar
  const lastNumber = lastRecord[0].recordNumber.split('-')[2];
  const nextNumber = (parseInt(lastNumber) + 1).toString().padStart(3, '0');
  
  return `${prefix}${nextNumber}`;
}

// Función auxiliar para calcular edad
function calculateAge(dateOfBirth: Date | null): number {
  if (!dateOfBirth) return 0;
  
  const today = new Date();
  const birthDate = new Date(dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
}