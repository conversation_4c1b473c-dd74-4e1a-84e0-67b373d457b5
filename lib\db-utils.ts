// Utilidades para manejo de conexiones de base de datos
import { db } from '@/db/drizzle';

// Función helper para ejecutar queries con reintentos
export async function executeWithRetry<T>(
  queryFn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> {
  let lastError: any;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await queryFn();
    } catch (error: any) {
      lastError = error;
      
      // Log del error
      console.error(`Intento ${attempt}/${maxRetries} falló:`, error.message);
      
      // Si es el último intento, lanzar el error
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Si es un error de conexión, esperar antes de reintentar
      if (
        error.message?.includes('Connection terminated') ||
        error.message?.includes('timeout') ||
        error.code === 'ECONNRESET' ||
        error.code === '57P01' // terminating connection due to administrator command
      ) {
        console.log(`Esperando ${delay}ms antes de reintentar...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        // Aumentar el delay para el próximo intento
        delay *= 1.5;
      } else {
        // Si no es un error de conexión, no reintentar
        throw error;
      }
    }
  }
  
  throw lastError;
}

// Función para verificar la conexión
export async function checkDatabaseConnection() {
  try {
    // Ejecutar una consulta simple para verificar la conexión
    await db.execute('SELECT 1');
    return true;
  } catch (error) {
    console.error('Error verificando conexión a base de datos:', error);
    return false;
  }
}

// Función para manejar errores de base de datos
export function handleDatabaseError(error: any): { message: string; status: number } {
  console.error('Error de base de datos:', error);

  // Errores de conexión
  if (
    error.message?.includes('Connection terminated') ||
    error.message?.includes('timeout') ||
    error.code === 'ECONNRESET'
  ) {
    return {
      message: 'Error de conexión con la base de datos. Por favor, intente nuevamente.',
      status: 503 // Service Unavailable
    };
  }

  // Error de restricción única
  if (error.code === '23505') {
    return {
      message: 'El registro ya existe en la base de datos.',
      status: 409 // Conflict
    };
  }

  // Error de restricción de llave foránea
  if (error.code === '23503') {
    return {
      message: 'No se puede completar la operación debido a dependencias con otros registros.',
      status: 400
    };
  }

  // Error genérico
  return {
    message: 'Error interno del servidor. Por favor, intente nuevamente.',
    status: 500
  };
}