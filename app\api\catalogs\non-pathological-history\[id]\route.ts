import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { nonPathologicalHistory } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener antecedente no patológico por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;

    const nonPathologicalHistoryData = await db.select()
      .from(nonPathologicalHistory)
      .where(eq(nonPathologicalHistory.id, id))
      .limit(1);

    if (nonPathologicalHistoryData.length === 0) {
      return NextResponse.json({ error: 'Antecedente no patológico no encontrado' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: nonPathologicalHistoryData[0] 
    });

  } catch (error) {
    console.error('Error obteniendo antecedente no patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar antecedente no patológico
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para eliminar antecedentes no patológicos' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical';

    // Verificar que el antecedente no patológico existe
    const existingHistory = await db.select()
      .from(nonPathologicalHistory)
      .where(eq(nonPathologicalHistory.id, id))
      .limit(1);

    if (existingHistory.length === 0) {
      return NextResponse.json({ error: 'Antecedente no patológico no encontrado' }, { status: 404 });
    }

    const nonPathologicalHistoryData = existingHistory[0];

    // Validación: No permitir eliminar antecedente activo SOLO para eliminación lógica
    if (nonPathologicalHistoryData.isActive && deleteType !== 'physical') {
      return NextResponse.json({ 
        error: 'No se puede eliminar un antecedente no patológico activo', 
        suggestion: 'Desactive el antecedente no patológico antes de eliminarlo',
        code: 'ACTIVE_NON_PATHOLOGICAL_HISTORY'
      }, { status: 400 });
    }

    if (deleteType === 'physical') {
      // Eliminación física
      await db.delete(nonPathologicalHistory)
        .where(eq(nonPathologicalHistory.id, id));

      return NextResponse.json({ 
        success: true, 
        message: 'Antecedente no patológico eliminado físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedHistory] = await db.update(nonPathologicalHistory)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(nonPathologicalHistory.id, id))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedHistory,
        message: 'Antecedente no patológico desactivado exitosamente'
      });
    }

  } catch (error) {
    console.error('Error eliminando antecedente no patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}