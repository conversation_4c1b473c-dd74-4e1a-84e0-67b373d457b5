import { db } from '@/db/drizzle';
import { appointments } from '@/db/schema';
import { eq } from 'drizzle-orm';

async function fixExistingAppointment() {
  const appointmentId = '5QS5sGhaDr2JgUVwbgZ4K';
  
  console.log(`🔧 Actualizando cita existente: ${appointmentId}\n`);

  try {
    // Actualizar la cita con el serviceId fijo por defecto
    const serviceId = 'Xeu0lWkOlGulg-XBGcDO9'; // Consulta General Pediátrica (Q250)
    const estimatedPrice = '250.00';
    
    console.log('🔍 Actualizando con:');
    console.log('- ServiceId:', serviceId);
    console.log('- Precio estimado:', estimatedPrice);
    console.log('- Título: Consulta General\n');

    const result = await db
      .update(appointments)
      .set({
        serviceId: serviceId,
        estimatedPrice: estimatedPrice,
        title: 'Consulta General',
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId))
      .returning();

    if (result.length > 0) {
      console.log('✅ Cita actualizada exitosamente');
      console.log('📋 Datos actualizados:');
      console.log('- ID:', result[0].id);
      console.log('- Título:', result[0].title);
      console.log('- ServiceId:', result[0].serviceId);
      console.log('- Precio estimado:', result[0].estimatedPrice);
      console.log('- Estado:', result[0].status);
      
      console.log('\n🚀 Ahora puedes:');
      console.log('1. Ir a la consulta existente desde el menú contextual');
      console.log('2. Verificar que los servicios ahora aparezcan correctamente');
      console.log('3. Los logs deberían mostrar que se transfieren los servicios');
    } else {
      console.log('❌ No se encontró la cita para actualizar');
    }

  } catch (error) {
    console.error('❌ Error actualizando cita:', error);
  }
}

fixExistingAppointment();