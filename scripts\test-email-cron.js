#!/usr/bin/env node

/**
 * Script para probar los cron jobs de emails manualmente
 * 
 * Uso:
 * node scripts/test-email-cron.js precheckin
 * node scripts/test-email-cron.js reminders
 * node scripts/test-email-cron.js both
 */

const https = require('https');
const http = require('http');
const fs = require('fs');
const path = require('path');

// Cargar variables de entorno desde .env.local
function loadEnvLocal() {
  const envPath = path.join(__dirname, '..', '.env.local');
  
  if (fs.existsSync(envPath)) {
    const envContent = fs.readFileSync(envPath, 'utf8');
    const envLines = envContent.split('\n');
    
    for (const line of envLines) {
      const trimmed = line.trim();
      if (trimmed && !trimmed.startsWith('#')) {
        const [key, ...valueParts] = trimmed.split('=');
        if (key && valueParts.length > 0) {
          const value = valueParts.join('=');
          process.env[key] = value;
        }
      }
    }
  }
}

// Cargar entorno
loadEnvLocal();

const BASE_URL = process.env.TEST_BASE_URL || 'http://localhost:3000';
const CRON_SECRET = process.env.CRON_SECRET || 'dev-secret';

async function makeRequest(endpoint) {
  return new Promise((resolve, reject) => {
    const url = new URL(`${BASE_URL}/api/cron/${endpoint}`);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CRON_SECRET}`,
        'Content-Type': 'application/json'
      }
    };

    const req = client.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          resolve({
            status: res.statusCode,
            data: response
          });
        } catch (error) {
          resolve({
            status: res.statusCode,
            data: { error: 'Invalid JSON response', raw: data }
          });
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

async function testPrecheckin() {
  console.log('🧪 Probando endpoint de pre-checkin...');
  console.log(`📍 URL: ${BASE_URL}/api/cron/send-precheckin`);
  console.log('');
  
  try {
    const result = await makeRequest('send-precheckin');
    
    if (result.status === 200) {
      console.log('✅ Pre-checkin test exitoso:');
      console.log(`   📧 Emails enviados: ${result.data.emailsSent || 0}`);
      console.log(`   ❌ Emails con error: ${result.data.emailsError || 0}`);
      console.log(`   📋 Total citas procesadas: ${result.data.totalAppointments || 0}`);
    } else {
      console.log('❌ Pre-checkin test falló:');
      console.log(`   Status: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log('❌ Error en pre-checkin test:');
    console.log(`   ${error.message}`);
  }
  
  console.log('');
}

async function testReminders() {
  console.log('🧪 Probando endpoint de recordatorios...');
  console.log(`📍 URL: ${BASE_URL}/api/cron/send-reminders`);
  console.log('');
  
  try {
    const result = await makeRequest('send-reminders');
    
    if (result.status === 200) {
      console.log('✅ Recordatorios test exitoso:');
      console.log(`   📧 Emails enviados: ${result.data.emailsSent || 0}`);
      console.log(`   ❌ Emails con error: ${result.data.emailsError || 0}`);
      console.log(`   📋 Total citas procesadas: ${result.data.totalAppointments || 0}`);
    } else {
      console.log('❌ Recordatorios test falló:');
      console.log(`   Status: ${result.status}`);
      console.log(`   Error: ${JSON.stringify(result.data, null, 2)}`);
    }
  } catch (error) {
    console.log('❌ Error en recordatorios test:');
    console.log(`   ${error.message}`);
  }
  
  console.log('');
}

async function main() {
  const command = process.argv[2];
  
  console.log('🚀 Script de prueba de Cron Jobs de Email');
  console.log('==========================================');
  console.log(`📅 Fecha: ${new Date().toISOString()}`);
  console.log(`🔑 Secret: ${CRON_SECRET}`);
  console.log('');
  
  if (!command || command === 'both') {
    await testPrecheckin();
    await testReminders();
  } else if (command === 'precheckin') {
    await testPrecheckin();
  } else if (command === 'reminders') {
    await testReminders();
  } else {
    console.log('❌ Comando inválido. Usa: precheckin, reminders, o both');
    console.log('');
    console.log('Ejemplos:');
    console.log('  node scripts/test-email-cron.js precheckin');
    console.log('  node scripts/test-email-cron.js reminders');
    console.log('  node scripts/test-email-cron.js both');
    process.exit(1);
  }
  
  console.log('✨ Prueba completada!');
}

main().catch(console.error);