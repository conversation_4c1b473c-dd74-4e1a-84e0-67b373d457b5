'use client';

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  CheckCircle,
  XCircle,
  User,
  Phone,
  AlertTriangle,
  Pill,
  FileText,
  Clock,
  AlertCircle
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface PreCheckinSummaryProps {
  data: {
    // Información básica del pre-checkin
    attendance: 'yes' | 'no';
    completedAt: string;
    mode: 'complete' | 'express' | 'presential' | 'none';
    
    // Información médica
    hasSymptoms: boolean;
    symptoms?: string;
    takingMedications: boolean;
    medications?: string;
    hasAllergies: boolean;
    allergies?: string;
    
    // Información de contacto
    contactInfo?: {
      phone?: string;
      emergencyContact?: string;
      emergencyPhone?: string;
    };
    
    // Información del acompañante
    companionInfo?: {
      name: string;
      relationship: string;
      phone?: string;
    };
    
    // Motivo y notas
    chiefComplaint: string;
    additionalNotes?: string;
    
    // Meta información
    isDependent: boolean;
    isFirstVisit: boolean;
  };
  onStructureInformation?: () => void;
  onStartManualCapture?: () => void;
  className?: string;
}

export function PreCheckinSummary({ 
  data, 
  onStructureInformation, 
  onStartManualCapture,
  className 
}: PreCheckinSummaryProps) {
  
  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours === 0) {
      const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
      return `hace ${diffInMinutes} minutos`;
    } else if (diffInHours < 24) {
      return `hace ${diffInHours} horas`;
    } else {
      return date.toLocaleDateString('es-ES', { 
        day: '2-digit', 
        month: '2-digit', 
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    }
  };

  const getModeInfo = () => {
    switch (data.mode) {
      case 'complete':
        return {
          label: 'Completo',
          color: 'bg-green-100 text-green-800',
          icon: <CheckCircle className="h-4 w-4" />
        };
      case 'express':
        return {
          label: 'Express',
          color: 'bg-orange-100 text-orange-800',
          icon: <Clock className="h-4 w-4" />
        };
      case 'presential':
        return {
          label: 'Presencial',
          color: 'bg-blue-100 text-blue-800',
          icon: <User className="h-4 w-4" />
        };
      case 'none':
        return {
          label: 'Sin Pre-checkin',
          color: 'bg-red-100 text-red-800',
          icon: <XCircle className="h-4 w-4" />
        };
      default:
        return {
          label: 'Desconocido',
          color: 'bg-gray-100 text-gray-800',
          icon: <AlertCircle className="h-4 w-4" />
        };
    }
  };

  const modeInfo = getModeInfo();

  // Si no hay pre-checkin completado
  if (data.mode === 'none' || data.attendance === 'no') {
    return (
      <Card className={cn("border-red-200 bg-red-50", className)}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <XCircle className="h-5 w-5" />
            {data.attendance === 'no' ? 'Paciente No Asistirá' : 'Sin Pre-checkin'}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {data.attendance === 'no' ? (
              <p className="text-red-700">
                El paciente canceló su cita. Motivo: {data.additionalNotes || 'No especificado'}
              </p>
            ) : (
              <>
                <div className="flex items-center gap-2 text-red-700">
                  <AlertTriangle className="h-4 w-4" />
                  <span className="font-medium">Pre-checkin no completado</span>
                </div>
                <div className="text-sm text-red-600">
                  <p>📋 Información a capturar manualmente:</p>
                  <ul className="list-disc list-inside mt-2 space-y-1">
                    <li>Síntomas actuales</li>
                    <li>Medicamentos que toma</li>
                    <li>Alergias conocidas</li>
                    <li>Motivo de consulta</li>
                    <li>Información de contacto</li>
                  </ul>
                </div>
                <div className="flex gap-2 pt-2">
                  {onStartManualCapture && (
                    <Button 
                      onClick={onStartManualCapture}
                      className="bg-red-600 hover:bg-red-700"
                    >
                      Iniciar Captura Manual
                    </Button>
                  )}
                  <Button variant="outline" size="sm">
                    Pre-checkin en Tablet
                  </Button>
                </div>
              </>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "border-green-200 bg-green-50",
      data.mode === 'express' && "border-orange-200 bg-orange-50",
      data.mode === 'presential' && "border-blue-200 bg-blue-50",
      className
    )}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Información Pre-checkin
          </CardTitle>
          <div className="flex items-center gap-2">
            <Badge className={modeInfo.color}>
              {modeInfo.icon}
              <span className="ml-1">{modeInfo.label}</span>
            </Badge>
            <span className="text-sm text-gray-500">
              {formatTime(data.completedAt)}
            </span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Estado de asistencia */}
        <div className="flex items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <span className="font-medium text-green-800">Confirmó asistencia</span>
          {data.isFirstVisit && (
            <Badge variant="secondary" className="text-xs">Primera visita</Badge>
          )}
        </div>

        <Separator />

        {/* Información médica */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-3">
            {/* Síntomas */}
            <div className="flex items-start gap-2">
              <div className="flex items-center gap-1 min-w-0">
                <span className="text-lg">🤒</span>
                <span className="font-medium text-sm">Síntomas:</span>
              </div>
              <span className="text-sm text-gray-700 break-words">
                {data.hasSymptoms && data.symptoms ? data.symptoms : 'Ninguno reportado'}
              </span>
            </div>

            {/* Medicamentos */}
            <div className="flex items-start gap-2">
              <div className="flex items-center gap-1 min-w-0">
                <Pill className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-sm">Medicamentos:</span>
              </div>
              <span className="text-sm text-gray-700 break-words">
                {data.takingMedications && data.medications ? data.medications : 'Ninguno'}
              </span>
            </div>

            {/* Alergias */}
            <div className="flex items-start gap-2">
              <div className="flex items-center gap-1 min-w-0">
                <AlertTriangle className="h-4 w-4 text-red-600" />
                <span className="font-medium text-sm">Alergias:</span>
              </div>
              <span className="text-sm text-gray-700 break-words">
                {data.hasAllergies && data.allergies ? data.allergies : 'Ninguna conocida'}
              </span>
            </div>
          </div>

          <div className="space-y-3">
            {/* Motivo de consulta */}
            <div className="flex items-start gap-2">
              <div className="flex items-center gap-1 min-w-0">
                <FileText className="h-4 w-4 text-purple-600" />
                <span className="font-medium text-sm">Motivo:</span>
              </div>
              <span className="text-sm text-gray-700 break-words">
                {data.chiefComplaint || 'No especificado'}
              </span>
            </div>

            {/* Acompañante (si aplica) */}
            {data.companionInfo && (
              <div className="flex items-start gap-2">
                <div className="flex items-center gap-1 min-w-0">
                  <User className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-sm">Acompañante:</span>
                </div>
                <span className="text-sm text-gray-700 break-words">
                  {data.companionInfo.name} ({data.companionInfo.relationship})
                </span>
              </div>
            )}

            {/* Información de contacto actualizada */}
            {data.contactInfo?.phone && (
              <div className="flex items-start gap-2">
                <div className="flex items-center gap-1 min-w-0">
                  <Phone className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-sm">Teléfono:</span>
                </div>
                <span className="text-sm text-gray-700">
                  {data.contactInfo.phone}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Notas adicionales */}
        {data.additionalNotes && (
          <>
            <Separator />
            <div className="space-y-2">
              <span className="font-medium text-sm text-gray-800">Observaciones adicionales:</span>
              <p className="text-sm text-gray-700 bg-white p-3 rounded border">
                {data.additionalNotes}
              </p>
            </div>
          </>
        )}

        {/* Advertencias según el modo */}
        {data.mode === 'express' && (
          <div className="flex items-center gap-2 text-orange-700 bg-orange-100 p-2 rounded">
            <AlertTriangle className="h-4 w-4" />
            <span className="text-sm font-medium">INFORMACIÓN LIMITADA - Completar manual</span>
          </div>
        )}

        {data.mode === 'presential' && (
          <div className="flex items-center gap-2 text-blue-700 bg-blue-100 p-2 rounded">
            <User className="h-4 w-4" />
            <span className="text-sm font-medium">Información capturada en recepción</span>
          </div>
        )}

        {/* Botones de acción */}
        <div className="flex gap-2 pt-2">
          {onStructureInformation && (
            <Button 
              onClick={onStructureInformation}
              className="bg-green-600 hover:bg-green-700"
            >
              <FileText className="h-4 w-4 mr-2" />
              Estructurar Información
            </Button>
          )}
          <Button variant="outline" size="sm">
            Ver Pre-checkin Completo
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}