import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { pathologicalHistory } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Cambiar estado activo/inactivo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'doctor'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para cambiar estado de antecedentes patológicos' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el antecedente patológico existe
    const existingHistory = await db.select()
      .from(pathologicalHistory)
      .where(eq(pathologicalHistory.id, id))
      .limit(1);

    if (existingHistory.length === 0) {
      return NextResponse.json({ error: 'Antecedente patológico no encontrado' }, { status: 404 });
    }

    const pathologicalHistoryData = existingHistory[0];
    const newStatus = !pathologicalHistoryData.isActive;

    // Actualizar estado
    const [updatedHistory] = await db.update(pathologicalHistory)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(pathologicalHistory.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedHistory,
      message: `Antecedente patológico ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error cambiando estado del antecedente patológico:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}