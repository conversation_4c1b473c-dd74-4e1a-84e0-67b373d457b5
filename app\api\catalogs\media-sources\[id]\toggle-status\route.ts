import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { mediaSources } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Cambiar estado activo/inactivo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para cambiar estado de medios de comunicación' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el medio de comunicación existe
    const existingSource = await db.select()
      .from(mediaSources)
      .where(eq(mediaSources.id, id))
      .limit(1);

    if (existingSource.length === 0) {
      return NextResponse.json({ error: 'Medio de comunicación no encontrado' }, { status: 404 });
    }

    const mediaSource = existingSource[0];
    const newStatus = !mediaSource.isActive;

    // Actualizar estado
    const [updatedSource] = await db.update(mediaSources)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(mediaSources.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedSource,
      message: `Medio de comunicación ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error cambiando estado del medio de comunicación:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}