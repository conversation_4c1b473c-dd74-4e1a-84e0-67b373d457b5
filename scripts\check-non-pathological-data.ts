import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { eq, and } from 'drizzle-orm';
import * as schema from '../db/schema';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql, { schema });

async function checkNonPathologicalData() {
  try {
    console.log('🔍 Verificando datos de antecedentes no patológicos...');
    
    // Buscar todos los antecedentes no patológicos
    const allData = await db
      .select()
      .from(schema.nonPathologicalHistory)
      .limit(10);
    
    console.log(`📊 Total de registros: ${allData.length}`);
    
    if (allData.length > 0) {
      console.log('\n📋 Primeros 5 registros:');
      allData.slice(0, 5).forEach((item, index) => {
        console.log(`${index + 1}. ID: ${item.id}, Nombre: ${item.name}, Categoría: ${item.category}, Activo: ${item.isActive}`);
      });
    }
    
    // Buscar solo los activos
    const activeData = await db
      .select()
      .from(schema.nonPathologicalHistory)
      .where(eq(schema.nonPathologicalHistory.isActive, true))
      .limit(10);
    
    console.log(`\n✅ Registros activos: ${activeData.length}`);
    
    // Buscar por categorías
    const categories = await db
      .select({
        category: schema.nonPathologicalHistory.category,
        count: schema.nonPathologicalHistory.id
      })
      .from(schema.nonPathologicalHistory)
      .where(eq(schema.nonPathologicalHistory.isActive, true))
      .groupBy(schema.nonPathologicalHistory.category);
    
    console.log('\n📂 Categorías disponibles:');
    categories.forEach(cat => {
      console.log(`- ${cat.category}`);
    });
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    process.exit(0);
  }
}

checkNonPathologicalData();