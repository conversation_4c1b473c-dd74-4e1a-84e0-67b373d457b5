import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  medicalConsultations, 
  appointments, 
  medicalRecords, 
  user,
  userRoles,
  consultories,
  medicalSpecialties
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

// GET - Obtener detalles de una consulta específica
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const consultationId = id;
    
    console.log('Fetching consultation with ID:', consultationId);
    console.log('User ID:', userId);

    // Obtener consulta con información relacionada - usando aliases para evitar conflictos
    const consultationData = await db
      .select({
        // Datos de la consulta
        id: medicalConsultations.id,
        doctorId: medicalConsultations.doctorId,
        consultationDate: medicalConsultations.consultationDate,
        services: medicalConsultations.services,
        chiefComplaint: medicalConsultations.chiefComplaint,
        currentIllness: medicalConsultations.currentIllness,
        vitalSigns: medicalConsultations.vitalSigns,
        physicalExam: medicalConsultations.physicalExam,
        diagnoses: medicalConsultations.diagnoses,
        treatment: medicalConsultations.treatment,
        prescriptions: medicalConsultations.prescriptions,
        recommendations: medicalConsultations.recommendations,
        followUpInstructions: medicalConsultations.followUpInstructions,
        nextAppointmentValue: medicalConsultations.nextAppointmentValue,
        nextAppointmentUnit: medicalConsultations.nextAppointmentUnit,
        nextAppointmentType: medicalConsultations.nextAppointmentType,
        nextAppointmentNotes: medicalConsultations.nextAppointmentNotes,
        nextAppointmentCalculatedDate: medicalConsultations.nextAppointmentCalculatedDate,
        attachments: medicalConsultations.attachments,
        status: medicalConsultations.status,
        
        // Datos del expediente
        medicalRecordId: medicalRecords.id,
        recordNumber: medicalRecords.recordNumber,
        
        // Datos del paciente
        patientId: user.id,
        patientFirstName: user.firstName,
        patientLastName: user.lastName,
        patientEmail: user.email,
        patientPhone: user.phone,
        patientDateOfBirth: user.dateOfBirth,
        patientDocumentNumber: user.documentNumber,
        
        // Datos de la cita (si existe)
        appointmentId: appointments.id,
        appointmentDate: appointments.scheduledDate,
        appointmentStartTime: appointments.startTime,
        appointmentEndTime: appointments.endTime,
        appointmentServiceId: appointments.serviceId,
        preCheckinCompleted: appointments.preCheckinCompleted,
        preCheckinData: appointments.preCheckinData,
        
        // Datos del consultorio
        consultoryId: consultories.id,
        consultoryName: consultories.name,
        consultoryAddress: consultories.address,
        consultoryPhone: consultories.phone,
        consultoryEmail: consultories.email,
        consultoryLogoUrl: consultories.logoUrl,
        
        // Auditoría
        createdAt: medicalConsultations.createdAt,
        updatedAt: medicalConsultations.updatedAt,
      })
      .from(medicalConsultations)
      .leftJoin(medicalRecords, eq(medicalConsultations.medicalRecordId, medicalRecords.id))
      .leftJoin(user, eq(medicalRecords.patientId, user.id))
      .leftJoin(appointments, eq(medicalConsultations.appointmentId, appointments.id))
      .leftJoin(consultories, eq(medicalConsultations.consultoryId, consultories.id))
      .where(eq(medicalConsultations.id, consultationId))
      .limit(1);

    console.log('Consultation data found:', consultationData.length > 0);
    
    if (!consultationData.length) {
      return NextResponse.json({ 
        error: 'Consulta no encontrada' 
      }, { status: 404 });
    }

    const consultation = consultationData[0];
    console.log('Consultation doctor ID:', consultation.doctorId);

    // Obtener datos completos del doctor
    const doctorData = await db
      .select({
        doctorId: user.id,
        doctorFirstName: user.firstName,
        doctorLastName: user.lastName,
        doctorEmail: user.email,
        doctorPhone: user.phone,
        medicalLicense: userRoles.medicalLicense,
        specialtyId: userRoles.specialtyId,
        specialtyName: medicalSpecialties.name,
      })
      .from(user)
      .leftJoin(userRoles, and(
        eq(userRoles.userId, user.id),
        eq(userRoles.role, 'doctor')
      ))
      .leftJoin(medicalSpecialties, eq(userRoles.specialtyId, medicalSpecialties.id))
      .where(eq(user.id, consultation.doctorId))
      .limit(1);

    const doctor = doctorData.length > 0 ? doctorData[0] : null;
    console.log('Doctor data found:', doctor ? true : false);

    // Verificar autorización (doctor de la consulta o admin)
    const userRole = await db
      .select()
      .from(userRoles)
      .where(and(
        eq(userRoles.userId, userId),
        eq(userRoles.status, 'active')
      ));

    const isDoctor = userRole.some(role => 
      role.role === 'doctor' && consultation.doctorId === userId
    );
    const isAdmin = userRole.some(role => role.role === 'admin');

    if (!isDoctor && !isAdmin) {
      return NextResponse.json({ 
        error: 'No autorizado para ver esta consulta' 
      }, { status: 403 });
    }

    // Procesar datos del pre-checkin
    let preCheckinSummary = null;
    if (consultation.preCheckinData) {
      let preCheckinData;
      
      console.log('🔍 DEBUG: Raw preCheckinData type:', typeof consultation.preCheckinData);
      console.log('🔍 DEBUG: Raw preCheckinData:', consultation.preCheckinData);
      
      // Manejar diferentes formatos de datos
      if (typeof consultation.preCheckinData === 'string') {
        // Si es string, intentar parsearlo como JSON
        if (consultation.preCheckinData === '[object Object]') {
          console.log('⚠️ WARNING: preCheckinData está corrupto como [object Object]');
          preCheckinData = {};
        } else {
          try {
            preCheckinData = JSON.parse(consultation.preCheckinData);
          } catch (error) {
            console.error('Error parsing preCheckinData JSON:', error);
            preCheckinData = {};
          }
        }
      } else if (typeof consultation.preCheckinData === 'object' && consultation.preCheckinData !== null) {
        // Si ya es objeto, usarlo directamente
        preCheckinData = consultation.preCheckinData as any;
      } else {
        preCheckinData = {};
      }
      
      console.log('🔍 DEBUG: PreCheckin data processed:', preCheckinData);
      console.log('🔍 DEBUG: PreCheckin data keys:', Object.keys(preCheckinData || {}));
      
      preCheckinSummary = {
        // Campos básicos
        attendance: preCheckinData?.attendance || preCheckinData?.willAttend || null,
        hasSymptoms: preCheckinData?.hasSymptoms || false,
        symptoms: preCheckinData?.symptoms || '',
        takingMedications: preCheckinData?.takingMedications || false,
        medications: preCheckinData?.medications || '',
        hasAllergies: preCheckinData?.hasAllergies || false,
        allergies: preCheckinData?.allergies || '',
        
        // Información de contacto
        phone: preCheckinData?.phone || '',
        emergencyContact: preCheckinData?.emergencyContact || '',
        emergencyPhone: preCheckinData?.emergencyPhone || '',
        contactInfo: preCheckinData?.contactInfo || {},
        
        // Información del acompañante
        isDependent: preCheckinData?.isDependent || false,
        companionInfo: preCheckinData?.companionInfo || {},
        
        // Motivo y notas
        chiefComplaint: preCheckinData?.chiefComplaint || '',
        additionalNotes: preCheckinData?.additionalNotes || '',
        
        // 📄 DOCUMENTOS - ¡Este era el campo que faltaba!
        documents: preCheckinData?.documents || [],
        
        // Timestamps
        completedAt: preCheckinData?.completedAt || null,
        submittedAt: preCheckinData?.submittedAt || null,
      };
      
      console.log('🔍 DEBUG: Documents found:', preCheckinSummary.documents?.length || 0);
    }

    // Calcular edad del paciente
    let patientAge = null;
    if (consultation.patientDateOfBirth) {
      const today = new Date();
      const birthDate = new Date(consultation.patientDateOfBirth);
      patientAge = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        patientAge--;
      }
    }

    console.log('🔍 DEBUG GET: Servicios de la consulta desde BD:', consultation.services);
    
    const response = {
      consultation: {
        id: consultation.id,
        consultationDate: consultation.consultationDate?.toISOString(),
        services: consultation.services || [],
        chiefComplaint: consultation.chiefComplaint || '',
        currentIllness: consultation.currentIllness || '',
        vitalSigns: consultation.vitalSigns || {},
        physicalExam: consultation.physicalExam || {},
        diagnoses: consultation.diagnoses || [],
        treatment: consultation.treatment || {},
        prescriptions: consultation.prescriptions || [],
        recommendations: consultation.recommendations || '',
        followUpInstructions: consultation.followUpInstructions || '',
        nextAppointment: {
          value: consultation.nextAppointmentValue || 1,
          unit: consultation.nextAppointmentUnit || 'weeks',
          type: consultation.nextAppointmentType || 'control',
          notes: consultation.nextAppointmentNotes || '',
          calculatedDate: consultation.nextAppointmentCalculatedDate?.toISOString() || null
        },
        attachments: consultation.attachments || [],
        status: consultation.status,
        createdAt: consultation.createdAt?.toISOString(),
        updatedAt: consultation.updatedAt?.toISOString(),
      },
      medicalRecord: {
        id: consultation.medicalRecordId,
        recordNumber: consultation.recordNumber,
      },
      patient: consultation.patientId ? {
        id: consultation.patientId,
        name: `${consultation.patientFirstName || ''} ${consultation.patientLastName || ''}`.trim(),
        firstName: consultation.patientFirstName || '',
        lastName: consultation.patientLastName || '',
        email: consultation.patientEmail || '',
        phone: consultation.patientPhone || '',
        documentNumber: consultation.patientDocumentNumber || '',
        dateOfBirth: consultation.patientDateOfBirth?.toISOString() || null,
        age: patientAge,
      } : null,
      appointment: consultation.appointmentId ? {
        id: consultation.appointmentId,
        date: consultation.appointmentDate?.toISOString(),
        startTime: consultation.appointmentStartTime?.toISOString(),
        endTime: consultation.appointmentEndTime?.toISOString(),
        serviceId: consultation.appointmentServiceId,
        preCheckinCompleted: consultation.preCheckinCompleted,
      } : null,
      doctor: doctor ? {
        id: doctor.doctorId,
        name: `${doctor.doctorFirstName || ''} ${doctor.doctorLastName || ''}`.trim(),
        firstName: doctor.doctorFirstName || '',
        lastName: doctor.doctorLastName || '',
        email: doctor.doctorEmail || '',
        phone: doctor.doctorPhone || '',
        medicalLicense: doctor.medicalLicense || '',
        specialty: doctor.specialtyName || '',
      } : null,
      consultory: {
        id: consultation.consultoryId,
        name: consultation.consultoryName || 'Consultorio',
        address: consultation.consultoryAddress || '',
        phone: consultation.consultoryPhone || '',
        email: consultation.consultoryEmail || '',
        logoUrl: consultation.consultoryLogoUrl || '',
      },
      preCheckin: preCheckinSummary,
    };

    return NextResponse.json({
      success: true,
      data: response,
    });

  } catch (error) {
    console.error('Error fetching consultation details:', error);
    
    // Log más detallado del error
    if (error instanceof Error) {
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    return NextResponse.json({ 
      error: 'Error interno del servidor',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// PUT - Actualizar consulta médica
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const consultationId = id;
    const body = await request.json();

    // Verificar que la consulta existe y pertenece al doctor
    const existingConsultation = await db
      .select()
      .from(medicalConsultations)
      .where(and(
        eq(medicalConsultations.id, consultationId),
        eq(medicalConsultations.doctorId, userId)
      ))
      .limit(1);

    if (!existingConsultation.length) {
      return NextResponse.json({ 
        error: 'Consulta no encontrada o no autorizada' 
      }, { status: 404 });
    }

    // Actualizar consulta
    const updateData = {
      currentIllness: body.currentIllness,
      services: body.services || [],
      vitalSigns: body.vitalSigns,
      physicalExam: body.physicalExam,
      diagnoses: body.diagnoses,
      treatment: body.treatment,
      prescriptions: body.prescriptions,
      recommendations: body.recommendations,
      followUpInstructions: body.followUpInstructions,
      // Manejar tanto el formato nuevo como el legacy
      nextAppointmentValue: body.nextAppointment?.value || null,
      nextAppointmentUnit: body.nextAppointment?.unit || null,
      nextAppointmentType: body.nextAppointment?.type || null,
      nextAppointmentNotes: body.nextAppointment?.notes || null,
      nextAppointmentCalculatedDate: body.nextAppointment?.calculatedDate ? new Date(body.nextAppointment.calculatedDate) : null,
      status: body.status || 'draft',
      updatedAt: new Date(),
      updatedBy: userId,
    };

    const result = await db
      .update(medicalConsultations)
      .set(updateData)
      .where(eq(medicalConsultations.id, consultationId))
      .returning();

    // Si se completa la consulta, actualizar la cita
    if (body.status === 'completed' && existingConsultation[0].appointmentId) {
      await db
        .update(appointments)
        .set({
          status: 'completed',
          completedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(eq(appointments.id, existingConsultation[0].appointmentId));
    }

    return NextResponse.json({
      success: true,
      message: 'Consulta actualizada exitosamente',
      data: result[0],
    });

  } catch (error) {
    console.error('Error updating consultation:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}