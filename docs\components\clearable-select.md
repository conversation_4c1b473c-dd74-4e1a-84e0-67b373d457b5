# ClearableSelect Component

## Descripción
Componente Select que automáticamente incluye una opción "Limpiar selección" para mejorar la UX al permitir que los usuarios deseleccionen opciones.

## Uso

```tsx
import { ClearableSelect } from '@/components/ui/clearable-select';
import { SelectItem } from '@/components/ui/select';

// Ejemplo básico
<ClearableSelect 
  value={formData.gender} 
  onValueChange={(value) => setFormData({...formData, gender: value})}
  placeholder="Seleccionar género"
>
  <SelectItem value="masculino">Ma<PERSON><PERSON>lino</SelectItem>
  <SelectItem value="femenino">Fe<PERSON>ino</SelectItem>
  <SelectItem value="otro">Otro</SelectItem>
</ClearableSelect>
```

## Props

- `value`: string - Valor actual seleccionado
- `onValueChange`: (value: string) => void - Función llamada cuando cambia la selección
- `placeholder`: string - Texto placeholder (opcional)
- `clearText`: string - Texto para la opción de limpiar (opcional, default: "Limpiar selección")
- `className`: string - Clases CSS adicionales (opcional)
- `disabled`: boolean - Si el select está deshabilitado (opcional)
- `children`: React.ReactNode - SelectItem elementos

## Comportamiento

- La primera opción siempre es "Limpiar selección" con valor vacío ("")
- Cuando se selecciona, limpia la selección y retorna valor vacío
- Mantiene toda la funcionalidad del Select original
- Estilo visual diferenciado para la opción de limpiar (texto gris e itálico)

## Migración

Para convertir un Select existente:

### Antes:
```tsx
<Select value={value} onValueChange={onChange}>
  <SelectTrigger>
    <SelectValue placeholder="Seleccionar..." />
  </SelectTrigger>
  <SelectContent>
    <SelectItem value="option1">Opción 1</SelectItem>
    <SelectItem value="option2">Opción 2</SelectItem>
  </SelectContent>
</Select>
```

### Después:
```tsx
<ClearableSelect 
  value={value} 
  onValueChange={onChange}
  placeholder="Seleccionar..."
>
  <SelectItem value="option1">Opción 1</SelectItem>
  <SelectItem value="option2">Opción 2</SelectItem>
</ClearableSelect>
```

## Archivos que necesitan migración

- `/app/(dashboard)/dashboard/admin/users/[id]/edit/page.tsx`
- `/app/(dashboard)/dashboard/admin/users/create/page.tsx`
- `/components/onboarding/general-info-form.tsx`
- Y otros formularios que usen Select