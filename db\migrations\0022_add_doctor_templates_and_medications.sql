-- Migración: Agregar tablas para plantillas de recetas y medicamentos favoritos

-- Tabla para plantillas de recetas del doctor
CREATE TABLE IF NOT EXISTS prescription_templates (
  id TEXT PRIMARY KEY,
  doctor_id TEXT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  content TEXT NOT NULL,
  category TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Tabla para medicamentos favoritos del doctor
CREATE TABLE IF NOT EXISTS doctor_favorite_medications (
  id TEXT PRIMARY KEY,
  doctor_id TEXT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
  medication_id TEXT NOT NULL REFERENCES medications(id) ON DELETE CASCADE,
  dosage TEXT,
  frequency TEXT,
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(doctor_id, medication_id)
);

-- Índices para optimizar consultas
CREATE INDEX IF NOT EXISTS prescription_templates_doctor_idx ON prescription_templates(doctor_id);
CREATE INDEX IF NOT EXISTS prescription_templates_active_idx ON prescription_templates(is_active);
CREATE INDEX IF NOT EXISTS doctor_favorite_medications_doctor_idx ON doctor_favorite_medications(doctor_id);
CREATE INDEX IF NOT EXISTS doctor_favorite_medications_medication_idx ON doctor_favorite_medications(medication_id);