'use client';

import { usePathname } from 'next/navigation';
import Navbar from '@/components/navbar';
import { useUserSync } from '@/hooks/use-user-sync';

interface LayoutProviderProps {
  children: React.ReactNode;
}

export function LayoutProvider({ children }: LayoutProviderProps) {
  const pathname = usePathname();
  const isDashboard = pathname.startsWith('/dashboard');
  const isPreCheckin = pathname.startsWith('/pre-checkin');
  
  // Sincronizar usuario automáticamente cuando se autentica
  // Pero NO en páginas de pre-checkin (permiten acceso contextual)
  const { isSyncing } = isPreCheckin ? { isSyncing: false } : useUserSync();

  return (
    <>
      {!isDashboard && !isPreCheckin && <Navbar />}
      {children}
    </>
  );
} 