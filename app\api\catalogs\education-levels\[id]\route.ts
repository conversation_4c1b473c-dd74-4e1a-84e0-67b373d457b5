import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { educationLevels } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener un nivel de escolaridad por ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de nivel de escolaridad requerido' },
        { status: 400 }
      );
    }

    // Buscar el nivel de escolaridad
    const educationLevel = await db.select().from(educationLevels).where(eq(educationLevels.id, id)).limit(1);

    if (educationLevel.length === 0) {
      return NextResponse.json(
        { error: 'Nivel de escolaridad no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      data: educationLevel[0]
    });

  } catch (error) {
    console.error('Error obteniendo nivel de escolaridad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}