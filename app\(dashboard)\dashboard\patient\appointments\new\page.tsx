'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  ArrowLeft, 
  ArrowRight, 
  Calendar, 
  Clock, 
  User, 
  MessageSquare,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { generateShortCode } from '@/lib/short-codes';

interface Doctor {
  id: string;
  firstName: string;
  lastName: string;
  specialties?: string[];
  imageUrl?: string;
  consultoryId?: string;
  consultoryName?: string;
  isFavorite?: boolean;
}

interface TimeSlot {
  id: string;
  date: string;
  time: string;
  displayDate: string;
  displayTime: string;
  available: boolean;
}

type WizardStep = 'doctor' | 'time' | 'reason' | 'confirmation';

export default function NewAppointmentWizard() {
  const router = useRouter();
  const { user } = useUser();
  
  // Estados del wizard
  const [currentStep, setCurrentStep] = useState<WizardStep>('doctor');
  const [loading, setLoading] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  
  // Datos seleccionados
  const [selectedDoctor, setSelectedDoctor] = useState<Doctor | null>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
  const [reason, setReason] = useState('');
  
  // Datos cargados
  const [doctors, setDoctors] = useState<Doctor[]>([]);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>([]);
  
  // Cargar doctores disponibles
  useEffect(() => {
    const fetchDoctors = async () => {
      setLoading(true);
      try {
        const response = await fetch('/api/appointments/doctors');
        const result = await response.json();
        
        if (result.success) {
          setDoctors(result.data);
          
          // Auto-seleccionar doctor favorito si existe
          const favoriteDoctor = result.data.find((doc: Doctor) => doc.isFavorite);
          if (favoriteDoctor) {
            setSelectedDoctor(favoriteDoctor);
          }
        }
      } catch (error) {
        console.error('Error fetching doctors:', error);
      } finally {
        setLoading(false);
      }
    };

    if (currentStep === 'doctor') {
      fetchDoctors();
    }
  }, [currentStep]);

  // Cargar horarios disponibles cuando se selecciona doctor
  useEffect(() => {
    const fetchTimeSlots = async () => {
      if (!selectedDoctor) return;
      
      setLoading(true);
      try {
        const response = await fetch(`/api/appointments/availability?doctorId=${selectedDoctor.id}&limit=5`);
        const result = await response.json();
        
        if (result.success) {
          setTimeSlots(result.data);
        }
      } catch (error) {
        console.error('Error fetching time slots:', error);
      } finally {
        setLoading(false);
      }
    };

    if (currentStep === 'time' && selectedDoctor) {
      fetchTimeSlots();
    }
  }, [currentStep, selectedDoctor]);

  // Navegación entre pasos
  const goToNextStep = () => {
    switch (currentStep) {
      case 'doctor':
        if (selectedDoctor) setCurrentStep('time');
        break;
      case 'time':
        if (selectedTimeSlot) setCurrentStep('reason');
        break;
      case 'reason':
        if (reason.trim()) setCurrentStep('confirmation');
        break;
    }
  };

  const goToPreviousStep = () => {
    switch (currentStep) {
      case 'time':
        setCurrentStep('doctor');
        break;
      case 'reason':
        setCurrentStep('time');
        break;
      case 'confirmation':
        setCurrentStep('reason');
        break;
    }
  };

  // Enviar solicitud de cita
  const submitAppointment = async () => {
    if (!selectedDoctor || !selectedTimeSlot || !reason.trim()) return;
    
    setSubmitting(true);
    
    try {
      const shortCode = generateShortCode();
      
      // Preparar fechas sin conversión UTC (mantener hora local de Guatemala)
      const dateStr = selectedTimeSlot.date; // Formato YYYY-MM-DD
      const timeStr = selectedTimeSlot.time; // Puede ser HH:MM o ISO string
      
      // Extraer hora y minutos correctamente
      let hours, minutes;
      if (timeStr.includes('T')) {
        // Es fecha ISO completa, extraer hora sin conversión UTC
        const timeMatch = timeStr.match(/T(\d{2}):(\d{2})/);
        if (!timeMatch) throw new Error(`No se pudo extraer hora de: ${timeStr}`);
        hours = parseInt(timeMatch[1], 10);
        minutes = parseInt(timeMatch[2], 10);
      } else {
        // Es solo hora HH:MM
        [hours, minutes] = timeStr.split(':').map(Number);
      }
      
      // Crear strings de fecha sin conversión UTC
      const dateParts = dateStr.split('-');
      const year = dateParts[0];
      const month = dateParts[1];
      const day = dateParts[2];
      
      const startTimeFormatted = `${year}-${month}-${day}T${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:00`;
      
      // Calcular hora de fin (30 minutos después)
      const endMinutes = minutes + 30;
      const endHours = hours + Math.floor(endMinutes / 60);
      const finalEndMinutes = endMinutes % 60;
      
      const endTimeFormatted = `${year}-${month}-${day}T${String(endHours).padStart(2, '0')}:${String(finalEndMinutes).padStart(2, '0')}:00`;
      
      // Servicio fijo por defecto para todas las citas del paciente
      const serviceId = 'Xeu0lWkOlGulg-XBGcDO9'; // Consulta General Pediátrica (Q250)
      const estimatedPrice = 250.00;

      const appointmentData = {
        title: 'Consulta General', // Título fijo
        doctorId: selectedDoctor.id,
        patientId: user?.id, // El ID del paciente actual
        consultoryId: selectedDoctor.consultoryId, // Ahora viene del doctor seleccionado
        serviceId: serviceId, // ✅ NUEVO: Agregar serviceId basado en el tipo de consulta
        activityTypeId: 'act_medical_consultation', // ID estándar para consultas médicas
        chiefComplaint: reason.trim(),
        scheduledDate: dateStr,
        startTime: startTimeFormatted,
        endTime: endTimeFormatted,
        duration: 30, // Duración estándar de 30 minutos
        estimatedPrice: estimatedPrice, // ✅ NUEVO: Precio estimado basado en el servicio
        currency: 'GTQ',
        isEmergency: false,
        requiresReminder: true
      };

      const response = await fetch('/api/appointments', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(appointmentData),
      });

      const result = await response.json();

      if (result.success) {
        // Redirigir al dashboard con mensaje de éxito
        router.push('/dashboard/patient?appointmentCreated=true');
      } else {
        console.error('Error creating appointment:', result.error);
        // Aquí podrías mostrar un toast de error
      }
    } catch (error) {
      console.error('Error submitting appointment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  const renderStepIndicator = () => {
    const steps = [
      { id: 'doctor', label: 'Doctor' },
      { id: 'time', label: 'Horario' },
      { id: 'reason', label: 'Motivo' },
      { id: 'confirmation', label: 'Confirmar' }
    ];

    const currentStepIndex = steps.findIndex(step => step.id === currentStep);

    return (
      <div className="flex items-center justify-between px-4">
        {steps.map((step, index) => (
          <div key={step.id} className="flex flex-col items-center flex-1">
            <div className="relative w-full flex items-center">
              {index > 0 && (
                <div className={`absolute left-0 right-1/2 h-0.5 -z-10 ${
                  index <= currentStepIndex ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
              {index < steps.length - 1 && (
                <div className={`absolute left-1/2 right-0 h-0.5 -z-10 ${
                  index < currentStepIndex ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
              <div className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium mx-auto ${
                index <= currentStepIndex 
                  ? 'bg-blue-600 text-white' 
                  : 'bg-gray-200 text-gray-500'
              }`}>
                {index + 1}
              </div>
            </div>
            <span className={`mt-1 text-xs ${
              index <= currentStepIndex ? 'text-blue-600 font-medium' : 'text-gray-500'
            }`}>
              {step.label}
            </span>
          </div>
        ))}
      </div>
    );
  };

  const renderDoctorSelection = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Selecciona tu Doctor</h2>
        <p className="text-gray-600">Escoge con qué médico deseas agendar tu cita</p>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      ) : (
        <div className="grid gap-4">
          {doctors.map((doctor) => (
            <Card 
              key={doctor.id}
              className={`cursor-pointer transition-all duration-200 ${
                selectedDoctor?.id === doctor.id 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : 'hover:shadow-md'
              }`}
              onClick={() => setSelectedDoctor(doctor)}
            >
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={doctor.imageUrl} />
                    <AvatarFallback className="bg-blue-100 text-blue-600">
                      {doctor.firstName[0]}{doctor.lastName[0]}
                    </AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold text-gray-900">
                        Dr. {doctor.firstName} {doctor.lastName}
                      </h3>
                      {doctor.isFavorite && (
                        <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
                          Favorito
                        </Badge>
                      )}
                    </div>
                    {doctor.specialties && doctor.specialties.length > 0 && (
                      <p className="text-sm text-gray-600">{doctor.specialties.join(', ')}</p>
                    )}
                  </div>
                  
                  {selectedDoctor?.id === doctor.id && (
                    <CheckCircle className="h-6 w-6 text-blue-600" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  const renderTimeSelection = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Selecciona Horario</h2>
        <p className="text-gray-600">
          Próximos horarios disponibles con Dr. {selectedDoctor?.firstName} {selectedDoctor?.lastName}
        </p>
      </div>

      {loading ? (
        <div className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        </div>
      ) : (
        <div className="grid gap-3">
          {timeSlots.map((slot) => (
            <Card 
              key={slot.id}
              className={`cursor-pointer transition-all duration-200 ${
                selectedTimeSlot?.id === slot.id 
                  ? 'ring-2 ring-blue-500 bg-blue-50' 
                  : 'hover:shadow-md'
              } ${!slot.available ? 'opacity-50 cursor-not-allowed' : ''}`}
              onClick={() => slot.available && setSelectedTimeSlot(slot)}
            >
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-gray-900">{slot.displayDate}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="h-5 w-5 text-blue-600" />
                      <span className="font-medium text-gray-900">{slot.displayTime}</span>
                    </div>
                  </div>
                  
                  {selectedTimeSlot?.id === slot.id && (
                    <CheckCircle className="h-6 w-6 text-blue-600" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );

  const renderReasonInput = () => (
    <div className="space-y-4">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Motivo de Consulta</h2>
        <p className="text-gray-600">Describe brevemente el motivo de tu visita</p>
      </div>

      <div className="space-y-4">
        <Textarea
          placeholder="Ej: Control de rutina, dolor de cabeza, seguimiento de tratamiento..."
          value={reason}
          onChange={(e) => setReason(e.target.value)}
          className="min-h-[120px] resize-none"
          maxLength={500}
        />
        <div className="text-right text-sm text-gray-500">
          {reason.length}/500 caracteres
        </div>
      </div>
    </div>
  );

  const renderConfirmation = () => (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Confirmar Cita</h2>
        <p className="text-gray-600">Revisa los detalles de tu cita antes de confirmar</p>
      </div>

      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-6 space-y-4">
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={selectedDoctor?.imageUrl} />
              <AvatarFallback className="bg-blue-100 text-blue-600 text-lg">
                {selectedDoctor?.firstName[0]}{selectedDoctor?.lastName[0]}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="text-xl font-semibold text-gray-900">
                Dr. {selectedDoctor?.firstName} {selectedDoctor?.lastName}
              </h3>
              {selectedDoctor?.specialties && selectedDoctor.specialties.length > 0 && (
                <p className="text-gray-600">{selectedDoctor.specialties.join(', ')}</p>
              )}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 pt-4 border-t border-blue-200">
            <div className="flex items-center gap-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Fecha</p>
                <p className="font-medium text-gray-900">{selectedTimeSlot?.displayDate}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-600" />
              <div>
                <p className="text-sm text-gray-600">Horario</p>
                <p className="font-medium text-gray-900">{selectedTimeSlot?.displayTime}</p>
              </div>
            </div>
          </div>

          <div className="pt-4 border-t border-blue-200">
            <div className="flex items-start gap-2">
              <MessageSquare className="h-5 w-5 text-blue-600 mt-0.5" />
              <div className="flex-1">
                <p className="text-sm text-gray-600">Motivo de consulta</p>
                <p className="font-medium text-gray-900">{reason}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="fixed inset-0 z-50 bg-gray-50 overflow-hidden">
      {/* Header móvil fijo */}
      <div className="fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-10">
        <div className="flex items-center justify-between p-4">
          <button
            onClick={() => router.back()}
            className="p-2 -ml-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ArrowLeft className="h-5 w-5 text-gray-700" />
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Agendar Cita</h1>
          <div className="w-9" /> {/* Espaciador para centrar título */}
        </div>
      </div>

      {/* Contenido con scroll */}
      <div className="fixed inset-0 top-[57px] bottom-[120px] overflow-y-auto">
        <div className="p-4">
          {renderStepIndicator()}
          
          <div className="mt-6">
            {currentStep === 'doctor' && renderDoctorSelection()}
            {currentStep === 'time' && renderTimeSelection()}
            {currentStep === 'reason' && renderReasonInput()}
            {currentStep === 'confirmation' && renderConfirmation()}
          </div>
        </div>
      </div>

      {/* Botones de navegación fijos en el bottom */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 z-10">
        <div className="flex gap-3">
          {currentStep !== 'doctor' && (
            <Button
              variant="outline"
              onClick={goToPreviousStep}
              size="lg"
              className="flex-1"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Anterior
            </Button>
          )}

          {currentStep === 'confirmation' ? (
            <Button
              onClick={submitAppointment}
              disabled={submitting}
              size="lg"
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            >
              {submitting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Agendando...
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Confirmar Cita
                </>
              )}
            </Button>
          ) : (
            <Button
              onClick={goToNextStep}
              disabled={
                (currentStep === 'doctor' && !selectedDoctor) ||
                (currentStep === 'time' && !selectedTimeSlot) ||
                (currentStep === 'reason' && !reason.trim())
              }
              size="lg"
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
            >
              Siguiente
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}