import { db } from '../db/drizzle';
import { sql } from 'drizzle-orm';
import fs from 'fs';
import path from 'path';

async function applyMigration() {
  try {
    console.log('🔄 Aplicando migración 0016: doctor_service_prices...');
    
    // Leer el archivo de migración
    const migrationPath = path.join(process.cwd(), 'db/migrations/0016_add_doctor_service_prices.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    // Ejecutar la migración
    await db.execute(sql.raw(migrationSQL));
    
    console.log('✅ Migración 0016 aplicada exitosamente');
    console.log('📋 Tabla doctor_service_prices creada con todos sus índices y constraints');
    
    // Verificar que la tabla existe
    const tableExists = await db.execute(sql`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'public' 
        AND table_name = 'doctor_service_prices'
      );
    `);
    
    console.log('🔍 Verificación de tabla:', tableExists.rows[0]?.exists ? 'EXISTE ✅' : 'NO EXISTE ❌');
    
  } catch (error) {
    console.error('❌ Error aplicando migración:', error);
    throw error;
  }
}

// Ejecutar si es llamado directamente
if (require.main === module) {
  applyMigration()
    .then(() => {
      console.log('🎉 Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { applyMigration };