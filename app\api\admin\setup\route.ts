import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const adminUserId = 'user_2zgdfxMtulphGc3A4vkwDRhq4bc';
    const adminEmail = '<EMAIL>';
    
    console.log('🔍 Configurando usuario admin con ID:', adminUserId);
    
    // Verificar si ya existe el rol de admin
    const existingRole = await db
      .select()
      .from(userRoles)
      .where(eq(userRoles.userId, adminUserId));
    
    if (existingRole.length > 0) {
      const roleData = existingRole[0];
      
      if (roleData.role === 'admin' && roleData.status === 'active') {
        return NextResponse.json({
          success: true,
          message: 'Usuario ya tiene rol de admin activo',
          user: { userId: adminUserId, role: roleData.role, status: roleData.status },
          action: 'exists'
        });
      }
      
      // Actualizar rol existente a admin
      await db
        .update(userRoles)
        .set({
          role: 'admin',
          status: 'active',
          updatedAt: new Date(),
        })
        .where(eq(userRoles.userId, adminUserId));
      
      console.log('✅ Rol actualizado a admin');
    } else {
      // Crear nuevo rol de admin
      await db.insert(userRoles).values({
        id: `${adminUserId}_admin`,
        userId: adminUserId,
        role: 'admin',
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      
      console.log('✅ Rol de admin creado');
    }
    
    // Actualizar estado del usuario a activo
    await db
      .update(user)
      .set({
        overallStatus: 'active',
        updatedAt: new Date(),
      })
      .where(eq(user.id, adminUserId));
    
    console.log('✅ Usuario marcado como activo');
    
    return NextResponse.json({
      success: true,
      message: 'Usuario configurado como admin exitosamente',
      userId: adminUserId,
      action: 'configured'
    });
    
  } catch (error) {
    console.error('Error en setup admin:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor', error: error.message },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const adminUserId = 'user_2zgdfxMtulphGc3A4vkwDRhq4bc';
    
    const existingUser = await db
      .select({
        userId: user.id,
        email: user.email,
        name: user.name,
        firstName: user.firstName,
        lastName: user.lastName,
        status: user.overallStatus,
        role: userRoles.role,
        roleStatus: userRoles.status,
        createdAt: user.createdAt,
      })
      .from(user)
      .leftJoin(userRoles, eq(user.id, userRoles.userId))
      .where(eq(user.id, adminUserId));
    
    return NextResponse.json({
      success: true,
      exists: existingUser.length > 0,
      user: existingUser[0] || null,
      message: existingUser.length > 0 ? 'Usuario encontrado' : 'Usuario no encontrado'
    });
    
  } catch (error) {
    console.error('Error consultando admin:', error);
    return NextResponse.json(
      { success: false, message: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}