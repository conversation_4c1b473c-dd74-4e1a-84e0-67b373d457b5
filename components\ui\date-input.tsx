'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Calendar, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn, formatDate } from '@/lib/utils';
import { format, addMonths, subMonths, startOfMonth, endOfMonth, startOfWeek, endOfWeek, addDays, isSameMonth, isSameDay, isToday, parse, isValid } from 'date-fns';
import { es } from 'date-fns/locale';
import { useRegionalConfig } from '@/hooks/use-regional-config';

interface DateInputProps {
  value: Date | undefined;
  onChange: (date: Date | undefined) => void;
  disabled?: boolean;
  className?: string;
  placeholder?: string;
  minDate?: Date;
  maxDate?: Date;
}

export function DateInput({ 
  value, 
  onChange, 
  disabled = false, 
  className, 
  placeholder,
  minDate,
  maxDate 
}: DateInputProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(value || new Date());
  const [inputValue, setInputValue] = useState('');
  const { config, getDatePlaceholder } = useRegionalConfig();
  const inputRef = useRef<HTMLInputElement>(null);
  
  // Use dynamic placeholder if not provided
  const displayPlaceholder = placeholder || getDatePlaceholder();

  // Sync input value with date value
  useEffect(() => {
    if (value && isValid(value)) {
      setInputValue(formatDate(value));
    } else {
      setInputValue('');
    }
  }, [value]);

  // Parse input value to date
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let newValue = e.target.value;
    
    // Remove all non-numeric characters except slashes
    const numbersOnly = newValue.replace(/[^\d]/g, '');
    
    // Auto-format as user types (add slashes automatically)
    let formattedValue = '';
    if (numbersOnly.length > 0) {
      // For dd/MM/yyyy format
      if (config.dateFormat === 'dd/MM/yyyy') {
        if (numbersOnly.length <= 2) {
          formattedValue = numbersOnly;
        } else if (numbersOnly.length <= 4) {
          formattedValue = numbersOnly.slice(0, 2) + '/' + numbersOnly.slice(2);
        } else {
          formattedValue = numbersOnly.slice(0, 2) + '/' + numbersOnly.slice(2, 4) + '/' + numbersOnly.slice(4, 8);
        }
      }
      // For MM/dd/yyyy format
      else if (config.dateFormat === 'MM/dd/yyyy') {
        if (numbersOnly.length <= 2) {
          formattedValue = numbersOnly;
        } else if (numbersOnly.length <= 4) {
          formattedValue = numbersOnly.slice(0, 2) + '/' + numbersOnly.slice(2);
        } else {
          formattedValue = numbersOnly.slice(0, 2) + '/' + numbersOnly.slice(2, 4) + '/' + numbersOnly.slice(4, 8);
        }
      }
      // For yyyy/MM/dd format
      else if (config.dateFormat === 'yyyy/MM/dd') {
        if (numbersOnly.length <= 4) {
          formattedValue = numbersOnly;
        } else if (numbersOnly.length <= 6) {
          formattedValue = numbersOnly.slice(0, 4) + '/' + numbersOnly.slice(4);
        } else {
          formattedValue = numbersOnly.slice(0, 4) + '/' + numbersOnly.slice(4, 6) + '/' + numbersOnly.slice(6, 8);
        }
      }
    }
    
    setInputValue(formattedValue);
    
    // Try to parse the date when we have enough digits
    const expectedLength = config.dateFormat.length; // 10 for dd/MM/yyyy
    if (formattedValue.length === expectedLength) {
      try {
        const parsedDate = parse(formattedValue, config.dateFormat, new Date());
        if (isValid(parsedDate)) {
          // Check date boundaries
          if (minDate && parsedDate < minDate) return;
          if (maxDate && parsedDate > maxDate) return;
          
          onChange(parsedDate);
          setCurrentMonth(parsedDate);
        }
      } catch (error) {
        // Invalid date, ignore
      }
    }
  };

  // Handle blur to validate and format
  const handleInputBlur = () => {
    if (!inputValue) {
      onChange(undefined);
      return;
    }

    // Try to parse with flexible formats
    const parseFlexibleDate = (input: string) => {
      // Clean input - remove all non-numeric characters
      const numbersOnly = input.replace(/[^\d]/g, '');
      
      // Try different interpretations based on length
      if (numbersOnly.length === 8) {
        // Could be ddMMyyyy, MMddyyyy, or yyyyMMdd
        const formats = [];
        if (config.dateFormat === 'dd/MM/yyyy') {
          formats.push('ddMMyyyy', 'dd/MM/yyyy');
        } else if (config.dateFormat === 'MM/dd/yyyy') {
          formats.push('MMddyyyy', 'MM/dd/yyyy');
        } else if (config.dateFormat === 'yyyy/MM/dd') {
          formats.push('yyyyMMdd', 'yyyy/MM/dd');
        }
        
        for (const format of formats) {
          try {
            const parsed = parse(numbersOnly, format, new Date());
            if (isValid(parsed)) return parsed;
          } catch {}
        }
      }
      
      // Try with current format (with slashes)
      try {
        const parsed = parse(input, config.dateFormat, new Date());
        if (isValid(parsed)) return parsed;
      } catch {}
      
      return null;
    };

    try {
      const parsedDate = parseFlexibleDate(inputValue);
      if (parsedDate) {
        // Check date boundaries
        if (minDate && parsedDate < minDate) {
          setInputValue(isValid(minDate) ? formatDate(minDate) : '');
          onChange(minDate);
          return;
        }
        if (maxDate && parsedDate > maxDate) {
          setInputValue(isValid(maxDate) ? formatDate(maxDate) : '');
          onChange(maxDate);
          return;
        }
        
        setInputValue(formatDate(parsedDate));
        onChange(parsedDate);
        setCurrentMonth(parsedDate);
      } else {
        // Reset to previous valid value
        setInputValue(value && isValid(value) ? formatDate(value) : '');
      }
    } catch (error) {
      // Reset to previous valid value
      setInputValue(value && isValid(value) ? formatDate(value) : '');
    }
  };

  // Calendar generation (same as DatePicker)
  const monthStart = startOfMonth(currentMonth);
  const monthEnd = endOfMonth(monthStart);
  const startDate = startOfWeek(monthStart, { weekStartsOn: config?.weekStartsOn || 1 });
  const endDate = endOfWeek(monthEnd, { weekStartsOn: config?.weekStartsOn || 1 });

  const dateFormat = "d";
  const rows = [];
  let days = [];
  let day = startDate;

  const weekDays = ['L', 'M', 'X', 'J', 'V', 'S', 'D'];

  while (day <= endDate) {
    for (let i = 0; i < 7; i++) {
      const formattedDate = format(day, dateFormat);
      const cloneDay = day;
      const isDisabled = (minDate && day < minDate) || (maxDate && day > maxDate);
      
      days.push(
        <div
          key={day.toString()}
          className={cn(
            "w-10 h-10 flex items-center justify-center text-sm cursor-pointer rounded-md transition-colors",
            !isSameMonth(day, monthStart) && "text-gray-300",
            isSameDay(day, value) && "bg-blue-500 text-white font-semibold",
            isToday(day) && !isSameDay(day, value) && "bg-blue-100 text-blue-800 font-medium",
            isSameMonth(day, monthStart) && !isSameDay(day, value) && !isToday(day) && !isDisabled && "hover:bg-gray-100",
            isDisabled && "text-gray-300 cursor-not-allowed"
          )}
          onClick={() => {
            if (!isDisabled) {
              onChange(cloneDay);
              setIsOpen(false);
              setInputValue(formatDate(cloneDay));
            }
          }}
        >
          {formattedDate}
        </div>
      );
      day = addDays(day, 1);
    }
    rows.push(
      <div key={day.toString()} className="grid grid-cols-7 gap-1">
        {days}
      </div>
    );
    days = [];
  }

  const handlePrevMonth = () => {
    setCurrentMonth(subMonths(currentMonth, 1));
  };

  const handleNextMonth = () => {
    setCurrentMonth(addMonths(currentMonth, 1));
  };

  if (disabled) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        {value && isValid(value) ? formatDate(value) : ''}
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <div className="relative">
        <Input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          onBlur={handleInputBlur}
          placeholder={displayPlaceholder}
          className="pr-10"
          disabled={disabled}
        />
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
          onClick={() => setIsOpen(!isOpen)}
        >
          <Calendar className="h-4 w-4 text-gray-400" />
        </Button>
      </div>
      
      {isOpen && (
        <>
          {/* Overlay para cerrar */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Selector de fecha */}
          <Card className="absolute z-50 top-12 left-0 w-80 shadow-lg border">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Header con navegación */}
                <div className="flex items-center justify-between">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handlePrevMonth}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  
                  <h3 className="font-semibold text-lg">
                    {format(currentMonth, 'MMMM yyyy', { locale: es })}
                  </h3>
                  
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={handleNextMonth}
                    className="h-8 w-8 p-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                
                {/* Días de la semana */}
                <div className="grid grid-cols-7 gap-1 mb-2">
                  {weekDays.map((day) => (
                    <div
                      key={day}
                      className="w-10 h-8 flex items-center justify-center text-xs font-semibold text-gray-600"
                    >
                      {day}
                    </div>
                  ))}
                </div>
                
                {/* Calendario */}
                <div className="space-y-1">
                  {rows}
                </div>
                
                {/* Botón para hoy */}
                <div className="border-t pt-3">
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => {
                      const today = new Date();
                      if ((!minDate || today >= minDate) && (!maxDate || today <= maxDate)) {
                        onChange(today);
                        setCurrentMonth(today);
                        setIsOpen(false);
                        setInputValue(formatDate(today));
                      }
                    }}
                    className="w-full text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                    disabled={
                      (minDate && new Date() < minDate) || 
                      (maxDate && new Date() > maxDate)
                    }
                  >
                    Seleccionar hoy
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}