import { db } from '@/db/drizzle';
import { sql } from 'drizzle-orm';

async function checkUserStatus() {
  const userId = 'user_2zkywFrWx1klhyfCzwGNzpVZ0Xg';

  try {
    console.log(`🔍 Verificando estado completo del usuario: ${userId}`);

    // 1. Usuario en tabla user
    const userQuery = await db.execute(sql`
      SELECT id, email, "overallStatus" as status FROM "user" WHERE id = ${userId} LIMIT 1
    `);

    console.log('👤 Usuario:', userQuery.rows[0] || 'NO ENCONTRADO');

    // 2. Roles activos
    const activeRolesQuery = await db.execute(sql`
      SELECT id, role, status, "rejectionReason", "rejectedAt" FROM user_roles 
      WHERE "userId" = ${userId} AND status = 'active'
    `);

    console.log('✅ Roles activos:', activeRolesQuery.rows);

    // 3. Roles rechazados
    const rejectedRolesQuery = await db.execute(sql`
      SELECT id, role, status, "rejectionReason", "rejectedAt" FROM user_roles 
      WHERE "userId" = ${userId} AND status = 'rejected'
    `);

    console.log('❌ Roles rechazados:', rejectedRolesQuery.rows);

    // 4. Todos los roles (para debug)
    const allRolesQuery = await db.execute(sql`
      SELECT id, role, status, "rejectionReason", "rejectedAt" FROM user_roles 
      WHERE "userId" = ${userId}
    `);

    console.log('📋 TODOS los roles:', allRolesQuery.rows);

    // 5. Solicitudes pendientes
    const pendingRequestsQuery = await db.execute(sql`
      SELECT id, role, status, "rejectionReason" FROM "registrationRequests" 
      WHERE "userId" = ${userId} AND status = 'pending'
    `);

    console.log('⏳ Solicitudes pendientes:', pendingRequestsQuery.rows);

    // 6. Solicitudes rechazadas
    const rejectedRequestsQuery = await db.execute(sql`
      SELECT id, role, status, "rejectionReason" FROM "registrationRequests" 
      WHERE "userId" = ${userId} AND status = 'rejected'
    `);

    console.log('🚫 Solicitudes rechazadas:', rejectedRequestsQuery.rows);

    // Resumen
    console.log('\n📊 RESUMEN:');
    console.log(`- Usuario status: ${userQuery.rows[0]?.status || 'NO ENCONTRADO'}`);
    console.log(`- Roles activos: ${activeRolesQuery.rows.length}`);
    console.log(`- Roles rechazados: ${rejectedRolesQuery.rows.length}`);
    console.log(`- Solicitudes pendientes: ${pendingRequestsQuery.rows.length}`);
    console.log(`- Solicitudes rechazadas: ${rejectedRequestsQuery.rows.length}`);

  } catch (error) {
    console.error('❌ Error verificando estado del usuario:', error);
  }
}

// Ejecutar el script
checkUserStatus().then(() => {
  console.log('🏁 Verificación completada');
  process.exit(0);
}).catch((error) => {
  console.error('💥 Error fatal:', error);
  process.exit(1);
}); 