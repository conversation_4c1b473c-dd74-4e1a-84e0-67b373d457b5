import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { religions } from '@/db/schema';
import { eq, and, desc, ilike, count, or } from 'drizzle-orm';
import { nanoid } from 'nanoid';
import { executeWithRetry, handleDatabaseError } from '@/lib/db-utils';

// GET /api/catalogs/religions - Listar religiones con filtros
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all'; // all, active, inactive
    const category = searchParams.get('category') || '';
    const sortBy = searchParams.get('sortBy') || 'order';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const offset = (page - 1) * limit;

    // Construir condiciones WHERE
    const conditions = [];
    
    if (status === 'active') {
      conditions.push(eq(religions.isActive, true));
    } else if (status === 'inactive') {
      conditions.push(eq(religions.isActive, false));
    }

    if (category && category !== 'all') {
      conditions.push(eq(religions.category, category));
    }

    if (search) {
      // Buscar en nombre y descripción
      conditions.push(
        or(
          ilike(religions.name, `%${search}%`),
          ilike(religions.description, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Obtener total de registros con reintentos
    const totalResult = await executeWithRetry(async () => {
      return await db
        .select({ count: count() })
        .from(religions)
        .where(whereClause);
    });

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Obtener datos con paginación y reintentos
    const orderColumn = sortBy === 'category' ? religions.category : 
                       sortBy === 'name' ? religions.name :
                       religions.order;

    const data = await executeWithRetry(async () => {
      return await db
        .select()
        .from(religions)
        .where(whereClause)
        .orderBy(sortOrder === 'desc' ? desc(orderColumn) : orderColumn)
        .limit(limit)
        .offset(offset);
    });

    // Obtener categorías para filtros con reintentos
    const categories = await executeWithRetry(async () => {
      return await db
        .selectDistinct({ category: religions.category })
        .from(religions)
        .where(eq(religions.isActive, true))
        .orderBy(religions.category);
    });

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        categories: categories.map(c => c.category).filter(Boolean),
      },
    });

  } catch (error: any) {
    const { message, status } = handleDatabaseError(error);
    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}

// POST /api/catalogs/religions - Crear nueva religión
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validaciones básicas
    if (!body.name || !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre de la religión es requerido' },
        { status: 400 }
      );
    }

    if (!body.category || !body.category.trim()) {
      return NextResponse.json(
        { error: 'La categoría es requerida' },
        { status: 400 }
      );
    }

    // Verificar si ya existe una religión con el mismo nombre
    const existingReligion = await executeWithRetry(async () => {
      return await db
        .select()
        .from(religions)
        .where(eq(religions.name, body.name.trim()))
        .limit(1);
    });

    if (existingReligion.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe una religión con ese nombre' },
        { status: 400 }
      );
    }

    // Crear nueva religión
    const newReligion = {
      id: nanoid(),
      name: body.name.trim(),
      category: body.category.trim(),
      description: body.description?.trim() || null,
      order: body.order || 0,
      isActive: body.isActive !== undefined ? body.isActive : true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await executeWithRetry(async () => {
      return await db.insert(religions).values(newReligion);
    });

    return NextResponse.json({
      success: true,
      message: 'Religión creada exitosamente',
      data: newReligion,
    });

  } catch (error: any) {
    const { message, status } = handleDatabaseError(error);
    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}

// PUT /api/catalogs/religions - Actualizar religión
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { error: 'El ID es requerido para actualizar' },
        { status: 400 }
      );
    }

    // Verificar que la religión existe
    const existingReligion = await executeWithRetry(async () => {
      return await db
        .select()
        .from(religions)
        .where(eq(religions.id, body.id))
        .limit(1);
    });

    if (!existingReligion.length) {
      return NextResponse.json(
        { error: 'Religión no encontrada' },
        { status: 404 }
      );
    }

    // Validaciones básicas para actualización
    if (body.name && !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre de la religión no puede estar vacío' },
        { status: 400 }
      );
    }

    // Preparar datos para actualizar
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Solo actualizar campos que se proporcionan
    if (body.name !== undefined) updateData.name = body.name.trim();
    if (body.category !== undefined) updateData.category = body.category.trim();
    if (body.description !== undefined) updateData.description = body.description?.trim() || null;
    if (body.order !== undefined) updateData.order = body.order || 0;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;

    // Actualizar religión
    await executeWithRetry(async () => {
      return await db
        .update(religions)
        .set(updateData)
        .where(eq(religions.id, body.id));
    });

    return NextResponse.json({
      success: true,
      message: 'Religión actualizada exitosamente',
    });

  } catch (error: any) {
    const { message, status } = handleDatabaseError(error);
    return NextResponse.json(
      { error: message },
      { status }
    );
  }
}