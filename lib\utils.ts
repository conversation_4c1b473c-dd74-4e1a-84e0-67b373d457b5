import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { randomBytes } from "crypto"
import { format } from "date-fns"
import { es } from "date-fns/locale"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function generateId(): string {
  return randomBytes(16).toString('hex');
}

// Generar ID corto para emails temporales (6-8 caracteres)
export function generateShortId(): string {
  return randomBytes(3).toString('hex'); // 6 caracteres hexadecimales
}

// Formatear email temporal para mostrar de forma amigable
export function formatTempEmail(email: string): string {
  if (!email) return '';
  
  // Si es un email temporal, mostrar versión amigable
  if (email.includes('@temp.local')) {
    if (email.startsWith('patient_')) {
      return '👶 Email temporal (Paciente)';
    } else if (email.startsWith('guardian_')) {
      return '👨‍👩‍👧‍👦 Email temporal (Tutor)';
    } else {
      return '📧 Email temporal';
    }
  }
  
  // Si es un email real, devolverlo tal como está
  return email;
}

// Configuración de localización
export interface LocalizationConfig {
  defaultCountryId: number;
  defaultCountryCode: string;
  defaultCountryName: string;
  administrativeLevels: {
    level1: {
      name: string; // "Departamento", "Estado", "Provincia"
      placeholder: string;
    };
    level2: {
      name: string; // "Municipio", "Ciudad", "Condado"
      placeholder: string;
    };
  };
  phoneCode: string;
  locale: string;
  timezone: string;
}

// Configuraciones predeterminadas por país
export const COUNTRY_LOCALIZATION_CONFIG: Record<string, LocalizationConfig> = {
  'GT': {
    defaultCountryId: 1,
    defaultCountryCode: 'GT',
    defaultCountryName: 'Guatemala',
    administrativeLevels: {
      level1: {
        name: 'Departamento',
        placeholder: 'Selecciona departamento'
      },
      level2: {
        name: 'Municipio',
        placeholder: 'Selecciona municipio'
      }
    },
    phoneCode: '+502',
    locale: 'es-GT',
    timezone: 'America/Guatemala'
  },
  'MX': {
    defaultCountryId: 2,
    defaultCountryCode: 'MX',
    defaultCountryName: 'México',
    administrativeLevels: {
      level1: {
        name: 'Estado',
        placeholder: 'Selecciona estado'
      },
      level2: {
        name: 'Municipio',
        placeholder: 'Selecciona municipio'
      }
    },
    phoneCode: '+52',
    locale: 'es-MX',
    timezone: 'America/Mexico_City'
  },
  'US': {
    defaultCountryId: 3,
    defaultCountryCode: 'US',
    defaultCountryName: 'Estados Unidos',
    administrativeLevels: {
      level1: {
        name: 'Estado',
        placeholder: 'Select state'
      },
      level2: {
        name: 'Condado',
        placeholder: 'Select county'
      }
    },
    phoneCode: '+1',
    locale: 'en-US',
    timezone: 'America/New_York'
  }
};

// Función para obtener configuración de localización
export function getLocalizationConfig(countryCode?: string): LocalizationConfig {
  // Por defecto usar Guatemala si no se especifica país
  const defaultCountry = countryCode || 'GT';
  return COUNTRY_LOCALIZATION_CONFIG[defaultCountry] || COUNTRY_LOCALIZATION_CONFIG['GT'];
}

// Función para obtener etiquetas dinámicas basadas en país
export function getLocationLabels(countryId?: number): {
  level1: string;
  level2: string;
  level1Placeholder: string;
  level2Placeholder: string;
} {
  // Guatemala = 1, México = 2, Estados Unidos = 3
  switch (countryId) {
    case 1: // Guatemala
      return {
        level1: 'Departamento',
        level2: 'Municipio',
        level1Placeholder: 'Selecciona departamento',
        level2Placeholder: 'Selecciona municipio'
      };
    case 2: // México
      return {
        level1: 'Estado',
        level2: 'Municipio',
        level1Placeholder: 'Selecciona estado',
        level2Placeholder: 'Selecciona municipio'
      };
    case 3: // Estados Unidos
      return {
        level1: 'Estado',
        level2: 'Condado',
        level1Placeholder: 'Select state',
        level2Placeholder: 'Select county'
      };
    default:
      // Por defecto Guatemala
      return {
        level1: 'Departamento',
        level2: 'Municipio',
        level1Placeholder: 'Selecciona departamento',
        level2Placeholder: 'Selecciona municipio'
      };
  }
}

// Función global para formatear fechas
export function formatDate(date: string | Date | null | undefined, formatStr: string = 'dd/MM/yyyy'): string {
  if (!date) return '';
  
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return format(dateObj, formatStr, { locale: es });
  } catch (error) {
    console.error('Error formatting date:', error);
    return '';
  }
}

// Función para formatear fecha y hora
export function formatDateTime(date: string | Date | null | undefined): string {
  return formatDate(date, 'dd/MM/yyyy HH:mm');
}

// Función para formatear solo hora
export function formatTime(date: string | Date | null | undefined): string {
  return formatDate(date, 'HH:mm');
}

// Función para formatear moneda con configuración regional
export function formatCurrency(
  amount: number | string | null | undefined, 
  currency: string = 'GTQ',
  currencySymbol: string = 'Q',
  position: 'before' | 'after' = 'before',
  decimalSeparator: string = '.',
  thousandsSeparator: string = ','
): string {
  if (amount === null || amount === undefined) return '';
  
  const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;
  if (isNaN(numAmount)) return '';
  
  // Formatear el número con separadores
  const parts = numAmount.toFixed(2).split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  const formattedAmount = parts.join(decimalSeparator);
  
  // Aplicar posición del símbolo
  if (position === 'before') {
    return `${currencySymbol}${formattedAmount}`;
  } else {
    return `${formattedAmount}${currencySymbol}`;
  }
}

// Función para formatear números con configuración regional
export function formatNumber(
  value: number | string | null | undefined,
  decimals: number = 2,
  decimalSeparator: string = '.',
  thousandsSeparator: string = ','
): string {
  if (value === null || value === undefined) return '';
  
  const numValue = typeof value === 'string' ? parseFloat(value) : value;
  if (isNaN(numValue)) return '';
  
  const parts = numValue.toFixed(decimals).split('.');
  parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, thousandsSeparator);
  return parts.join(decimalSeparator);
}

// Función para traducir relaciones de inglés a español
export function translateRelationship(relationship?: string): string {
  if (!relationship) return 'responsable';
  
  const translations: Record<string, string> = {
    'parent': 'padre/madre',
    'father': 'padre', 
    'mother': 'madre',
    'guardian': 'tutor',
    'grandparent': 'abuelo/abuela',
    'grandfather': 'abuelo',
    'grandmother': 'abuela',
    'uncle': 'tío',
    'aunt': 'tía',
    'sibling': 'hermano/hermana',
    'brother': 'hermano',
    'sister': 'hermana',
    'spouse': 'cónyuge',
    'caregiver': 'cuidador',
    'legal_guardian': 'tutor legal',
    'responsable': 'responsable',
    'tutor': 'tutor',
    'encargado': 'encargado'
  };
  
  // Buscar traducción (insensible a mayúsculas)
  const key = relationship.toLowerCase();
  return translations[key] || relationship || 'responsable';
}

// Función para invertir relaciones familiares (para mostrar desde la perspectiva del guardián)
export function getInverseRelationship(guardianRelationship?: string): string {
  if (!guardianRelationship) return 'dependiente';
  
  const inverseRelations: Record<string, string> = {
    'parent': 'hijo/hija',
    'father': 'hijo/hija',
    'mother': 'hijo/hija',
    'grandparent': 'nieto/nieta',
    'grandfather': 'nieto/nieta',
    'grandmother': 'nieto/nieta',
    'uncle': 'sobrino/sobrina',
    'aunt': 'sobrino/sobrina',
    'sibling': 'hermano/hermana',
    'brother': 'hermano/hermana',
    'sister': 'hermano/hermana',
    'guardian': 'pupilo/pupila',
    'legal_guardian': 'pupilo/pupila',
    'caregiver': 'persona bajo cuidado',
    'spouse': 'cónyuge',
    'responsable': 'dependiente',
    'tutor': 'pupilo/pupila',
    'encargado': 'dependiente'
  };
  
  // Buscar relación inversa (insensible a mayúsculas)
  const key = guardianRelationship.toLowerCase();
  return inverseRelations[key] || 'dependiente';
}
