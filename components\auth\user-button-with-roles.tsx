'use client';

import { UserButton } from '@clerk/nextjs';
import { useEffect, useState } from 'react';
import { useRouter, usePathname } from 'next/navigation';
import { Stethoscope, UserCheck, User, Heart, Package, Briefcase, Home } from 'lucide-react';

interface UserContexts {
  work: {
    isDoctor?: any;
    isAssistant?: any;
    isProvider?: any;
  };
  personal: {
    isPatient?: any;
    isGuardian?: any;
  };
}

interface RoleOption {
  role: string;
  label: string;
  icon: string;
  url: string;
}

export function UserButtonWithRoles({ appearance }: { appearance?: any }) {
  const [roleOptions, setRoleOptions] = useState<RoleOption[]>([]);
  const [isMounted, setIsMounted] = useState(false);
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    setIsMounted(true);
    fetchAvailableRoles();
  }, []);

  const fetchAvailableRoles = async () => {
    try {
      const response = await fetch('/api/auth/user-contexts');
      if (!response.ok) return;
      
      const data = await response.json();
      const contexts: UserContexts = data.contexts;
      
      const options: RoleOption[] = [];
      
      // Agregar roles de trabajo
      if (contexts.work.isDoctor) {
        options.push({
          role: 'doctor',
          label: 'Médico',
          icon: '🩺',
          url: '/dashboard/doctor'
        });
      }
      
      if (contexts.work.isAssistant) {
        options.push({
          role: 'assistant', 
          label: 'Asistente',
          icon: '👩‍💼',
          url: '/dashboard/assistant'
        });
      }
      
      if (contexts.work.isProvider) {
        options.push({
          role: 'provider',
          label: 'Proveedor', 
          icon: '📦',
          url: '/dashboard/provider'
        });
      }
      
      // Agregar roles personales
      if (contexts.personal.isPatient) {
        options.push({
          role: 'patient',
          label: 'Paciente',
          icon: '👤',
          url: '/dashboard/patient'
        });
      }
      
      if (contexts.personal.isGuardian) {
        options.push({
          role: 'guardian',
          label: 'Encargado',
          icon: '👨‍👩‍👧‍👦',
          url: '/dashboard/guardian'
        });
      }
      
      // Solo mostrar opciones si hay múltiples roles y no está ya en esa página
      const filteredOptions = options.filter(option => 
        options.length > 1 && !pathname.includes(option.role)
      );
      
      setRoleOptions(filteredOptions);
      
    } catch (error) {
      console.error('Error fetching available roles:', error);
    }
  };

  // No renderizar hasta que el componente esté montado en el cliente
  if (!isMounted) {
    return null;
  }

  // Si no hay opciones de roles, mostrar UserButton normal
  if (roleOptions.length === 0) {
    return <UserButton appearance={appearance} />;
  }

  // Validar que todas las opciones tengan label
  const validOptions = roleOptions.filter(option => 
    option && option.label && option.label.trim() !== ''
  );
  
  if (validOptions.length === 0) {
    return <UserButton appearance={appearance} />;
  }

  return (
    <UserButton 
      appearance={appearance}
    >
      <UserButton.MenuItems>
        {validOptions.map((option) => (
          <UserButton.Action
            key={option.role}
            label={option.label}
            onClick={() => {
              router.push(option.url);
            }}
          />
        ))}
      </UserButton.MenuItems>
    </UserButton>
  );
}