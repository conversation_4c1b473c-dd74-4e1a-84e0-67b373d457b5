#!/usr/bin/env tsx

/**
 * Script para probar el flujo completo del sistema médico:
 * 1. <PERSON><PERSON><PERSON> paciente
 * 2. Agendar cita
 * 3. Enviar pre-checkin (simulado)
 * 4. Realizar consulta médica
 * 5. Actualizar historial médico
 */

import { db } from '../db/drizzle';
import { 
  user, 
  userRoles, 
  appointments, 
  medicalRecords, 
  medicalConsultations,
  patientMedicalHistory,
  consultories,
  medicalServices
} from '../db/schema';
import { eq, and } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// Colores para los logs
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

const log = (message: string, color = colors.reset) => {
  console.log(`${color}${message}${colors.reset}`);
};

const stepLog = (step: number, message: string) => {
  log(`\n${colors.bright}${colors.blue}PASO ${step}: ${message}${colors.reset}`);
};

const successLog = (message: string) => {
  log(`✅ ${message}`, colors.green);
};

const errorLog = (message: string) => {
  log(`❌ ${message}`, colors.red);
};

const warningLog = (message: string) => {
  log(`⚠️  ${message}`, colors.yellow);
};

interface TestData {
  patient?: any;
  doctor?: any;
  consultory?: any;
  service?: any;
  appointment?: any;
  medicalRecord?: any;
  consultation?: any;
}

async function testCompleteFlow() {
  log(`\n${colors.bright}${colors.magenta}🧪 INICIANDO PRUEBA DEL FLUJO COMPLETO${colors.reset}`);
  log(`${colors.cyan}Fecha: ${new Date().toLocaleString('es-ES')}${colors.reset}`);
  
  const testData: TestData = {};
  
  try {
    // PASO 1: Verificar que existe un doctor para las pruebas
    stepLog(1, 'Verificar doctor de prueba');
    
    const doctorResult = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        consultoryId: userRoles.consultoryId
      })
      .from(user)
      .innerJoin(userRoles, and(
        eq(userRoles.userId, user.id),
        eq(userRoles.role, 'doctor'),
        eq(userRoles.status, 'active')
      ))
      .limit(1);

    if (doctorResult.length === 0) {
      errorLog('No se encontró ningún doctor activo en el sistema');
      return false;
    }

    testData.doctor = doctorResult[0];
    successLog(`Doctor encontrado: Dr. ${testData.doctor.firstName} ${testData.doctor.lastName}`);

    // Verificar consultorio
    if (testData.doctor.consultoryId) {
      const consultoryResult = await db
        .select()
        .from(consultories)
        .where(and(
          eq(consultories.id, testData.doctor.consultoryId),
          eq(consultories.isActive, true)
        ))
        .limit(1);

      if (consultoryResult.length > 0) {
        testData.consultory = consultoryResult[0];
        successLog(`Consultorio: ${testData.consultory.name}`);
      }
    }

    // PASO 2: Crear paciente de prueba
    stepLog(2, 'Crear paciente de prueba');
    
    const patientId = nanoid();
    const patientEmail = `paciente.prueba.${Date.now()}@test.com`;
    
    const patientData = {
      id: patientId,
      firstName: 'Juan Carlos',
      lastName: 'Pérez García',
      email: patientEmail,
      phone: '+502 1234-5678',
      dateOfBirth: new Date('1985-03-15'),
      gender: 'male' as const,
      documentType: 'DPI',
      documentNumber: '1234567890101',
      address: 'Ciudad de Guatemala',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const newPatient = await db.insert(user).values(patientData).returning();
    testData.patient = newPatient[0];
    successLog(`Paciente creado: ${testData.patient.firstName} ${testData.patient.lastName}`);

    // Crear rol de paciente
    await db.insert(userRoles).values({
      id: nanoid(),
      userId: patientId,
      role: 'patient',
      status: 'active',
      consultoryId: testData.doctor.consultoryId,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    successLog('Rol de paciente asignado');

    // PASO 3: Obtener servicio médico para la cita
    stepLog(3, 'Obtener servicio médico');
    
    const serviceResult = await db
      .select()
      .from(medicalServices)
      .where(eq(medicalServices.isActive, true))
      .limit(1);

    if (serviceResult.length > 0) {
      testData.service = serviceResult[0];
      successLog(`Servicio médico: ${testData.service.name}`);
    } else {
      warningLog('No se encontraron servicios médicos, se usará servicio genérico');
    }

    // PASO 4: Crear cita médica
    stepLog(4, 'Crear cita médica');
    
    const appointmentId = nanoid();
    const scheduledDate = new Date();
    scheduledDate.setDate(scheduledDate.getDate() + 1); // Mañana
    scheduledDate.setHours(10, 0, 0, 0); // 10:00 AM

    const appointmentData = {
      id: appointmentId,
      doctorId: testData.doctor.id,
      patientId: testData.patient.id,
      consultoryId: testData.doctor.consultoryId,
      scheduledDate,
      startTime: scheduledDate,
      endTime: new Date(scheduledDate.getTime() + 30 * 60 * 1000), // 30 minutos
      duration: 30,
      status: 'confirmed' as const,
      serviceId: testData.service?.id || null,
      serviceName: testData.service?.name || 'Consulta médica',
      patientFirstName: testData.patient.firstName,
      patientLastName: testData.patient.lastName,
      doctorFirstName: testData.doctor.firstName,
      doctorLastName: testData.doctor.lastName,
      chiefComplaint: 'Dolor de cabeza recurrente',
      preCheckinCompleted: false,
      createdBy: testData.doctor.id,
      updatedBy: testData.doctor.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const newAppointment = await db.insert(appointments).values(appointmentData).returning();
    testData.appointment = newAppointment[0];
    successLog(`Cita creada para ${scheduledDate.toLocaleDateString('es-ES')} a las ${scheduledDate.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}`);

    // PASO 5: Simular pre-checkin del paciente
    stepLog(5, 'Simular pre-checkin del paciente');
    
    const preCheckinData = {
      willAttend: 'yes',
      hasSymptoms: true,
      symptoms: 'Dolor de cabeza intenso desde hace 3 días, acompañado de náuseas ocasionales',
      takingMedications: true,
      medications: 'Paracetamol 500mg cada 8 horas',
      hasAllergies: true,
      allergies: 'Penicilina - causa sarpullido',
      phone: testData.patient.phone,
      emergencyContact: 'María Pérez (hermana)',
      emergencyPhone: '+502 8765-4321',
      chiefComplaint: 'Dolor de cabeza persistente y náuseas',
      additionalNotes: 'El dolor empeora por las mañanas y mejora levemente con el paracetamol',
      completedAt: new Date().toISOString(),
      mode: 'complete'
    };

    // Actualizar cita con datos del pre-checkin
    await db
      .update(appointments)
      .set({
        preCheckinCompleted: true,
        preCheckinData: preCheckinData,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    successLog('Pre-checkin completado y guardado en la cita');

    // PASO 6: Crear expediente médico desde pre-checkin
    stepLog(6, 'Crear expediente médico desde información del pre-checkin');
    
    const recordNumber = `EXP-${new Date().getFullYear()}-${String(Math.floor(Math.random() * 1000)).padStart(3, '0')}`;
    
    const medicalRecordData = {
      id: nanoid(),
      patientId: testData.patient.id,
      consultoryId: testData.doctor.consultoryId,
      primaryDoctorId: testData.doctor.id,
      recordNumber,
      openDate: new Date(),
      status: 'active' as const,
      patientSummary: {
        fullName: `${testData.patient.firstName} ${testData.patient.lastName}`,
        dateOfBirth: testData.patient.dateOfBirth,
        age: new Date().getFullYear() - new Date(testData.patient.dateOfBirth).getFullYear()
      },
      isMinor: false,
      demographics: {
        bloodType: 'O+',
        weight: '70',
        height: '175'
      },
      vitalSigns: {
        temperature: '36.5',
        bloodPressure: '120/80',
        heartRate: '72',
        respiratoryRate: '16',
        oxygenSaturation: '98',
        weight: '70',
        height: '175'
      },
      allergies: [
        {
          substance: 'Penicilina',
          reaction: 'Sarpullido',
          severity: 'moderate',
          confirmedDate: new Date().toISOString().split('T')[0]
        }
      ],
      currentMedications: [
        {
          name: 'Paracetamol',
          dosage: '500mg',
          frequency: 'cada 8 horas',
          startDate: new Date().toISOString().split('T')[0],
          isActive: true
        }
      ],
      medicalEmergencyContact: 'María Pérez (hermana)',
      medicalEmergencyPhone: '+502 8765-4321',
      doctorNotes: 'Expediente creado desde pre-checkin. Paciente refiere dolor de cabeza persistente.',
      totalConsultations: 0,
      lastAccessDate: new Date(),
      createdBy: testData.doctor.id,
      updatedBy: testData.doctor.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const newMedicalRecord = await db.insert(medicalRecords).values(medicalRecordData).returning();
    testData.medicalRecord = newMedicalRecord[0];
    successLog(`Expediente médico creado: ${testData.medicalRecord.recordNumber}`);

    // Crear historial médico inicial
    await db.insert(patientMedicalHistory).values({
      id: nanoid(),
      medicalRecordId: testData.medicalRecord.id,
      pathologicalHistory: [],
      nonPathologicalHistory: [],
      familyHistory: [],
      allergies: medicalRecordData.allergies,
      hospitalizations: [],
      surgeries: [],
      vaccinations: [],
      updatedBy: testData.doctor.id,
      createdAt: new Date(),
      updatedAt: new Date()
    });

    successLog('Historial médico inicial creado');

    // PASO 7: Simular check-in del paciente
    stepLog(7, 'Simular check-in del paciente en la clínica');
    
    await db
      .update(appointments)
      .set({
        status: 'checked_in',
        checkedInAt: new Date(),
        checkedInBy: testData.doctor.id,
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    successLog('Paciente registrado como llegado (check-in completado)');

    // PASO 8: Crear consulta médica
    stepLog(8, 'Registrar consulta médica');
    
    const consultationData = {
      id: nanoid(),
      appointmentId: testData.appointment.id,
      patientId: testData.patient.id,
      doctorId: testData.doctor.id,
      medicalRecordId: testData.medicalRecord.id,
      consultoryId: testData.doctor.consultoryId,
      consultationDate: new Date(),
      chiefComplaint: preCheckinData.chiefComplaint,
      consultationNotes: `
MOTIVO DE CONSULTA:
${preCheckinData.chiefComplaint}

SÍNTOMAS ACTUALES:
${preCheckinData.symptoms}

MEDICAMENTOS ACTUALES:
${preCheckinData.medications}

ALERGIAS CONOCIDAS:
${preCheckinData.allergies}

EXPLORACIÓN FÍSICA:
- Signos vitales estables
- Paciente consciente y orientado
- No se observan signos neurológicos focales

IMPRESIÓN DIAGNÓSTICA:
Cefalea tensional

PLAN DE TRATAMIENTO:
1. Continuar con paracetamol según necesidad
2. Hidratación adecuada
3. Descanso
4. Evitar factores desencadenantes (estrés, falta de sueño)
5. Control en 1 semana si persisten síntomas

OBSERVACIONES:
Paciente educado sobre manejo de cefalea tensional y signos de alarma.
      `,
      diagnosis: 'Cefalea tensional',
      treatment: 'Tratamiento sintomático con analgésicos y medidas generales',
      prescriptions: [
        {
          medication: 'Paracetamol',
          dosage: '500mg',
          frequency: 'cada 8 horas por necesidad',
          duration: '7 días',
          instructions: 'Tomar con alimentos. No exceder 3 gramos por día.'
        }
      ],
      followUpInstructions: 'Control médico en 1 semana si persisten los síntomas. Acudir inmediatamente si presenta: fiebre alta, rigidez nucal, alteraciones visuales o neurológicas.',
      nextAppointmentRecommended: true,
      nextAppointmentDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // En 1 semana
      consultationStatus: 'completed' as const,
      duration: 30,
      createdBy: testData.doctor.id,
      updatedBy: testData.doctor.id,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const newConsultation = await db.insert(medicalConsultations).values(consultationData).returning();
    testData.consultation = newConsultation[0];
    successLog('Consulta médica registrada exitosamente');

    // PASO 9: Actualizar estadísticas del expediente médico
    stepLog(9, 'Actualizar estadísticas del expediente médico');
    
    await db
      .update(medicalRecords)
      .set({
        totalConsultations: 1,
        lastAccessDate: new Date(),
        updatedAt: new Date(),
        updatedBy: testData.doctor.id
      })
      .where(eq(medicalRecords.id, testData.medicalRecord.id));

    // Actualizar historial médico con nuevo diagnóstico
    await db
      .update(patientMedicalHistory)
      .set({
        pathologicalHistory: [
          {
            condition: 'Cefalea tensional',
            diagnosedDate: new Date().toISOString().split('T')[0],
            status: 'active',
            notes: 'Dolor de cabeza recurrente, probablemente relacionado con estrés'
          }
        ],
        updatedBy: testData.doctor.id,
        updatedAt: new Date()
      })
      .where(eq(patientMedicalHistory.medicalRecordId, testData.medicalRecord.id));

    successLog('Historial médico actualizado con nuevo diagnóstico');

    // PASO 10: Finalizar cita
    stepLog(10, 'Finalizar cita médica');
    
    await db
      .update(appointments)
      .set({
        status: 'completed',
        completedAt: new Date(),
        updatedAt: new Date()
      })
      .where(eq(appointments.id, appointmentId));

    successLog('Cita marcada como completada');

    // RESUMEN FINAL
    log(`\n${colors.bright}${colors.green}🎉 ¡FLUJO COMPLETO EJECUTADO EXITOSAMENTE! 🎉${colors.reset}`);
    
    log(`\n${colors.bright}RESUMEN DE LA PRUEBA:${colors.reset}`);
    log(`${colors.cyan}├─ Paciente creado: ${testData.patient.firstName} ${testData.patient.lastName}${colors.reset}`);
    log(`${colors.cyan}├─ Doctor asignado: Dr. ${testData.doctor.firstName} ${testData.doctor.lastName}${colors.reset}`);
    log(`${colors.cyan}├─ Cita programada: ${testData.appointment.scheduledDate.toLocaleDateString('es-ES')}${colors.reset}`);
    log(`${colors.cyan}├─ Pre-checkin completado: ✅${colors.reset}`);
    log(`${colors.cyan}├─ Expediente médico: ${testData.medicalRecord.recordNumber}${colors.reset}`);
    log(`${colors.cyan}├─ Check-in realizado: ✅${colors.reset}`);
    log(`${colors.cyan}├─ Consulta médica: ${testData.consultation.id}${colors.reset}`);
    log(`${colors.cyan}├─ Diagnóstico: ${consultationData.diagnosis}${colors.reset}`);
    log(`${colors.cyan}└─ Cita completada: ✅${colors.reset}`);

    log(`\n${colors.bright}${colors.magenta}DATOS PARA VERIFICACIÓN MANUAL:${colors.reset}`);
    log(`${colors.yellow}• ID del Paciente: ${testData.patient.id}${colors.reset}`);
    log(`${colors.yellow}• ID de la Cita: ${testData.appointment.id}${colors.reset}`);
    log(`${colors.yellow}• ID del Expediente: ${testData.medicalRecord.id}${colors.reset}`);
    log(`${colors.yellow}• ID de la Consulta: ${testData.consultation.id}${colors.reset}`);

    return true;

  } catch (error) {
    errorLog(`Error durante la ejecución del flujo: ${error}`);
    console.error(error);
    return false;
  }
}

// Ejecutar el script
if (require.main === module) {
  testCompleteFlow()
    .then((success) => {
      if (success) {
        log(`\n${colors.bright}${colors.green}✅ Prueba completada exitosamente${colors.reset}`);
        process.exit(0);
      } else {
        log(`\n${colors.bright}${colors.red}❌ Prueba falló${colors.reset}`);
        process.exit(1);
      }
    })
    .catch((error) => {
      errorLog(`Error crítico: ${error}`);
      process.exit(1);
    });
}

export { testCompleteFlow };