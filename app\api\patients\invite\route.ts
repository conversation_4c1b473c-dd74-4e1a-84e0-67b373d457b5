import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, patientInvitations, guardianPatientRelations } from '@/db/schema';
import { generateId } from '@/lib/utils';
import { eq } from 'drizzle-orm';
import crypto from 'crypto';

export async function POST(request: NextRequest) {
  try {
    const { userId: currentUserId } = await auth();
    
    if (!currentUserId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const {
      patientId,
      guardianEmail,
      relationship = 'parent'
    } = body;

    if (!patientId || !guardianEmail) {
      return NextResponse.json(
        { error: 'ID del paciente y email del guardián son requeridos' },
        { status: 400 }
      );
    }

    // Verificar que el paciente existe
    const patient = await db
      .select()
      .from(user)
      .where(eq(user.id, patientId))
      .limit(1);

    if (patient.length === 0) {
      return NextResponse.json(
        { error: 'Paciente no encontrado' },
        { status: 404 }
      );
    }

    // Verificar que el guardián existe (usuario con ese email)
    const guardian = await db
      .select()
      .from(user)
      .where(eq(user.email, guardianEmail))
      .limit(1);

    if (guardian.length === 0) {
      return NextResponse.json(
        { error: 'No se encontró usuario con ese email. El guardián debe tener una cuenta en el sistema.' },
        { status: 400 }
      );
    }

    // Verificar si ya existe una relación
    const existingRelation = await db
      .select()
      .from(guardianPatientRelations)
      .where(eq(guardianPatientRelations.guardianId, guardian[0].id))
      .where(eq(guardianPatientRelations.patientId, patientId))
      .limit(1);

    if (existingRelation.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe una relación entre este guardián y paciente' },
        { status: 400 }
      );
    }

    // Generar token único para la invitación
    const invitationToken = crypto.randomUUID();
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días

    await db.transaction(async (tx) => {
      // 1. Crear invitación
      await tx.insert(patientInvitations).values({
        id: generateId(),
        patientUserId: patientId,
        guardianEmail,
        invitationToken,
        status: 'pending',
        expiresAt,
      });

      // 2. Crear relación familiar inmediatamente (será activada cuando se acepte)
      await tx.insert(guardianPatientRelations).values({
        id: generateId(),
        guardianId: guardian[0].id,
        patientId: patientId,
        relationship,
        isPrimary: false, // Se puede marcar como primario después
        canMakeDecisions: true,
      });
    });

    return NextResponse.json({
      success: true,
      data: {
        invitationToken,
        expiresAt,
        guardianName: `${guardian[0].firstName} ${guardian[0].lastName}`,
        patientName: `${patient[0].firstName} ${patient[0].lastName}`,
      },
      message: 'Relación familiar creada exitosamente',
    });

  } catch (error) {
    console.error('Error creating family invitation:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Error al crear invitación familiar'
      },
      { status: 500 }
    );
  }
}