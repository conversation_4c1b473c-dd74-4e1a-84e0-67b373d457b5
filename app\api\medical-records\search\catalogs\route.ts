import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { 
  pathologicalHistory,
  nonPathologicalHistory,
  medications,
  symptoms
} from '@/db/schema';
import { eq, and, ilike, or } from 'drizzle-orm';

// GET - Buscar en catálogos para antecedentes médicos
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const type = searchParams.get('type'); // pathological, non_pathological, medications, symptoms
    const search = searchParams.get('search') || '';
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 50);

    if (!type) {
      return NextResponse.json({ error: 'Tipo de catálogo requerido' }, { status: 400 });
    }

    let results = [];

    switch (type) {
      case 'pathological':
        results = await db
          .select({
            id: pathologicalHistory.id,
            name: pathologicalHistory.name,
            category: pathologicalHistory.category,
            icd11Code: pathologicalHistory.icd11Code,
            severity: pathologicalHistory.severity,
            isHereditary: pathologicalHistory.isHereditary,
            commonInChildren: pathologicalHistory.commonInChildren,
            riskLevel: pathologicalHistory.riskLevel,
          })
          .from(pathologicalHistory)
          .where(and(
            eq(pathologicalHistory.isActive, true),
            search ? ilike(pathologicalHistory.name, `%${search}%`) : undefined
          ))
          .limit(limit);
        break;

      case 'non_pathological':
        results = await db
          .select({
            id: nonPathologicalHistory.id,
            name: nonPathologicalHistory.name,
            category: nonPathologicalHistory.category,
            isPositive: nonPathologicalHistory.isPositive,
            ageRelevant: nonPathologicalHistory.ageRelevant,
          })
          .from(nonPathologicalHistory)
          .where(and(
            eq(nonPathologicalHistory.isActive, true),
            search ? ilike(nonPathologicalHistory.name, `%${search}%`) : undefined
          ))
          .limit(limit);
        break;

      case 'medications':
        results = await db
          .select({
            id: medications.id,
            name: medications.name,
            genericName: medications.genericName,
            brandName: medications.brandName,
            activeIngredient: medications.activeIngredient,
            dosageForm: medications.dosageForm,
            strength: medications.strength,
            therapeuticClass: medications.therapeuticClass,
            indication: medications.indication,
            contraindications: medications.contraindications,
            sideEffects: medications.sideEffects,
            requiresPrescription: medications.requiresPrescription,
            isControlled: medications.isControlled,
          })
          .from(medications)
          .where(and(
            eq(medications.isActive, true),
            search ? or(
              ilike(medications.name, `%${search}%`),
              ilike(medications.genericName, `%${search}%`),
              ilike(medications.activeIngredient, `%${search}%`)
            ) : undefined
          ))
          .limit(limit);
        break;

      case 'symptoms':
        results = await db
          .select({
            id: symptoms.id,
            name: symptoms.name,
            category: symptoms.category,
            icdCode: symptoms.icdCode,
            isSymptom: symptoms.isSymptom,
            commonCauses: symptoms.commonCauses,
          })
          .from(symptoms)
          .where(and(
            eq(symptoms.isActive, true),
            search ? ilike(symptoms.name, `%${search}%`) : undefined
          ))
          .limit(limit);
        break;

      default:
        return NextResponse.json({ error: 'Tipo de catálogo no válido' }, { status: 400 });
    }

    return NextResponse.json({
      data: results,
      pagination: {
        limit,
        total: results.length,
        hasMore: results.length === limit
      }
    });
  } catch (error) {
    console.error('Error searching catalogs:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}

// POST - Buscar múltiples catálogos simultáneamente
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { searches } = body; // Array de {type, search, limit}

    if (!Array.isArray(searches)) {
      return NextResponse.json({ error: 'Se requiere un array de búsquedas' }, { status: 400 });
    }

    const results: Record<string, any[]> = {};

    for (const searchConfig of searches) {
      const { type, search = '', limit = 20 } = searchConfig;
      const searchLimit = Math.min(limit, 50);

      try {
        switch (type) {
          case 'pathological':
            results[type] = await db
              .select({
                id: pathologicalHistory.id,
                name: pathologicalHistory.name,
                category: pathologicalHistory.category,
                icd11Code: pathologicalHistory.icd11Code,
                severity: pathologicalHistory.severity,
                isHereditary: pathologicalHistory.isHereditary,
                commonInChildren: pathologicalHistory.commonInChildren,
                riskLevel: pathologicalHistory.riskLevel,
              })
              .from(pathologicalHistory)
              .where(and(
                eq(pathologicalHistory.isActive, true),
                search ? ilike(pathologicalHistory.name, `%${search}%`) : undefined
              ))
              .limit(searchLimit);
            break;

          case 'non_pathological':
            results[type] = await db
              .select({
                id: nonPathologicalHistory.id,
                name: nonPathologicalHistory.name,
                category: nonPathologicalHistory.category,
                isPositive: nonPathologicalHistory.isPositive,
                ageRelevant: nonPathologicalHistory.ageRelevant,
              })
              .from(nonPathologicalHistory)
              .where(and(
                eq(nonPathologicalHistory.isActive, true),
                search ? ilike(nonPathologicalHistory.name, `%${search}%`) : undefined
              ))
              .limit(searchLimit);
            break;

          case 'medications':
            results[type] = await db
              .select({
                id: medications.id,
                name: medications.name,
                genericName: medications.genericName,
                brandName: medications.brandName,
                activeIngredient: medications.activeIngredient,
                dosageForm: medications.dosageForm,
                strength: medications.strength,
                therapeuticClass: medications.therapeuticClass,
                indication: medications.indication,
                contraindications: medications.contraindications,
                sideEffects: medications.sideEffects,
                requiresPrescription: medications.requiresPrescription,
                isControlled: medications.isControlled,
              })
              .from(medications)
              .where(and(
                eq(medications.isActive, true),
                search ? or(
                  ilike(medications.name, `%${search}%`),
                  ilike(medications.genericName, `%${search}%`),
                  ilike(medications.activeIngredient, `%${search}%`)
                ) : undefined
              ))
              .limit(searchLimit);
            break;

          case 'symptoms':
            results[type] = await db
              .select({
                id: symptoms.id,
                name: symptoms.name,
                category: symptoms.category,
                icdCode: symptoms.icdCode,
                isSymptom: symptoms.isSymptom,
                commonCauses: symptoms.commonCauses,
              })
              .from(symptoms)
              .where(and(
                eq(symptoms.isActive, true),
                search ? ilike(symptoms.name, `%${search}%`) : undefined
              ))
              .limit(searchLimit);
            break;

          default:
            results[type] = [];
        }
      } catch (error) {
        console.error(`Error searching ${type}:`, error);
        results[type] = [];
      }
    }

    return NextResponse.json({
      data: results,
    });
  } catch (error) {
    console.error('Error in batch catalog search:', error);
    return NextResponse.json({ error: 'Error interno del servidor' }, { status: 500 });
  }
}