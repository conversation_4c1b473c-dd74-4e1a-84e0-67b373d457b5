'use client';

import { useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Eye, Edit, Trash2, Check, X, Clock, UserCheck, DollarSign, FileText, AlertCircle, RotateCcw, ClipboardList, Stethoscope } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';

interface AppointmentActionsMenuProps {
  appointment: any;
  onView?: (appointment: any) => void;
  onEdit?: (appointment: any) => void;
  onConfirm?: (appointment: any) => void;
  onCancel?: (appointment: any) => void;
  onDelete?: (appointment: any) => void;
  onCheckIn?: (appointment: any) => void;
  onNoShow?: (appointment: any) => void;
  onStart?: (appointment: any) => void;
  onComplete?: (appointment: any) => void;
  onPayment?: (appointment: any) => void;
  onRevertNoShow?: (appointment: any) => void;
  onRevertCompleted?: (appointment: any) => void;
  onViewPreCheckin?: (appointment: any) => void;
  onViewConsultation?: (appointment: any) => void;
  className?: string;
  iconSize?: 'sm' | 'md' | 'lg';
  userRole?: 'doctor' | 'assistant' | 'admin';
}

const statusConfig = {
  scheduled: {
    color: 'bg-blue-100 text-blue-800',
    label: 'Programada'
  },
  pending_confirmation: {
    color: 'bg-orange-100 text-orange-800',
    label: 'Pendiente de Confirmación'
  },
  confirmed: {
    color: 'bg-[#50bed2]/10 text-[#50bed2]',
    label: 'Confirmada'
  },
  checked_in: {
    color: 'bg-teal-100 text-teal-800',
    label: 'Paciente llegó'
  },
  in_progress: {
    color: 'bg-purple-100 text-purple-800',
    label: 'En consulta'
  },
  completed: {
    color: 'bg-gray-100 text-gray-800',
    label: 'Completada'
  },
  cancelled: {
    color: 'bg-red-100 text-red-800',
    label: 'Cancelada'
  },
  no_show: {
    color: 'bg-orange-100 text-orange-800',
    label: 'No se presentó'
  }
};

export function AppointmentActionsMenu({
  appointment,
  onView,
  onEdit,
  onConfirm,
  onCancel,
  onDelete,
  onCheckIn,
  onNoShow,
  onStart,
  onComplete,
  onPayment,
  onRevertNoShow,
  onRevertCompleted,
  onViewPreCheckin,
  onViewConsultation,
  className,
  iconSize = 'sm',
  userRole = 'assistant'
}: AppointmentActionsMenuProps) {
  const [open, setOpen] = useState(false);
  
  const status = appointment.status;
  const config = statusConfig[status as keyof typeof statusConfig];
  
  const iconSizeClasses = {
    sm: 'h-3 w-3',
    md: 'h-4 w-4',
    lg: 'h-5 w-5'
  };
  
  const iconClass = iconSizeClasses[iconSize];

  // Determinar qué acciones están disponibles según el estado y rol
  const getAvailableActions = () => {
    const actions = [];
    
    // Siempre disponible para todos
    actions.push({
      key: 'view',
      label: 'Ver detalles',
      icon: Eye,
      onClick: onView,
      color: 'text-gray-600'
    });
    
    // Acciones según estado y rol
    switch (status) {
      case 'scheduled':
        // Asistente puede confirmar cita
        if (onConfirm && ['assistant', 'admin'].includes(userRole)) {
          actions.push({
            key: 'confirm',
            label: 'Confirmar cita',
            icon: Check,
            onClick: onConfirm,
            color: 'text-green-600'
          });
        }
        // Asistente y Doctor pueden editar
        if (onEdit && ['assistant', 'doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'edit',
            label: 'Editar',
            icon: Edit,
            onClick: onEdit,
            color: 'text-blue-600'
          });
        }
        // Todos pueden cancelar
        if (onCancel) {
          actions.push({
            key: 'cancel',
            label: 'Cancelar',
            icon: X,
            onClick: onCancel,
            color: 'text-red-600'
          });
        }
        break;
      
      case 'pending_confirmation':
        // Asistente puede confirmar cita (transición a confirmed)
        if (onConfirm && ['assistant', 'admin'].includes(userRole)) {
          actions.push({
            key: 'confirm',
            label: 'Confirmar cita',
            icon: Check,
            onClick: onConfirm,
            color: 'text-green-600'
          });
        }
        // Asistente y Doctor pueden editar
        if (onEdit && ['assistant', 'doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'edit',
            label: 'Editar',
            icon: Edit,
            onClick: onEdit,
            color: 'text-blue-600'
          });
        }
        // Todos pueden cancelar
        if (onCancel) {
          actions.push({
            key: 'cancel',
            label: 'Cancelar',
            icon: X,
            onClick: onCancel,
            color: 'text-red-600'
          });
        }
        break;
        
      case 'confirmed':
        // Solo doctor puede iniciar consulta directamente
        if (onStart && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'start',
            label: 'Iniciar consulta',
            icon: Clock,
            onClick: onStart,
            color: 'text-purple-600'
          });
        }
        // Asistente y Doctor pueden editar
        if (onEdit && ['assistant', 'doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'edit',
            label: 'Editar',
            icon: Edit,
            onClick: onEdit,
            color: 'text-blue-600'
          });
        }
        // Asistente, doctor y admin pueden registrar llegada (solo el mismo día)
        if (onCheckIn && ['assistant', 'doctor', 'admin'].includes(userRole)) {
          // Verificar que sea el mismo día de la cita
          const today = new Date();
          const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD
          const appointmentDateStr = appointment.scheduledDate?.split('T')[0]; // YYYY-MM-DD
          const isSameDay = appointmentDateStr === todayStr;
          
          if (isSameDay && !appointment.checkedInAt) {
            actions.push({
              key: 'checkin',
              label: 'Registrar llegada',
              icon: UserCheck,
              onClick: onCheckIn,
              color: 'text-teal-600'
            });
          }
        }
        // Asistente puede marcar no show
        if (onNoShow && ['assistant', 'admin'].includes(userRole)) {
          actions.push({
            key: 'no_show',
            label: 'Marcar no show',
            icon: AlertCircle,
            onClick: onNoShow,
            color: 'text-orange-600'
          });
        }
        
        // Pre-checkin status - visible para doctor y asistente
        if (onViewPreCheckin && ['doctor', 'assistant', 'admin'].includes(userRole)) {
          if (appointment.preCheckinCompleted) {
            actions.push({
              key: 'view-precheckin',
              label: 'Ver Pre-checkin ✅',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-green-600'
            });
          } else if (appointment.preCheckinSent) {
            actions.push({
              key: 'precheckin-pending',
              label: 'Pre-checkin pendiente ⏳',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-orange-600'
            });
          } else {
            actions.push({
              key: 'precheckin-not-sent',
              label: 'Pre-checkin no enviado ❌',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-gray-600'
            });
          }
        }
        
        // Todos pueden cancelar
        if (onCancel) {
          actions.push({
            key: 'cancel',
            label: 'Cancelar',
            icon: X,
            onClick: onCancel,
            color: 'text-red-600'
          });
        }
        break;
        
      case 'checked_in':
        // Solo doctor puede iniciar consulta
        if (onStart && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'start',
            label: 'Iniciar consulta',
            icon: Clock,
            onClick: onStart,
            color: 'text-purple-600'
          });
        }
        // Asistente puede marcar no show
        if (onNoShow && ['assistant', 'admin'].includes(userRole)) {
          actions.push({
            key: 'no_show',
            label: 'Marcar no show',
            icon: AlertCircle,
            onClick: onNoShow,
            color: 'text-orange-600'
          });
        }
        
        // Pre-checkin status - visible para doctor y asistente
        if (onViewPreCheckin && ['doctor', 'assistant', 'admin'].includes(userRole)) {
          if (appointment.preCheckinCompleted) {
            actions.push({
              key: 'view-precheckin',
              label: 'Ver Pre-checkin ✅',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-green-600'
            });
          } else if (appointment.preCheckinSent) {
            actions.push({
              key: 'precheckin-pending',
              label: 'Pre-checkin pendiente ⏳',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-orange-600'
            });
          } else {
            actions.push({
              key: 'precheckin-not-sent',
              label: 'Pre-checkin no enviado ❌',
              icon: ClipboardList,
              onClick: onViewPreCheckin,
              color: 'text-gray-600'
            });
          }
        }
        
        // Todos pueden cancelar
        if (onCancel) {
          actions.push({
            key: 'cancel',
            label: 'Cancelar',
            icon: X,
            onClick: onCancel,
            color: 'text-red-600'
          });
        }
        break;
        
      case 'in_progress':
        // Doctor puede ver/editar la consulta en curso
        if (onViewConsultation && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'view_consultation',
            label: 'Ver/Editar Consulta',
            icon: Stethoscope,
            onClick: onViewConsultation,
            color: 'text-purple-600'
          });
        }
        // Solo doctor puede completar consulta
        if (onComplete && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'complete',
            label: 'Completar consulta',
            icon: Check,
            onClick: onComplete,
            color: 'text-green-600'
          });
        }
        break;
        
      case 'completed':
        // Doctor puede crear/ver expediente
        if (onComplete && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'create_record',
            label: 'Crear/Ver expediente',
            icon: FileText,
            onClick: onComplete,
            color: 'text-blue-600'
          });
        }
        // Asistente y Doctor pueden gestionar pago
        if (onPayment) {
          actions.push({
            key: 'payment',
            label: 'Gestionar pago',
            icon: DollarSign,
            onClick: onPayment,
            color: 'text-green-600'
          });
        }
        // Permitir revertir estado completado (solo doctor y admin)
        if (onRevertCompleted && ['doctor', 'admin'].includes(userRole)) {
          actions.push({
            key: 'revert_completed',
            label: 'Revertir a En Progreso',
            icon: RotateCcw,
            onClick: onRevertCompleted,
            color: 'text-orange-600'
          });
        }
        break;
        
      case 'cancelled':
        // Solo ver detalles para citas canceladas
        break;
        
      case 'no_show':
        // Permitir revertir no-show a confirmed
        if (onRevertNoShow && ['assistant', 'admin'].includes(userRole)) {
          actions.push({
            key: 'revert_no_show',
            label: 'Revertir a Confirmada',
            icon: Check,
            onClick: onRevertNoShow,
            color: 'text-green-600'
          });
        }
        break;
    }
    
    // Eliminar solo disponible para estados sin actividad médica/contable
    // PERMITIDO: scheduled, pending_confirmation, cancelled, no_show
    // PROHIBIDO: confirmed, checked_in, in_progress, completed (tienen actividad médica/histórica)
    const canDelete = [
      'scheduled',           // Solo programada, sin actividad
      'pending_confirmation', // Pendiente, sin confirmación
      'cancelled',           // Ya cancelada, se puede limpiar
      'no_show'             // Paciente no vino, se puede limpiar
    ].includes(status);
    
    // Permisos por estado:
    // - scheduled, pending_confirmation: assistant, admin, doctor (sin actividad médica)
    // - cancelled, no_show: assistant, admin (limpieza administrativa)
    const deletePermissions = {
      scheduled: ['assistant', 'admin', 'doctor'],
      pending_confirmation: ['assistant', 'admin', 'doctor'], 
      cancelled: ['assistant', 'admin', 'doctor'],
      no_show: ['assistant', 'admin']
    };
    
    const canUserDelete = deletePermissions[status]?.includes(userRole) || false;
    
    if (onDelete && canDelete && canUserDelete) {
      actions.push({
        key: 'delete',
        label: 'Eliminar',
        icon: Trash2,
        onClick: onDelete,
        color: 'text-red-600',
        separator: true
      });
    }
    
    return actions;
  };

  const actions = getAvailableActions();

  const handleAction = (action: any, e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setOpen(false);
    action.onClick?.(appointment);
  };

  const handleMenuClick = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className={cn(
            "h-7 w-7 p-0 hover:bg-[#50bed2]/10 hover:border-[#50bed2]/30 hover:text-[#50bed2] border border-gray-200 bg-white/90 opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-sm",
            className
          )}
          onClick={handleMenuClick}
        >
          <MoreHorizontal className={cn(iconClass, "text-gray-600 group-hover:text-[#50bed2] transition-colors")} />
          <span className="sr-only">Abrir menú</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-44">
        {actions.map((action, index) => (
          <div key={action.key}>
            {action.separator && index > 0 && <DropdownMenuSeparator />}
            <DropdownMenuItem 
              onClick={(e) => handleAction(action, e)}
              className="cursor-pointer"
            >
              <action.icon className={cn(iconClass, "mr-2", action.color)} />
              <span>{action.label}</span>
            </DropdownMenuItem>
          </div>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}