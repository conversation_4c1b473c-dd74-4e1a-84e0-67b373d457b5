'use client';

import Link from "next/link";
import { Heart, MapPin, Phone, Mail, Calendar, Baby, BookOpen, Stethoscope, Shield } from "lucide-react";
import { useConsultoryInfo } from "@/hooks/use-consultory-info";
import Image from "next/image";

const quickLinks = [
  { title: "Agendar Cita", href: "/dashboard", icon: Calendar },
  { title: "Portal Pacientes", href: "/sign-in", icon: Baby },
  { title: "Blog de Salud", href: "/blog", icon: BookOpen },
  { title: "Emergencias", href: "tel:+50256789012", icon: Phone },
];

export default function FooterSection() {
  const { consultory, isLoading } = useConsultoryInfo();

  return (
    <footer className="bg-gradient-to-br from-[#ADB6CA]/10 via-[#FCEEA8]/20 to-[#F8E59A]/30 border-t border-[#ADB6CA]/30 relative overflow-hidden">
      <div className="absolute top-0 left-0 w-64 h-64 bg-[#ADB6CA]/20 rounded-full blur-3xl -ml-32 -mt-32"></div>
      <div className="absolute bottom-0 right-0 w-48 h-48 bg-[#FCEEA8]/30 rounded-full blur-3xl -mr-24 -mb-24"></div>
      <div className="mx-auto max-w-7xl px-6 py-8 relative z-10">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-6">
          
          {/* Brand Section */}
          <div className="lg:col-span-1">
            <Link href="/" className="flex items-center space-x-2 mb-3 group">
              {consultory?.logoUrl && !isLoading ? (
                <div className="w-20 h-20 rounded-xl overflow-hidden shadow-lg group-hover:shadow-xl transform group-hover:scale-110 transition-all">
                  <Image
                    src={consultory.logoUrl}
                    alt={`${consultory.name} logo`}
                    width={80}
                    height={80}
                    className="w-full h-full object-cover"
                  />
                </div>
              ) : (
                <div className="w-20 h-20 bg-gradient-to-br from-[#ea6cb0] to-[#ea6cb0]/80 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transform group-hover:scale-110 transition-all">
                  <Baby className="h-10 w-10 text-[#FCEEA8]" />
                </div>
              )}
              <span className="text-xl font-bold text-[#ea6cb0]">
                {isLoading ? 'Cargando...' : consultory?.name || 'Mundo Pediatra'}
              </span>
            </Link>
            <p className="text-[#3D4E80]/80 text-sm leading-relaxed mb-4">
              Tu clínica pediátrica de confianza con tecnología y diversión.
            </p>
            
            {/* Contact Info */}
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm text-[#3D4E80]/80 hover:text-[#3D4E80] transition-colors cursor-pointer">
                <MapPin className="h-4 w-4 text-[#3D4E80]" />
                <span>{isLoading ? 'Cargando...' : consultory?.address || 'Av. Principal 123, Zona 10'}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-[#3D4E80]/80 hover:text-[#3D4E80] transition-colors cursor-pointer">
                <Phone className="h-4 w-4 text-[#3D4E80]" />
                <span>{isLoading ? 'Cargando...' : consultory?.phone || '+502 2234-5678'}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-[#3D4E80]/80 hover:text-[#3D4E80] transition-colors cursor-pointer">
                <Mail className="h-4 w-4 text-[#3D4E80]" />
                <span>{isLoading ? 'Cargando...' : consultory?.email || '<EMAIL>'}</span>
              </div>
            </div>
          </div>

          {/* Quick Access Cards */}
          <div className="lg:col-span-2">
            <h3 className="text-lg font-bold mb-4 text-[#3D4E80] flex items-center gap-2">
              <Stethoscope className="h-5 w-5 text-[#3D4E80]" />
              Acceso Rápido
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {quickLinks.map((link) => (
                <Link
                  key={link.title}
                  href={link.href}
                  className="group p-4 bg-white/60 hover:bg-white/80 rounded-xl border border-[#ADB6CA]/30 hover:border-[#ADB6CA]/50 transition-all duration-300 hover:shadow-md transform hover:scale-105"
                >
                  <div className="flex flex-col items-center text-center space-y-2">
                    <div className="w-10 h-10 bg-gradient-to-br from-[#FCEEA8]/30 to-[#F8E59A]/30 rounded-lg flex items-center justify-center group-hover:from-[#FCEEA8]/50 group-hover:to-[#F8E59A]/50 transition-all">
                      <link.icon className="h-5 w-5 text-[#3D4E80]" />
                    </div>
                    <span className="text-xs font-medium text-[#3D4E80]/80 group-hover:text-[#3D4E80] transition-colors">
                      {link.title}
                    </span>
                  </div>
                </Link>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-[#ADB6CA]/30 pt-4">
          <div className="flex flex-col md:flex-row items-center justify-between gap-3">
            <div className="flex items-center gap-2 text-xs text-[#3D4E80]/80">
              <Shield className="h-3 w-3 text-[#3D4E80]" />
              <span>© {new Date().getFullYear()} <span className="text-[#ea6cb0] font-semibold">{isLoading ? 'Cargando...' : consultory?.name || 'Mundo Pediatra'}</span>. Todos los derechos reservados.</span>
            </div>

            <div className="flex items-center gap-4 text-xs">
              <Link href="/privacy-policy" className="text-[#3D4E80]/80 hover:text-[#3D4E80] transition-colors">
                Privacidad
              </Link>
              <Link href="/terms-of-service" className="text-[#3D4E80]/80 hover:text-[#3D4E80] transition-colors">
                Términos
              </Link>
              <div className="flex items-center gap-1 text-[#3D4E80]/80">
                <span>Hecho con</span>
                <Heart className="h-3 w-3 text-[#FAC9D1] fill-current" />
                <span>para pequeños héroes</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
