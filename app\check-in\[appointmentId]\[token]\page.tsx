'use client';

import { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { CheckCircle, Clock, Calendar, User, Loader2, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';
import { formatDate } from '@/lib/utils';

interface AppointmentData {
  id: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  status: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  serviceName: string;
  consultoryName: string;
  estimatedPrice: string;
  currency: string;
}

export default function CheckInPage() {
  const params = useParams();
  const router = useRouter();
  const [appointment, setAppointment] = useState<AppointmentData | null>(null);
  const [loading, setLoading] = useState(true);
  const [checkingIn, setCheckingIn] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const appointmentId = params.appointmentId as string;
  const token = params.token as string;

  useEffect(() => {
    const fetchAppointment = async () => {
      try {
        const response = await fetch(`/api/check-in/${appointmentId}/${token}`);
        
        if (!response.ok) {
          if (response.status === 404) {
            setError('Cita no encontrada o enlace expirado');
          } else if (response.status === 400) {
            setError('Esta cita ya fue procesada');
          } else {
            setError('Error al cargar la información de la cita');
          }
          return;
        }

        const data = await response.json();
        setAppointment(data.appointment);
      } catch (error) {
        console.error('Error fetching appointment:', error);
        setError('Error de conexión. Por favor intente nuevamente.');
      } finally {
        setLoading(false);
      }
    };

    if (appointmentId && token) {
      fetchAppointment();
    }
  }, [appointmentId, token]);

  const handleCheckIn = async () => {
    setCheckingIn(true);
    try {
      const response = await fetch(`/api/check-in/${appointmentId}/${token}`, {
        method: 'POST',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al realizar check-in');
      }

      setSuccess(true);
      toast.success('¡Check-in realizado exitosamente!');
      
      // Redirect after 3 seconds
      setTimeout(() => {
        router.push('/');
      }, 3000);

    } catch (error: any) {
      console.error('Error during check-in:', error);
      toast.error(error.message || 'Error al realizar check-in');
    } finally {
      setCheckingIn(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="flex items-center justify-center space-x-2">
              <Loader2 className="h-6 w-6 animate-spin text-blue-600" />
              <span className="text-gray-600">Cargando información...</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <AlertCircle className="h-12 w-12 text-red-500 mx-auto" />
              <h2 className="text-xl font-semibold text-gray-900">Error</h2>
              <p className="text-gray-600">{error}</p>
              <Button 
                onClick={() => router.push('/')}
                variant="outline"
                className="w-full"
              >
                Volver al inicio
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6">
            <div className="text-center space-y-4">
              <CheckCircle className="h-16 w-16 text-green-500 mx-auto" />
              <h2 className="text-xl font-semibold text-gray-900">¡Check-in Exitoso!</h2>
              <p className="text-gray-600">
                Su llegada ha sido registrada. El personal médico ha sido notificado.
              </p>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-sm text-green-800">
                  Por favor tome asiento en la sala de espera. 
                  Será llamado cuando el doctor esté listo para atenderle.
                </p>
              </div>
              <p className="text-xs text-gray-500">
                Será redirigido automáticamente en unos segundos...
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  if (!appointment) {
    return null;
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      confirmed: { label: 'Confirmada', color: 'bg-green-100 text-green-800' },
      checked_in: { label: 'Ya registrado', color: 'bg-teal-100 text-teal-800' },
    };
    
    const config = statusConfig[status as keyof typeof statusConfig] || 
                   { label: status, color: 'bg-gray-100 text-gray-800' };
    
    return (
      <Badge className={config.color}>
        {config.label}
      </Badge>
    );
  };

  const isAlreadyCheckedIn = appointment.status === 'checked_in';
  const canCheckIn = appointment.status === 'confirmed';

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold text-gray-900">
            Check-in de Cita
          </CardTitle>
          <p className="text-gray-600">
            Confirme su llegada para la cita médica
          </p>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Información de la cita */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 space-y-3">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Información de la Cita</h3>
              {getStatusBadge(appointment.status)}
            </div>
            
            <div className="grid grid-cols-1 gap-3 text-sm">
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Paciente:</span>
                <span>{appointment.patientFirstName} {appointment.patientLastName}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Fecha:</span>
                <span>{formatDate(appointment.scheduledDate)}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Hora:</span>
                <span>
                  {format(new Date(appointment.startTime), 'HH:mm')} - 
                  {format(new Date(appointment.endTime), 'HH:mm')}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="font-medium">Doctor:</span>
                <span>Dr. {appointment.doctorFirstName} {appointment.doctorLastName}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Servicio:</span>
                <span>{appointment.serviceName}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="font-medium">Consultorio:</span>
                <span>{appointment.consultoryName}</span>
              </div>
            </div>
          </div>

          {/* Botones de acción */}
          <div className="space-y-3">
            {isAlreadyCheckedIn && (
              <div className="bg-teal-50 border border-teal-200 rounded-lg p-4 text-center">
                <CheckCircle className="h-8 w-8 text-teal-600 mx-auto mb-2" />
                <p className="text-teal-800 font-medium">
                  Su llegada ya fue registrada
                </p>
                <p className="text-sm text-teal-600 mt-1">
                  Por favor espere en la sala de espera
                </p>
              </div>
            )}

            {canCheckIn && (
              <Button 
                onClick={handleCheckIn}
                disabled={checkingIn}
                className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                size="lg"
              >
                {checkingIn ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Registrando llegada...
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Confirmar llegada
                  </>
                )}
              </Button>
            )}

            {!canCheckIn && !isAlreadyCheckedIn && (
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 text-center">
                <AlertCircle className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
                <p className="text-yellow-800 font-medium">
                  No es posible realizar check-in
                </p>
                <p className="text-sm text-yellow-600 mt-1">
                  Estado actual: {appointment.status}
                </p>
              </div>
            )}
          </div>

          {/* Información adicional */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">Instrucciones:</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Llegue 15 minutos antes de su cita</li>
              <li>• Confirme su llegada usando este botón</li>
              <li>• Espere en la sala de espera hasta ser llamado</li>
              <li>• Traiga su documento de identidad</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}