'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { DateInput } from '@/components/ui/date-input';;
import { 
  ArrowLeft,
  User,
  Mail,
  Phone,
  MapPin,
  Heart,
  Shield,
  Edit,
  UserCheck,
  UserX,
  Trash2,
  AlertCircle,
  Calendar,
  Save
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { formatDate } from '@/lib/utils';

interface UserRole {
  id: string;
  role: string;
  status: string;
  consultoryId?: string;
  specialtyId?: number;
  medicalLicense?: string;
  roleData?: any;
  createdAt: string;
  updatedAt: string;
}

interface UserData {
  id: string;
  email: string;
  emailVerified: boolean;
  name?: string;
  image?: string;
  firstName?: string;
  lastName?: string;
  documentType?: string;
  documentNumber?: string;
  dateOfBirth?: string;
  gender?: string;
  phone?: string;
  alternativePhone?: string;
  address?: string;
  countryId?: string;
  departmentId?: string;
  municipalityId?: string;
  occupationId?: string;
  emergencyContact?: string;
  emergencyPhone?: string;
  emergencyRelationshipId?: string;
  overallStatus: string;
  createdAt: string;
  updatedAt: string;
  roles: UserRole[];
}

const roleLabels: { [key: string]: string } = {
  admin: 'Administrador',
  doctor: 'Doctor',
  assistant: 'Asistente',
  patient: 'Paciente',
  guardian: 'Guardian',
  provider: 'Proveedor'
};

const statusColors: { [key: string]: string } = {
  active: 'bg-emerald-100 text-emerald-800',
  inactive: 'bg-gray-100 text-gray-800',
  pending: 'bg-yellow-100 text-yellow-800',
  suspended: 'bg-red-100 text-red-800',
  deleted: 'bg-red-100 text-red-800'
};

const statusLabels: { [key: string]: string } = {
  active: 'Activo',
  inactive: 'Inactivo',
  pending: 'Pendiente',
  suspended: 'Suspendido',
  deleted: 'Eliminado'
};

// Helper para obtener nombre del consultorio
const getConsultoryName = (consultoryId: string | undefined, consultories: any[]) => {
  if (!consultoryId) return null;
  const consultory = consultories.find(c => c.id === consultoryId);
  return consultory?.name || `Consultorio ${consultoryId}`;
};

export default function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const searchParams = useSearchParams();
  const resolvedParams = use(params);
  
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState('');
  
  // Estados para el diálogo de eliminación
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [deleteAnalysis, setDeleteAnalysis] = useState<any>(null);
  const [deletingUser, setDeletingUser] = useState(false);
  
  // Estados para edición de consultorio
  const [showEditConsultoryDialog, setShowEditConsultoryDialog] = useState(false);
  const [editingRoleId, setEditingRoleId] = useState<string | null>(null);
  const [selectedConsultoryId, setSelectedConsultoryId] = useState('');
  
  // Estados para modo de visualización
  const [isEditMode, setIsEditMode] = useState(searchParams.get('mode') === 'edit');
  const [activeTab, setActiveTab] = useState('general');
  
  // Estados para catálogos
  const [documentTypes, setDocumentTypes] = useState<any[]>([]);
  const [relationships, setRelationships] = useState<any[]>([]);
  const [consultories, setConsultories] = useState<any[]>([]);
  const [genderOptions] = useState([
    { id: 'masculino', name: 'Masculino' },
    { id: 'femenino', name: 'Femenino' },
    { id: 'otro', name: 'Otro' }
  ]);
  
  // Estados para el formulario de edición
  const [editFormData, setEditFormData] = useState({
    firstName: '',
    lastName: '',
    documentType: '',
    documentNumber: '',
    dateOfBirth: '',
    gender: '',
    phone: '',
    alternativePhone: '',
    address: '',
    emergencyContact: '',
    emergencyPhone: '',
    emergencyRelationshipId: '',
  });

  const fetchUser = async () => {
    try {
      setError('');
      const response = await fetch(`/api/admin/users/${resolvedParams.id}`);
      
      if (!response.ok) {
        throw new Error('Error al cargar usuario');
      }

      const result = await response.json();
      if (result.success) {
        setUser(result.data);
        // Inicializar datos del formulario de edición
        setEditFormData({
          firstName: result.data.firstName || '',
          lastName: result.data.lastName || '',
          documentType: result.data.documentType || '',
          documentNumber: result.data.documentNumber || '',
          dateOfBirth: result.data.dateOfBirth || '',
          gender: result.data.gender || '',
          phone: result.data.phone || '',
          alternativePhone: result.data.alternativePhone || '',
          address: result.data.address || '',
          emergencyContact: result.data.emergencyContact || '',
          emergencyPhone: result.data.emergencyPhone || '',
          emergencyRelationshipId: result.data.emergencyRelationshipId || '',
        });
      } else {
        throw new Error(result.error || 'Error desconocido');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error al cargar usuario');
    } finally {
      setLoading(false);
    }
  };

  const fetchCatalogs = async () => {
    try {
      // Cargar tipos de documento
      const documentTypesResponse = await fetch('/api/catalogs/document-types');
      if (documentTypesResponse.ok) {
        const documentTypesData = await documentTypesResponse.json();
        setDocumentTypes(documentTypesData.data || []);
      }

      // Cargar relaciones
      const relationshipsResponse = await fetch('/api/catalogs/relationships');
      if (relationshipsResponse.ok) {
        const relationshipsData = await relationshipsResponse.json();
        setRelationships(relationshipsData.data || []);
      }
      
      // Cargar consultorios
      const consultoriesResponse = await fetch('/api/catalogs/consultories');
      if (consultoriesResponse.ok) {
        const consultoriesData = await consultoriesResponse.json();
        setConsultories(consultoriesData.data || []);
      }
    } catch (error) {
      console.error('Error cargando catálogos:', error);
    }
  };

  useEffect(() => {
    fetchUser();
    fetchCatalogs();
  }, [resolvedParams.id]);

  const handleToggleStatus = async () => {
    if (!user) return;
    
    setProcessing(true);
    try {
      const newStatus = user.overallStatus === 'active' ? 'inactive' : 'active';
      const response = await fetch(`/api/admin/users/${user.id}/toggle-status`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ newStatus }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success(result.data.message);
        fetchUser();
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al actualizar estado');
    } finally {
      setProcessing(false);
    }
  };

  const handleDelete = async () => {
    if (!user) return;
    
    try {
      const analysisResponse = await fetch(`/api/admin/users/${user.id}/analyze-deletion`, {
        method: 'POST',
      });
      
      const analysisResult = await analysisResponse.json();
      
      if (analysisResult.success) {
        setDeleteAnalysis(analysisResult);
        setShowDeleteDialog(true);
      } else {
        throw new Error(analysisResult.error);
      }
    } catch (error) {
      toast.error('Error al analizar impacto de eliminación: ' + (error instanceof Error ? error.message : 'Error desconocido'));
    }
  };

  const executePhysicalDeletion = async () => {
    if (!user) return;
    
    setDeletingUser(true);
    try {
      const response = await fetch(
        `/api/admin/users/${user.id}?deletePhysically=true&deleteFromClerk=true`, 
        {
          method: 'DELETE',
        }
      );

      const result = await response.json();
      if (result.success) {
        toast.success('Usuario eliminado físicamente exitosamente');
        setShowDeleteDialog(false);
        router.push('/dashboard/admin/users');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al eliminar usuario');
    } finally {
      setDeletingUser(false);
    }
  };

  const executeLogicalDeletion = async () => {
    if (!user) return;
    
    setDeletingUser(true);
    try {
      const response = await fetch(
        `/api/admin/users/${user.id}?deletePhysically=false&deleteFromClerk=false`, 
        {
          method: 'DELETE',
        }
      );

      const result = await response.json();
      if (result.success) {
        toast.success('Usuario eliminado lógicamente exitosamente');
        setShowDeleteDialog(false);
        router.push('/dashboard/admin/users');
      } else {
        throw new Error(result.error);
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al eliminar usuario');
    } finally {
      setDeletingUser(false);
    }
  };

  const handleSave = async () => {
    if (!user) return;
    
    setProcessing(true);
    try {
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editFormData),
      });

      const result = await response.json();
      if (result.success) {
        toast.success('Usuario actualizado exitosamente');
        setIsEditMode(false);
        fetchUser(); // Recargar datos actualizados
      } else {
        throw new Error(result.error || 'Error al actualizar usuario');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al actualizar usuario');
    } finally {
      setProcessing(false);
    }
  };

  // Función para abrir modal de edición de consultorio
  const handleEditConsultory = (roleId: string, currentConsultoryId?: string) => {
    setEditingRoleId(roleId);
    setSelectedConsultoryId(currentConsultoryId || 'none');
    setShowEditConsultoryDialog(true);
  };

  // Función para guardar consultorio
  const handleSaveConsultory = async () => {
    if (!editingRoleId) return;
    
    setProcessing(true);
    try {
      const response = await fetch(`/api/admin/users/${user?.id}/update-consultory`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          roleId: editingRoleId,
          consultoryId: selectedConsultoryId === 'none' ? null : selectedConsultoryId
        }),
      });

      const result = await response.json();
      if (result.success) {
        toast.success('Consultorio actualizado exitosamente');
        setShowEditConsultoryDialog(false);
        fetchUser(); // Recargar datos
      } else {
        throw new Error(result.error || 'Error al actualizar consultorio');
      }
    } catch (error) {
      toast.error(error instanceof Error ? error.message : 'Error al actualizar consultorio');
    } finally {
      setProcessing(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (error || !user) {
    return (
      <div className="space-y-8">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardContent className="p-6">
            <div className="text-center">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 text-red-500" />
              <p className="text-gray-900 mb-2">{error || 'No se pudo cargar el usuario'}</p>
              <Button 
                variant="outline" 
                className="mt-4 hover:bg-gray-50"
                onClick={() => router.push('/dashboard/admin/users')}
              >
                Volver al listado
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6 p-6">
        {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:items-start lg:justify-between lg:space-y-0">
        <div className="flex items-center space-x-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/dashboard/admin/users')}
            className="hover:bg-green-100 hover:text-green-600 transition-colors"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              {user.firstName} {user.lastName}
            </h1>
            <p className="text-gray-600 text-sm">
              ID: {user.id}
            </p>
            {/* Badges en móvil */}
            <div className="flex items-center gap-2 mt-2 lg:hidden">
              <Badge className={statusColors[user.overallStatus]}>
                {statusLabels[user.overallStatus] || user.overallStatus}
              </Badge>
              {user.emailVerified && (
                <Badge variant="secondary">Email Verificado</Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {/* Badges en desktop */}
          <div className="hidden lg:flex items-center gap-2">
            <Badge className={statusColors[user.overallStatus]}>
              {statusLabels[user.overallStatus] || user.overallStatus}
            </Badge>
            {user.emailVerified && (
              <Badge variant="secondary">Email Verificado</Badge>
            )}
          </div>
        </div>
      </div>

      

      {/* Tabs */}
      <div className="space-y-4">
        {/* Custom Tab Navigation */}
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="flex flex-col sm:flex-row space-y-1 sm:space-y-0 sm:space-x-1 p-1">
            <button
              onClick={() => setActiveTab('general')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'general'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Información General</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('contact')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'contact'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Contacto</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('roles')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'roles'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Roles y Permisos</div>
              </div>
            </button>
            <button
              onClick={() => setActiveTab('activity')}
              className={`flex-1 px-4 py-3 sm:px-6 sm:py-4 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeTab === 'activity'
                  ? 'bg-blue-500 text-white shadow-md'
                  : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
              }`}
            >
              <div className="text-center">
                <div className="font-semibold">Actividad</div>
              </div>
            </button>
          </div>
        </div>

        {/* Contenido de los tabs */}
        {activeTab === 'general' && (
          <>
            <div className="space-y-4">
              <div className="space-y-4">
              {/* Datos Personales */}
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-3">
                      <Avatar className="h-16 w-16">
                        <AvatarImage src={user.image || undefined} />
                        <AvatarFallback className="bg-gray-100 text-gray-600 text-xl">
                          {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="text-xl font-semibold">{user.firstName} {user.lastName}</div>
                        <div className="text-sm text-gray-500">{user.email}</div>
                        <div className="flex items-center gap-2 mt-2">
                          <Badge className={statusColors[user.overallStatus]}>
                            {statusLabels[user.overallStatus] || user.overallStatus}
                          </Badge>
                          {user.emailVerified && (
                            <Badge variant="secondary">Email Verificado</Badge>
                          )}
                        </div>
                      </div>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {!isEditMode ? (
                      // Modo Ver - Solo lectura
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Tipo de Documento</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.documentType || 'No especificado'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Número de Documento</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.documentNumber || 'No especificado'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                          <p className="text-sm text-gray-900 mt-1">
                            {user.dateOfBirth 
                              ? formatDate(user.dateOfBirth)
                              : 'No especificado'}
                          </p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Género</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.gender || 'No especificado'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Tipo de Sangre</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.bloodType || 'No especificado'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">ID de Usuario</Label>
                          <p className="text-sm text-gray-900 font-mono mt-1">{user.id}</p>
                        </div>
                      </>
                    ) : (
                      // Modo Edición - Formularios
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                          <DateInput
              className="mt-1"
              value={editFormData.dateOfBirth ? new Date(editFormData.dateOfBirth) : undefined}
              onChange={(date) => setEditFormData({...editFormData, dateOfBirth: date ? date.toISOString() : ''})}
            />
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Género</Label>
                          <Select
                            value={editFormData.gender}
                            onValueChange={(value) => setEditFormData({...editFormData, gender: value})}
                          >
                            <SelectTrigger className="mt-1">
                              <SelectValue placeholder="Selecciona género" />
                            </SelectTrigger>
                            <SelectContent>
                              {genderOptions.map((option) => (
                                <SelectItem key={option.id} value={option.id}>
                                  {option.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
                
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Estado de Verificación</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-gray-400" />
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Email</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={user.emailVerified ? "default" : "secondary"}>
                          {user.emailVerified ? "Verificado" : "No verificado"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-gray-400" />
                    <div>
                      <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                      <div className="flex items-center gap-2 mt-1">
                        <Badge variant={user.phoneVerified ? "default" : "secondary"}>
                          {user.phoneVerified ? "Verificado" : "No verificado"}
                        </Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
              </div>
            </div>
          </>
        )}
        
        {activeTab === 'contact' && (
          <>
            <div className="space-y-4">
              <div className="space-y-4">
              {/* Información de Contacto */}
              <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Mail className="h-5 w-5 text-blue-600" />
                    Información de Contacto
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {!isEditMode ? (
                      // Modo Ver - Solo lectura
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Email</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.email}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.phone || 'No registrado'}</p>
                        </div>
                        <div className="md:col-span-2">
                          <Label className="text-sm font-medium text-gray-700">Dirección</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.address || 'No registrado'}</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Teléfono Alternativo</Label>
                          <p className="text-sm text-gray-900 mt-1">{user.alternativePhone || 'No registrado'}</p>
                        </div>
                      </>
                    ) : (
                      // Modo Edición - Formularios
                      <>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Email</Label>
                          <p className="text-sm text-gray-500 mt-1">{user.email} (No editable)</p>
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                          <Input
                            value={editFormData.phone}
                            onChange={(e) => setEditFormData({...editFormData, phone: e.target.value})}
                            placeholder="Teléfono principal"
                            className="mt-1"
                          />
                        </div>
                        <div className="md:col-span-2">
                          <Label className="text-sm font-medium text-gray-700">Dirección</Label>
                          <Textarea
                            value={editFormData.address}
                            onChange={(e) => setEditFormData({...editFormData, address: e.target.value})}
                            placeholder="Dirección completa"
                            className="mt-1"
                            rows={2}
                          />
                        </div>
                        <div>
                          <Label className="text-sm font-medium text-gray-700">Teléfono Alternativo</Label>
                          <Input
                            value={editFormData.alternativePhone}
                            onChange={(e) => setEditFormData({...editFormData, alternativePhone: e.target.value})}
                            placeholder="Teléfono alternativo"
                            className="mt-1"
                          />
                        </div>
                      </>
                    )}
                  </CardContent>
                </Card>
                
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center gap-2">
                    <Heart className="h-5 w-5 text-red-600" />
                    Contacto de Emergencia
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {!isEditMode ? (
                    // Modo Ver - Solo lectura
                    <>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                        <p className="text-sm text-gray-900 mt-1">{user.emergencyContact || 'No registrado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                        <p className="text-sm text-gray-900 mt-1">{user.emergencyPhone || 'No registrado'}</p>
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Relación</Label>
                        <p className="text-sm text-gray-900 mt-1">
                          {user.emergencyRelationshipId ? 'Configurado' : 'No especificado'}
                        </p>
                      </div>
                    </>
                  ) : (
                    // Modo Edición - Formularios
                    <>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                        <Input
                          value={editFormData.emergencyContact}
                          onChange={(e) => setEditFormData({...editFormData, emergencyContact: e.target.value})}
                          placeholder="Nombre del contacto de emergencia"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                        <Input
                          value={editFormData.emergencyPhone}
                          onChange={(e) => setEditFormData({...editFormData, emergencyPhone: e.target.value})}
                          placeholder="Teléfono de emergencia"
                          className="mt-1"
                        />
                      </div>
                      <div>
                        <Label className="text-sm font-medium text-gray-700">Relación</Label>
                        <Select
                          value={editFormData.emergencyRelationshipId}
                          onValueChange={(value) => setEditFormData({...editFormData, emergencyRelationshipId: value})}
                        >
                          <SelectTrigger className="mt-1">
                            <SelectValue placeholder="Selecciona relación" />
                          </SelectTrigger>
                          <SelectContent>
                            {relationships.map((relationship) => (
                              <SelectItem key={relationship.id} value={relationship.id}>
                                {relationship.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    </>
                  )}
                </CardContent>
              </Card>
              </div>
            </div>
          </>
        )}
        
        {activeTab === 'roles' && (
          <>
            <div className="space-y-4">
              <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Shield className="h-5 w-5 text-blue-600" />
                  Roles y Permisos
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {user.roles.map((role) => (
                    <div key={role.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border">
                      <div className="flex items-center gap-4">
                        <Shield className="h-6 w-6 text-gray-500" />
                        <div>
                          <p className="font-medium text-lg">{roleLabels[role.role] || role.role}</p>
                          <p className="text-sm text-gray-500">
                            Creado el {formatDate(role.createdAt)}
                          </p>
                          {role.medicalLicense && (
                            <p className="text-sm text-blue-600">
                              Licencia: {role.medicalLicense}
                            </p>
                          )}
                          {role.consultoryId && (
                            <p className="text-sm text-green-600">
                              Consultorio: {getConsultoryName(role.consultoryId, consultories)}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        {(role.role === 'doctor' || role.role === 'assistant') && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditConsultory(role.id, role.consultoryId)}
                            className="text-xs"
                          >
                            <Edit className="h-3 w-3 mr-1" />
                            {role.consultoryId ? 'Cambiar' : 'Asignar'} Consultorio
                          </Button>
                        )}
                        <Badge className={statusColors[role.status]}>
                          {statusLabels[role.status] || role.status}
                        </Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
            </div>
          </>
        )}
        
        {activeTab === 'activity' && (
          <>
            <div className="space-y-4">
              <Card>
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-blue-600" />
                  Información de Registro
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Fecha de Registro</Label>
                  <p className="text-sm text-gray-900 mt-1">
                    {format(new Date(user.createdAt), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es })}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">Última Actualización</Label>
                  <p className="text-sm text-gray-900 mt-1">
                    {format(new Date(user.updatedAt), "dd 'de' MMMM 'de' yyyy 'a las' HH:mm", { locale: es })}
                  </p>
                </div>
                <div>
                  <Label className="text-sm font-medium text-gray-700">ID de Usuario</Label>
                  <p className="text-sm text-gray-900 font-mono mt-1 bg-gray-100 p-2 rounded">{user.id}</p>
                </div>
              </CardContent>
            </Card>
            </div>
          </>
        )}
        
        {/* Botones de acción */}
        {isEditMode ? (
          <div className="flex justify-end gap-3 pt-4">
            <Button 
              variant="outline"
              onClick={() => {
                router.push('/dashboard/admin/users');
              }}
              disabled={processing}
            >
              Cancelar
            </Button>
            <Button 
              onClick={handleSave}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
              disabled={processing}
            >
              <Save className="h-4 w-4 mr-2" />
              {processing ? 'Guardando...' : 'Guardar Cambios'}
            </Button>
          </div>
        ) : (
          <div className="flex justify-end gap-3 pt-4">
            <Button 
              onClick={() => router.push(`/dashboard/admin/users/${user.id}?mode=edit`)}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Button>
          </div>
        )}
      </div>

      {/* Diálogo de Confirmación de Eliminación */}
      <Dialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Confirmar Eliminación de Usuario
            </DialogTitle>
          </DialogHeader>
          
          {deleteAnalysis && (
            <div className="space-y-4">
              {/* Información del Usuario */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Usuario a eliminar:</h4>
                <div className="text-sm text-gray-700 space-y-1">
                  <p><strong>Nombre:</strong> {deleteAnalysis.user?.name}</p>
                  <p><strong>Email:</strong> {deleteAnalysis.user?.email}</p>
                </div>
              </div>

              {/* Análisis de Impacto */}
              {deleteAnalysis.summary?.hasImpact ? (
                <div className="bg-amber-50 border border-amber-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-amber-600" />
                    <h4 className="font-semibold text-amber-800">Impacto de la eliminación</h4>
                  </div>
                  <p className="text-sm text-amber-700 mb-3">
                    Se eliminarán <strong>{deleteAnalysis.summary.totalItems}</strong> registros adicionales:
                  </p>
                  <ul className="text-sm text-amber-700 space-y-1">
                    {deleteAnalysis.summary.itemsList.map((item: string, index: number) => (
                      <li key={index} className="flex items-start gap-2">
                        <span className="text-amber-600">•</span>
                        <span>{item}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              ) : (
                <div className="bg-green-50 border border-green-200 p-4 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <Shield className="h-4 w-4 text-green-600" />
                    <h4 className="font-semibold text-green-800">Sin impacto adicional</h4>
                  </div>
                  <p className="text-sm text-green-700">
                    No hay registros adicionales que se vean afectados por esta eliminación.
                  </p>
                </div>
              )}

              {/* Opciones de Eliminación */}
              <div className="space-y-3">
                <div className="bg-red-50 border border-red-200 p-3 rounded-lg">
                  <h5 className="font-semibold text-red-800 mb-1">Eliminación Física</h5>
                  <p className="text-sm text-red-700">
                    Elimina completamente de la base de datos y Clerk. 
                    El email podrá ser reutilizado para nuevos registros.
                  </p>
                </div>
                
                <div className="bg-blue-50 border border-blue-200 p-3 rounded-lg">
                  <h5 className="font-semibold text-blue-800 mb-1">Eliminación Lógica</h5>
                  <p className="text-sm text-blue-700">
                    Solo marca como eliminado. Es reversible y mantiene 
                    la integridad de los datos históricos.
                  </p>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowDeleteDialog(false)}
              disabled={deletingUser}
            >
              Cancelar
            </Button>
            <Button
              variant="secondary"
              onClick={executeLogicalDeletion}
              disabled={deletingUser}
            >
              {deletingUser ? 'Procesando...' : 'Eliminar Lógicamente'}
            </Button>
            <Button
              variant="destructive"
              onClick={executePhysicalDeletion}
              disabled={deletingUser}
            >
              {deletingUser ? 'Procesando...' : 'Eliminar Físicamente'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Diálogo de Edición de Consultorio */}
      <Dialog open={showEditConsultoryDialog} onOpenChange={setShowEditConsultoryDialog}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Edit className="h-5 w-5 text-blue-600" />
              Asignar Consultorio
            </DialogTitle>
            <DialogDescription>
              Selecciona el consultorio que deseas asignar a este rol.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div>
              <Label htmlFor="consultory-select">Consultorio</Label>
              <Select value={selectedConsultoryId} onValueChange={setSelectedConsultoryId}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar consultorio..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">Sin consultorio asignado</SelectItem>
                  {consultories.map((consultory) => (
                    <SelectItem key={consultory.id} value={consultory.id}>
                      {consultory.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowEditConsultoryDialog(false)}
              disabled={processing}
            >
              Cancelar
            </Button>
            <Button
              onClick={handleSaveConsultory}
              disabled={processing}
            >
              {processing ? 'Guardando...' : 'Guardar'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </>
  );
}