import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { patientInvitations, user } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, clerkUserId } = body;

    if (!token || !clerkUserId) {
      return NextResponse.json(
        { success: false, error: 'Token y ID de usuario requeridos' },
        { status: 400 }
      );
    }

    // Buscar invitación válida
    const invitations = await db
      .select()
      .from(patientInvitations)
      .where(
        and(
          eq(patientInvitations.invitationToken, token),
          eq(patientInvitations.status, 'pending')
        )
      )
      .limit(1);

    if (invitations.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Token inválido o ya utilizado' },
        { status: 400 }
      );
    }

    const invitation = invitations[0];

    // Iniciar transacción
    await db.transaction(async (tx) => {
      // 1. Actualizar el usuario con el clerkUserId
      await tx
        .update(user)
        .set({
          clerkUserId: clerkUserId,
          email: invitation.guardianEmail, // Actualizar con el email real del guardián
          emailVerified: true,
          updatedAt: new Date(),
        })
        .where(eq(user.id, invitation.patientUserId));

      // 2. Marcar la invitación como aceptada
      await tx
        .update(patientInvitations)
        .set({
          status: 'accepted',
          acceptedAt: new Date(),
        })
        .where(eq(patientInvitations.id, invitation.id));
    });

    // Obtener información actualizada del paciente
    const updatedPatient = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
      })
      .from(user)
      .where(eq(user.id, invitation.patientUserId))
      .limit(1);

    return NextResponse.json({
      success: true,
      message: 'Cuenta activada exitosamente',
      data: updatedPatient[0],
    });

  } catch (error) {
    console.error('Error completing activation:', error);
    return NextResponse.json(
      { success: false, error: 'Error al completar activación' },
      { status: 500 }
    );
  }
}