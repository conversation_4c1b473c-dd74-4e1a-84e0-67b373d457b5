import { nanoid } from 'nanoid';
import { db } from '../db/drizzle';
import { medications, medicationPrices, consultories } from '../db/schema';
import { eq } from 'drizzle-orm';

// Precios típicos en Guatemala (GTQ)
const MEDICATION_PRICES = [
  // Analgésicos y Antiinflamatorios
  { medicationName: 'Paracetamol 500mg', purchasePrice: 15.00, salePrice: 25.00 },
  { medicationName: 'Ibuprofeno 400mg', purchasePrice: 12.00, salePrice: 20.00 },
  
  // Antibióticos
  { medicationName: 'Amoxicilina 500mg', purchasePrice: 45.00, salePrice: 75.00 },
  { medicationName: 'Azitromicina 500mg', purchasePrice: 85.00, salePrice: 140.00 },
  
  // Medicamentos cardiovasculares
  { medicationName: 'Enalapril 10mg', purchasePrice: 35.00, salePrice: 55.00 },
  { medicationName: 'Atorvastatina 20mg', purchasePrice: 65.00, salePrice: 110.00 },
  
  // Medicamentos para diabetes
  { medicationName: 'Metformina 850mg', purchasePrice: 28.00, salePrice: 45.00 },
  
  // Medicamentos controlados
  { medicationName: 'Tramadol 50mg', purchasePrice: 55.00, salePrice: 90.00 },
  { medicationName: 'Diazepam 5mg', purchasePrice: 25.00, salePrice: 42.00 },
  
  // Medicamentos gastrointestinales
  { medicationName: 'Omeprazol 20mg', purchasePrice: 38.00, salePrice: 65.00 },
  
  // Medicamentos respiratorios
  { medicationName: 'Salbutamol 100mcg/dosis', purchasePrice: 85.00, salePrice: 140.00 },
  
  // Vitaminas y suplementos
  { medicationName: 'Complejo B', purchasePrice: 22.00, salePrice: 38.00 },
];

export async function seedMedicationPrices() {
  try {
    console.log('🌱 Seeding medication prices...');
    
    // Obtener todos los consultorios activos
    const activeConsultories = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true));
    
    if (activeConsultories.length === 0) {
      console.log('⚠️ No active consultories found. Skipping medication prices seeding.');
      return;
    }
    
    // Obtener todos los medicamentos
    const allMedications = await db
      .select()
      .from(medications)
      .where(eq(medications.isActive, true));
    
    console.log(`📊 Found ${allMedications.length} medications and ${activeConsultories.length} consultories`);
    
    let totalPricesAdded = 0;
    
    // Para cada consultorio, agregar precios de medicamentos
    for (const consultory of activeConsultories) {
      console.log(`💰 Adding prices for consultory: ${consultory.name}`);
      
      for (const priceData of MEDICATION_PRICES) {
        // Buscar el medicamento por nombre
        const medication = allMedications.find(med => 
          med.name === priceData.medicationName
        );
        
        if (!medication) {
          console.log(`⚠️ Medication not found: ${priceData.medicationName}`);
          continue;
        }
        
        // Verificar si ya existe el precio
        const existingPrice = await db
          .select()
          .from(medicationPrices)
          .where(eq(medicationPrices.medicationId, medication.id))
          .where(eq(medicationPrices.consultoryId, consultory.id))
          .limit(1);
        
        if (existingPrice.length > 0) {
          console.log(`💰 Price already exists for ${medication.name} in ${consultory.name}`);
          continue;
        }
        
        // Insertar precio
        await db.insert(medicationPrices).values({
          id: nanoid(),
          medicationId: medication.id,
          consultoryId: consultory.id,
          purchasePrice: priceData.purchasePrice,
          salePrice: priceData.salePrice,
          currency: 'GTQ',
          isActive: true,
          effectiveFrom: new Date(),
          effectiveTo: null, // Sin fecha de vencimiento
          createdAt: new Date(),
          updatedAt: new Date(),
        });
        
        totalPricesAdded++;
        console.log(`✅ Added price for ${medication.name}: Q${priceData.salePrice}`);
      }
    }
    
    console.log(`✅ Successfully added ${totalPricesAdded} medication prices`);
    
  } catch (error) {
    console.error('❌ Error seeding medication prices:', error);
    throw error;
  }
}

// Ejecutar si el archivo se ejecuta directamente
if (require.main === module) {
  seedMedicationPrices()
    .then(() => {
      console.log('✅ Medication prices seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Medication prices seeding failed:', error);
      process.exit(1);
    });
}