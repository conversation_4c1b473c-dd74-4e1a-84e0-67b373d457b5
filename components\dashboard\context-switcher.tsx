'use client';

import { useState } from 'react';
import { useUserContexts } from '@/hooks/use-user-contexts';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
  DropdownMenuLabel,
} from '@/components/ui/dropdown-menu';
import { 
  ChevronDown, 
  Users, 
  Stethoscope, 
  User, 
  Heart, 
  Package, 
  Settings, 
  RefreshCw,
  Bell
} from 'lucide-react';
import { toast } from 'sonner';

export function ContextSwitcher() {
  const { data, currentContext, switchContext } = useUserContexts();
  const [switching, setSwitching] = useState(false);

  if (!data || !data.hasMultipleContexts) {
    return null;
  }

  const contextConfig = {
    doctor: { 
      name: '<PERSON><PERSON><PERSON><PERSON>', 
      icon: Stethoscope, 
      color: 'bg-blue-100 text-blue-800 border-blue-200',
      description: data.contexts.work.isDoctor?.specialty 
    },
    assistant: { 
      name: '<PERSON><PERSON><PERSON>', 
      icon: Users, 
      color: 'bg-green-100 text-green-800 border-green-200',
      description: 'Gestión médica' 
    },
    patient: { 
      name: 'Paciente', 
      icon: User, 
      color: 'bg-purple-100 text-purple-800 border-purple-200',
      description: `${data.contexts.personal.isPatient?.appointments?.length || 0} citas` 
    },
    guardian: { 
      name: 'Encargado', 
      icon: Heart, 
      color: 'bg-orange-100 text-orange-800 border-orange-200',
      description: `${data.contexts.personal.isGuardian?.totalDependents || 0} dependientes` 
    },
    provider: { 
      name: 'Proveedor', 
      icon: Package, 
      color: 'bg-indigo-100 text-indigo-800 border-indigo-200',
      description: data.contexts.work.isProvider?.company 
    },
    full: { 
      name: 'Completo', 
      icon: Settings, 
      color: 'bg-gray-100 text-gray-800 border-gray-200',
      description: 'Todos los contextos' 
    }
  };

  const currentConfig = contextConfig[currentContext as keyof typeof contextConfig];
  const CurrentIcon = currentConfig?.icon || Settings;

  const handleContextSwitch = async (newContext: string) => {
    if (newContext === currentContext) return;
    
    setSwitching(true);
    try {
      await switchContext(newContext);
    } finally {
      setSwitching(false);
    }
  };

  const getAvailableContexts = () => {
    const available = [];
    
    if (data.contexts.work.isDoctor) {
      available.push({
        key: 'doctor',
        ...contextConfig.doctor,
        description: data.contexts.work.isDoctor.specialty
      });
    }
    
    if (data.contexts.work.isAssistant) {
      available.push({
        key: 'assistant',
        ...contextConfig.assistant
      });
    }
    
    if (data.contexts.work.isProvider) {
      available.push({
        key: 'provider',
        ...contextConfig.provider,
        description: data.contexts.work.isProvider.company
      });
    }
    
    if (data.contexts.personal.isPatient) {
      available.push({
        key: 'patient',
        ...contextConfig.patient,
        description: `${data.contexts.personal.isPatient.appointments?.length || 0} próximas citas`
      });
    }
    
    if (data.contexts.personal.isGuardian) {
      available.push({
        key: 'guardian',
        ...contextConfig.guardian,
        description: `${data.contexts.personal.isGuardian.totalDependents} dependientes`
      });
    }
    
    // Siempre disponible
    available.push({
      key: 'full',
      ...contextConfig.full
    });
    
    return available;
  };

  const availableContexts = getAvailableContexts();
  const hasNotifications = data.quickAccess.length > 0;

  return (
    <div className="flex items-center gap-2">
      {/* Notificaciones contextuales */}
      {hasNotifications && (
        <div className="relative">
          <Button 
            variant="ghost" 
            size="sm"
            className="text-yellow-600 hover:text-yellow-700 hover:bg-yellow-50"
          >
            <Bell className="h-4 w-4" />
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 text-xs p-0 flex items-center justify-center"
            >
              {data.quickAccess.length}
            </Badge>
          </Button>
        </div>
      )}

      {/* Selector de contexto */}
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="outline" 
            className="gap-2 text-sm"
            disabled={switching}
          >
            {switching ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <CurrentIcon className="h-4 w-4" />
            )}
            <div className="flex flex-col items-start">
              <span className="font-medium">{currentConfig?.name}</span>
              {currentConfig?.description && (
                <span className="text-xs text-gray-500">{currentConfig.description}</span>
              )}
            </div>
            <ChevronDown className="h-3 w-3" />
          </Button>
        </DropdownMenuTrigger>
        
        <DropdownMenuContent align="end" className="w-64">
          <DropdownMenuLabel>Cambiar Contexto</DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {availableContexts.map((context) => {
            const Icon = context.icon;
            const isActive = context.key === currentContext;
            
            return (
              <DropdownMenuItem
                key={context.key}
                onClick={() => handleContextSwitch(context.key)}
                className={`flex items-center gap-3 ${isActive ? 'bg-gray-100' : ''}`}
              >
                <div className={`p-1 rounded ${isActive ? 'bg-gray-200' : 'bg-gray-100'}`}>
                  <Icon className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="font-medium">{context.name}</div>
                  {context.description && (
                    <div className="text-xs text-gray-500">{context.description}</div>
                  )}
                </div>
                {isActive && (
                  <Badge variant="secondary" className="text-xs">Actual</Badge>
                )}
              </DropdownMenuItem>
            );
          })}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}