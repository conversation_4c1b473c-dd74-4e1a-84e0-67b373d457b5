import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalSpecialties } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET - Obtener especialidad médica por ID
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = await params;

    const specialty = await db
      .select()
      .from(medicalSpecialties)
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .limit(1);

    if (specialty.length === 0) {
      return NextResponse.json({ error: 'Especialidad médica no encontrada' }, { status: 404 });
    }

    return NextResponse.json({ 
      success: true, 
      data: specialty[0] 
    });

  } catch (error) {
    console.error('Error fetching medical specialty:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// PUT - Actualizar especialidad médica
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden editar especialidades médicas.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { name, category, isActive } = body;

    // Validar datos requeridos
    if (!name) {
      return NextResponse.json({ 
        error: 'El nombre es requerido' 
      }, { status: 400 });
    }

    // Verificar que la especialidad existe
    const existingSpecialty = await db
      .select()
      .from(medicalSpecialties)
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .limit(1);

    if (existingSpecialty.length === 0) {
      return NextResponse.json({ error: 'Especialidad médica no encontrada' }, { status: 404 });
    }

    // Actualizar especialidad
    const [updatedSpecialty] = await db
      .update(medicalSpecialties)
      .set({
        name,
        category: category || null,
        isActive: isActive !== undefined ? isActive : true,
        updatedAt: new Date()
      })
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedSpecialty,
      message: 'Especialidad médica actualizada exitosamente'
    });

  } catch (error) {
    console.error('Error updating medical specialty:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}

// DELETE - Eliminar especialidad médica
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden eliminar especialidades médicas.' 
      }, { status: 403 });
    }

    const { id } = await params;
    const url = new URL(request.url);
    const deleteType = url.searchParams.get('type') || 'logical'; // 'logical' o 'physical'

    // Verificar que la especialidad existe
    const existingSpecialty = await db
      .select()
      .from(medicalSpecialties)
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .limit(1);

    if (existingSpecialty.length === 0) {
      return NextResponse.json({ error: 'Especialidad médica no encontrada' }, { status: 404 });
    }

    if (deleteType === 'physical') {
      // Eliminación física - remover completamente del base de datos
      await db
        .delete(medicalSpecialties)
        .where(eq(medicalSpecialties.id, parseInt(id)));

      return NextResponse.json({ 
        success: true, 
        message: 'Especialidad médica eliminada físicamente exitosamente'
      });
    } else {
      // Eliminación lógica - marcar como inactivo
      const [updatedSpecialty] = await db
        .update(medicalSpecialties)
        .set({
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(medicalSpecialties.id, parseInt(id)))
        .returning();

      return NextResponse.json({ 
        success: true, 
        data: updatedSpecialty,
        message: 'Especialidad médica desactivada exitosamente'
      });
    }

  } catch (error) {
    console.error('Error deleting medical specialty:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}