import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { activityTypes } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Cambiar estado activo/inactivo
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    if (!['admin', 'assistant'].includes(userRole)) {
      return NextResponse.json({ 
        error: 'Sin permisos para cambiar estado de tipos de actividad' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el tipo de actividad existe
    const existingType = await db.select()
      .from(activityTypes)
      .where(eq(activityTypes.id, id))
      .limit(1);

    if (existingType.length === 0) {
      return NextResponse.json({ error: 'Tipo de actividad no encontrado' }, { status: 404 });
    }

    const activityType = existingType[0];
    const newStatus = !activityType.isActive;

    // Actualizar estado
    const [updatedType] = await db.update(activityTypes)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(activityTypes.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedType,
      message: `Tipo de actividad ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error cambiando estado del tipo de actividad:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}