import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { medications } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/catalogs/medications/[id]/toggle-status - Activar/Desactivar medicamento
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Obtener el medicamento actual
    const currentMedication = await db
      .select()
      .from(medications)
      .where(eq(medications.id, id))
      .limit(1);

    if (!currentMedication.length) {
      return NextResponse.json(
        { error: 'Medicamento no encontrado' },
        { status: 404 }
      );
    }

    const medication = currentMedication[0];
    const newStatus = !medication.isActive;

    // Actualizar estado
    await db
      .update(medications)
      .set({
        isActive: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(medications.id, id));

    return NextResponse.json({
      success: true,
      message: `Medicamento ${newStatus ? 'activado' : 'desactivado'} exitosamente`,
      data: {
        id,
        isActive: newStatus,
      },
    });

  } catch (error: any) {
    console.error('Error toggling medication status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}