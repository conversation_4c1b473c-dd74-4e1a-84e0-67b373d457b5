import { db } from '@/db/drizzle';
import { 
  countries, 
  departments, 
  municipalities, 
  relationships, 
  occupations, 
  medicalSpecialties,
  consultories,
  user
} from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export class CatalogValidator {
  
  // Validar que existe un país
  static async validateCountry(countryId: number): Promise<boolean> {
    const result = await db.select().from(countries)
      .where(and(eq(countries.id, countryId), eq(countries.active, true)));
    return result.length > 0;
  }
  
  // Validar que existe un departamento
  static async validateDepartment(departmentId: number, countryId?: number): Promise<boolean> {
    const conditions = [eq(departments.id, departmentId), eq(departments.active, true)];
    if (countryId) conditions.push(eq(departments.countryId, countryId));
    
    const result = await db.select().from(departments).where(and(...conditions));
    return result.length > 0;
  }
  
  // Validar que existe un municipio
  static async validateMunicipality(municipalityId: number, departmentId?: number): Promise<boolean> {
    const conditions = [eq(municipalities.id, municipalityId), eq(municipalities.active, true)];
    if (departmentId) conditions.push(eq(municipalities.departmentId, departmentId));
    
    const result = await db.select().from(municipalities).where(and(...conditions));
    return result.length > 0;
  }
  
  // Validar que existe una relación
  static async validateRelationship(relationshipId: number): Promise<boolean> {
    const result = await db.select().from(relationships)
      .where(and(eq(relationships.id, relationshipId), eq(relationships.active, true)));
    return result.length > 0;
  }
  
  // Validar que existe una ocupación
  static async validateOccupation(occupationId: number): Promise<boolean> {
    const result = await db.select().from(occupations)
      .where(and(eq(occupations.id, occupationId), eq(occupations.active, true)));
    return result.length > 0;
  }
  
  // Validar que existe una especialidad médica
  static async validateMedicalSpecialty(specialtyId: number): Promise<boolean> {
    const result = await db.select().from(medicalSpecialties)
      .where(and(eq(medicalSpecialties.id, specialtyId), eq(medicalSpecialties.active, true)));
    return result.length > 0;
  }
  
  // Validar que existe un consultorio
  static async validateConsultory(consultoryId: string): Promise<boolean> {
    const result = await db.select().from(consultories)
      .where(and(eq(consultories.id, consultoryId), eq(consultories.isActive, true)));
    return result.length > 0;
  }
  
  // Validar que existe un usuario
  static async validateUser(userId: string): Promise<boolean> {
    const result = await db.select().from(user)
      .where(eq(user.id, userId));
    return result.length > 0;
  }
  
  // Validar que existe un doctor
  static async validateDoctor(doctorId: string): Promise<boolean> {
    const result = await db.select().from(user)
      .where(eq(user.id, doctorId));
    
    if (result.length === 0) return false;
    
    const userData = result[0];
    return userData.activeRoles?.includes('doctor') || false;
  }
  
  // Validar perfil general completo
  static async validateGeneralProfile(generalProfile: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar país
    if (generalProfile.countryId && !(await this.validateCountry(generalProfile.countryId))) {
      errors.push('País no válido');
    }
    
    // Validar departamento
    if (generalProfile.departmentId && !(await this.validateDepartment(generalProfile.departmentId, generalProfile.countryId))) {
      errors.push('Departamento no válido');
    }
    
    // Validar municipio
    if (generalProfile.municipalityId && !(await this.validateMunicipality(generalProfile.municipalityId, generalProfile.departmentId))) {
      errors.push('Municipio no válido');
    }
    
    // Validar ocupación
    if (generalProfile.occupationId && !(await this.validateOccupation(generalProfile.occupationId))) {
      errors.push('Ocupación no válida');
    }
    
    // Validar relación de emergencia
    if (generalProfile.emergencyRelationshipId && !(await this.validateRelationship(generalProfile.emergencyRelationshipId))) {
      errors.push('Relación de emergencia no válida');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos específicos del doctor
  static async validateDoctorData(doctorData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar especialidad
    if (doctorData.specialtyId && !(await this.validateMedicalSpecialty(doctorData.specialtyId))) {
      errors.push('Especialidad médica no válida');
    }
    
    // Validar consultorio
    if (doctorData.consultoryId && !(await this.validateConsultory(doctorData.consultoryId))) {
      errors.push('Consultorio no válido');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos específicos del paciente
  static async validatePatientData(patientData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar doctor preferido
    if (patientData.preferredDoctorId && !(await this.validateDoctor(patientData.preferredDoctorId))) {
      errors.push('Doctor preferido no válido');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos específicos del asistente
  static async validateAssistantData(assistantData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar doctor asociado
    if (assistantData.associatedDoctorId && !(await this.validateDoctor(assistantData.associatedDoctorId))) {
      errors.push('Doctor asociado no válido');
    }
    
    // Validar consultorio
    if (assistantData.consultoryId && !(await this.validateConsultory(assistantData.consultoryId))) {
      errors.push('Consultorio no válido');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos específicos del encargado
  static async validateGuardianData(guardianData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar dependientes
    if (guardianData.dependents && Array.isArray(guardianData.dependents)) {
      for (const dependentId of guardianData.dependents) {
        if (!(await this.validateUser(dependentId))) {
          errors.push(`Dependiente ${dependentId} no válido`);
        }
      }
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos específicos del proveedor
  static async validateProviderData(providerData: any): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];
    
    // Validar consultorio
    if (providerData.consultoryId && !(await this.validateConsultory(providerData.consultoryId))) {
      errors.push('Consultorio no válido');
    }
    
    return { valid: errors.length === 0, errors };
  }
  
  // Validar datos completos del usuario
  static async validateUserData(userData: {
    generalProfile: any;
    rolesData: any;
    activeRoles: string[];
  }): Promise<{ valid: boolean; errors: string[] }> {
    const allErrors: string[] = [];
    
    // Validar perfil general
    const generalValidation = await this.validateGeneralProfile(userData.generalProfile);
    allErrors.push(...generalValidation.errors);
    
    // Validar cada rol activo
    for (const role of userData.activeRoles) {
      const roleData = userData.rolesData[role];
      if (!roleData) continue;
      
      let roleValidation: { valid: boolean; errors: string[] };
      
      switch (role) {
        case 'doctor':
          roleValidation = await this.validateDoctorData(roleData);
          break;
        case 'patient':
          roleValidation = await this.validatePatientData(roleData);
          break;
        case 'assistant':
          roleValidation = await this.validateAssistantData(roleData);
          break;
        case 'guardian':
          roleValidation = await this.validateGuardianData(roleData);
          break;
        case 'provider':
          roleValidation = await this.validateProviderData(roleData);
          break;
        default:
          continue;
      }
      
      allErrors.push(...roleValidation.errors);
    }
    
    return { valid: allErrors.length === 0, errors: allErrors };
  }
}

// Función helper para usar en APIs
export async function validateBeforeSubmit(userData: any): Promise<{ success: boolean; errors: string[] }> {
  const validation = await CatalogValidator.validateUserData(userData);
  return { success: validation.valid, errors: validation.errors };
} 