/**
 * Script para aplicar estandarización a todos los menús contextuales
 * Agrega separador antes de las acciones de eliminar
 */

import fs from 'fs';
import path from 'path';

// Lista de archivos que necesitan actualización según el análisis
const filesToUpdate = [
  'app/(dashboard)/dashboard/admin/catalogs/doctor-service-prices/page-template.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/doctor-service-prices/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/media-sources/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/non-pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/pathological-history/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/religions/page.tsx',
  'app/(dashboard)/dashboard/admin/catalogs/symptoms/page.tsx',
];

function updateFile(filePath: string) {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let updated = false;
    
    // Primero, asegurar que DropdownMenuSeparator esté importado
    if (!content.includes('DropdownMenuSeparator')) {
      content = content.replace(
        /from '@\/components\/ui\/dropdown-menu';/,
        `from '@/components/ui/dropdown-menu';`
      );
      
      content = content.replace(
        /DropdownMenuLabel,\s*\n\s*DropdownMenuTrigger,/,
        `DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,`
      );
      updated = true;
    }
    
    // Patrón para encontrar menús que necesitan separador
    // Busca "Editar" seguido de Toggle/Desactivar/Activar sin separador
    const pattern = /(Editar\s*<\/DropdownMenuItem>\s*)(\s*<DropdownMenuItem[^>]*(?:toggle|Desactivar|Activar))/g;
    
    if (pattern.test(content)) {
      content = content.replace(pattern, '$1\n                              <DropdownMenuSeparator />$2');
      updated = true;
    }
    
    // También buscar en versión móvil (con menos espacios)
    const mobilePattern = /(Editar\s*<\/DropdownMenuItem>\s*)(\s*<DropdownMenuItem[^>]*(?:toggle|Desactivar|Activar))/g;
    content = content.replace(mobilePattern, (match, p1, p2) => {
      if (!match.includes('DropdownMenuSeparator')) {
        updated = true;
        return p1 + '\n                          <DropdownMenuSeparator />' + p2;
      }
      return match;
    });
    
    // Actualizar className para eliminar (agregar focus:text-red-600)
    content = content.replace(
      /className="text-red-600"\s*onClick=\{[^}]*openDeleteDialog/g,
      'className="text-red-600 focus:text-red-600" \n                                onClick={() => openDeleteDialog'
    );
    
    // Versión móvil
    content = content.replace(
      /className="text-red-600"\s*onClick=\{[^}]*openDeleteDialog/g,
      'className="text-red-600 focus:text-red-600" \n                            onClick={() => openDeleteDialog'
    );
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf-8');
      console.log(`✅ Actualizado: ${filePath}`);
      return true;
    } else {
      console.log(`⏭️  Sin cambios: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Error actualizando ${filePath}:`, error);
    return false;
  }
}

// Main
async function main() {
  console.log('🔧 Aplicando estandarización a menús contextuales...\n');
  
  let updatedCount = 0;
  let errorCount = 0;
  
  for (const file of filesToUpdate) {
    const result = updateFile(file);
    if (result) {
      updatedCount++;
    } else {
      errorCount++;
    }
  }
  
  console.log('\n📊 Resumen:');
  console.log(`✅ Archivos actualizados: ${updatedCount}`);
  console.log(`❌ Archivos con errores o sin cambios: ${errorCount}`);
  console.log(`📁 Total procesados: ${filesToUpdate.length}`);
  
  console.log('\n💡 Recuerda verificar los cambios y hacer pruebas antes de confirmar.');
}

main().catch(console.error);