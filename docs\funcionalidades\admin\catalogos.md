# Funcionalidad: Gestión de Catálogos Administrativos - Por Fases

## 📋 Resumen

**Ubicación**: `/dashboard/admin/catalogs`  
**Acceso**: Solo administradores  
**Propósito**: CRUD completo de catálogos administrativos fundamentales para el sistema médico pediátrico

## 🚀 PLAN DE IMPLEMENTACIÓN POR FASES

### **🔴 FASE 1: CATÁLOGOS CRÍTICOS PARA FUNCIONALIDAD INMEDIATA**
**Objetivo**: Completar onboarding + citas + expediente básico  
**Total**: 15 catálogos (15 ✅ implementados)  
**Progreso**: 100% completado 🎉

### **🟡 FASE 2: SERVICIOS MÉDICOS Y SEGUROS** 
**Objetivo**: Facturación Guatemala + seguros médicos + Integración CIE-11  
**Total**: 8 catálogos (3 ✅ implementados)  
**Progreso**: 37.5% completado

### **🟢 FASE 3: CATÁLOGOS AVANZADOS**
**Objetivo**: Funcionalidades opcionales y empresariales  
**Total**: 7 catálogos (3 ✅ implementados)  
**Progreso**: 42.9% completado

---

## 🚀 **FASE 1: CATÁLOGOS CRÍTICOS (15 catálogos) - 100% COMPLETADO**

### **✅ IMPLEMENTADOS EN BASE DE DATOS (6 catálogos)**

#### 1. 🌍 **Países** ✅
```typescript
interface Country {
  id: string;
  name: string;          // "Guatemala", "Estados Unidos"
  code: string;          // "GT", "US", "MX"
  phoneCode: string;     // "+502", "+1", "+52"
  currency: string;      // "GTQ", "USD", "MXN"
  isActive: boolean;
}
```

#### 2. 🏛️ **Departamentos** ✅
```typescript
interface Department {
  id: string;
  name: string;          // "Guatemala", "Sacatepéquez"
  countryId: string;     // Relación con país
  code: string;          // Código departamental
  isActive: boolean;
}
```

#### 3. 🏘️ **Municipios** ✅
```typescript
interface Municipality {
  id: string;
  name: string;          // "Guatemala", "Antigua Guatemala"
  departmentId: string;  // Relación con departamento
  code: string;          // Código municipal
  isActive: boolean;
}
```

#### 4. 💼 **Ocupaciones** ✅
```typescript
interface Occupation {
  id: string;
  name: string;          // "Médico", "Ingeniero", "Estudiante"
  category: string;      // "Salud", "Tecnología", "Educación"
  isActive: boolean;
}
```

#### 5. 👨‍👩‍👧‍👦 **Parentescos** ✅
```typescript
interface Relationship {
  id: string;
  name: string;          // "Padre", "Madre", "Hermano", "Cónyuge"
  canBeGuardian: boolean; // Puede ser encargado legal
  isActive: boolean;
}
```

#### 6. 🩺 **Especialidades Médicas** ✅
```typescript
interface MedicalSpecialty {
  id: string;
  name: string;          // "Pediatría", "Cardiología", "Neurología"
  code: string;          // Código profesional
  description: string;
  isActive: boolean;
}
```

### **✅ COMPLETAMENTE IMPLEMENTADAS EN FASE 1 (9 catálogos)**

> **ESTADO**: Todas las funcionalidades CRUD completadas con estándares unificados
> 
> **ÚLTIMA ACTUALIZACIÓN**: Julio 2025
> 
> **FUNCIONALIDADES IMPLEMENTADAS**:
> - ✅ CRUD completo (Crear, Leer, Actualizar, Eliminar físico/lógico)
> - ✅ Formularios de creación y edición con validaciones
> - ✅ Sistema de filtros avanzados (búsqueda, estado, categorías)
> - ✅ Paginación del lado del servidor
> - ✅ Ordenamiento por columnas
> - ✅ Modal de detalles informativos
> - ✅ Activación/desactivación desde grid
> - ✅ Colores estandarizados en acciones (naranja/verde para estado, rojo para eliminar)
> - ✅ Responsive design (desktop + mobile)
> - ✅ Validaciones de backend y frontend
> - ✅ Manejo de errores y toast notifications

#### 7. 💰 **Monedas** ✅
```typescript
interface Currency {
  id: string;
  name: string;           // "Quetzal", "Dólar Estadounidense"
  code: string;           // "GTQ", "USD"
  symbol: string;         // "Q", "$"
  exchangeRate: number;   // Tasa de cambio base
  isDefault: boolean;     // Moneda principal del consultorio
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/currencies
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con datos iniciales (GTQ, USD, EUR)
// ✅ Funcionalidad: CRUD completo para admin
```

#### 9. 📅 **Tipos de Actividad** ✅
```typescript
interface ActivityType {
  id: string;
  name: string;          // "Cita", "Cirugía", "Vacaciones", "Teleconsulta"
  category: 'medical' | 'administrative' | 'personal';
  color: string;         // Color para calendario (#3B82F6)
  duration: number;      // Duración típica en minutos
  requiresPatient: boolean;
  allowsRecurrence: boolean; // Permite actividades recurrentes
  icon: string;          // Icono para UI
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/activity-types
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 10 tipos predefinidos
// ✅ Funcionalidad: CRUD para admin y asistentes
```

#### 12. 📢 **Medios/Origen de Pacientes** ✅
```typescript
interface MediaSource {
  id: string;
  name: string;          // "Facebook", "Google", "Referido", "Instagram"
  type: 'digital' | 'traditional' | 'referral' | 'direct';
  category: string;      // "Redes Sociales", "Buscadores", "Referidos"
  trackingEnabled: boolean; // Si se puede hacer tracking
  cost: number;          // Costo por adquisición (para ROI)
  description: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/media-sources
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 15 medios estratégicos
// ✅ Funcionalidad: CRUD + Analytics de ROI
```

#### 13. 🎓 **Niveles de Escolaridad** ✅
```typescript
interface EducationLevel {
  id: string;
  name: string;           // "Primaria completa", "Universidad", "Posgrado"
  order: number;          // Orden jerárquico (0-9)
  description: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/education-levels
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 10 niveles para Guatemala
// ✅ Funcionalidad: CRUD para admin
```

#### 14. 📄 **Tipos de Documento** ✅
```typescript
interface DocumentType {
  id: string;
  name: string;           // "DPI", "Pasaporte", "CURP", "SSN"
  shortName: string;      // "DPI", "Passport"
  countryId: string;      // "GT", "US", "MX"
  countryName: string;
  format: string;         // Regex de validación
  maxLength: number;
  minLength: number;
  isRequired: boolean;    // Documento obligatorio para el país
  description: string;
  example: string;        // Ejemplo de formato válido
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/document-types
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con validación para GT, US, MX
// ✅ Funcionalidad: CRUD + validación de formato
```

#### 15. 💍 **Estados Civiles** ✅
```typescript
interface MaritalStatus {
  id: string;
  name: string;           // "Soltero", "Casado", "Divorciado"
  allowsSpouse: boolean;  // Si permite registrar cónyuge
  legalImplications: string; // Implicaciones legales médicas
  description: string;
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/marital-status
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 8 estados + implicaciones médicas
// ✅ Funcionalidad: CRUD + consulta de implicaciones legales
```

#### 16. 🩺 **Antecedentes Patológicos** ✅
```typescript
interface PathologicalHistory {
  id: string;
  name: string;           // "Asma", "Diabetes tipo 1", "Epilepsia"
  category: string;       // "Respiratorio", "Endocrino", "Neurológico"
  icd11Code: string;      // Código CIE-11
  severity: 'low' | 'moderate' | 'high';
  isHereditary: boolean;
  requiresSpecialistFollow: boolean;
  commonInChildren: boolean;
  riskLevel: 'low' | 'medium' | 'high';
  description: string;
  symptoms: string[];     // Síntomas principales
  treatments: string[];   // Tratamientos comunes
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/pathological-history
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 15 condiciones pediátricas
// ✅ Funcionalidad: CRUD + filtros por categoría y severidad
```

#### 17. 🌱 **Antecedentes No Patológicos** ✅
```typescript
interface NonPathologicalHistory {
  id: string;
  name: string;           // "Lactancia materna", "Ejercicio regular"
  category: string;       // "Alimentación", "Actividad Física", "Sueño"
  subcategory: string;    // "Lactancia", "Ejercicio", "Horarios"
  isPositive: boolean;    // true = beneficioso, false = factor de riesgo
  importance: 'low' | 'medium' | 'high';
  ageRelevant: string;    // "infant", "child", "adolescent", "all"
  description: string;
  benefits?: string[];    // Beneficios (si es positivo)
  risks?: string[];       // Riesgos (si es negativo)
  duration: string;       // Duración típica o recomendada
  order: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/non-pathological-history
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 20 antecedentes relevantes
// ✅ Funcionalidad: CRUD + filtros por edad y tipo
```

#### 18. 🏥 **Consultorios (Ampliado)** ✅
```typescript
interface Consultory {
  id: string;
  name: string;           // "Consultorio Pediatría Principal"
  code: string;           // "PED-001", "CAR-001"
  type: 'pediatric' | 'specialty' | 'emergency' | 'procedure' | 'telemedicine';
  specialty: string;      // "Pediatría General", "Cardiología Pediátrica"
  capacity: number;       // Número de pacientes simultáneos
  floor: number;
  building: string;       // "Edificio Principal", "Edificio Especialidades"
  address: string;
  phone: string;
  email: string;
  businessHours: {
    monday: { start: string; end: string; isActive: boolean };
    tuesday: { start: string; end: string; isActive: boolean };
    wednesday: { start: string; end: string; isActive: boolean };
    thursday: { start: string; end: string; isActive: boolean };
    friday: { start: string; end: string; isActive: boolean };
    saturday: { start: string; end: string; isActive: boolean };
    sunday: { start: string; end: string; isActive: boolean };
  };
  equipment: string[];    // Equipos médicos disponibles
  services: string[];     // Servicios que se ofrecen
  isEmergencyCapable: boolean;
  hasAirConditioning: boolean;
  hasWaitingRoom: boolean;
  accessibility: {
    wheelchairAccessible: boolean;
    hasElevator: boolean;
    hasBrailleSignage: boolean;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/consultories
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 8 consultorios completos
// ✅ Funcionalidad: CRUD + filtros por tipo y disponibilidad
```


---

## 📊 **ESTADO ACTUAL DE IMPLEMENTACIÓN**

**🔄 PROGRESO GENERAL:**
- **Total de catálogos del sistema**: 30 catálogos
- **Fase 1 completada**: 15/15 catálogos (100%) 🎉
- **Fase 2 en progreso**: 3/8 catálogos (37.5%) 🚀
- **Fase 3 en progreso**: 3/7 catálogos (42.9%) 🚀
- **Progreso total**: 21/30 catálogos = **70% completado**

**🚀 CRUDS COMPLETOS IMPLEMENTADOS RECIENTEMENTE:**

> **IMPLEMENTACIÓN COMPLETA DE FUNCIONALIDADES CRUD**:
> - **Education Levels**: Frontend y backend completo con activación/desactivación
> - **Document Types**: CRUD completo con validaciones de formato y país
> - **Marital Status**: CRUD completo con implicaciones legales
> - **Todas las funcionalidades**: Formularios, filtros, paginación, eliminación física/lógica

**📋 Catálogos con CRUD completo implementado:**
1. ✅ **Países** - CRUD completo + colores estandarizados
2. ✅ **Departamentos** - CRUD completo + colores estandarizados  
3. ✅ **Municipios** - CRUD completo + colores estandarizados
4. ✅ **Ocupaciones** - CRUD completo + colores estandarizados
5. ✅ **Parentescos** - CRUD completo + colores estandarizados
6. ✅ **Especialidades Médicas** - CRUD completo + colores estandarizados
7. ✅ **Niveles de Escolaridad** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
8. ✅ **Tipos de Documento** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
9. ✅ **Estados Civiles** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
10. ✅ **Monedas** - CRUD completo + colores estandarizados
11. ✅ **Consultorios** - CRUD completo + colores estandarizados  
12. ✅ **Tipos de Actividad** - CRUD completo + colores estandarizados
13. ✅ **Antecedentes Patológicos** - CRUD completo + colores estandarizados
14. ✅ **Antecedentes No Patológicos** - CRUD completo + colores estandarizados
15. ✅ **Medios de Comunicación** - CRUD completo + colores estandarizados
16. ✅ **Empresas/NITs** - CRUD completo + validación NIT guatemalteco
17. ✅ **Servicios Médicos** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
18. ✅ **Precios por Médico** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
19. ✅ **Medicamentos** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
20. ✅ **Síntomas/Padecimientos** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**
21. ✅ **Religiones** - **CRUD COMPLETO RECIÉN IMPLEMENTADO**

**🏥 Arquitectura de APIs implementada:**
- Todas las APIs incluyen autenticación con Clerk
- Control de acceso basado en roles (admin, doctor, assistant)
- Validaciones completas de datos
- Paginación y filtros avanzados
- Soft delete para mantener integridad referencial
- **✅ Base de datos PostgreSQL con Drizzle ORM**
- **✅ Queries optimizadas con índices de rendimiento**
- **✅ Data seeding completado con datos de producción**
- Compatibilidad con contexto guatemalteco

**📄 Navegación implementada:**
- Sidebar actualizado para admin, doctor y assistant
- Páginas de catálogos con diferentes niveles de permisos
- Filtros por fase de implementación
- Métricas de progreso en tiempo real

**✅ Base de Datos PostgreSQL completada:**
- Esquemas Drizzle creados para los 9 nuevos catálogos
- Datos mock migrados exitosamente a PostgreSQL con Drizzle ORM
- Relaciones implementadas entre catálogos en la base de datos
- Todas las APIs migradas de mock data a queries reales

**✅ ESTANDARIZACIÓN COMPLETADA:**
- ✅ **Campos isActive**: Agregados a formularios de crear y editar en todos los catálogos
- ✅ **Colores de acciones**: Naranja para desactivar, verde para activar, rojo para eliminar
- ✅ **Activación/desactivación**: Desde dropdown de acciones en todos los grids
- ✅ **Consistencia UI/UX**: Patrones unificados en todos los catálogos

**🔄 Siguientes pasos recomendados:**
1. ✅ ~~Crear esquemas Drizzle~~ - **COMPLETADO**
2. ✅ ~~Migrar datos mock~~ - **COMPLETADO**
3. ✅ ~~Implementar relaciones~~ - **COMPLETADO**
4. ✅ ~~Implementar CRUDs completos~~ - **COMPLETADO**
5. ✅ ~~Estandarizar colores y UI~~ - **COMPLETADO**
6. **Probar todas las APIs** en el admin backend
7. **Comenzar Fase 2** con servicios médicos y seguros

---

## 🔄 **RESUMEN DE APIS IMPLEMENTADAS - FASE 1**

### **Catálogos base (implementados previamente):**
1. ✅ **Países** - `/api/catalogs/countries`
2. ✅ **Departamentos** - `/api/catalogs/departments` 
3. ✅ **Municipios** - `/api/catalogs/municipalities`
4. ✅ **Ocupaciones** - `/api/catalogs/occupations`
5. ✅ **Parentescos** - `/api/catalogs/relationships`
6. ✅ **Especialidades Médicas** - `/api/catalogs/medical-specialties`

### **Catálogos recién implementados:**
7. ✅ **Monedas** - `/api/catalogs/currencies`
8. ✅ **Tipos de Actividad** - `/api/catalogs/activity-types`
9. ✅ **Medios/Origen de Pacientes** - `/api/catalogs/media-sources`
10. ✅ **Niveles de Escolaridad** - `/api/catalogs/education-levels`
11. ✅ **Tipos de Documento** - `/api/catalogs/document-types`
12. ✅ **Estados Civiles** - `/api/catalogs/marital-status`
13. ✅ **Antecedentes Patológicos** - `/api/catalogs/pathological-history`
14. ✅ **Antecedentes No Patológicos** - `/api/catalogs/non-pathological-history`
15. ✅ **Consultorios (Ampliado)** - `/api/catalogs/consultories`

### **Catálogos de Fase 2 implementados:**
16. ✅ **Empresas/NITs** - `/api/catalogs/companies`
17. ✅ **Servicios Médicos** - `/api/catalogs/medical-services`
18. ✅ **Precios por Médico** - `/api/catalogs/doctor-service-prices`

#### 18. 💰 **Precios por Médico** ✅ - **RECIÉN COMPLETADO**
```typescript
interface DoctorServicePrice {
  id: string;
  doctorId: string;         // 👨‍⚕️ Referencia al médico
  serviceId: string;        // 📋 Referencia al servicio médico
  customPrice: number;      // 💰 Precio personalizado por médico
  currency: string;         // \"GTQ\", \"USD\" (default: GTQ)
  isActive: boolean;        // Si este precio está activo
  effectiveFrom: Date;      // 📅 Fecha desde cuando aplica
  effectiveUntil?: Date;    // 📅 Fecha hasta cuando aplica (opcional)
  notes?: string;           // Notas adicionales sobre el precio
  createdAt: Date;
  updatedAt: Date;
  
  // Datos relacionados (para vistas)
  doctorName: string;
  doctorLastName: string;
  doctorEmail: string;
  serviceName: string;
  serviceCategory: string;
  serviceBasePrice: number; // Precio base del servicio
}

// ✅ API: /api/catalogs/doctor-service-prices
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Tabla con restricciones únicas y relaciones
// ✅ Funcionalidad: CRUD completo + lógica de selección de precios
// ✅ Lógica de Negocio: 
//     - Solo un precio activo por médico-servicio
//     - Precio personalizado sobrescribe precio base
//     - Control de fechas de vigencia
//     - Eliminación lógica y física
// ✅ UI: CRUD completo siguiendo estándares del sistema
// ✅ Paginación: Solo visible cuando totalPages > 1 (CORREGIDO)
// ✅ Endpoints Adicionales:
//     - /api/services/pricing - Obtener precio efectivo
//     - /api/services/pricing/bulk - Precios múltiples
//     - /api/catalogs/doctor-service-prices/doctors - Lista de médicos
```

**Lógica de Selección de Precios:**
```typescript
// Algoritmo de selección de precio efectivo:
// 1. ¿Existe precio personalizado activo para este médico + servicio?
//    → SÍ: Usar precio personalizado
//    → NO: Usar precio base del servicio
// 2. Verificar vigencia del precio personalizado
// 3. Considerar moneda y fecha de consulta
```

---

#### 13. 🎓 **Niveles de Escolaridad** ❌
```typescript
interface EducationLevel {
  id: string;
  name: string;          // "Primaria", "Secundaria", "Universidad", "Posgrado"
  order: number;         // Orden secuencial (1, 2, 3, 4...)
  description: string;   // Descripción del nivel
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Ejemplos de niveles:
const educationLevels = [
  { name: "Sin educación formal", order: 0 },
  { name: "Primaria incompleta", order: 1 },
  { name: "Primaria completa", order: 2 },
  { name: "Secundaria incompleta", order: 3 },
  { name: "Secundaria completa", order: 4 },
  { name: "Universidad incompleta", order: 5 },
  { name: "Universidad completa", order: 6 },
  { name: "Posgrado", order: 7 },
  { name: "Doctorado", order: 8 }
];
```

#### 14. 🏷️ **Tipos de Documento** ❌
```typescript
interface DocumentType {
  id: string;
  name: string;          // "DPI", "Pasaporte", "Cédula de Vecindad"
  countryId: string;     // Relación con país
  format: string;        // Patrón de validación regex
  maxLength: number;     // Longitud máxima
  minLength: number;     // Longitud mínima
  isRequired: boolean;   // Obligatorio para el país
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Ejemplos para Guatemala:
const documentTypes = [
  { 
    name: "DPI", 
    countryId: "GT", 
    format: "^[0-9]{13}$", 
    maxLength: 13, 
    minLength: 13, 
    isRequired: true 
  },
  { 
    name: "Pasaporte", 
    countryId: "GT", 
    format: "^[A-Z0-9]{6,9}$", 
    maxLength: 9, 
    minLength: 6, 
    isRequired: false 
  }
];
```

#### 15. 💍 **Estados Civiles** ❌
```typescript
interface MaritalStatus {
  id: string;
  name: string;          // "Soltero(a)", "Casado(a)", "Divorciado(a)", "Viudo(a)"
  allowsSpouse: boolean; // Permite registrar cónyuge
  legalImplications: string; // Implicaciones legales para consentimientos
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Ejemplos:
const maritalStatuses = [
  { name: "Soltero(a)", allowsSpouse: false },
  { name: "Casado(a)", allowsSpouse: true },
  { name: "Unión de hecho", allowsSpouse: true },
  { name: "Divorciado(a)", allowsSpouse: false },
  { name: "Viudo(a)", allowsSpouse: false },
  { name: "Separado(a)", allowsSpouse: false }
];
```

---

## 🟡 **FASE 2: SERVICIOS MÉDICOS Y SEGUROS (7 catálogos)**

### **Objetivo: Facturación Guatemala + Seguros Médicos + Integración CIE-11**

#### 16. 🏢 **Empresas/NITs** ✅
```typescript
interface Company {
  id: string;
  businessName: string;  // Razón social
  commercialName: string; // Nombre comercial
  nit: string;           // NIT para facturación
  address: string;
  phone: string;
  email: string;
  website: string;
  legalRepresentative: string;
  activityType: string;  // Tipo de actividad económica
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// ✅ API: /api/catalogs/companies
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 10 empresas modelo
// ✅ Funcionalidad: CRUD completo con validación de NIT guatemalteco
```

#### 17. 💊 **Servicios Médicos** ✅
```typescript
interface MedicalService {
  id: string;
  name: string;          // "Consulta General", "Procedimiento Menor", "Examen Físico"
  description: string;   // Descripción detallada del servicio
  code: string;          // Código interno del servicio (opcional)
  category: string;      // "Consulta", "Procedimiento", "Emergencia", "Preventivo", "Diagnóstico"
  basePrice: number;     // Precio base sugerido
  currency: string;      // "GTQ", "USD"
  duration: number;      // Duración estimada en minutos
  requiresEquipment: boolean;    // Requiere equipo especial (LEGACY - usar tags)
  requiresSpecialist: boolean;   // Requiere especialista (LEGACY - usar tags)
  tags?: ServiceTag[];   // Sistema de etiquetas flexible
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// 🏷️ NUEVO: Sistema de Tags para Servicios
interface ServiceTag {
  id: string;
  name: string;          // "Requiere Equipo", "Pediatría", "Ambulatorio"
  color: string;         // "red", "green", "blue" para UI
  category: string;      // "requirement", "specialty", "type", "duration", "general"
}

// ✅ API: /api/catalogs/medical-services
// ✅ API Tags: /api/catalogs/service-tags
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Seeded con 15 servicios médicos en 5 categorías + 20 tags iniciales
// ✅ Funcionalidad: CRUD completo + filtros avanzados + sistema de tags flexible
// ✅ Características: 
//    - Precios en GTQ/USD
//    - Sistema de tags reemplaza características booleanas
//    - Tags categorizados y con colores
//    - Relación muchos-a-muchos con servicios

// Categorías implementadas:
const categories = [
  "Consulta",      // Consultas médicas generales y especializadas
  "Procedimiento", // Procedimientos menores y curaciones
  "Emergencia",    // Atención de urgencias
  "Preventivo",    // Controles, vacunas, evaluaciones
  "Diagnóstico"    // Pruebas y exámenes diagnósticos
];

// Servicios de ejemplo implementados:
const serviceExamples = [
  // Consultas
  { name: "Consulta General Pediátrica", category: "Consulta", price: "Q250.00", duration: 30 },
  { name: "Consulta Cardiológica Pediátrica", category: "Consulta", price: "Q400.00", duration: 45, requiresSpecialist: true },
  { name: "Teleconsulta", category: "Consulta", price: "Q180.00", duration: 25 },
  
  // Procedimientos
  { name: "Curaciones Menores", category: "Procedimiento", price: "Q150.00", duration: 15, requiresEquipment: true },
  { name: "Nebulizaciones", category: "Procedimiento", price: "Q75.00", duration: 20, requiresEquipment: true },
  
  // Preventivos
  { name: "Control de Niño Sano", category: "Preventivo", price: "Q200.00", duration: 30 },
  { name: "Vacunación", category: "Preventivo", price: "Q50.00", duration: 10 },
  
  // Diagnósticos
  { name: "Prueba Rápida COVID-19", category: "Diagnóstico", price: "Q80.00", duration: 15, requiresEquipment: true },
  { name: "Examen Auditivo", category: "Diagnóstico", price: "Q120.00", duration: 20, requiresEquipment: true }
];
```

#### 18. 💰 **Precios por Médico** ✅
```typescript
interface DoctorServicePrice {
  id: string;
  doctorId: string;         // 👨‍⚕️ Referencia al médico
  serviceId: string;        // 📋 Referencia al servicio médico
  customPrice: number;      // 💰 Precio personalizado por médico
  currency: string;         // "GTQ", "USD" (default: GTQ)
  isActive: boolean;        // Si este precio está activo
  effectiveFrom: Date;      // 📅 Fecha desde cuando aplica
  effectiveUntil?: Date;    // 📅 Fecha hasta cuando aplica (opcional)
  notes?: string;           // Notas adicionales sobre el precio
  createdAt: Date;
  updatedAt: Date;
  
  // Datos relacionados (para vistas)
  doctorName: string;
  doctorLastName: string;
  doctorEmail: string;
  serviceName: string;
  serviceCategory: string;
  serviceBasePrice: number; // Precio base del servicio
}

// ✅ API: /api/catalogs/doctor-service-prices
// ✅ Estado: Implementado con PostgreSQL + Drizzle ORM
// ✅ Data: Tabla con restricciones únicas y relaciones
// ✅ Funcionalidad: CRUD completo + lógica de selección de precios
// ✅ Lógica de Negocio: 
//     - Solo un precio activo por médico-servicio
//     - Precio personalizado sobrescribe precio base
//     - Control de fechas de vigencia
//     - Eliminación lógica y física
// ✅ UI: CRUD completo siguiendo estándares del sistema
// ✅ Endpoints Adicionales:
//     - /api/services/pricing - Obtener precio efectivo
//     - /api/services/pricing/bulk - Precios múltiples
//     - /api/catalogs/doctor-service-prices/doctors - Lista de médicos
```

**Lógica de Selección de Precios:**
```typescript
// Algoritmo de selección de precio efectivo:
// 1. ¿Existe precio personalizado activo para este médico + servicio?
//    → SÍ: Usar precio personalizado
//    → NO: Usar precio base del servicio
// 2. Verificar vigencia del precio personalizado
// 3. Considerar moneda y fecha de consulta
```

#### 19. 🏥 **Aseguradoras** ❌
```typescript
interface InsuranceProvider {
  id: string;
  name: string;              // "RPN", "El Roble", "Seguros G&T"
  code: string;              // "RPN", "ROBLE", "GT"
  nit: string;               // NIT de la aseguradora
  contactInfo: {
    phone: string;
    email: string;
    address: string;
  };
  billingAddress: string;
  paymentTerms: number;      // Días para pago (30, 45, 60)
  requiresConsolidation: boolean; // Facturas consolidadas
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 20. 📋 **Planes de Cobertura por Aseguradora** ❌
```typescript
interface InsuranceCoveragePlan {
  id: string;
  insuranceProviderId: string;
  serviceId: string;
  
  // Configuración del copago del paciente
  patientCopayType: 'percentage' | 'fixed_amount';
  patientCopayValue: number;     // 30 (%) o 50 (Q)
  
  // Configuración de cobertura del seguro
  insuranceCoverageType: 'percentage' | 'fixed_amount' | 'remaining';
  insuranceCoverageValue: number; // 70 (%) o monto fijo
  
  // Límites y condiciones
  maxCoverageAmount: number;     // Máximo que cubre el seguro
  minPatientCopay: number;       // Mínimo que debe pagar paciente
  requiresPreauthorization: boolean;
  
  // Vigencia
  effectiveDate: Date;
  expiryDate: Date;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Ejemplos de configuración:
const coverageExamples = [
  // RPN: Paciente 30%, Seguro 70%
  {
    insuranceProvider: "RPN",
    service: "Consulta General",
    patientCopayType: "percentage",
    patientCopayValue: 30,
    insuranceCoverageValue: 70,
    maxCoverageAmount: 1000
  },
  
  // El Roble: Paciente 40%, Seguro 60%
  {
    insuranceProvider: "El Roble", 
    service: "Consulta General",
    patientCopayType: "percentage",
    patientCopayValue: 40,
    insuranceCoverageValue: 60,
    maxCoverageAmount: 800
  }
];
```

#### 21. 💊 **Medicamentos** ✅
```typescript
interface Medication {
  id: string;
  name: string;               // "Paracetamol", "Amoxicilina"
  genericName: string;        // Nombre genérico
  brandName: string;          // Nombre comercial
  activeIngredient: string;   // Principio activo
  dosageForm: string;         // "Tableta", "Jarabe", "Inyección"
  strength: string;           // "500mg", "250mg/5ml"
  therapeuticClass: string;   // Clase terapéutica
  indication: string;         // Indicaciones principales
  contraindications: string[];// Contraindicaciones
  sideEffects: string[];      // Efectos secundarios comunes
  dosageInstructions: string; // Instrucciones de dosificación
  requiresPrescription: boolean;
  isControlled: boolean;      // Medicamento controlado
  manufacturer: string;       // Laboratorio fabricante
  barcode: string;           // Código de barras
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Integración con recetas médicas
interface PrescriptionMedication {
  id: string;
  prescriptionId: string;
  medicationId: string;
  dosage: string;           // "1 tableta cada 8 horas"
  duration: string;         // "por 7 días"
  quantity: number;         // Cantidad a dispensar
  instructions: string;     // Instrucciones específicas
}
```

#### 22. 🩺 **Integración CIE-11 (API Externa)** ❌
```typescript
// En lugar de un catálogo local, integración con API CIE-11
interface CIE11Integration {
  id: string;
  searchTerm: string;       // Término de búsqueda
  cie11Code: string;        // Código CIE-11 obtenido de la API
  title: string;            // Título del diagnóstico
  definition: string;       // Definición desde CIE-11
  inclusions: string[];     // Incluye (desde API)
  exclusions: string[];     // Excluye (desde API)
  category: string;         // Categoría principal
  lastUpdated: Date;        // Última actualización desde API
  consultoryId: string;     // Para cache por consultorio
  isActive: boolean;
}

// Configuración de conexión CIE-11
interface CIE11Config {
  apiEndpoint: string;      // "https://id.who.int/icd/release/11/2024-01"
  clientId: string;         // Credenciales WHO
  clientSecret: string;     // Credenciales WHO
  language: string;         // "es" para español
  cacheTimeout: number;     // Tiempo de cache en horas
  fallbackMode: boolean;    // Modo offline con cache
}

// Funciones de integración
interface CIE11Service {
  search(term: string): Promise<CIE11Result[]>;
  getByCode(code: string): Promise<CIE11Detail>;
  cacheResult(result: CIE11Detail): Promise<void>;
  getFromCache(code: string): Promise<CIE11Detail | null>;
}

// Uso en consultas médicas
interface PatientDiagnosis {
  id: string;
  consultationId: string;
  patientId: string;
  cie11Code: string;        // Código obtenido de la API
  diagnosisTitle: string;   // Título del diagnóstico
  diagnosisType: 'primary' | 'secondary' | 'differential';
  notes: string;            // Notas del médico
  diagnosisDate: Date;
  doctorId: string;
  isActive: boolean;
}
```

#### 23. 💳 **Formas de Pago** ❌
```typescript
interface PaymentMethod {
  id: string;
  name: string;          // "Efectivo", "Tarjeta", "Transferencia"
  type: 'cash' | 'card' | 'bank' | 'digital' | 'credit';
  requiresReference: boolean;
  processingFee: number; // Comisión de procesamiento
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

---

## 🟢 **FASE 3: CATÁLOGOS AVANZADOS (7 catálogos)**

### **Objetivo: Funcionalidades Opcionales y Empresariales**

#### 24. 🎫 **Membresías** ❌
```typescript
interface Membership {
  id: string;
  name: string;          // "Básica", "Premium", "VIP"
  description: string;
  price: number;
  currency: string;
  duration: number;      // Duración en meses
  benefits: string[];    // Lista de beneficios
  discountPercentage: number;
  creditAmount: number;  // Créditos incluidos
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 25. 🏦 **Bancos** ❌
```typescript
interface Bank {
  id: string;
  name: string;          // "Banco Industrial", "Banrural"
  code: string;          // Código bancario
  swiftCode: string;     // Para transferencias internacionales
  countryId: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 26. 🤒 **Padecimientos/Síntomas** ✅
```typescript
interface Condition {
  id: string;
  name: string;          // "Dolor de cabeza", "Fiebre", "Náuseas"
  category: string;      // "Síntomas", "Signos", "Síndromes"
  icdCode: string;       // Código CIE-11
  isSymptom: boolean;
  description: string;
  commonCauses: string[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 27. 🗺️ **Zonas** ❌
```typescript
interface Zone {
  id: string;
  name: string;          // "Zona 1", "Zona 10", "Zona Centro"
  municipalityId: string; // Relación con municipio
  description: string;   // Descripción de la zona
  postalCode: string;    // Código postal si aplica
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 28. 🏘️ **Colonias** ❌
```typescript
interface Colony {
  id: string;
  name: string;          // "La Aurora", "El Cerrito", "Las Flores"
  zoneId: string;        // Relación con zona
  municipalityId: string; // Relación directa con municipio
  description: string;
  postalCode: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

#### 29. ⛪ **Religiones** ✅
```typescript
interface Religion {
  id: string;
  name: string;          // "Católica", "Evangélica", "Otra", "Ninguna"
  category: string;      // "Cristiana", "Otra", "No religiosa"
  description: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Ejemplos para Guatemala:
const religions = [
  { name: "Católica", category: "Cristiana" },
  { name: "Evangélica", category: "Cristiana" },
  { name: "Adventista", category: "Cristiana" },
  { name: "Testigo de Jehová", category: "Cristiana" },
  { name: "Mormón", category: "Cristiana" },
  { name: "Otra religión", category: "Otra" },
  { name: "No religiosa", category: "No religiosa" },
  { name: "Prefiere no decir", category: "No especificada" }
];
```

#### 30. 🏷️ **Tipos de Documento** ❌
```typescript
interface DocumentType {
  id: string;
  name: string;          // "DPI", "Pasaporte", "Cédula"
  countryId: string;
  format: string;        // Formato de validación
  isRequired: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}
```

---

## 🎯 **IMPORTANCIA ESTRATÉGICA DE CADA CATÁLOGO**

### **📊 Medios/Origen de Pacientes - Análisis Estratégico**

#### **En Onboarding:**
```typescript
// Pregunta obligatoria en formulario de paciente/encargado
const onboardingField = {
  field: "mediaSourceId",
  label: "¿Cómo nos conoció?",
  type: "select",
  required: true,
  placeholder: "Seleccione cómo nos conoció...",
  options: "FROM media_sources WHERE active = true ORDER BY category, name"
};
```

#### **Reportes Estratégicos:**
```sql
-- 1. Pacientes por medio en el último mes
SELECT ms.name, ms.category, COUNT(*) as pacientes,
       ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as porcentaje
FROM users u 
JOIN media_sources ms ON u.media_source_id = ms.id
WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 1 MONTH)
  AND u.role IN ('patient', 'guardian')
GROUP BY ms.id, ms.name, ms.category
ORDER BY pacientes DESC;

-- 2. ROI por medio (si tienen costo)
SELECT ms.name, ms.cost, COUNT(*) as pacientes,
       (COUNT(*) * 200 - ms.cost) as roi_estimado -- Asumiendo Q200 valor promedio
FROM media_sources ms
LEFT JOIN users u ON u.media_source_id = ms.id
WHERE ms.cost > 0
GROUP BY ms.id, ms.name, ms.cost;

-- 3. Efectividad de canales digitales vs tradicionales
SELECT ms.type,
       COUNT(*) as pacientes,
       ROUND(AVG(ms.cost), 2) as costo_promedio
FROM media_sources ms
LEFT JOIN users u ON u.media_source_id = ms.id
GROUP BY ms.type
ORDER BY pacientes DESC;
```

### **💰 Servicios + Seguros - Facturación Guatemala**

#### **Lógica de Facturación con Seguros:**
```typescript
async function calculateServiceBilling(
  serviceId: string,
  servicePrice: number,
  patientInsuranceId?: string
) {
  const billing = [];

  if (patientInsuranceId) {
    // Buscar plan específico para esta aseguradora + servicio
    const coveragePlan = await getCoveragePlan(patientInsuranceId, serviceId);

    if (coveragePlan) {
      let patientAmount = 0;
      let insuranceAmount = 0;

      // Calcular copago del paciente
      if (coveragePlan.patientCopayType === 'percentage') {
        patientAmount = servicePrice * (coveragePlan.patientCopayValue / 100);
      } else {
        patientAmount = coveragePlan.patientCopayValue; // Monto fijo
      }

      // Aplicar mínimo del paciente
      if (patientAmount < coveragePlan.minPatientCopay) {
        patientAmount = coveragePlan.minPatientCopay;
      }

      // Calcular cobertura del seguro
      insuranceAmount = servicePrice - patientAmount;

      // Aplicar máximo de cobertura
      if (insuranceAmount > coveragePlan.maxCoverageAmount) {
        insuranceAmount = coveragePlan.maxCoverageAmount;
        patientAmount = servicePrice - insuranceAmount;
      }

      // Generar 2 facturas
      billing.push({
        recipient_type: 'patient',
        amount: patientAmount,
        company_id: await getServiceCompany(serviceId)
      });

      billing.push({
        recipient_type: 'insurance',
        recipient_id: patientInsuranceId,
        amount: insuranceAmount,
        company_id: await getServiceCompany(serviceId),
        requires_consolidation: coveragePlan.requiresConsolidation
      });
    }
  } else {
    // Sin seguro - paciente paga todo
    billing.push({
      recipient_type: 'patient', 
      amount: servicePrice,
      company_id: await getServiceCompany(serviceId)
    });
  }

  return billing;
}
```

---

## 🎨 **DISEÑO DE INTERFAZ**

### **Página Principal: Lista de Catálogos por Fases**
```typescript
const CatalogsMainPage = () => {
  const [activePhase, setActivePhase] = useState('phase1');
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Gestión de Catálogos</h1>
          <p className="text-gray-600">Administra catálogos por fases de implementación</p>
        </div>
      </div>

      {/* Navegación por Fases */}
      <div className="flex space-x-4 border-b">
        <button 
          className={`pb-2 px-4 ${activePhase === 'phase1' ? 'border-b-2 border-blue-500' : ''}`}
          onClick={() => setActivePhase('phase1')}
        >
          🚀 Fase 1: Críticos (12)
        </button>
        <button 
          className={`pb-2 px-4 ${activePhase === 'phase2' ? 'border-b-2 border-blue-500' : ''}`}
          onClick={() => setActivePhase('phase2')}
        >
          🟡 Fase 2: Servicios (6)
        </button>
        <button 
          className={`pb-2 px-4 ${activePhase === 'phase3' ? 'border-b-2 border-blue-500' : ''}`}
          onClick={() => setActivePhase('phase3')}
        >
          🟢 Fase 3: Avanzados (4)
        </button>
      </div>

      {/* Catálogos por Fase */}
      <CatalogsByPhase phase={activePhase} />
    </div>
  );
};
```

### **Navegación de Catálogos Fase 1**
```typescript
const Phase1Catalogs = () => {
  const catalogs = [
    // ✅ Implementados
    { id: 'countries', name: 'Países', icon: '🌍', status: 'implemented', count: 3 },
    { id: 'departments', name: 'Departamentos', icon: '🏛️', status: 'implemented', count: 22 },
    { id: 'municipalities', name: 'Municipios', icon: '🏘️', status: 'implemented', count: 340 },
    { id: 'occupations', name: 'Ocupaciones', icon: '💼', status: 'implemented', count: 45 },
    { id: 'relationships', name: 'Parentescos', icon: '👨‍👩‍👧‍👦', status: 'implemented', count: 12 },
    { id: 'specialties', name: 'Especialidades', icon: '🩺', status: 'implemented', count: 20 },
    
    // ❌ Pendientes
    { id: 'currencies', name: 'Monedas', icon: '💰', status: 'pending', count: 0 },
    { id: 'consultories', name: 'Consultorios', icon: '🏥', status: 'pending', count: 0 },
    { id: 'activities', name: 'Tipos de Actividad', icon: '📅', status: 'pending', count: 0 },
    { id: 'pathological', name: 'Ant. Patológicos', icon: '🔬', status: 'pending', count: 0 },
    { id: 'nonPathological', name: 'Ant. No Patológicos', icon: '🚭', status: 'pending', count: 0 },
    { id: 'media', name: 'Medios/Origen', icon: '📢', status: 'pending', count: 0 },
  ];

  return (
    <div className="bg-white rounded-lg shadow">
      <div className="p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Catálogos Fase 1: Funcionalidad Inmediata</h2>
          <Badge variant="outline">6/12 Implementados</Badge>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {catalogs.map((catalog) => (
            <Card 
              key={catalog.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                catalog.status === 'implemented' ? 'border-green-200 bg-green-50' : 
                'border-orange-200 bg-orange-50'
              }`}
              onClick={() => setActiveCatalog(catalog.id)}
            >
              <CardContent className="p-4 text-center">
                <div className="text-2xl mb-2">{catalog.icon}</div>
                <h3 className="font-medium text-sm">{catalog.name}</h3>
                <div className="flex justify-between items-center mt-2">
                  <Badge variant={catalog.status === 'implemented' ? 'default' : 'secondary'}>
                    {catalog.count}
                  </Badge>
                  {catalog.status === 'implemented' ? 
                    <Badge className="bg-green-500">✅</Badge> : 
                    <Badge variant="outline">❌</Badge>
                  }
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
};
```

---

## 🔧 **FUNCIONALIDADES ESPECÍFICAS**

### **1. Gestión de Medios con Analytics**
```typescript
const MediaSourceManagement = () => {
  const [analytics, setAnalytics] = useState(null);
  
  const generateAnalytics = async () => {
    const response = await fetch('/api/admin/catalogs/media/analytics');
    const data = await response.json();
    setAnalytics(data);
  };
  
  return (
    <div className="space-y-6">
      {/* CRUD de Medios */}
      <MediaSourceTable />
      
      {/* Analytics de Medios */}
      <Card>
        <CardHeader>
          <CardTitle>📊 Análisis de Origen de Pacientes</CardTitle>
          <Button onClick={generateAnalytics}>Generar Reporte</Button>
        </CardHeader>
        <CardContent>
          {analytics && (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 p-4 rounded">
                <h3 className="font-semibold">Canales Digitales</h3>
                <p className="text-2xl font-bold">{analytics.digital}%</p>
              </div>
              <div className="bg-green-50 p-4 rounded">
                <h3 className="font-semibold">Referidos</h3>
                <p className="text-2xl font-bold">{analytics.referrals}%</p>
              </div>
              <div className="bg-orange-50 p-4 rounded">
                <h3 className="font-semibold">Tradicionales</h3>
                <p className="text-2xl font-bold">{analytics.traditional}%</p>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
```

### **2. Gestión de Seguros con Calculadora**
```typescript
const InsuranceManagement = () => {
  const [selectedInsurance, setSelectedInsurance] = useState('');
  const [selectedService, setSelectedService] = useState('');
  const [servicePrice, setServicePrice] = useState(300);
  
  const calculateCoverage = async () => {
    const response = await fetch('/api/admin/catalogs/insurance/calculate', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        insuranceId: selectedInsurance,
        serviceId: selectedService, 
        servicePrice: servicePrice
      })
    });
    
    const billing = await response.json();
    return billing;
  };
  
  return (
    <div className="space-y-6">
      {/* Configuración de Planes */}
      <InsurancePlansTable />
      
      {/* Calculadora de Cobertura */}
      <Card>
        <CardHeader>
          <CardTitle>🧮 Calculadora de Cobertura</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div>
              <Label>Aseguradora</Label>
              <Select value={selectedInsurance} onValueChange={setSelectedInsurance}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar aseguradora" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rpn">RPN</SelectItem>
                  <SelectItem value="roble">El Roble</SelectItem>
                  <SelectItem value="gt">Seguros G&T</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Servicio</Label>
              <Select value={selectedService} onValueChange={setSelectedService}>
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar servicio" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="consulta">Consulta General</SelectItem>
                  <SelectItem value="procedimiento">Procedimiento</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <Label>Precio del Servicio</Label>
              <Input 
                type="number" 
                value={servicePrice} 
                onChange={(e) => setServicePrice(Number(e.target.value))}
              />
            </div>
          </div>
          
          <Button onClick={calculateCoverage}>Calcular Cobertura</Button>
        </CardContent>
      </Card>
    </div>
  );
};
```

---

## 🔄 **API ENDPOINTS POR FASES**

### **Fase 1: APIs Básicas**
```typescript
// /api/admin/catalogs/[catalogType]/route.ts
export async function GET(request: NextRequest, { params }: { params: { catalogType: string } }) {
  const { catalogType } = params;
  
  // Validar que sea un catálogo de Fase 1
  const phase1Catalogs = [
    'countries', 'departments', 'municipalities', 'occupations', 
    'relationships', 'specialties', 'currencies', 'consultories',
    'activities', 'pathological', 'non-pathological', 'media'
  ];
  
  if (!phase1Catalogs.includes(catalogType)) {
    return NextResponse.json({ error: 'Catálogo no disponible en Fase 1' }, { status: 400 });
  }
  
  // Continuar con lógica normal...
}
```

### **Fase 2: APIs de Servicios y Seguros**
```typescript
// /api/admin/catalogs/insurance/calculate/route.ts
export async function POST(request: NextRequest) {
  const { insuranceId, serviceId, servicePrice } = await request.json();
  
  try {
    const billing = await calculateServiceBilling(serviceId, servicePrice, insuranceId);
    return NextResponse.json({ billing });
  } catch (error) {
    return NextResponse.json({ error: 'Error calculando cobertura' }, { status: 500 });
  }
}

// /api/admin/catalogs/media/analytics/route.ts
export async function GET() {
  try {
    const analytics = await db.query(`
      SELECT 
        ms.type,
        COUNT(*) as count,
        ROUND(COUNT(*) * 100.0 / SUM(COUNT(*)) OVER(), 2) as percentage
      FROM users u
      JOIN media_sources ms ON u.media_source_id = ms.id
      WHERE u.created_at >= DATE_SUB(NOW(), INTERVAL 3 MONTH)
      GROUP BY ms.type
    `);
    
    return NextResponse.json({ analytics });
  } catch (error) {
    return NextResponse.json({ error: 'Error generando analytics' }, { status: 500 });
  }
}
```

---

## 📊 **PROGRESO Y MÉTRICAS**

### **Estado Actual del Proyecto**
```typescript
const projectStatus = {
  fase1: {
    total: 15,
    implemented: 9,        // ⬆️ ACTUALIZADO: +3 catálogos
    pending: 6,           // ⬇️ ACTUALIZADO: -3 catálogos
    progress: '60%',      // ⬆️ ACTUALIZADO: +20%
    priority: 'CRÍTICO'
  },
  fase2: {
    total: 8,
    implemented: 0,
    pending: 8,
    progress: '0%',
    priority: 'ALTO'
  },
  fase3: {
    total: 7,
    implemented: 0,
    pending: 7,
    progress: '0%',
    priority: 'MEDIO'
  },
  
  overall: {
    total: 30,
    implemented: 9,       // ⬆️ ACTUALIZADO: +3 catálogos
    pending: 21,         // ⬇️ ACTUALIZADO: -3 catálogos
    progress: '30%'      // ⬆️ ACTUALIZADO: +10%
  }
};

// 🎉 LOGROS RECIENTES:
// - ✅ Monedas (currencies): API + 3 monedas iniciales
// - ✅ Tipos de Actividad (activity-types): API + 10 tipos predefinidos  
// - ✅ Medios/Origen (media-sources): API + 15 medios + Analytics ROI
```

### **Próximos Pasos Críticos**
1. **🔄 Completar Fase 1** - 6 catálogos pendientes (antes eran 9)
   - ✅ ~~currencies~~ - **COMPLETADO**
   - ✅ ~~activities~~ - **COMPLETADO** 
   - ✅ ~~media~~ - **COMPLETADO**
   - ❌ consultories, pathological, non-pathological, education_levels, document_types, marital_status
2. **🔄 Integrar con Base de Datos** - Migrar APIs mock a PostgreSQL con Drizzle
3. **🔄 Implementar Fase 2** - Servicios, seguros y CIE-11 para facturación médica
4. **🔍 Investigar CIE-11 API** - Conexión y integración con WHO
5. **⭐ Evaluar Fase 3** - Según necesidades reales del consultorio

---

## 🎯 **RESUMEN EJECUTIVO**

### **Catálogos por Implementar en Orden de Prioridad:**

#### **🔴 PRIORIDAD MÁXIMA (Esta semana)**
1. **currencies** - Para configuración de consultorios
2. **activity_types** - Para habilitar sistema de citas
3. **media_sources** - Para completar onboarding con análisis estratégico

#### **🟡 PRIORIDAD ALTA (Próxima semana)**
4. **pathological_history** - Para expedientes clínicos
5. **non_pathological_history** - Para expedientes clínicos
6. **consultories** (ampliar) - Datos completos del consultorio
7. **education_levels** - Para perfiles completos de usuarios
8. **document_types** - Para validación de documentos
9. **marital_status** - Para información demográfica

#### **🟢 PRIORIDAD MEDIA (Después)**
10-17. **Fase 2**: Servicios médicos, seguros y CIE-11
18-24. **Fase 3**: Catálogos opcionales (zonas, colonias, religiones, etc.)

#### **🚀 NUEVAS FUNCIONALIDADES CLAVE**
- **Categorización Multinivel de Servicios**: Sistema configurable por admin para organizar servicios médicos
- **Integración CIE-11**: API externa para diagnósticos médicos sin catálogo local
- **Gestión de Medicamentos**: Catálogo completo para recetas médicas
- **Demografía Completa**: Educación, estado civil, religión para análisis estratégico

**Este documento define la implementación por fases de todos los 30 catálogos administrativos necesarios para el sistema médico pediátrico, priorizando funcionalidad inmediata, valor estratégico y cumplimiento de estándares médicos internacionales.**

---

## 🎉 **ESTADO ACTUAL DE IMPLEMENTACIÓN**

### **📱 Interfaces de Usuario Implementadas**

#### **1. Panel de Administrador** ✅
**Ubicación**: `/dashboard/admin/catalogs`
- ✅ Vista por fases con navegación por pestañas
- ✅ Estadísticas generales (30 catálogos, progreso actualizado)
- ✅ Cards de catálogos con estados (Implementado ✅ / Pendiente ❌)
- ✅ Vista de cards y lista intercambiable
- ✅ Progreso por fase con barras de progreso
- ✅ Próximos pasos recomendados por fase

#### **2. Panel de Médico** ✅
**Ubicación**: `/dashboard/doctor/catalogs`
- ✅ Vista de solo lectura para catálogos médicos relevantes
- ✅ Filtros por categoría (Médico, Demográfico, Geográfico)
- ✅ Información sobre limitaciones de acceso
- ✅ Funcionalidad de exportación de datos
- ✅ 6 catálogos disponibles para consulta

#### **3. Panel de Asistente** ✅
**Ubicación**: `/dashboard/assistant/catalogs`
- ✅ Vista con permisos configurables por admin
- ✅ Badges de permisos: "Edición" vs "Solo Lectura"
- ✅ Catálogos marcados como "Configurable" por admin
- ✅ Información detallada sobre tipos de permisos
- ✅ 9 catálogos con diferentes niveles de acceso

### **🎛️ Navegación en Sidebars Implementada**

#### **Administrador** ✅
- ✅ Opción "Catálogos" con icono `Database`
- ✅ Acceso completo a gestión de catálogos

#### **Doctor** ✅
- ✅ Opción "Catálogos" con icono `BookOpen`
- ✅ Acceso de solo lectura a catálogos relevantes

#### **Asistente** ✅
- ✅ Opción "Catálogos" con icono `BookOpen`
- ✅ Acceso configurable según permisos asignados

### **🔌 APIs Implementadas**

#### **1. API Principal de Catálogos** ✅
**Endpoint**: `/api/admin/catalogs`
- ✅ Metadatos de todos los 30 catálogos organizados por fases
- ✅ Control de acceso por roles (admin, doctor, assistant)
- ✅ Filtrado automático de catálogos según permisos
- ✅ Estadísticas y métricas de implementación
- ✅ Información de endpoints específicos por catálogo

#### **2. APIs de Catálogos Específicos Implementados** ✅

##### **💰 API de Monedas** ✅
**Endpoint**: `/api/catalogs/currencies`
- ✅ CRUD completo para administradores
- ✅ Datos iniciales: GTQ (predeterminado), USD, EUR
- ✅ Validaciones de tasa de cambio y unicidad
- ✅ Paginación y filtros de búsqueda

##### **📅 API de Tipos de Actividad** ✅
**Endpoint**: `/api/catalogs/activity-types`
- ✅ CRUD para admin y asistentes
- ✅ 10 tipos predefinidos: consulta, emergencia, procedimiento, etc.
- ✅ Categorías: medical, administrative, personal
- ✅ Configuración de duración, colores e iconos

##### **📢 API de Medios/Origen de Pacientes** ✅
**Endpoint**: `/api/catalogs/media-sources`
- ✅ CRUD para admin y asistentes
- ✅ 15 medios estratégicos: Facebook, Google, referidos, etc.
- ✅ Análisis de ROI y costos por canal
- ✅ Analytics de efectividad por tipo de medio

##### **🏢 API de Empresas/NITs** ✅
**Endpoint**: `/api/catalogs/companies`
- ✅ CRUD completo para administradores
- ✅ Validación de NIT guatemalteco con dígito verificador
- ✅ 10 empresas modelo seeded
- ✅ Paginación y filtros de búsqueda

##### **💊 API de Servicios Médicos** ✅
**Endpoint**: `/api/catalogs/medical-services`
- ✅ CRUD completo para administradores
- ✅ 15 servicios médicos en 5 categorías
- ✅ Sistema de tags flexible (reemplaza checkboxes de características)
- ✅ Filtros por categoría, tags y estado
- ✅ Precios configurables en GTQ/USD
- ✅ Códigos internos de servicio
- ✅ **NUEVO**: Sistema de etiquetas con 20 tags iniciales

##### **🏷️ API de Tags de Servicios** ✅
**Endpoint**: `/api/catalogs/service-tags`
- ✅ Gestión de tags disponibles para servicios
- ✅ 20 tags iniciales en 5 categorías:
  - **Requerimientos**: Requiere Equipo, Requiere Especialista, Requiere Anestesia, Requiere Ayuno
  - **Especialidades**: Pediatría, Geriatría, Ginecología, Cardiología
  - **Tipo de Servicio**: Ambulatorio, Hospitalario, Domiciliario, Telemedicina
  - **Duración**: Consulta Rápida, Estándar, Extendida
  - **General**: Urgente, Preventivo, Seguimiento, Primera Vez, Control
- ✅ Tags con colores y categorías para mejor organización

### **🎯 Métricas de Progreso Actual**

```typescript
const implementationStatus = {
  // Estado actualizado
  fase1: {
    total: 15,
    implemented: 9,        // ⬆️ Aumentó de 6 a 9
    pending: 6,           // ⬇️ Disminuyó de 9 a 6
    progress: '60%',      // ⬆️ Aumentó de 40% a 60%
    priority: 'CRÍTICO'
  },
  
  overall: {
    total: 30,
    implemented: 9,       // ⬆️ Aumentó de 6 a 9
    pending: 21,         // ⬇️ Disminuyó de 24 a 21
    progress: '30%'      // ⬆️ Aumentó de 20% a 30%
  }
};
```

### **🔐 Sistema de Permisos Implementado**

#### **Control de Acceso por Rol**
- ✅ **Admin**: Acceso completo (CRUD) a todos los catálogos
- ✅ **Doctor**: Solo lectura a catálogos relevantes médicamente
- ✅ **Asistente**: Permisos configurables por catálogo (lectura/edición)

#### **Permisos Específicos por Catálogo**
```typescript
// Ejemplo de matriz de permisos implementada
const permissions = {
  'currencies': { admin: 'edit', doctor: 'read', assistant: 'read' },
  'activity-types': { admin: 'edit', doctor: 'read', assistant: 'edit' },
  'media-sources': { admin: 'edit', doctor: 'read', assistant: 'edit' },
  'specialties': { admin: 'edit', doctor: 'read', assistant: 'read' },
  // ... más catálogos
};
```

### **🚀 Funcionalidades Avanzadas Implementadas**

#### **1. Analytics de Medios/Origen** ✅
- ✅ Reportes de ROI por canal de marketing
- ✅ Análisis de efectividad digital vs tradicional
- ✅ Costos por adquisición de pacientes
- ✅ Métricas de tracking y conversión

#### **2. Sistema de Filtrado y Búsqueda** ✅
- ✅ Búsqueda por nombre y descripción
- ✅ Filtros por categoría y tipo
- ✅ Filtros por estado (activo/inactivo)
- ✅ Paginación configurable

#### **3. Validaciones de Negocio** ✅
- ✅ Solo una moneda predeterminada por consultorio
- ✅ Validación de códigos únicos
- ✅ Rangos de valores permitidos
- ✅ Formato de datos consistente

---

## 🔥 **LOGROS PRINCIPALES COMPLETADOS**

### **✅ Arquitectura Base Sólida**
- Sistema de catálogos completamente funcional
- Control de acceso por roles implementado
- APIs RESTful con validaciones completas
- Interfaz de usuario responsive y moderna

### **✅ Funcionalidad Estratégica**
- Sistema de análisis de origen de pacientes
- Gestión de tipos de actividad para citas
- Configuración de monedas para facturación
- Base para sistema de citas médicas

### **✅ Experiencia de Usuario**
- Navegación intuitiva por fases
- Visualización clara del progreso
- Acceso diferenciado por rol
- Feedback visual de estados

### **✅ Escalabilidad**
- Diseño preparado para 30 catálogos
- APIs extensibles para nuevas funcionalidades
- Sistema de permisos configurable
- Integración lista para CIE-11

---

## 🔄 **CAMBIO DE ARQUITECTURA - VISTA UNIFICADA**

### **Decisión de Diseño - Julio 2025**

**Problema Identificado**: 
- 30 catálogos distribuidos en múltiples páginas crean navegación compleja
- Dificultad para visualizar el estado general del sistema
- Indicadores de progreso dispersos e ineficientes

**Solución Implementada**: 
- Vista unificada en `/dashboard/admin/catalogs` con secciones organizadas
- Solo catálogos implementados y funcionales son visibles
- Indicadores mejorados con contadores de registros en tiempo real

### **Beneficios de la Nueva Arquitectura:**

#### **🎯 Experiencia de Usuario Mejorada**
- ✅ **Un solo punto de acceso** para todos los catálogos activos
- ✅ **Navegación eficiente** - menos clicks, acceso directo a CRUDs
- ✅ **Vista consolidada del progreso** - estado completo de un vistazo
- ✅ **Indicadores informativos** con contadores de registros

#### **📊 Organización por Secciones Lógicas**

**📍 Sección Geográfica**
- Países, Departamentos, Municipios

**🏥 Sección Médica** 
- Especialidades Médicas, Servicios Médicos, Medicamentos, Síntomas
- Antecedentes Patológicos y No Patológicos

**👥 Sección Demográfica**
- Ocupaciones, Religiones, Relaciones Familiares
- Niveles de Educación, Estados Civiles

**💼 Sección Administrativa**
- Monedas, Empresas/NITs, Tipos de Documento
- Precios por Médico, Tipos de Actividad, Medios de Origen

#### **🎨 Indicadores Visuales Mejorados**
- 🟢 **Con Datos** (X registros) - Verde con contador preciso
- 🟡 **Sin Datos** (CRUD listo) - Amarillo, listo para población inicial
- 🔵 **Funcional** - Azul para catálogos completamente operativos
- ❌ **Oculto** - Catálogos no implementados no aparecen en UI

### **Criterios de Visibilidad**

**✅ Catálogos Visibles en la Nueva UI:**
- API completamente funcional (GET, POST, PUT, DELETE)
- Esquema de base de datos implementado
- Datos iniciales disponibles (seed data)
- CRUD frontend completamente operativo

**❌ Catálogos Ocultos (No Aparecen en UI):**

**Fase 2 Pendientes:**
- Aseguradoras, Planes de Cobertura, CIE-11, Formas de Pago

**Fase 3 Pendientes:**
- Membresías, Bancos, Zonas, Colonias

**Nota**: Estos catálogos permanecen documentados pero no son visibles hasta completar su implementación.

### **Impacto en Métricas de Progreso**

**Antes**: Progreso basado en documentación (70%)
**Después**: Progreso basado en funcionalidad real visible para usuarios

Esta vista unificada **mejora significativamente la usabilidad** y proporciona una **experiencia administrativa más profesional y eficiente**.

---

## 🎯 **PRÓXIMOS PASOS INMEDIATOS**

### **✅ FASE 1 COMPLETADA AL 100%**
1. ✅ **education_levels** - Niveles de escolaridad - **MIGRADO A DRIZZLE**
2. ✅ **document_types** - Tipos de documento - **MIGRADO A DRIZZLE**
3. ✅ **marital_status** - Estados civiles - **MIGRADO A DRIZZLE**
4. ✅ **pathological_history** - Antecedentes patológicos - **MIGRADO A DRIZZLE**
5. ✅ **non_pathological_history** - Antecedentes no patológicos - **MIGRADO A DRIZZLE**
6. ✅ **consultories** - Ampliar datos de consultorios - **MIGRADO A DRIZZLE**

### **🟡 SIGUIENTE FASE - Comenzar Fase 2**
1. ✅ Crear esquemas Drizzle para nuevos catálogos - **COMPLETADO**
2. ✅ Migrar datos mock a base de datos PostgreSQL - **COMPLETADO**
3. ✅ Implementar relaciones entre catálogos - **COMPLETADO**
4. ✅ Optimizar queries y performance - **COMPLETADO**
5. **🔄 Probar todas las APIs** en el admin backend
6. **🔄 Comenzar Fase 2** con servicios médicos y seguros

---

## 🏷️ **SISTEMA DE TAGS - IMPLEMENTACIÓN Y FUTURAS MEJORAS**

### **✅ Implementación Actual**

#### **Base de Datos**
- **Tabla `service_tags`**: Almacena todos los tags disponibles
- **Tabla `medical_service_tags`**: Relación muchos-a-muchos con servicios
- **20 tags iniciales** categorizados y con colores

#### **Funcionalidad**
- **TagSelector Component**: Selector responsive que funciona en todas las resoluciones
- **API completa**: GET/POST para gestión de tags
- **Migración automática**: Script para convertir servicios existentes
- **Filtros legacy**: Los campos `requiresEquipment` y `requiresSpecialist` se mantienen por compatibilidad

### **🚀 Mejoras Futuras Sugeridas**

#### **1. CRUD de Tags (Corto plazo)**
```typescript
// Página administrativa para gestionar tags
/dashboard/admin/catalogs/service-tags
- Crear nuevos tags
- Editar tags existentes
- Activar/desactivar tags
- Reorganizar categorías
```

#### **2. Tags en Otros Catálogos (Mediano plazo)**
- Aplicar sistema de tags a **Pacientes** (ej: "VIP", "Alérgico", "Crónico")
- Tags para **Citas** (ej: "Urgente", "Control", "Primera vez")
- Tags para **Doctores** (ej: especialidades adicionales)

#### **3. Analytics de Tags (Largo plazo)**
- Reportes de servicios más utilizados por tag
- Análisis de rentabilidad por categoría de tags
- Tendencias de uso de servicios por tipo

#### **4. Búsqueda Avanzada**
- Filtrar servicios por múltiples tags
- Búsqueda de pacientes por tags asignados
- Combinaciones de tags para reportes

### **📝 Notas de Implementación**

1. **Compatibilidad**: Los campos booleanos originales se mantienen para no romper código existente
2. **Migración**: Script disponible en `/scripts/migrate-services-to-tags.ts`
3. **Extensibilidad**: El sistema está diseñado para ser reutilizable en cualquier entidad
4. **Performance**: Índices optimizados para búsquedas rápidas por tags