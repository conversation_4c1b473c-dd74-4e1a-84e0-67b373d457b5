import { db } from '@/db/drizzle';
import { user } from '@/db/schema';
import { eq } from 'drizzle-orm';
import { formatPhoneForStorage } from '@/lib/phone-utils';

/**
 * Script para migrar todos los teléfonos existentes al formato estándar
 * Formato objetivo: +502 5555-5555
 */
async function migratePhoneFormats() {
  console.log('🔧 INICIANDO MIGRACIÓN DE FORMATOS DE TELÉFONO');
  console.log('==============================================\n');

  try {
    // 1. Obtener todos los usuarios con teléfonos
    console.log('1️⃣ Obteniendo usuarios con teléfonos...');
    const users = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        emergencyPhone: user.emergencyPhone
      })
      .from(user)
      .where(
        // Usuarios que tengan al menos un teléfono
        // SQL: WHERE phone IS NOT NULL OR emergency_phone IS NOT NULL
      );
    
    console.log(`Encontrados ${users.length} usuarios con teléfonos\n`);

    // 2. Analizar formatos actuales
    console.log('2️⃣ Analizando formatos actuales...');
    const phoneStats = {
      totalPhones: 0,
      totalEmergencyPhones: 0,
      withCountryCode: 0,
      withoutCountryCode: 0,
      withSpaces: 0,
      withDashes: 0,
      alreadyStandard: 0,
      needsMigration: 0
    };

    const phonesToMigrate: Array<{
      userId: string;
      name: string;
      currentPhone?: string;
      newPhone?: string;
      currentEmergencyPhone?: string;
      newEmergencyPhone?: string;
    }> = [];

    for (const userRecord of users) {
      const userName = `${userRecord.firstName} ${userRecord.lastName}`;
      const migrationData = {
        userId: userRecord.id,
        name: userName,
        currentPhone: userRecord.phone,
        newPhone: undefined as string | undefined,
        currentEmergencyPhone: userRecord.emergencyPhone,
        newEmergencyPhone: undefined as string | undefined
      };

      // Analizar teléfono principal
      if (userRecord.phone) {
        phoneStats.totalPhones++;
        
        // Verificar si ya está en formato estándar
        const standardFormat = formatPhoneForStorage(userRecord.phone);
        if (userRecord.phone === standardFormat) {
          phoneStats.alreadyStandard++;
        } else {
          phoneStats.needsMigration++;
          migrationData.newPhone = standardFormat;
        }

        // Estadísticas de formato
        if (userRecord.phone.includes('+') || userRecord.phone.includes('502')) {
          phoneStats.withCountryCode++;
        } else {
          phoneStats.withoutCountryCode++;
        }
        
        if (userRecord.phone.includes(' ')) phoneStats.withSpaces++;
        if (userRecord.phone.includes('-')) phoneStats.withDashes++;
      }

      // Analizar teléfono de emergencia
      if (userRecord.emergencyPhone) {
        phoneStats.totalEmergencyPhones++;
        
        const standardFormat = formatPhoneForStorage(userRecord.emergencyPhone);
        if (userRecord.emergencyPhone !== standardFormat) {
          migrationData.newEmergencyPhone = standardFormat;
        }
      }

      // Solo agregar si necesita migración
      if (migrationData.newPhone || migrationData.newEmergencyPhone) {
        phonesToMigrate.push(migrationData);
      }
    }

    // 3. Mostrar estadísticas
    console.log('📊 ESTADÍSTICAS ACTUALES:');
    console.log(`📱 Total teléfonos principales: ${phoneStats.totalPhones}`);
    console.log(`🚨 Total teléfonos de emergencia: ${phoneStats.totalEmergencyPhones}`);
    console.log(`✅ Ya en formato estándar: ${phoneStats.alreadyStandard}`);
    console.log(`🔄 Necesitan migración: ${phoneStats.needsMigration}`);
    console.log(`📞 Con código país: ${phoneStats.withCountryCode}`);
    console.log(`📞 Sin código país: ${phoneStats.withoutCountryCode}`);
    console.log(`   Con espacios: ${phoneStats.withSpaces}`);
    console.log(`   Con guiones: ${phoneStats.withDashes}\n`);

    if (phonesToMigrate.length === 0) {
      console.log('🎉 ¡Todos los teléfonos ya están en formato estándar!');
      return;
    }

    // 4. Mostrar ejemplos de migración
    console.log('📋 EJEMPLOS DE MIGRACIÓN:');
    phonesToMigrate.slice(0, 5).forEach((item, index) => {
      console.log(`${index + 1}. ${item.name}:`);
      if (item.newPhone) {
        console.log(`   Teléfono: "${item.currentPhone}" → "${item.newPhone}"`);
      }
      if (item.newEmergencyPhone) {
        console.log(`   Emergencia: "${item.currentEmergencyPhone}" → "${item.newEmergencyPhone}"`);
      }
    });
    
    if (phonesToMigrate.length > 5) {
      console.log(`   ... y ${phonesToMigrate.length - 5} más\n`);
    } else {
      console.log('');
    }

    // 5. Confirmación antes de migrar
    console.log('⚠️  ADVERTENCIA: Esta operación actualizará los teléfonos en la base de datos.');
    console.log('⚠️  Asegúrate de tener un backup antes de continuar.');
    console.log('');
    console.log('Para continuar con la migración, descomenta la sección de migración en el script.\n');

    // MIGRACIÓN ACTUAL COMENTADA POR SEGURIDAD
    // Descomenta este bloque para ejecutar la migración real
    
    console.log('🚀 INICIANDO MIGRACIÓN...');
    let migratedCount = 0;
    
    for (const item of phonesToMigrate) {
      try {
        const updateData: any = {};
        
        if (item.newPhone) {
          updateData.phone = item.newPhone;
        }
        
        if (item.newEmergencyPhone) {
          updateData.emergencyPhone = item.newEmergencyPhone;
        }
        
        await db
          .update(user)
          .set(updateData)
          .where(eq(user.id, item.userId));
          
        migratedCount++;
        console.log(`✅ Migrado: ${item.name} (${migratedCount}/${phonesToMigrate.length})`);
        
      } catch (error) {
        console.error(`❌ Error migrando ${item.name}:`, error);
      }
    }
    
    console.log(`\n🎉 MIGRACIÓN COMPLETADA: ${migratedCount}/${phonesToMigrate.length} usuarios actualizados`);

  } catch (error) {
    console.error('❌ Error durante la migración:', error);
  } finally {
    process.exit(0);
  }
}

// Ejecutar migración
migratePhoneFormats();