import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { religions } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST /api/catalogs/religions/[id]/toggle-status - Activar/Desactivar religión
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    // Obtener la religión actual
    const currentReligion = await db
      .select()
      .from(religions)
      .where(eq(religions.id, id))
      .limit(1);

    if (!currentReligion.length) {
      return NextResponse.json(
        { error: 'Religión no encontrada' },
        { status: 404 }
      );
    }

    const religion = currentReligion[0];
    const newStatus = !religion.isActive;

    // Actualizar estado
    await db
      .update(religions)
      .set({
        isActive: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(religions.id, id));

    return NextResponse.json({
      success: true,
      message: `Religión ${newStatus ? 'activada' : 'desactivada'} exitosamente`,
      data: {
        id,
        isActive: newStatus,
      },
    });

  } catch (error: any) {
    console.error('Error toggling religion status:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}