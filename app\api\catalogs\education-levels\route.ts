import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { educationLevels } from '@/db/schema';
import { eq, and, count, asc, desc, ilike } from 'drizzle-orm';

// GET - Listar niveles de escolaridad
export async function GET(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const onlyActive = searchParams.get('active') === 'true';
    const orderBy = searchParams.get('orderBy') || 'order'; // 'order', 'name', 'createdAt'
    const orderDirection = searchParams.get('orderDirection') || 'asc';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Construir query con Drizzle
    let query = db.select().from(educationLevels);

    // Aplicar filtros
    const conditions = [];
    
    if (search) {
      conditions.push(
        ilike(educationLevels.name, `%${search}%`),
        ilike(educationLevels.description, `%${search}%`)
      );
    }

    if (onlyActive) {
      conditions.push(eq(educationLevels.isActive, true));
    }

    if (conditions.length > 0) {
      query = query.where(and(...conditions));
    }

    // Aplicar ordenamiento
    const orderColumn = orderBy === 'name' ? educationLevels.name : 
                       orderBy === 'createdAt' ? educationLevels.createdAt : 
                       educationLevels.order;
    
    query = query.orderBy(orderDirection === 'desc' ? desc(orderColumn) : asc(orderColumn));

    // Aplicar paginación
    query = query.limit(limit).offset(offset);

    // Ejecutar query principal
    const educationLevelsData = await query;

    // Obtener total para paginación
    const totalQuery = db.select({ count: count() }).from(educationLevels);
    if (conditions.length > 0) {
      totalQuery.where(and(...conditions));
    }
    const [{ count: total }] = await totalQuery;

    return NextResponse.json({
      data: educationLevelsData,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page * limit < total,
        hasPrev: page > 1
      },
      stats: {
        total,
        active: educationLevelsData.filter(level => level.isActive).length
      }
    });

  } catch (error) {
    console.error('Error obteniendo niveles de escolaridad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nuevo nivel de escolaridad (solo admin)
export async function POST(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden crear niveles de escolaridad' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { name, order, description, isActive } = body;

    // Validaciones
    if (!name || order === undefined) {
      return NextResponse.json(
        { error: 'Nombre y orden son requeridos' },
        { status: 400 }
      );
    }

    if (order < 0 || order > 99) {
      return NextResponse.json(
        { error: 'El orden debe estar entre 0 y 99' },
        { status: 400 }
      );
    }

    // Generar ID único
    const id = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 50);

    // Verificar que no exista un nivel con el mismo ID
    const existingLevel = await db.select().from(educationLevels).where(eq(educationLevels.id, id)).limit(1);
    if (existingLevel.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un nivel de escolaridad con este nombre' },
        { status: 400 }
      );
    }

    // Verificar que no exista un nivel con el mismo orden
    const existingOrder = await db.select().from(educationLevels).where(eq(educationLevels.order, order)).limit(1);
    if (existingOrder.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un nivel de escolaridad con este orden' },
        { status: 400 }
      );
    }

    const newEducationLevel = {
      id,
      name: name.trim(),
      order: parseInt(order),
      description: description?.trim() || '',
      isActive: isActive !== undefined ? Boolean(isActive) : true
    };

    // Insertar en base de datos
    const [insertedEducationLevel] = await db.insert(educationLevels).values(newEducationLevel).returning();

    return NextResponse.json({
      message: 'Nivel de escolaridad creado exitosamente',
      data: insertedEducationLevel
    });

  } catch (error) {
    console.error('Error creando nivel de escolaridad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT - Actualizar nivel de escolaridad (solo admin)
export async function PUT(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden actualizar niveles de escolaridad' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { id, name, order, description, isActive } = body;

    if (!id) {
      return NextResponse.json(
        { error: 'ID de nivel de escolaridad requerido' },
        { status: 400 }
      );
    }

    // Buscar nivel existente
    const existingLevel = await db.select().from(educationLevels).where(eq(educationLevels.id, id)).limit(1);
    if (existingLevel.length === 0) {
      return NextResponse.json(
        { error: 'Nivel de escolaridad no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones
    if (order !== undefined && (order < 0 || order > 99)) {
      return NextResponse.json(
        { error: 'El orden debe estar entre 0 y 99' },
        { status: 400 }
      );
    }

    // Verificar que no exista otro nivel con el mismo orden
    if (order !== undefined) {
      const existingOrder = await db.select().from(educationLevels)
        .where(and(eq(educationLevels.order, order), eq(educationLevels.id, id)))
        .limit(1);
      if (existingOrder.length > 0 && existingOrder[0].id !== id) {
        return NextResponse.json(
          { error: 'Ya existe un nivel de escolaridad con este orden' },
          { status: 400 }
        );
      }
    }

    const updateData = {
      ...(name && { name: name.trim() }),
      ...(order !== undefined && { order: parseInt(order) }),
      ...(description !== undefined && { description: description.trim() }),
      ...(isActive !== undefined && { isActive: Boolean(isActive) }),
      updatedAt: new Date()
    };

    // Actualizar en base de datos
    const [updatedEducationLevel] = await db.update(educationLevels)
      .set(updateData)
      .where(eq(educationLevels.id, id))
      .returning();

    return NextResponse.json({
      message: 'Nivel de escolaridad actualizado exitosamente',
      data: updatedEducationLevel
    });

  } catch (error) {
    console.error('Error actualizando nivel de escolaridad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE - Eliminar nivel de escolaridad (solo admin)
export async function DELETE(request: NextRequest) {
  try {
    const { userId, sessionClaims } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const userRole = sessionClaims?.metadata?.role;
    
    if (userRole !== 'admin') {
      return NextResponse.json(
        { error: 'Solo administradores pueden eliminar niveles de escolaridad' },
        { status: 403 }
      );
    }

    const searchParams = request.nextUrl.searchParams;
    const id = searchParams.get('id');
    const type = searchParams.get('type') || 'soft'; // 'soft' o 'hard'

    if (!id) {
      return NextResponse.json(
        { error: 'ID de nivel de escolaridad requerido' },
        { status: 400 }
      );
    }

    // Buscar nivel existente
    const existingLevel = await db.select().from(educationLevels).where(eq(educationLevels.id, id)).limit(1);
    if (existingLevel.length === 0) {
      return NextResponse.json(
        { error: 'Nivel de escolaridad no encontrado' },
        { status: 404 }
      );
    }

    if (type === 'hard') {
      // Eliminación física
      await db.delete(educationLevels).where(eq(educationLevels.id, id));
      
      return NextResponse.json({
        message: 'Nivel de escolaridad eliminado permanentemente'
      });
    } else {
      // Eliminación virtual (soft delete)
      const [updatedLevel] = await db.update(educationLevels)
        .set({ 
          isActive: false,
          updatedAt: new Date()
        })
        .where(eq(educationLevels.id, id))
        .returning();

      return NextResponse.json({
        message: 'Nivel de escolaridad desactivado exitosamente',
        data: updatedLevel
      });
    }

  } catch (error) {
    console.error('Error eliminando nivel de escolaridad:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}