import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

const isPublicRoute = createRouteMatcher([
  '/',
  '/blog(.*)',
  '/about',
  '/contact',
  '/api/payments/webhooks',
  '/pricing(.*)',
  '/privacy-policy',
  '/terms-of-service',
  '/success',
  '/onboarding(.*)',
  '/api/onboarding(.*)',
  '/api/catalogs(.*)',
  '/api/system(.*)',
  '/api/auth/sync',
  '/api/cron(.*)', // Endpoints de cron jobs (protegidos por CRON_SECRET)
  '/api/consultories', // Endpoint público para obtener consultorio activo
  '/appointments(.*)', // Páginas públicas de citas (cancelación, pre-checkin)
  '/api/appointments/(.*)/public', // API pública de citas
  '/api/appointments/(.*)/verify-code', // API verificación de código
  '/api/appointments/(.*)/cancel', // API cancelación pública
  '/api/appointments/by-code/(.*)', // API búsqueda por código público
  '/api/appointments/check-in', // API check-in público
  '/pre-checkin(.*)', // Páginas de pre-checkin
  '/api/pre-checkin(.*)', // API de pre-checkin
  '/check-in', // Página pública de check-in
]);

export default clerkMiddleware(async (auth, req) => {
  try {
    const { userId, sessionClaims } = await auth();
    
    // Obtener metadata de Clerk CORRECTAMENTE según documentación oficial
    const role = sessionClaims?.metadata?.role;
    const onboardingCompleted = sessionClaims?.metadata?.onboardingCompleted;
    const status = sessionClaims?.metadata?.status;
    
    // Solo logging mínimo para errores importantes
    // (Remover en producción si es necesario)
    
    // Verificar rutas
    const isOnboardingPage = req.nextUrl.pathname.startsWith('/onboarding');
    const isDashboardPage = req.nextUrl.pathname.startsWith('/dashboard');
    const isApiRoute = req.nextUrl.pathname.startsWith('/api/');
    
    // Si no está autenticado y trata de acceder a ruta protegida
    if (!userId && !isPublicRoute(req)) {
      const homeUrl = new URL('/', req.url);
      homeUrl.searchParams.set('redirect_url', req.url);
      return NextResponse.redirect(homeUrl);
    }

    // Para usuarios autenticados
    if (userId) {
      // Permitir acceso al API de sincronización siempre
      if (req.nextUrl.pathname === '/api/auth/sync') {
        return NextResponse.next();
      }
      
      // Permitir acceso a todas las rutas API
      if (isApiRoute) {
        return NextResponse.next();
      }

      
      // Si el usuario está activo con onboarding completo y tiene rol
      if (onboardingCompleted && role && status === 'active') {
        // Si está en onboarding pero ya completó, redirigir al dashboard
        if (isOnboardingPage) {
          const dashboardUrl = new URL(`/dashboard/${role}`, req.url);
          return NextResponse.redirect(dashboardUrl);
        }
        
        // Si está en la homepage, redirigir a dashboard
        if (req.nextUrl.pathname === '/') {
          const dashboardUrl = new URL(`/dashboard/${role}`, req.url);
          return NextResponse.redirect(dashboardUrl);
        }
        
        // Si está en /dashboard genérico, redirigir a su rol específico
        if (req.nextUrl.pathname === '/dashboard') {
          const dashboardUrl = new URL(`/dashboard/${role}`, req.url);
          return NextResponse.redirect(dashboardUrl);
        }
        
        return NextResponse.next();
      }
      
      // Si el usuario tiene onboarding completado pero está pendiente
      if (onboardingCompleted && status === 'pending') {
        // Si es una ruta pública, permitir acceso
        if (isPublicRoute(req)) {
          return NextResponse.next();
        }
        
        // Si trata de acceder a rutas protegidas, redirigir a página de pendiente
        if (req.nextUrl.pathname !== '/onboarding/pending') {
          const pendingUrl = new URL('/onboarding/pending', req.url);
          return NextResponse.redirect(pendingUrl);
        }
        
        return NextResponse.next();
      }
      
      // Si el usuario tiene onboarding completado pero está rechazado
      if (onboardingCompleted && status === 'rejected') {
        // Si es una ruta pública, permitir acceso
        if (isPublicRoute(req)) {
          return NextResponse.next();
        }
        
        // Si trata de acceder a rutas protegidas, redirigir a página de rechazo
        if (req.nextUrl.pathname !== '/onboarding/rejected') {
          const rejectedUrl = new URL('/onboarding/rejected', req.url);
          return NextResponse.redirect(rejectedUrl);
        }
        
        return NextResponse.next();
      }
      
      // Para usuarios sin onboarding completado o sin metadatos
      if (!onboardingCompleted || !role) {
        // Si está en página de pending, permitir (puede ser que Clerk aún no sincronice)
        if (req.nextUrl.pathname === '/onboarding/pending') {
          return NextResponse.next();
        }
        
        // Si está en la página principal o dashboard después de login, redirigir a onboarding
        if (req.nextUrl.pathname === '/' || req.nextUrl.pathname === '/dashboard') {
          const onboardingUrl = new URL('/onboarding', req.url);
          return NextResponse.redirect(onboardingUrl);
        }
        
        // Si no está en una página de onboarding, redirigir allí
        if (!isOnboardingPage) {
          const onboardingUrl = new URL('/onboarding', req.url);
          return NextResponse.redirect(onboardingUrl);
        }
        
        // Si ya está en onboarding, permitir continuar
        return NextResponse.next();
      }
    }

    return NextResponse.next();
    
  } catch (error) {
    console.error('❌ Error en middleware:', error);
    // En caso de error, permitir continuar
    return NextResponse.next();
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
  ],
};
