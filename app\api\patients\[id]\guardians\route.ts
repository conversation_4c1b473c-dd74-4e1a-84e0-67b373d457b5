import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles, guardianPatientRelations, patientInvitations } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { generateId } from '@/lib/utils';
import { sendEmail, emailTemplates } from '@/lib/email';
import crypto from 'crypto';

interface GuardianData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  relationship: string;
  isPrimary: boolean;
  canMakeDecisions: boolean;
  sendInvitation: boolean;
  patientId: string;
}

export async function POST(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;
    const { guardians }: { guardians: GuardianData[] } = await request.json();

    if (!guardians || guardians.length === 0) {
      return NextResponse.json(
        { error: 'Se requiere al menos un encargado' },
        { status: 400 }
      );
    }

    // Verificar que el paciente existe
    const patientData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        dateOfBirth: user.dateOfBirth
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (patientData.length === 0) {
      return NextResponse.json(
        { error: 'Paciente no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const patient = patientData[0];

    // Obtener información del doctor actual
    const doctorData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.clerkUserId, userId),
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (doctorData.length === 0) {
      return NextResponse.json(
        { error: 'Doctor no encontrado' },
        { status: 404 }
      );
    }

    const doctor = doctorData[0];
    const results = [];

    // Procesar cada encargado
    for (const guardianData of guardians) {
      try {
        // Verificar si ya existe un usuario con este email
        const existingUser = await db
          .select()
          .from(user)
          .where(eq(user.email, guardianData.email))
          .limit(1);

        let guardianUserId: string;

        if (existingUser.length > 0) {
          // Usuario existe - usar el ID existente
          guardianUserId = existingUser[0].id;
          
          // Verificar que tenga rol de guardian, si no crearlo
          const existingGuardianRole = await db
            .select()
            .from(userRoles)
            .where(
              and(
                eq(userRoles.userId, guardianUserId),
                eq(userRoles.role, 'guardian')
              )
            )
            .limit(1);

          if (existingGuardianRole.length === 0) {
            // Crear rol de guardian
            await db.insert(userRoles).values({
              id: generateId(),
              userId: guardianUserId,
              role: 'guardian',
              status: 'active',
              roleData: {
                createdByDoctor: doctor.id,
                createdForPatient: patientId
              },
              createdAt: new Date(),
              updatedAt: new Date()
            });
          }
        } else {
          // Usuario no existe - crear nuevo usuario básico
          const newUser = await db.insert(user).values({
            id: generateId(),
            firstName: guardianData.firstName,
            lastName: guardianData.lastName,
            email: guardianData.email,
            phone: guardianData.phone || null,
            overallStatus: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
          }).returning();

          guardianUserId = newUser[0].id;

          // Crear rol de guardian
          await db.insert(userRoles).values({
            id: generateId(),
            userId: guardianUserId,
            role: 'guardian',
            status: 'active',
            roleData: {
              createdByDoctor: doctor.id,
              createdForPatient: patientId,
              relationship: guardianData.relationship
            },
            createdAt: new Date(),
            updatedAt: new Date()
          });

          // Si se solicita invitación, crear token y enviar email
          if (guardianData.sendInvitation) {
            const invitationToken = crypto.randomUUID();
            const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000); // 7 días

            await db.insert(patientInvitations).values({
              id: generateId(),
              patientUserId: guardianUserId,
              guardianEmail: guardianData.email,
              invitationToken,
              status: 'pending',
              expiresAt,
              createdAt: new Date()
            });

            // Enviar email de invitación
            const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';
            const activationLink = `${baseUrl}/activate-account?token=${invitationToken}`;

            const emailResult = await sendEmail({
              to: guardianData.email,
              subject: 'Invitación como Encargado - Portal Médico',
              html: emailTemplates.accountActivation({
                patientName: `${patient.firstName} ${patient.lastName}`,
                doctorName: `Dr. ${doctor.firstName} ${doctor.lastName}`,
                consultoryName: 'Consultorio Médico',
                activationLink,
                appointmentDate: 'próximamente',
                recipientType: 'guardian',
                guardianName: `${guardianData.firstName} ${guardianData.lastName}`,
                relationship: guardianData.relationship
              })
            });

            if (!emailResult.success) {
              console.warn('No se pudo enviar email de invitación a:', guardianData.email);
            }
          }
        }

        // Verificar si ya existe una relación guardian-paciente
        const existingRelation = await db
          .select()
          .from(guardianPatientRelations)
          .where(
            and(
              eq(guardianPatientRelations.guardianId, guardianUserId),
              eq(guardianPatientRelations.patientId, patientId)
            )
          )
          .limit(1);

        if (existingRelation.length === 0) {
          // Crear relación guardian-paciente
          await db.insert(guardianPatientRelations).values({
            id: generateId(),
            guardianId: guardianUserId,
            patientId: patientId,
            relationship: guardianData.relationship,
            isPrimary: guardianData.isPrimary,
            canMakeDecisions: guardianData.canMakeDecisions,
            validUntil: null, // Sin límite de tiempo por defecto
            createdAt: new Date(),
            updatedAt: new Date()
          });
        }

        results.push({
          guardianId: guardianUserId,
          email: guardianData.email,
          name: `${guardianData.firstName} ${guardianData.lastName}`,
          relationship: guardianData.relationship,
          isPrimary: guardianData.isPrimary,
          invitationSent: guardianData.sendInvitation
        });

      } catch (error) {
        console.error(`Error procesando encargado ${guardianData.email}:`, error);
        // Continuar con el siguiente encargado
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        guardiansCreated: results.length,
        guardians: results,
        patientName: `${patient.firstName} ${patient.lastName}`
      },
      message: `${results.length} encargado(s) procesado(s) exitosamente`
    });

  } catch (error) {
    console.error('Error creating guardians:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function GET(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;

    // Obtener encargados del paciente
    const guardians = await db
      .select({
        id: guardianPatientRelations.id,
        guardianId: guardianPatientRelations.guardianId,
        relationship: guardianPatientRelations.relationship,
        isPrimary: guardianPatientRelations.isPrimary,
        canMakeDecisions: guardianPatientRelations.canMakeDecisions,
        validUntil: guardianPatientRelations.validUntil,
        createdAt: guardianPatientRelations.createdAt,
        // Datos del encargado
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone
      })
      .from(guardianPatientRelations)
      .innerJoin(user, eq(guardianPatientRelations.guardianId, user.id))
      .where(eq(guardianPatientRelations.patientId, patientId))
      .orderBy(guardianPatientRelations.isPrimary, guardianPatientRelations.createdAt);

    return NextResponse.json({
      success: true,
      data: guardians
    });

  } catch (error) {
    console.error('Error fetching guardians:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}