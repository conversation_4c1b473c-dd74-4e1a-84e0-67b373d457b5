'use client';

import { useEffect, useState } from 'react';
import { 
  Calendar, 
  Users, 
  ClipboardList, 
  CheckSquare,
  Clock,
  UserCheck,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatDate } from '@/lib/utils';
import { WaitingPatientsCard } from '@/components/waiting-room/waiting-patients-card';

interface WaitingPatient {
  id: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  serviceName: string;
  consultoryName: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  checkedInAt: string;
}

export default function AssistantDashboard() {
  const [waitingPatients, setWaitingPatients] = useState<WaitingPatient[]>([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    pendingConfirmations: 0,
    waitingPatients: 0,
    todayAppointments: 0,
    pendingTasks: 0,
    pendingTasksBreakdown: {
      needsPreCheckin: 0,
      needsReminder: 0,
      needsConfirmation: 0,
      preCheckinNotCompleted: 0
    }
  });

  const fetchWaitingPatients = async () => {
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      const response = await fetch(`/api/appointments?status=checked_in&dateFrom=${today}&dateTo=${today}&orderBy=checkedInAt&orderDirection=asc`);
      if (response.ok) {
        const data = await response.json();
        const waitingList = data.data || [];
        setWaitingPatients(waitingList);
        setStats(prev => ({
          ...prev,
          waitingPatients: waitingList.length
        }));
      }
    } catch (error) {
      console.error('Error fetching waiting patients:', error);
    }
  };

  const fetchStats = async () => {
    try {
      // Usar la nueva API de estadísticas del asistente
      const assistantStatsRes = await fetch('/api/assistant/stats');
      
      if (assistantStatsRes.ok) {
        const assistantData = await assistantStatsRes.json();
        const statsData = assistantData.data;
        
        setStats(prev => ({
          ...prev,
          pendingConfirmations: statsData.todayStats.pendingConfirmations,
          todayAppointments: statsData.todayStats.totalAppointments,
          pendingTasks: statsData.pendingTasks.total,
          pendingTasksBreakdown: statsData.pendingTasks.breakdown
        }));
      } else {
        // Fallback a las APIs originales si la nueva falla
        const today = new Date().toISOString().split('T')[0];
        
        const [confirmationsRes, todayRes] = await Promise.all([
          fetch('/api/appointments?status=scheduled&today=true'),
          fetch(`/api/appointments?date=${today}`)
        ]);

        if (confirmationsRes.ok) {
          const confirmationsData = await confirmationsRes.json();
          setStats(prev => ({
            ...prev,
            pendingConfirmations: confirmationsData.data?.length || 0
          }));
        }

        if (todayRes.ok) {
          const todayData = await todayRes.json();
          setStats(prev => ({
            ...prev,
            todayAppointments: todayData.data?.length || 0
          }));
        }
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWaitingPatients();
    fetchStats();
    
    // Actualizar cada 30 segundos
    const interval = setInterval(() => {
      fetchWaitingPatients();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleRefreshWaitingRoom = () => {
    fetchWaitingPatients();
  };
  return (
    <div className="space-y-4 md:space-y-6 lg:space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-[#3D4E80]">Dashboard Asistente</h1>
          <p className="text-sm md:text-base text-[#3D4E80]/70 mt-1 md:mt-2">Gestiona las citas y pacientes del consultorio</p>
        </div>
        <div className="hidden sm:block text-xs md:text-sm text-[#3D4E80]/70 bg-white px-3 md:px-4 py-1.5 md:py-2 rounded-lg border border-[#ADB6CA]/30">
          {new Date().toLocaleDateString('es-ES', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4">
        <Card className="hover:shadow-lg transition-all duration-300 border-[#F8E59A]/50 bg-[#FCEEA8]/20 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Citas por Confirmar
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#F8E59A]/30 flex items-center justify-center">
              <Calendar className="h-5 w-5 md:h-6 md:w-6 text-[#3D4E80]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.pendingConfirmations}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-[#3D4E80]/70">Para hoy</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#50bed2]/30 bg-[#50bed2]/10 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Pacientes en Espera
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#50bed2]/20 flex items-center justify-center">
              <Users className="h-5 w-5 md:h-6 md:w-6 text-[#50bed2]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.waitingPatients}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-[#3D4E80]/70">En sala de espera</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#3D4E80]/20 bg-[#3D4E80]/5 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Agenda del Día
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#3D4E80]/20 flex items-center justify-center">
              <ClipboardList className="h-5 w-5 md:h-6 md:w-6 text-[#3D4E80]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.todayAppointments}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              <span className="text-[#3D4E80]/70">Citas programadas</span>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:shadow-lg transition-all duration-300 border-[#ea6cb0]/20 bg-[#ea6cb0]/5 shadow-sm">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 md:pb-3">
            <CardTitle className="text-xs md:text-sm font-semibold text-[#3D4E80]/70 uppercase tracking-wide">
              Tareas Pendientes
            </CardTitle>
            <div className="h-10 w-10 md:h-12 md:w-12 rounded-full bg-[#ea6cb0]/20 flex items-center justify-center">
              <CheckSquare className="h-5 w-5 md:h-6 md:w-6 text-[#ea6cb0]" />
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="text-2xl md:text-3xl font-bold text-[#3D4E80] mb-1 md:mb-2">
              {loading ? '...' : stats.pendingTasks}
            </div>
            <div className="flex items-center text-xs md:text-sm">
              {!loading && stats.pendingTasks > 0 ? (
                <div className="text-[#3D4E80]/70">
                  <div>Pre-checkin: {stats.pendingTasksBreakdown.needsPreCheckin}</div>
                  <div>Recordatorios: {stats.pendingTasksBreakdown.needsReminder}</div>
                  {stats.pendingTasksBreakdown.preCheckinNotCompleted > 0 && (
                    <div>Sin completar: {stats.pendingTasksBreakdown.preCheckinNotCompleted}</div>
                  )}
                </div>
              ) : (
                <span className="text-[#3D4E80]/70">
                  {loading ? 'Cargando...' : 'Todas las tareas al día'}
                </span>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Sala de Espera */}
      <WaitingPatientsCard
        patients={waitingPatients}
        onRefresh={fetchWaitingPatients}
        onViewDetails={(patient) => window.open(`/dashboard/assistant/agenda/appointment/${patient.id}`, '_blank')}
        userRole="assistant"
        loading={loading}
        maxPatients={8}
        showViewAllLink={true}
        viewAllHref="/dashboard/assistant/waiting-room"
      />
    </div>
  );
}