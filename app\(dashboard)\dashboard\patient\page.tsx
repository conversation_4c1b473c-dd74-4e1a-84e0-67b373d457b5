'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { 
  Calendar, 
  Shield, 
  FileText, 
  Activity,
  Clock,
  DollarSign,
  Pill,
  MessageSquare,
  Bell,
  Plus,
  User,
  Heart,
  ChevronRight,
  Phone,
  Mail,
  AlertCircle,
  Download,
  CreditCard,
  Settings,
  Edit,
  X,
  Loader2,
  CheckCircle,
  UserCheck
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { formatPhoneForDisplay } from '@/lib/phone-utils';
import { formatTempEmail } from '@/lib/utils';
import { toast } from 'sonner';
import { formatLocalDateTime, formatTimeInConsultoryTimezone, formatInConsultoryTimezone } from '@/lib/timezone-utils';
import { useRegionalConfig } from '@/hooks/use-regional-config';
import AppointmentWizardModal from '@/components/appointments/appointment-wizard-modal';
import PreCheckinModal from '@/components/pre-checkin/pre-checkin-modal';

export default function PatientDashboard() {
  const { user } = useUser();
  const router = useRouter();
  const { config: regionalConfig } = useRegionalConfig();
  
  // Estados para datos del usuario
  const [patientData, setPatientData] = useState(null);
  const [nextAppointment, setNextAppointment] = useState(null);
  const [loading, setLoading] = useState(true);
  const [showAppointmentModal, setShowAppointmentModal] = useState(false);
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showPreCheckinModal, setShowPreCheckinModal] = useState(false);
  const [cancelling, setCancelling] = useState(false);
  const [checkingIn, setCheckingIn] = useState(false);

  // Función para cargar citas del paciente
  const fetchAppointments = async () => {
    try {
      if (!user?.id) return;
      
      const response = await fetch(`/api/appointments?patientId=${user.id}&orderBy=scheduledDate&orderDirection=asc&limit=5`);
      const result = await response.json();
      
      if (response.ok && result.data && result.data.length > 0) {
        // Buscar la primera cita que NO esté completada, cancelada, no-show, o ya con check-in
        const validAppointment = result.data.find(apt => 
          !['completed', 'cancelled', 'no_show', 'in_progress'].includes(apt.status) && 
          !apt.checkedInAt
        );
        
        if (validAppointment) {
          const appointment = validAppointment;
        
        // DEBUG: Ver qué datos llegan de la base de datos
        console.log('🗓️ Datos de cita desde DB:', {
          scheduledDate: appointment.scheduledDate,
          startTime: appointment.startTime,
          endTime: appointment.endTime,
          status: appointment.status,
          preCheckinCompleted: appointment.preCheckinCompleted,
          preCheckinCompletedAt: appointment.preCheckinCompletedAt
        });
        
        // Formatear fechas (ya están en hora local de Guatemala)
        const scheduledDateFormatted = formatLocalDateTime(
          appointment.startTime, // Usar startTime que tiene fecha y hora completa
          'dd MMM yyyy'
        );
        
        const startTimeFormatted = formatLocalDateTime(
          appointment.startTime, 
          regionalConfig.locale === 'en-US' ? 'h:mm a' : 'HH:mm'
        );

        console.log('🗓️ Fechas formateadas (CORREGIDAS):', {
          scheduledDateFormatted,
          startTimeFormatted
        });

        // Verificar si puede hacer check-in
        const now = new Date();
        const appointmentStart = new Date(appointment.startTime);
        
        // Convertir a hora local de Guatemala para debug
        const nowLocal = now.toLocaleString('es-GT', { timeZone: 'America/Guatemala' });
        const appointmentLocal = appointmentStart.toLocaleString('es-GT', { timeZone: 'America/Guatemala' });
        
        // Calcular diferencia en horas desde el inicio de la cita
        const hoursSinceAppointment = (now.getTime() - appointmentStart.getTime()) / (1000 * 60 * 60);
        
        // Permitir check-in desde 2 horas antes hasta 24 horas después del inicio de la cita
        const canCheckInByTime = hoursSinceAppointment >= -2 && hoursSinceAppointment <= 24;
        
        // Debug para verificar las fechas
        console.log('🔍 Verificando check-in:', {
          appointmentStartUTC: appointmentStart.toISOString(),
          appointmentStartLocal: appointmentLocal,
          nowUTC: now.toISOString(),
          nowLocal: nowLocal,
          hoursSinceAppointment: Math.round(hoursSinceAppointment * 10) / 10,
          canCheckInByTime,
          status: appointment.status,
          checkedInAt: appointment.checkedInAt
        });
        
        const canCheckIn = (
          (appointment.status === 'confirmed' || appointment.status === 'pending_confirmation') &&
          canCheckInByTime &&
          !appointment.checkedInAt
        );
        
        console.log('✅ Resultado canCheckIn:', canCheckIn);

        const formattedAppointment = {
          id: appointment.id,
          date: scheduledDateFormatted,
          time: startTimeFormatted,
          doctor: `${appointment.doctorFirstName || 'Dr.'} ${appointment.doctorLastName || 'Doctor'}`,
          specialty: appointment.activityTypeName || 'Consulta General',
          type: appointment.title || 'Consulta',  
          status: appointment.status,
          shortCode: appointment.shortCode,
          canDoPreCheckin: (appointment.status === 'pending_confirmation' || appointment.status === 'confirmed') && !appointment.preCheckinCompleted,
          preCheckinCompleted: appointment.preCheckinCompleted,
          preCheckinCompletedAt: appointment.preCheckinCompletedAt,
          canCheckIn: canCheckIn,
          checkedInAt: appointment.checkedInAt
        };
        
          setNextAppointment(formattedAppointment);
        } else {
          // No hay citas válidas pendientes
          setNextAppointment(null);
        }
      } else {
        // No hay citas, asegurar que el estado sea null
        setNextAppointment(null);
      }
    } catch (error) {
      console.error('Error cargando citas:', error);
    }
  };

  // Función para cancelar cita
  const handleCancelAppointment = async () => {
    if (!nextAppointment?.id) return;
    
    setCancelling(true);
    try {
      const response = await fetch(`/api/appointments/${nextAppointment.id}`, {
        method: 'DELETE',
      });

      const result = await response.json();

      if (response.ok) {
        // Recargar las citas para actualizar la vista
        await fetchAppointments();
        setShowCancelModal(false);
        // Mostrar mensaje de éxito
        toast.success('Cita cancelada exitosamente. Puedes agendar una nueva cita cuando gustes.');
      } else {
        console.error('Error cancelando cita:', result.error);
        toast.error(`Error al cancelar la cita: ${result.error || 'Error desconocido'}`);
      }
    } catch (error) {
      console.error('Error cancelando cita:', error);
      toast.error('Error de conexión al cancelar la cita. Intenta nuevamente.');
    } finally {
      setCancelling(false);
    }
  };

  // Función para hacer check-in
  const handleCheckIn = async () => {
    if (!nextAppointment?.id) return;
    
    setCheckingIn(true);
    try {
      const response = await fetch(`/api/appointments/${nextAppointment.id}/check-in`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      const result = await response.json();

      if (response.ok) {
        // Recargar las citas para actualizar la vista
        await fetchAppointments();
        // Mostrar mensaje de éxito
        toast.success('¡Check-in completado exitosamente! El doctor ha sido notificado de tu llegada.');
      } else {
        console.error('Error en check-in:', result.error);
        toast.error(`Error al registrar llegada: ${result.error || 'Error desconocido'}`);
      }
    } catch (error) {
      console.error('Error en check-in:', error);
      toast.error('Error de conexión al registrar llegada. Intenta nuevamente.');
    } finally {
      setCheckingIn(false);
    }
  };

  // Cargar datos del paciente
  useEffect(() => {
    const fetchPatientData = async () => {
      try {
        const response = await fetch('/api/auth/current-user');
        const result = await response.json();
        
        if (result.success && result.data) {
          setPatientData(result.data);
        }
      } catch (error) {
        console.error('Error fetching patient data:', error);
      } finally {
        setLoading(false);
      }
    };

    const loadData = async () => {
      await fetchPatientData();
      await fetchAppointments();
    };

    if (user?.id) {
      loadData();
    }
  }, [user?.id]);

  const [pendingPayment] = useState(null); // Cambiar a null para probar estado sin pagos pendientes
  // const [pendingPayment] = useState({
  //   id: 1,
  //   consultationDate: '10 Ene 2025',
  //   amount: 150.00,
  //   doctor: 'Dr. González'
  // });

  const [recentPrescription] = useState({
    id: 1,
    medication: 'Amoxicilina 500mg',
    prescribedBy: 'Dr. González',
    date: '12 Ene 2025',
    canDownload: true
  });

  const [unreadMessages] = useState(2);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Cargando tu información...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Compacto Mobile-First */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src={user?.imageUrl} />
            <AvatarFallback className="text-lg bg-[#ea6cb0]/20 text-[#3D4E80]">
              {patientData?.firstName?.[0] || user?.firstName?.[0]}
              {patientData?.lastName?.[0] || user?.lastName?.[0]}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 min-w-0">
            <h1 className="text-xl font-bold text-[#3D4E80] truncate">
              {patientData?.firstName || user?.firstName} {patientData?.lastName || user?.lastName}
            </h1>
            <div className="space-y-1">
              <div className="flex items-center gap-2 text-sm text-[#3D4E80]/70">
                <Phone className="h-3 w-3 text-[#50bed2]" />
                <span>{patientData?.phone ? formatPhoneForDisplay(patientData.phone) : '+502 5555-5555'}</span>
              </div>
              <div className="flex items-center gap-2 text-sm text-[#3D4E80]/70">
                <Mail className="h-3 w-3 text-[#50bed2]" />
                <span className="truncate">{formatTempEmail(patientData?.email || user?.emailAddresses?.[0]?.emailAddress || '')}</span>
              </div>
            </div>
          </div>

          {/* Botón de Expediente */}
          <div className="flex flex-col gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push('/dashboard/patient/profile')}
              className="flex items-center gap-1 text-xs border-[#ADB6CA] text-[#3D4E80] hover:bg-[#ea6cb0]/10 hover:border-[#ea6cb0] hover:text-[#ea6cb0]"
            >
              <Edit className="h-3 w-3" />
              Mi Expediente
            </Button>
          </div>
        </div>
      </div>

      {/* Contenido Principal */}
      <div className="p-4 space-y-4 pb-6">

        {/* Grid Citas y Pagos */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          
          {/* Próxima Cita o Crear Nueva */}
          <Card className="border-[#3D4E80]/20 bg-[#3D4E80]/5 hover:shadow-lg transition-all duration-300">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg flex items-center gap-2">
                <Calendar className="h-5 w-5 text-[#50bed2]" />
                <span className="text-[#3D4E80]">{nextAppointment ? 'Próxima Cita' : 'Mis Citas'}</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {nextAppointment ? (
                // Tiene cita programada
                <>
                  <div className="flex justify-between items-start">
                    <div>
                      <p className="font-semibold text-[#3D4E80]">{nextAppointment.doctor}</p>
                      <p className="text-sm text-[#3D4E80]/70">{nextAppointment.specialty}</p>
                      <p className="text-sm text-[#3D4E80]/70">{nextAppointment.type}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-[#50bed2]">{nextAppointment.date}</p>
                      <p className="text-sm text-[#50bed2]/80">{nextAppointment.time}</p>
                    </div>
                  </div>
                  
                  {/* Código de confirmación compacto */}
                  {nextAppointment.shortCode && (
                    <div className="bg-[#FCEEA8]/40 rounded-md p-3 border-2 border-[#F8E59A] mt-3">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-xs font-medium text-[#3D4E80]">Código de Confirmación</p>
                          <p className="text-lg font-bold text-[#3D4E80] tracking-wider">{nextAppointment.shortCode}</p>
                          <p className="text-xs text-[#3D4E80]/70 mt-1">Presenta este código al llegar a tu cita</p>
                        </div>
                        <div className="text-[#50bed2]">
                          <Badge className="h-4 w-4" />
                        </div>
                      </div>
                    </div>
                  )}
                  
                  <div className="space-y-2 pt-3">
                    {/* Indicador de Pre-checkin completado */}
                    {nextAppointment.preCheckinCompleted && (
                      <div className="bg-[#50bed2]/10 rounded-md p-2 border border-[#50bed2]/30">
                        <div className="flex items-center justify-center gap-2">
                          <CheckCircle className="h-4 w-4 text-[#50bed2]" />
                          <span className="text-sm font-medium text-[#3D4E80]">Pre-checkin completado</span>
                        </div>
                        {nextAppointment.preCheckinCompletedAt && (
                          <p className="text-xs text-[#3D4E80]/70 text-center mt-1">
                            {formatInConsultoryTimezone(nextAppointment.preCheckinCompletedAt, 'dd MMM HH:mm')}
                          </p>
                        )}
                      </div>
                    )}

                    {/* Indicador de Check-in completado */}
                    {nextAppointment.checkedInAt && (
                      <div className="bg-[#ADB6CA]/20 rounded-md p-2 border border-[#ADB6CA]/40">
                        <div className="flex items-center justify-center gap-2">
                          <UserCheck className="h-4 w-4 text-[#50bed2]" />
                          <span className="text-sm font-medium text-[#3D4E80]">Ya registraste tu llegada</span>
                        </div>
                        <p className="text-xs text-[#3D4E80]/70 text-center mt-1">
                          {formatTimeInConsultoryTimezone(nextAppointment.checkedInAt)}
                        </p>
                      </div>
                    )}
                    
                    <div className="flex gap-2">
                      <Button 
                        variant="outline" 
                        size="sm" 
                        className="flex-1 border-[#ADB6CA] text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2]"
                        onClick={() => setShowDetailsModal(true)}
                      >
                        Ver Detalles
                      </Button>
                      
                      {/* Botón de Pre-Check */}
                      {nextAppointment.canDoPreCheckin && (
                        <Button 
                          size="sm" 
                          className="flex-1 bg-[#3D4E80] hover:bg-[#3D4E80]/80 text-white"
                          onClick={() => setShowPreCheckinModal(true)}
                        >
                          Pre-Check
                        </Button>
                      )}
                      
                      {/* Botón de Check-in */}
                      {nextAppointment.canCheckIn && (
                        <Button 
                          size="sm" 
                          className="flex-1 bg-[#50bed2] hover:bg-[#50bed2]/80 text-white"
                          onClick={handleCheckIn}
                          disabled={checkingIn}
                        >
                          {checkingIn ? (
                            <>
                              <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                              Registrando...
                            </>
                          ) : (
                            <>
                              <UserCheck className="h-3 w-3 mr-1" />
                              Check-in
                            </>
                          )}
                        </Button>
                      )}
                    </div>
                  </div>
                </>
              ) : (
                // No tiene citas programadas
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-[#50bed2]/20 flex items-center justify-center">
                      <Calendar className="h-5 w-5 text-[#50bed2]" />
                    </div>
                    <div>
                      <p className="text-[#3D4E80] font-medium text-sm">No tienes citas programadas</p>
                      <p className="text-xs text-[#3D4E80]/70">Agenda tu próxima consulta</p>
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button 
                      variant="outline" 
                      size="sm"
                      className="border-[#ADB6CA] text-[#3D4E80] hover:bg-[#ea6cb0]/10 hover:border-[#ea6cb0] hover:text-[#ea6cb0]"
                      onClick={() => router.push('/dashboard/patient/appointments')}
                    >
                      Historial
                    </Button>
                    <Button 
                      size="sm"
                      className="bg-[#50bed2] hover:bg-[#50bed2]/80 text-white"
                      onClick={() => setShowAppointmentModal(true)}
                      disabled={!!nextAppointment && !nextAppointment.checkedInAt && nextAppointment.status !== 'completed'}
                      title={
                        nextAppointment?.checkedInAt 
                          ? "Puedes agendar tu próxima cita" 
                          : nextAppointment?.status === 'completed'
                          ? "Puedes agendar una nueva cita"
                          : nextAppointment 
                          ? "Ya tienes una cita programada. Cancela la actual para agendar una nueva." 
                          : "Agendar nueva cita"
                      }
                    >
                      <Plus className="h-3 w-3 mr-1" />
                      Agendar
                    </Button>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Pagos - Siempre visible */}
          <Card className={`hover:shadow-lg transition-all duration-300 ${
            pendingPayment ? 'border-[#FAC9D1]/60 bg-[#FAC9D1]/20' : 'border-[#50bed2]/30 bg-[#50bed2]/10'
          }`}>
            <CardHeader className="pb-3">
              <CardTitle className={`text-lg flex items-center gap-2 ${
                pendingPayment ? 'text-[#3D4E80]' : 'text-[#3D4E80]'
              }`}>
                {pendingPayment ? (
                  <>
                    <AlertCircle className="h-5 w-5 text-[#FAC9D1]" />
                    Pago Pendiente
                  </>
                ) : (
                  <>
                    <CreditCard className="h-5 w-5 text-[#50bed2]" />
                    Pagos al Día
                  </>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {pendingPayment ? (
                // Tiene pagos pendientes
                <div className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-[#3D4E80]">Consulta del {pendingPayment.consultationDate}</p>
                    <p className="text-sm text-[#3D4E80]/70">{pendingPayment.doctor}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold text-[#3D4E80]">Q.{pendingPayment.amount.toFixed(2)}</p>
                    <Button className="bg-[#FAC9D1] hover:bg-[#ea6cb0] hover:text-white text-[#3D4E80] mt-2">
                      <CreditCard className="h-4 w-4 mr-2" />
                      PAGAR AHORA
                    </Button>
                  </div>
                </div>
              ) : (
                // No tiene pagos pendientes
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-[#50bed2]/20 flex items-center justify-center">
                      <CreditCard className="h-5 w-5 text-[#50bed2]" />
                    </div>
                    <div>
                      <p className="text-[#3D4E80] font-medium text-sm">No tienes pagos pendientes</p>
                      <p className="text-xs text-[#3D4E80]/70">Todas las consultas están pagadas</p>
                    </div>
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    className="border-[#ADB6CA] text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2]"
                    onClick={() => router.push('/dashboard/patient/payments')}
                  >
                    Ver Pagos
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

        </div>

        {/* Grid 2x2 con iconos grandes */}
        <div className="grid grid-cols-2 gap-4">
          
          {/* Recetas y Tratamientos */}
          <Card className="hover:shadow-lg transition-all duration-300 border-[#ea6cb0]/20 bg-[#ea6cb0]/5 shadow-sm cursor-pointer" 
                onClick={() => router.push('/dashboard/patient/prescriptions')}>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="h-16 w-16 rounded-full bg-[#ea6cb0]/20 flex items-center justify-center">
                  <Pill className="h-8 w-8 text-[#ea6cb0]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#3D4E80]">Recetas</h3>
                  <p className="text-sm text-[#3D4E80]/70">Ver tratamientos</p>
                </div>
                {recentPrescription && (
                  <div className="w-full pt-3 border-t border-[#ADB6CA]/30">
                    <p className="text-xs font-medium text-[#3D4E80] truncate">{recentPrescription.medication}</p>
                    <p className="text-xs text-[#3D4E80]/60">{recentPrescription.date}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Mensajes */}
          <Card className="hover:shadow-lg transition-all duration-300 border-[#50bed2]/30 bg-[#50bed2]/10 shadow-sm cursor-pointer" 
                onClick={() => router.push('/dashboard/patient/messages')}>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="h-16 w-16 rounded-full bg-[#50bed2]/20 flex items-center justify-center relative">
                  <MessageSquare className="h-8 w-8 text-[#50bed2]" />
                  {unreadMessages > 0 && (
                    <Badge className="absolute -top-1 -right-1 h-6 w-6 p-0 text-xs bg-[#FAC9D1] hover:bg-[#FAC9D1]/80 text-[#3D4E80] flex items-center justify-center">
                      {unreadMessages}
                    </Badge>
                  )}
                </div>
                <div>
                  <h3 className="font-semibold text-[#3D4E80]">Mensajes</h3>
                  <p className="text-sm text-[#3D4E80]/70">
                    {unreadMessages > 0 ? `${unreadMessages} no leídos` : 'Todos leídos'}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Última Receta */}
          {recentPrescription && (
            <Card className="hover:shadow-lg transition-all duration-300 border-[#FCEEA8]/50 bg-[#FCEEA8]/20 shadow-sm cursor-pointer" 
                  onClick={() => router.push('/dashboard/patient/prescriptions')}>
              <CardContent className="p-6">
                <div className="flex flex-col items-center text-center space-y-4">
                  <div className="h-16 w-16 rounded-full bg-[#F8E59A]/30 flex items-center justify-center">
                    <Download className="h-8 w-8 text-[#3D4E80]" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-[#3D4E80]">Última Receta</h3>
                    <p className="text-sm text-[#3D4E80]/70">{recentPrescription.medication}</p>
                  </div>
                  <div className="w-full pt-3 border-t border-[#ADB6CA]/30">
                    <p className="text-xs text-[#3D4E80]/60">{recentPrescription.date}</p>
                    <p className="text-xs text-[#3D4E80]/60">{recentPrescription.prescribedBy}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Mis Consultas */}
          <Card className="hover:shadow-lg transition-all duration-300 border-[#ADB6CA]/40 bg-[#ADB6CA]/15 shadow-sm cursor-pointer" 
                onClick={() => router.push('/dashboard/patient/appointments')}>
            <CardContent className="p-6">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="h-16 w-16 rounded-full bg-[#ADB6CA]/30 flex items-center justify-center">
                  <Calendar className="h-8 w-8 text-[#3D4E80]" />
                </div>
                <div>
                  <h3 className="font-semibold text-[#3D4E80]">Historial de Citas</h3>
                  <p className="text-sm text-[#3D4E80]/70">Ver todas mis citas</p>
                </div>
              </div>
            </CardContent>
          </Card>

        </div>

      </div>
      
      {/* Modal de Agendar Cita */}
      <AppointmentWizardModal
        isOpen={showAppointmentModal}
        onClose={() => setShowAppointmentModal(false)}
        onSuccess={() => {
          // Recargar las citas sin recargar toda la página
          fetchAppointments();
          setShowAppointmentModal(false);
        }}
      />

      {/* Modal de Confirmación de Cancelación */}
      <Dialog open={showCancelModal} onOpenChange={setShowCancelModal}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2 text-red-600">
              <AlertCircle className="h-5 w-5" />
              Cancelar Cita Médica
            </DialogTitle>
            <DialogDescription>
              Esta acción no se puede deshacer
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            {/* Información de la cita */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-semibold text-gray-900 mb-2">Cita a cancelar:</h4>
              <div className="space-y-1 text-sm text-gray-700">
                <p><strong>Doctor:</strong> {nextAppointment?.doctor}</p>
                <p><strong>Fecha:</strong> {nextAppointment?.date}</p>
                <p><strong>Hora:</strong> {nextAppointment?.time}</p>
                <p><strong>Código:</strong> {nextAppointment?.shortCode}</p>
              </div>
            </div>

            {/* Advertencia clara */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-start gap-3">
                <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5 flex-shrink-0" />
                <div className="space-y-2">
                  <p className="font-semibold text-yellow-800">
                    ⚠️ Importante: Lee antes de cancelar
                  </p>
                  <ul className="text-sm text-yellow-700 space-y-1">
                    <li>• Tu cita será cancelada permanentemente</li>
                    <li>• Tendrás que volver a agendar si necesitas una nueva cita</li>
                    <li>• Es posible que no haya disponibilidad inmediata</li>
                    <li>• Si cancelas con poca anticipación, podrían aplicar políticas de cancelación</li>
                  </ul>
                </div>
              </div>
            </div>

            {/* Pregunta de confirmación */}
            <div className="text-center py-2">
              <p className="text-gray-700 font-medium">
                ¿Estás seguro de que deseas cancelar esta cita?
              </p>
            </div>
          </div>

          <DialogFooter className="gap-2">
            <Button
              variant="outline"
              onClick={() => setShowCancelModal(false)}
              disabled={cancelling}
            >
              No, Mantener Cita
            </Button>
            <Button
              variant="destructive"
              onClick={handleCancelAppointment}
              disabled={cancelling}
              className="bg-red-600 hover:bg-red-700"
            >
              {cancelling ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cancelando...
                </>
              ) : (
                <>
                  <X className="h-4 w-4 mr-2" />
                  Sí, Cancelar Cita
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Detalles de la Cita */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="w-[95vw] max-w-[550px] max-h-[85vh] p-0 gap-0 overflow-hidden flex flex-col">
          <DialogHeader className="text-center sm:text-left px-4 sm:px-6 pt-4 sm:pt-6 pb-2 flex-shrink-0">
            <DialogTitle className="flex items-center justify-center sm:justify-start gap-2 text-[#3D4E80] text-lg">
              <Calendar className="h-5 w-5 text-[#50bed2]" />
              Detalles de tu Cita
            </DialogTitle>
            <DialogDescription className="text-center sm:text-left text-[#3D4E80]/70">
              Información completa de tu próxima cita médica
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-y-auto px-4 sm:px-6">
            <div className="space-y-4 py-2">
              {/* Información del Doctor */}
              <div className="bg-[#3D4E80]/10 rounded-lg p-3 sm:p-4 border border-[#3D4E80]/20">
                <h4 className="font-semibold text-[#3D4E80] mb-2 flex items-center gap-2 text-sm sm:text-base">
                  <User className="h-4 w-4 flex-shrink-0" />
                  Información del Doctor
                </h4>
                <div className="space-y-1 sm:space-y-2">
                  <p className="text-[#3D4E80] text-sm sm:text-base">
                    <strong>Doctor:</strong> {nextAppointment?.doctor}
                  </p>
                  <p className="text-[#3D4E80]/80 text-sm">
                    <strong>Especialidad:</strong> {nextAppointment?.specialty}
                  </p>
                  <p className="text-[#3D4E80]/80 text-sm">
                    <strong>Tipo:</strong> {nextAppointment?.type}
                  </p>
                </div>
              </div>

              {/* Fecha y Hora */}
              <div className="bg-[#50bed2]/10 rounded-lg p-3 sm:p-4 border border-[#50bed2]/30">
                <h4 className="font-semibold text-[#50bed2] mb-2 flex items-center gap-2 text-sm sm:text-base">
                  <Clock className="h-4 w-4 flex-shrink-0" />
                  Fecha y Hora
                </h4>
                <div className="grid grid-cols-2 gap-3 sm:gap-4">
                  <div className="text-center sm:text-left">
                    <p className="text-xs text-[#50bed2] font-medium">FECHA</p>
                    <p className="text-base sm:text-lg font-bold text-[#3D4E80]">{nextAppointment?.date}</p>
                  </div>
                  <div className="text-center sm:text-left">
                    <p className="text-xs text-[#50bed2] font-medium">HORA</p>
                    <p className="text-base sm:text-lg font-bold text-[#3D4E80]">{nextAppointment?.time}</p>
                  </div>
                </div>
              </div>

              {/* Código de Confirmación */}
              {nextAppointment?.shortCode && (
                <div className="bg-[#FCEEA8]/30 rounded-lg p-3 sm:p-4 border border-[#F8E59A]/50">
                  <h4 className="font-semibold text-[#3D4E80] mb-2 flex items-center gap-2 text-sm sm:text-base">
                    <Badge className="h-4 w-4 flex-shrink-0" />
                    Código de Confirmación
                  </h4>
                  <div className="bg-white rounded-lg p-2 sm:p-3 border-2 border-[#F8E59A]">
                    <div className="text-center">
                      <p className="text-xs sm:text-sm text-[#3D4E80]/80 mb-1">Tu código es:</p>
                      <p className="text-xl sm:text-2xl font-bold text-[#3D4E80] tracking-wider">{nextAppointment.shortCode}</p>
                      <p className="text-xs text-[#3D4E80]/70 mt-1">Presenta este código al llegar</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Estado de la Cita */}
              <div className="bg-[#ADB6CA]/20 rounded-lg p-3 sm:p-4 border border-[#ADB6CA]/40">
                <h4 className="font-semibold text-[#3D4E80] mb-2 flex items-center gap-2 text-sm sm:text-base">
                  <AlertCircle className="h-4 w-4 flex-shrink-0" />
                  Estado de la Cita
                </h4>
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2">
                  <Badge 
                    className={`text-xs sm:text-sm ${
                      nextAppointment?.status === 'confirmed' 
                        ? 'bg-[#50bed2]/20 text-[#50bed2] border-[#50bed2]/30' 
                        : nextAppointment?.status === 'pending_confirmation'
                        ? 'bg-[#F8E59A]/50 text-[#3D4E80] border-[#F8E59A]'
                        : 'bg-[#ADB6CA]/30 text-[#3D4E80] border-[#ADB6CA]'
                    }`}
                  >
                    {nextAppointment?.status === 'confirmed' && 'Confirmada'}
                    {nextAppointment?.status === 'pending_confirmation' && 'Pendiente de Confirmación'}
                    {nextAppointment?.status === 'scheduled' && 'Programada'}
                  </Badge>
                  {nextAppointment?.preCheckinCompleted ? (
                    <span className="text-xs sm:text-sm text-[#50bed2] flex items-center gap-1">
                      <CheckCircle className="h-3 w-3" />
                      Pre-check completado
                    </span>
                  ) : nextAppointment?.canDoPreCheckin ? (
                    <span className="text-xs sm:text-sm text-[#3D4E80]/70">Pre-check disponible</span>
                  ) : null}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter className="px-4 sm:px-6 pb-4 sm:pb-6 pt-3 border-t bg-white flex-shrink-0">
            <div className="flex flex-col gap-3 w-full">
              {/* Primera fila: Botones de acción principales */}
              <div className="flex flex-col sm:flex-row gap-2">
                {/* Botón de Pre-Check */}
                {nextAppointment?.preCheckinCompleted ? (
                  <Button 
                    variant="outline"
                    className="w-full border-[#50bed2] text-[#50bed2] hover:bg-[#50bed2]/10"
                    disabled
                  >
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Pre-Check Completado
                  </Button>
                ) : nextAppointment?.canDoPreCheckin ? (
                  <Button 
                    className="bg-[#3D4E80] hover:bg-[#3D4E80]/80 w-full text-white"
                    onClick={() => {
                      setShowDetailsModal(false);
                      setShowPreCheckinModal(true);
                    }}
                  >
                    <Bell className="h-4 w-4 mr-2" />
                    Hacer Pre-Check
                  </Button>
                ) : null}
                
                {/* Botón de Check-in */}
                {nextAppointment?.checkedInAt ? (
                  <Button 
                    variant="outline"
                    className="w-full border-[#50bed2] text-[#50bed2] hover:bg-[#50bed2]/10"
                    disabled
                  >
                    <UserCheck className="h-4 w-4 mr-2" />
                    Check-in Completado
                  </Button>
                ) : nextAppointment?.canCheckIn ? (
                  <Button 
                    className="bg-[#50bed2] hover:bg-[#50bed2]/80 w-full text-white"
                    onClick={() => {
                      setShowDetailsModal(false);
                      handleCheckIn();
                    }}
                    disabled={checkingIn}
                  >
                    {checkingIn ? (
                      <>
                        <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        Registrando llegada...
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4 mr-2" />
                        Registrar mi Llegada
                      </>
                    )}
                  </Button>
                ) : null}
              </div>

              {/* Segunda fila: Botones secundarios */}
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDetailsModal(false)}
                  className="w-full sm:flex-1 border-[#ADB6CA] text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2]"
                >
                  Cerrar
                </Button>
                
                {/* Solo mostrar botón de cancelar si NO se ha hecho check-in */}
                {!nextAppointment?.checkedInAt && (
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowDetailsModal(false);
                      setShowCancelModal(true);
                    }}
                    className="bg-[#FAC9D1]/20 border-[#FAC9D1] text-[#3D4E80] hover:bg-[#ea6cb0] hover:border-[#ea6cb0] hover:text-white w-full sm:flex-1"
                  >
                    <X className="h-4 w-4 mr-2" />
                    Cancelar Cita
                  </Button>
                )}
              </div>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Modal de Pre-checkin */}
      <PreCheckinModal
        isOpen={showPreCheckinModal}
        onClose={() => setShowPreCheckinModal(false)}
        appointmentId={nextAppointment?.id}
        onSuccess={() => {
          fetchAppointments(); // Recargar para actualizar estado
          setShowPreCheckinModal(false);
        }}
      />
    </div>
  );
}