/**
 * SISTEMA DE LOGGING PARA CITAS MÉDICAS
 * Registra todos los eventos importantes del ciclo de vida de una cita
 */

import { db } from '@/db/drizzle';
import { appointmentLogs } from '@/db/schema';
import { eq, sql } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// Tipos de eventos
export type AppointmentEventType = 
  | 'created' | 'updated' | 'confirmed' | 'cancelled' | 'completed' 
  | 'rescheduled' | 'checked_in' | 'started' | 'no_show'
  | 'email_sent' | 'status_changed' | 'payment_updated'
  | 'precheckin_completed' | 'reminder_sent';

export type AppointmentEventCategory = 
  | 'system' | 'user_action' | 'email' | 'schedule' | 'payment' | 'medical' | 'notification';

export type AppointmentEmailType = 
  | 'appointment_created' | 'appointment_reminder' | 'appointment_cancelled'
  | 'patient_created' | 'precheckin_invitation' | 'confirmation_request';

// Interface para logging
export interface AppointmentLogData {
  appointmentId: string;
  eventType: AppointmentEventType;
  eventCategory: AppointmentEventCategory;
  title: string;
  description?: string;
  metadata?: Record<string, any>;
  
  // Usuario que ejecutó la acción
  triggeredBy?: string;
  triggeredByRole?: 'patient' | 'doctor' | 'admin' | 'system' | 'guardian';
  
  // Estados antes/después para cambios
  previousState?: Record<string, any>;
  newState?: Record<string, any>;
  
  // Para eventos de email
  emailType?: AppointmentEmailType;
  emailRecipient?: string;
  emailStatus?: 'sent' | 'failed' | 'delivered' | 'opened' | 'bounced';
  
  // Metadatos de sistema
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Registra un evento en el log de la cita
 */
export async function logAppointmentEvent(data: AppointmentLogData): Promise<void> {
  try {
    await db.insert(appointmentLogs).values({
      id: nanoid(),
      appointmentId: data.appointmentId,
      eventType: data.eventType,
      eventCategory: data.eventCategory,
      title: data.title,
      description: data.description,
      metadata: data.metadata,
      triggeredBy: data.triggeredBy,
      triggeredByRole: data.triggeredByRole,
      previousState: data.previousState,
      newState: data.newState,
      emailType: data.emailType,
      emailRecipient: data.emailRecipient,
      emailStatus: data.emailStatus,
      ipAddress: data.ipAddress,
      userAgent: data.userAgent,
    });
    
    console.log(`📝 Log registrado: ${data.title} (${data.eventType})`);
  } catch (error) {
    console.error('❌ Error registrando log de cita:', error);
    // No fallar el proceso principal por un error de logging
  }
}

/**
 * FUNCIONES DE CONVENIENCIA PARA EVENTOS COMUNES
 */

export async function logAppointmentCreated(
  appointmentId: string, 
  userId: string, 
  appointmentData: any
) {
  await logAppointmentEvent({
    appointmentId,
    eventType: 'created',
    eventCategory: 'user_action',
    title: 'Cita médica creada',
    description: `Nueva cita: ${appointmentData.title}`,
    triggeredBy: userId,
    triggeredByRole: 'doctor', // Asumiendo que solo doctores crean citas
    newState: appointmentData,
    metadata: {
      patientId: appointmentData.patientId,
      consultoryId: appointmentData.consultoryId,
      scheduledDate: appointmentData.scheduledDate,
      duration: appointmentData.duration,
    }
  });
}

export async function logEmailSent(
  appointmentId: string,
  emailType: AppointmentEmailType,
  recipient: string,
  status: 'sent' | 'failed',
  errorMessage?: string
) {
  await logAppointmentEvent({
    appointmentId,
    eventType: 'email_sent',
    eventCategory: 'email',
    title: `Email ${status === 'sent' ? 'enviado' : 'falló'}`,
    description: `${emailType} -> ${recipient}`,
    triggeredByRole: 'system',
    emailType,
    emailRecipient: recipient,
    emailStatus: status,
    metadata: {
      errorMessage: status === 'failed' ? errorMessage : undefined
    }
  });
}

export async function logStatusChange(
  appointmentId: string,
  userId: string,
  previousStatus: string,
  newStatus: string,
  reason?: string
) {
  await logAppointmentEvent({
    appointmentId,
    eventType: 'status_changed',
    eventCategory: 'user_action',
    title: `Estado cambiado: ${previousStatus} → ${newStatus}`,
    description: reason,
    triggeredBy: userId,
    previousState: { status: previousStatus },
    newState: { status: newStatus },
    metadata: {
      previousStatus,
      newStatus,
      reason
    }
  });
}

export async function logPreCheckinCompleted(
  appointmentId: string,
  completedBy: string,
  isDependent: boolean,
  preCheckinData: any
) {
  await logAppointmentEvent({
    appointmentId,
    eventType: 'precheckin_completed',
    eventCategory: 'user_action',
    title: 'Pre-checkin completado',
    description: `Completado por ${isDependent ? 'encargado' : 'paciente'}`,
    triggeredBy: completedBy,
    triggeredByRole: isDependent ? 'guardian' : 'patient',
    newState: preCheckinData,
    metadata: {
      isDependent,
      willAttend: preCheckinData.attendance,
      hasSymptoms: preCheckinData.hasSymptoms,
      takingMedications: preCheckinData.takingMedications,
    }
  });
}

/**
 * Obtiene el historial completo de eventos de una cita
 */
export async function getAppointmentHistory(appointmentId: string) {
  try {
    const history = await db
      .select()
      .from(appointmentLogs)
      .where(eq(appointmentLogs.appointmentId, appointmentId))
      .orderBy(appointmentLogs.createdAt);
    
    return history;
  } catch (error) {
    console.error('Error obteniendo historial de cita:', error);
    return [];
  }
}

/**
 * Obtiene estadísticas de eventos por tipo
 */
export async function getAppointmentStats(appointmentId?: string) {
  try {
    let query = db
      .select({
        eventType: appointmentLogs.eventType,
        eventCategory: appointmentLogs.eventCategory,
        count: sql<number>`count(*)`.as('count')
      })
      .from(appointmentLogs);
    
    if (appointmentId) {
      query = query.where(eq(appointmentLogs.appointmentId, appointmentId));
    }
    
    const stats = await query
      .groupBy(appointmentLogs.eventType, appointmentLogs.eventCategory)
      .orderBy(appointmentLogs.eventType);
    
    return stats;
  } catch (error) {
    console.error('Error obteniendo estadísticas:', error);
    return [];
  }
}