import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalSpecialties } from '@/db/schema';
import { and, eq, ilike, or, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(
        or(
          ilike(medicalSpecialties.name, `%${search}%`),
          ilike(medicalSpecialties.category, `%${search}%`)
        )
      );
    }

    const category = url.searchParams.get('category');
    if (category) {
      conditions.push(ilike(medicalSpecialties.category, `%${category}%`));
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(medicalSpecialties.isActive, active === 'true'));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(medicalSpecialties)
      .where(whereClause);

    // Get paginated data
    const specialtiesData = await db
      .select({
        id: medicalSpecialties.id,
        name: medicalSpecialties.name,
        category: medicalSpecialties.category,
        isActive: medicalSpecialties.isActive,
        createdAt: medicalSpecialties.createdAt,
        updatedAt: medicalSpecialties.updatedAt
      })
      .from(medicalSpecialties)
      .where(whereClause)
      .orderBy(medicalSpecialties.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: specialtiesData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching medical specialties:', error);
    return NextResponse.json(
      { error: 'Failed to fetch medical specialties' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, category, isActive = true } = body;

    if (!name) {
      return NextResponse.json(
        { error: 'El nombre es requerido' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(medicalSpecialties);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newSpecialty = await db.insert(medicalSpecialties).values({
      id: nextId,
      name,
      category: category || null,
      isActive
    }).returning();

    return NextResponse.json({ 
      success: true, 
      data: newSpecialty[0],
      message: 'Especialidad médica creada exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating medical specialty:', error);
    return NextResponse.json(
      { error: 'Failed to create medical specialty' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, category, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedSpecialty = await db
      .update(medicalSpecialties)
      .set({
        name,
        category,
        isActive,
        updatedAt: new Date()
      })
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .returning();

    if (updatedSpecialty.length === 0) {
      return NextResponse.json(
        { error: 'Medical specialty not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedSpecialty[0]);
  } catch (error) {
    console.error('Error updating medical specialty:', error);
    return NextResponse.json(
      { error: 'Failed to update medical specialty' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedSpecialty = await db
      .update(medicalSpecialties)
      .set({
        isActive: false,
        updatedAt: new Date()
      })
      .where(eq(medicalSpecialties.id, parseInt(id)))
      .returning();

    if (deletedSpecialty.length === 0) {
      return NextResponse.json(
        { error: 'Medical specialty not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Medical specialty deleted successfully' });
  } catch (error) {
    console.error('Error deleting medical specialty:', error);
    return NextResponse.json(
      { error: 'Failed to delete medical specialty' },
      { status: 500 }
    );
  }
} 