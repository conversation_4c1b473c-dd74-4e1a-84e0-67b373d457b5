'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  User, 
  CheckCircle, 
  AlertCircle, 
  UserCheck,
  RefreshCw,
  Calendar
} from 'lucide-react';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';

interface UpcomingAppointmentsProps {
  className?: string;
  onViewAll?: () => void;
}

const statusConfig = {
  scheduled: {
    label: 'Programada',
    color: 'bg-blue-100 text-blue-800 border-blue-300',
    icon: Calendar
  },
  pending_confirmation: {
    label: 'Pendiente de Confirmación',
    color: 'bg-orange-100 text-orange-800 border-orange-300',
    icon: AlertCircle
  },
  confirmed: {
    label: 'Confirmada',
    color: 'bg-green-100 text-green-800 border-green-300',
    icon: CheckCircle
  },
  checked_in: {
    label: 'Paciente llegó',
    color: 'bg-teal-100 text-teal-800 border-teal-300',
    icon: UserCheck
  },
  in_progress: {
    label: 'En consulta',
    color: 'bg-purple-100 text-purple-800 border-purple-300',
    icon: Clock
  },
  no_show: {
    label: 'No se presentó',
    color: 'bg-pink-100 text-pink-800 border-pink-300',
    icon: AlertCircle
  }
};

export function UpcomingAppointments({ className, onViewAll }: UpcomingAppointmentsProps) {
  const [appointments, setAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState<string | null>(null);

  const fetchTodayAppointments = async () => {
    try {
      setLoading(true);
      const today = format(new Date(), 'yyyy-MM-dd');
      
      const params = new URLSearchParams({
        dateFrom: today,
        dateTo: today,
        orderBy: 'startTime',
        orderDirection: 'asc',
        limit: '10' // Solo las próximas 10 citas
      });

      const response = await fetch(`/api/appointments?${params}`);
      const data = await response.json();

      if (response.ok) {
        // Filtrar solo citas relevantes (no completadas ni canceladas)
        const relevantAppointments = (data.data || []).filter((apt: any) => 
          !['completed', 'cancelled'].includes(apt.status)
        );
        setAppointments(relevantAppointments);
      }
    } catch (error) {
      console.error('Error fetching appointments:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTodayAppointments();
    // Refrescar cada 2 minutos
    const interval = setInterval(fetchTodayAppointments, 120000);
    return () => clearInterval(interval);
  }, []);

  const handleQuickAction = async (appointmentId: string, action: string) => {
    setProcessing(appointmentId);
    
    try {
      let response;
      let successMessage = '';

      switch (action) {
        case 'confirm':
          response = await fetch(`/api/appointments/${appointmentId}`, {
            method: 'PUT',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ status: 'confirmed' })
          });
          successMessage = 'Cita confirmada';
          break;
          
        case 'checkin':
          response = await fetch(`/api/appointments/${appointmentId}/check-in`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
          });
          successMessage = 'Llegada registrada';
          break;
          
        case 'noshow':
          response = await fetch(`/api/appointments/${appointmentId}/no-show`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ reason: 'Marcado desde dashboard' })
          });
          successMessage = 'Marcado como no show';
          break;
      }

      if (response?.ok) {
        toast.success(successMessage);
        setTimeout(() => {
          fetchTodayAppointments();
        }, 300);
      } else {
        const error = await response?.json();
        toast.error(error?.error || 'Error al procesar acción');
      }
    } catch (error) {
      toast.error('Error de conexión');
    } finally {
      setProcessing(null);
    }
  };

  const getQuickActions = (appointment: any) => {
    const isProcessing = processing === appointment.id;
    
    switch (appointment.status) {
      case 'scheduled':
        return (
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleQuickAction(appointment.id, 'confirm')}
            disabled={isProcessing}
            className="text-green-600 border-green-300 hover:bg-green-50"
          >
            {isProcessing ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <CheckCircle className="h-3 w-3 mr-1" />
            )}
            Confirmar
          </Button>
        );
        
      case 'confirmed':
        return (
          <div className="flex gap-1">
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleQuickAction(appointment.id, 'checkin')}
              disabled={isProcessing}
              className="text-teal-600 border-teal-300 hover:bg-teal-50"
            >
              {isProcessing ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <UserCheck className="h-3 w-3 mr-1" />
              )}
              Check-in
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleQuickAction(appointment.id, 'noshow')}
              disabled={isProcessing}
              className="text-orange-600 border-orange-300 hover:bg-orange-50"
            >
              <AlertCircle className="h-3 w-3" />
            </Button>
          </div>
        );
        
      default:
        return null;
    }
  };

  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Próximas Citas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <RefreshCw className="h-6 w-6 animate-spin text-gray-400" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Clock className="h-5 w-5 text-blue-600" />
            Próximas Citas
            {appointments.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {appointments.length}
              </Badge>
            )}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="ghost"
              onClick={fetchTodayAppointments}
              disabled={loading}
            >
              <RefreshCw className={cn(
                "h-4 w-4",
                loading && "animate-spin"
              )} />
            </Button>
            {onViewAll && (
              <Button
                size="sm"
                variant="outline"
                onClick={onViewAll}
              >
                Ver todas
              </Button>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {appointments.length === 0 ? (
          <div className="text-center text-gray-500 py-8">
            <Calendar className="h-12 w-12 mx-auto mb-3 text-gray-300" />
            <p className="text-sm">No hay citas pendientes para hoy</p>
          </div>
        ) : (
          appointments.map((appointment: any) => {
            const statusInfo = statusConfig[appointment.status as keyof typeof statusConfig] || statusConfig.scheduled;
            const StatusIcon = statusInfo.icon;
            
            return (
              <div
                key={appointment.id}
                className="flex items-center gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors"
              >
                <div className="flex-shrink-0">
                  <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center">
                    <StatusIcon className="h-5 w-5 text-gray-600" />
                  </div>
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <p className="font-medium text-sm truncate">
                      {appointment.title || 'Sin título'}
                    </p>
                    <Badge className={cn('text-xs', statusInfo.color)}>
                      {statusInfo.label}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-3 text-xs text-gray-500">
                    <span className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {format(new Date(appointment.startTime), 'HH:mm', { locale: es })}
                    </span>
                    {appointment.patientName && (
                      <span className="flex items-center gap-1 truncate">
                        <User className="h-3 w-3" />
                        {appointment.patientName}
                      </span>
                    )}
                  </div>
                </div>
                
                <div className="flex-shrink-0">
                  {getQuickActions(appointment)}
                </div>
              </div>
            );
          })
        )}
      </CardContent>
    </Card>
  );
}