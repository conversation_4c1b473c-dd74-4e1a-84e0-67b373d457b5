'use client';

import { useState, useEffect } from 'react';
import { 
  Clock, 
  Users, 
  RefreshCw, 
  AlertCircle,
  User,
  Calendar,
  Stethoscope,
  Home,
  ArrowUp,
  ArrowDown,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { toast } from 'sonner';

interface WaitingPatient {
  id: string;
  patientFirstName: string;
  patientLastName: string;
  doctorFirstName: string;
  doctorLastName: string;
  serviceName: string;
  consultoryName: string;
  scheduledDate: string;
  startTime: string;
  endTime: string;
  checkedInAt: string;
  title: string;
}

export default function WaitingRoomPage() {
  const [waitingPatients, setWaitingPatients] = useState<WaitingPatient[]>([]);
  const [loading, setLoading] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [stats, setStats] = useState({
    total: 0,
    avgWaitTime: 0,
    longestWait: 0,
    urgent: 0
  });

  const fetchWaitingPatients = async () => {
    try {
      const today = format(new Date(), 'yyyy-MM-dd');
      const response = await fetch(`/api/appointments?status=checked_in&dateFrom=${today}&dateTo=${today}&orderBy=checkedInAt&orderDirection=asc&_t=${Date.now()}`);
      if (response.ok) {
        const data = await response.json();
        const waitingList = data.data || [];
        setWaitingPatients(waitingList);
        
        // Calcular estadísticas
        if (waitingList.length > 0) {
          const now = Date.now();
          const waitTimes = waitingList.map((patient: WaitingPatient) => {
            const arrivalTime = new Date(patient.checkedInAt || patient.startTime).getTime();
            return Math.floor((now - arrivalTime) / (1000 * 60));
          });
          
          const avgWait = Math.floor(waitTimes.reduce((a, b) => a + b, 0) / waitTimes.length);
          const longestWait = Math.max(...waitTimes);
          const urgentCount = waitTimes.filter(time => time > 15).length;
          
          setStats({
            total: waitingList.length,
            avgWaitTime: avgWait,
            longestWait: longestWait,
            urgent: urgentCount
          });
        } else {
          setStats({ total: 0, avgWaitTime: 0, longestWait: 0, urgent: 0 });
        }
      }
    } catch (error) {
      console.error('Error fetching waiting patients:', error);
      toast.error('Error al cargar la sala de espera');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWaitingPatients();
    
    if (autoRefresh) {
      const interval = setInterval(fetchWaitingPatients, 30000); // Actualizar cada 30 segundos
      return () => clearInterval(interval);
    }
  }, [autoRefresh]);

  const handleStartConsultation = async (appointment: WaitingPatient) => {
    try {
      const response = await fetch(`/api/appointments/${appointment.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status: 'in_progress' })
      });

      if (response.ok) {
        toast.success('Consulta iniciada');
        fetchWaitingPatients();
      } else {
        toast.error('Error al iniciar consulta');
      }
    } catch (error) {
      toast.error('Error de conexión');
    }
  };

  const getWaitingTime = (checkedInAt: string, startTime: string) => {
    const arrivalTime = new Date(checkedInAt || startTime);
    const waitingMinutes = Math.floor((Date.now() - arrivalTime.getTime()) / (1000 * 60));
    return waitingMinutes;
  };

  const getWaitingStatus = (waitingMinutes: number) => {
    if (waitingMinutes > 30) return { level: 'critical', color: 'bg-red-500', textColor: 'text-red-700' };
    if (waitingMinutes > 15) return { level: 'warning', color: 'bg-orange-500', textColor: 'text-orange-700' };
    return { level: 'normal', color: 'bg-green-500', textColor: 'text-green-700' };
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900 flex items-center gap-3">
            <Clock className="h-8 w-8 text-blue-600" />
            Sala de Espera Virtual
          </h1>
          <p className="text-base text-gray-600 mt-2">
            Monitoreo en tiempo real de pacientes en espera
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button
            variant="outline"
            onClick={() => setAutoRefresh(!autoRefresh)}
            className={autoRefresh ? 'bg-green-50 border-green-300 text-green-700' : ''}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${autoRefresh ? 'animate-spin' : ''}`} />
            Auto-actualizar {autoRefresh ? 'ON' : 'OFF'}
          </Button>
          <Button onClick={fetchWaitingPatients} disabled={loading}>
            <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pacientes en Espera</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.total}</div>
            <p className="text-xs text-muted-foreground">Total actual</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tiempo Promedio</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.avgWaitTime}min</div>
            <p className="text-xs text-muted-foreground">Espera promedio</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Mayor Espera</CardTitle>
            <AlertCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.longestWait}min</div>
            <p className="text-xs text-muted-foreground">Tiempo máximo</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Casos Urgentes</CardTitle>
            <XCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.urgent}</div>
            <p className="text-xs text-muted-foreground">Más de 15min</p>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Pacientes */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Pacientes en Espera</span>
            {autoRefresh && (
              <Badge variant="outline" className="bg-green-50 text-green-700">
                Actualización automática cada 30s
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-3 text-gray-500">Cargando sala de espera...</span>
            </div>
          ) : waitingPatients.length === 0 ? (
            <div className="text-center py-12">
              <CheckCircle className="h-16 w-16 text-green-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                ¡Sala de espera vacía!
              </h3>
              <p className="text-gray-500">
                No hay pacientes esperando actualmente
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {waitingPatients.map((patient, index) => {
                const waitingMinutes = getWaitingTime(patient.checkedInAt, patient.startTime);
                const status = getWaitingStatus(waitingMinutes);
                const appointmentTime = new Date(patient.startTime);
                const arrivalTime = new Date(patient.checkedInAt || patient.startTime);
                const isLate = arrivalTime > appointmentTime;
                
                return (
                  <div 
                    key={patient.id}
                    className={`p-6 rounded-xl border-2 transition-all duration-200 ${
                      status.level === 'critical' 
                        ? 'bg-red-50 border-red-200 shadow-lg' 
                        : status.level === 'warning'
                        ? 'bg-orange-50 border-orange-200'
                        : 'bg-green-50 border-green-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4">
                        {/* Número de turno */}
                        <div className={`h-12 w-12 rounded-full flex items-center justify-center text-white font-bold text-lg ${status.color}`}>
                          {index + 1}
                        </div>
                        
                        {/* Información del paciente */}
                        <div className="flex-1">
                          <div className="flex items-center gap-3 mb-2">
                            <h3 className="text-lg font-semibold text-gray-900">
                              {patient.patientFirstName} {patient.patientLastName}
                            </h3>
                            {status.level === 'critical' && (
                              <Badge variant="destructive" className="animate-pulse">
                                URGENTE
                              </Badge>
                            )}
                            {status.level === 'warning' && (
                              <Badge className="bg-orange-500 text-white">
                                PRIORIDAD
                              </Badge>
                            )}
                            {isLate && (
                              <Badge variant="outline" className="text-red-600 border-red-300">
                                Llegó tarde
                              </Badge>
                            )}
                          </div>
                          
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div className="flex items-center gap-2">
                              <Stethoscope className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                Dr. {patient.doctorFirstName} {patient.doctorLastName}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Home className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">{patient.consultoryName}</span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                Cita: {format(appointmentTime, 'HH:mm')}
                              </span>
                            </div>
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-gray-400" />
                              <span className="text-gray-600">
                                Llegó: {format(arrivalTime, 'HH:mm')}
                              </span>
                            </div>
                          </div>
                          
                          <div className="mt-2">
                            <p className="text-sm text-gray-700">
                              <strong>Servicio:</strong> {patient.serviceName || patient.title}
                            </p>
                          </div>
                        </div>
                      </div>
                      
                      {/* Panel de tiempo y acciones */}
                      <div className="text-right space-y-3">
                        <div className={`text-2xl font-bold ${status.textColor}`}>
                          {waitingMinutes}min
                        </div>
                        <div className="text-xs text-gray-500 mb-3">
                          en espera
                        </div>
                        
                        <Button
                          onClick={() => handleStartConsultation(patient)}
                          className="bg-blue-600 hover:bg-blue-700 text-white"
                          size="sm"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Iniciar Consulta
                        </Button>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}