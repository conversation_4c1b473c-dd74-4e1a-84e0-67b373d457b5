# 🎨 Paleta de Colores - Sistema de Gestión Clínica

## Colores Primarios

### 🔵 Azul Principal
- **Código**: `#3D4E80`
- **Uso**: Texto principal, t<PERSON><PERSON><PERSON>, información importante
- **Aplicación**: Headers, nombres, textos destacados, fondos de botones primarios

### 🌊 <PERSON><PERSON> (<PERSON>)
- **Código**: `#50bed2`
- **Uso**: Iconos principales, botones de acción, elementos destacados
- **Aplicación**: Check-in, confirmaciones, iconos de contacto, botones informativos

### 🌸 Rosa Consultorio
- **Código**: `#ea6cb0`
- **Uso**: Identidad de marca, elementos especiales del consultorio
- **Aplicación**: Nombre del consultorio, avatares, hover de botones importantes

## Colores Secundarios

### 💙 Azul Grisáceo
- **Código**: `#ADB6CA`
- **Uso**: Elementos neutrales, bordes, fondos sutiles
- **Aplicación**: Botones secundarios, bordes, estados inactivos

### 💛 Amarillo Claro
- **Código**: `#FCEEA8`
- **Uso**: Códigos de confirmación, alertas informativas
- **Aplicación**: Fondos de códigos, elementos de confirmación

### 💛 Amarillo
- **Código**: `#F8E59A`
- **Uso**: Bordes de elementos informativos, estados pendientes
- **Aplicación**: Bordes de códigos, badges de estados pendientes

### 🌸 Rosa Suave
- **Código**: `#FAC9D1`
- **Uso**: Alertas suaves, pagos pendientes, notificaciones
- **Aplicación**: Fondos de alertas, botones de pago, badges de notificaciones

## Sistema de Botones

### 🔘 Botones Primarios (Sólidos)

#### Botón Principal (Azul)
```css
bg-[#3D4E80] hover:bg-[#3D4E80]/80 text-white
```
- **Uso**: Pre-check, acciones principales del sistema
- **Hover**: 80% de opacidad para efecto suave

#### Botón Cyan
```css
bg-[#50bed2] hover:bg-[#50bed2]/80 text-white
```
- **Uso**: Check-in, agendar citas, acciones informativas
- **Hover**: 80% de opacidad para efecto suave

#### Botón Rosa (Pagos/Alertas)
```css
bg-[#FAC9D1] hover:bg-[#ea6cb0] hover:text-white text-[#3D4E80]
```
- **Uso**: Pagos, acciones importantes, cancelaciones
- **Hover**: Transición a rosa consultorio con texto blanco

### 🔳 Botones Outline (Bordes)

#### Hover Rosa (Botones Importantes)
```css
border-[#ADB6CA] text-[#3D4E80] hover:bg-[#ea6cb0]/10 hover:border-[#ea6cb0] hover:text-[#ea6cb0]
```
- **Uso**: Mi Expediente, Historial, Cancelar
- **Aplicación**: Acciones relacionadas con el consultorio

#### Hover Cyan (Botones Informativos)
```css
border-[#ADB6CA] text-[#3D4E80] hover:bg-[#50bed2]/10 hover:border-[#50bed2] hover:text-[#50bed2]
```
- **Uso**: Ver Detalles, Ver Pagos, Cerrar
- **Aplicación**: Acciones informativas y navegación

### 📱 Estados de Botones

#### Botones Deshabilitados
```css
border-[#50bed2] text-[#50bed2] hover:bg-[#50bed2]/10
```
- **Uso**: Acciones completadas (Pre-check, Check-in)

#### Botones de Carga
```css
Loader2 className="h-4 w-4 mr-2 animate-spin"
```
- **Color**: Heredan el color del texto del botón

## Sistema de Tarjetas

### 📋 Tarjetas Principales

#### Tarjeta de Citas
```css
border-[#3D4E80]/20 bg-[#3D4E80]/5
```
- **Borde**: Azul principal con 20% opacidad
- **Fondo**: Azul principal con 5% opacidad

#### Tarjeta de Pagos (Al día)
```css
border-[#50bed2]/30 bg-[#50bed2]/10
```
- **Borde**: Cyan con 30% opacidad
- **Fondo**: Cyan con 10% opacidad

#### Tarjeta de Pagos (Pendientes)
```css
border-[#FAC9D1]/60 bg-[#FAC9D1]/20
```
- **Borde**: Rosa suave con 60% opacidad
- **Fondo**: Rosa suave con 20% opacidad

### 🔲 Tarjetas de Servicios (Grid 2x2)

#### Recetas
```css
border-[#ea6cb0]/20 bg-[#ea6cb0]/5
```
- **Color**: Rosa consultorio

#### Mensajes
```css
border-[#50bed2]/30 bg-[#50bed2]/10
```
- **Color**: Cyan

#### Última Receta
```css
border-[#FCEEA8]/50 bg-[#FCEEA8]/20
```
- **Color**: Amarillo claro

#### Mis Consultas
```css
border-[#ADB6CA]/40 bg-[#ADB6CA]/15
```
- **Color**: Azul grisáceo

## Sistema de Estados

### ✅ Estados de Confirmación

#### Pre-check Completado
```css
bg-[#50bed2]/10 border-[#50bed2]/30
icon: text-[#50bed2]
text: text-[#3D4E80]
```

#### Check-in Completado
```css
bg-[#ADB6CA]/20 border-[#ADB6CA]/40
icon: text-[#50bed2]
text: text-[#3D4E80]
```

### 📝 Códigos de Confirmación
```css
bg-[#FCEEA8]/40 border-[#F8E59A]
text: text-[#3D4E80]
```

### 🏷️ Badges de Estado

#### Confirmada
```css
bg-[#50bed2]/20 text-[#50bed2] border-[#50bed2]/30
```

#### Pendiente
```css
bg-[#F8E59A]/50 text-[#3D4E80] border-[#F8E59A]
```

#### Programada
```css
bg-[#ADB6CA]/30 text-[#3D4E80] border-[#ADB6CA]
```

## Sistema de Iconos

### 📱 Iconos por Función

#### Contacto
- **Teléfono**: `text-[#50bed2]`
- **Email**: `text-[#50bed2]`

#### Acciones
- **Calendar**: `text-[#50bed2]`
- **User**: `text-[#3D4E80]`
- **CheckCircle**: `text-[#50bed2]`
- **UserCheck**: `text-[#50bed2]`

#### Servicios
- **Pill (Recetas)**: `text-[#ea6cb0]`
- **MessageSquare**: `text-[#50bed2]`
- **Download**: `text-[#3D4E80]`
- **FileText**: `text-[#3D4E80]`
- **CreditCard**: `text-[#50bed2]`

## Avatares y Perfiles

### 👤 Avatar del Usuario
```css
bg-[#ea6cb0]/20 text-[#3D4E80]
```
- **Fondo**: Rosa consultorio con 20% opacidad
- **Texto**: Azul principal

### 🎭 Círculos de Iconos
```css
bg-[COLOR]/20 → Para fondo del círculo
text-[COLOR] → Para el icono
```

## Modales y Overlays

### 🪟 Headers de Modales
```css
title: text-[#3D4E80]
icon: text-[#50bed2]
description: text-[#3D4E80]/70
```

### 📋 Secciones de Información

#### Información del Doctor
```css
bg-[#3D4E80]/10 border-[#3D4E80]/20
```

#### Fecha y Hora
```css
bg-[#50bed2]/10 border-[#50bed2]/30
```

#### Código de Confirmación
```css
bg-[#FCEEA8]/30 border-[#F8E59A]/50
```

#### Estado de la Cita
```css
bg-[#ADB6CA]/20 border-[#ADB6CA]/40
```

## Reglas de Opacidad

### 📊 Escalas de Transparencia

#### Fondos
- **5%**: Fondos muy sutiles (tarjetas principales)
- **10%**: Fondos suaves (hover effects)
- **15%**: Fondos medio (secciones destacadas)
- **20%**: Fondos visibles (avatares, alerts)

#### Bordes
- **20%**: Bordes sutiles
- **30%**: Bordes normales
- **40%**: Bordes destacados
- **60%**: Bordes prominentes

#### Texto
- **70%**: Texto secundario
- **80%**: Texto menos importante
- **100%**: Texto principal

## 🔄 Transiciones y Animaciones

### ⚡ Hover Effects
```css
transition-all duration-300
hover:shadow-lg
```

### 🔄 Loading States
```css
animate-spin → Para loaders
animate-pulse → Para placeholders
```

## 📋 Checklist de Implementación

- [ ] Aplicar colores primarios a textos principales
- [ ] Configurar sistema de hover en botones
- [ ] Implementar estados de badges
- [ ] Establecer colores de iconos por función
- [ ] Configurar fondos de tarjetas con opacidades
- [ ] Aplicar colores de identidad de marca
- [ ] Verificar contraste y accesibilidad
- [ ] Documentar variaciones específicas por componente

---

*Este documento define el sistema de colores completo para mantener consistencia visual en toda la aplicación. Actualizar este documento cada vez que se modifique la paleta de colores.*