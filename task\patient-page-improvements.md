# Plan de Mejora - Página de Pacientes

## Estado Actual
La página actual muestra información básica del paciente pero no está integrada con el diseño real de la base de datos.

## Problemas Identificados
1. **No muestra información de medicalRecords** - La página actual no muestra si el paciente tiene expediente médico
2. **No muestra información del guardian** - Si el paciente es menor, no se muestra info del encargado
3. **No hay acceso directo al expediente** - No hay botón para ir al expediente médico
4. **Información médica vacía** - La tab de información médica muestra arrays vacíos
5. **No usa las relaciones correctas de BD** - No aprovecha las tablas guardianPatientRelations, medicalRecords

## Mejoras Propuestas

### 1. Actualizar API /api/patients/[id]
- Agregar consulta para obtener medicalRecords del paciente
- Agregar consulta para obtener guardian si el paciente es menor
- Incluir información de preferredDoctor
- Incluir última consulta y próxima cita

### 2. Mejorar Interfaz de Usuario
- Agregar sección prominente con info del expediente médico
- Mostrar número de expediente
- Mostrar médico de cabecera
- Mostrar consultorio asignado
- Agregar botón "Ver Expediente Completo"

### 3. Información del Guardian
- Si el paciente es menor, mostrar card con info del guardian
- Incluir nombre, teléfono y relación
- Mostrar si puede tomar decisiones médicas

### 4. Información Médica Real
- Obtener alergias del medicalRecord
- Obtener condiciones crónicas
- Obtener medicamentos actuales
- Mostrar historial resumido

### 5. Actividad Reciente
- Mostrar últimas consultas
- Próximas citas programadas
- Documentos recientes

## Estructura de Datos Necesaria

```typescript
interface EnhancedPatientData {
  // Datos actuales del paciente
  ...patient,
  
  // Expediente médico
  medicalRecord?: {
    id: string;
    recordNumber: string;
    status: string;
    openDate: Date;
    primaryDoctor: {
      id: string;
      name: string;
    };
    consultory: {
      id: string;
      name: string;
    };
  };
  
  // Guardian (si es menor)
  guardian?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    relationship: string;
    canMakeDecisions: boolean;
  };
  
  // Información médica
  medicalInfo: {
    allergies: Array<{
      allergen: string;
      severity: string;
    }>;
    chronicConditions: Array<{
      condition: string;
      diagnosisDate: Date;
    }>;
    currentMedications: Array<{
      medication: string;
      dosage: string;
    }>;
  };
  
  // Actividad
  recentActivity: {
    lastConsultation?: Date;
    nextAppointment?: Date;
    totalConsultations: number;
  };
}
```