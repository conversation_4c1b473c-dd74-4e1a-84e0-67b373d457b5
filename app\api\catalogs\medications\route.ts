import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { medications } from '@/db/schema';
import { eq, and, desc, ilike, count, or } from 'drizzle-orm';
import { nanoid } from 'nanoid';

// GET /api/catalogs/medications - Listar medicamentos con filtros
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || 'all'; // all, active, inactive
    const therapeuticClass = searchParams.get('therapeuticClass') || '';
    const dosageForm = searchParams.get('dosageForm') || '';
    const controlled = searchParams.get('controlled') || 'all'; // all, controlled, non-controlled
    const prescription = searchParams.get('prescription') || 'all'; // all, required, not-required
    const sortBy = searchParams.get('sortBy') || 'name';
    const sortOrder = searchParams.get('sortOrder') || 'asc';

    const offset = (page - 1) * limit;

    // Construir condiciones WHERE
    const conditions = [];
    
    if (status === 'active') {
      conditions.push(eq(medications.isActive, true));
    } else if (status === 'inactive') {
      conditions.push(eq(medications.isActive, false));
    }

    if (therapeuticClass && therapeuticClass !== 'all') {
      conditions.push(eq(medications.therapeuticClass, therapeuticClass));
    }

    if (dosageForm && dosageForm !== 'all') {
      conditions.push(eq(medications.dosageForm, dosageForm));
    }

    if (controlled === 'controlled') {
      conditions.push(eq(medications.isControlled, true));
    } else if (controlled === 'non-controlled') {
      conditions.push(eq(medications.isControlled, false));
    }

    if (prescription === 'required') {
      conditions.push(eq(medications.requiresPrescription, true));
    } else if (prescription === 'not-required') {
      conditions.push(eq(medications.requiresPrescription, false));
    }

    if (search) {
      // Buscar en nombre, nombre genérico, principio activo y marca
      conditions.push(
        or(
          ilike(medications.name, `%${search}%`),
          ilike(medications.genericName, `%${search}%`),
          ilike(medications.activeIngredient, `%${search}%`),
          ilike(medications.brandName, `%${search}%`)
        )
      );
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Obtener total de registros
    const totalResult = await db
      .select({ count: count() })
      .from(medications)
      .where(whereClause);

    const total = totalResult[0]?.count || 0;
    const totalPages = Math.ceil(total / limit);

    // Obtener datos con paginación
    const orderColumn = sortBy === 'generic' ? medications.genericName : 
                       sortBy === 'therapeutic' ? medications.therapeuticClass :
                       sortBy === 'form' ? medications.dosageForm :
                       sortBy === 'controlled' ? medications.isControlled :
                       medications.name;

    const data = await db
      .select()
      .from(medications)
      .where(whereClause)
      .orderBy(sortOrder === 'desc' ? desc(orderColumn) : orderColumn)
      .limit(limit)
      .offset(offset);

    // Obtener listas para filtros
    const therapeuticClasses = await db
      .selectDistinct({ therapeuticClass: medications.therapeuticClass })
      .from(medications)
      .where(eq(medications.isActive, true))
      .orderBy(medications.therapeuticClass);

    const dosageForms = await db
      .selectDistinct({ dosageForm: medications.dosageForm })
      .from(medications)
      .where(eq(medications.isActive, true))
      .orderBy(medications.dosageForm);

    return NextResponse.json({
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
      filters: {
        therapeuticClasses: therapeuticClasses.map(t => t.therapeuticClass).filter(Boolean),
        dosageForms: dosageForms.map(d => d.dosageForm).filter(Boolean),
      },
    });

  } catch (error: any) {
    console.error('Error fetching medications:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST /api/catalogs/medications - Crear nuevo medicamento
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();

    // Validaciones básicas
    if (!body.name || !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre del medicamento es requerido' },
        { status: 400 }
      );
    }

    if (!body.genericName || !body.genericName.trim()) {
      return NextResponse.json(
        { error: 'El nombre genérico es requerido' },
        { status: 400 }
      );
    }

    if (!body.activeIngredient || !body.activeIngredient.trim()) {
      return NextResponse.json(
        { error: 'El principio activo es requerido' },
        { status: 400 }
      );
    }

    if (!body.dosageForm || !body.dosageForm.trim()) {
      return NextResponse.json(
        { error: 'La forma farmacéutica es requerida' },
        { status: 400 }
      );
    }

    if (!body.strength || !body.strength.trim()) {
      return NextResponse.json(
        { error: 'La concentración/fuerza es requerida' },
        { status: 400 }
      );
    }

    if (!body.therapeuticClass || !body.therapeuticClass.trim()) {
      return NextResponse.json(
        { error: 'La clase terapéutica es requerida' },
        { status: 400 }
      );
    }

    // Verificar si ya existe un medicamento con el mismo nombre genérico y concentración
    const existingMedication = await db
      .select()
      .from(medications)
      .where(
        and(
          eq(medications.genericName, body.genericName.trim()),
          eq(medications.strength, body.strength.trim()),
          eq(medications.dosageForm, body.dosageForm.trim())
        )
      )
      .limit(1);

    if (existingMedication.length > 0) {
      return NextResponse.json(
        { error: 'Ya existe un medicamento con el mismo nombre genérico, concentración y forma farmacéutica' },
        { status: 400 }
      );
    }

    // Crear nuevo medicamento
    const newMedication = {
      id: nanoid(),
      name: body.name.trim(),
      genericName: body.genericName.trim(),
      brandName: body.brandName?.trim() || null,
      activeIngredient: body.activeIngredient.trim(),
      dosageForm: body.dosageForm.trim(),
      strength: body.strength.trim(),
      concentration: body.concentration?.trim() || null,
      therapeuticClass: body.therapeuticClass.trim(),
      pharmacologicalGroup: body.pharmacologicalGroup?.trim() || null,
      atcCode: body.atcCode?.trim() || null,
      indication: body.indication?.trim() || null,
      contraindications: Array.isArray(body.contraindications) ? body.contraindications : [],
      sideEffects: Array.isArray(body.sideEffects) ? body.sideEffects : [],
      dosageInstructions: body.dosageInstructions?.trim() || null,
      warnings: Array.isArray(body.warnings) ? body.warnings : [],
      requiresPrescription: body.requiresPrescription === true,
      isControlled: body.isControlled === true,
      controlledCategory: body.controlledCategory?.trim() || null,
      manufacturer: body.manufacturer?.trim() || null,
      barcode: body.barcode?.trim() || null,
      ndc: body.ndc?.trim() || null,
      presentation: body.presentation?.trim() || null,
      storageConditions: body.storageConditions?.trim() || null,
      shelfLife: body.shelfLife?.trim() || null,
      description: body.description?.trim() || null,
      notes: body.notes?.trim() || null,
      order: body.order || 0,
      isActive: body.isActive !== undefined ? body.isActive : true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await db.insert(medications).values(newMedication);

    return NextResponse.json({
      success: true,
      message: 'Medicamento creado exitosamente',
      data: newMedication,
    });

  } catch (error: any) {
    console.error('Error creating medication:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// PUT /api/catalogs/medications - Actualizar medicamento
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();

    if (!body.id) {
      return NextResponse.json(
        { error: 'El ID es requerido para actualizar' },
        { status: 400 }
      );
    }

    // Verificar que el medicamento existe
    const existingMedication = await db
      .select()
      .from(medications)
      .where(eq(medications.id, body.id))
      .limit(1);

    if (!existingMedication.length) {
      return NextResponse.json(
        { error: 'Medicamento no encontrado' },
        { status: 404 }
      );
    }

    // Validaciones básicas para actualización
    if (body.name && !body.name.trim()) {
      return NextResponse.json(
        { error: 'El nombre del medicamento no puede estar vacío' },
        { status: 400 }
      );
    }

    if (body.genericName && !body.genericName.trim()) {
      return NextResponse.json(
        { error: 'El nombre genérico no puede estar vacío' },
        { status: 400 }
      );
    }

    // Preparar datos para actualizar
    const updateData: any = {
      updatedAt: new Date(),
    };

    // Solo actualizar campos que se proporcionan
    if (body.name !== undefined) updateData.name = body.name.trim();
    if (body.genericName !== undefined) updateData.genericName = body.genericName.trim();
    if (body.brandName !== undefined) updateData.brandName = body.brandName?.trim() || null;
    if (body.activeIngredient !== undefined) updateData.activeIngredient = body.activeIngredient.trim();
    if (body.dosageForm !== undefined) updateData.dosageForm = body.dosageForm.trim();
    if (body.strength !== undefined) updateData.strength = body.strength.trim();
    if (body.concentration !== undefined) updateData.concentration = body.concentration?.trim() || null;
    if (body.therapeuticClass !== undefined) updateData.therapeuticClass = body.therapeuticClass.trim();
    if (body.pharmacologicalGroup !== undefined) updateData.pharmacologicalGroup = body.pharmacologicalGroup?.trim() || null;
    if (body.atcCode !== undefined) updateData.atcCode = body.atcCode?.trim() || null;
    if (body.indication !== undefined) updateData.indication = body.indication?.trim() || null;
    if (body.contraindications !== undefined) updateData.contraindications = Array.isArray(body.contraindications) ? body.contraindications : [];
    if (body.sideEffects !== undefined) updateData.sideEffects = Array.isArray(body.sideEffects) ? body.sideEffects : [];
    if (body.dosageInstructions !== undefined) updateData.dosageInstructions = body.dosageInstructions?.trim() || null;
    if (body.warnings !== undefined) updateData.warnings = Array.isArray(body.warnings) ? body.warnings : [];
    if (body.requiresPrescription !== undefined) updateData.requiresPrescription = body.requiresPrescription === true;
    if (body.isControlled !== undefined) updateData.isControlled = body.isControlled === true;
    if (body.controlledCategory !== undefined) updateData.controlledCategory = body.controlledCategory?.trim() || null;
    if (body.manufacturer !== undefined) updateData.manufacturer = body.manufacturer?.trim() || null;
    if (body.barcode !== undefined) updateData.barcode = body.barcode?.trim() || null;
    if (body.ndc !== undefined) updateData.ndc = body.ndc?.trim() || null;
    if (body.presentation !== undefined) updateData.presentation = body.presentation?.trim() || null;
    if (body.storageConditions !== undefined) updateData.storageConditions = body.storageConditions?.trim() || null;
    if (body.shelfLife !== undefined) updateData.shelfLife = body.shelfLife?.trim() || null;
    if (body.description !== undefined) updateData.description = body.description?.trim() || null;
    if (body.notes !== undefined) updateData.notes = body.notes?.trim() || null;
    if (body.order !== undefined) updateData.order = body.order || 0;
    if (body.isActive !== undefined) updateData.isActive = body.isActive;

    // Actualizar medicamento
    await db
      .update(medications)
      .set(updateData)
      .where(eq(medications.id, body.id));

    return NextResponse.json({
      success: true,
      message: 'Medicamento actualizado exitosamente',
    });

  } catch (error: any) {
    console.error('Error updating medication:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}