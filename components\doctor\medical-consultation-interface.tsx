'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { 
  User, 
  Calendar,
  Clock,
  FileText,
  Heart,
  Stethoscope,
  Save,
  RefreshCw,
  AlertCircle
} from 'lucide-react';
import { PreCheckinSummary } from '@/components/pre-checkin/pre-checkin-summary';
import { MedicalRecordForm } from '@/components/medical-records/medical-record-form';
import { toast } from 'sonner';

interface Patient {
  id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: Date;
  age: number;
  email?: string;
}

interface Appointment {
  id: string;
  scheduledDate: string;
  duration: number;
  status: string;
  serviceName: string;
  preCheckinData?: any;
  preCheckinCompleted: boolean;
}

interface MedicalRecord {
  id?: string;
  demographics?: any;
  vitalSigns?: any;
  allergies?: any[];
  currentMedications?: any[];
  medicalEmergencyContact?: string;
  medicalEmergencyPhone?: string;
  doctorNotes?: string;
}

interface MedicalConsultationInterfaceProps {
  patient: Patient;
  appointment: Appointment;
  existingMedicalRecord?: MedicalRecord;
  onConsultationComplete: (consultationData: any) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
}

export function MedicalConsultationInterface({
  patient,
  appointment,
  existingMedicalRecord,
  onConsultationComplete,
  onCancel,
  loading = false
}: MedicalConsultationInterfaceProps) {
  const [activeTab, setActiveTab] = useState('precheckin');
  const [medicalRecord, setMedicalRecord] = useState<MedicalRecord | null>(existingMedicalRecord || null);
  const [isStructuringInfo, setIsStructuringInfo] = useState(false);
  const [consultationNotes, setConsultationNotes] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Procesar datos del pre-checkin para el resumen
  const preCheckinSummaryData = appointment.preCheckinData ? {
    attendance: appointment.preCheckinData.willAttend || 'yes',
    completedAt: appointment.preCheckinData.completedAt || new Date().toISOString(),
    mode: appointment.preCheckinData.mode || 'complete',
    hasSymptoms: appointment.preCheckinData.hasSymptoms || false,
    symptoms: appointment.preCheckinData.symptoms || '',
    takingMedications: appointment.preCheckinData.takingMedications || false,
    medications: appointment.preCheckinData.medications || '',
    hasAllergies: appointment.preCheckinData.hasAllergies || false,
    allergies: appointment.preCheckinData.allergies || '',
    contactInfo: {
      phone: appointment.preCheckinData.phone || '',
      emergencyContact: appointment.preCheckinData.emergencyContact || '',
      emergencyPhone: appointment.preCheckinData.emergencyPhone || ''
    },
    companionInfo: appointment.preCheckinData.companionName ? {
      name: appointment.preCheckinData.companionName,
      relationship: appointment.preCheckinData.companionRelationship || '',
      phone: appointment.preCheckinData.companionPhone || ''
    } : undefined,
    chiefComplaint: appointment.preCheckinData.chiefComplaint || '',
    additionalNotes: appointment.preCheckinData.additionalNotes || '',
    isDependent: patient.age < 18,
    isFirstVisit: !existingMedicalRecord
  } : {
    attendance: 'no' as const,
    completedAt: new Date().toISOString(),
    mode: 'none' as const,
    hasSymptoms: false,
    takingMedications: false,
    hasAllergies: false,
    chiefComplaint: '',
    isDependent: patient.age < 18,
    isFirstVisit: !existingMedicalRecord
  };

  const handleStructureInformation = async () => {
    if (!appointment.preCheckinData) return;
    
    setIsStructuringInfo(true);
    
    try {
      const response = await fetch('/api/medical-records/from-precheckin', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          patientId: patient.id,
          appointmentId: appointment.id,
          demographics: {
            bloodType: '',
            weight: '',
            height: ''
          },
          vitalSigns: {},
          allergies: appointment.preCheckinData.allergies ? [appointment.preCheckinData.allergies] : [],
          currentMedications: appointment.preCheckinData.medications ? [appointment.preCheckinData.medications] : [],
          medicalEmergencyContact: appointment.preCheckinData.emergencyContact || '',
          medicalEmergencyPhone: appointment.preCheckinData.emergencyPhone || '',
          doctorNotes: `Pre-checkin procesado automáticamente.\nSíntomas: ${appointment.preCheckinData.symptoms || 'Ninguno'}\nMotivo: ${appointment.preCheckinData.chiefComplaint || 'No especificado'}`
        }),
      });

      const result = await response.json();

      if (response.ok) {
        setMedicalRecord(result.data);
        setActiveTab('medical-record');
        toast.success('Información estructurada exitosamente');
      } else {
        toast.error(result.error || 'Error al estructurar información');
      }
    } catch (error) {
      console.error('Error structuring information:', error);
      toast.error('Error de conexión');
    } finally {
      setIsStructuringInfo(false);
    }
  };

  const handleMedicalRecordSubmit = async (medicalRecordData: any) => {
    try {
      let recordResponse;
      
      if (medicalRecord?.id) {
        // Actualizar expediente existente
        recordResponse = await fetch(`/api/medical-records/${medicalRecord.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...medicalRecordData,
            appointmentId: appointment.id
          }),
        });
      } else {
        // Crear desde pre-checkin
        recordResponse = await fetch('/api/medical-records/from-precheckin', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            ...medicalRecordData,
            appointmentId: appointment.id
          }),
        });
      }

      const recordResult = await recordResponse.json();

      if (recordResponse.ok) {
        setMedicalRecord(recordResult.data);
        setActiveTab('consultation');
        toast.success('Expediente médico actualizado');
      } else {
        throw new Error(recordResult.error || 'Error al guardar expediente');
      }
    } catch (error) {
      console.error('Error saving medical record:', error);
      throw error;
    }
  };

  const handleConsultationSubmit = async () => {
    setIsSubmitting(true);
    
    try {
      const consultationData = {
        appointmentId: appointment.id,
        patientId: patient.id,
        medicalRecordId: medicalRecord?.id,
        chiefComplaint: preCheckinSummaryData.chiefComplaint,
        consultationNotes,
        preCheckinData: appointment.preCheckinData,
        consultationDate: new Date().toISOString()
      };

      await onConsultationComplete(consultationData);
      toast.success('Consulta registrada exitosamente');
    } catch (error) {
      console.error('Error completing consultation:', error);
      toast.error('Error al completar consulta');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header con información del paciente y cita */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                <div>
                  <h2 className="text-xl font-semibold">
                    {patient.firstName} {patient.lastName}
                  </h2>
                  <p className="text-sm text-gray-600">
                    {patient.age} años • Paciente ID: {patient.id.slice(-8)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <div className="flex items-center gap-1">
                  <Calendar className="h-4 w-4" />
                  <span>{new Date(appointment.scheduledDate).toLocaleDateString('es-ES')}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  <span>{new Date(appointment.scheduledDate).toLocaleTimeString('es-ES', { 
                    hour: '2-digit', 
                    minute: '2-digit' 
                  })}</span>
                </div>
                <Badge variant={appointment.preCheckinCompleted ? 'default' : 'secondary'}>
                  {appointment.preCheckinCompleted ? 'Pre-checkin Completo' : 'Sin Pre-checkin'}
                </Badge>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Tabs de navegación */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="precheckin">Pre-checkin</TabsTrigger>
          <TabsTrigger value="medical-record">Expediente Médico</TabsTrigger>
          <TabsTrigger value="consultation">Consulta</TabsTrigger>
        </TabsList>

        {/* Pre-checkin Summary */}
        <TabsContent value="precheckin" className="space-y-4">
          <PreCheckinSummary
            data={preCheckinSummaryData}
            onStructureInformation={handleStructureInformation}
            onStartManualCapture={() => setActiveTab('medical-record')}
          />
          
          {isStructuringInfo && (
            <Card className="border-blue-200 bg-blue-50">
              <CardContent className="pt-4">
                <div className="flex items-center justify-center gap-2 text-blue-700">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Estructurando información del pre-checkin...</span>
                </div>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Medical Record Form */}
        <TabsContent value="medical-record" className="space-y-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-red-600" />
              <h3 className="text-lg font-semibold">
                {medicalRecord ? 'Actualizar Expediente Médico' : 'Crear Expediente Médico'}
              </h3>
            </div>
            {!medicalRecord && (
              <div className="flex items-center gap-2 text-amber-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                <span>Expediente nuevo - Completar información</span>
              </div>
            )}
          </div>

          <MedicalRecordForm
            patientId={patient.id}
            existingRecord={medicalRecord}
            preCheckinData={appointment.preCheckinData}
            onSubmit={handleMedicalRecordSubmit}
            onCancel={() => setActiveTab('precheckin')}
            loading={loading}
          />
        </TabsContent>

        {/* Consultation Notes */}
        <TabsContent value="consultation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Stethoscope className="h-5 w-5 text-green-600" />
                Notas de Consulta
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Resumen del motivo de consulta */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium text-gray-800 mb-2">Motivo de Consulta:</h4>
                <p className="text-gray-700">
                  {preCheckinSummaryData.chiefComplaint || 'No especificado en pre-checkin'}
                </p>
              </div>

              {/* Área de notas de la consulta */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700">
                  Notas de la Consulta Médica *
                </label>
                <textarea
                  value={consultationNotes}
                  onChange={(e) => setConsultationNotes(e.target.value)}
                  className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Descripción de la consulta, diagnóstico, tratamiento recomendado..."
                />
              </div>

              {/* Información del expediente médico */}
              {medicalRecord && (
                <div className="bg-green-50 p-4 rounded-lg">
                  <h4 className="font-medium text-green-800 mb-2">Expediente Médico:</h4>
                  <p className="text-sm text-green-700">
                    ✅ Expediente actualizado con información del pre-checkin
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Botones de acción */}
          <div className="flex justify-end gap-4">
            {onCancel && (
              <Button 
                variant="outline" 
                onClick={onCancel}
                disabled={isSubmitting}
              >
                Cancelar
              </Button>
            )}
            <Button 
              onClick={handleConsultationSubmit}
              disabled={isSubmitting || !consultationNotes.trim()}
              className="min-w-32"
            >
              {isSubmitting ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Guardando...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  Completar Consulta
                </>
              )}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}