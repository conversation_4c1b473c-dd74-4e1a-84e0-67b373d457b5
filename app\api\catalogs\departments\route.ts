import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { departments, countries } from '@/db/schema';
import { and, eq, ilike, or, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');
    const countryId = url.searchParams.get('countryId');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(ilike(departments.name, `%${search}%`));
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(departments.isActive, active === 'true'));
    }

    if (countryId) {
      conditions.push(eq(departments.countryId, parseInt(countryId)));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(departments)
      .where(whereClause);

    // Get paginated data with country info
    const departmentsData = await db
      .select({
        id: departments.id,
        name: departments.name,
        countryId: departments.countryId,
        countryName: countries.name,
        isActive: departments.isActive,
        createdAt: departments.createdAt,
        updatedAt: departments.updatedAt
      })
      .from(departments)
      .leftJoin(countries, eq(departments.countryId, countries.id))
      .where(whereClause)
      .orderBy(departments.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: departmentsData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching departments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch departments' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, countryId, isActive = true } = body;

    if (!name || !countryId) {
      return NextResponse.json(
        { error: 'Name and countryId are required' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(departments);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newDepartment = await db.insert(departments).values({
      id: nextId,
      name,
      countryId: parseInt(countryId),
      isActive
    }).returning();

    return NextResponse.json({ 
      success: true, 
      data: newDepartment[0],
      message: 'Departamento creado exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating department:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Error al crear el departamento',
        details: error instanceof Error ? error.message : 'Error desconocido'
      },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, countryId, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedDepartment = await db
      .update(departments)
      .set({
        name,
        countryId: countryId ? parseInt(countryId) : undefined,
        isActive,
      })
      .where(eq(departments.id, parseInt(id)))
      .returning();

    if (updatedDepartment.length === 0) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedDepartment[0]);
  } catch (error) {
    console.error('Error updating department:', error);
    return NextResponse.json(
      { error: 'Failed to update department' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedDepartment = await db
      .update(departments)
      .set({
        isActive: false,
      })
      .where(eq(departments.id, parseInt(id)))
      .returning();

    if (deletedDepartment.length === 0) {
      return NextResponse.json(
        { error: 'Department not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Department deleted successfully' });
  } catch (error) {
    console.error('Error deleting department:', error);
    return NextResponse.json(
      { error: 'Failed to delete department' },
      { status: 500 }
    );
  }
} 