'use client';

import { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON><PERSON>ooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Trash2, RefreshCw } from 'lucide-react';

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  itemToDelete: any;
  onDelete: (type: 'logical' | 'physical') => Promise<void>;
  isDeleting?: boolean;
  showPhysicalDelete?: boolean;
}

export function DeleteConfirmationDialog({
  open,
  onOpenChange,
  itemToDelete,
  onDelete,
  isDeleting = false,
  showPhysicalDelete = false
}: DeleteConfirmationDialogProps) {
  const [deletingType, setDeletingType] = useState<'logical' | 'physical' | null>(null);

  const handleDelete = async (type: 'logical' | 'physical') => {
    setDeletingType(type);
    try {
      await onDelete(type);
      onOpenChange(false);
    } catch (error) {
      // Error handled by parent
    } finally {
      setDeletingType(null);
    }
  };

  const isProcessing = deletingType !== null || isDeleting;

  return (
    <Dialog open={open} onOpenChange={!isProcessing ? onOpenChange : undefined}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Trash2 className="h-5 w-5 text-red-500" />
            Confirmar Eliminación
          </DialogTitle>
          <DialogDescription>
            Esta acción no se puede deshacer. Seleccione el tipo de eliminación.
          </DialogDescription>
        </DialogHeader>
        
        {itemToDelete && (
          <div className="py-4">
            {/* Información del elemento a eliminar */}
            <div className="p-4 bg-gray-50 rounded-lg mb-4">
              <p className="text-sm font-medium text-gray-900 mb-2">
                Cita a eliminar:
              </p>
              <p className="text-lg font-semibold text-gray-800">
                {itemToDelete.patientFirstName && itemToDelete.patientLastName 
                  ? `${itemToDelete.patientFirstName} ${itemToDelete.patientLastName}`
                  : 'Sin paciente'
                } - {new Date(itemToDelete.scheduledDate).toLocaleDateString()} {new Date(itemToDelete.startTime).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
              </p>
            </div>
            
            {/* Opciones de eliminación */}
            <div className="space-y-3">
              <div className="p-3 border border-orange-200 bg-orange-50 rounded-lg">
                <p className="text-sm font-medium text-orange-800 mb-1">
                  Eliminación Lógica (Recomendada)
                </p>
                <p className="text-xs text-orange-700">
                  Se marcará como cancelada pero se mantendrá para referencia histórica.
                </p>
              </div>
              
              <div className="p-3 border border-red-200 bg-red-50 rounded-lg">
                <p className="text-sm font-medium text-red-800 mb-1">
                  Eliminación Física (Permanente)
                </p>
                <p className="text-xs text-red-700">
                  Se eliminará completamente. Esta acción es irreversible.
                </p>
              </div>
            </div>
          </div>
        )}
        
        <DialogFooter className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            disabled={isProcessing}
          >
            Cancelar
          </Button>
          <Button 
            variant="outline" 
            className="bg-orange-500 text-white hover:bg-orange-600"
            onClick={() => handleDelete('logical')}
            disabled={isProcessing}
          >
            {deletingType === 'logical' ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Procesando...
              </>
            ) : (
              'Eliminar Lógicamente'
            )}
          </Button>
          <Button 
            variant="destructive"
            onClick={() => handleDelete('physical')}
            disabled={isProcessing}
          >
            {deletingType === 'physical' ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Procesando...
              </>
            ) : (
              'Eliminar Físicamente'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}