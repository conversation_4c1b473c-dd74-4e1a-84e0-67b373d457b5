import { NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { userRoles, medicalSpecialties } from '@/db/schema';
import { and, eq } from 'drizzle-orm';

export async function GET(request: Request) {
  try {
    console.log('📍 Iniciando GET /api/auth/current-user');
    
    // Obtener parámetros de la URL
    const url = new URL(request.url);
    const requestedUserId = url.searchParams.get('userId');
    
    const authData = await auth();
    console.log('🔑 AuthData:', { userId: authData?.userId, hasUser: !!authData?.userId, requestedUserId });
    
    if (!authData?.userId) {
      console.log('❌ No hay userId en authData');
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    // Usar el userId solicitado o el del usuario actual
    const targetUserId = requestedUserId || authData.userId;
    console.log('🔍 Buscando usuario en BD:', targetUserId);

    // Consulta con el patrón que sabemos que funciona
    try {
      const userInfo = await db.query.userRoles.findFirst({
        where: eq(userRoles.userId, targetUserId),
        with: {
          user: true,
          specialty: true
        }
      });

      console.log('🔍 Query result userInfo:', JSON.stringify(userInfo, null, 2));
      console.log('🔍 User imageUrl específico:', userInfo.user.imageUrl);
      console.log('🔍 User image específico:', userInfo.user.image);

      if (!userInfo) {
        console.log('❌ Usuario no encontrado para userId:', targetUserId);
        return NextResponse.json({ 
          error: 'Usuario no encontrado',
          userId: targetUserId
        }, { status: 404 });
      }

      const response = {
        success: true,
        data: {
          id: userInfo.user.id,
          firstName: userInfo.user.firstName,
          lastName: userInfo.user.lastName,
          email: userInfo.user.email,
          imageUrl: userInfo.user.image,
          name: userInfo.user.name,
          role: userInfo.role,
          medicalSpecialty: userInfo.specialty?.name || null,
          specialtyId: userInfo.specialtyId,
          status: userInfo.status
        }
      };

      console.log('✅ Enviando respuesta:', JSON.stringify(response, null, 2));
      console.log('🖼️ ImageURL específico:', userInfo.user.image);
      
      return NextResponse.json(response);
      
    } catch (queryError) {
      console.error('❌ Error en la consulta a BD:', queryError);
      throw queryError;
    }

  } catch (error) {
    console.error('Error al obtener información del usuario:', error);
    console.error('Stack trace:', error.stack);
    return NextResponse.json(
      { 
        error: 'Error al cargar información del usuario',
        details: process.env.NODE_ENV === 'development' ? error.message : undefined
      },
      { status: 500 }
    );
  }
}