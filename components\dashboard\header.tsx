'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { UserButtonWithRoles } from '@/components/auth/user-button-with-roles';
import { Bell, Search, Menu, Settings, X, Stethoscope, Users, User, Baby } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useSidebar } from './sidebar-context';
import { Badge } from '@/components/ui/badge';
import { NotificationBell } from '@/components/notifications/notification-bell';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRouter, usePathname } from 'next/navigation';

export function Header() {
  const { toggleMobileSidebar, isCollapsed } = useSidebar();
  const [showMobileSearch, setShowMobileSearch] = useState(false);
  const { user } = useUser();
  const [userInfo, setUserInfo] = useState(null);
  const [availableRoles, setAvailableRoles] = useState([]);
  const router = useRouter();
  const pathname = usePathname();
  
  // Obtener información del usuario desde nuestra BD
  const fetchUserInfo = async () => {
    try {
      if (!user?.id) return;
      
      const response = await fetch('/api/auth/current-user');
      const data = await response.json();
      
      if (response.ok && data.success) {
        setUserInfo(data.data);
      }
    } catch (error) {
      console.error('Error al obtener información del usuario:', error);
    }
  };

  // Obtener roles disponibles
  const fetchAvailableRoles = async () => {
    try {
      if (!user?.id) return;
      
      const response = await fetch('/api/auth/user-contexts');
      if (!response.ok) return;
      
      const data = await response.json();
      const contexts = data.contexts;
      
      const roles = [];
      
      // Roles de trabajo
      if (contexts.work.isDoctor) {
        roles.push({
          role: 'doctor',
          label: 'Médico',
          icon: Stethoscope,
          url: '/dashboard/doctor'
        });
      }
      
      if (contexts.work.isAssistant) {
        roles.push({
          role: 'assistant',
          label: 'Asistente',
          icon: Users,
          url: '/dashboard/assistant'
        });
      }
      
      // Roles personales
      if (contexts.personal.isPatient) {
        roles.push({
          role: 'patient',
          label: 'Paciente',
          icon: User,
          url: '/dashboard/patient'
        });
      }
      
      if (contexts.personal.isGuardian) {
        roles.push({
          role: 'guardian',
          label: 'Encargado',
          icon: Baby,
          url: '/dashboard/guardian'
        });
      }
      
      // Solo mostrar si hay múltiples roles
      if (roles.length > 1) {
        setAvailableRoles(roles.filter(role => !pathname.includes(role.role)));
      }
      
    } catch (error) {
      console.error('Error al obtener roles disponibles:', error);
    }
  };

  useEffect(() => {
    if (user?.id) {
      fetchUserInfo();
      fetchAvailableRoles();
    }
  }, [user?.id, pathname]);
  
  const userName = userInfo 
    ? `${userInfo.firstName || ''} ${userInfo.lastName || ''}`.trim() || userInfo.name || 'Usuario'
    : 'Cargando...';
  const userRole = userInfo?.role || 'usuario';
  
  // Mapear roles a títulos más amigables
  const getRoleTitle = (role: string) => {
    const roleMap: { [key: string]: string } = {
      'admin': 'Administrador',
      'doctor': 'Doctor',
      'assistant': 'Asistente Médico',
      'patient': 'Paciente',
      'guardian': 'Tutor Legal',
      'provider': 'Proveedor'
    };
    return roleMap[role] || role.charAt(0).toUpperCase() + role.slice(1);
  };
  
  return (
    <>
      {/* Mobile Search Overlay */}
      {showMobileSearch && (
        <div className="fixed inset-0 z-50 bg-white md:hidden">
          <div className="flex items-center p-4 border-b">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowMobileSearch(false)}
              className="mr-2 text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 w-10 p-0 rounded-xl"
            >
              <X className="h-5 w-5" />
            </Button>
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <Input
                type="text"
                placeholder="Buscar pacientes, citas..."
                className="pl-10 h-10 w-full bg-gray-50 border-gray-200 focus:bg-white focus:border-emerald-500 focus:ring-emerald-500 text-sm rounded-xl font-medium placeholder:text-gray-400"
                autoFocus
              />
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm text-gray-500">Escribe para buscar pacientes, citas o expedientes...</p>
          </div>
        </div>
      )}
      
      <header className="h-16 md:h-20 lg:h-24 bg-white border-b border-gray-200 px-4 md:px-6 lg:px-8 flex items-center justify-between shadow-sm">
        {/* Left side - Mobile menu and search */}
        <div className="flex items-center space-x-2 md:space-x-4 lg:space-x-8 flex-1">
          {/* Mobile sidebar toggle - Only on mobile */}
          <Button 
            variant="ghost" 
            size="lg" 
            className="text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 w-10 md:hidden rounded-xl"
            onClick={toggleMobileSidebar}
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Search - Hidden on mobile */}
          <div className="hidden md:flex flex-1 max-w-2xl">
            <div className="relative w-full">
              <Search className="absolute left-3 md:left-4 lg:left-5 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 md:h-5 lg:h-6 w-4 md:w-5 lg:w-6" />
              <Input
                type="text"
                placeholder="Buscar pacientes, citas..."
                className="pl-10 md:pl-12 lg:pl-14 h-10 md:h-12 lg:h-14 w-full bg-gray-50 border-gray-200 focus:bg-white focus:border-emerald-500 focus:ring-emerald-500 text-sm md:text-base lg:text-lg rounded-xl md:rounded-xl lg:rounded-2xl font-medium placeholder:text-gray-400"
              />
            </div>
          </div>
          
          {/* Mobile Search Icon */}
          <Button
            variant="ghost"
            size="sm"
            className="md:hidden text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 w-10 p-0 rounded-xl"
            onClick={() => setShowMobileSearch(true)}
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>

      {/* Right side - Actions and User */}
      <div className="flex items-center space-x-2 md:space-x-4 lg:space-x-6">
        {/* Role Switcher - Hidden on mobile */}
        {availableRoles.length > 0 ? (
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="lg"
                className="hidden md:flex text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 md:h-12 w-10 md:w-12 p-0 rounded-xl"
              >
                <Settings className="h-5 md:h-6 w-5 md:w-6" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>Cambiar a:</DropdownMenuLabel>
              <DropdownMenuSeparator />
              {availableRoles.map((role) => {
                const IconComponent = role.icon;
                return (
                  <DropdownMenuItem
                    key={role.role}
                    onClick={() => router.push(role.url)}
                    className="cursor-pointer"
                  >
                    <IconComponent className="mr-2 h-4 w-4" />
                    <span>{role.label}</span>
                  </DropdownMenuItem>
                );
              })}
            </DropdownMenuContent>
          </DropdownMenu>
        ) : (
          <Button
            variant="ghost"
            size="lg"
            className="hidden md:flex text-gray-600 hover:text-gray-900 hover:bg-gray-100 h-10 md:h-12 w-10 md:w-12 p-0 rounded-xl"
          >
            <Settings className="h-5 md:h-6 w-5 md:w-6" />
          </Button>
        )}

        {/* Notifications */}
        <NotificationBell />

        {/* Divider - Hidden on mobile */}
        <div className="hidden md:block h-8 md:h-10 w-px bg-gray-200" />

        {/* User Profile */}
        <div className="flex items-center space-x-2 md:space-x-4">
          <div className="text-right hidden lg:block">
            <p className="text-base lg:text-lg font-semibold text-gray-900">{userName}</p>
            <p className="text-xs lg:text-sm text-gray-500 font-medium">{getRoleTitle(userRole)}</p>
          </div>
          <div className="scale-100 md:scale-110 lg:scale-125">
            <UserButtonWithRoles 
              appearance={{
                elements: {
                  avatarBox: "w-10 h-10 md:w-11 md:h-11 lg:w-12 lg:h-12 rounded-xl md:rounded-xl lg:rounded-2xl border-2 border-gray-200 shadow-lg"
                }
              }}
            />
          </div>
        </div>
      </div>
    </header>
    </>
  );
}