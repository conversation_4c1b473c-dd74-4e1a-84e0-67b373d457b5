import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { countries } from '@/db/schema';
import { eq } from 'drizzle-orm';

// POST - Toggle status de país (activar/desactivar)
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { userId, sessionClaims } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Verificar permisos de admin
    const role = sessionClaims?.metadata?.role;
    if (role !== 'admin') {
      return NextResponse.json({ 
        error: 'Permisos insuficientes. Solo administradores pueden cambiar el estado de países.' 
      }, { status: 403 });
    }

    const { id } = await params;

    // Verificar que el país existe
    const existingCountry = await db
      .select()
      .from(countries)
      .where(eq(countries.id, id))
      .limit(1);

    if (existingCountry.length === 0) {
      return NextResponse.json({ error: 'País no encontrado' }, { status: 404 });
    }

    const currentCountry = existingCountry[0];
    const newStatus = !currentCountry.isActive;

    // Actualizar el estado
    const [updatedCountry] = await db
      .update(countries)
      .set({
        isActive: newStatus,
        updatedAt: new Date()
      })
      .where(eq(countries.id, id))
      .returning();

    return NextResponse.json({ 
      success: true, 
      data: updatedCountry,
      message: `País ${newStatus ? 'activado' : 'desactivado'} exitosamente`
    });

  } catch (error) {
    console.error('Error toggling country status:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}