'use client';

import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useUser } from '@clerk/nextjs';
import { toast } from 'sonner';

export interface UserWorkContext {
  isDoctor?: {
    specialty: string;
    consultory: string;
    consultoryId: string;
  };
  isAssistant?: {
    forDoctors: Array<{ id: string; name: string }>;
  };
  isProvider?: {
    company: string;
    serviceType: string;
  };
}

export interface UserPersonalContext {
  isPatient?: {
    hasActiveRole: boolean;
    appointments: Array<{
      id: string;
      date: string;
      doctor: string;
      status: string;
    }>;
    appointmentCount: number;
  };
  isGuardian?: {
    dependents: Array<{
      id: string;
      name: string;
      age: number;
      relationship: string;
    }>;
    totalDependents: number;
  };
}

export interface QuickAccessItem {
  type: 'appointment' | 'precheckin' | 'task';
  priority: 'high' | 'medium' | 'low';
  title: string;
  action: string;
  data: any;
}

export interface UserContextData {
  user: {
    id: string;
    name: string;
    email: string;
    firstName: string;
    lastName: string;
  };
  contexts: {
    work: UserWorkContext;
    personal: UserPersonalContext;
  };
  quickAccess: QuickAccessItem[];
  currentContext: 'assistant' | 'doctor' | 'patient' | 'guardian' | 'provider' | 'full' | 'pending';
  hasMultipleContexts: boolean;
}

interface UserContextsContextValue {
  data: UserContextData | null;
  loading: boolean;
  error: string | null;
  currentContext: string;
  setCurrentContext: (context: string) => void;
  refreshContexts: () => Promise<void>;
  switchContext: (newContext: string) => Promise<void>;
}

const UserContextsContext = createContext<UserContextsContextValue | undefined>(undefined);

export function UserContextsProvider({ children }: { children: ReactNode }) {
  const { user, isLoaded } = useUser();
  const [data, setData] = useState<UserContextData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentContext, setCurrentContext] = useState<string>('full');

  const fetchContexts = async () => {
    if (!user?.id) return;


    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/auth/user-contexts', {
        headers: {
          'X-User-Context': currentContext,
        },
      });

      if (!response.ok) {
        throw new Error('Error al cargar contextos');
      }

      const contextData = await response.json();
      setData(contextData);

      // Si el usuario está pendiente, no establecer contextos
      if (contextData.isPending) {
        setCurrentContext('pending');
        return;
      }

      // Si tiene un solo contexto, establecerlo automáticamente
      if (!contextData.hasMultipleContexts) {
        const contexts = contextData.contexts;
        if (contexts.work.isDoctor) setCurrentContext('doctor');
        else if (contexts.work.isAssistant) setCurrentContext('assistant');
        else if (contexts.work.isProvider) setCurrentContext('provider');
        else if (contexts.personal.isPatient) setCurrentContext('patient');
        else if (contexts.personal.isGuardian) setCurrentContext('guardian');
      }

    } catch (err) {
      console.error('Error fetching contexts:', err);
      setError(err instanceof Error ? err.message : 'Error desconocido');
      toast.error('Error al cargar contextos de usuario');
    } finally {
      setLoading(false);
    }
  };

  const switchContext = async (newContext: string) => {
    const previousContext = currentContext;
    setCurrentContext(newContext);

    // Guardar en localStorage
    localStorage.setItem('userContext', newContext);

    // Log del cambio de contexto (para auditoría)
    try {
      await fetch('/api/auth/log-context-switch', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fromContext: previousContext,
          toContext: newContext,
          timestamp: new Date().toISOString(),
        }),
      });
    } catch (error) {
      console.error('Error logging context switch:', error);
    }

    // Mostrar notificación
    const contextNames: Record<string, string> = {
      doctor: 'Médico',
      assistant: 'Asistente',
      provider: 'Proveedor',
      patient: 'Paciente',
      guardian: 'Encargado',
      full: 'Completo',
      pending: 'Pendiente',
    };

    toast.success(`Cambiado a modo ${contextNames[newContext] || newContext}`);

    // Recargar datos con nuevo contexto
    await fetchContexts();
  };

  // Cargar contexto guardado
  useEffect(() => {
    const savedContext = localStorage.getItem('userContext');
    if (savedContext) {
      setCurrentContext(savedContext);
    }
  }, []);

  // Cargar contextos cuando el usuario esté disponible
  useEffect(() => {
    if (isLoaded && user) {
      fetchContexts();
    }
  }, [isLoaded, user]);

  return (
    <UserContextsContext.Provider
      value={{
        data,
        loading,
        error,
        currentContext,
        setCurrentContext,
        refreshContexts: fetchContexts,
        switchContext,
      }}
    >
      {children}
    </UserContextsContext.Provider>
  );
}

export function useUserContexts() {
  const context = useContext(UserContextsContext);
  if (context === undefined) {
    throw new Error('useUserContexts must be used within a UserContextsProvider');
  }
  return context;
}

// Hook auxiliar para obtener el contexto actual de trabajo
export function useWorkContext() {
  const { data, currentContext } = useUserContexts();
  
  if (!data) return null;

  switch (currentContext) {
    case 'doctor':
      return { type: 'doctor', ...data.contexts.work.isDoctor };
    case 'assistant':
      return { type: 'assistant', ...data.contexts.work.isAssistant };
    case 'provider':
      return { type: 'provider', ...data.contexts.work.isProvider };
    default:
      return null;
  }
}

// Hook auxiliar para obtener el contexto personal
export function usePersonalContext() {
  const { data, currentContext } = useUserContexts();
  
  if (!data) return null;

  switch (currentContext) {
    case 'patient':
      return { type: 'patient', ...data.contexts.personal.isPatient };
    case 'guardian':
      return { type: 'guardian', ...data.contexts.personal.isGuardian };
    default:
      return null;
  }
}