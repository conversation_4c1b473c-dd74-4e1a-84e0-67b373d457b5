-- Migración: Sistema de Expedientes Clínicos Pediátricos
-- Fecha: 2025-01-21

-- 1. EXPEDIENTES CLÍNICOS PRINCIPALES
CREATE TABLE IF NOT EXISTS medical_records (
  id TEXT PRIMARY KEY,
  patient_id TEXT NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
  consultory_id TEXT NOT NULL REFERENCES consultories(id),
  primary_doctor_id TEXT NOT NULL REFERENCES "user"(id),
  
  -- Información del expediente
  record_number TEXT UNIQUE NOT NULL,
  open_date TIMESTAMP DEFAULT NOW() NOT NULL,
  status TEXT DEFAULT 'active', -- active, inactive, transferred, archived
  
  -- Datos demográficos resumidos (cache del paciente)
  patient_summary JSONB, -- fullName, dateOfBirth, age, gender, bloodType, allergies
  
  -- Configuración
  is_minor BOOLEAN DEFAULT false,
  guardian_info JSONB, -- nombre, parentesco, teléfono, email
  
  -- Metadata de acceso
  last_access_date TIMESTAMP,
  total_consultations INTEGER DEFAULT 0,
  
  -- Audi<PERSON><PERSON>
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by TEXT NOT NULL REFERENCES "user"(id),
  updated_by TEXT NOT NULL REFERENCES "user"(id)
);

-- 2. CONSULTAS MÉDICAS (HISTORIALES)
CREATE TABLE IF NOT EXISTS medical_consultations (
  id TEXT PRIMARY KEY,
  medical_record_id TEXT NOT NULL REFERENCES medical_records(id) ON DELETE CASCADE,
  appointment_id TEXT REFERENCES appointments(id), -- referencia opcional a cita
  doctor_id TEXT NOT NULL REFERENCES "user"(id),
  consultory_id TEXT NOT NULL REFERENCES consultories(id),
  
  -- Datos de la consulta
  consultation_date TIMESTAMP NOT NULL,
  consultation_type TEXT NOT NULL, -- first_time, follow_up, control, emergency, telemedicine
  chief_complaint TEXT NOT NULL, -- motivo principal
  current_illness TEXT, -- enfermedad actual
  
  -- Examen físico y signos vitales (JSON por flexibilidad)
  vital_signs JSONB, -- peso, talla, temperatura, FC, FR, PA, satO2, IMC calculado
  physical_exam JSONB, -- examen por sistemas
  
  -- Diagnósticos y tratamiento
  diagnoses JSONB, -- array de diagnósticos
  treatment JSONB, -- plan de tratamiento
  prescriptions JSONB, -- recetas/medicamentos
  
  -- Seguimiento
  recommendations TEXT,
  next_appointment TIMESTAMP,
  follow_up_instructions TEXT,
  
  -- Adjuntos
  attachments JSONB, -- URLs de documentos/imágenes
  
  -- Estado de la consulta
  status TEXT DEFAULT 'completed', -- draft, completed, reviewed, billed
  
  -- Auditoría
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  created_by TEXT NOT NULL REFERENCES "user"(id),
  updated_by TEXT NOT NULL REFERENCES "user"(id)
);

-- 3. ANTECEDENTES MÉDICOS (INTEGRADO CON CATÁLOGOS EXISTENTES)
CREATE TABLE IF NOT EXISTS patient_medical_history (
  id TEXT PRIMARY KEY,
  medical_record_id TEXT NOT NULL REFERENCES medical_records(id) ON DELETE CASCADE,
  
  -- Antecedentes patológicos (del catálogo existente)
  pathological_history JSONB, -- [{historyId, historyName, diagnosedDate, severity, status, notes, treatingDoctor}]
  
  -- Antecedentes no patológicos (del catálogo existente)  
  non_pathological_history JSONB, -- [{historyId, historyName, value, startDate, endDate, notes}]
  
  -- Antecedentes familiares
  family_history JSONB, -- [{relationship, condition, age, notes}]
  
  -- Alergias (usando catálogo medications + otros)
  allergies JSONB, -- [{allergen, type, reaction, severity, diagnosedDate, notes}]
  
  -- Hospitalizaciones previas
  hospitalizations JSONB, -- [{date, hospital, reason, duration, complications, notes}]
  
  -- Cirugías previas
  surgeries JSONB, -- [{date, procedure, surgeon, hospital, complications, notes}]
  
  -- Vacunación (usando datos específicos)
  vaccinations JSONB, -- [{vaccine, date, doseNumber, lot, administrator, reactions, nextDueDate}]
  
  -- Auditoría
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  updated_by TEXT NOT NULL REFERENCES "user"(id)
);

-- 4. DOCUMENTOS MÉDICOS
CREATE TABLE IF NOT EXISTS medical_documents (
  id TEXT PRIMARY KEY,
  medical_record_id TEXT NOT NULL REFERENCES medical_records(id) ON DELETE CASCADE,
  consultation_id TEXT REFERENCES medical_consultations(id), -- si pertenece a consulta específica
  
  -- Información del documento
  file_name TEXT NOT NULL,
  original_name TEXT NOT NULL,
  file_type TEXT NOT NULL, -- PDF, JPG, PNG, DICOM, etc.
  file_size INTEGER NOT NULL, -- bytes
  file_path TEXT NOT NULL, -- ruta en el storage
  
  -- Metadata del documento
  document_type TEXT NOT NULL, -- lab_result, imaging, prescription, certificate, referral, consent, other
  title TEXT NOT NULL,
  description TEXT,
  document_date TIMESTAMP NOT NULL, -- fecha del documento (no upload)
  
  -- Información médica
  related_diagnosis TEXT,
  laboratory TEXT, -- lab que emitió
  physician TEXT, -- médico que solicitó
  
  -- Control de acceso
  visibility TEXT DEFAULT 'doctor_only', -- doctor_only, medical_staff, patient_visible
  is_confidential BOOLEAN DEFAULT false,
  
  -- Estado
  status TEXT DEFAULT 'pending_review', -- pending_review, reviewed, filed, archived
  reviewed_by TEXT REFERENCES "user"(id),
  reviewed_date TIMESTAMP,
  review_notes TEXT,
  
  -- Auditoría
  uploaded_at TIMESTAMP DEFAULT NOW(),
  uploaded_by TEXT NOT NULL REFERENCES "user"(id),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- ÍNDICES PARA EXPEDIENTES CLÍNICOS
CREATE INDEX IF NOT EXISTS medical_records_patient_idx ON medical_records(patient_id);
CREATE INDEX IF NOT EXISTS medical_records_doctor_idx ON medical_records(primary_doctor_id);
CREATE INDEX IF NOT EXISTS medical_records_consultory_idx ON medical_records(consultory_id);
CREATE INDEX IF NOT EXISTS medical_records_status_idx ON medical_records(status);
CREATE INDEX IF NOT EXISTS medical_records_created_idx ON medical_records(created_at);

-- ÍNDICES PARA CONSULTAS MÉDICAS
CREATE INDEX IF NOT EXISTS medical_consultations_record_idx ON medical_consultations(medical_record_id);
CREATE INDEX IF NOT EXISTS medical_consultations_doctor_idx ON medical_consultations(doctor_id);
CREATE INDEX IF NOT EXISTS medical_consultations_appointment_idx ON medical_consultations(appointment_id);
CREATE INDEX IF NOT EXISTS medical_consultations_date_idx ON medical_consultations(consultation_date);
CREATE INDEX IF NOT EXISTS medical_consultations_status_idx ON medical_consultations(status);
CREATE INDEX IF NOT EXISTS medical_consultations_type_idx ON medical_consultations(consultation_type);

-- ÍNDICES PARA ANTECEDENTES MÉDICOS
CREATE UNIQUE INDEX IF NOT EXISTS patient_medical_history_record_idx ON patient_medical_history(medical_record_id);
CREATE INDEX IF NOT EXISTS patient_medical_history_updated_idx ON patient_medical_history(updated_at);

-- ÍNDICES PARA DOCUMENTOS MÉDICOS
CREATE INDEX IF NOT EXISTS medical_documents_record_idx ON medical_documents(medical_record_id);
CREATE INDEX IF NOT EXISTS medical_documents_consultation_idx ON medical_documents(consultation_id);
CREATE INDEX IF NOT EXISTS medical_documents_type_idx ON medical_documents(document_type);
CREATE INDEX IF NOT EXISTS medical_documents_status_idx ON medical_documents(status);
CREATE INDEX IF NOT EXISTS medical_documents_uploaded_idx ON medical_documents(uploaded_at);
CREATE INDEX IF NOT EXISTS medical_documents_date_idx ON medical_documents(document_date);