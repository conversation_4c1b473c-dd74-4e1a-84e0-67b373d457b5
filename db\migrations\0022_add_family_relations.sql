-- Crear tabla para relaciones familiares
CREATE TABLE IF NOT EXISTS family_relations (
  id VARCHAR(255) PRIMARY KEY,
  guardian_user_id VARCHAR(255) NOT NULL,
  dependent_user_id VARCHAR(255) NOT NULL,
  relationship_type VARCHAR(50) NOT NULL, -- 'parent', 'guardian', 'spouse', etc.
  is_primary_contact B<PERSON><PERSON>EAN DEFAULT false,
  can_make_appointments BOOLEAN DEFAULT true,
  can_view_records BOOLEAN DEFAULT true,
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  
  FOREIGN KEY (guardian_user_id) REFERENCES "user"(id) ON DELETE CASCADE,
  FOREIGN KEY (dependent_user_id) REFERENCES "user"(id) ON DELETE CASCADE,
  
  -- Evitar relaciones duplicadas
  UNIQUE(guardian_user_id, dependent_user_id)
);

-- <PERSON><PERSON>r índices para performance
CREATE INDEX IF NOT EXISTS idx_family_relations_guardian ON family_relations(guardian_user_id);
CREATE INDEX IF NOT EXISTS idx_family_relations_dependent ON family_relations(dependent_user_id);
CREATE INDEX IF NOT EXISTS idx_family_relations_status ON family_relations(status);

-- Agregar campos para invitaciones automáticas
CREATE TABLE IF NOT EXISTS patient_invitations (
  id VARCHAR(255) PRIMARY KEY,
  patient_user_id VARCHAR(255) NOT NULL,
  guardian_email VARCHAR(255) NOT NULL,
  invitation_token VARCHAR(255) UNIQUE NOT NULL,
  status VARCHAR(20) DEFAULT 'pending', -- 'pending', 'accepted', 'expired'
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  accepted_at TIMESTAMP,
  
  FOREIGN KEY (patient_user_id) REFERENCES "user"(id) ON DELETE CASCADE
);

CREATE INDEX IF NOT EXISTS idx_patient_invitations_token ON patient_invitations(invitation_token);
CREATE INDEX IF NOT EXISTS idx_patient_invitations_status ON patient_invitations(status);
CREATE INDEX IF NOT EXISTS idx_patient_invitations_guardian_email ON patient_invitations(guardian_email);