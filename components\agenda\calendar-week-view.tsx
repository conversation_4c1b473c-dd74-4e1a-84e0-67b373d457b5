'use client';

import { useState } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { format, startOfWeek, endOfWeek, eachDayOfInterval, addMinutes, setHours, setMinutes } from 'date-fns';
import { es } from 'date-fns/locale';
import { formatTimeInConsultoryTimezone, formatDateInConsultoryTimezone, formatLocalTimeFromUTC, formatLocalDateFromUTC } from '@/lib/timezone-utils';
import { Calendar, Clock, Plus, User, Stethoscope } from 'lucide-react';
import { cn } from '@/lib/utils';
import { AppointmentActionsMenu } from './appointment-actions-menu';
import { AppointmentTooltip } from './appointment-tooltip';

interface CalendarWeekViewProps {
  appointments: any[];
  currentDate: Date;
  onTimeSlotClick?: (date: Date, time: string) => void;
  onAppointmentClick?: (appointment: any) => void;
  onAppointmentEdit?: (appointment: any) => void;
  onAppointmentConfirm?: (appointment: any) => void;
  onAppointmentCancel?: (appointment: any) => void;
  onAppointmentDelete?: (appointment: any) => void;
  onAppointmentCheckIn?: (appointment: any) => void;
  onAppointmentNoShow?: (appointment: any) => void;
  onAppointmentStart?: (appointment: any) => void;
  onAppointmentComplete?: (appointment: any) => void;
  onAppointmentRevertNoShow?: (appointment: any) => void;
  onAppointmentRevertCompleted?: (appointment: any) => void;
  onViewPreCheckin?: (appointment: any) => void;
  onViewConsultation?: (appointment: any) => void;
  loading?: boolean;
  userRole?: 'doctor' | 'assistant' | 'admin';
}

const statusColors = {
  scheduled: 'bg-blue-100 border-l-blue-500 text-blue-800',
  confirmed: 'bg-[#50bed2]/10 border-l-[#50bed2] text-[#50bed2]', 
  in_progress: 'bg-purple-100 border-l-purple-500 text-purple-800',
  completed: 'bg-gray-100 border-l-gray-500 text-gray-700',
  cancelled: 'bg-red-100 border-l-red-500 text-red-700',
  urgent: 'bg-orange-100 border-l-orange-500 text-orange-800'
};

const timeSlots = [];
for (let hour = 7; hour <= 22; hour++) { // Extendido hasta 10 PM
  for (let minute = 0; minute < 60; minute += 30) {
    const time = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    timeSlots.push(time);
  }
}

export function CalendarWeekView({ 
  appointments, 
  currentDate, 
  onTimeSlotClick, 
  onAppointmentClick,
  onAppointmentEdit,
  onAppointmentConfirm,
  onAppointmentCancel,
  onAppointmentDelete,
  onAppointmentCheckIn,
  onAppointmentNoShow,
  onAppointmentStart,
  onAppointmentComplete,
  onAppointmentRevertNoShow,
  onAppointmentRevertCompleted,
  onViewPreCheckin,
  onViewConsultation,
  loading = false,
  userRole = 'assistant'
}: CalendarWeekViewProps) {
  const [hoveredSlot, setHoveredSlot] = useState<string | null>(null);

  // Obtener días de la semana
  const weekStart = startOfWeek(currentDate, { locale: es });
  const weekEnd = endOfWeek(currentDate, { locale: es });
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  // Agrupar citas por fecha y hora
  const appointmentsByDateAndTime = appointments.reduce((acc, appointment) => {
    const date = formatLocalDateFromUTC(appointment.scheduledDate || appointment.startTime, 'yyyy-MM-dd');
    const time = formatLocalTimeFromUTC(appointment.startTime);
    const key = `${date}-${time}`;
    
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(appointment);
    return acc;
  }, {} as Record<string, any[]>);

  // Obtener citas para un slot específico
  const getAppointmentsForSlot = (date: Date, time: string) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const key = `${dateStr}-${time}`;
    return appointmentsByDateAndTime[key] || [];
  };

  // Contar citas por día
  const getAppointmentCountForDay = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd');
    return appointments.filter(app =>
      formatLocalDateFromUTC(app.scheduledDate || app.startTime, 'yyyy-MM-dd') === dateStr
    ).length;
  };

  const handleSlotClick = (date: Date, time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    const slotDateTime = setMinutes(setHours(date, hours), minutes);
    onTimeSlotClick?.(slotDateTime, time);
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-96">
            <div className="text-center">
              <Clock className="h-8 w-8 animate-spin text-gray-400 mx-auto mb-2" />
              <p className="text-gray-500">Cargando calendario...</p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="overflow-hidden">
      <CardContent className="p-0">
        {/* Header de días de la semana */}
        <div className="grid grid-cols-8 border-b bg-gray-50">
          {/* Columna de horas */}
          <div className="py-2 px-4 border-r bg-gray-100">
            <div className="flex items-center gap-2">
              <Calendar className="h-4 w-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Hora</span>
            </div>
          </div>
          
          {/* Columnas de días */}
          {weekDays.map((day) => {
            const isToday = format(day, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd');
            const appointmentCount = getAppointmentCountForDay(day);
            
            return (
              <div key={day.toString()} className="py-2 px-4 border-r text-center">
                <div className="space-y-1">
                  <div className="text-sm font-medium text-gray-600">
                    {format(day, 'EEE', { locale: es })}
                  </div>
                  <div className={cn(
                    "text-xl font-bold transition-colors",
                    isToday ? "text-blue-600" : "text-gray-800"
                  )}>
                    {format(day, 'd')}
                  </div>
                  {appointmentCount > 0 && (
                    <Badge variant="outline" className="text-xs">
                      {appointmentCount} citas
                    </Badge>
                  )}
                </div>
              </div>
            );
          })}
        </div>

        {/* Grid de horarios */}
        <div className="max-h-[600px] overflow-y-auto">
          {timeSlots.map((time) => (
            <div key={time} className="grid grid-cols-8 border-b min-h-[60px] hover:bg-gray-50/50 transition-colors">
              {/* Columna de hora */}
              <div className="p-3 border-r bg-gray-50 flex items-center justify-center">
                <span className="text-sm text-gray-600 font-medium">{time}</span>
              </div>
              
              {/* Columnas de días */}
              {weekDays.map((day) => {
                const slotKey = `${format(day, 'yyyy-MM-dd')}-${time}`;
                const slotAppointments = getAppointmentsForSlot(day, time);
                const hasAppointments = slotAppointments.length > 0;
                const isHovered = hoveredSlot === slotKey;
                
                return (
                  <div
                    key={slotKey}
                    className={cn(
                      "border-r p-1 relative cursor-pointer transition-all duration-200",
                      hasAppointments ? "bg-white" : "hover:bg-blue-50",
                      isHovered && !hasAppointments && "bg-blue-100 shadow-inner"
                    )}
                    onClick={() => !hasAppointments && handleSlotClick(day, time)}
                    onMouseEnter={() => !hasAppointments && setHoveredSlot(slotKey)}
                    onMouseLeave={() => setHoveredSlot(null)}
                  >
                    {/* Botón para crear cita (slot vacío) */}
                    {!hasAppointments && isHovered && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0 bg-blue-500 hover:bg-blue-600 text-white shadow-md"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    )}
                    
                    {/* Citas en este slot */}
                    {slotAppointments.map((appointment, index) => (
                      <AppointmentTooltip 
                        key={appointment.id} 
                        appointment={appointment}
                        side="right"
                      >
                        <div
                          className={cn(
                            "group mb-1 p-2 rounded-md border-l-4 cursor-pointer hover:shadow-md transition-all text-xs relative",
                            statusColors[appointment.status] || statusColors.scheduled,
                            index > 0 && "mt-1"
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            onAppointmentClick?.(appointment);
                          }}
                        >
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="font-medium truncate">
                              {appointment.patientFirstName && appointment.patientLastName 
                                ? `${appointment.patientFirstName} ${appointment.patientLastName}`
                                : appointment.activityTypeName || appointment.title
                              }
                            </div>
                            <div className="text-xs opacity-75 truncate mt-1">
                              {appointment.serviceName || appointment.title}
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <Clock className="h-3 w-3" />
                              <span>{formatLocalTimeFromUTC(appointment.startTime)}</span>
                              {appointment.doctorFirstName && (
                                <>
                                  <Stethoscope className="h-3 w-3 ml-1" />
                                  <span className="truncate">Dr. {appointment.doctorFirstName}</span>
                                </>
                              )}
                            </div>
                            <div className="flex items-center gap-1 mt-1">
                              <Badge 
                                variant="secondary" 
                                className={cn(
                                  "text-xs px-1.5 py-0.5 h-4",
                                  appointment.status === 'scheduled' && "bg-blue-100 text-blue-800 border-blue-200",
                                  appointment.status === 'pending_confirmation' && "bg-orange-100 text-orange-800 border-orange-200",
                                  appointment.status === 'confirmed' && "bg-[#50bed2]/10 text-[#50bed2] border-[#50bed2]/20",
                                  appointment.status === 'checked_in' && "bg-teal-100 text-teal-800 border-teal-200",
                                  appointment.status === 'in_progress' && "bg-purple-100 text-purple-800 border-purple-200",
                                  appointment.status === 'completed' && "bg-gray-100 text-gray-700 border-gray-200",
                                  appointment.status === 'cancelled' && "bg-red-100 text-red-700 border-red-200",
                                  appointment.status === 'no_show' && "bg-pink-100 text-pink-800 border-pink-200"
                                )}
                              >
                                {appointment.status === 'scheduled' && 'Programada'}
                                {appointment.status === 'pending_confirmation' && 'Pendiente de Confirmación'}
                                {appointment.status === 'confirmed' && 'Confirmada'}
                                {appointment.status === 'checked_in' && 'Paciente llegó'}
                                {appointment.status === 'in_progress' && 'En consulta'}
                                {appointment.status === 'completed' && 'Completada'}
                                {appointment.status === 'cancelled' && 'Cancelada'}
                                {appointment.status === 'no_show' && 'No asistió'}
                                {!['scheduled', 'pending_confirmation', 'confirmed', 'checked_in', 'in_progress', 'completed', 'cancelled', 'no_show'].includes(appointment.status) && 'Programada'}
                              </Badge>
                              {appointment.isEmergency && (
                                <Badge variant="destructive" className="text-xs">
                                  Urgente
                                </Badge>
                              )}
                            </div>
                          </div>
                          
                          <AppointmentActionsMenu
                            appointment={appointment}
                            onView={onAppointmentClick}
                            onEdit={onAppointmentEdit}
                            onConfirm={onAppointmentConfirm}
                            onCancel={onAppointmentCancel}
                            onDelete={onAppointmentDelete}
                            onCheckIn={onAppointmentCheckIn}
                            onNoShow={onAppointmentNoShow}
                            onStart={onAppointmentStart}
                            onComplete={onAppointmentComplete}
                            onRevertNoShow={onAppointmentRevertNoShow}
                            onRevertCompleted={onAppointmentRevertCompleted}
                            onViewPreCheckin={onViewPreCheckin}
                            onViewConsultation={onViewConsultation}
                            userRole={userRole}
                            className="ml-1 flex-shrink-0"
                          />
                        </div>
                        </div>
                      </AppointmentTooltip>
                    ))}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}