CREATE TABLE "appointments" (
	"id" text PRIMARY KEY NOT NULL,
	"patient_id" text NOT NULL,
	"doctor_id" text NOT NULL,
	"consultory_id" text NOT NULL,
	"scheduled_date" timestamp NOT NULL,
	"scheduled_time" text NOT NULL,
	"duration" integer DEFAULT 30,
	"appointment_type" text NOT NULL,
	"status" text DEFAULT 'scheduled',
	"appointment_data" jsonb,
	"confirmed_at" timestamp,
	"started_at" timestamp,
	"completed_at" timestamp,
	"cancelled_at" timestamp,
	"cancellation_reason" text,
	"created_by" text NOT NULL,
	"last_modified_by" text,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "countries" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "countries_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "departments" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"country_id" integer,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "medical_records" (
	"id" text PRIMARY KEY NOT NULL,
	"patient_id" text NOT NULL,
	"doctor_id" text NOT NULL,
	"consultory_id" text NOT NULL,
	"record_type" text NOT NULL,
	"appointment_id" text,
	"primary_diagnosis" text,
	"secondary_diagnoses" jsonb,
	"vitals" jsonb,
	"record_data" jsonb,
	"status" text DEFAULT 'draft',
	"created_by" text NOT NULL,
	"last_modified_by" text,
	"reviewed_by" text,
	"reviewed_at" timestamp,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "medical_specialties" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "medical_specialties_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "municipalities" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"department_id" integer,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "occupations" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "occupations_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "relationships" (
	"id" serial PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"code" text NOT NULL,
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "relationships_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "user_consultories" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"consultory_id" text NOT NULL,
	"role" text NOT NULL,
	"permissions" jsonb,
	"is_active" boolean DEFAULT true,
	"start_date" timestamp DEFAULT now(),
	"end_date" timestamp,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "user_documents" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"role" text,
	"document_type" text NOT NULL,
	"file_name" text NOT NULL,
	"file_path" text NOT NULL,
	"file_size" integer,
	"mime_type" text,
	"title" text,
	"description" text,
	"is_verified" boolean DEFAULT false,
	"verified_by" text,
	"verified_at" timestamp,
	"expiration_date" timestamp,
	"uploaded_by" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now()
);
--> statement-breakpoint
CREATE TABLE "user_roles" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"role" text NOT NULL,
	"status" text DEFAULT 'active',
	"role_data" jsonb,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	"activated_at" timestamp,
	"deactivated_at" timestamp
);
--> statement-breakpoint
CREATE TABLE "user_specialties" (
	"id" text PRIMARY KEY NOT NULL,
	"user_id" text NOT NULL,
	"specialty_id" integer NOT NULL,
	"is_primary" boolean DEFAULT false,
	"certification_date" timestamp,
	"certification_number" text,
	"is_active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now()
);
--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "requestedRoles" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "userData" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "rolesData" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "documentsData" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "approvedRoles" jsonb;--> statement-breakpoint
ALTER TABLE "registrationRequests" ADD COLUMN "rejectedRoles" jsonb;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "first_name" text NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "last_name" text NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "document_type" text NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "document_number" text NOT NULL;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "date_of_birth" timestamp;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "gender" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "phone" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "alternative_phone" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "country_id" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "department_id" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "municipality_id" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "occupation_id" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "address" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergency_contact" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergency_phone" text;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "emergency_relationship_id" integer;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "preferences" jsonb;--> statement-breakpoint
ALTER TABLE "user" ADD COLUMN "metadata" jsonb;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_patient_id_user_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_doctor_id_user_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_consultory_id_consultories_id_fk" FOREIGN KEY ("consultory_id") REFERENCES "public"."consultories"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_last_modified_by_user_id_fk" FOREIGN KEY ("last_modified_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "departments" ADD CONSTRAINT "departments_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_patient_id_user_id_fk" FOREIGN KEY ("patient_id") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_doctor_id_user_id_fk" FOREIGN KEY ("doctor_id") REFERENCES "public"."user"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_consultory_id_consultories_id_fk" FOREIGN KEY ("consultory_id") REFERENCES "public"."consultories"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_created_by_user_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_last_modified_by_user_id_fk" FOREIGN KEY ("last_modified_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_records" ADD CONSTRAINT "medical_records_reviewed_by_user_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "municipalities" ADD CONSTRAINT "municipalities_department_id_departments_id_fk" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consultories" ADD CONSTRAINT "user_consultories_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_consultories" ADD CONSTRAINT "user_consultories_consultory_id_consultories_id_fk" FOREIGN KEY ("consultory_id") REFERENCES "public"."consultories"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_documents" ADD CONSTRAINT "user_documents_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_documents" ADD CONSTRAINT "user_documents_verified_by_user_id_fk" FOREIGN KEY ("verified_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_documents" ADD CONSTRAINT "user_documents_uploaded_by_user_id_fk" FOREIGN KEY ("uploaded_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_roles" ADD CONSTRAINT "user_roles_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_specialties" ADD CONSTRAINT "user_specialties_user_id_user_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user_specialties" ADD CONSTRAINT "user_specialties_specialty_id_medical_specialties_id_fk" FOREIGN KEY ("specialty_id") REFERENCES "public"."medical_specialties"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "patient_appointments_idx" ON "appointments" USING btree ("patient_id","scheduled_date");--> statement-breakpoint
CREATE INDEX "doctor_appointments_idx" ON "appointments" USING btree ("doctor_id","scheduled_date");--> statement-breakpoint
CREATE INDEX "consultory_appointments_idx" ON "appointments" USING btree ("consultory_id","scheduled_date");--> statement-breakpoint
CREATE INDEX "appointment_status_idx" ON "appointments" USING btree ("status");--> statement-breakpoint
CREATE INDEX "appointment_type_idx" ON "appointments" USING btree ("appointment_type");--> statement-breakpoint
CREATE INDEX "appointment_datetime_idx" ON "appointments" USING btree ("scheduled_date","scheduled_time");--> statement-breakpoint
CREATE INDEX "patient_records_idx" ON "medical_records" USING btree ("patient_id");--> statement-breakpoint
CREATE INDEX "doctor_records_idx" ON "medical_records" USING btree ("doctor_id");--> statement-breakpoint
CREATE INDEX "consultory_records_idx" ON "medical_records" USING btree ("consultory_id");--> statement-breakpoint
CREATE INDEX "record_type_idx" ON "medical_records" USING btree ("record_type");--> statement-breakpoint
CREATE INDEX "record_status_idx" ON "medical_records" USING btree ("status");--> statement-breakpoint
CREATE INDEX "record_date_idx" ON "medical_records" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "primary_diagnosis_idx" ON "medical_records" USING btree ("primary_diagnosis");--> statement-breakpoint
CREATE UNIQUE INDEX "user_consultory_idx" ON "user_consultories" USING btree ("user_id","consultory_id","role");--> statement-breakpoint
CREATE INDEX "consultory_idx" ON "user_consultories" USING btree ("consultory_id");--> statement-breakpoint
CREATE INDEX "consultory_role_idx" ON "user_consultories" USING btree ("role");--> statement-breakpoint
CREATE INDEX "consultory_active_idx" ON "user_consultories" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "user_doc_type_idx" ON "user_documents" USING btree ("user_id","document_type");--> statement-breakpoint
CREATE INDEX "role_doc_idx" ON "user_documents" USING btree ("role","document_type");--> statement-breakpoint
CREATE INDEX "verification_idx" ON "user_documents" USING btree ("is_verified");--> statement-breakpoint
CREATE INDEX "doc_active_idx" ON "user_documents" USING btree ("is_active");--> statement-breakpoint
CREATE UNIQUE INDEX "user_role_idx" ON "user_roles" USING btree ("user_id","role");--> statement-breakpoint
CREATE INDEX "role_idx" ON "user_roles" USING btree ("role");--> statement-breakpoint
CREATE INDEX "status_idx" ON "user_roles" USING btree ("status");--> statement-breakpoint
CREATE UNIQUE INDEX "user_specialty_idx" ON "user_specialties" USING btree ("user_id","specialty_id");--> statement-breakpoint
CREATE INDEX "specialty_idx" ON "user_specialties" USING btree ("specialty_id");--> statement-breakpoint
CREATE INDEX "primary_specialty_idx" ON "user_specialties" USING btree ("user_id","is_primary");--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_country_id_countries_id_fk" FOREIGN KEY ("country_id") REFERENCES "public"."countries"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_department_id_departments_id_fk" FOREIGN KEY ("department_id") REFERENCES "public"."departments"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_municipality_id_municipalities_id_fk" FOREIGN KEY ("municipality_id") REFERENCES "public"."municipalities"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_occupation_id_occupations_id_fk" FOREIGN KEY ("occupation_id") REFERENCES "public"."occupations"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "user" ADD CONSTRAINT "user_emergency_relationship_id_relationships_id_fk" FOREIGN KEY ("emergency_relationship_id") REFERENCES "public"."relationships"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "reg_user_idx" ON "registrationRequests" USING btree ("userId");--> statement-breakpoint
CREATE INDEX "reg_status_idx" ON "registrationRequests" USING btree ("status");--> statement-breakpoint
CREATE INDEX "reg_submitted_idx" ON "registrationRequests" USING btree ("submittedAt");--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "role";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "generalData";--> statement-breakpoint
ALTER TABLE "registrationRequests" DROP COLUMN "specificData";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "role";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "generalProfile";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "roleSpecificData";--> statement-breakpoint
ALTER TABLE "user" DROP COLUMN "documents";