'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { 
  Plus, 
  Search, 
  Edit, 
  Eye, 
  Trash2, 
  MoreHorizontal,
  ChevronDown,
  Check,
  ChevronLeft,
  ChevronRight,
  ToggleLeft,
  ArrowLeft,
  RefreshCw,
  Download,
  Heart
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { toast } from 'sonner';
import { useRouter } from 'next/navigation';

interface Religion {
  id: string;
  name: string;
  category: string;
  description?: string;
  order: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

interface FiltersData {
  categories: string[];
}

export default function ReligionsPage() {
  const router = useRouter();
  const [religions, setReligions] = useState<Religion[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [filtersData, setFiltersData] = useState<FiltersData>({ categories: [] });
  
  // Pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Modals
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isViewModalOpen, setIsViewModalOpen] = useState(false);
  const [selectedReligion, setSelectedReligion] = useState<Religion | null>(null);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    description: '',
    order: 0,
    isActive: true,
  });

  // Form helpers
  const [categoryOpen, setCategoryOpen] = useState(false);

  // Filter helpers
  const [categoryFilterOpen, setCategoryFilterOpen] = useState(false);

  // Refresh and export handlers
  const [refreshing, setRefreshing] = useState(false);

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchReligions();
    setRefreshing(false);
  };

  const handleExport = () => {
    toast.info('Funcionalidad de exportación en desarrollo');
  };

  const fetchReligions = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: itemsPerPage.toString(),
        search: searchTerm,
        status: statusFilter,
        ...(categoryFilter && { category: categoryFilter }),
      });

      const response = await fetch(`/api/catalogs/religions?${params}`);
      if (!response.ok) throw new Error('Error al cargar religiones');

      const result = await response.json();
      setReligions(result.data || []);
      
      if (result.pagination) {
        setTotalPages(result.pagination.totalPages);
        setTotalItems(result.pagination.total);
      }

      if (result.filters) {
        setFiltersData(result.filters);
      }
    } catch (error) {
      console.error('Error fetching religions:', error);
      toast.error('Error al cargar las religiones');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchReligions();
  }, [currentPage, itemsPerPage, searchTerm, statusFilter, categoryFilter]);

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const resetForm = () => {
    setFormData({
      name: '',
      category: '',
      description: '',
      order: 0,
      isActive: true,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const method = selectedReligion ? 'PUT' : 'POST';
      const url = '/api/catalogs/religions';
      
      const payload = selectedReligion 
        ? { ...formData, id: selectedReligion.id }
        : formData;

      const response = await fetch(url, {
        method,
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Error al guardar');
      }

      toast.success(selectedReligion ? 'Religión actualizada exitosamente' : 'Religión creada exitosamente');
      
      setIsCreateModalOpen(false);
      setIsEditModalOpen(false);
      resetForm();
      setSelectedReligion(null);
      fetchReligions();
    } catch (error: any) {
      toast.error(error.message || 'Error al guardar la religión');
    }
  };

  const handleEdit = (religion: Religion) => {
    setSelectedReligion(religion);
    setFormData({
      name: religion.name,
      category: religion.category,
      description: religion.description || '',
      order: religion.order,
      isActive: religion.isActive,
    });
    setIsEditModalOpen(true);
  };

  const handleView = (religion: Religion) => {
    setSelectedReligion(religion);
    setIsViewModalOpen(true);
  };

  const handleToggleStatus = async (religion: Religion) => {
    try {
      const response = await fetch(`/api/catalogs/religions/${religion.id}/toggle-status`, {
        method: 'POST',
      });

      if (!response.ok) throw new Error('Error al cambiar estado');

      toast.success(`Religión ${religion.isActive ? 'desactivada' : 'activada'} exitosamente`);
      fetchReligions();
    } catch (error) {
      toast.error('Error al cambiar el estado de la religión');
    }
  };

  const handleDelete = async (religion: Religion) => {
    if (!confirm('¿Estás seguro de que deseas eliminar esta religión?')) return;

    try {
      const response = await fetch(`/api/catalogs/religions/${religion.id}?type=logical`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Error al eliminar');

      toast.success('Religión eliminada exitosamente');
      fetchReligions();
    } catch (error) {
      toast.error('Error al eliminar la religión');
    }
  };

  return (
    <div className="space-y-6 p-6">
      {/* Header con navegación y acciones principales */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            {/* Botón de regreso */}
            <Button variant="ghost" size="sm" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4" />
            </Button>
            
            {/* Título y descripción */}
            <div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                Gestión de Religiones
              </h1>
              <p className="text-sm lg:text-base text-gray-600">
                Administra catálogo de religiones del sistema
              </p>
            </div>
          </div>
        </div>
        
        {/* Botones de acción */}
        <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
          <Button onClick={handleRefresh} variant="outline" size="sm" disabled={refreshing}>
            <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
            Actualizar
          </Button>
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Dialog open={isCreateModalOpen} onOpenChange={setIsCreateModalOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Nueva Religión
              </Button>
            </DialogTrigger>
          </Dialog>
        </div>
      </div>

      {/* Card de filtros de búsqueda */}
      <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <Search className="h-5 w-5 text-blue-600" />
            Filtros de Búsqueda
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* Búsqueda */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar religiones..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="pl-9"
                />
              </div>
            </div>

            {/* Estado */}
            <div>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setCurrentPage(1);
                }}
                className="flex h-10 w-full rounded-md border-0 bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">Todos los estados</option>
                <option value="active">Activos</option>
                <option value="inactive">Inactivos</option>
              </select>
            </div>

            {/* Categoría */}
            <div>
              <Popover open={categoryFilterOpen} onOpenChange={setCategoryFilterOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={categoryFilterOpen}
                    className="w-full justify-between border-0"
                  >
                    {categoryFilter || "Categoría"}
                    <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-full p-0">
                  <Command>
                    <CommandInput placeholder="Buscar categoría..." />
                    <CommandEmpty>No se encontraron categorías.</CommandEmpty>
                    <CommandGroup>
                      <CommandItem
                        value=""
                        onSelect={() => {
                          setCategoryFilter('');
                          setCategoryFilterOpen(false);
                          setCurrentPage(1);
                        }}
                      >
                        Todas las categorías
                      </CommandItem>
                      {filtersData.categories.map((category) => (
                        <CommandItem
                          key={category}
                          value={category}
                          onSelect={() => {
                            setCategoryFilter(category);
                            setCategoryFilterOpen(false);
                            setCurrentPage(1);
                          }}
                        >
                          <Check
                            className={`mr-2 h-4 w-4 ${
                              categoryFilter === category ? "opacity-100" : "opacity-0"
                            }`}
                          />
                          {category}
                        </CommandItem>
                      ))}
                    </CommandGroup>
                  </Command>
                </PopoverContent>
              </Popover>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Card principal con grid de datos */}
      <div className="hidden md:block">
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Heart className="h-5 w-5 text-blue-600" />
              Religiones
            </CardTitle>
          </CardHeader>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Nombre</TableHead>
                <TableHead>Categoría</TableHead>
                <TableHead>Descripción</TableHead>
                <TableHead>Orden</TableHead>
                <TableHead>Estado</TableHead>
                <TableHead className="text-right">Acciones</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    Cargando religiones...
                  </TableCell>
                </TableRow>
              ) : religions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    No se encontraron religiones
                  </TableCell>
                </TableRow>
              ) : (
                religions.map((religion) => (
                  <TableRow key={religion.id}>
                    <TableCell className="font-medium">
                      {religion.name}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline">{religion.category}</Badge>
                    </TableCell>
                    <TableCell className="max-w-xs truncate">
                      {religion.description || '-'}
                    </TableCell>
                    <TableCell>{religion.order}</TableCell>
                    <TableCell>
                      <Badge variant={religion.isActive ? "default" : "secondary"}>
                        {religion.isActive ? "Activo" : "Inactivo"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                          <DropdownMenuItem onClick={() => handleView(religion)}>
                            <Eye className="mr-2 h-4 w-4" />
                            Ver detalles
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleEdit(religion)}>
                            <Edit className="mr-2 h-4 w-4" />
                            Editar
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(religion)}
                            className={religion.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            <ToggleLeft className="mr-2 h-4 w-4" />
                            {religion.isActive ? 'Desactivar' : 'Activar'}
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            onClick={() => handleDelete(religion)}
                            className="text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="mr-2 h-4 w-4" />
                            Eliminar
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </Card>
      </div>

      {/* Cards para Mobile */}
      <div className="md:hidden space-y-4">
        <Card className="mb-4">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Heart className="h-5 w-5 text-blue-600" />
              Religiones
            </CardTitle>
          </CardHeader>
        </Card>
        {loading ? (
          <Card>
            <CardContent className="pt-6 text-center">
              Cargando religiones...
            </CardContent>
          </Card>
        ) : religions.length === 0 ? (
          <Card>
            <CardContent className="pt-6 text-center">
              No se encontraron religiones
            </CardContent>
          </Card>
        ) : (
          religions.map((religion) => (
            <Card key={religion.id}>
              <CardContent className="pt-6">
                <div className="flex justify-between items-start mb-2">
                  <div className="flex items-center gap-2">
                    <Heart className="h-4 w-4 text-blue-600" />
                    <h3 className="font-semibold">{religion.name}</h3>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Acciones</DropdownMenuLabel>
                      <DropdownMenuItem onClick={() => handleView(religion)}>
                        <Eye className="mr-2 h-4 w-4" />
                        Ver detalles
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleEdit(religion)}>
                        <Edit className="mr-2 h-4 w-4" />
                        Editar
                      </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            onClick={() => handleToggleStatus(religion)}
                            className={religion.isActive ? "text-orange-600" : "text-green-600"}
                          >
                            <ToggleLeft className="mr-2 h-4 w-4" />
                            {religion.isActive ? 'Desactivar' : 'Activar'}
                          </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => handleDelete(religion)}
                        className="text-red-600 focus:text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        Eliminar
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                <div className="space-y-2">
                  <div className="flex flex-wrap gap-2">
                    <Badge variant="outline">{religion.category}</Badge>
                    <Badge variant={religion.isActive ? "default" : "secondary"}>
                      {religion.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                  
                  <div className="text-sm text-muted-foreground">
                    {religion.description && <p><strong>Descripción:</strong> {religion.description}</p>}
                    <p><strong>Orden:</strong> {religion.order}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>

      {/* Paginación */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium">Filas por página</p>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value));
                setCurrentPage(1);
              }}
              className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm"
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>

          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Página {currentPage} de {totalPages}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage >= totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Modal Crear/Editar */}
      <Dialog open={isCreateModalOpen || isEditModalOpen} onOpenChange={(open) => {
        if (!open) {
          setIsCreateModalOpen(false);
          setIsEditModalOpen(false);
          resetForm();
          setSelectedReligion(null);
        }
      }}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>
              {selectedReligion ? 'Editar Religión' : 'Agregar Religión'}
            </DialogTitle>
          </DialogHeader>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-4">
              {/* Nombre */}
              <div>
                <Label htmlFor="name">Nombre *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>

              {/* Categoría */}
              <div>
                <Label htmlFor="category">Categoría *</Label>
                <Input
                  id="category"
                  value={formData.category}
                  onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                  required
                  placeholder="ej: Cristianismo, Islam, Budismo"
                />
              </div>

              {/* Descripción */}
              <div>
                <Label htmlFor="description">Descripción</Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  placeholder="Descripción opcional de la religión"
                />
              </div>

              {/* Orden */}
              <div>
                <Label htmlFor="order">Orden</Label>
                <Input
                  id="order"
                  type="number"
                  value={formData.order}
                  onChange={(e) => setFormData({ ...formData, order: parseInt(e.target.value) || 0 })}
                />
              </div>

              {/* Estado */}
              <div className="flex items-center space-x-2">
                <Switch
                  id="isActive"
                  checked={formData.isActive}
                  onCheckedChange={(checked) => setFormData({ ...formData, isActive: checked })}
                />
                <Label htmlFor="isActive">Activo</Label>
              </div>
            </div>

            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setIsCreateModalOpen(false);
                  setIsEditModalOpen(false);
                  resetForm();
                  setSelectedReligion(null);
                }}
              >
                Cancelar
              </Button>
              <Button type="submit">
                {selectedReligion ? 'Actualizar' : 'Crear'}
              </Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      {/* Modal Ver Detalles */}
      <Dialog open={isViewModalOpen} onOpenChange={setIsViewModalOpen}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Detalles de la Religión</DialogTitle>
          </DialogHeader>
          
          {selectedReligion && (
            <div className="space-y-4">
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Nombre</Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Heart className="h-4 w-4 text-blue-600" />
                    <p className="text-sm mt-1">{selectedReligion.name}</p>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Categoría</Label>
                  <div className="mt-1">
                    <Badge variant="outline">{selectedReligion.category}</Badge>
                  </div>
                </div>

                {selectedReligion.description && (
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Descripción</Label>
                    <p className="text-sm mt-1">{selectedReligion.description}</p>
                  </div>
                )}

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Orden</Label>
                  <p className="text-sm mt-1">{selectedReligion.order}</p>
                </div>

                <div>
                  <Label className="text-sm font-medium text-muted-foreground">Estado</Label>
                  <div className="mt-1">
                    <Badge variant={selectedReligion.isActive ? "default" : "secondary"}>
                      {selectedReligion.isActive ? "Activo" : "Inactivo"}
                    </Badge>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Creado</Label>
                    <p className="text-sm mt-1">{new Date(selectedReligion.createdAt).toLocaleDateString()}</p>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-muted-foreground">Actualizado</Label>
                    <p className="text-sm mt-1">{new Date(selectedReligion.updatedAt).toLocaleDateString()}</p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}