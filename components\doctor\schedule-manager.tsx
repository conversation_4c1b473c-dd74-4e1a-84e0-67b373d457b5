'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  Save, 
  RefreshCw, 
  Calendar,
  AlertCircle,
  CheckCircle2,
  Coffee,
  Settings,
  Plus
} from 'lucide-react';
import { toast } from 'sonner';

interface DaySchedule {
  dayOfWeek: number;
  dayName: string;
  isActive: boolean;
  startTime: string;
  endTime: string;
  lunchBreakStart: string;
  lunchBreakEnd: string;
  appointmentDuration: number;
  maxAppointmentsPerHour: number;
  allowEmergencies: boolean;
  allowOnlineBooking: boolean;
  notes: string;
}

const DAYS_OF_WEEK = [
  { value: 1, name: '<PERSON><PERSON>', shortName: 'Lun' },
  { value: 2, name: '<PERSON><PERSON>', shortName: 'Mar' },
  { value: 3, name: 'Miércoles', shortName: 'Mié' },
  { value: 4, name: 'Jueves', shortName: 'Jue' },
  { value: 5, name: 'Viernes', shortName: 'Vie' },
  { value: 6, name: 'Sábado', shortName: 'Sáb' },
  { value: 0, name: 'Domingo', shortName: 'Dom' },
];

const DEFAULT_SCHEDULE: Omit<DaySchedule, 'dayOfWeek' | 'dayName'> = {
  isActive: false,
  startTime: '08:00',
  endTime: '17:00',
  lunchBreakStart: '12:00',
  lunchBreakEnd: '13:00',
  appointmentDuration: 30,
  maxAppointmentsPerHour: 2,
  allowEmergencies: true,
  allowOnlineBooking: true,
  notes: '',
};

export function DoctorScheduleManager() {
  const [schedules, setSchedules] = useState<DaySchedule[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    fetchSchedules();
  }, []);

  const fetchSchedules = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/doctor/schedule');
      const data = await response.json();
      
      if (data.success) {
        // Crear horarios para todos los días de la semana
        const schedulesMap = new Map();
        data.data.schedules.forEach((schedule: any) => {
          schedulesMap.set(schedule.dayOfWeek, schedule);
        });

        const fullSchedules = DAYS_OF_WEEK.map(day => {
          const existing = schedulesMap.get(day.value);
          return {
            dayOfWeek: day.value,
            dayName: day.name,
            ...(existing ? {
              isActive: existing.isActive,
              startTime: existing.startTime,
              endTime: existing.endTime,
              lunchBreakStart: existing.lunchBreakStart || '',
              lunchBreakEnd: existing.lunchBreakEnd || '',
              appointmentDuration: existing.appointmentDuration,
              maxAppointmentsPerHour: existing.maxAppointmentsPerHour,
              allowEmergencies: existing.allowEmergencies,
              allowOnlineBooking: existing.allowOnlineBooking,
              notes: existing.notes || '',
            } : DEFAULT_SCHEDULE)
          };
        });

        setSchedules(fullSchedules);
      } else {
        toast.error('Error al cargar horarios');
      }
    } catch (error) {
      console.error('Error fetching schedules:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setLoading(false);
    }
  };

  const updateSchedule = (dayOfWeek: number, updates: Partial<DaySchedule>) => {
    setSchedules(prev => 
      prev.map(schedule => 
        schedule.dayOfWeek === dayOfWeek 
          ? { ...schedule, ...updates }
          : schedule
      )
    );
  };

  const saveSchedules = async () => {
    try {
      setSaving(true);
      
      const response = await fetch('/api/doctor/schedule', {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          schedules: schedules.filter(s => s.isActive),
        }),
      });

      const data = await response.json();
      
      if (data.success) {
        toast.success('Horarios guardados correctamente');
      } else {
        toast.error(data.error || 'Error al guardar horarios');
      }
    } catch (error) {
      console.error('Error saving schedules:', error);
      toast.error('Error al conectar con el servidor');
    } finally {
      setSaving(false);
    }
  };

  const copySchedule = (fromDay: number, toDay: number) => {
    const sourceSchedule = schedules.find(s => s.dayOfWeek === fromDay);
    if (sourceSchedule) {
      updateSchedule(toDay, {
        isActive: sourceSchedule.isActive,
        startTime: sourceSchedule.startTime,
        endTime: sourceSchedule.endTime,
        lunchBreakStart: sourceSchedule.lunchBreakStart,
        lunchBreakEnd: sourceSchedule.lunchBreakEnd,
        appointmentDuration: sourceSchedule.appointmentDuration,
        maxAppointmentsPerHour: sourceSchedule.maxAppointmentsPerHour,
        allowEmergencies: sourceSchedule.allowEmergencies,
        allowOnlineBooking: sourceSchedule.allowOnlineBooking,
        notes: sourceSchedule.notes,
      });
      toast.success(`Horario copiado de ${sourceSchedule.dayName}`);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header con acciones globales */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h3 className="text-lg font-semibold">Horarios de Trabajo</h3>
          <p className="text-sm text-gray-600">
            Configura tus horarios de disponibilidad para cada día de la semana
          </p>
        </div>
        
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            onClick={fetchSchedules}
            disabled={loading}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Recargar
          </Button>
          
          <Button 
            onClick={saveSchedules}
            disabled={saving}
            className="bg-emerald-600 hover:bg-emerald-700"
          >
            {saving ? (
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
            ) : (
              <Save className="h-4 w-4 mr-2" />
            )}
            Guardar Cambios
          </Button>
        </div>
      </div>

      {/* Vista resumida de la semana */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Resumen Semanal
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-7 gap-2">
            {DAYS_OF_WEEK.map(day => {
              const schedule = schedules.find(s => s.dayOfWeek === day.value);
              return (
                <div key={day.value} className="text-center p-2 border rounded-lg">
                  <div className="font-medium text-sm">{day.shortName}</div>
                  {schedule?.isActive ? (
                    <div className="text-xs text-green-600 mt-1">
                      <div>{schedule.startTime}</div>
                      <div>{schedule.endTime}</div>
                      <Badge variant="secondary" className="text-xs mt-1">
                        {schedule.appointmentDuration}min
                      </Badge>
                    </div>
                  ) : (
                    <div className="text-xs text-gray-400 mt-1">
                      Cerrado
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Configuración detallada por día */}
      <div className="space-y-4">
        {schedules.map(schedule => (
          <Card key={schedule.dayOfWeek} className={schedule.isActive ? 'border-emerald-200' : 'border-gray-200'}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Checkbox
                    className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                    checked={schedule.isActive}
                    onCheckedChange={(checked) => 
                      updateSchedule(schedule.dayOfWeek, { isActive: checked as boolean })
                    }
                  />
                  <div>
                    <CardTitle className="text-lg">{schedule.dayName}</CardTitle>
                    {schedule.isActive ? (
                      <div className="flex items-center gap-2 text-sm text-green-600">
                        <CheckCircle2 className="h-4 w-4" />
                        Día laboral activo
                      </div>
                    ) : (
                      <div className="flex items-center gap-2 text-sm text-gray-500">
                        <AlertCircle className="h-4 w-4" />
                        Día no laboral
                      </div>
                    )}
                  </div>
                </div>

                {schedule.isActive && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const otherDays = DAYS_OF_WEEK.filter(d => d.value !== schedule.dayOfWeek);
                      // Por simplicidad, copiamos al siguiente día que esté disponible
                      const nextDay = otherDays.find(d => {
                        const targetSchedule = schedules.find(s => s.dayOfWeek === d.value);
                        return !targetSchedule?.isActive;
                      });
                      if (nextDay) {
                        copySchedule(schedule.dayOfWeek, nextDay.value);
                      }
                    }}
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Copiar
                  </Button>
                )}
              </div>
            </CardHeader>

            {schedule.isActive && (
              <CardContent className="space-y-6">
                {/* Horarios básicos */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Hora de inicio
                    </Label>
                    <Input
                      type="time"
                      value={schedule.startTime}
                      onChange={(e) => 
                        updateSchedule(schedule.dayOfWeek, { startTime: e.target.value })
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="flex items-center gap-2">
                      <Clock className="h-4 w-4" />
                      Hora de fin
                    </Label>
                    <Input
                      type="time"
                      value={schedule.endTime}
                      onChange={(e) => 
                        updateSchedule(schedule.dayOfWeek, { endTime: e.target.value })
                      }
                    />
                  </div>
                </div>

                <Separator />

                {/* Descanso de almuerzo */}
                <div>
                  <Label className="flex items-center gap-2 mb-3">
                    <Coffee className="h-4 w-4" />
                    Descanso de almuerzo (opcional)
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Inicio del descanso</Label>
                      <Input
                        type="time"
                        value={schedule.lunchBreakStart}
                        onChange={(e) => 
                          updateSchedule(schedule.dayOfWeek, { lunchBreakStart: e.target.value })
                        }
                        placeholder="12:00"
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Fin del descanso</Label>
                      <Input
                        type="time"
                        value={schedule.lunchBreakEnd}
                        onChange={(e) => 
                          updateSchedule(schedule.dayOfWeek, { lunchBreakEnd: e.target.value })
                        }
                        placeholder="13:00"
                      />
                    </div>
                  </div>
                </div>

                <Separator />

                {/* Configuración de citas */}
                <div>
                  <Label className="flex items-center gap-2 mb-3">
                    <Settings className="h-4 w-4" />
                    Configuración de citas
                  </Label>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Duración por cita (minutos)</Label>
                      <Input
                        type="number"
                        min="15"
                        max="120"
                        step="15"
                        value={schedule.appointmentDuration}
                        onChange={(e) => 
                          updateSchedule(schedule.dayOfWeek, { 
                            appointmentDuration: parseInt(e.target.value) || 30 
                          })
                        }
                      />
                    </div>

                    <div className="space-y-2">
                      <Label className="text-sm text-gray-600">Máximo citas por hora</Label>
                      <Input
                        type="number"
                        min="1"
                        max="6"
                        value={schedule.maxAppointmentsPerHour}
                        onChange={(e) => 
                          updateSchedule(schedule.dayOfWeek, { 
                            maxAppointmentsPerHour: parseInt(e.target.value) || 2 
                          })
                        }
                      />
                    </div>
                  </div>
                </div>

                {/* Opciones adicionales */}
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Permitir citas de emergencia</Label>
                      <p className="text-xs text-gray-600">
                        Permite agendar citas fuera del horario normal
                      </p>
                    </div>
                    <Checkbox
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                      checked={schedule.allowEmergencies}
                      onCheckedChange={(checked) => 
                        updateSchedule(schedule.dayOfWeek, { allowEmergencies: checked as boolean })
                      }
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Permitir reserva online</Label>
                      <p className="text-xs text-gray-600">
                        Los pacientes pueden agendar citas en línea
                      </p>
                    </div>
                    <Checkbox
                      className="border-green-500 data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
                      checked={schedule.allowOnlineBooking}
                      onCheckedChange={(checked) => 
                        updateSchedule(schedule.dayOfWeek, { allowOnlineBooking: checked as boolean })
                      }
                    />
                  </div>
                </div>

                {/* Notas */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium">Notas especiales</Label>
                  <Textarea
                    placeholder="Notas especiales para este día (opcional)"
                    value={schedule.notes}
                    onChange={(e) => 
                      updateSchedule(schedule.dayOfWeek, { notes: e.target.value })
                    }
                    rows={2}
                  />
                </div>
              </CardContent>
            )}
          </Card>
        ))}
      </div>

      {/* Botón de guardar inferior */}
      <div className="flex justify-end pt-4">
        <Button 
          onClick={saveSchedules}
          disabled={saving}
          size="lg"
          className="bg-emerald-600 hover:bg-emerald-700"
        >
          {saving ? (
            <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
          ) : (
            <Save className="h-5 w-5 mr-2" />
          )}
          Guardar Todos los Horarios
        </Button>
      </div>
    </div>
  );
}