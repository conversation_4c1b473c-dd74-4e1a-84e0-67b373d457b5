{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(rm:*)", "Bash(cp:*)", "Bash(npx drizzle-kit:*)", "Bash(node:*)", "Bash(find:*)", "Bash(ls:*)", "Bash(grep:*)", "Bash(npx tsc:*)", "Bash(wc:*)", "<PERSON><PERSON>(tail:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(curl:*)", "<PERSON><PERSON>(sed:*)", "<PERSON><PERSON>(dos2unix:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(true)", "Bash(npx eslint:*)", "Bash(npm run build:*)", "Bash(npm run dev:*)", "Bash(echo $NODE_ENV)"], "deny": []}, "$schema": "https://json.schemastore.org/claude-code-settings.json"}