import { db } from '../db/drizzle';
import { user, userRoles } from '../db/schema';
import { eq, and } from 'drizzle-orm';

async function fixPendingPatients() {
  console.log('🔧 Arreglando pacientes en estado pending...\n');
  
  try {
    // Buscar pacientes con overallStatus = 'pending'
    const pendingPatients = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        overallStatus: user.overallStatus,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(user.overallStatus, 'pending')
        )
      );

    console.log(`📊 Encontrados ${pendingPatients.length} pacientes en estado pending\n`);

    if (pendingPatients.length === 0) {
      console.log('✅ No hay pacientes pendientes que arreglar');
      process.exit(0);
    }

    for (const patient of pendingPatients) {
      console.log(`Actualizando paciente: ${patient.firstName} ${patient.lastName} (${patient.id})`);
      
      // Actualizar overallStatus a 'active'
      await db
        .update(user)
        .set({ 
          overallStatus: 'active',
          updatedAt: new Date()
        })
        .where(eq(user.id, patient.id));
      
      console.log('✅ Actualizado a estado active');
    }

    console.log('\n✅ Todos los pacientes han sido actualizados');
    process.exit(0);
    
  } catch (error) {
    console.error('❌ Error:', error);
    process.exit(1);
  }
}

fixPendingPatients();