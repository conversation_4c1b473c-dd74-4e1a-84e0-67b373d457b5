import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/db/drizzle';
import { symptoms } from '@/db/schema';
import { eq } from 'drizzle-orm';

// GET /api/catalogs/symptoms/[id] - Obtener síntoma específico
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    const data = await db
      .select()
      .from(symptoms)
      .where(eq(symptoms.id, id))
      .limit(1);

    if (!data.length) {
      return NextResponse.json(
        { error: 'Síntoma no encontrado' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data[0],
    });

  } catch (error: any) {
    console.error('Error fetching symptom:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// DELETE /api/catalogs/symptoms/[id] - Eliminar síntoma
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') || 'logical'; // logical o physical

    // Verificar que el síntoma existe
    const existingSymptom = await db
      .select()
      .from(symptoms)
      .where(eq(symptoms.id, id))
      .limit(1);

    if (!existingSymptom.length) {
      return NextResponse.json(
        { error: 'Síntoma no encontrado' },
        { status: 404 }
      );
    }

    if (type === 'logical') {
      // Eliminación lógica - marcar como inactivo
      await db
        .update(symptoms)
        .set({
          isActive: false,
          updatedAt: new Date(),
        })
        .where(eq(symptoms.id, id));

      return NextResponse.json({
        success: true,
        message: 'Síntoma desactivado exitosamente',
      });

    } else if (type === 'physical') {
      // Eliminación física - borrar registro completamente
      await db
        .delete(symptoms)
        .where(eq(symptoms.id, id));

      return NextResponse.json({
        success: true,
        message: 'Síntoma eliminado permanentemente',
      });

    } else {
      return NextResponse.json(
        { error: 'Tipo de eliminación no válido. Use "logical" o "physical"' },
        { status: 400 }
      );
    }

  } catch (error: any) {
    console.error('Error deleting symptom:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}