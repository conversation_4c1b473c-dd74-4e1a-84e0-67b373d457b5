/**
 * Script para aplicar los colores del logo de la Dra. Bárbara I<PERSON> Morales
 * al consultorio principal y establecer la paleta de colores dinámica
 */

import { db } from '../db/drizzle';
import { consultories } from '../db/schema';
import { eq } from 'drizzle-orm';

async function seedConsultoryColors() {
  try {
    console.log('🎨 Iniciando seed de colores del consultorio...');

    // Paleta de colores extraída del logo de la Dra. Bárbara I. Morales
    const colorPalette = {
      // Colores principales del logo (corregidos basados en análisis visual)
      primary: '#E91E63',        // Rosa/magenta del nombre principal
      secondary: '#4DD0E1',      // Turquesa claro del círculo 
      accent: '#2E3F4F',         // Azul marino de los contornos
      
      // Colores de fondo y texto
      background: '#FFFFFF',     // Blanco del fondo
      text: '#2E3F4F',          // Azul marino para texto principal
      
      // Colores para estados del sistema
      success: '#4CAF50',       // Verde para éxito (derivado de la temática médica)
      warning: '#FF9800',       // Naranja para advertencias
      error: '#F44336',         // Rojo para errores
      info: '#4DD0E1',          // Mismo turquesa para información
      neutral: '#607D8B',       // Gris azulado neutral
      
      // Gradientes basados en los colores principales
      gradientStart: '#E91E63',  // Rosa principal
      gradientEnd: '#4DD0E1',    // Turquesa secundario
      
      // Colores extraídos del análisis corregido de la imagen
      extractedColors: [
        '#E91E63',  // Rosa principal del texto
        '#4DD0E1',  // Turquesa del círculo
        '#2E3F4F',  // Azul marino de contornos
        '#F4C2A1',  // Beige/piel del bebé
        '#FFFFFF',  // Blanco de fondo
        '#F8BBD9',  // Rosa claro (variación)
        '#B2EBF2',  // Turquesa muy claro
        '#90A4AE',  // Gris azulado
        '#FFE0B2',  // Beige muy claro
        '#37474F'   // Azul marino más oscuro
      ],
      
      // Metadatos
      isAutoGenerated: false,    // Fue creada manualmente basada en el logo
      lastUpdated: new Date().toISOString()
    };

    // Buscar el consultorio principal/activo
    const consultory = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true))
      .limit(1);

    if (consultory.length === 0) {
      console.log('⚠️  No se encontró consultorio activo. Buscando cualquier consultorio...');
      
      const anyConsultory = await db
        .select()
        .from(consultories)
        .limit(1);
        
      if (anyConsultory.length === 0) {
        console.log('❌ No se encontraron consultorios. Creando consultorio de ejemplo...');
        
        // Crear consultorio de ejemplo con los datos de la imagen
        await db.insert(consultories).values({
          id: 'dra-barbara-morales',
          name: 'Dra. Bárbara I. Morales',
          code: 'DBM-001',
          type: 'pediatric',
          specialty: 'Pediatría General',
          capacity: 1,
          floor: 1,
          building: 'Clínica Pediátrica',
          isActive: true,
          isPrimary: true,
          colorPalette: colorPalette,
          createdAt: new Date(),
          updatedAt: new Date()
        });
        
        console.log('✅ Consultorio creado con paleta de colores aplicada');
      } else {
        // Actualizar consultorio existente
        await db
          .update(consultories)
          .set({
            colorPalette: colorPalette,
            updatedAt: new Date()
          })
          .where(eq(consultories.id, anyConsultory[0].id));
          
        console.log(`✅ Paleta de colores aplicada al consultorio: ${anyConsultory[0].name}`);
      }
    } else {
      // Actualizar consultorio activo
      await db
        .update(consultories)
        .set({
          colorPalette: colorPalette,
          updatedAt: new Date()
        })
        .where(eq(consultories.id, consultory[0].id));
        
      console.log(`✅ Paleta de colores aplicada al consultorio activo: ${consultory[0].name}`);
    }

    // Mostrar resumen de colores aplicados
    console.log('\n🎨 PALETA DE COLORES APLICADA (CORREGIDA):');
    console.log(`🔴 Primario (Rosa): ${colorPalette.primary}`);
    console.log(`🔵 Secundario (Turquesa): ${colorPalette.secondary}`);
    console.log(`⚫ Acento (Azul Marino): ${colorPalette.accent}`);
    console.log(`⚪ Fondo: ${colorPalette.background}`);
    console.log(`📝 Texto: ${colorPalette.text}`);
    console.log(`🌈 Gradiente: ${colorPalette.gradientStart} → ${colorPalette.gradientEnd}`);
    console.log(`\n💡 ${colorPalette.extractedColors.length} colores extraídos y corregidos del logo`);
    
    console.log('\n🚀 ¡Seed de colores completado exitosamente!');
    
  } catch (error) {
    console.error('❌ Error aplicando colores del consultorio:', error);
    process.exit(1);
  }
}

// Ejecutar el seed si es llamado directamente
if (require.main === module) {
  seedConsultoryColors()
    .then(() => {
      console.log('✨ Proceso completado');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error fatal:', error);
      process.exit(1);
    });
}

export { seedConsultoryColors };