import { db } from '../drizzle';
import { serviceTags } from '../schema';
import { nanoid } from 'nanoid';

export async function seedServiceTags() {
  console.log('🏷️  Seeding service tags...');

  const tags = [
    // Tags de Requerimientos (reemplazan características actuales)
    {
      id: nanoid(),
      name: 'Requiere Equipo',
      description: 'Servicio que requiere equipo médico especializado',
      color: 'red',
      category: 'requirement'
    },
    {
      id: nanoid(),
      name: 'Requiere Especialista',
      description: 'Servicio que debe ser realizado por un especialista',
      color: 'purple',
      category: 'requirement'
    },
    {
      id: nanoid(),
      name: 'Requiere Anestesia',
      description: 'Procedimiento que requiere algún tipo de anestesia',
      color: 'orange',
      category: 'requirement'
    },
    {
      id: nanoid(),
      name: 'Requiere Ayuno',
      description: 'Servicio que requiere ayuno previo del paciente',
      color: 'yellow',
      category: 'requirement'
    },

    // Tags de Especialidad
    {
      id: nanoid(),
      name: 'Pediatría',
      description: 'Servicio específico para pacientes pediátricos',
      color: 'green',
      category: 'specialty'
    },
    {
      id: nanoid(),
      name: 'Geriatría',
      description: 'Servicio específico para pacientes geriátricos',
      color: 'blue',
      category: 'specialty'
    },
    {
      id: nanoid(),
      name: 'Ginecología',
      description: 'Servicio específico de ginecología',
      color: 'pink',
      category: 'specialty'
    },
    {
      id: nanoid(),
      name: 'Cardiología',
      description: 'Servicio específico de cardiología',
      color: 'red',
      category: 'specialty'
    },

    // Tags de Tipo de Servicio
    {
      id: nanoid(),
      name: 'Ambulatorio',
      description: 'Servicio que no requiere hospitalización',
      color: 'green',
      category: 'type'
    },
    {
      id: nanoid(),
      name: 'Hospitalario',
      description: 'Servicio que requiere hospitalización',
      color: 'orange',
      category: 'type'
    },
    {
      id: nanoid(),
      name: 'Domiciliario',
      description: 'Servicio que se puede realizar en domicilio',
      color: 'blue',
      category: 'type'
    },
    {
      id: nanoid(),
      name: 'Telemedicina',
      description: 'Servicio que se puede realizar por telemedicina',
      color: 'cyan',
      category: 'type'
    },

    // Tags de Duración
    {
      id: nanoid(),
      name: 'Consulta Rápida',
      description: 'Consulta de menos de 15 minutos',
      color: 'green',
      category: 'duration'
    },
    {
      id: nanoid(),
      name: 'Consulta Estándar',
      description: 'Consulta de 15-30 minutos',
      color: 'blue',
      category: 'duration'
    },
    {
      id: nanoid(),
      name: 'Consulta Extendida',
      description: 'Consulta de más de 30 minutos',
      color: 'orange',
      category: 'duration'
    },

    // Tags Generales
    {
      id: nanoid(),
      name: 'Urgente',
      description: 'Servicio de atención urgente',
      color: 'red',
      category: 'general'
    },
    {
      id: nanoid(),
      name: 'Preventivo',
      description: 'Servicio de medicina preventiva',
      color: 'green',
      category: 'general'
    },
    {
      id: nanoid(),
      name: 'Seguimiento',
      description: 'Servicio de seguimiento de tratamiento',
      color: 'blue',
      category: 'general'
    },
    {
      id: nanoid(),
      name: 'Primera Vez',
      description: 'Servicio para pacientes nuevos',
      color: 'purple',
      category: 'general'
    },
    {
      id: nanoid(),
      name: 'Control',
      description: 'Consulta de control o revisión',
      color: 'gray',
      category: 'general'
    }
  ];

  try {
    // Insertar todos los tags
    for (const tag of tags) {
      await db.insert(serviceTags).values(tag).onConflictDoNothing();
    }

    console.log(`✅ Successfully seeded ${tags.length} service tags`);
    
    // Mostrar resumen por categoría
    const categories = [...new Set(tags.map(t => t.category))];
    for (const category of categories) {
      const count = tags.filter(t => t.category === category).length;
      console.log(`   - ${category}: ${count} tags`);
    }

  } catch (error) {
    console.error('❌ Error seeding service tags:', error);
    throw error;
  }
}

// Si este archivo se ejecuta directamente
if (require.main === module) {
  seedServiceTags()
    .then(() => {
      console.log('🎉 Service tags seeding completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Service tags seeding failed:', error);
      process.exit(1);
    });
}