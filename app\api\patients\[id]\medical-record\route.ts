import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { medicalRecords, user, userRoles } from '@/db/schema';
import { eq, and } from 'drizzle-orm';
import { generateId } from '@/lib/utils';

export async function POST(
  request: NextRequest,
  props: { params: Promise<{ id: string }> }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const params = await props.params;
    const patientId = params.id;

    // Verificar que el paciente existe y es un paciente
    const patientData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        dateOfBirth: user.dateOfBirth
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.id, patientId),
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (patientData.length === 0) {
      return NextResponse.json(
        { error: 'Paciente no encontrado o inactivo' },
        { status: 404 }
      );
    }

    const patient = patientData[0];

    // Verificar que no tenga ya un expediente médico
    const existingRecord = await db
      .select()
      .from(medicalRecords)
      .where(eq(medicalRecords.patientId, patientId))
      .limit(1);

    if (existingRecord.length > 0) {
      return NextResponse.json(
        { error: 'El paciente ya tiene un expediente médico' },
        { status: 400 }
      );
    }

    // Obtener información del doctor actual (usuario autenticado)
    const doctorData = await db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(user.clerkUserId, userId),
          eq(userRoles.role, 'doctor'),
          eq(userRoles.status, 'active')
        )
      )
      .limit(1);

    if (doctorData.length === 0) {
      return NextResponse.json(
        { error: 'Doctor no encontrado' },
        { status: 404 }
      );
    }

    const doctor = doctorData[0];

    // Calcular si es menor de edad
    let isMinor = false;
    if (patient.dateOfBirth) {
      const today = new Date();
      const birthDate = new Date(patient.dateOfBirth);
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age - 1;
      }
      isMinor = age < 18;
    }

    // Generar número de expediente único
    const recordNumber = `EXP-${Date.now()}-${patientId.slice(0, 6).toUpperCase()}`;

    // Crear el expediente médico
    const newRecord = await db.insert(medicalRecords).values({
      id: generateId(),
      patientId: patientId,
      recordNumber,
      status: 'active',
      openDate: new Date(),
      primaryDoctorId: doctor.id,
      consultoryId: null, // TODO: Obtener consultorio del doctor si está disponible
      demographics: {
        createdManually: true,
        createdBy: doctor.id,
        createdDate: new Date().toISOString()
      },
      administrative: {
        source: 'manual_creation',
        notes: `Expediente creado manualmente por Dr. ${doctor.firstName} ${doctor.lastName}`
      },
      isMinor,
      totalConsultations: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    }).returning();

    if (newRecord.length === 0) {
      return NextResponse.json(
        { error: 'Error al crear el expediente médico' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: {
        id: newRecord[0].id,
        recordNumber: newRecord[0].recordNumber,
        patientName: `${patient.firstName} ${patient.lastName}`,
        doctorName: `Dr. ${doctor.firstName} ${doctor.lastName}`,
        openDate: newRecord[0].openDate,
        isMinor
      },
      message: 'Expediente médico creado exitosamente'
    });

  } catch (error) {
    console.error('Error creating medical record:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}