'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { 
  Database,
  Settings,
  CheckCircle2,
  Clock,
  BarChart3,
  AlertCircle,
  Globe,
  Building,
  MapPin,
  Briefcase,
  Users,
  Stethoscope,
  DollarSign,
  Hospital,
  Calendar,
  FileText,
  Cigarette,
  Radio,
  GraduationCap,
  CreditCard,
  Heart,
  Building2,
  Pill,
  Church
} from 'lucide-react';

// Datos de los catálogos organizados por secciones lógicas
// Solo se incluyen catálogos implementados y funcionales
const catalogSections = {
  geographic: {
    title: "Configuración Geográfica",
    description: "Ubicaciones y divisiones territoriales",
    icon: Globe,
    catalogs: [
      { id: 'countries', name: 'Países', icon: Globe, count: 0, route: '/countries' },
      { id: 'departments', name: 'Departamentos', icon: Building, count: 0, route: '/departments' },
      { id: 'municipalities', name: 'Municipios', icon: MapPin, count: 0, route: '/municipalities' }
    ]
  },
  medical: {
    title: "Configuración Médica",
    description: "Servicios, especialidades y recursos médicos",
    icon: Stethoscope,
    catalogs: [
      { id: 'specialties', name: 'Especialidades Médicas', icon: Stethoscope, count: 0, route: '/medical-specialties' },
      { id: 'services', name: 'Servicios Médicos', icon: Stethoscope, count: 0, route: '/medical-services' },
      { id: 'medications', name: 'Medicamentos', icon: Pill, count: 0, route: '/medications' },
      { id: 'symptoms', name: 'Síntomas/Padecimientos', icon: FileText, count: 0, route: '/symptoms' },
      { id: 'pathological', name: 'Antecedentes Patológicos', icon: FileText, count: 0, route: '/pathological-history' },
      { id: 'nonPathological', name: 'Antecedentes No Patológicos', icon: Cigarette, count: 0, route: '/non-pathological-history' }
    ]
  },
  demographic: {
    title: "Información Demográfica",
    description: "Datos personales y sociales de pacientes",
    icon: Users,
    catalogs: [
      { id: 'occupations', name: 'Ocupaciones', icon: Briefcase, count: 0, route: '/occupations' },
      { id: 'relationships', name: 'Relaciones Familiares', icon: Users, count: 0, route: '/relationships' },
      { id: 'religions', name: 'Religiones', icon: Church, count: 0, route: '/religions' },
      { id: 'education', name: 'Niveles de Educación', icon: GraduationCap, count: 0, route: '/education-levels' },
      { id: 'marital', name: 'Estados Civiles', icon: Heart, count: 0, route: '/marital-status' }
    ]
  },
  administrative: {
    title: "Configuración Administrativa",
    description: "Gestión empresarial y operativa",
    icon: Settings,
    catalogs: [
      { id: 'currencies', name: 'Monedas', icon: DollarSign, count: 0, route: '/currencies' },
      { id: 'companies', name: 'Empresas/NITs', icon: Building2, count: 0, route: '/companies' },
      { id: 'documents', name: 'Tipos de Documento', icon: CreditCard, count: 0, route: '/document-types' },
      { id: 'pricing', name: 'Precios por Médico', icon: DollarSign, count: 0, route: '/doctor-service-prices' },
      { id: 'activities', name: 'Tipos de Actividad', icon: Calendar, count: 0, route: '/activity-types' },
      { id: 'media', name: 'Medios de Origen', icon: Radio, count: 0, route: '/media-sources' },
      { id: 'consultories', name: 'Consultorios', icon: Hospital, count: 0, route: '/consultories' }
    ]
  }
};

// Las estadísticas se calcularán dinámicamente

export default function CatalogsPage() {
  const [catalogCounts, setCatalogCounts] = useState<Record<string, number>>({});
  const [loadingCounts, setLoadingCounts] = useState(true);
  const router = useRouter();

  // Calcular estadísticas dinámicamente de todas las secciones
  const allCatalogs = Object.values(catalogSections).flatMap(section => section.catalogs);
  const totalCatalogs = allCatalogs.length;
  const catalogsWithData = allCatalogs.filter(catalog => (catalogCounts[catalog.id] || 0) > 0).length;
  const catalogsWithoutData = allCatalogs.filter(catalog => (catalogCounts[catalog.id] || 0) === 0).length;

  const handleManageCatalog = (catalog: any) => {
    router.push(`/dashboard/admin/catalogs${catalog.route}`);
  };

  // Función para cargar conteos de cada catálogo
  const loadCatalogCounts = async () => {
    setLoadingCounts(true);
    const counts: Record<string, number> = {};
    
    // Lista de endpoints de API para cada catálogo
    const apiEndpoints = [
      { id: 'countries', endpoint: '/api/catalogs/countries' },
      { id: 'departments', endpoint: '/api/catalogs/departments' },
      { id: 'municipalities', endpoint: '/api/catalogs/municipalities' },
      { id: 'occupations', endpoint: '/api/catalogs/occupations' },
      { id: 'relationships', endpoint: '/api/catalogs/relationships' },
      { id: 'specialties', endpoint: '/api/catalogs/medical-specialties' },
      { id: 'currencies', endpoint: '/api/catalogs/currencies' },
      { id: 'consultories', endpoint: '/api/catalogs/consultories' },
      { id: 'activities', endpoint: '/api/catalogs/activity-types' },
      { id: 'pathological', endpoint: '/api/catalogs/pathological-history' },
      { id: 'nonPathological', endpoint: '/api/catalogs/non-pathological-history' },
      { id: 'media', endpoint: '/api/catalogs/media-sources' },
      { id: 'education', endpoint: '/api/catalogs/education-levels' },
      { id: 'documents', endpoint: '/api/catalogs/document-types' },
      { id: 'marital', endpoint: '/api/catalogs/marital-status' },
      { id: 'companies', endpoint: '/api/catalogs/companies' },
      { id: 'services', endpoint: '/api/catalogs/medical-services' },
      { id: 'pricing', endpoint: '/api/catalogs/doctor-service-prices' },
      { id: 'medications', endpoint: '/api/catalogs/medications' },
      { id: 'symptoms', endpoint: '/api/catalogs/symptoms' },
      { id: 'religions', endpoint: '/api/catalogs/religions' }
    ];

    // Cargar conteos en paralelo
    await Promise.allSettled(
      apiEndpoints.map(async ({ id, endpoint }) => {
        try {
          const response = await fetch(`${endpoint}?limit=1`);
          if (response.ok) {
            const data = await response.json();
            counts[id] = data.pagination?.total || 0;
          } else {
            counts[id] = 0;
          }
        } catch (error) {
          console.error(`Error loading count for ${id}:`, error);
          counts[id] = 0;
        }
      })
    );

    setCatalogCounts(counts);
    setLoadingCounts(false);
  };

  useEffect(() => {
    loadCatalogCounts();
  }, []);

  // Calcular estadísticas dinámicamente
  const overallStats = {
    total: totalCatalogs, // Total de catálogos implementados y visibles
    configured: catalogsWithData,
    pending: catalogsWithoutData,
    progress: Math.round((catalogsWithData / totalCatalogs) * 100)
  };


  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
        <div>
          <div className="flex items-center space-x-3 mb-2">
            <div>
              <h1 className="text-2xl lg:text-3xl font-bold text-gray-900">Gestión de Catálogos</h1>
              <p className="text-gray-600 text-sm lg:text-base">Administra todos los catálogos del sistema organizados por secciones</p>
            </div>
          </div>
        </div>
      </div>

      {/* Estadísticas Generales */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Configuraciones</p>
                <p className="text-3xl font-bold text-gray-900">{overallStats.total}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Database className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Configurados</p>
                <p className="text-3xl font-bold text-green-600">{overallStats.configured}</p>
              </div>
              <div className="h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                <CheckCircle2 className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Por Configurar</p>
                <p className="text-3xl font-bold text-orange-600">{overallStats.pending}</p>
              </div>
              <div className="h-12 w-12 bg-orange-100 rounded-full flex items-center justify-center">
                <Clock className="h-6 w-6 text-orange-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Completado</p>
                <p className="text-3xl font-bold text-blue-600">{overallStats.progress}%</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="mt-3">
              <Progress value={overallStats.progress} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Catálogos por Secciones */}
      <div className="space-y-6">
        {Object.entries(catalogSections).map(([sectionKey, section]) => {
          const sectionCatalogs = section.catalogs.map(catalog => ({
            ...catalog,
            count: catalogCounts[catalog.id] || 0
          }));
          
          const catalogsWithData = sectionCatalogs.filter(catalog => catalog.count > 0).length;
          const totalSectionCatalogs = sectionCatalogs.length;
          const sectionProgress = Math.round((catalogsWithData / totalSectionCatalogs) * 100);
          
          return (
            <Card key={sectionKey}>
              <CardHeader>
                <div className="flex flex-col space-y-4 lg:flex-row lg:justify-between lg:items-start lg:space-y-0">
                  <div>
                    <CardTitle className="text-lg lg:text-xl flex items-center gap-3">
                      <section.icon className="h-6 w-6 text-gray-600" />
                      {section.title}
                    </CardTitle>
                    <p className="text-gray-600 mt-1 text-sm lg:text-base">{section.description}</p>
                  </div>
                  <div className="flex flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-3">
                    <Badge variant="outline">
                      {catalogsWithData}/{totalSectionCatalogs} con datos
                    </Badge>
                    <Badge 
                      className={
                        sectionProgress === 100 ? 'bg-green-100 text-green-800 border-green-200' :
                        sectionProgress >= 75 ? 'bg-blue-100 text-blue-800 border-blue-200' :
                        sectionProgress >= 50 ? 'bg-yellow-100 text-yellow-800 border-yellow-200' :
                        sectionProgress >= 25 ? 'bg-orange-100 text-orange-800 border-orange-200' :
                        'bg-red-100 text-red-800 border-red-200'
                      }
                    >
                      {sectionProgress}% completado
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {sectionCatalogs.map((catalog) => {
                    const IconComponent = catalog.icon;
                    const hasData = catalog.count > 0;
                    
                    return (
                      <Button 
                        key={catalog.id}
                        className="h-auto p-4 flex flex-col items-center gap-2 relative"
                        variant="outline"
                        onClick={() => handleManageCatalog(catalog)}
                      >
                        <IconComponent className="h-12 w-12" />
                        <span className="text-center">{catalog.name}</span>
                        {/* Indicador de estado en la esquina */}
                        {hasData && (
                          <div className="absolute -top-1 -right-1">
                            <div className="h-4 w-4 rounded-full bg-green-600 flex items-center justify-center">
                              <span className="text-white text-xs font-bold">{catalog.count}</span>
                            </div>
                          </div>
                        )}
                      </Button>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>


      {/* Información y Recomendaciones */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-blue-900 mb-2">Estado de tu Configuración</h3>
              <p className="text-blue-800">
                <strong>Vista Unificada:</strong> Aquí se muestran solo los catálogos implementados y funcionales. 
                Cada sección agrupa configuraciones relacionadas para facilitar la administración. 
                Los catálogos con datos aparecen en <span className="text-green-700 font-medium">verde</span>, 
                los que necesitan configuración inicial en <span className="text-orange-700 font-medium">naranja</span>.
              </p>
              {overallStats.progress === 100 && (
                <div className="mt-3 p-3 bg-green-100 border border-green-200 rounded-lg">
                  <p className="text-green-800 font-medium">
                    🎉 ¡Felicitaciones! Todas las configuraciones tienen datos iniciales. 
                    Tu sistema está completamente configurado y listo para usar.
                  </p>
                </div>
              )}
              {overallStats.progress < 100 && (
                <div className="mt-3 p-3 bg-orange-100 border border-orange-200 rounded-lg">
                  <p className="text-orange-800">
                    📋 Tienes <strong>{overallStats.pending} catálogos</strong> que necesitan datos iniciales. 
                    Haz clic en "Configurar" para agregar la información básica necesaria.
                  </p>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}