'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TimePickerProps {
  value: string; // Format: "HH:mm"
  onChange: (time: string) => void;
  disabled?: boolean;
  className?: string;
}

export function TimePicker({ value, onChange, disabled = false, className }: TimePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  // Extraer horas y minutos del valor
  const [hours, minutes] = value.split(':').map(Number);
  
  // Generar opciones de horas (7 AM a 8 PM)
  const hourOptions = Array.from({ length: 14 }, (_, i) => i + 7);
  
  // Opciones de minutos
  const minuteOptions = [0, 15, 30, 45];
  
  const handleHourChange = (hour: number) => {
    const newTime = `${hour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
    onChange(newTime);
  };
  
  const handleMinuteChange = (minute: number) => {
    const newTime = `${hours.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
    onChange(newTime);
  };
  
  const formatDisplayTime = (h: number, m: number) => {
    const period = h >= 12 ? 'PM' : 'AM';
    const displayHour = h > 12 ? h - 12 : h === 0 ? 12 : h;
    return `${displayHour}:${m.toString().padStart(2, '0')} ${period}`;
  };

  if (disabled) {
    return (
      <div className={cn('p-3 bg-gray-50 rounded-md text-sm text-gray-900', className)}>
        {formatDisplayTime(hours, minutes)}
      </div>
    );
  }

  return (
    <div className={cn('relative', className)}>
      <Button
        type="button"
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full h-11 justify-start text-left font-normal"
      >
        <Clock className="mr-2 h-4 w-4" />
        {formatDisplayTime(hours, minutes)}
      </Button>
      
      {isOpen && (
        <>
          {/* Overlay para cerrar */}
          <div 
            className="fixed inset-0 z-40" 
            onClick={() => setIsOpen(false)}
          />
          
          {/* Selector de tiempo */}
          <Card className="absolute z-50 top-12 left-0 w-80 shadow-lg border">
            <CardContent className="p-4">
              <div className="space-y-4">
                {/* Selector de horas */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Hora
                  </Label>
                  <div className="grid grid-cols-7 gap-1">
                    {hourOptions.map((hour) => (
                      <Button
                        key={hour}
                        type="button"
                        size="sm"
                        variant={hours === hour ? "default" : "outline"}
                        onClick={() => handleHourChange(hour)}
                        className={cn(
                          "h-8 text-xs",
                          hours === hour && "bg-blue-500 text-white hover:bg-blue-600"
                        )}
                      >
                        {hour > 12 ? hour - 12 : hour}{hour >= 12 ? 'P' : 'A'}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Selector de minutos */}
                <div>
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Minutos
                  </Label>
                  <div className="grid grid-cols-4 gap-2">
                    {minuteOptions.map((minute) => (
                      <Button
                        key={minute}
                        type="button"
                        size="sm"
                        variant={minutes === minute ? "default" : "outline"}
                        onClick={() => handleMinuteChange(minute)}
                        className={cn(
                          "h-8",
                          minutes === minute && "bg-blue-500 text-white hover:bg-blue-600"
                        )}
                      >
                        :{minute.toString().padStart(2, '0')}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Botones de acción rápida */}
                <div className="border-t pt-3">
                  <Label className="text-sm font-medium text-gray-700 mb-2 block">
                    Horarios comunes
                  </Label>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      { label: '8:00 AM', value: '08:00' },
                      { label: '9:00 AM', value: '09:00' },
                      { label: '2:00 PM', value: '14:00' },
                      { label: '3:00 PM', value: '15:00' }
                    ].map((preset) => (
                      <Button
                        key={preset.value}
                        type="button"
                        size="sm"
                        variant="ghost"
                        onClick={() => {
                          onChange(preset.value);
                          setIsOpen(false);
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-50"
                      >
                        {preset.label}
                      </Button>
                    ))}
                  </div>
                </div>
                
                {/* Botón para cerrar */}
                <div className="border-t pt-3">
                  <Button
                    type="button"
                    size="sm"
                    onClick={() => setIsOpen(false)}
                    className="w-full bg-emerald-600 hover:bg-emerald-700 text-white"
                  >
                    Confirmar
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      )}
    </div>
  );
}