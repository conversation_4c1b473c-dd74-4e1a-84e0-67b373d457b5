CREATE TABLE "admin_audit_logs" (
	"id" text PRIMARY KEY NOT NULL,
	"action" text NOT NULL,
	"executed_by" text NOT NULL,
	"executed_by_name" text NOT NULL,
	"timestamp" timestamp DEFAULT now() NOT NULL,
	"details" jsonb,
	"success" boolean DEFAULT true NOT NULL,
	"error_message" text,
	"ip_address" text,
	"user_agent" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "patient_invitations" (
	"id" text PRIMARY KEY NOT NULL,
	"patient_user_id" text NOT NULL,
	"guardian_email" text NOT NULL,
	"invitation_token" text NOT NULL,
	"status" text DEFAULT 'pending',
	"expires_at" timestamp NOT NULL,
	"created_at" timestamp DEFAULT now(),
	"accepted_at" timestamp,
	CONSTRAINT "patient_invitations_invitation_token_unique" UNIQUE("invitation_token")
);
--> statement-breakpoint
CREATE TABLE "system_config" (
	"id" integer PRIMARY KEY NOT NULL,
	"key" text NOT NULL,
	"value" jsonb NOT NULL,
	"description" text,
	"category" text DEFAULT 'general',
	"active" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now(),
	"updated_at" timestamp DEFAULT now(),
	CONSTRAINT "system_config_key_unique" UNIQUE("key")
);
--> statement-breakpoint
DROP INDEX "medical_consultations_type_idx";--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "checkedInBy" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "noShowReason" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinSent" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinCompleted" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinToken" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinCompletedAt" timestamp;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinCompletedBy" text;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "preCheckinData" jsonb;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "reminderSent48h" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "reminderSent24h" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "appointments" ADD COLUMN "invitationSent" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "logoUrl" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "logoPublicId" text;--> statement-breakpoint
ALTER TABLE "consultories" ADD COLUMN "isPrimary" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "medical_consultations" ADD COLUMN "services" jsonb DEFAULT '[]'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "demographics" jsonb DEFAULT '{}'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "administrative" jsonb DEFAULT '{}'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "emergency_contact" jsonb DEFAULT '{}'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "selectedSymptoms" jsonb DEFAULT '[]'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "pathologicalHistory" jsonb DEFAULT '[]'::jsonb;--> statement-breakpoint
ALTER TABLE "medical_records" ADD COLUMN "nonPathologicalHistory" jsonb DEFAULT '[]'::jsonb;--> statement-breakpoint
ALTER TABLE "admin_audit_logs" ADD CONSTRAINT "admin_audit_logs_executed_by_user_id_fk" FOREIGN KEY ("executed_by") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "patient_invitations" ADD CONSTRAINT "patient_invitations_patient_user_id_user_id_fk" FOREIGN KEY ("patient_user_id") REFERENCES "public"."user"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "admin_audit_logs_action_idx" ON "admin_audit_logs" USING btree ("action");--> statement-breakpoint
CREATE INDEX "admin_audit_logs_executed_by_idx" ON "admin_audit_logs" USING btree ("executed_by");--> statement-breakpoint
CREATE INDEX "admin_audit_logs_timestamp_idx" ON "admin_audit_logs" USING btree ("timestamp");--> statement-breakpoint
CREATE INDEX "admin_audit_logs_success_idx" ON "admin_audit_logs" USING btree ("success");--> statement-breakpoint
CREATE INDEX "patient_invitations_token_idx" ON "patient_invitations" USING btree ("invitation_token");--> statement-breakpoint
CREATE INDEX "patient_invitations_status_idx" ON "patient_invitations" USING btree ("status");--> statement-breakpoint
CREATE INDEX "patient_invitations_guardian_email_idx" ON "patient_invitations" USING btree ("guardian_email");--> statement-breakpoint
CREATE UNIQUE INDEX "system_config_key_idx" ON "system_config" USING btree ("key");--> statement-breakpoint
CREATE INDEX "system_config_category_idx" ON "system_config" USING btree ("category");--> statement-breakpoint
CREATE INDEX "system_config_active_idx" ON "system_config" USING btree ("active");--> statement-breakpoint
ALTER TABLE "appointments" ADD CONSTRAINT "appointments_checkedInBy_user_id_fk" FOREIGN KEY ("checkedInBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medical_consultations" DROP COLUMN "consultationType";