'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { X, Building, FileText, Package, ShoppingCart } from 'lucide-react';
import { ProviderSpecificData, Consultory } from '@/lib/types/onboarding';

interface ProviderSpecificFormProps {
  data: Partial<ProviderSpecificData>;
  onChange: (data: Partial<ProviderSpecificData>) => void;
  onNext: () => void;
  onBack: () => void;
}

const SERVICE_TYPES = [
  'Medicamentos',
  'Suministros Médicos',
  'Equipos Médicos',
  'Insumos de Laboratorio',
  'Productos de Rehabilitación',
  'Servicios de Limpieza',
  'Servicios de Mantenimiento',
  'Servicios de Tecnología',
  'Servicios de Transporte',
  'Alimentos y Bebidas',
  'Papelería y Oficina',
  'Otro'
];

export function ProviderSpecificForm({ data, onChange, onNext, onBack }: ProviderSpecificFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [consultories, setConsultories] = useState<Consultory[]>([]);
  const [loadingConsultories, setLoadingConsultories] = useState(true);
  const [selectedConsultories, setSelectedConsultories] = useState<string[]>(data.consultoryIds || []);
  const [selectedServices, setSelectedServices] = useState<string[]>(data.serviceTypes || []);
  const [customService, setCustomService] = useState('');
  const [documents, setDocuments] = useState<{
    nitDocument?: File;
    commercialLicense?: File;
    catalog?: File;
  }>({});

  // Cargar consultorios disponibles
  useEffect(() => {
    const fetchConsultories = async () => {
      try {
        const response = await fetch('/api/onboarding/consultories');
        const result = await response.json();
        if (result.success) {
          setConsultories(result.data);
        }
      } catch (error) {
        console.error('Error cargando consultorios:', error);
      } finally {
        setLoadingConsultories(false);
      }
    };

    fetchConsultories();
  }, []);

  const handleInputChange = (field: keyof ProviderSpecificData, value: any) => {
    onChange({ ...data, [field]: value });
    // Limpiar error si existe
    if (errors[field]) {
      setErrors({ ...errors, [field]: '' });
    }
  };

  const handleConsultoryToggle = (consultoryId: string) => {
    const newSelected = selectedConsultories.includes(consultoryId)
      ? selectedConsultories.filter(id => id !== consultoryId)
      : [...selectedConsultories, consultoryId];
    
    setSelectedConsultories(newSelected);
    handleInputChange('consultoryIds', newSelected);
  };

  const handleServiceToggle = (service: string) => {
    const newSelected = selectedServices.includes(service)
      ? selectedServices.filter(s => s !== service)
      : [...selectedServices, service];
    
    setSelectedServices(newSelected);
    handleInputChange('serviceTypes', newSelected);
  };

  const addCustomService = () => {
    if (customService.trim() && !selectedServices.includes(customService.trim())) {
      const newServices = [...selectedServices, customService.trim()];
      setSelectedServices(newServices);
      handleInputChange('serviceTypes', newServices);
      setCustomService('');
    }
  };

  const removeService = (serviceToRemove: string) => {
    const newServices = selectedServices.filter(s => s !== serviceToRemove);
    setSelectedServices(newServices);
    handleInputChange('serviceTypes', newServices);
  };

  const handleFileChange = (field: 'nitDocument' | 'commercialLicense' | 'catalog', file: File | null) => {
    if (file) {
      setDocuments({ ...documents, [field]: file });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Validaciones requeridas
    if (!data.companyName?.trim()) newErrors.companyName = 'Nombre de empresa es requerido';
    if (!data.nit?.trim()) newErrors.nit = 'NIT es requerido';
    if (!data.companyAddress?.trim()) newErrors.companyAddress = 'Dirección de empresa es requerida';
    if (!data.companyPhone?.trim()) newErrors.companyPhone = 'Teléfono de empresa es requerido';
    if (!selectedServices.length) newErrors.serviceTypes = 'Debe seleccionar al menos un tipo de servicio';
    if (!selectedConsultories.length) newErrors.consultoryIds = 'Debe seleccionar al menos un consultorio';

    // Validar NIT (formato básico guatemalteco)
    if (data.nit && !/^\d{2,12}-?\d{1}$/.test(data.nit.replace(/\s/g, ''))) {
      newErrors.nit = 'Formato de NIT inválido (ej: 12345678-9)';
    }

    // Validar teléfono empresa
    if (data.companyPhone && !/^\d{8,}$/.test(data.companyPhone.replace(/\s/g, ''))) {
      newErrors.companyPhone = 'Teléfono debe tener al menos 8 dígitos';
    }

    // Validar documento NIT requerido
    if (!documents.nitDocument) newErrors.nitDocument = 'Documento de NIT es requerido';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateForm()) {
      // Agregar documentos a los datos
      const dataWithDocuments = {
        ...data,
        documents: documents
      };
      onChange(dataWithDocuments);
      onNext();
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Información de la Empresa */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Building className="h-5 w-5 text-indigo-600" />
            <span>Información de la Empresa</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Nombre de empresa */}
          <div className="space-y-2">
            <Label htmlFor="companyName">Nombre de la Empresa *</Label>
            <Input
              id="companyName"
              value={data.companyName || ''}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
              className={errors.companyName ? 'border-red-500' : ''}
              placeholder="Farmacia San Carlos S.A."
            />
            {errors.companyName && <p className="text-red-500 text-sm">{errors.companyName}</p>}
          </div>

          {/* NIT */}
          <div className="space-y-2">
            <Label htmlFor="nit">NIT *</Label>
            <Input
              id="nit"
              value={data.nit || ''}
              onChange={(e) => handleInputChange('nit', e.target.value)}
              className={errors.nit ? 'border-red-500' : ''}
              placeholder="12345678-9"
            />
            {errors.nit && <p className="text-red-500 text-sm">{errors.nit}</p>}
          </div>

          {/* Dirección de empresa */}
          <div className="space-y-2">
            <Label htmlFor="companyAddress">Dirección de la Empresa *</Label>
            <Textarea
              id="companyAddress"
              value={data.companyAddress || ''}
              onChange={(e) => handleInputChange('companyAddress', e.target.value)}
              className={errors.companyAddress ? 'border-red-500' : ''}
              placeholder="Avenida Principal 123, Zona 1, Guatemala"
              rows={3}
            />
            {errors.companyAddress && <p className="text-red-500 text-sm">{errors.companyAddress}</p>}
          </div>

          {/* Teléfono de empresa */}
          <div className="space-y-2">
            <Label htmlFor="companyPhone">Teléfono de la Empresa *</Label>
            <Input
              id="companyPhone"
              value={data.companyPhone || ''}
              onChange={(e) => handleInputChange('companyPhone', e.target.value)}
              className={errors.companyPhone ? 'border-red-500' : ''}
              placeholder="+502 3933-3333"
            />
            {errors.companyPhone && <p className="text-red-500 text-sm">{errors.companyPhone}</p>}
          </div>
        </CardContent>
      </Card>

      {/* Tipos de Servicios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Package className="h-5 w-5 text-indigo-600" />
            <span>Tipos de Servicios</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-sm text-gray-600">
            Seleccione los tipos de productos o servicios que ofrece:
          </div>

          {/* Servicios predefinidos */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {SERVICE_TYPES.map(service => (
              <div
                key={service}
                className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                  selectedServices.includes(service)
                    ? 'border-indigo-500 bg-indigo-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => handleServiceToggle(service)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Checkbox
                      checked={selectedServices.includes(service)}
                      onChange={() => handleServiceToggle(service)}
                    />
                    <span className="text-sm font-medium">{service}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Servicio personalizado */}
          <div className="space-y-2">
            <Label htmlFor="customService">Agregar Servicio Personalizado</Label>
            <div className="flex space-x-2">
              <Input
                id="customService"
                value={customService}
                onChange={(e) => setCustomService(e.target.value)}
                placeholder="Escribir tipo de servicio..."
                onKeyPress={(e) => e.key === 'Enter' && addCustomService()}
              />
              <Button 
                type="button" 
                variant="outline" 
                onClick={addCustomService}
                disabled={!customService.trim()}
              >
                Agregar
              </Button>
            </div>
          </div>

          {/* Servicios seleccionados */}
          {selectedServices.length > 0 && (
            <div className="space-y-2">
              <Label>Servicios Seleccionados:</Label>
              <div className="flex flex-wrap gap-2">
                {selectedServices.map(service => (
                  <Badge 
                    key={service} 
                    variant="secondary"
                    className="flex items-center space-x-1"
                  >
                    <span>{service}</span>
                    <X 
                      className="h-3 w-3 cursor-pointer hover:text-red-500" 
                      onClick={() => removeService(service)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {errors.serviceTypes && <p className="text-red-500 text-sm">{errors.serviceTypes}</p>}
        </CardContent>
      </Card>

      {/* Consultorios */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ShoppingCart className="h-5 w-5 text-indigo-600" />
            <span>Consultorios a Atender</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {loadingConsultories ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-indigo-600 mx-auto"></div>
              <p className="mt-2">Cargando consultorios disponibles...</p>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="text-sm text-gray-600">
                Seleccione los consultorios a los que desea brindar servicios:
              </div>
              
              {consultories.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  No hay consultorios disponibles en este momento.
                </div>
              ) : (
                <div className="grid gap-3">
                  {consultories.map(consultory => (
                    <div
                      key={consultory.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedConsultories.includes(consultory.id)
                          ? 'border-indigo-500 bg-indigo-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => handleConsultoryToggle(consultory.id)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={selectedConsultories.includes(consultory.id)}
                            onChange={() => handleConsultoryToggle(consultory.id)}
                          />
                          <div>
                            <div className="font-medium">{consultory.name}</div>
                            {consultory.address && (
                              <div className="text-sm text-gray-500">{consultory.address}</div>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
              
              {errors.consultoryIds && <p className="text-red-500 text-sm">{errors.consultoryIds}</p>}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Documentos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <FileText className="h-5 w-5 text-indigo-600" />
            <span>Documentos Requeridos</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Documento NIT */}
          <div className="space-y-2">
            <Label htmlFor="nitDocument">Documento de NIT * (PDF, JPG, PNG)</Label>
            <Input
              id="nitDocument"
              type="file"
              accept=".pdf,image/*"
              onChange={(e) => handleFileChange('nitDocument', e.target.files?.[0] || null)}
              className={errors.nitDocument ? 'border-red-500' : ''}
            />
            {errors.nitDocument && <p className="text-red-500 text-sm">{errors.nitDocument}</p>}
          </div>

          {/* Licencia comercial */}
          <div className="space-y-2">
            <Label htmlFor="commercialLicense">Licencia Comercial (PDF, JPG, PNG)</Label>
            <Input
              id="commercialLicense"
              type="file"
              accept=".pdf,image/*"
              onChange={(e) => handleFileChange('commercialLicense', e.target.files?.[0] || null)}
            />
            <div className="text-sm text-gray-500">
              Licencia sanitaria, patente de comercio, etc.
            </div>
          </div>

          {/* Catálogo */}
          <div className="space-y-2">
            <Label htmlFor="catalog">Catálogo de Productos/Servicios (PDF)</Label>
            <Input
              id="catalog"
              type="file"
              accept=".pdf"
              onChange={(e) => handleFileChange('catalog', e.target.files?.[0] || null)}
            />
            <div className="text-sm text-gray-500">
              Lista de productos, servicios y precios que ofrece
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Botones de navegación */}
      <div className="flex justify-between">
        <Button 
          variant="outline" 
          onClick={onBack}
          className="flex items-center space-x-2"
        >
          <span>← Volver</span>
        </Button>
        <Button 
          onClick={handleNext}
          className="bg-indigo-600 hover:bg-indigo-700 flex items-center space-x-2"
          disabled={selectedConsultories.length === 0 || selectedServices.length === 0}
        >
          <span>Continuar →</span>
        </Button>
      </div>
    </div>
  );
} 