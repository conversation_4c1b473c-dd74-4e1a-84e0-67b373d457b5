import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { appointments, medicalConsultations } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

interface RouteParams {
  params: Promise<{
    id: string;
  }>;
}

export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const { id } = await params;
    const appointmentId = id;

    // Verificar que la cita existe y está completada
    const appointment = await db
      .select()
      .from(appointments)
      .where(eq(appointments.id, appointmentId))
      .limit(1);

    if (!appointment.length) {
      return NextResponse.json({ 
        error: 'Cita no encontrada' 
      }, { status: 404 });
    }

    if (appointment[0].status !== 'completed') {
      return NextResponse.json({ 
        error: 'Solo se pueden revertir citas completadas' 
      }, { status: 400 });
    }

    // Verificar si hay una consulta médica asociada
    const consultation = await db
      .select()
      .from(medicalConsultations)
      .where(eq(medicalConsultations.appointmentId, appointmentId))
      .limit(1);

    let newStatus = 'in_progress';
    let consultationStatus = 'in_progress';

    // Si no hay consulta médica, regresar a 'checked_in'
    if (!consultation.length) {
      newStatus = 'checked_in';
    } else {
      // Si hay consulta médica completada, revertir también la consulta
      if (consultation[0].status === 'completed') {
        await db
          .update(medicalConsultations)
          .set({
            status: consultationStatus,
            updatedAt: new Date(),
            updatedBy: userId
          })
          .where(eq(medicalConsultations.id, consultation[0].id));
      }
    }

    // Actualizar el estado de la cita
    const updatedAppointment = await db
      .update(appointments)
      .set({
        status: newStatus,
        completedAt: null, // Limpiar fecha de completado
        updatedAt: new Date(),
      })
      .where(eq(appointments.id, appointmentId))
      .returning();

    return NextResponse.json({
      success: true,
      message: `Cita revertida exitosamente a estado: ${newStatus === 'checked_in' ? 'Paciente llegó' : 'En progreso'}`,
      data: {
        appointment: updatedAppointment[0],
        previousState: 'completed',
        newState: newStatus,
        consultationReverted: consultation.length > 0 && consultation[0].status === 'completed'
      }
    });

  } catch (error) {
    console.error('Error reverting appointment:', error);
    return NextResponse.json({ 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}