import { sendAppointmentConfirmation } from '../lib/email-platform';

async function testAppointmentEmail() {
  console.log('🧪 Probando envío de email de confirmación de cita...\n');
  
  try {
    // Datos de prueba
    const testData = {
      patientId: 'test-patient-123',
      appointmentId: 'test-appointment-456',
      appointmentData: {
        title: 'Consulta General',
        scheduledDate: new Date('2025-08-03'),
        startTime: new Date('2025-08-03T14:00:00'),
        endTime: new Date('2025-08-03T14:30:00'),
        doctorFirstName: 'Barbara',
        doctorLastName: '<PERSON>',
        consultoryName: 'Consultorio Principal'
      },
      shortCode: 'ABC123'
    };
    
    // Email de prueba - cambiar por tu email real
    const testEmail = '<EMAIL>';
    
    console.log('📧 Enviando email de prueba a:', testEmail);
    console.log('📋 Datos de la cita:', {
      título: testData.appointmentData.title,
      fecha: testData.appointmentData.scheduledDate.toLocaleDateString('es-ES'),
      hora: testData.appointmentData.startTime.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' }),
      doctor: `${testData.appointmentData.doctorFirstName} ${testData.appointmentData.doctorLastName}`,
      código: testData.shortCode
    });
    
    // Necesitamos mockear el determinePreCheckinRecipient para que devuelva nuestro email de prueba
    // Por ahora, vamos a usar el sistema directo
    const { sendPatientEmail } = require('../lib/email-platform/core');
    
    const result = await sendPatientEmail(
      'appointment_created',
      testEmail,
      {
        patientName: 'Paciente de Prueba',
        doctorName: `${testData.appointmentData.doctorFirstName} ${testData.appointmentData.doctorLastName}`,
        consultoryName: testData.appointmentData.consultoryName,
        appointmentDate: testData.appointmentData.scheduledDate.toLocaleDateString('es-ES', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        }),
        appointmentTime: testData.appointmentData.startTime.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' }),
        appointmentType: testData.appointmentData.title,
        confirmationCode: testData.shortCode,
        preCheckinLink: `http://localhost:3000/pre-checkin/${testData.appointmentId}`
      },
      {
        patientId: testData.patientId,
        appointmentId: testData.appointmentId,
        flowType: 'test'
      }
    );
    
    console.log('\n📊 Resultado:', result);
    
    if (result.success) {
      console.log('✅ Email enviado exitosamente!');
      console.log('📬 Revisa tu bandeja de entrada');
    } else {
      console.error('❌ Error al enviar email:', result.error);
    }
    
  } catch (error) {
    console.error('❌ Error durante la prueba:', error);
  }
}

// Ejecutar prueba
testAppointmentEmail();