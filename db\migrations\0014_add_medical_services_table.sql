-- Migration: Add medical services table
CREATE TABLE "medical_services" (
	"id" text PRIMARY KEY NOT NULL,
	"name" text NOT NULL,
	"description" text,
	"code" text,
	"category" text NOT NULL,
	"basePrice" numeric(10, 2),
	"currency" text DEFAULT 'GTQ',
	"duration" integer,
	"requiresEquipment" boolean DEFAULT false,
	"requiresSpecialist" boolean DEFAULT false,
	"isActive" boolean DEFAULT true,
	"createdAt" timestamp DEFAULT now(),
	"updatedAt" timestamp DEFAULT now(),
	CONSTRAINT "medical_services_code_unique" UNIQUE("code")
);

-- Create indexes for better query performance
CREATE UNIQUE INDEX "medical_services_code_idx" ON "medical_services" ("code");
CREATE INDEX "medical_services_category_idx" ON "medical_services" ("category");
CREATE INDEX "medical_services_active_idx" ON "medical_services" ("isActive");
CREATE INDEX "medical_services_name_idx" ON "medical_services" ("name");
CREATE INDEX "medical_services_price_idx" ON "medical_services" ("basePrice");