import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email';

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();
    
    if (!email) {
      return NextResponse.json({ error: '<PERSON>ail requerido' }, { status: 400 });
    }

    const result = await sendEmail({
      to: email,
      subject: 'Test Email - SGC Sistema',
      html: `
        <h1>¡Email de Prueba!</h1>
        <p>Si recibes este email, Resend está configurado correctamente.</p>
        <p>Enviado desde: SGC Sistema Médico</p>
        <p>Fecha: ${new Date().toLocaleString()}</p>
      `,
    });

    return NextResponse.json({
      success: result.success,
      data: result.data,
      error: result.error,
    });
  } catch (error) {
    console.error('Error sending test email:', error);
    return NextResponse.json({ error: 'Error interno' }, { status: 500 });
  }
}