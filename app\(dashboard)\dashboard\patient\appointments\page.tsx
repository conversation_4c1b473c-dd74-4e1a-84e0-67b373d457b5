'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import { useRouter } from 'next/navigation';
import { 
  Calendar, 
  Clock, 
  ArrowLeft,
  CheckCircle,
  XCircle,
  AlertCircle,
  FileText,
  User,
  Activity,
  Filter,
  Search
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { formatLocalDateTime } from '@/lib/timezone-utils';
import { toast } from 'sonner';

// Configuración de estado de citas
const statusConfig = {
  scheduled: {
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: Calendar,
    label: 'Programada'
  },
  pending_confirmation: {
    color: 'bg-orange-100 text-orange-800 border-orange-200',
    icon: Clock,
    label: 'Pendiente Confirmación'
  },
  confirmed: {
    color: 'bg-cyan-100 text-cyan-800 border-cyan-200',
    icon: CheckCircle,
    label: 'Confirmada'
  },
  checked_in: {
    color: 'bg-teal-100 text-teal-800 border-teal-200',
    icon: User,
    label: 'En Espera'
  },
  in_progress: {
    color: 'bg-purple-100 text-purple-800 border-purple-200',
    icon: Activity,
    label: 'En Consulta'
  },
  completed: {
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: CheckCircle,
    label: 'Completada'
  },
  cancelled: {
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: XCircle,
    label: 'Cancelada'
  },
  no_show: {
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: AlertCircle,
    label: 'No Asistió'
  }
};

export default function AppointmentsHistoryPage() {
  const { user } = useUser();
  const router = useRouter();
  const [appointments, setAppointments] = useState([]);
  const [filteredAppointments, setFilteredAppointments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [timeFilter, setTimeFilter] = useState('all'); // all, upcoming, past

  // Cargar historial de citas
  const fetchAppointments = async () => {
    try {
      if (!user?.id) return;
      
      setLoading(true);
      const response = await fetch(`/api/appointments?patientId=${user.id}&orderBy=scheduledDate&orderDirection=desc&limit=100`);
      const result = await response.json();
      
      if (response.ok && result.data) {
        setAppointments(result.data);
        setFilteredAppointments(result.data);
      } else {
        toast.error('Error al cargar el historial de citas');
      }
    } catch (error) {
      console.error('Error cargando citas:', error);
      toast.error('Error de conexión');
    } finally {
      setLoading(false);
    }
  };

  // Filtrar citas
  useEffect(() => {
    let filtered = [...appointments];
    
    // Filtro por búsqueda
    if (searchTerm) {
      filtered = filtered.filter(apt => 
        apt.doctorFirstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.doctorLastName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.serviceName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        apt.title?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Filtro por estado
    if (statusFilter !== 'all') {
      filtered = filtered.filter(apt => apt.status === statusFilter);
    }
    
    // Filtro por tiempo
    const now = new Date();
    if (timeFilter === 'upcoming') {
      filtered = filtered.filter(apt => new Date(apt.startTime) >= now);
    } else if (timeFilter === 'past') {
      filtered = filtered.filter(apt => new Date(apt.startTime) < now);
    }
    
    setFilteredAppointments(filtered);
  }, [searchTerm, statusFilter, timeFilter, appointments]);

  useEffect(() => {
    fetchAppointments();
  }, [user]);

  // Agrupar citas por mes/año
  const groupAppointmentsByMonth = (appointments) => {
    const grouped = {};
    appointments.forEach(apt => {
      const date = new Date(apt.startTime);
      const monthYear = date.toLocaleDateString('es-GT', { year: 'numeric', month: 'long' });
      
      if (!grouped[monthYear]) {
        grouped[monthYear] = [];
      }
      grouped[monthYear].push(apt);
    });
    return grouped;
  };

  const groupedAppointments = groupAppointmentsByMonth(filteredAppointments);

  // Estadísticas rápidas
  const stats = {
    total: appointments.length,
    completed: appointments.filter(a => a.status === 'completed').length,
    cancelled: appointments.filter(a => a.status === 'cancelled').length,
    upcoming: appointments.filter(a => ['confirmed', 'pending_confirmation', 'scheduled'].includes(a.status)).length
  };

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-6">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="ghost"
          onClick={() => router.push('/dashboard/patient')}
          className="mb-4"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Volver al Dashboard
        </Button>
        
        <h1 className="text-2xl font-bold text-gray-900">Historial de Citas</h1>
        <p className="text-gray-600 mt-1">Consulta todas tus citas médicas</p>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold">{stats.total}</p>
              </div>
              <Calendar className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Completadas</p>
                <p className="text-2xl font-bold text-green-600">{stats.completed}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Próximas</p>
                <p className="text-2xl font-bold text-blue-600">{stats.upcoming}</p>
              </div>
              <Clock className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Canceladas</p>
                <p className="text-2xl font-bold text-red-600">{stats.cancelled}</p>
              </div>
              <XCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Búsqueda */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Buscar por doctor o servicio..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Filtro por tiempo */}
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filtrar por tiempo" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todas</SelectItem>
                <SelectItem value="upcoming">Próximas</SelectItem>
                <SelectItem value="past">Pasadas</SelectItem>
              </SelectContent>
            </Select>
            
            {/* Filtro por estado */}
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-full md:w-[180px]">
                <SelectValue placeholder="Filtrar por estado" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Todos los estados</SelectItem>
                <SelectItem value="completed">Completadas</SelectItem>
                <SelectItem value="confirmed">Confirmadas</SelectItem>
                <SelectItem value="cancelled">Canceladas</SelectItem>
                <SelectItem value="no_show">No asistió</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de citas agrupadas */}
      {loading ? (
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
            </div>
          </CardContent>
        </Card>
      ) : filteredAppointments.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron citas</h3>
            <p className="text-gray-600">
              {searchTerm || statusFilter !== 'all' || timeFilter !== 'all' 
                ? 'Intenta ajustar los filtros de búsqueda'
                : 'Aún no tienes citas en tu historial'}
            </p>
          </CardContent>
        </Card>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedAppointments).map(([monthYear, monthAppointments]) => (
            <div key={monthYear}>
              <h2 className="text-lg font-semibold text-gray-900 mb-3">{monthYear}</h2>
              <div className="space-y-3">
                {monthAppointments.map((appointment) => {
                  const config = statusConfig[appointment.status] || statusConfig.scheduled;
                  const StatusIcon = config.icon;
                  
                  return (
                    <Card key={appointment.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <div className="flex items-center gap-3 mb-2">
                              <StatusIcon className="h-5 w-5 text-gray-600" />
                              <div>
                                <p className="font-medium text-gray-900">
                                  {appointment.serviceName || appointment.title || 'Consulta Médica'}
                                </p>
                                <p className="text-sm text-gray-600">
                                  Dr. {appointment.doctorFirstName} {appointment.doctorLastName}
                                </p>
                              </div>
                            </div>
                            
                            <div className="flex flex-wrap items-center gap-4 text-sm text-gray-600">
                              <div className="flex items-center gap-1">
                                <Calendar className="h-3 w-3" />
                                {formatLocalDateTime(appointment.startTime, 'dd/MM/yyyy')}
                              </div>
                              <div className="flex items-center gap-1">
                                <Clock className="h-3 w-3" />
                                {formatLocalDateTime(appointment.startTime, 'HH:mm')}
                              </div>
                              {appointment.checkedInAt && (
                                <div className="flex items-center gap-1 text-teal-600">
                                  <User className="h-3 w-3" />
                                  Check-in realizado
                                </div>
                              )}
                              {appointment.preCheckinCompleted && (
                                <div className="flex items-center gap-1 text-blue-600">
                                  <FileText className="h-3 w-3" />
                                  Pre-checkin completado
                                </div>
                              )}
                            </div>
                          </div>
                          
                          <Badge className={`${config.color} ml-4`}>
                            {config.label}
                          </Badge>
                        </div>
                        
                        {/* Botones de acción según el estado */}
                        {appointment.status === 'completed' && (
                          <div className="mt-3 pt-3 border-t flex gap-2">
                            <Button 
                              size="sm" 
                              variant="outline"
                              onClick={() => router.push(`/dashboard/patient/appointments/${appointment.id}/details`)}
                            >
                              <FileText className="h-3 w-3 mr-1" />
                              Ver Detalles
                            </Button>
                            {appointment.hasRecord && (
                              <Button 
                                size="sm" 
                                variant="outline"
                                onClick={() => router.push(`/dashboard/patient/records/${appointment.id}`)}
                              >
                                <FileText className="h-3 w-3 mr-1" />
                                Ver Expediente
                              </Button>
                            )}
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}