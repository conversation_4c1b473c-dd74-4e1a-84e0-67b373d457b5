'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useUser } from '@clerk/nextjs';

interface RegionalSettings {
  dateFormat: string;
  dateTimeFormat: string;
  timeFormat: string;
  currency: string;
  currencySymbol: string;
  currencyPosition: 'before' | 'after';
  locale: string;
  timezone: string;
  decimalSeparator: string;
  thousandsSeparator: string;
  weekStartsOn: 0 | 1 | 2 | 3 | 4 | 5 | 6;
  phoneFormat: string;
  phoneDigits: number;
  phoneExample: string;
}

interface ConsultoryInfo {
  id: string;
  name: string;
  countryId: number;
  phoneCode: string;
}

interface RegionalConfigContextType {
  config: RegionalSettings;
  consultory: ConsultoryInfo | null;
  loading: boolean;
  error: string | null;
  refreshConfig: () => Promise<void>;
  getDatePlaceholder: () => string;
  getPhonePlaceholder: () => string;
}

// Configuración por defecto
const defaultConfig: RegionalSettings = {
  dateFormat: 'dd/MM/yyyy',
  dateTimeFormat: 'dd/MM/yyyy HH:mm',
  timeFormat: 'HH:mm',
  currency: 'GTQ',
  currencySymbol: 'Q',
  currencyPosition: 'before',
  locale: 'es-GT',
  timezone: 'America/Guatemala',
  decimalSeparator: '.',
  thousandsSeparator: ',',
  weekStartsOn: 1,
  phoneFormat: '####-####',
  phoneDigits: 8,
  phoneExample: '5555-5555'
};

const RegionalConfigContext = createContext<RegionalConfigContextType>({
  config: defaultConfig,
  consultory: null,
  loading: true,
  error: null,
  refreshConfig: async () => {},
  getDatePlaceholder: () => 'dd/mm/aaaa',
  getPhonePlaceholder: () => '+502 5555-5555'
});

export function RegionalConfigProvider({ children }: { children: React.ReactNode }) {
  const { user } = useUser();
  const [config, setConfig] = useState<RegionalSettings>(defaultConfig);
  const [consultory, setConsultory] = useState<ConsultoryInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchConfig = async () => {
    try {
      setLoading(true);
      setError(null);

      // 1. Primero obtener la configuración global del sistema
      const systemResponse = await fetch('/api/system/config?category=regional');
      
      if (systemResponse.ok) {
        const systemData = await systemResponse.json();
        const systemConfig = systemData.data || {};
        
        // Convertir el formato del API al formato esperado
        const globalConfig: RegionalSettings = {
          dateFormat: systemConfig.dateFormat || defaultConfig.dateFormat,
          dateTimeFormat: systemConfig.dateTimeFormat || defaultConfig.dateTimeFormat,
          timeFormat: systemConfig.timeFormat || defaultConfig.timeFormat,
          currency: systemConfig.currency || defaultConfig.currency,
          currencySymbol: systemConfig.currencySymbol || defaultConfig.currencySymbol,
          currencyPosition: systemConfig.currencyPosition || defaultConfig.currencyPosition,
          locale: systemConfig.locale || defaultConfig.locale,
          timezone: systemConfig.timezone || defaultConfig.timezone,
          decimalSeparator: systemConfig.decimalSeparator || defaultConfig.decimalSeparator,
          thousandsSeparator: systemConfig.thousandsSeparator || defaultConfig.thousandsSeparator,
          weekStartsOn: systemConfig.weekStartsOn || defaultConfig.weekStartsOn,
          phoneFormat: systemConfig.phoneFormat || defaultConfig.phoneFormat,
          phoneDigits: systemConfig.phoneDigits || defaultConfig.phoneDigits,
          phoneExample: systemConfig.phoneExample || defaultConfig.phoneExample
        };

        // 2. Si el usuario tiene un consultorio asignado, obtener su configuración
        if (user?.publicMetadata?.consultoryId) {
          const consultoryResponse = await fetch(`/api/consultories/${user.publicMetadata.consultoryId}`);
          
          if (consultoryResponse.ok) {
            const consultoryData = await consultoryResponse.json();
            const consultoryInfo = consultoryData.data;
            const consultorySettings = consultoryInfo?.regionalSettings || {};
            
            // Obtener información del país si existe
            let phoneCode = '+502'; // Default Guatemala
            if (consultoryInfo?.countryId) {
              const countryResponse = await fetch(`/api/catalogs/countries/${consultoryInfo.countryId}`);
              if (countryResponse.ok) {
                const countryData = await countryResponse.json();
                phoneCode = countryData.data?.phoneCode || '+502';
              }
            }
            
            // Guardar información del consultorio
            setConsultory({
              id: consultoryInfo.id,
              name: consultoryInfo.name,
              countryId: consultoryInfo.countryId,
              phoneCode: phoneCode
            });
            
            // Mezclar configuración global con la del consultorio (consultorio tiene prioridad)
            const mergedConfig: RegionalSettings = {
              ...globalConfig,
              ...consultorySettings
            };
            
            setConfig(mergedConfig);
          } else {
            // Si no se puede obtener el consultorio, usar solo la configuración global
            setConfig(globalConfig);
          }
        } else {
          // Si no hay consultorio asignado, usar solo la configuración global
          setConfig(globalConfig);
        }
      } else {
        // Si falla la carga de configuración del sistema, usar valores por defecto
        setConfig(defaultConfig);
      }
    } catch (err) {
      console.error('Error loading regional configuration:', err);
      setError('Error al cargar la configuración regional');
      setConfig(defaultConfig);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchConfig();
  }, [user?.publicMetadata?.consultoryId]);

  const refreshConfig = async () => {
    await fetchConfig();
  };

  const getDatePlaceholder = () => {
    // Convert the date format to a readable placeholder
    // dd/MM/yyyy -> dd/mm/aaaa (Spanish)
    // MM/dd/yyyy -> mm/dd/yyyy (English)
    // yyyy-MM-dd -> aaaa-mm-dd
    return config.dateFormat
      .toLowerCase()
      .replace(/yyyy/g, 'aaaa')
      .replace(/yy/g, 'aa');
  };

  const getPhonePlaceholder = () => {
    // Combinar el código del país con el ejemplo de formato
    const phoneCode = consultory?.phoneCode || '+502';
    const example = config.phoneExample || '5555-5555';
    return `${phoneCode} ${example}`;
  };

  return (
    <RegionalConfigContext.Provider value={{ config, consultory, loading, error, refreshConfig, getDatePlaceholder, getPhonePlaceholder }}>
      {children}
    </RegionalConfigContext.Provider>
  );
}

export function useRegionalConfig() {
  const context = useContext(RegionalConfigContext);
  if (!context) {
    throw new Error('useRegionalConfig must be used within RegionalConfigProvider');
  }
  return context;
}