/**
 * UTILIDADES MEJORADAS PARA PACIENTES
 * Manejo correcto de edad, encargados y notificaciones
 */

import { db } from '@/db/drizzle';
import { user, guardianPatientRelations } from '@/db/schema';
import { eq } from 'drizzle-orm';

/**
 * Verifica si un paciente es menor de edad basado en su fecha de nacimiento
 */
export function isMinorPatient(dateOfBirth: Date | string | null): boolean {
  if (!dateOfBirth) return false;
  
  const birthDate = typeof dateOfBirth === 'string' ? new Date(dateOfBirth) : dateOfBirth;
  const today = new Date();
  const age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  // Ajustar si no ha cumplido años este año
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    return age - 1 < 18;
  }
  
  return age < 18;
}

/**
 * Determina quién debe recibir las notificaciones del paciente
 */
export async function getNotificationRecipient(patientId: string): Promise<{
  canSendEmail: boolean;
  recipientEmail: string | null;
  recipientType: 'patient' | 'guardian' | 'emergency_contact' | 'none';
  recipientName: string | null;
  reason?: string;
}> {
  // 1. Obtener datos del paciente
  const patientData = await db
    .select()
    .from(user)
    .where(eq(user.id, patientId))
    .limit(1);
    
  if (patientData.length === 0) {
    return {
      canSendEmail: false,
      recipientEmail: null,
      recipientType: 'none',
      recipientName: null,
      reason: 'Paciente no encontrado'
    };
  }
  
  const patient = patientData[0];
  
  // 2. Verificar si es menor
  const isMinor = isMinorPatient(patient.dateOfBirth);
  
  // 3. Si es menor, buscar encargado
  if (isMinor) {
    // Buscar encargados registrados
    const guardians = await db
      .select({
        guardianId: guardianPatientRelations.guardianId,
        isPrimary: guardianPatientRelations.isPrimary,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      })
      .from(guardianPatientRelations)
      .innerJoin(user, eq(guardianPatientRelations.guardianId, user.id))
      .where(eq(guardianPatientRelations.patientId, patientId));
    
    // Buscar encargado principal o el primero disponible
    const primaryGuardian = guardians.find(g => g.isPrimary) || guardians[0];
    
    if (primaryGuardian && primaryGuardian.email && !primaryGuardian.email.includes('@temp.local')) {
      return {
        canSendEmail: true,
        recipientEmail: primaryGuardian.email,
        recipientType: 'guardian',
        recipientName: `${primaryGuardian.firstName} ${primaryGuardian.lastName}`
      };
    }
    
    // Si no hay encargado pero hay contacto de emergencia con email
    if (patient.emergencyContact && patient.emergencyPhone) {
      // Por ahora solo retornamos que no se puede enviar
      // TODO: Implementar lógica para crear encargado desde contacto de emergencia
      return {
        canSendEmail: false,
        recipientEmail: null,
        recipientType: 'none',
        recipientName: null,
        reason: 'Paciente menor sin encargado con email válido. Contacto de emergencia: ' + patient.emergencyContact
      };
    }
    
    return {
      canSendEmail: false,
      recipientEmail: null,
      recipientType: 'none',
      recipientName: null,
      reason: 'Paciente menor sin encargado registrado'
    };
  }
  
  // 4. Si es adulto, verificar su email
  if (patient.email && !patient.email.includes('@temp.local')) {
    return {
      canSendEmail: true,
      recipientEmail: patient.email,
      recipientType: 'patient',
      recipientName: `${patient.firstName} ${patient.lastName}`
    };
  }
  
  // 5. Si es adulto sin email válido
  return {
    canSendEmail: false,
    recipientEmail: null,
    recipientType: 'none',
    recipientName: null,
    reason: 'Paciente sin email válido. Por favor actualice el email del paciente para recibir notificaciones.'
  };
}

/**
 * Crea un encargado desde el contacto de emergencia (si no existe)
 */
export async function createGuardianFromEmergencyContact(
  patientId: string,
  emergencyEmail?: string
): Promise<{ success: boolean; guardianId?: string; error?: string }> {
  // TODO: Implementar lógica para crear encargado desde contacto de emergencia
  // 1. Verificar que el paciente sea menor
  // 2. Verificar datos del contacto de emergencia
  // 3. Buscar o crear usuario para el encargado
  // 4. Crear relación guardian-patient
  // 5. Enviar email de activación al encargado
  
  return {
    success: false,
    error: 'Funcionalidad en desarrollo'
  };
}