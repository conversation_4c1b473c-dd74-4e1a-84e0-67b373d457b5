import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { municipalities, departments } from '@/db/schema';
import { and, eq, ilike, or, count, sql } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1');
    const limit = parseInt(url.searchParams.get('limit') || '10');
    const search = url.searchParams.get('search') || '';
    const active = url.searchParams.get('active');
    const departmentId = url.searchParams.get('departmentId');

    const offset = (page - 1) * limit;

    // Build where conditions
    const conditions = [];
    
    if (search) {
      conditions.push(ilike(municipalities.name, `%${search}%`));
    }

    if (active !== null && active !== undefined && active !== '') {
      conditions.push(eq(municipalities.isActive, active === 'true'));
    }

    if (departmentId) {
      conditions.push(eq(municipalities.departmentId, parseInt(departmentId)));
    }

    const whereClause = conditions.length > 0 ? and(...conditions) : undefined;

    // Get total count
    const [{ count: total }] = await db
      .select({ count: count() })
      .from(municipalities)
      .where(whereClause);

    // Get paginated data with department info
    const municipalitiesData = await db
      .select({
        id: municipalities.id,
        name: municipalities.name,
        departmentId: municipalities.departmentId,
        departmentName: departments.name,
        isActive: municipalities.isActive,
        createdAt: municipalities.createdAt,
        updatedAt: municipalities.updatedAt
      })
      .from(municipalities)
      .leftJoin(departments, eq(municipalities.departmentId, departments.id))
      .where(whereClause)
      .orderBy(municipalities.name)
      .limit(limit)
      .offset(offset);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      data: municipalitiesData,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching municipalities:', error);
    return NextResponse.json(
      { error: 'Failed to fetch municipalities' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { name, departmentId, isActive = true } = body;

    if (!name || !departmentId) {
      return NextResponse.json(
        { error: 'Name and departmentId are required' },
        { status: 400 }
      );
    }

    // Get the next available ID
    const maxIdResult = await db
      .select({ maxId: sql<number>`COALESCE(MAX(id), 0)` })
      .from(municipalities);
    
    const nextId = (maxIdResult[0]?.maxId || 0) + 1;

    const newMunicipality = await db.insert(municipalities).values({
      id: nextId,
      name,
      departmentId: parseInt(departmentId),
      isActive
    }).returning();

    return NextResponse.json({ 
      success: true, 
      data: newMunicipality[0],
      message: 'Municipalidad creada exitosamente'
    }, { status: 201 });
  } catch (error) {
    console.error('Error creating municipality:', error);
    return NextResponse.json(
      { error: 'Failed to create municipality' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { id, name, departmentId, isActive } = body;

    if (!id || !name) {
      return NextResponse.json(
        { error: 'ID and name are required' },
        { status: 400 }
      );
    }

    const updatedMunicipality = await db
      .update(municipalities)
      .set({
        name,
        departmentId: departmentId ? parseInt(departmentId) : undefined,
        isActive
      })
      .where(eq(municipalities.id, parseInt(id)))
      .returning();

    if (updatedMunicipality.length === 0) {
      return NextResponse.json(
        { error: 'Municipality not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(updatedMunicipality[0]);
  } catch (error) {
    console.error('Error updating municipality:', error);
    return NextResponse.json(
      { error: 'Failed to update municipality' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { userId } = await auth();
    if (!userId) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const url = new URL(request.url);
    const id = url.searchParams.get('id');

    if (!id) {
      return NextResponse.json(
        { error: 'ID is required' },
        { status: 400 }
      );
    }

    // Soft delete - set isActive to false
    const deletedMunicipality = await db
      .update(municipalities)
      .set({
        isActive: false,
      })
      .where(eq(municipalities.id, parseInt(id)))
      .returning();

    if (deletedMunicipality.length === 0) {
      return NextResponse.json(
        { error: 'Municipality not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Municipality deleted successfully' });
  } catch (error) {
    console.error('Error deleting municipality:', error);
    return NextResponse.json(
      { error: 'Failed to delete municipality' },
      { status: 500 }
    );
  }
} 