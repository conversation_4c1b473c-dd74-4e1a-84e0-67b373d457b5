import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { user, userRoles } from '@/db/schema';
import { eq, and, ilike, desc, asc } from 'drizzle-orm';

export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const search = searchParams.get('search');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = Math.min(parseInt(searchParams.get('limit') || '20'), 100);
    const offset = (page - 1) * limit;

    // Query base para obtener usuarios con rol de paciente activo
    let query = db
      .select({
        id: user.id,
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
        phone: user.phone,
        documentType: user.documentType,
        documentNumber: user.documentNumber,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
      })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active'),
          eq(user.overallStatus, 'active')
        )
      );

    // Aplicar filtro de búsqueda si existe
    if (search) {
      query = query.where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active'),
          eq(user.overallStatus, 'active'),
          ilike(user.firstName, `%${search}%`)
        )
      );
    }

    // Aplicar ordenamiento, paginación y ejecutar
    const patients = await query
      .orderBy(asc(user.firstName), asc(user.lastName))
      .limit(limit)
      .offset(offset);

    // Obtener conteo total para paginación
    const totalResult = await db
      .select({ count: user.id })
      .from(user)
      .innerJoin(userRoles, eq(user.id, userRoles.userId))
      .where(
        and(
          eq(userRoles.role, 'patient'),
          eq(userRoles.status, 'active'),
          eq(user.overallStatus, 'active')
        )
      );

    const total = totalResult.length;

    return NextResponse.json({
      success: true,
      data: patients,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit),
        hasNext: page < Math.ceil(total / limit),
        hasPrev: page > 1,
      },
    });

  } catch (error) {
    console.error('Error fetching patients:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Error interno del servidor' 
    }, { status: 500 });
  }
}