import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';

// Cargar variables de entorno
dotenv.config({ path: '.env.local' });

const sql = neon(process.env.DATABASE_URL!);
const db = drizzle(sql);

async function applyMigration() {
  console.log('🚀 Aplicando migración de appointment_logs...');
  
  try {
    // Leer el archivo SQL
    const migrationPath = path.join(process.cwd(), 'db', 'migrations', '0034_add_appointment_logs.sql');
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');
    
    console.log('📄 Ejecutando SQL...');
    
    // Ejecutar la migración completa
    console.log('⚡ Ejecutando migración completa...');
    
    // <PERSON>rear la tabla
    await sql`
      CREATE TABLE IF NOT EXISTS "appointment_logs" (
        "id" text PRIMARY KEY NOT NULL,
        "appointment_id" text NOT NULL,
        "event_type" text NOT NULL,
        "event_category" text NOT NULL,
        "title" text NOT NULL,
        "description" text,
        "metadata" jsonb,
        "triggered_by" text,
        "triggered_by_role" text,
        "previous_state" jsonb,
        "new_state" jsonb,
        "email_type" text,
        "email_recipient" text,
        "email_status" text,
        "ip_address" text,
        "user_agent" text,
        "created_at" timestamp DEFAULT now() NOT NULL
      )
    `;
    console.log('✅ Tabla appointment_logs creada');
    
    // Crear índices
    await sql`CREATE INDEX IF NOT EXISTS "appointment_logs_appointment_idx" ON "appointment_logs" ("appointment_id")`;
    await sql`CREATE INDEX IF NOT EXISTS "appointment_logs_event_type_idx" ON "appointment_logs" ("event_type")`;
    await sql`CREATE INDEX IF NOT EXISTS "appointment_logs_event_category_idx" ON "appointment_logs" ("event_category")`;
    await sql`CREATE INDEX IF NOT EXISTS "appointment_logs_created_at_idx" ON "appointment_logs" ("created_at")`;
    await sql`CREATE INDEX IF NOT EXISTS "appointment_logs_triggered_by_idx" ON "appointment_logs" ("triggered_by")`;
    console.log('✅ Índices creados');
    
    // Agregar foreign keys
    try {
      await sql`ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_appointment_id_appointments_id_fk" FOREIGN KEY ("appointment_id") REFERENCES "appointments"("id") ON DELETE cascade`;
      console.log('✅ FK a appointments agregada');
    } catch (e) {
      console.log('⚠️ FK a appointments ya existe o error:', (e as Error).message);
    }
    
    try {
      await sql`ALTER TABLE "appointment_logs" ADD CONSTRAINT "appointment_logs_triggered_by_user_id_fk" FOREIGN KEY ("triggered_by") REFERENCES "user"("id")`;
      console.log('✅ FK a user agregada');
    } catch (e) {
      console.log('⚠️ FK a user ya existe o error:', (e as Error).message);
    }
    
    console.log('✅ Migración aplicada exitosamente');
    console.log('📋 Tabla appointment_logs creada con índices');
    
  } catch (error) {
    console.error('❌ Error aplicando migración:', error);
    process.exit(1);
  }
}

applyMigration();