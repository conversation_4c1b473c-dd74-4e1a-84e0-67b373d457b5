CREATE TABLE "medication_prices" (
	"id" text PRIMARY KEY NOT NULL,
	"medicationId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"purchasePrice" numeric(10, 2),
	"salePrice" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ' NOT NULL,
	"isActive" boolean DEFAULT true NOT NULL,
	"effectiveFrom" timestamp DEFAULT now() NOT NULL,
	"effectiveTo" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"createdBy" text NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sales_invoices" (
	"id" text PRIMARY KEY NOT NULL,
	"transactionId" text NOT NULL,
	"companyId" text NOT NULL,
	"invoiceNumber" text NOT NULL,
	"felNumber" text,
	"felSeries" text,
	"subtotal" numeric(10, 2) NOT NULL,
	"taxAmount" numeric(10, 2) DEFAULT '0',
	"total" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ' NOT NULL,
	"status" text DEFAULT 'draft' NOT NULL,
	"pdfUrl" text,
	"xmlUrl" text,
	"customerName" text NOT NULL,
	"customerNit" text,
	"customerAddress" text,
	"generatedAt" timestamp,
	"sentAt" timestamp,
	"cancelledAt" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "sales_invoices_invoiceNumber_unique" UNIQUE("invoiceNumber"),
	CONSTRAINT "sales_invoices_felNumber_unique" UNIQUE("felNumber")
);
--> statement-breakpoint
CREATE TABLE "sales_payments" (
	"id" text PRIMARY KEY NOT NULL,
	"transactionId" text NOT NULL,
	"paymentMethod" text NOT NULL,
	"amount" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ' NOT NULL,
	"referenceNumber" text,
	"authorizationCode" text,
	"externalTransactionId" text,
	"status" text DEFAULT 'pending' NOT NULL,
	"paymentData" jsonb,
	"processedAt" timestamp,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sales_transaction_items" (
	"id" text PRIMARY KEY NOT NULL,
	"transactionId" text NOT NULL,
	"itemType" text NOT NULL,
	"itemId" text NOT NULL,
	"description" text NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"unitPrice" numeric(10, 2) NOT NULL,
	"discount" numeric(10, 2) DEFAULT '0',
	"subtotal" numeric(10, 2) NOT NULL,
	"taxAmount" numeric(10, 2) DEFAULT '0',
	"total" numeric(10, 2) NOT NULL,
	"companyId" text,
	"batchNumber" text,
	"expirationDate" timestamp,
	"stockLocationId" text,
	"createdAt" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sales_transactions" (
	"id" text PRIMARY KEY NOT NULL,
	"consultationId" text NOT NULL,
	"patientId" text NOT NULL,
	"doctorId" text NOT NULL,
	"consultoryId" text NOT NULL,
	"subtotal" numeric(10, 2) NOT NULL,
	"taxAmount" numeric(10, 2) DEFAULT '0',
	"discountAmount" numeric(10, 2) DEFAULT '0',
	"totalAmount" numeric(10, 2) NOT NULL,
	"currency" text DEFAULT 'GTQ' NOT NULL,
	"status" text DEFAULT 'pending' NOT NULL,
	"createdAt" timestamp DEFAULT now() NOT NULL,
	"createdBy" text NOT NULL,
	"updatedAt" timestamp DEFAULT now() NOT NULL,
	"completedAt" timestamp
);
--> statement-breakpoint
ALTER TABLE "medication_prices" ADD CONSTRAINT "medication_prices_medicationId_medications_id_fk" FOREIGN KEY ("medicationId") REFERENCES "public"."medications"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medication_prices" ADD CONSTRAINT "medication_prices_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "medication_prices" ADD CONSTRAINT "medication_prices_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_invoices" ADD CONSTRAINT "sales_invoices_transactionId_sales_transactions_id_fk" FOREIGN KEY ("transactionId") REFERENCES "public"."sales_transactions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_invoices" ADD CONSTRAINT "sales_invoices_companyId_companies_id_fk" FOREIGN KEY ("companyId") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_payments" ADD CONSTRAINT "sales_payments_transactionId_sales_transactions_id_fk" FOREIGN KEY ("transactionId") REFERENCES "public"."sales_transactions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transaction_items" ADD CONSTRAINT "sales_transaction_items_transactionId_sales_transactions_id_fk" FOREIGN KEY ("transactionId") REFERENCES "public"."sales_transactions"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transaction_items" ADD CONSTRAINT "sales_transaction_items_companyId_companies_id_fk" FOREIGN KEY ("companyId") REFERENCES "public"."companies"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transactions" ADD CONSTRAINT "sales_transactions_patientId_user_id_fk" FOREIGN KEY ("patientId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transactions" ADD CONSTRAINT "sales_transactions_doctorId_user_id_fk" FOREIGN KEY ("doctorId") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transactions" ADD CONSTRAINT "sales_transactions_consultoryId_consultories_id_fk" FOREIGN KEY ("consultoryId") REFERENCES "public"."consultories"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sales_transactions" ADD CONSTRAINT "sales_transactions_createdBy_user_id_fk" FOREIGN KEY ("createdBy") REFERENCES "public"."user"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE UNIQUE INDEX "medication_prices_med_cons_idx" ON "medication_prices" USING btree ("medicationId","consultoryId");--> statement-breakpoint
CREATE INDEX "medication_prices_active_idx" ON "medication_prices" USING btree ("isActive");--> statement-breakpoint
CREATE INDEX "sales_invoices_transaction_idx" ON "sales_invoices" USING btree ("transactionId");--> statement-breakpoint
CREATE INDEX "sales_invoices_company_idx" ON "sales_invoices" USING btree ("companyId");--> statement-breakpoint
CREATE UNIQUE INDEX "sales_invoices_fel_number_idx" ON "sales_invoices" USING btree ("felNumber");--> statement-breakpoint
CREATE INDEX "sales_invoices_status_idx" ON "sales_invoices" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sales_payments_transaction_idx" ON "sales_payments" USING btree ("transactionId");--> statement-breakpoint
CREATE INDEX "sales_payments_method_idx" ON "sales_payments" USING btree ("paymentMethod");--> statement-breakpoint
CREATE INDEX "sales_payments_status_idx" ON "sales_payments" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sales_items_transaction_idx" ON "sales_transaction_items" USING btree ("transactionId");--> statement-breakpoint
CREATE INDEX "sales_items_type_idx" ON "sales_transaction_items" USING btree ("itemType");--> statement-breakpoint
CREATE INDEX "sales_items_company_idx" ON "sales_transaction_items" USING btree ("companyId");--> statement-breakpoint
CREATE INDEX "sales_transactions_consultation_idx" ON "sales_transactions" USING btree ("consultationId");--> statement-breakpoint
CREATE INDEX "sales_transactions_patient_idx" ON "sales_transactions" USING btree ("patientId");--> statement-breakpoint
CREATE INDEX "sales_transactions_doctor_idx" ON "sales_transactions" USING btree ("doctorId");--> statement-breakpoint
CREATE INDEX "sales_transactions_status_idx" ON "sales_transactions" USING btree ("status");