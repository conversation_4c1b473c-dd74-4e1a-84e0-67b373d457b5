-- Migración para crear tabla de logs de auditoría administrativa
CREATE TABLE IF NOT EXISTS "admin_audit_logs" (
	"id" text PRIMARY KEY NOT NULL,
	"action" text NOT NULL,
	"executed_by" text NOT NULL,
	"executed_by_name" text NOT NULL,
	"timestamp" timestamp with time zone DEFAULT now() NOT NULL,
	"details" jsonb,
	"success" boolean NOT NULL DEFAULT true,
	"error_message" text,
	"ip_address" text,
	"user_agent" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);

-- Índices para mejorar el rendimiento
CREATE INDEX IF NOT EXISTS "admin_audit_logs_action_idx" ON "admin_audit_logs" ("action");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_executed_by_idx" ON "admin_audit_logs" ("executed_by");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_timestamp_idx" ON "admin_audit_logs" ("timestamp");
CREATE INDEX IF NOT EXISTS "admin_audit_logs_success_idx" ON "admin_audit_logs" ("success");

-- Función para actualizar updated_at automáticamente
CREATE OR REPLACE FUNCTION update_admin_audit_logs_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger para updated_at
CREATE TRIGGER update_admin_audit_logs_updated_at
    BEFORE UPDATE ON admin_audit_logs
    FOR EACH ROW
    EXECUTE FUNCTION update_admin_audit_logs_updated_at();