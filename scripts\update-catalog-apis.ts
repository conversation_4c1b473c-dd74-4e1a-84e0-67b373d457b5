// Script para actualizar todas las APIs de catálogos con manejo de reintentos
// Este script es para referencia - se debe aplicar manualmente a cada API

import fs from 'fs';
import path from 'path';

const catalogAPIs = [
  'countries',
  'departments', 
  'municipalities',
  'occupations',
  'relationships',
  'medical-specialties',
  'currencies',
  'consultories',
  'activity-types',
  'pathological-history',
  'non-pathological-history',
  'media-sources',
  'education-levels',
  'document-types',
  'marital-status',
  'companies',
  'medical-services',
  'doctor-service-prices',
  'medications',
  'symptoms',
  'religions'
];

console.log('APIs de catálogos que necesitan actualización:');
console.log('=============================================');

catalogAPIs.forEach((api, index) => {
  console.log(`${index + 1}. /api/catalogs/${api}/route.ts`);
});

console.log('\nCambios requeridos:');
console.log('==================');
console.log('1. Importar: import { executeWithRetry, handleDatabaseError } from "@/lib/db-utils";');
console.log('2. Envolver queries de base de datos con executeWithRetry()');
console.log('3. Reemplazar el manejo de errores genérico con handleDatabaseError()');
console.log('\nEjemplo:');
console.log('--------');
console.log(`
// Antes:
const data = await db.select().from(table);

// Después:
const data = await executeWithRetry(async () => {
  return await db.select().from(table);
});

// Manejo de errores antes:
} catch (error: any) {
  console.error('Error:', error);
  return NextResponse.json(
    { error: 'Error interno del servidor' },
    { status: 500 }
  );
}

// Manejo de errores después:
} catch (error: any) {
  const { message, status } = handleDatabaseError(error);
  return NextResponse.json(
    { error: message },
    { status }
  );
}
`);

console.log('\nNota: Este script es solo una guía. Los cambios deben aplicarse manualmente.');