# Actualizar Checkboxes con Colores Temáticos en Catálogos ✅ COMPLETADO

## Objetivo
Actualizar todos los checkboxes en los componentes de catálogos para que tengan colores temáticos que coincidan con el ícono de cada catálogo.

## Lista de Tareas

### Catálogos y sus Colores Temáticos:
- [x] medical-specialties - azul (Stethoscope)
- [x] occupations - azul (Briefcase)
- [x] relationships - rosa/pink (Users/Heart)
- [x] departments - naranja (Building)
- [x] municipalities - rojo (MapPin)
- [x] countries - verde (Globe)
- [x] activity-types - morado (Calendar)
- [x] pathological-history - azul (TestTube2)
- [x] non-pathological-history - azul (Shield)
- [x] media-sources - morado (Radio)
- [x] education-levels - azul (GraduationCap)
- [x] document-types - gris (CreditCard)
- [x] marital-status - rosa (Heart)
- [x] consultories - teal (Hospital)

### Patrón a Aplicar:
Para cada archivo, buscar y actualizar:
1. TableHead con className="w-12" - Checkbox de seleccionar todo
2. TableCell en las filas - Checkbox individual
3. Vista móvil si existe - Checkbox individual

Reemplazar con:
```tsx
<Checkbox className="border-[color]-500 data-[state=checked]:bg-[color]-500 data-[state=checked]:border-[color]-500" />
```

## Colores específicos de Tailwind:
- azul: blue
- rosa/pink: pink
- naranja: orange
- rojo: red
- verde: green
- morado: purple
- gris: gray
- teal: teal