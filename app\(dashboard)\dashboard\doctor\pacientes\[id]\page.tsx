'use client';

import { useState, useEffect, use } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useUser } from '@clerk/nextjs';
import { 
  User, 
  ArrowLeft, 
  Edit, 
  Save, 
  Heart, 
  Shield, 
  Users, 
  Phone, 
  Mail, 
  Calendar, 
  MapPin, 
  Briefcase, 
  AlertTriangle,
  Activity,
  RefreshCw,
  FileText,
  Stethoscope,
  Clock,
  Building,
  Pill,
  IdCard,
  Baby
} from 'lucide-react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';
import { format } from 'date-fns';
import { es } from 'date-fns/locale';
import { cn } from '@/lib/utils';
import { formatPhoneForDisplay } from '@/lib/phone-utils';
import { GuardianManagement } from '@/components/medical-records/guardian-management';

// Helper function to safely format dates
const formatDate = (date: string | Date | null | undefined, formatString: string = 'dd/MM/yyyy'): string => {
  if (!date) return 'No registrada';
  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (isNaN(dateObj.getTime())) return 'Fecha inválida';
    return format(dateObj, formatString, { locale: es });
  } catch (error) {
    return 'Error en fecha';
  }
};

// Helper function to format gender
const formatGender = (gender: string | null | undefined): string => {
  if (!gender) return 'No especificado';
  switch (gender.toLowerCase()) {
    case 'male':
      return 'Masculino';
    case 'female':
      return 'Femenino';
    case 'other':
      return 'Otro';
    default:
      return gender; // Fallback para valores legacy
  }
};

// Helper function to format relationship
const formatRelationship = (relationship: string | null | undefined): string => {
  if (!relationship) return 'No especificado';
  
  const relationships: { [key: string]: string } = {
    'padre': 'Padre',
    'madre': 'Madre',
    'abuelo': 'Abuelo',
    'abuela': 'Abuela',
    'tio': 'Tío',
    'tia': 'Tía',
    'hermano': 'Hermano',
    'hermana': 'Hermana',
    'tutor_legal': 'Tutor Legal',
    'cuidador': 'Cuidador',
    'otro': 'Otro'
  };
  
  return relationships[relationship.toLowerCase()] || relationship;
};

interface PatientDetailProps {
  params: Promise<{
    id: string;
  }>;
}

interface PatientData {
  // Datos básicos del paciente
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  alternativePhone?: string;
  dateOfBirth?: string;
  gender?: string;
  documentType?: string;
  documentNumber?: string;
  address?: string;
  
  // Información geográfica
  location: {
    country?: string;
    department?: string;
    municipality?: string;
  };
  
  // Información laboral y personal
  occupation?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship?: string;
  };
  
  // Estado y metadata
  status: string;
  createdAt: string;
  updatedAt: string;
  isMinor: boolean;
  age?: number;
  
  // Expediente médico
  hasMedicalRecord: boolean;
  medicalRecord?: {
    id: string;
    recordNumber: string;
    status: string;
    openDate: string;
    primaryDoctor?: {
      id: string;
      name: string;
    };
    consultory?: {
      id: string;
      name: string;
      address?: string;
    };
    demographics?: any;
    administrative?: any;
  };
  
  // Guardian
  guardian?: {
    id: string;
    name: string;
    email: string;
    phone: string;
    relationship?: string;
    canMakeDecisions?: boolean;
    validUntil?: string;
  };
  
  // Historial médico detallado
  medicalHistory: {
    allergies: any[];
    pathologicalHistory: any[];
    nonPathologicalHistory: any[];
    familyHistory: any[];
    hospitalizations: any[];
    surgeries: any[];
    vaccinations: any[];
  };
  
  // Actividad reciente
  recentActivity: {
    lastConsultation?: {
      date: string;
      chiefComplaint: string;
    };
    nextAppointment?: {
      date: string;
      time: string;
    };
    totalConsultations: number;
  };
}

export default function PatientDetailPage({ params }: PatientDetailProps) {
  const resolvedParams = use(params);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { user } = useUser();
  
  // Estados principales
  const [patient, setPatient] = useState<PatientData | null>(null);
  const [loading, setLoading] = useState(true);
  const [processing, setProcessing] = useState(false);
  const [creatingRecord, setCreatingRecord] = useState(false);
  const [showGuardianManagement, setShowGuardianManagement] = useState(false);
  const [error, setError] = useState('');
  
  // Estados para modos
  const [isEditMode, setIsEditMode] = useState(searchParams.get('mode') === 'edit');
  
  // Estados para catálogos
  const [documentTypes, setDocumentTypes] = useState<any[]>([]);
  const [relationships, setRelationships] = useState<any[]>([]);
  const [occupations, setOccupations] = useState<any[]>([]);
  
  // Estados para formulario de edición
  const [editFormData, setEditFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    documentType: '',
    documentNumber: '',
    address: '',
    city: '',
    occupation: '',
    emergencyContactName: '',
    emergencyContactPhone: '',
    emergencyContactRelationship: ''
  });

  // Función para obtener datos del paciente
  const fetchPatient = async () => {
    try {
      setLoading(true);
      setError('');
      
      const response = await fetch(`/api/patients/${resolvedParams.id}`);
      if (!response.ok) {
        throw new Error('Error al cargar datos del paciente');
      }
      
      const data = await response.json();
      setPatient(data);
      
      // Inicializar form data si estamos en modo edición
      if (isEditMode) {
        setEditFormData({
          firstName: data.firstName || '',
          lastName: data.lastName || '',
          email: data.email || '',
          phone: data.phone || '',
          dateOfBirth: data.dateOfBirth ? data.dateOfBirth.split('T')[0] : '',
          gender: data.gender || '',
          documentType: data.documentType || '',
          documentNumber: data.documentNumber || '',
          address: data.address || '',
          city: data.city || '',
          occupation: data.occupation || '',
          emergencyContactName: data.emergencyContact?.name || '',
          emergencyContactPhone: data.emergencyContact?.phone || '',
          emergencyContactRelationship: data.emergencyContact?.relationship || ''
        });
      }
      
    } catch (error: any) {
      console.error('Error fetching patient:', error);
      setError(error.message || 'Error al cargar datos del paciente');
      toast.error(error.message || 'Error al cargar datos del paciente');
    } finally {
      setLoading(false);
    }
  };

  // Función para cargar catálogos
  const fetchCatalogs = async () => {
    try {
      // Cargar tipos de documento
      const documentTypesResponse = await fetch('/api/catalogs/document-types');
      if (documentTypesResponse.ok) {
        const documentTypesData = await documentTypesResponse.json();
        setDocumentTypes(documentTypesData.data || []);
      }

      // Cargar relaciones
      const relationshipsResponse = await fetch('/api/catalogs/relationships');
      if (relationshipsResponse.ok) {
        const relationshipsData = await relationshipsResponse.json();
        setRelationships(relationshipsData.data || []);
      }

      // Cargar ocupaciones
      const occupationsResponse = await fetch('/api/catalogs/occupations');
      if (occupationsResponse.ok) {
        const occupationsData = await occupationsResponse.json();
        setOccupations(occupationsData.data || []);
      }
    } catch (error) {
      console.error('Error cargando catálogos:', error);
    }
  };

  useEffect(() => {
    fetchPatient();
    fetchCatalogs();
  }, [resolvedParams.id]);

  // Función para guardar cambios
  const handleSave = async () => {
    try {
      setProcessing(true);
      
      const response = await fetch(`/api/patients/${resolvedParams.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...editFormData,
          emergencyContact: {
            name: editFormData.emergencyContactName,
            phone: editFormData.emergencyContactPhone,
            relationship: editFormData.emergencyContactRelationship
          }
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al guardar cambios');
      }

      const updatedPatient = await response.json();
      setPatient(updatedPatient);
      setIsEditMode(false);
      
      toast.success('Información del paciente actualizada exitosamente');
      
      // Actualizar URL para quitar el modo de edición
      router.replace(`/dashboard/doctor/pacientes/${resolvedParams.id}`);
      
    } catch (error: any) {
      console.error('Error saving patient:', error);
      toast.error(error.message || 'Error al guardar cambios');
    } finally {
      setProcessing(false);
    }
  };

  // Función para cancelar edición
  const handleCancel = () => {
    setIsEditMode(false);
    router.push('/dashboard/doctor/pacientes');
  };

  // Función para crear expediente médico
  const handleCreateMedicalRecord = async () => {
    if (!patient) return;
    
    // Verificar si es menor de edad y no tiene encargados
    if (patient.isMinor && !patient.guardian) {
      // Mostrar formulario de gestión de encargados primero
      setShowGuardianManagement(true);
      return;
    }
    
    // Crear expediente médico directamente
    await createMedicalRecord();
  };

  // Función auxiliar para crear el expediente médico
  const createMedicalRecord = async () => {
    if (!patient) return;
    
    try {
      setCreatingRecord(true);
      
      const response = await fetch(`/api/patients/${resolvedParams.id}/medical-record`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Error al crear expediente médico');
      }

      const result = await response.json();
      
      toast.success(`Expediente médico creado exitosamente: ${result.data.recordNumber}`);
      
      // Recargar datos del paciente para mostrar el nuevo expediente
      await fetchPatient();
      
    } catch (error: any) {
      console.error('Error creating medical record:', error);
      toast.error(error.message || 'Error al crear expediente médico');
    } finally {
      setCreatingRecord(false);
    }
  };

  // Función para manejar cuando se crean los encargados
  const handleGuardiansCreated = async () => {
    setShowGuardianManagement(false);
    // Después de crear encargados, crear el expediente médico
    await createMedicalRecord();
  };

  // Función para entrar en modo edición
  const handleEdit = () => {
    setIsEditMode(true);
    router.replace(`/dashboard/doctor/pacientes/${resolvedParams.id}?mode=edit`);
    
    // Inicializar form data
    if (patient) {
      setEditFormData({
        firstName: patient.firstName || '',
        lastName: patient.lastName || '',
        email: patient.email || '',
        phone: patient.phone || '',
        dateOfBirth: patient.dateOfBirth ? patient.dateOfBirth.split('T')[0] : '',
        gender: patient.gender || '',
        documentType: patient.documentType || '',
        documentNumber: patient.documentNumber || '',
        address: patient.address || '',
        city: patient.city || '',
        occupation: patient.occupation || '',
        emergencyContactName: patient.emergencyContact?.name || '',
        emergencyContactPhone: patient.emergencyContact?.phone || '',
        emergencyContactRelationship: patient.emergencyContact?.relationship || ''
      });
    }
  };

  // Función para actualizar datos del formulario
  const updateFormData = (field: string, value: string) => {
    setEditFormData(prev => ({ ...prev, [field]: value }));
  };

  // Calcular edad del paciente
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Cargando información del paciente...</p>
        </div>
      </div>
    );
  }

  if (error || !patient) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <AlertTriangle className="h-12 w-12 text-red-500 mx-auto mb-4" />
          <h1 className="text-xl font-semibold text-gray-900 mb-2">Error al cargar paciente</h1>
          <p className="text-gray-600 mb-4">{error || 'Paciente no encontrado'}</p>
          <Button onClick={() => router.push('/dashboard/doctor/pacientes')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Volver a Pacientes
          </Button>
        </div>
      </div>
    );
  }

  // Mostrar gestión de encargados si es necesario
  if (showGuardianManagement && patient) {
    return (
      <div className="space-y-6 p-6">
        <GuardianManagement
          patientId={patient.id}
          patientName={`${patient.firstName} ${patient.lastName}`}
          patientAge={patient.age || undefined}
          isMinor={patient.isMinor || false}
          onGuardiansCreated={handleGuardiansCreated}
          onCancel={() => setShowGuardianManagement(false)}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Botón de volver simple */}
      <div className="mb-6">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => router.push('/dashboard/doctor/pacientes')}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Volver a Pacientes
        </Button>
      </div>

      {/* Header del perfil del paciente */}
      <div className="flex flex-col lg:flex-row lg:items-start lg:justify-between gap-6">
        <div className="flex items-center gap-4">
          <Avatar className="h-20 w-20">
            <AvatarFallback className="text-lg bg-blue-100 text-blue-700">
              {patient.firstName?.[0] || 'P'}{patient.lastName?.[0] || 'A'}
            </AvatarFallback>
          </Avatar>
          
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {patient.firstName} {patient.lastName}
            </h1>
            <p className="text-gray-600">
              {isEditMode ? 'Editando perfil del paciente' : 'Perfil del paciente'}
            </p>
            
            <div className="flex flex-wrap gap-2 mt-2">
              <Badge variant={patient.status === 'active' ? 'default' : 'secondary'}>
                {patient.status === 'active' ? 'Activo' : 'Inactivo'}
              </Badge>
              {patient.isMinor && (
                <Badge className="bg-amber-100 text-amber-800">
                  <Baby className="h-3 w-3 mr-1" />
                  Menor de Edad
                </Badge>
              )}
              {patient.hasMedicalRecord && (
                <Badge className="bg-green-100 text-green-800">
                  <FileText className="h-3 w-3 mr-1" />
                  Con Expediente
                </Badge>
              )}
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {!isEditMode ? (
            <Button 
              onClick={handleEdit}
              className="bg-emerald-600 hover:bg-emerald-700 text-white"
            >
              <Edit className="h-4 w-4 mr-2" />
              Editar
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" onClick={handleCancel}>
                Cancelar
              </Button>
              <Button 
                onClick={handleSave} 
                className="bg-emerald-600 hover:bg-emerald-700 text-white"
                disabled={processing}
              >
                <Save className="h-4 w-4 mr-2" />
                {processing ? 'Guardando...' : 'Guardar'}
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Información rápida */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Mail className="h-8 w-8 text-blue-600" />
            <div>
              <p className="text-sm text-gray-600">Email</p>
              <p className="font-medium">{patient.email || 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <Phone className="h-8 w-8 text-green-600" />
            <div>
              <p className="text-sm text-gray-600">Teléfono</p>
              <p className="font-medium">{patient.phone ? formatPhoneForDisplay(patient.phone) : 'No registrado'}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="flex items-center gap-3 pt-6">
            <IdCard className="h-8 w-8 text-purple-600" />
            <div>
              <p className="text-sm text-gray-600">Documento / Edad</p>
              <p className="font-medium">
                {patient.documentNumber 
                  ? `${patient.documentType} ${patient.documentNumber}` 
                  : (patient.dateOfBirth ? `${calculateAge(patient.dateOfBirth)} años` : 'No registrado')
                }
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Layout principal con sidebar condicional */}
      <div className="flex flex-col lg:flex-row gap-6">
        {/* Contenido principal */}
        <div className={cn("flex-1", (patient.guardian || patient.hasMedicalRecord || !patient.hasMedicalRecord) && "lg:w-2/3")}>
          {/* Contenido principal ahora sin expediente médico */}


      {/* Información sin tabs - todas las secciones visibles */}
      <div className="space-y-6">
        {/* Sección 1: Datos Personales */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <User className="h-5 w-5 text-blue-600" />
              Datos Personales
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isEditMode ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="firstName">
                    Nombre(s) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="firstName"
                    value={editFormData.firstName}
                    onChange={(e) => updateFormData('firstName', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="lastName">
                    Apellido(s) <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="lastName"
                    value={editFormData.lastName}
                    onChange={(e) => updateFormData('lastName', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="email">
                    Email <span className="text-red-500">*</span>
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={editFormData.email}
                    onChange={(e) => updateFormData('email', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="documentType">Tipo de Documento</Label>
                  <Select
                    value={editFormData.documentType}
                    onValueChange={(value) => updateFormData('documentType', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona tipo" />
                    </SelectTrigger>
                    <SelectContent>
                      {documentTypes.map((type) => (
                        <SelectItem key={type.id} value={type.name}>
                          {type.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="documentNumber">Número de Documento</Label>
                  <Input
                    id="documentNumber"
                    value={editFormData.documentNumber}
                    onChange={(e) => updateFormData('documentNumber', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="dateOfBirth">Fecha de Nacimiento</Label>
                  <Input
                    id="dateOfBirth"
                    type="date"
                    value={editFormData.dateOfBirth}
                    onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="gender">Género</Label>
                  <Select
                    value={editFormData.gender}
                    onValueChange={(value) => updateFormData('gender', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona género" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="male">Masculino</SelectItem>
                      <SelectItem value="female">Femenino</SelectItem>
                      <SelectItem value="other">Otro</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="occupation">Ocupación</Label>
                  <Select
                    value={editFormData.occupation}
                    onValueChange={(value) => updateFormData('occupation', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona ocupación" />
                    </SelectTrigger>
                    <SelectContent>
                      {occupations.map((occupation) => (
                        <SelectItem key={occupation.id} value={occupation.name}>
                          {occupation.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Dirección</Label>
                  <Input
                    id="address"
                    value={editFormData.address}
                    onChange={(e) => updateFormData('address', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="city">Ciudad</Label>
                  <Input
                    id="city"
                    value={editFormData.city}
                    onChange={(e) => updateFormData('city', e.target.value)}
                  />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nombre Completo</Label>
                  <p className="text-sm text-gray-900">{patient.firstName} {patient.lastName}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Email</Label>
                  <p className="text-sm text-gray-900">{patient.email || 'No registrado'}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Documento</Label>
                  <p className="text-sm text-gray-900">
                    {patient.documentType && patient.documentNumber 
                      ? `${patient.documentType} - ${patient.documentNumber}`
                      : 'No registrado'
                    }
                  </p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Fecha de Nacimiento</Label>
                  <p className="text-sm text-gray-900">
                    {patient.dateOfBirth 
                      ? formatDate(patient.dateOfBirth)
                      : 'No registrada'
                    }
                  </p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Edad</Label>
                  <p className="text-sm text-gray-900">
                    {patient.dateOfBirth ? `${calculateAge(patient.dateOfBirth)} años` : 'No calculable'}
                  </p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Género</Label>
                  <p className="text-sm text-gray-900">{formatGender(patient.gender)}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Ocupación</Label>
                  <p className="text-sm text-gray-900">{patient.occupation || 'No especificada'}</p>
                </div>
                
                <div className="md:col-span-2">
                  <Label className="text-sm font-medium text-gray-700">Dirección</Label>
                  <p className="text-sm text-gray-900">{patient.address || 'No registrada'}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Ciudad</Label>
                  <p className="text-sm text-gray-900">{patient.city || 'No registrada'}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sección 2: Información de Contacto */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Phone className="h-5 w-5 text-blue-600" />
              Información de Contacto
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isEditMode ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="phone">Teléfono</Label>
                  <Input
                    id="phone"
                    value={editFormData.phone}
                    onChange={(e) => updateFormData('phone', e.target.value)}
                  />
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                  <p className="text-sm text-gray-900">{patient.phone ? formatPhoneForDisplay(patient.phone) : 'No registrado'}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sección 3: Contacto de Emergencia */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Contacto de Emergencia
            </CardTitle>
          </CardHeader>
          <CardContent>
            {isEditMode ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="emergencyContactName">Nombre del Contacto</Label>
                  <Input
                    id="emergencyContactName"
                    value={editFormData.emergencyContactName}
                    onChange={(e) => updateFormData('emergencyContactName', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="emergencyContactPhone">Teléfono de Contacto</Label>
                  <Input
                    id="emergencyContactPhone"
                    value={editFormData.emergencyContactPhone}
                    onChange={(e) => updateFormData('emergencyContactPhone', e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="emergencyContactRelationship">Relación</Label>
                  <Select
                    value={editFormData.emergencyContactRelationship}
                    onValueChange={(value) => updateFormData('emergencyContactRelationship', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecciona relación" />
                    </SelectTrigger>
                    <SelectContent>
                      {relationships.map((relationship) => (
                        <SelectItem key={relationship.id} value={relationship.name}>
                          {relationship.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <Label className="text-sm font-medium text-gray-700">Nombre</Label>
                  <p className="text-sm text-gray-900">{patient.emergencyContact?.name || 'No registrado'}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Teléfono</Label>
                  <p className="text-sm text-gray-900">{patient.emergencyContact?.phone || 'No registrado'}</p>
                </div>
                
                <div>
                  <Label className="text-sm font-medium text-gray-700">Relación</Label>
                  <p className="text-sm text-gray-900">{patient.emergencyContact?.relationship || 'No especificada'}</p>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Sección 4: Información Médica */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Heart className="h-5 w-5 text-blue-600" />
              Información Médica
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div>
                <Label className="text-sm font-medium text-gray-700">Alergias</Label>
                <div className="mt-2">
                  {patient.medicalInfo?.allergies?.length ? (
                    <div className="space-y-2">
                      {patient.medicalInfo.allergies.map((allergy: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg border border-red-200">
                          <div className="flex items-center gap-3">
                            <AlertTriangle className="h-5 w-5 text-red-600" />
                            <div>
                              <p className="font-medium text-red-900">{allergy.allergen || allergy}</p>
                              {allergy.severity && (
                                <p className="text-sm text-red-700">Severidad: {allergy.severity}</p>
                              )}
                            </div>
                          </div>
                          {allergy.type && (
                            <Badge variant="outline" className="bg-red-100 text-red-800">
                              {allergy.type}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No se han registrado alergias</p>
                  )}
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Condiciones Crónicas</Label>
                <div className="mt-2">
                  {patient.medicalInfo?.conditions?.length ? (
                    <div className="space-y-2">
                      {patient.medicalInfo.conditions.map((condition: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                          <div className="flex items-center gap-3">
                            <Heart className="h-5 w-5 text-orange-600" />
                            <div>
                              <p className="font-medium text-orange-900">{condition.name || condition}</p>
                              {condition.diagnosisDate && (
                                <p className="text-sm text-orange-700">
                                  Diagnosticado: {formatDate(condition.diagnosisDate)}
                                </p>
                              )}
                            </div>
                          </div>
                          {condition.status && (
                            <Badge variant="outline" className="bg-orange-100 text-orange-800">
                              {condition.status}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No se han registrado condiciones crónicas</p>
                  )}
                </div>
              </div>
              
              <div>
                <Label className="text-sm font-medium text-gray-700">Medicamentos Actuales</Label>
                <div className="mt-2">
                  {patient.medicalInfo?.medications?.length ? (
                    <div className="space-y-2">
                      {patient.medicalInfo.medications.map((medication: any, index: number) => (
                        <div key={index} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                          <div className="flex items-center gap-3">
                            <Pill className="h-5 w-5 text-blue-600" />
                            <div>
                              <p className="font-medium text-blue-900">{medication.name || medication}</p>
                              {medication.dosage && (
                                <p className="text-sm text-blue-700">Dosis: {medication.dosage}</p>
                              )}
                            </div>
                          </div>
                          {medication.frequency && (
                            <Badge variant="outline" className="bg-blue-100 text-blue-800">
                              {medication.frequency}
                            </Badge>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-sm text-gray-500">No se han registrado medicamentos</p>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sección 5: Actividad Reciente */}
        <Card className="hover:shadow-lg transition-all duration-300 border-0 shadow-sm">
          <CardHeader>
            <CardTitle className="text-lg flex items-center gap-2">
              <Activity className="h-5 w-5 text-blue-600" />
              Actividad Reciente
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Estadísticas de consultas */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="bg-blue-50 border-blue-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-blue-600">Total Consultas</p>
                        <p className="text-2xl font-bold text-blue-900">
                          {patient.recentActivity?.totalConsultations || 0}
                        </p>
                      </div>
                      <Stethoscope className="h-8 w-8 text-blue-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-green-50 border-green-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-green-600">Última Consulta</p>
                        <p className="text-sm font-bold text-green-900">
                          {patient.recentActivity?.lastConsultation 
                            ? formatDate(patient.recentActivity.lastConsultation)
                            : 'Sin consultas'}
                        </p>
                      </div>
                      <Calendar className="h-8 w-8 text-green-500" />
                    </div>
                  </CardContent>
                </Card>
                
                <Card className="bg-purple-50 border-purple-200">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-purple-600">Próxima Cita</p>
                        <p className="text-sm font-bold text-purple-900">
                          {patient.recentActivity?.nextAppointment 
                            ? formatDate(patient.recentActivity.nextAppointment.date)
                            : 'Sin citas'}
                        </p>
                        {patient.recentActivity?.nextAppointment?.time && (
                          <p className="text-xs text-purple-700">
                            {patient.recentActivity.nextAppointment.time}
                          </p>
                        )}
                      </div>
                      <Clock className="h-8 w-8 text-purple-500" />
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              {/* Timeline de actividad */}
              <div className="space-y-4">
                <h4 className="font-medium text-gray-700">Historial de Actividad</h4>
                
                {patient.recentActivity?.nextAppointment && (
                  <div className="border-l-4 border-purple-500 pl-4">
                    <p className="text-sm font-medium text-gray-900">Próxima cita programada</p>
                    <p className="text-xs text-gray-500">
                      {formatDate(patient.recentActivity.nextAppointment.date)}
                      {patient.recentActivity.nextAppointment.time && ` a las ${patient.recentActivity.nextAppointment.time}`}
                    </p>
                  </div>
                )}
                
                {patient.recentActivity?.lastConsultation && (
                  <div className="border-l-4 border-green-500 pl-4">
                    <p className="text-sm font-medium text-gray-900">Última consulta médica</p>
                    <p className="text-xs text-gray-500">
                      {formatDate(patient.recentActivity.lastConsultation, 'dd/MM/yyyy HH:mm')}
                    </p>
                  </div>
                )}
                
                {patient.updatedAt && (
                  <div className="border-l-4 border-yellow-500 pl-4">
                    <p className="text-sm font-medium text-gray-900">Información actualizada</p>
                    <p className="text-xs text-gray-500">
                      {formatDate(patient.updatedAt, 'dd/MM/yyyy HH:mm')}
                    </p>
                  </div>
                )}
                
                <div className="border-l-4 border-blue-500 pl-4">
                  <p className="text-sm font-medium text-gray-900">Paciente registrado</p>
                  <p className="text-xs text-gray-500">
                    {formatDate(patient.createdAt, 'dd/MM/yyyy HH:mm')}
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

        </div>

        {/* Sidebar - Cards informativos */}
        <div className="lg:w-80 space-y-6">
          {/* Card del Expediente Médico */}
          {patient.hasMedicalRecord && patient.medicalRecord ? (
            <Card className="bg-blue-50 border-blue-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  Expediente Médico
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Número de Expediente</p>
                    <p className="font-medium text-blue-900">{patient.medicalRecord.recordNumber}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">Médico de Cabecera</p>
                    <p className="font-medium text-blue-900">{patient.medicalRecord.primaryDoctor?.name || 'No asignado'}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">Consultorio</p>
                    <p className="font-medium text-blue-900">{patient.medicalRecord.consultory?.name || 'No asignado'}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">Estado</p>
                    <Badge className={
                      patient.medicalRecord.status === 'active' 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-gray-100 text-gray-800'
                    }>
                      {patient.medicalRecord.status === 'active' ? 'Activo' : 'Inactivo'}
                    </Badge>
                  </div>
                  
                  <div className="pt-3 border-t border-blue-200">
                    <Button 
                      size="sm"
                      className="w-full"
                      onClick={() => router.push(`/dashboard/doctor/expedientes/${patient.medicalRecord.id}`)}
                    >
                      <FileText className="h-4 w-4 mr-2" />
                      Ver Completo
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ) : (
            /* Sin expediente médico */
            <Card className="bg-orange-50 border-orange-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <FileText className="h-5 w-5 text-orange-600" />
                  Expediente Médico
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="text-center py-4">
                    <FileText className="h-12 w-12 text-orange-400 mx-auto mb-3" />
                    <p className="text-sm font-medium text-orange-900 mb-2">
                      Sin Expediente Médico
                    </p>
                    <p className="text-xs text-orange-700 mb-4">
                      Este paciente aún no tiene expediente médico creado.
                    </p>
                  </div>
                  
                  <div className="pt-3 border-t border-orange-200">
                    <Button 
                      size="sm"
                      className="w-full bg-orange-600 hover:bg-orange-700"
                      onClick={handleCreateMedicalRecord}
                      disabled={creatingRecord}
                    >
                      {creatingRecord ? (
                        <>
                          <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                          Creando...
                        </>
                      ) : (
                        <>
                          <FileText className="h-4 w-4 mr-2" />
                          Crear Expediente
                        </>
                      )}
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Card del Guardián Legal (solo si existe) */}
          {patient.guardian && (
            <Card className="bg-amber-50 border-amber-200">
              <CardHeader>
                <CardTitle className="text-lg flex items-center gap-2">
                  <Users className="h-5 w-5 text-amber-600" />
                  Guardián Legal
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div>
                    <p className="text-sm text-gray-600">Nombre</p>
                    <p className="font-medium text-gray-900">{patient.guardian.name}</p>
                  </div>
                  
                  {patient.guardian.relationship && (
                    <div>
                      <p className="text-sm text-gray-600">Relación</p>
                      <p className="font-medium text-gray-900">{formatRelationship(patient.guardian.relationship)}</p>
                    </div>
                  )}
                  
                  <div>
                    <p className="text-sm text-gray-600">Teléfono</p>
                    <p className="font-medium text-gray-900">{formatPhoneForDisplay(patient.guardian.phone)}</p>
                  </div>
                  
                  <div>
                    <p className="text-sm text-gray-600">Email</p>
                    <p className="font-medium text-gray-900">{patient.guardian.email}</p>
                  </div>
                  
                  {patient.guardian.canMakeDecisions !== undefined && (
                    <div className="pt-3 border-t border-amber-200">
                      <Badge variant={patient.guardian.canMakeDecisions ? "default" : "secondary"} className="w-full justify-center">
                        {patient.guardian.canMakeDecisions ? 
                          "✓ Puede tomar decisiones médicas" : 
                          "✗ No puede tomar decisiones médicas"}
                      </Badge>
                    </div>
                  )}
                  
                  {patient.guardian.validUntil && (
                    <div>
                      <p className="text-sm text-gray-600">Válido hasta</p>
                      <p className="font-medium text-gray-900">{formatDate(patient.guardian.validUntil)}</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}