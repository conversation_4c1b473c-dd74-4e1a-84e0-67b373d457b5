import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { doctorServicePrices } from '@/db/schema';
import { eq, and } from 'drizzle-orm';

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { serviceId, price, currency, isActive, notes } = body;
    const servicePriceId = params.id;

    // Validaciones
    if (!serviceId || !price) {
      return NextResponse.json(
        { error: 'El servicio y precio son requeridos' },
        { status: 400 }
      );
    }

    if (isNaN(parseFloat(price)) || parseFloat(price) <= 0) {
      return NextResponse.json(
        { error: 'El precio debe ser un número mayor a 0' },
        { status: 400 }
      );
    }

    // Verificar que el precio pertenece al doctor actual
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(
        and(
          eq(doctorServicePrices.id, servicePriceId),
          eq(doctorServicePrices.doctorId, userId)
        )
      )
      .limit(1);

    if (existingPrice.length === 0) {
      return NextResponse.json(
        { error: 'Precio de servicio no encontrado' },
        { status: 404 }
      );
    }

    // Actualizar el precio del servicio
    const updatedServicePrice = await db
      .update(doctorServicePrices)
      .set({
        serviceId,
        customPrice: parseFloat(price),
        currency: currency || 'GTQ',
        isActive: isActive ?? true,
        notes: notes?.trim() || null,
        updatedAt: new Date()
      })
      .where(eq(doctorServicePrices.id, servicePriceId))
      .returning();

    return NextResponse.json({
      success: true,
      message: 'Precio de servicio actualizado exitosamente',
      data: updatedServicePrice[0]
    });
  } catch (error) {
    console.error('Error al actualizar precio de servicio:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json(
        { error: 'No autorizado' },
        { status: 401 }
      );
    }

    const servicePriceId = params.id;

    // Verificar que el precio pertenece al doctor actual
    const existingPrice = await db
      .select()
      .from(doctorServicePrices)
      .where(
        and(
          eq(doctorServicePrices.id, servicePriceId),
          eq(doctorServicePrices.doctorId, userId)
        )
      )
      .limit(1);

    if (existingPrice.length === 0) {
      return NextResponse.json(
        { error: 'Precio de servicio no encontrado' },
        { status: 404 }
      );
    }

    // Eliminar el precio del servicio
    await db
      .delete(doctorServicePrices)
      .where(eq(doctorServicePrices.id, servicePriceId));

    return NextResponse.json({
      success: true,
      message: 'Precio de servicio eliminado exitosamente'
    });
  } catch (error) {
    console.error('Error al eliminar precio de servicio:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}