/**
 * Script completo para estandarizar TODOS los menús contextuales
 * Asegura que todos tengan el mismo patrón con separador
 */

import fs from 'fs';
import path from 'path';

// Función para buscar archivos recursivamente
function findFilesRecursively(dir: string, pattern: RegExp): string[] {
  const files: string[] = [];
  
  function walk(currentDir: string) {
    try {
      const entries = fs.readdirSync(currentDir, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(currentDir, entry.name);
        
        if (entry.isDirectory() && !entry.name.includes('node_modules') && !entry.name.includes('.next')) {
          walk(fullPath);
        } else if (entry.isFile() && pattern.test(entry.name)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Ignorar errores de permisos
    }
  }
  
  walk(dir);
  return files;
}

// Buscar archivos que probablemente tengan menús contextuales
function findPotentialDropdownFiles(): string[] {
  const patterns = [
    // Catálogos
    'app/(dashboard)/dashboard/admin/catalogs/**/*.tsx',
    // Usuarios
    'app/(dashboard)/dashboard/admin/users/**/*.tsx',
    // Pacientes
    'app/(dashboard)/dashboard/admin/patients/**/*.tsx',
    'app/(dashboard)/dashboard/doctor/pacientes/**/*.tsx',
    // Expedientes
    'app/(dashboard)/dashboard/doctor/expedientes/**/*.tsx',
    // Agenda
    'app/(dashboard)/dashboard/admin/agenda/**/*.tsx',
    'app/(dashboard)/dashboard/doctor/agenda/**/*.tsx',
  ];
  
  const allFiles: string[] = [];
  
  // Buscar en app directory
  const appFiles = findFilesRecursively('app', /page\.tsx$/);
  
  // Filtrar archivos relevantes
  const relevantFiles = appFiles.filter(file => {
    const normalized = file.replace(/\\/g, '/');
    return normalized.includes('catalogs') || 
           normalized.includes('users') || 
           normalized.includes('patients') || 
           normalized.includes('pacientes') ||
           normalized.includes('expedientes') ||
           normalized.includes('agenda');
  });
  
  return relevantFiles;
}

interface MenuAnalysis {
  file: string;
  hasDropdownMenu: boolean;
  hasLabel: boolean;
  hasSeparator: boolean;
  separatorPosition: 'correct' | 'incorrect' | 'missing';
  menuStructure: string;
}

// Analizar estructura del menú
function analyzeMenuStructure(content: string, file: string): MenuAnalysis[] {
  const results: MenuAnalysis[] = [];
  
  // Buscar todos los DropdownMenuContent
  const menuRegex = /<DropdownMenuContent[^>]*>[\s\S]*?<\/DropdownMenuContent>/g;
  const menus = content.match(menuRegex) || [];
  
  for (const menu of menus) {
    const hasLabel = menu.includes('DropdownMenuLabel');
    const hasSeparator = menu.includes('DropdownMenuSeparator');
    
    // Verificar posición del separador
    let separatorPosition: 'correct' | 'incorrect' | 'missing' = 'missing';
    
    if (hasSeparator) {
      // El separador debe estar después de Editar y antes de Desactivar/Toggle
      const editarIndex = menu.indexOf('Editar');
      const separatorIndex = menu.indexOf('DropdownMenuSeparator');
      const toggleIndex = Math.max(
        menu.indexOf('Desactivar'),
        menu.indexOf('Activar'),
        menu.indexOf('toggle')
      );
      
      if (editarIndex > -1 && separatorIndex > editarIndex && 
          (toggleIndex === -1 || separatorIndex < toggleIndex)) {
        separatorPosition = 'correct';
      } else {
        separatorPosition = 'incorrect';
      }
    }
    
    results.push({
      file,
      hasDropdownMenu: true,
      hasLabel,
      hasSeparator,
      separatorPosition,
      menuStructure: menu.substring(0, 100) + '...'
    });
  }
  
  return results;
}

// Estandarizar un archivo
function standardizeFile(filePath: string): boolean {
  try {
    let content = fs.readFileSync(filePath, 'utf-8');
    let updated = false;
    
    // 1. Asegurar importaciones
    if (content.includes('DropdownMenu') && !content.includes('DropdownMenuSeparator')) {
      // Buscar el bloque de importación de dropdown-menu
      const importRegex = /from\s+['"]@\/components\/ui\/dropdown-menu['"]/;
      const importMatch = content.match(importRegex);
      
      if (importMatch) {
        // Agregar DropdownMenuSeparator a las importaciones
        content = content.replace(
          /DropdownMenuLabel,?\s*\n\s*DropdownMenuTrigger/,
          'DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger'
        );
        
        // Si no tiene Label, agregarlo también
        if (!content.includes('DropdownMenuLabel')) {
          content = content.replace(
            /DropdownMenuItem,\s*\n\s*DropdownMenuTrigger/,
            'DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger'
          );
        }
        updated = true;
      }
    }
    
    // 2. Procesar cada menú contextual
    const menuRegex = /<DropdownMenuContent[^>]*>([\s\S]*?)<\/DropdownMenuContent>/g;
    
    content = content.replace(menuRegex, (match, menuContent) => {
      let newContent = menuContent;
      let menuUpdated = false;
      
      // Agregar label si no existe
      if (!menuContent.includes('DropdownMenuLabel')) {
        newContent = '\n                          <DropdownMenuLabel>Acciones</DropdownMenuLabel>' + newContent;
        menuUpdated = true;
      }
      
      // Verificar si necesita separador
      const hasEditAction = menuContent.includes('Editar');
      const hasToggleAction = menuContent.includes('Desactivar') || 
                             menuContent.includes('Activar') || 
                             menuContent.includes('toggle');
      const hasDeleteAction = menuContent.includes('Eliminar');
      const hasSeparator = menuContent.includes('DropdownMenuSeparator');
      
      if (hasEditAction && (hasToggleAction || hasDeleteAction) && !hasSeparator) {
        // Insertar separador después de Editar
        const editPattern = /(<\/DropdownMenuItem>\s*)(<DropdownMenuItem[^>]*>[\s\S]*?(?:Desactivar|Activar|toggle))/;
        newContent = newContent.replace(editPattern, '$1\n                          <DropdownMenuSeparator />$2');
        menuUpdated = true;
      }
      
      // Actualizar clases de botones de eliminar
      if (hasDeleteAction) {
        newContent = newContent.replace(
          /className="text-red-600"/g,
          'className="text-red-600 focus:text-red-600"'
        );
        if (newContent !== menuContent) menuUpdated = true;
      }
      
      // Actualizar clases de toggle
      if (hasToggleAction) {
        // Buscar patrones de toggle y actualizar clases
        newContent = newContent.replace(
          /<DropdownMenuItem\s+onClick=\{[^}]*toggle[^}]*\}[^>]*>/g,
          (toggleMatch) => {
            if (!toggleMatch.includes('className')) {
              return toggleMatch.replace('>', ' className={item.isActive ? "text-orange-600" : "text-green-600"}>');
            }
            return toggleMatch;
          }
        );
      }
      
      if (menuUpdated) {
        updated = true;
        return `<DropdownMenuContent${match.match(/[^>]*>/)[0]}${newContent}</DropdownMenuContent>`;
      }
      
      return match;
    });
    
    if (updated) {
      fs.writeFileSync(filePath, content, 'utf-8');
      return true;
    }
    
    return false;
  } catch (error) {
    console.error(`Error procesando ${filePath}:`, error);
    return false;
  }
}

// Main
async function main() {
  console.log('🔍 Buscando archivos con menús contextuales...\n');
  
  const files = findPotentialDropdownFiles();
  console.log(`📄 Encontrados ${files.length} archivos potenciales\n`);
  
  // Primero analizar
  console.log('📊 Analizando estructura de menús...\n');
  const needsFix: string[] = [];
  
  for (const file of files) {
    const content = fs.readFileSync(file, 'utf-8');
    if (content.includes('DropdownMenuContent')) {
      const analyses = analyzeMenuStructure(content, file);
      
      for (const analysis of analyses) {
        if (!analysis.hasLabel || !analysis.hasSeparator || analysis.separatorPosition !== 'correct') {
          needsFix.push(file);
          console.log(`⚠️  ${file.replace(/\\/g, '/')}`);
          console.log(`   - Label: ${analysis.hasLabel ? '✓' : '✗'}`);
          console.log(`   - Separador: ${analysis.hasSeparator ? '✓' : '✗'}`);
          console.log(`   - Posición: ${analysis.separatorPosition}`);
          break;
        }
      }
    }
  }
  
  if (needsFix.length === 0) {
    console.log('✅ Todos los menús ya están estandarizados!');
    return;
  }
  
  console.log(`\n🔧 Archivos que necesitan corrección: ${needsFix.length}\n`);
  
  // Aplicar correcciones
  console.log('🛠️  Aplicando correcciones...\n');
  let fixedCount = 0;
  
  for (const file of needsFix) {
    console.log(`Procesando: ${file.replace(/\\/g, '/')}`);
    if (standardizeFile(file)) {
      console.log(`✅ Corregido`);
      fixedCount++;
    } else {
      console.log(`⏭️  Sin cambios necesarios`);
    }
  }
  
  console.log('\n📊 Resumen final:');
  console.log(`✅ Archivos corregidos: ${fixedCount}`);
  console.log(`📁 Total procesados: ${needsFix.length}`);
  
  // Mostrar el patrón estándar
  console.log('\n📐 PATRÓN ESTÁNDAR:');
  console.log(`
<DropdownMenuContent align="end">
  <DropdownMenuLabel>Acciones</DropdownMenuLabel>
  <DropdownMenuItem onClick={() => handleView(item)}>
    <Eye className="h-4 w-4 mr-2" />
    Ver detalles
  </DropdownMenuItem>
  <DropdownMenuItem onClick={() => handleEdit(item)}>
    <Edit className="h-4 w-4 mr-2" />
    Editar
  </DropdownMenuItem>
  <DropdownMenuSeparator />
  <DropdownMenuItem 
    onClick={() => handleToggleStatus(item)}
    className={item.isActive ? "text-orange-600" : "text-green-600"}
  >
    <ToggleLeft className="h-4 w-4 mr-2" />
    {item.isActive ? 'Desactivar' : 'Activar'}
  </DropdownMenuItem>
  <DropdownMenuItem 
    onClick={() => handleDelete(item)}
    className="text-red-600 focus:text-red-600"
  >
    <Trash2 className="h-4 w-4 mr-2" />
    Eliminar
  </DropdownMenuItem>
</DropdownMenuContent>
  `);
}

main().catch(console.error);