'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Heart,
  Thermometer,
  Scale,
  Ruler,
  Droplet,
  AlertTriangle,
  Pill,
  Plus,
  X,
  Save,
  RefreshCw
} from 'lucide-react';
import { toast } from 'sonner';

interface MedicalRecordFormProps {
  patientId: string;
  existingRecord?: any;
  preCheckinData?: {
    symptoms?: string;
    medications?: string;
    allergies?: string;
    contactInfo?: any;
  };
  onSubmit: (data: any) => Promise<void>;
  onCancel?: () => void;
  loading?: boolean;
}

interface AllergyItem {
  substance: string;
  reaction: string;
  severity: 'mild' | 'moderate' | 'severe';
  confirmedDate: string;
}

interface MedicationItem {
  name: string;
  dosage: string;
  frequency: string;
  startDate: string;
  prescribedBy?: string;
  isActive: boolean;
}

interface VitalSigns {
  temperature: string;
  bloodPressure: string;
  heartRate: string;
  respiratoryRate: string;
  oxygenSaturation: string;
  weight: string;
  height: string;
}

export function MedicalRecordForm({
  patientId,
  existingRecord,
  preCheckinData,
  onSubmit,
  onCancel,
  loading = false
}: MedicalRecordFormProps) {
  const [formData, setFormData] = useState({
    // Datos demográficos médicos
    bloodType: existingRecord?.demographics?.bloodType || '',
    weight: existingRecord?.demographics?.weight || '',
    height: existingRecord?.demographics?.height || '',
    
    // Signos vitales actuales
    vitalSigns: {
      temperature: '',
      bloodPressure: '',
      heartRate: '',
      respiratoryRate: '',
      oxygenSaturation: '',
      weight: '',
      height: ''
    } as VitalSigns,
    
    // Alergias estructuradas
    allergies: existingRecord?.allergies || [] as AllergyItem[],
    
    // Medicamentos actuales
    currentMedications: existingRecord?.currentMedications || [] as MedicationItem[],
    
    // Contactos médicos de emergencia
    medicalEmergencyContact: existingRecord?.medicalEmergencyContact || '',
    medicalEmergencyPhone: existingRecord?.medicalEmergencyPhone || '',
    
    // Notas del médico
    doctorNotes: existingRecord?.doctorNotes || ''
  });

  const [newAllergy, setNewAllergy] = useState<Partial<AllergyItem>>({
    substance: '',
    reaction: '',
    severity: 'mild',
    confirmedDate: new Date().toISOString().split('T')[0]
  });

  const [newMedication, setNewMedication] = useState<Partial<MedicationItem>>({
    name: '',
    dosage: '',
    frequency: '',
    startDate: new Date().toISOString().split('T')[0],
    isActive: true
  });

  const [showAddAllergy, setShowAddAllergy] = useState(false);
  const [showAddMedication, setShowAddMedication] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Auto-estructurar información del pre-checkin al cargar
  useEffect(() => {
    if (preCheckinData && !existingRecord) {
      // Parsear alergias del pre-checkin
      if (preCheckinData.allergies) {
        const parsedAllergies = parsePreCheckinAllergies(preCheckinData.allergies);
        setFormData(prev => ({ ...prev, allergies: parsedAllergies }));
      }

      // Parsear medicamentos del pre-checkin
      if (preCheckinData.medications) {
        const parsedMedications = parsePreCheckinMedications(preCheckinData.medications);
        setFormData(prev => ({ ...prev, currentMedications: parsedMedications }));
      }
    }
  }, [preCheckinData, existingRecord]);

  const parsePreCheckinAllergies = (allergiesText: string): AllergyItem[] => {
    // Lógica simple para parsear texto libre de alergias
    // En Fase 2 esto sería reemplazado por IA
    const allergies: AllergyItem[] = [];
    
    // Dividir por comas o puntos y comas
    const items = allergiesText.split(/[,;]/).map(item => item.trim());
    
    items.forEach(item => {
      if (item.toLowerCase().includes('penicilina')) {
        allergies.push({
          substance: 'Penicilina',
          reaction: item.toLowerCase().includes('sarpullido') ? 'Sarpullido' : 'Reacción alérgica',
          severity: 'moderate',
          confirmedDate: new Date().toISOString().split('T')[0]
        });
      } else if (item) {
        allergies.push({
          substance: item,
          reaction: 'Reacción alérgica',
          severity: 'mild',
          confirmedDate: new Date().toISOString().split('T')[0]
        });
      }
    });

    return allergies;
  };

  const parsePreCheckinMedications = (medicationsText: string): MedicationItem[] => {
    // Lógica simple para parsear medicamentos
    const medications: MedicationItem[] = [];
    
    // Dividir por comas o saltos de línea
    const items = medicationsText.split(/[,\n]/).map(item => item.trim());
    
    items.forEach(item => {
      if (item) {
        // Extraer nombre y dosis básica
        const parts = item.split(/\s+/);
        const name = parts[0] || item;
        const dosage = parts.find(part => /\d+mg|\d+ml/.test(part)) || '';
        const frequency = item.toLowerCase().includes('cada') ? 
          item.match(/cada\s+\d+\s*h/)?.[0] || 'Según indicación' : 
          'Según indicación';

        medications.push({
          name: name,
          dosage: dosage,
          frequency: frequency,
          startDate: new Date().toISOString().split('T')[0],
          isActive: true
        });
      }
    });

    return medications;
  };

  const addAllergy = () => {
    if (newAllergy.substance && newAllergy.reaction) {
      setFormData(prev => ({
        ...prev,
        allergies: [...prev.allergies, newAllergy as AllergyItem]
      }));
      setNewAllergy({
        substance: '',
        reaction: '',
        severity: 'mild',
        confirmedDate: new Date().toISOString().split('T')[0]
      });
      setShowAddAllergy(false);
    }
  };

  const removeAllergy = (index: number) => {
    setFormData(prev => ({
      ...prev,
      allergies: prev.allergies.filter((_, i) => i !== index)
    }));
  };

  const addMedication = () => {
    if (newMedication.name && newMedication.dosage) {
      setFormData(prev => ({
        ...prev,
        currentMedications: [...prev.currentMedications, newMedication as MedicationItem]
      }));
      setNewMedication({
        name: '',
        dosage: '',
        frequency: '',
        startDate: new Date().toISOString().split('T')[0],
        isActive: true
      });
      setShowAddMedication(false);
    }
  };

  const removeMedication = (index: number) => {
    setFormData(prev => ({
      ...prev,
      currentMedications: prev.currentMedications.filter((_, i) => i !== index)
    }));
  };

  const calculateBMI = () => {
    const weight = parseFloat(formData.vitalSigns.weight);
    const height = parseFloat(formData.vitalSigns.height);
    
    if (weight && height) {
      const heightInMeters = height / 100;
      const bmi = weight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return '';
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const medicalRecordData = {
        patientId,
        demographics: {
          bloodType: formData.bloodType,
          weight: formData.vitalSigns.weight || formData.weight,
          height: formData.vitalSigns.height || formData.height,
          bmi: calculateBMI()
        },
        vitalSigns: formData.vitalSigns,
        allergies: formData.allergies,
        currentMedications: formData.currentMedications,
        medicalEmergencyContact: formData.medicalEmergencyContact,
        medicalEmergencyPhone: formData.medicalEmergencyPhone,
        doctorNotes: formData.doctorNotes,
        lastUpdated: new Date().toISOString()
      };

      await onSubmit(medicalRecordData);
    } catch (error) {
      console.error('Error submitting medical record:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Signos Vitales */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-5 w-5 text-red-600" />
            Signos Vitales Actuales
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Thermometer className="h-4 w-4" />
                Temperatura (°C)
              </Label>
              <Input
                type="number"
                step="0.1"
                value={formData.vitalSigns.temperature}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, temperature: e.target.value }
                }))}
                placeholder="37.0"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Presión Arterial</Label>
              <Input
                value={formData.vitalSigns.bloodPressure}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, bloodPressure: e.target.value }
                }))}
                placeholder="120/80"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Freq. Cardíaca (bpm)</Label>
              <Input
                type="number"
                value={formData.vitalSigns.heartRate}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, heartRate: e.target.value }
                }))}
                placeholder="80"
              />
            </div>
            
            <div className="space-y-2">
              <Label>Saturación O2 (%)</Label>
              <Input
                type="number"
                value={formData.vitalSigns.oxygenSaturation}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, oxygenSaturation: e.target.value }
                }))}
                placeholder="98"
              />
            </div>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Scale className="h-4 w-4" />
                Peso (kg)
              </Label>
              <Input
                type="number"
                step="0.1"
                value={formData.vitalSigns.weight}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, weight: e.target.value }
                }))}
                placeholder="70.0"
              />
            </div>
            
            <div className="space-y-2">
              <Label className="flex items-center gap-1">
                <Ruler className="h-4 w-4" />
                Altura (cm)
              </Label>
              <Input
                type="number"
                value={formData.vitalSigns.height}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  vitalSigns: { ...prev.vitalSigns, height: e.target.value }
                }))}
                placeholder="170"
              />
            </div>
            
            <div className="space-y-2">
              <Label>IMC</Label>
              <Input
                value={calculateBMI()}
                readOnly
                className="bg-gray-50"
                placeholder="Calculado automáticamente"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alergias */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              Alergias
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddAllergy(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Agregar Alergia
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.allergies.length === 0 ? (
            <p className="text-gray-500 text-center py-4">
              No hay alergias registradas
            </p>
          ) : (
            <div className="space-y-2">
              {formData.allergies.map((allergy, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-red-50 border border-red-200 rounded">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{allergy.substance}</span>
                      <Badge variant={
                        allergy.severity === 'severe' ? 'destructive' :
                        allergy.severity === 'moderate' ? 'default' : 'secondary'
                      }>
                        {allergy.severity === 'severe' ? 'Severa' :
                         allergy.severity === 'moderate' ? 'Moderada' : 'Leve'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      Reacción: {allergy.reaction} • Confirmada: {new Date(allergy.confirmedDate).toLocaleDateString()}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeAllergy(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Formulario para agregar alergia */}
          {showAddAllergy && (
            <Card>
              <CardContent className="pt-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Sustancia/Medicamento</Label>
                    <Input
                      value={newAllergy.substance}
                      onChange={(e) => setNewAllergy(prev => ({ ...prev, substance: e.target.value }))}
                      placeholder="Ej: Penicilina"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Reacción</Label>
                    <Input
                      value={newAllergy.reaction}
                      onChange={(e) => setNewAllergy(prev => ({ ...prev, reaction: e.target.value }))}
                      placeholder="Ej: Sarpullido"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Severidad</Label>
                    <Select
                      value={newAllergy.severity}
                      onValueChange={(value: 'mild' | 'moderate' | 'severe') => 
                        setNewAllergy(prev => ({ ...prev, severity: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="mild">Leve</SelectItem>
                        <SelectItem value="moderate">Moderada</SelectItem>
                        <SelectItem value="severe">Severa</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Fecha de Confirmación</Label>
                    <Input
                      type="date"
                      value={newAllergy.confirmedDate}
                      onChange={(e) => setNewAllergy(prev => ({ ...prev, confirmedDate: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button type="button" onClick={addAllergy} size="sm">
                    Agregar
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddAllergy(false)} size="sm">
                    Cancelar
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Medicamentos Actuales */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Pill className="h-5 w-5 text-blue-600" />
              Medicamentos Actuales
            </CardTitle>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => setShowAddMedication(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Agregar Medicamento
            </Button>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {formData.currentMedications.length === 0 ? (
            <p className="text-gray-500 text-center py-4">
              No hay medicamentos registrados
            </p>
          ) : (
            <div className="space-y-2">
              {formData.currentMedications.map((medication, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span className="font-medium">{medication.name}</span>
                      <Badge variant={medication.isActive ? 'default' : 'secondary'}>
                        {medication.isActive ? 'Activo' : 'Suspendido'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600">
                      {medication.dosage} • {medication.frequency} • Desde: {new Date(medication.startDate).toLocaleDateString()}
                    </p>
                  </div>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeMedication(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          )}

          {/* Formulario para agregar medicamento */}
          {showAddMedication && (
            <Card>
              <CardContent className="pt-4 space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Nombre del Medicamento</Label>
                    <Input
                      value={newMedication.name}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Ej: Paracetamol"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Dosis</Label>
                    <Input
                      value={newMedication.dosage}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, dosage: e.target.value }))}
                      placeholder="Ej: 500mg"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Frecuencia</Label>
                    <Input
                      value={newMedication.frequency}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, frequency: e.target.value }))}
                      placeholder="Ej: cada 8 horas"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Fecha de Inicio</Label>
                    <Input
                      type="date"
                      value={newMedication.startDate}
                      onChange={(e) => setNewMedication(prev => ({ ...prev, startDate: e.target.value }))}
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <Button type="button" onClick={addMedication} size="sm">
                    Agregar
                  </Button>
                  <Button type="button" variant="outline" onClick={() => setShowAddMedication(false)} size="sm">
                    Cancelar
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </CardContent>
      </Card>

      {/* Información Adicional */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Droplet className="h-5 w-5 text-purple-600" />
            Información Adicional
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Tipo de Sangre</Label>
              <Select
                value={formData.bloodType}
                onValueChange={(value) => setFormData(prev => ({ ...prev, bloodType: value }))}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Seleccionar tipo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A-">A-</SelectItem>
                  <SelectItem value="B+">B+</SelectItem>
                  <SelectItem value="B-">B-</SelectItem>
                  <SelectItem value="AB+">AB+</SelectItem>
                  <SelectItem value="AB-">AB-</SelectItem>
                  <SelectItem value="O+">O+</SelectItem>
                  <SelectItem value="O-">O-</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label>Notas del Médico</Label>
            <Textarea
              value={formData.doctorNotes}
              onChange={(e) => setFormData(prev => ({ ...prev, doctorNotes: e.target.value }))}
              placeholder="Observaciones adicionales sobre el expediente médico..."
              rows={3}
            />
          </div>
        </CardContent>
      </Card>

      {/* Botones de acción */}
      <div className="flex justify-end gap-4 pt-6">
        {onCancel && (
          <Button 
            type="button" 
            variant="outline" 
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancelar
          </Button>
        )}
        <Button 
          type="submit" 
          disabled={isSubmitting}
          className="min-w-32"
        >
          {isSubmitting ? (
            <>
              <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
              Guardando...
            </>
          ) : (
            <>
              <Save className="h-4 w-4 mr-2" />
              Guardar Expediente
            </>
          )}
        </Button>
      </div>
    </form>
  );
}