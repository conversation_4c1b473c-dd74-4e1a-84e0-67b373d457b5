'use client';

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { 
  BookOpen,
  Search,
  Eye,
  Filter,
  Download,
  Info,
  Users,
  MapPin,
  Briefcase,
  Heart,
  Stethoscope,
  DollarSign
} from 'lucide-react';

// Catálogos disponibles para médicos (solo lectura)
const medicalCatalogs = [
  {
    id: 'specialties',
    name: 'Especialidades Médicas',
    description: 'Catálogo de especialidades médicas disponibles',
    icon: '🩺',
    count: 20,
    category: 'Médico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'occupations',
    name: 'Ocupaciones',
    description: 'Lista de ocupaciones profesionales',
    icon: '💼',
    count: 45,
    category: 'Demográfico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'relationships',
    name: '<PERSON>rentes<PERSON>',
    description: 'Tipos de relación familiar para encargados',
    icon: '👨‍👩‍👧‍👦',
    count: 12,
    category: 'Demográfico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'countries',
    name: 'Países',
    description: 'Catálogo de países disponibles',
    icon: '🌍',
    count: 3,
    category: 'Geográfico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'departments',
    name: 'Departamentos',
    description: 'Departamentos de Guatemala',
    icon: '🏛️',
    count: 22,
    category: 'Geográfico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'municipalities',
    name: 'Municipios',
    description: 'Municipios por departamento',
    icon: '🏘️',
    count: 340,
    category: 'Geográfico',
    available: true,
    lastUpdated: '2024-01-15'
  },
  {
    id: 'pathological',
    name: 'Antecedentes Patológicos',
    description: 'Antecedentes médicos patológicos',
    icon: '🔬',
    count: 0,
    category: 'Médico',
    available: false,
    lastUpdated: null
  },
  {
    id: 'nonPathological',
    name: 'Antecedentes No Patológicos',
    description: 'Hábitos y antecedentes no patológicos',
    icon: '🚭',
    count: 0,
    category: 'Médico',
    available: false,
    lastUpdated: null
  },
  {
    id: 'medications',
    name: 'Medicamentos',
    description: 'Catálogo de medicamentos disponibles',
    icon: '💊',
    count: 0,
    category: 'Médico',
    available: false,
    lastUpdated: null
  },
  {
    id: 'services',
    name: 'Servicios Médicos',
    description: 'Servicios médicos y procedimientos',
    icon: '🏥',
    count: 0,
    category: 'Médico',
    available: false,
    lastUpdated: null
  }
];

const categories = [
  { id: 'all', name: 'Todos', icon: BookOpen },
  { id: 'Médico', name: 'Médico', icon: Stethoscope },
  { id: 'Demográfico', name: 'Demográfico', icon: Users },
  { id: 'Geográfico', name: 'Geográfico', icon: MapPin }
];

export default function DoctorCatalogsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(false);

  const filteredCatalogs = medicalCatalogs.filter(catalog => {
    const matchesSearch = catalog.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         catalog.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || catalog.category === selectedCategory;
    const matchesAvailability = !showOnlyAvailable || catalog.available;
    
    return matchesSearch && matchesCategory && matchesAvailability;
  });

  const availableCatalogs = medicalCatalogs.filter(c => c.available).length;
  const totalRecords = medicalCatalogs.filter(c => c.available).reduce((sum, c) => sum + c.count, 0);

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 mb-2">Catálogos Médicos</h1>
            <p className="text-gray-600">Consulta catálogos administrativos y médicos</p>
          </div>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Exportar
          </Button>
          <Button variant="outline" size="sm">
            <Info className="h-4 w-4 mr-2" />
            Ayuda
          </Button>
        </div>
      </div>

      {/* Estadísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Catálogos Disponibles</p>
                <p className="text-3xl font-bold text-emerald-600">{availableCatalogs}</p>
              </div>
              <div className="h-12 w-12 bg-emerald-100 rounded-full flex items-center justify-center">
                <BookOpen className="h-6 w-6 text-emerald-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total de Registros</p>
                <p className="text-3xl font-bold text-blue-600">{totalRecords.toLocaleString()}</p>
              </div>
              <div className="h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Users className="h-6 w-6 text-blue-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Acceso</p>
                <p className="text-3xl font-bold text-gray-700">Solo Lectura</p>
              </div>
              <div className="h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center">
                <Eye className="h-6 w-6 text-gray-600" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros y Búsqueda */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            {/* Búsqueda */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Buscar catálogos..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Filtro por categoría */}
            <div className="flex space-x-2">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <Button
                    key={category.id}
                    variant={selectedCategory === category.id ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedCategory(category.id)}
                    className="flex items-center space-x-2"
                  >
                    <Icon className="h-4 w-4" />
                    <span>{category.name}</span>
                  </Button>
                );
              })}
            </div>

            {/* Filtro disponibilidad */}
            <Button
              variant={showOnlyAvailable ? 'default' : 'outline'}
              size="sm"
              onClick={() => setShowOnlyAvailable(!showOnlyAvailable)}
            >
              <Filter className="h-4 w-4 mr-2" />
              Solo Disponibles
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Catálogos */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredCatalogs.map((catalog) => (
          <Card 
            key={catalog.id}
            className={`transition-all hover:shadow-md ${
              catalog.available 
                ? 'border-emerald-200 hover:border-emerald-300' 
                : 'border-gray-200 bg-gray-50'
            }`}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-2xl">{catalog.icon}</div>
                  <div>
                    <CardTitle className="text-base">{catalog.name}</CardTitle>
                    <Badge variant="outline" className="mt-1">
                      {catalog.category}
                    </Badge>
                  </div>
                </div>
                {catalog.available ? (
                  <Badge className="bg-emerald-500">
                    Disponible
                  </Badge>
                ) : (
                  <Badge variant="secondary">
                    No Disponible
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4">{catalog.description}</p>
              
              <div className="space-y-3">
                <div className="flex justify-between items-center text-sm">
                  <span className="text-gray-600">Registros:</span>
                  <span className="font-medium">{catalog.count.toLocaleString()}</span>
                </div>
                
                {catalog.lastUpdated && (
                  <div className="flex justify-between items-center text-sm">
                    <span className="text-gray-600">Última actualización:</span>
                    <span className="font-medium">{catalog.lastUpdated}</span>
                  </div>
                )}
                
                <div className="pt-2">
                  {catalog.available ? (
                    <Button className="w-full" size="sm">
                      <Eye className="h-4 w-4 mr-2" />
                      Consultar
                    </Button>
                  ) : (
                    <Button variant="outline" className="w-full" size="sm" disabled>
                      <Info className="h-4 w-4 mr-2" />
                      No Implementado
                    </Button>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Mensaje cuando no hay resultados */}
      {filteredCatalogs.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <div className="text-gray-400 mb-4">
              <Search className="h-12 w-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">No se encontraron catálogos</h3>
            <p className="text-gray-600">
              Intenta cambiar los filtros o el término de búsqueda.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Información adicional */}
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-6">
          <div className="flex items-start space-x-4">
            <div className="h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
              <Info className="h-5 w-5 text-blue-600" />
            </div>
            <div className="flex-1">
              <h3 className="font-semibold text-blue-900 mb-2">Información Importante</h3>
              <ul className="text-blue-800 space-y-1 text-sm">
                <li>• Los catálogos se muestran en <strong>modo de solo lectura</strong> para médicos</li>
                <li>• Para modificaciones, contacta al administrador del sistema</li>
                <li>• Los datos se actualizan automáticamente cada 24 horas</li>
                <li>• Puedes exportar datos para uso offline utilizando el botón "Exportar"</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}