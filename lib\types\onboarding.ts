// Estados de usuario
export enum UserStatus {
  PENDING = 'pending',
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  SUSPENDED = 'suspended'
}

// Tipos de documento
export enum DocumentType {
  DPI = 'DPI',
  PASAPORTE = 'PASAPORTE',
  OTRO = 'OTRO'
}

// Géneros
export enum Gender {
  M = 'M',
  F = 'F',
  OTRO = 'OTRO'
}

// Roles del sistema
export enum UserRole {
  DOCTOR = 'doctor',
  ASSISTANT = 'assistant',
  PATIENT = 'patient',
  GUARDIAN = 'guardian',
  PROVIDER = 'provider'
}

// Tipos de relación familiar
export enum RelationshipType {
  PADRE = 'PADRE',
  MADRE = 'MADRE',
  TUTOR_LEGAL = 'TUTOR_LEGAL',
  OTRO = 'OTRO'
}

// Datos generales para todos los roles
export interface GeneralProfileData {
  // Información Personal
  firstName?: string;
  lastName?: string;
  documentType?: string;
  documentNumber?: string;
  dateOfBirth?: Date;
  gender?: string;
  
  // Contacto
  phone?: string;
  alternativePhone?: string;
  email?: string;
  address?: string;
  
  // Localización (IDs referenciando catálogos)
  countryId?: number;
  departmentId?: number;
  municipalityId?: number;
  occupationId?: number;
  
  // Emergencia
  emergencyContact?: string;
  emergencyPhone?: string;
  emergencyRelationshipId?: number;
}

// Horarios de trabajo
export interface WorkSchedule {
  [day: string]: {
    start: string;
    end: string;
    available: boolean;
  };
}

// Documentos requeridos
export interface DocumentData {
  url: string;
  uploadedAt: Date;
  verified: boolean;
  verifiedBy?: string;
}

// Datos específicos del Doctor
export interface DoctorSpecificData extends GeneralProfileData {
  // Profesional
  medicalLicense: string;
  specialty: string;
  subSpecialties?: string[];
  university?: string;
  graduationYear?: number;
  
  // Asociación
  consultoryId: string;
  
  // Horarios disponibles
  workSchedule: WorkSchedule;
  
  // Documentos requeridos
  documents: {
    licensePhoto?: File;
    diplomaPhoto?: File;
    cvPdf?: File;
  };
}

// Permisos de asistente
export interface AssistantPermissions {
  canScheduleAppointments: boolean;
  canHandlePayments: boolean;
  canAccessMedicalRecords: boolean;
  canManageInventory: boolean;
}

// Datos específicos del Asistente
export interface AssistantSpecificData extends GeneralProfileData {
  // Profesional
  position: string;
  yearsExperience: number;
  
  // Asociación
  consultoryId: string;
  assignedDoctors: string[];
  
  // Permisos especiales
  permissions: AssistantPermissions;
}

// Datos específicos del Paciente
export interface PatientSpecificData extends GeneralProfileData {
  // Información médica básica
  bloodType?: string;
  allergies?: string[];
  chronicConditions?: string[];
  currentMedications?: string[];
  
  // Asociación
  preferredDoctorId: string;
  insuranceCompany?: string;
  insuranceNumber?: string;
  
  // Para menores de edad
  isMinor: boolean;
  guardianRequired: boolean;
}

// Datos específicos del Guardian
export interface GuardianSpecificData extends GeneralProfileData {
  // Relación
  relationship: RelationshipType;
  
  // Asociación
  associationCode?: string;
  createMinor?: boolean;
  minorData?: PatientSpecificData;
  
  // Documentos
  legalGuardianshipDoc?: File;
}

// Datos específicos del Proveedor
export interface ProviderSpecificData extends GeneralProfileData {
  // Empresa
  companyName: string;
  nit: string;
  companyAddress: string;
  companyPhone: string;
  
  // Servicios
  serviceTypes: string[];
  
  // Asociación
  consultoryIds: string[];
  
  // Documentos
  documents: {
    nitDocument: File;
    commercialLicense?: File;
    catalog?: File;
  };
}

// Solicitud de registro
export interface RegistrationRequest {
  id: string;
  userId: string;
  role: UserRole;
  submittedAt: Date;
  status: 'pending' | 'reviewing' | 'approved' | 'rejected';
  generalData: GeneralProfileData;
  specificData: any;
  documents: DocumentData[];
  reviewNotes?: string;
  reviewedBy?: string;
  reviewedAt?: Date;
  rejectionReason?: string;
}

// Opciones de aprobación
export interface ApprovalOptions {
  activateImmediately: boolean;
  sendWelcomeEmail: boolean;
  assignToConsultory?: string;
  notes?: string;
}

// Código de asociación
export interface AssociationCode {
  id: string;
  code: string;
  patientId: string;
  expiresAt: Date;
  usedBy?: string;
  usedAt?: Date;
  createdAt: Date;
}

// Relación Doctor-Asistente
export interface DoctorAssistantRelation {
  id: string;
  doctorId: string;
  assistantId: string;
  consultoryId?: string;
  permissions: AssistantPermissions;
  active: boolean;
  createdAt: Date;
}

// Relación Paciente-Guardian
export interface PatientGuardianRelation {
  id: string;
  patientId: string;
  guardianId: string;
  relationship: RelationshipType;
  isPrimary: boolean;
  canMakeDecisions: boolean;
  validUntil?: Date;
  createdAt: Date;
}

// Consultorio
export interface Consultory {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  email?: string;
  active: boolean;
  description?: string;
  services?: string[];
  workingHours?: WorkSchedule;
  createdAt: Date;
  updatedAt: Date;
}

// Notificación
export interface Notification {
  id: string;
  userId: string;
  type: 'onboarding' | 'approval' | 'rejection' | 'info_request' | 'general';
  title: string;
  message: string;
  read: boolean;
  data?: any;
  createdAt: Date;
}

// Respuestas de API
export interface OnboardingResponse {
  success: boolean;
  message: string;
  data?: any;
  redirectUrl?: string;
}

// Datos del formulario de onboarding
export interface OnboardingFormData {
  step: number;
  role: UserRole;
  generalData?: Partial<GeneralProfileData>;
  specificData?: any;
  documents?: { [key: string]: File };
  completed: boolean;
}

// Validaciones por rol
export interface ValidationRules {
  doctor: {
    validateLicense: (licenseNumber: string) => Promise<boolean>;
    validateConsultory: (consultoryId: string) => Promise<boolean>;
  };
  assistant: {
    validateDoctors: (doctorIds: string[]) => Promise<boolean>;
    checkAssistantLimit: (doctorId: string) => Promise<boolean>;
  };
  guardian: {
    validateAssociationCode: (code: string) => Promise<boolean>;
    validatePatientAge: (patientId: string) => Promise<boolean>;
  };
  patient: {
    requiresGuardian: (age: number) => boolean;
  };
}

// Configuración por rol
export interface RoleConfig {
  title: string;
  description: string;
  requiredDocuments: string[];
  validationRules: any;
}

// Acciones del administrador
export interface AdminActions {
  viewRequest: (requestId: string) => Promise<RegistrationRequest>;
  verifyDocuments: (requestId: string) => Promise<boolean>;
  approve: (requestId: string, options: ApprovalOptions) => Promise<void>;
  reject: (requestId: string, reason: string) => Promise<void>;
  requestMoreInfo: (requestId: string, fields: string[]) => Promise<void>;
}

// Configuración de notificaciones
export interface NotificationConfig {
  onSubmit: {
    user: string;
    admin: string;
  };
  onApprove: {
    user: string;
    relatedUsers: string;
  };
  onReject: {
    user: string;
  };
  onRequestInfo: {
    user: string;
  };
} 

// Tipos para múltiples roles
export interface MultiRoleUser {
  id: string;
  generalProfile: GeneralProfileData;
  activeRoles: UserRole[];
  roles: {
    doctor?: DoctorSpecificData;
    patient?: PatientSpecificData;
    guardian?: GuardianSpecificData;
    assistant?: AssistantSpecificData;
    provider?: ProviderSpecificData;
  };
  documents: {
    doctor?: { licensePhoto?: string; diplomaPhoto?: string; };
    patient?: { insuranceCard?: string; };
    guardian?: { legalDocuments?: string[]; };
    assistant?: { certificatePhoto?: string; };
    provider?: { businessLicense?: string; };
  };
  status: 'pending' | 'active' | 'inactive' | 'suspended';
}

// Datos de solicitud de múltiples roles
export interface MultiRoleOnboardingData {
  generalData: GeneralProfileData;
  requestedRoles: UserRole[];
  rolesData: {
    doctor?: DoctorSpecificData;
    patient?: PatientSpecificData;
    guardian?: GuardianSpecificData;
    assistant?: AssistantSpecificData;
    provider?: ProviderSpecificData;
  };
  documents: {
    doctor?: { licensePhoto?: File; diplomaPhoto?: File; };
    patient?: { insuranceCard?: File; };
    guardian?: { legalDocuments?: File[]; };
    assistant?: { certificatePhoto?: File; };
    provider?: { businessLicense?: File; };
  };
} 