// Script simple para sembrar configuración regional
const { neon } = require('@neondatabase/serverless');

// Leer DATABASE_URL desde .env.local
require('dotenv').config({ path: '.env.local' });

if (!process.env.DATABASE_URL) {
  console.error('❌ DATABASE_URL no encontrada');
  process.exit(1);
}

async function seedRegionalConfig() {
  const sql = neon(process.env.DATABASE_URL);
  
  console.log('🚀 Insertando configuraciones regionales...');
  
  const configs = [
    { key: 'regional.dateFormat', value: '"dd/MM/yyyy"', description: 'Formato de fecha predeterminado' },
    { key: 'regional.currency', value: '"GTQ"', description: 'Moneda predeterminada' },
    { key: 'regional.currencySymbol', value: '"Q"', description: 'Símbolo de moneda' },
    { key: 'regional.locale', value: '"es-GT"', description: 'Configuración regional' }
  ];
  
  for (const config of configs) {
    try {
      await sql`
        INSERT INTO system_config (key, value, description, category, active) 
        VALUES (${config.key}, ${config.value}, ${config.description}, 'regional', true)
        ON CONFLICT (key) DO UPDATE SET 
          value = ${config.value}, 
          description = ${config.description}
      `;
      console.log(`✅ ${config.key}`);
    } catch (error) {
      console.error(`❌ Error con ${config.key}:`, error.message);
    }
  }
  
  console.log('🎉 ¡Configuración completada!');
}

seedRegionalConfig().catch(console.error);