# Análisis de uso de campos `active` vs `isActive` en Consultorios

## Resumen del problema
La tabla `consultories` tiene dos campos booleanos duplicados:
- `active: boolean("active").default(true)` - Campo legacy
- `isActive: boolean("isActive").default(true)` - Campo nuevo estándar

## Archivos que usan `consultories.active`:

1. **lib/validations/catalog-validation.ts**
   - Línea 65: Validación de consultorios activos

2. **scripts/set-primary-consultory.ts**
   - Línea 16: Búsqueda de consultorios activos
   - Línea 37: Actualización para activar consultorio

3. ~~**app/api/consultories/route.ts** (API público)~~
   - ~~Línea 22: Búsqueda del consultorio activo para la web pública~~
   - **ACTUALIZADO**: Ahora usa `isActive`

4. **app/api/onboarding/submit/route.ts**
   - Línea 111: Búsqueda de consultorio activo durante onboarding

## Archivos que usan `consultories.isActive`:

1. **scripts/setup-regional-config.ts**
   - Línea 149: Configuración regional de consultorios activos

2. **app/api/medical-records/[id]/route.ts**
   - Línea 413: Validación de consultorio activo en expedientes médicos

3. **app/api/medical-records/[id]/consultations/route.ts**
   - Línea 281: Validación de consultorio activo en consultas

4. **app/api/appointments/route.ts**
   - Línea 442: Validación de consultorio activo en citas

5. **app/api/medical-records/route.ts**
   - Línea 361: Validación de consultorio activo al crear expediente

6. **app/api/catalogs/consultories/route.ts**
   - Líneas 77, 79: Filtrado de consultorios por estado activo/inactivo

## Conclusiones

### APIs públicos y onboarding usan `active`:
- API público para web (`/api/consultories`)
- Proceso de onboarding
- Validaciones de catálogos

### APIs médicos y de gestión usan `isActive`:
- Expedientes médicos
- Consultas médicas
- Citas
- Catálogos administrativos

## Recomendación

Parece que hay una división clara:
- **`active`**: Se usa para funcionalidades públicas y de configuración inicial
- **`isActive`**: Se usa para funcionalidades médicas y administrativas

**Problema**: Esto puede causar inconsistencias donde un consultorio esté activo para el público pero no para el sistema médico, o viceversa.

**Solución propuesta**: 
1. Decidir cuál campo usar como estándar
2. Crear una migración para sincronizar ambos campos
3. Actualizar todo el código para usar solo un campo
4. Eliminar el campo duplicado del esquema