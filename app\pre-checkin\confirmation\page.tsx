import { Suspense } from 'react';
import { notFound } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, Calendar, Clock, MapPin, ArrowLeft } from 'lucide-react';
import Link from 'next/link';

interface ConfirmationPageProps {
  searchParams: {
    appointment?: string;
  };
}

export default async function PreCheckinConfirmationPage({ searchParams }: ConfirmationPageProps) {
  const appointmentId = searchParams.appointment;

  if (!appointmentId) {
    notFound();
  }

  // En un caso real, aquí obtendríamos los detalles de la cita
  // Para este ejemplo, mostraremos un mensaje genérico

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 to-emerald-100 py-8 px-4">
      <div className="max-w-2xl mx-auto">
        {/* Header de éxito */}
        <div className="text-center mb-8">
          <div className="flex justify-center mb-4">
            <div className="bg-green-100 p-6 rounded-full">
              <CheckCircle className="h-16 w-16 text-green-600" />
            </div>
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            ¡Pre-checkin Completado!
          </h1>
          <p className="text-lg text-gray-600">
            Gracias por completar tu pre-checkin médico
          </p>
        </div>

        {/* Información de confirmación */}
        <Card className="border-green-200 bg-green-50 mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span>Confirmación Exitosa</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-green-700">
              <p className="font-medium">✅ Tu asistencia ha sido confirmada</p>
              <p className="font-medium">📋 Información médica actualizada</p>
              <p className="font-medium">📧 Notificación enviada al consultorio</p>
            </div>
          </CardContent>
        </Card>

        {/* Próximos pasos */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Calendar className="h-5 w-5 text-blue-600" />
              <span>Próximos Pasos</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-2 rounded-full mt-1">
                  <Clock className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Llega 15 minutos antes</h4>
                  <p className="text-sm text-gray-600">
                    Te recomendamos llegar 15 minutos antes de tu cita para completar cualquier proceso pendiente.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-2 rounded-full mt-1">
                  <MapPin className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Ubica el consultorio</h4>
                  <p className="text-sm text-gray-600">
                    Confirma la ubicación exacta del consultorio y planifica tu ruta con anticipación.
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-3">
                <div className="bg-blue-100 p-2 rounded-full mt-1">
                  <CheckCircle className="h-4 w-4 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-medium text-gray-900">Prepara tu documentación</h4>
                  <p className="text-sm text-gray-600">
                    Trae tu documento de identidad, tarjeta de seguro (si aplica) y cualquier estudio médico reciente.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Lista de verificación */}
        <Card className="mb-8">
          <CardHeader>
            <CardTitle>📋 Lista de Verificación</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center">
                  <span className="text-xs">□</span>
                </div>
                <span className="text-sm">Documento de identidad</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center">
                  <span className="text-xs">□</span>
                </div>
                <span className="text-sm">Tarjeta de seguro médico (si aplica)</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center">
                  <span className="text-xs">□</span>
                </div>
                <span className="text-sm">Lista de medicamentos actuales</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center">
                  <span className="text-xs">□</span>
                </div>
                <span className="text-sm">Exámenes médicos previos</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 border-2 border-gray-300 rounded flex items-center justify-center">
                  <span className="text-xs">□</span>
                </div>
                <span className="text-sm">Medio de pago (efectivo o tarjeta)</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Información importante */}
        <Card className="border-amber-200 bg-amber-50 mb-8">
          <CardContent className="pt-6">
            <h3 className="font-semibold text-amber-800 mb-2">ℹ️ Información Importante</h3>
            <ul className="text-sm text-amber-700 space-y-1">
              <li>• Si necesitas cancelar o reprogramar, contacta al consultorio con al menos 24 horas de anticipación</li>
              <li>• En caso de emergencia, dirígete al centro de salud más cercano</li>
              <li>• Mantén tu teléfono disponible por si el consultorio necesita contactarte</li>
            </ul>
          </CardContent>
        </Card>

        {/* Acciones */}
        <div className="text-center space-y-4">
          <p className="text-gray-600">
            Tu pre-checkin está completo. ¡Te esperamos en tu cita!
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button variant="outline" asChild>
              <Link href="/" className="flex items-center space-x-2">
                <ArrowLeft className="h-4 w-4" />
                <span>Volver al inicio</span>
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}