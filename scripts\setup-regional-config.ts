#!/usr/bin/env node
import { config } from 'dotenv';
import { join } from 'path';

// Cargar variables de entorno desde .env.local
config({ path: join(process.cwd(), '.env.local') });

import { drizzle } from 'drizzle-orm/neon-http';
import { neon } from '@neondatabase/serverless';
import { systemConfig, consultories } from '../db/schema';
import { eq } from 'drizzle-orm';

// Configurar la conexión a la base de datos
if (!process.env.DATABASE_URL) {
  console.error('❌ Error: DATABASE_URL no encontrada en las variables de entorno');
  console.log('📝 Asegúrate de tener un archivo .env.local con DATABASE_URL');
  process.exit(1);
}

const sql = neon(process.env.DATABASE_URL);
const db = drizzle(sql);

async function setupRegionalConfig() {
  try {
    console.log('🚀 Configurando sistema de configuración regional...');

    // 1. Insertar configuraciones regionales por defecto en system_config
    console.log('📊 Insertando configuraciones globales...');
    
    const configs = [
      {
        key: 'regional.dateFormat',
        value: JSON.stringify('dd/MM/yyyy'),
        description: 'Formato de fecha predeterminado del sistema',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.dateTimeFormat',
        value: JSON.stringify('dd/MM/yyyy HH:mm'),
        description: 'Formato de fecha y hora predeterminado',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.timeFormat',
        value: JSON.stringify('HH:mm'),
        description: 'Formato de hora predeterminado (24 horas)',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.currency',
        value: JSON.stringify('GTQ'),
        description: 'Moneda predeterminada del sistema',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.currencySymbol',
        value: JSON.stringify('Q'),
        description: 'Símbolo de moneda predeterminado',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.currencyPosition',
        value: JSON.stringify('before'),
        description: 'Posición del símbolo de moneda (before/after)',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.locale',
        value: JSON.stringify('es-GT'),
        description: 'Configuración regional predeterminada',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.timezone',
        value: JSON.stringify('America/Guatemala'),
        description: 'Zona horaria predeterminada',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.decimalSeparator',
        value: JSON.stringify('.'),
        description: 'Separador decimal para números',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.thousandsSeparator',
        value: JSON.stringify(','),
        description: 'Separador de miles para números',
        category: 'regional',
        active: true
      },
      {
        key: 'regional.weekStartsOn',
        value: JSON.stringify(1),
        description: 'Día de inicio de semana (0=Domingo, 1=Lunes)',
        category: 'regional',
        active: true
      }
    ];

    for (const config of configs) {
      try {
        await db.insert(systemConfig).values(config).onConflictDoUpdate({
          target: systemConfig.key,
          set: {
            value: config.value,
            description: config.description,
            category: config.category,
            active: config.active,
            updatedAt: new Date()
          }
        });
        console.log(`✅ Configuración ${config.key} insertada/actualizada`);
      } catch (error) {
        console.error(`❌ Error con configuración ${config.key}:`, error);
      }
    }

    // 2. Actualizar todos los consultorios existentes con configuración regional por defecto
    console.log('🏥 Actualizando consultorios existentes...');
    
    const defaultRegionalSettings = {
      dateFormat: 'dd/MM/yyyy',
      dateTimeFormat: 'dd/MM/yyyy HH:mm',
      timeFormat: 'HH:mm',
      currency: 'GTQ',
      currencySymbol: 'Q',
      currencyPosition: 'before' as const,
      locale: 'es-GT',
      timezone: 'America/Guatemala',
      decimalSeparator: '.',
      thousandsSeparator: ',',
      weekStartsOn: 1 as const
    };

    // Obtener todos los consultorios que no tienen configuración regional
    const consultoriesWithoutConfig = await db
      .select()
      .from(consultories)
      .where(eq(consultories.isActive, true));

    for (const consultory of consultoriesWithoutConfig) {
      // Solo actualizar si no tiene configuración regional o está vacía
      if (!consultory.regionalSettings || Object.keys(consultory.regionalSettings).length === 0) {
        await db
          .update(consultories)
          .set({
            regionalSettings: defaultRegionalSettings,
            updatedAt: new Date()
          })
          .where(eq(consultories.id, consultory.id));
        
        console.log(`✅ Consultorio "${consultory.name}" actualizado con configuración regional`);
      } else {
        console.log(`ℹ️ Consultorio "${consultory.name}" ya tiene configuración regional`);
      }
    }

    console.log('🎉 ¡Configuración regional completada exitosamente!');
    console.log('');
    console.log('📋 Resumen:');
    console.log(`- ${configs.length} configuraciones globales insertadas/actualizadas`);
    console.log(`- ${consultoriesWithoutConfig.length} consultorios verificados`);
    console.log('');
    console.log('🔧 Próximos pasos:');
    console.log('1. Ejecutar la migración: npx drizzle-kit push');
    console.log('2. Agregar el RegionalConfigProvider al layout principal');
    console.log('3. Usar useRegionalConfig() en los componentes');

  } catch (error) {
    console.error('❌ Error configurando sistema regional:', error);
    process.exit(1);
  }
}

// Ejecutar el script si se llama directamente
if (require.main === module) {
  setupRegionalConfig().then(() => {
    console.log('✨ Script completado');
    process.exit(0);
  }).catch((error) => {
    console.error('💥 Error ejecutando script:', error);
    process.exit(1);
  });
}