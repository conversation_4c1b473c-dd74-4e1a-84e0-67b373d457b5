import { db } from '../db/drizzle';
import { medicalServices, serviceTags, medicalServiceTags } from '../db/schema';
import { eq } from 'drizzle-orm';

async function migrateServicesToTags() {
  try {
    console.log('🔄 Starting migration of medical services to tags system...');

    // 1. Obtener los tags de requerimientos
    const requiresEquipmentTag = await db
      .select()
      .from(serviceTags)
      .where(eq(serviceTags.name, 'Requiere Equipo'))
      .limit(1);

    const requiresSpecialistTag = await db
      .select()
      .from(serviceTags)
      .where(eq(serviceTags.name, 'Requiere Especialista'))
      .limit(1);

    if (!requiresEquipmentTag[0] || !requiresSpecialistTag[0]) {
      console.error('❌ No se encontraron los tags necesarios. Asegúrate de ejecutar el seed de tags primero.');
      return;
    }

    const equipmentTagId = requiresEquipmentTag[0].id;
    const specialistTagId = requiresSpecialistTag[0].id;

    // 2. Obtener todos los servicios médicos
    const allServices = await db.select().from(medicalServices);
    console.log(`📋 Encontrados ${allServices.length} servicios médicos`);

    let migratedCount = 0;

    // 3. Migrar cada servicio
    for (const service of allServices) {
      const tagsToAdd = [];

      if (service.requiresEquipment) {
        tagsToAdd.push({
          serviceId: service.id,
          tagId: equipmentTagId,
        });
      }

      if (service.requiresSpecialist) {
        tagsToAdd.push({
          serviceId: service.id,
          tagId: specialistTagId,
        });
      }

      if (tagsToAdd.length > 0) {
        // Verificar si ya existen las relaciones
        for (const tagRelation of tagsToAdd) {
          const existing = await db
            .select()
            .from(medicalServiceTags)
            .where(
              eq(medicalServiceTags.serviceId, tagRelation.serviceId)
            )
            .where(
              eq(medicalServiceTags.tagId, tagRelation.tagId)
            );

          if (existing.length === 0) {
            await db.insert(medicalServiceTags).values(tagRelation);
            console.log(`✅ Agregado tag a servicio: ${service.name}`);
            migratedCount++;
          }
        }
      }
    }

    console.log(`\n✅ Migración completada!`);
    console.log(`   - Total de servicios: ${allServices.length}`);
    console.log(`   - Relaciones de tags creadas: ${migratedCount}`);
    console.log(`\n📌 Nota: Los campos requiresEquipment y requiresSpecialist se mantienen por compatibilidad.`);
    console.log(`   Puedes eliminarlos en una futura migración cuando todo esté estable.`);

  } catch (error) {
    console.error('❌ Error durante la migración:', error);
    throw error;
  }
}

// Si este archivo se ejecuta directamente
if (require.main === module) {
  migrateServicesToTags()
    .then(() => {
      console.log('🎉 Migración completada exitosamente!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Error en la migración:', error);
      process.exit(1);
    });
}