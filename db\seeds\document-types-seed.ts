import { db } from '@/db/drizzle';
import { documentTypes } from '@/db/schema';

export const documentTypesData = [
  // Guatemala
  {
    id: 'gt-dpi',
    name: '<PERSON><PERSON> (Documento Personal de Identificación)',
    shortName: 'DPI',
    countryId: 1, // Guatemala ID
    countryName: 'Guatemala',
    format: '^[0-9]{13}$',
    maxLength: 13,
    minLength: 13,
    isRequired: true,
    description: 'Documento de identificación personal obligatorio para guatemaltecos',
    example: '*********0123'
  },
  {
    id: 'gt-passport',
    name: 'Pasaporte Guatemalteco',
    shortName: 'Pasaporte',
    countryId: 1, // Guatemala ID
    countryName: 'Guatemala',
    format: '^[A-Z]{1,2}[0-9]{6,8}$',
    maxLength: 9,
    minLength: 7,
    isRequired: false,
    description: 'Pasaporte emitido por Guatemala para viajes internacionales',
    example: 'A1234567'
  },
  {
    id: 'gt-cedula',
    name: '<PERSON><PERSON><PERSON><PERSON>indad',
    shortName: 'Cédu<PERSON>',
    countryId: 1, // Guatemala ID
    countryName: 'Guatemala',
    format: '^[A-Z]-[0-9]{1,6}$',
    maxLength: 8,
    minLength: 3,
    isRequired: false,
    description: 'Documento de identificación anterior al DPI (en desuso)',
    example: 'A-123456',
    isActive: false // Documento obsoleto
  },

  // Estados Unidos
  {
    id: 'us-passport',
    name: 'US Passport',
    shortName: 'Passport',
    countryId: 2, // US ID
    countryName: 'Estados Unidos',
    format: '^[0-9]{9}$',
    maxLength: 9,
    minLength: 9,
    isRequired: true,
    description: 'Pasaporte estadounidense',
    example: '*********'
  },
  {
    id: 'us-ssn',
    name: 'Social Security Number',
    shortName: 'SSN',
    countryId: 2, // US ID
    countryName: 'Estados Unidos',
    format: '^[0-9]{3}-[0-9]{2}-[0-9]{4}$',
    maxLength: 11,
    minLength: 11,
    isRequired: false,
    description: 'Número de Seguro Social',
    example: '***********'
  },

  // México
  {
    id: 'mx-curp',
    name: 'CURP (Clave Única de Registro de Población)',
    shortName: 'CURP',
    countryId: 3, // Mexico ID
    countryName: 'México',
    format: '^[A-Z]{4}[0-9]{6}[HM][A-Z]{5}[0-9A-Z][0-9]$',
    maxLength: 18,
    minLength: 18,
    isRequired: true,
    description: 'Clave única de registro poblacional de México',
    example: 'ABCD123456HDFMNL09'
  },
  {
    id: 'mx-passport',
    name: 'Pasaporte Mexicano',
    shortName: 'Pasaporte',
    countryId: 3, // Mexico ID
    countryName: 'México',
    format: '^[A-Z]{1}[0-9]{8}$',
    maxLength: 9,
    minLength: 9,
    isRequired: false,
    description: 'Pasaporte emitido por México',
    example: 'G12345678'
  }
];

export async function seedDocumentTypes() {
  console.log('📄 Seeding document types...');
  
  try {
    await db.insert(documentTypes).values(documentTypesData).onConflictDoNothing();
    console.log('✅ Document types seeded successfully');
  } catch (error) {
    console.error('❌ Error seeding document types:', error);
    throw error;
  }
}