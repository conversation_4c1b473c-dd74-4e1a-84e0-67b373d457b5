import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@clerk/nextjs/server';
import { db } from '@/db/drizzle';
import { notifications } from '@/db/schema';
import { eq, desc, and } from 'drizzle-orm';

// GET - Obtener notificaciones del usuario
export async function GET(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const searchParams = request.nextUrl.searchParams;
    const limit = parseInt(searchParams.get('limit') || '20');
    const onlyUnread = searchParams.get('unread') === 'true';

    // Construir query
    let query = db
      .select()
      .from(notifications)
      .where(eq(notifications.userId, userId))
      .orderBy(desc(notifications.createdAt))
      .limit(limit);

    if (onlyUnread) {
      query = db
        .select()
        .from(notifications)
        .where(
          and(
            eq(notifications.userId, userId),
            eq(notifications.read, false)
          )
        )
        .orderBy(desc(notifications.createdAt))
        .limit(limit);
    }

    const userNotifications = await query;
    
    // Contar no leídas
    const unreadCount = await db
      .select()
      .from(notifications)
      .where(
        and(
          eq(notifications.userId, userId),
          eq(notifications.read, false)
        )
      );

    return NextResponse.json({
      success: true,
      notifications: userNotifications,
      unreadCount: unreadCount.length
    });

  } catch (error) {
    console.error('Error fetching notifications:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}

// POST - Crear nueva notificación
export async function POST(request: NextRequest) {
  try {
    const { userId } = await auth();
    
    if (!userId) {
      return NextResponse.json({ error: 'No autorizado' }, { status: 401 });
    }

    const body = await request.json();
    const { type, title, message, data, targetUserId } = body;

    if (!type || !title || !message) {
      return NextResponse.json(
        { error: 'Campos requeridos: type, title, message' },
        { status: 400 }
      );
    }

    // Si targetUserId es proporcionado, usarlo; sino usar el userId actual
    const recipientId = targetUserId || userId;

    const [newNotification] = await db
      .insert(notifications)
      .values({
        userId: recipientId,
        type,
        title,
        message,
        data: data ? JSON.stringify(data) : null,
        read: false
      })
      .returning();

    return NextResponse.json({
      success: true,
      notification: newNotification
    });

  } catch (error) {
    console.error('Error creating notification:', error);
    return NextResponse.json(
      { error: 'Error interno del servidor' },
      { status: 500 }
    );
  }
}